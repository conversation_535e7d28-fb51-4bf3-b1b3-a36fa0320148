<script lang="ts">
import { VNode, CreateElement } from "vue/types";
import { Component, Vue } from "vue-property-decorator";
import CtBreadcrumbBase from "@/components/CtBreadcrumb.vue";
import { routes as globalRouters } from "@/router/index";
import { utils } from "@/router";

const flattenRoutes = utils.RouterHelper.getFlattenRoutes(globalRouters);

@Component({
    name: "CtBreadcrumb",
    components: { CtBreadcrumbBase },
})
export default class extends Vue {
    private render(h: CreateElement): VNode {
        return h("ct-breadcrumb-base", {
            attrs: {
                flattenRoutes,
            },
        });
    }
}
</script>
