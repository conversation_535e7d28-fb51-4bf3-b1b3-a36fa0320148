<template>
    <el-card>
        <el-table max-height="468px" :data="fetchData" v-loading="loading"
            :element-loading-text="$t('statistics.common.table.loading')">
            <el-table-column :label="$t('statistics.rank.common.tableColumn1')" prop="rank" align="center" />
            <el-table-column :label="$t('statistics.rank.domainRank.tableColumn2')" prop="domain" align="center" />
            <el-table-column :label="$t('log.list.label4')" prop="inst_name" align="center">
                <template v-slot="{ row }">
                    {{ renderInstName(row) }}
                </template>
            </el-table-column>
            <el-table-column :label="$t('statistics.rank.domainRank.tableColumn3')" prop="flow" align="center" />
            <el-table-column :label="$t('statistics.rank.common.tableColumn4')" prop="flow_per" align="center" />
            <el-table-column :label="$t('statistics.rank.domainRank.tableColumn5')" prop="top_band_width"
                align="center" />
            <el-table-column :label="$t('statistics.eas.tip8')" prop="top_band_width_time" align="center">
                <template slot-scope="scope">
                    {{ scope.row.top_band_width_time | timeFormat }}
                </template>
            </el-table-column>
            <el-table-column :label="$t('statistics.eas.tab[2]')" prop="connections" width="80" align="center" />
            <el-table-column :label="$t('statistics.eas.tip9')" prop="connections_per" width="150" align="center" />
            <el-table-column :label="$t('statistics.eas.tip10')" prop="qps" width="120" align="center" />
            <el-table-column :label="$t('statistics.eas.tip11')" prop="qps_time" align="center">
                <template slot-scope="scope">
                    {{ scope.row.qps_time | timeFormat }}
                </template>
            </el-table-column>
        </el-table>
    </el-card>
</template>

<script>
import { statistics as StatisticsUrl } from "@/config/url/ipa/statistics";
import { convertFlowB2P, convertBandwidthB2P } from "@/utils";
import { timeFormat } from "@cdnplus/common/filters/index";
import { get } from "lodash-es";
import rankMixin from "../rankMixin";
import chartShim from "../chartShim";

export default {
    props: {
        instNameOptionsAll: {
            type: Array,
            default: () => []
        }
    },
    mixins: [rankMixin, chartShim],
    data() {
        return {
            fetchData: [],
            loading: false,
            downloadDataList: [],
            totalRequest: "",
            totalFlow: "",
        };
    },
    computed: {
        // 域名和实例名的映射表
        domainInstNameMap() {
            return this.instNameOptionsAll.reduce((map, item) => {
                map[item.value] = item.label;
                return map;
            }, {});
        }
    },
    mounted() {
        this.$ctBus.$on("ipa:download:hot:domainRanking", this.downloadTable);
    },
    beforeDestroy() {
        this.$ctBus.$off("ipa:download:hot:domainRanking");
    },
    watch: {},
    filters: {
        timeFormat,
    },
    methods: {
        // 获取表格数据
        initData(reqParam) {
            this.getData(reqParam);
        },
        // 接口获取数据
        async getData(params) {
            this.loading = true;
            const loadingIndex = this.loadingIndex++;
            const rst = await this.$ctFetch(StatisticsUrl.topDomainList, {
                method: "POST",
                transferType: "json",
                // body: { data: { ...params, top: 100 } },
                body: { data: params },
            });
            if (loadingIndex !== this.loadingIndex - 1) return console.warn("find different loading index");
            this.loading = false;
            // this.downloadDataList = this.fetchData = rst.result;
            this.downloadDataList = rst.result
                .map(item => ({
                    ...item,
                }))
                .sort((a, b) => +a.rank - +b.rank);

            this.fetchData = rst.result
                .map(item => ({
                    ...item,
                    flow: convertFlowB2P(item.flow, this.scale).result,
                    top_band_width: convertBandwidthB2P(item.top_band_width, this.scale).result,
                }))
                .sort((a, b) => +a.rank - +b.rank);

            // ***获取总连接数和总流量
            // this.totalRequest = "1";
            // this.totalFlow = "2";
            this.totalRequest = 0;
            this.totalFlow = 0.0;
            rst.result.map(item => {
                this.totalRequest += item.connections;
                this.totalFlow += item.flow;
            });
        },

        downloadTable() {
            if (!Array.isArray(this.fetchData) || !this.fetchData.length) {
                this.$message(`${this.$t("statistics.common.chart.errMsg[2]")}`);
                return true;
            }

            this.download();
        },

        // 下载
        download() {
            this.tableToExcel();
        },
        // 重写 excel 数据拼接方法
        tableToExcel() {
            let str = "";

            str = `${this.$t("statistics.rank.domainRank.tableColumn2")},${this.$t("log.list.label4")},${this.$t("statistics.rank.domainRank.tableColumn3")}(${this.MB}),${this.$t("statistics.rank.common.tableColumn4")},${this.$t("statistics.usageQuery.BandwidthFlow.tableColumn3", { unit: this.Mbps })},${this.$t("statistics.rank.domainRank.tableColumn6")},${this.$t("statistics.eas.tab[2]")},${this.$t("statistics.eas.tip9")},${this.$t("statistics.eas.tip10")},${this.$t("statistics.eas.tip11")}\n`;

            this.downloadDataList.forEach(item => {
                const peakBandWidthTime = timeFormat(item.top_band_width_time);
                str += `${item.domain},${item.inst_name},${(item.flow / Math.pow(this.scale, 2)).toFixed(2)},${item.flow_per},${(item.top_band_width / Math.pow(this.scale, 2)).toFixed(2)
                    },${peakBandWidthTime},${item.connections},${item.connections_per},${item.qps},${timeFormat(
                        item.qps_time
                    )
                    } \n`;
            });

            //增加峰值带宽和95峰值带宽
            str += `\n${this.$t("statistics.rank.common.tableToExcel.excelColumn2")}\n${this.$t("statistics.eas.excel.tip1")}${this.totalRequest} \n${this.$t("statistics.eas.excel.tip2")}${(this.totalFlow / Math.pow(this.scale, 2)).toFixed(2)} ${this.MB} \n`;

            //表格CSV格式和内容
            this.downloadExcel({
                name: `${this.$t("statistics.rank.domainRank.tableToExcel.excelName")}`,
                str,
            });
        },
        /**
         * 渲染实例名称
         */
        renderInstName(row) {
            return get(this.domainInstNameMap, row.domain) || "-";
        }
    },
};
</script>
<style lang="scss" scoped></style>
