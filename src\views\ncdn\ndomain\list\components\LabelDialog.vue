<template>
    <el-dialog
        :title="$t('label.title')"
        :append-to-body="true"
        :close-on-click-modal="false"
        :visible.sync="labelVisible"
        :before-close="cancel"
        class="label-dialog"
    >
        <div class="domain-div">
            <span class="domain-span">{{ $t("domain.dialog.label1") }}</span>
            <span>{{ domainItem.domain }}</span>
        </div>
        <div class="label-transfer">
            <el-card shadow="never">
                <el-tree
                    :data="getLabelList"
                    show-checkbox
                    node-key="value"
                    ref="labelTree"
                    @check="handleCheckChange"
                >
                    <span class="custom-tree-node" slot-scope="{ node }">
                        <span>{{ node.label }}</span>
                    </span>
                </el-tree>
            </el-card>
            <el-card class="box-card" shadow="never">
                <ul class="text item" ref="rightPanel">
                    <li class="selected-label-item" v-for="(o, idx) in selectedLabelList" :key="o.value">
                        <span>{{ getGroupNameLabelBySubKey.get(o.value) }} > {{ o.label }}</span>
                        <el-button
                            type="text"
                            icon="el-icon-close"
                            @click="handleRemoveItem(idx)"
                        ></el-button>
                    </li>
                </ul>
            </el-card>
        </div>
        <div class="footer-bar">
            <span class="illustration-span">{{ $t("label.maxTagLimit") }}</span>
            <div class="selected-info">
                <span>{{ $t("common.labelSelect.labelSlectTagsText2", { num: selectedLabelList.length }) }}</span>
                <el-button
                    type="text"
                    @click="handleClear"
                    :disabled="!selectedLabelList.length"
                    :style="{ paddingRight: rightPanelHasScroll ? '20px' : '0px' }"
                    >{{ $t("certificate.bindDomain.clearAll") }}</el-button
                >
            </div>
        </div>

        <div slot="footer" class="dialog-footer">
            <el-button @click="cancel">{{ $t("common.dialog.cancel") }}</el-button>
            <el-button type="primary" @click="handleOperateLabelNew">{{
                $t("common.dialog.submit")
            }}</el-button>
        </div>
    </el-dialog>
</template>

<!-- eslint-disable no-undef -->
<script>
import { LabelUrl } from "@/config/url";
import { LabelModule } from "@/store/modules/label";
import { getCtiamAction } from '@/store/config';

export default {
    props: {
        labelVisible: Boolean,
        domainItem: {}, // 被操作的域名元素
    },
    data() {
        return {
            selectedLabelList: [], // 当前选中的标签列表
            rightPanelHasScroll: false, // 右侧标签列表是否滚动
        };
    },
    computed: {
        getGroupNameLabelBySubKey() {
            const map = new Map();

            LabelModule.nativeList.map(group => {
                if (!group.children) return;

                group.children.map(({ labelId }) => {
                    map.set(labelId, group.name);
                });
            });

            return map;
        },
        getLabelList() {
            return LabelModule.labelList;
        },
    },
    watch: {
        labelVisible(val) {
            if (!val) return;

            this.$nextTick(async () => {
                // await LabelModule.GetLabelList();
                await LabelModule.GetLabelList({
                    cache: false,
                    action: getCtiamAction("DomainListLabel"),
                });
                this.$refs.labelTree.setCheckedKeys(this.getSelectedKeyList());
                this.handleCheckChange();
            });
        },
        selectedLabelList() {
            this.$nextTick(() => {
                this.rightPanelHasScroll = 400 < this.$refs.rightPanel.scrollHeight;
            });
        },
    },
    methods: {
        cancel() {
            this.$emit("cancel");
        },
        handleCheckChange() {
            this.selectedLabelList = this.$refs.labelTree.getCheckedNodes(true);
        },
        handleClear() {
            this.selectedLabelList = [];
            this.$refs.labelTree.setCheckedNodes([]);
        },
        handleRemoveItem(idx) {
            this.selectedLabelList.splice(idx, 1);
            this.$refs.labelTree.setCheckedNodes(this.selectedLabelList);
        },
        // 获取域名绑定的标签组
        getSelectedKeyList() {
            const labelGroup = [];
            LabelModule.nativeList.map(group => {
                for (let i = 0; i < group.children.length; i += 1) {
                    const itm = group.children[i];
                    if (itm.domainList.includes(this.domainItem.domain)) {
                        labelGroup.push(itm.labelId);
                    }
                }
            });
            return labelGroup;
        },
        async handleOperateLabelNew() {
            // 如果当前域名是新增标签列表，并且只添加了标签组，不进行保存
            if (!LabelModule.domainMapLabel[this.domainItem.domain] && this.selectedLabelList?.length === 0)
                return this.$emit("cancel");
            // 获取初始标签组，判断q是否有修改
            const { domain } = this.domainItem;
            const selectedValueList = this.selectedLabelList.map(o => o.value);
            const originLabelIds = structuredClone(LabelModule.domainMapLabelJr[domain])?.sort().join(",");
            const currentLabelIds = structuredClone(selectedValueList)?.sort().join(",");

            if (originLabelIds === currentLabelIds) {
                this.$emit("cancel");
                return;
            }

            if (selectedValueList.length > 20) {
                this.$message.error(this.$t("label.maxTagLimit"));
                return;
            }

            // 更新标签
            await this.$ctFetch(LabelUrl.binding, {
                method: "POST",
                // 不清除空参数，保证全部删除标签时，空数组labelIds不被清除
                clearEmptyParams: false,
                data: {
                    workspaceId: this.$route.query.workspaceId,
                    domain: this.domainItem.domain,
                    labelIds: structuredClone(selectedValueList),
                },
                headers: {
                    "Content-Type": "application/json",
                },
            });

            // 数据更新后调用获取标签列表的方法
            // await LabelModule.GetLabelList();
            await LabelModule.GetLabelList({
                cache: false,
                action: getCtiamAction("DomainListLabel"),
            });

            this.$message.success(this.$t("domain.dialog.tip1"));
            // 关闭dialog
            this.$emit("cancel");
        },
    },
};
</script>

<style lang="scss" scoped>
.label-dialog {
    ::v-deep .el-dialog {
        min-width: 950px;
    }

    .domain-div {
        margin-bottom: 8px;
    }

    .illustration-span {
        color: $neutral-7;
    }

    .label-transfer {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 12px;
        margin-bottom: 12px;

        ::v-deep {
            .el-tree {
                overflow: auto;
                margin-top: 0;
            }

            .el-card__body {
                height: 400px;
                overflow: auto;
            }
        }

        .selected-label-item {
            padding: 8px;
            display: flex;
            justify-content: space-between;
            gap: 12px;

            > span {
                overflow-wrap: anywhere;
            }

            &:hover {
                background: #f7f8fa;
            }
        }
    }

    .footer-bar {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 12px;

        .selected-info {
            display: flex;
            justify-content: space-between;
            padding: 0 20px;
        }
    }
}
</style>
