/*
 * @Description: 工单相关的类型声明
 * @Author: wang yuegong
 */
import { OrderTypeEnum, OrderActionEnum, OrderStatusEnum } from "../../config/map";
import { DomainDetail } from "../domain";
import { OperationItem } from "../store";

// 使用配置的 value 生成 type
export type OrderType = typeof OrderTypeEnum[keyof typeof OrderTypeEnum];
export type OrderAction = typeof OrderActionEnum[keyof typeof OrderActionEnum];
export type OrderStatus = typeof OrderStatusEnum[keyof typeof OrderStatusEnum];

// 工单 item
export interface OrderItem {
    orderId: string; // 工单 id（查详情用，对应工单的 ext_order_id）
    domain: string;
    action: OrderAction; // 工单操作
    type: OrderType; // 工单类型
    status: OrderStatus; // 工单状态
    createTime: string; // 创建时间
    orderNo: string; // 工单编号（页面展示、沟通工单系统用）
    signature: string; // 签名
    operation: OperationItem[];
}

// 工单详情数据
export interface OrderInfo {
    orderNo: string; // 工单编号
    domain: string; // 域名
    type: OrderType; // 工单类型
    action: OrderAction; // 工单操作
    status: OrderStatus; // 工单状态
    createTime: string; // 创建时间
    compTime: string; // 完成时间
    reason?: string; // 失败原因
    formFields?: DomainDetail;
    operation: OperationItem[]; // 工单详情中的操作按钮：刷新、重新发起等
    orderId?: string;
    signature?: string;
}
