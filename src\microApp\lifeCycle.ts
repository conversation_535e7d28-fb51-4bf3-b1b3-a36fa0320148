import actions from "@/microApp/actions";
import Vue from "vue";
import i18n from "@/i18n";
import router from "@/router/index";
import store from "@/store";
import App from "@/App.vue";
import Custom from "@/microApp/custom";
import { get } from "lodash-es";
import { AppModule } from "@/store/modules/app";
import docHelp from "@/utils/logic/docHelp";

Vue.prototype.$store = store;

let instance: any = null;

(window as any).custom = new Custom();

export function render(props: any) {
    if (props) actions.setActions(props);
    const { routerInstance } = props;
    if (routerInstance) AppModule.SET_BASE_APP_ROUTER(routerInstance);
    const { container } = props;
    instance = new Vue({
        router,
        store,
        i18n,
        render: h => h(App),
    }).$mount(container ? container.querySelector("#app") : "#app");

    const defaultPath = get(props, "defaultPath");
    if (defaultPath) {
        router.push(defaultPath);
    }
    // 全局注册
    (window as any).$docHelp = docHelp; 
}

// 生命周期
export async function bootstrap(app: any): Promise<void> {
    console.log("[vue] vue app bootstraped");
}

export async function mount(props: { [keys: string]: any } | undefined): Promise<void> {
    render(props);
}

// 增加 update 钩子以便主应用手动更新微应用
export async function update(props: { [keys: string]: any } | undefined) {
    console.log("子应用更新啦.....", props);
}

export async function unmount(): Promise<void> {
    instance.$destroy();
    instance.$el.innerHTML = "";
    instance = null;
}
