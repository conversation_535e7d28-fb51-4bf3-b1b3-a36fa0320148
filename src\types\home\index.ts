// 1、导出 home 页公共声明和常量
// 2、导出当前项目私有的 home 声明和常量
// 公告数据接口类型
export type bbsContent = {
    actionEnable: string;
    channelId: string;
    createDate: string;
    displayOrder: string;
    doneDate: string;
    isRead: string;
    msgClassId: string;
    msgId: string;
    msgLink: string;
    msgTitle: string;
    msgType: string;
    sender: string;
    state: string;
    userId: string;
    // workspaceId: string; // 在 ctFetch 中配置了
};
export type messageContent = {
    content: string;
    createTime: string;
    domain: string;
    id: string;
    productCode: string;
    type: string;
};
// 首页公告
export type bbsFetchData = {
    total: string;
    list: bbsContent[];
};
// 域名消息
export type messageFetchData = {
    total: string;
    list: messageContent[];
};
// 计费详情
export type billingData = {
    account_id: string;
    billing_type: number;
    billing_type_cname: string;
    product_cname: string;
    product_code: string;
    product_type: string;
    resource_id: string;
    security_info: null;
    status: number;
};

// 概览 计费详情
export type BillingItem = {
    billingType: {
        billingType: string;
        billingTypeName: string;
        status: number; // 1: 已生效 2: 未生效
    };
    productCode: string;
    resourceType: string;
    overseaRegion: string;
};
