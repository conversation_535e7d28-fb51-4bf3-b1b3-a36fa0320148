<template>
    <div class="ct-edit-wrapper">
        <el-form
            label-width="140px"
            label-position="left"
            :model="udfScriptForm"
            :rules="rules"
            ref="udfScriptForm"
            :disabled="!isEdit || !isService"
        >
            <div v-if="!isPoweredByQiankun">
              <el-form-item :label="$t('domain.detail.label31')" class="link-wrapper">
                <template v-if="udfScriptForm.script && udfScriptForm.script.length">
                    <div class="link-button" v-for="item in udfScriptForm.script" :key="item.id">
                        <!-- 脚本名称: -->
                        <!-- 业务脚本未国际化，不作跳转 -->
                        <el-button type="text" :disabled="isCtclouds || isEn" @click="go(item.id, 'detail')">
                            {{ item.name }}
                        </el-button>
                    </div>
                </template>
                <div v-else>
                    <!-- 脚本名称: -->
                    <span class="tooltips">{{ $t("domain.detail.label83") }}</span>
                </div>
              </el-form-item>

            </div>

        </el-form>
    </div>
</template>

<script>
import minxin from "@/components/simpleform/minxin";
import { nUserModule } from "@/store/modules/nuser";
import { getLang } from "@/utils";

export default {
    name: "udfScript",
    components: {},
    mixins: [
      minxin
    ],
    props: {
        datas: Object,
        isPoweredByQiankun: Boolean,
    },
    data() {
        return {
            udfScriptForm: {
                script: [
                    {
                        id: "",
                        name: "",
                    },
                ],
            },

            rules: {},
        };
    },
    computed: {
      isCtclouds() {
        return nUserModule.isCtclouds;
      },
      isEn() {
        return getLang() === "en";
      },
    },
    watch: {
        datas: {
            deep: true,
            handler(val) {
                this.init(val);
            },
            immediate: true,
        },
    },
    methods: {
      init(v) {
        if (v?.script && v?.script?.length > 0) {
            this.udfScriptForm.script = v?.script;
        } else {
            this.udfScriptForm.script = [];
        }
      },
      go(id, type) {
        this.$router.push({
            name: `nscript.business.${type}`,
            query: {
                id: id,
                operateType: type,
            },
        });
      },
    },
};
</script>

<style lang="scss" scoped>
.ct-edit-wrapper {
    ::v-deep {
        .el-form--label-left .el-form-item__label {
            text-align: right;
        }
    }
}

.tooltips {
    color: $neutral-7;
}
</style>
