import index from "./index.vue";
import { RouteConfig } from "vue-router";

const indexRouter: RouteConfig = {
    path: "/index",
    name: "home",
    component: index,
    redirect: {
        // 子应用下找不到路由重定向到refresh，独立访问跳转到nhome
        name: (window as any).__POWERED_BY_QIANKUN__ ? "refresh" : "nhome",
    },
    meta: {
        breadcrumb: {
            title: "首页",
            route: ["home"],
        },
    },
    beforeEnter(to, from, next) {
        next();
    },
    children: [
        {
            path: "overview",
            name: "overview",
            component: () => import("./overview.vue"),
            meta: {
                isOnline: true,
                breadcrumb: {
                    title: "概览",
                    route: ["home", "overview"],
                },
            },
        },
    ],
};

export default indexRouter;
