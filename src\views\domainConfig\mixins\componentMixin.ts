import { SecurityAbilityModule } from "@/store/modules/securityAbility";
import { get } from "lodash-es";
import { nUserModule } from "@/store/modules/nuser";
import { Component, Vue } from "vue-property-decorator";
import { computed } from "vue";

@Component({
    name: "componentMixinTyped",
})
export default class ComponentMixinTyped extends Vue {
    get isEdit() {
        return SecurityAbilityModule.isEdit;
    }
    // 域名是否启用中
    get isService() {
        const status = get(SecurityAbilityModule.securityDomainInfo, "domainStatus");
        return status === "NORMAL";
    }
    get isPoweredByQiankun() {
        return window.__POWERED_BY_QIANKUN__;
    }
    get isCtclouds() {
        return nUserModule.isCtclouds;
    }
}


export function useComponentMixin() {
    const isEdit = computed(() => SecurityAbilityModule.isEdit);

    // 域名是否启用中
    const isService = computed(() => {
        const status = get(SecurityAbilityModule.securityDomainInfo, "domainStatus");
        return status === "NORMAL";
    });

    const isPoweredByQiankun = computed(() => window.__POWERED_BY_QIANKUN__);

    const isCtclouds = computed(() => nUserModule.isCtclouds);

    // 当前域名，用于传递参数
    const securityDomain = computed(() => {
        return get(SecurityAbilityModule.securityDomainInfo, "domain");
    });

    return {
        isEdit,
        isService,
        isPoweredByQiankun,
        isCtclouds,
        securityDomain
    };
}
