<template>
    <div class="chart-wrap">
        <div class="title-wrap">
            <div class="operate-btn">
                <el-radio-group v-model="chartType" size="small">
                    <el-radio-button
                        :label="$t('statistics.dcdn.privateNetworkAcceleratorWhole.radioBtn1')"></el-radio-button>
                    <el-radio-button
                        :label="$t('statistics.dcdn.privateNetworkAcceleratorWhole.radioBtn2')"></el-radio-button>
                </el-radio-group>
            </div>
            <template v-if="showBandwidth">
                <div class="total">
                    <div class="tip">
                        {{ titlePrefix }}
                        {{ $t("statistics.common.vchartTip1", { type: "", num: "" }) }}{{ titleSuffix }}：
                        <span class="num">
                            {{ fetchData1.topBandwidth | convertBandwidthB2P(scale) }}
                        </span>
                        <span class="date">{{ fetchData1.topBandwidthTime || "" }}</span>
                    </div>
                    <div class="tip">
                        {{ titlePrefix }}
                        {{ $t("statistics.common.vchartTip2", { type: "", num: "" }) }}{{ titleSuffix }}：
                        <span class="num">
                            {{ fetchData1.top95Bandwidth | convertBandwidthB2P(scale) }}
                        </span>
                    </div>
                </div>
                <template v-if="isWebsocket">
                    <div class="total">
                        <div class="tip">
                            {{ $t("statistics.common.vchartTip1", { type: "http(s)", num: "" })
                            }}{{ titleSuffix }}：
                            <span class="num">
                                {{ fetchData3.topBandwidth | convertBandwidthB2P(scale) }}
                            </span>
                            <span class="date">{{ fetchData3.topBandwidthTime || "" }}</span>
                        </div>
                        <div class="tip">
                            {{ $t("statistics.common.vchartTip2", { type: "http(s)", num: "" })
                            }}{{ titleSuffix }}：
                            <span class="num">
                                {{ fetchData3.top95Bandwidth | convertBandwidthB2P(scale) }}
                            </span>
                        </div>
                    </div>
                    <div class="total">
                        <div class="tip">
                            {{ $t("statistics.common.vchartTip1", { type: "websocket", num: "" })
                            }}{{ titleSuffix }}：
                            <span class="num">
                                {{ fetchData5.topBandwidth | convertBandwidthB2P(scale) }}
                            </span>
                            <span class="date">{{ fetchData3.topBandwidthTime || "" }}</span>
                        </div>
                        <div class="tip">
                            {{ $t("statistics.common.vchartTip2", { type: "websocket", num: "" })
                            }}{{ titleSuffix }}：
                            <span class="num">
                                {{ fetchData5.top95Bandwidth | convertBandwidthB2P(scale) }}
                            </span>
                        </div>
                    </div>
                </template>
                <template v-if="useCompare">
                    <div class="total">
                        <div class="tip">
                            {{ titlePrefix }}
                            {{ $t("statistics.common.vchartTip1", { type: "", num: "2" }) }}：
                            <span class="num">
                                {{ fetchData2.topBandwidth | convertBandwidthB2P(scale) }}
                            </span>
                            <span class="date">{{ fetchData2.topBandwidthTime || "" }}</span>
                        </div>
                        <div class="tip">
                            {{ titlePrefix }}
                            {{ $t("statistics.common.vchartTip2", { type: "", num: "2" }) }}：
                            <span class="num">
                                {{ fetchData2.top95Bandwidth | convertBandwidthB2P(scale) }}
                            </span>
                        </div>
                    </div>
                    <template v-if="isWebsocket">
                        <div class="total">
                            <div class="tip">
                                {{ $t("statistics.common.vchartTip1", { type: "http(s)", num: "2" }) }}：
                                <span class="num">
                                    {{ fetchData4.topBandwidth | convertBandwidthB2P(scale) }}
                                </span>
                                <span class="date">{{ fetchData4.topBandwidthTime || "" }}</span>
                            </div>
                            <div class="tip">
                                {{ $t("statistics.common.vchartTip2", { type: "http(s)", num: "2" }) }}：
                                <span class="num">
                                    {{ fetchData4.top95Bandwidth | convertBandwidthB2P(scale) }}
                                </span>
                            </div>
                        </div>
                        <div class="total">
                            <div class="tip">
                                {{ $t("statistics.common.vchartTip1", { type: "websocket", num: "2" }) }}：
                                <span class="num">
                                    {{ fetchData6.topBandwidth | convertBandwidthB2P(scale) }}
                                </span>
                                <span class="date">{{ fetchData4.topBandwidthTime || "" }}</span>
                            </div>
                            <div class="tip">
                                {{ $t("statistics.common.vchartTip2", { type: "websocket", num: "2" }) }}：
                                <span class="num">
                                    {{ fetchData6.top95Bandwidth | convertBandwidthB2P(scale) }}
                                </span>
                            </div>
                        </div>
                    </template>
                </template>
            </template>
            <template v-else>
                <div class="total">
                    <div class="tip">
                        {{ $t("statistics.common.vchartTip3", { type: "", num: "" }) }}{{ titleSuffix }}：
                        <span class="num">{{ fetchData1.totalFlow | convertFlowB2P(scale) }}</span>
                    </div>
                    <div class="tip" v-if="useCompare">
                        {{ $t("statistics.common.vchartTip3", { type: "", num: "2" }) }}：
                        <span class="num">{{ fetchData2.totalFlow | convertFlowB2P(scale) }}</span>
                    </div>
                </div>
                <template v-if="isWebsocket">
                    <div class="total">
                        <div class="tip">
                            {{ $t("statistics.common.vchartTip3", { type: "http(s)", num: "" })
                            }}{{ titleSuffix }}：
                            <span class="num">{{ fetchData3.totalFlow | convertFlowB2P(scale) }}</span>
                        </div>
                        <div class="tip" v-if="useCompare">
                            {{ $t("statistics.common.vchartTip3", { type: "http(s)", num: "2" }) }}:
                            <span class="num">{{ fetchData4.totalFlow | convertFlowB2P(scale) }}</span>
                        </div>
                    </div>
                    <div class="total">
                        <div class="tip">
                            {{ $t("statistics.common.vchartTip3", { type: "websocket", num: "" })
                            }}{{ titleSuffix }}：
                            <span class="num">{{ fetchData5.totalFlow | convertFlowB2P(scale) }}</span>
                        </div>
                        <div class="tip" v-if="useCompare">
                            {{ $t("statistics.common.vchartTip3", { type: "websocket", num: "2" }) }}：
                            <span class="num">{{ fetchData6.totalFlow | convertFlowB2P(scale) }}</span>
                        </div>
                    </div>
                </template>
            </template>
        </div>

        <v-chart class="chart" v-loading="loading" :element-loading-text="$t('statistics.common.chart.loading')"
            autoresize theme="cdn" :options="options" @legendselectchanged="legendselectchanged" />
        <div v-for="(item, index) in dailyList" :key="index">
            <ct-tip>
                {{ $t("statistics.dcdn.privateNetworkAcceleratorWhole.tableTip", {
                    type: index ? dailyType(index) : ''
                }) }}
                <el-tooltip effect="dark" :content="$t('statistics.common.searchDownloadContent')" placement="right">
                    <i class="el-icon-download usage-download-icon" @click="downloadTable(index)"></i>
                </el-tooltip>
            </ct-tip>

            <el-table :data="item.daily" v-loading="loading"
                :element-loading-text="$t('statistics.common.table.loading')" :empty-text="$t('common.table.empty')">
                <el-table-column :label="$t('statistics.dcdn.privateNetworkAcceleratorWhole.tableColumn1')" prop="date"
                    :sortable="true"></el-table-column>
                <el-table-column :label="$t('statistics.dcdn.privateNetworkAcceleratorWhole.tableColumn2', {
                    type: index ? dailyType(index) : '',
                    unit: wrapWithBrackets(indentFlowConfig.unit),
                })
                    ">
                    <template slot-scope="{ row }">
                        {{ (row.flow / indentFlowConfig.scale).toFixed(2) }}
                    </template>
                </el-table-column>
                <el-table-column :label="$t('statistics.dcdn.privateNetworkAcceleratorWhole.tableColumn3', {
                    unit: wrapWithBrackets(indentQueryBandwidthConfig.unit),
                })
                    ">
                    <template slot-scope="{ row }">
                        {{ (row.topBandwidth / indentQueryBandwidthConfig.scale).toFixed(2) }}
                    </template>
                </el-table-column>
                <el-table-column :label="$t('statistics.dcdn.privateNetworkAcceleratorWhole.tableColumn4', {
                    type: index ? dailyType(index) : '',
                })
                    ">
                    <template slot-scope="{ row }">
                        {{ row.topBandwidthTime | timeFormat }}
                    </template>
                </el-table-column>
            </el-table>
        </div>
    </div>
</template>

<script lang="ts">
import { Component } from "vue-property-decorator";
import { mixins } from "vue-class-component";
import { StatisticsUsageUrl } from "@/config/url/statistics";
import { downloadCsv } from "@/utils";
import { timeFormat } from "@/filters/index";
import { Query5Min, SearchParams } from "@/types/statistics/usage";
import { QueryFetchData } from "@/types/statistics/usage";
import { THEME_AREA_STYLE, themeColorArr } from "@/config/echart/cdn-theme";
import { ProductCodeEnum } from "@/config/map";
import ChartMixin from "../chartMixin";
import { nUserModule } from "@/store/modules/nuser";
import { get } from "lodash-es";
import { cloneDeep } from "lodash-es";

type tooltipParam = { name: string; marker: string; value: string | number; seriesName: string; seriesIndex: number };

const defaultFetchData: QueryFetchData = {
    "5min": [],
    daily: [],
    topBandwidth: 0,
    top95Bandwidth: 0,
    totalFlow: 0,
    avgQueryFlow: 0,
    dailyPeakMonthlyAverage: 0,
    topBandwidthTime: 0,
};

@Component({
    name: "PrivateNetworkAcceleratorWhole",
})
export default class PrivateNetworkAcceleratorWhole extends mixins(ChartMixin) {
    chartType = this.$t("statistics.dcdn.privateNetworkAcceleratorWhole.chartType");
    isWebsocket = false; //是否为websocket产品
    useCompare = false; // 是否使用了数据对比
    // 缓存请求参数
    private searchParams2?: SearchParams;
    // 接口数据（总1）
    fetchData1: QueryFetchData = {
        ...defaultFetchData,
    };
    // 接口数据（总2，对比用）
    fetchData2: QueryFetchData = {
        ...defaultFetchData,
    };
    // 以下只有 全站加速-websocket 会用到，此时需要额外2组数据
    // 接口数据（http）
    fetchData3: QueryFetchData = {
        ...defaultFetchData,
    };
    // 接口数据（http，对比用）
    fetchData4: QueryFetchData = {
        ...defaultFetchData,
    };
    // 接口数据（webspcket）
    fetchData5: QueryFetchData = {
        ...defaultFetchData,
    };
    // 接口数据（webspcket，对比用）
    fetchData6: QueryFetchData = {
        ...defaultFetchData,
    };
    protected downloadDataList: QueryFetchData["5min"] = []; // 用于下载的 总数据
    protected downloadDataList2: QueryFetchData["5min"] = []; // 用于下载的 https 数据
    protected downloadDataList3: QueryFetchData["5min"] = []; // 用于下载的 websocket 数据
    private timeMinus?: number; // 时间差
    private legend1Selected = true; // 是否选择了图例1

    // 请求生成，type： flow-流量带宽数据 request-请求数 status-回源数据
    private async localFetchGenerator<T>(params: SearchParams, busiType?: number[]) {
        // 当 websocket 选项时由于要请求三次（总[]、http[0]、websocket[2]）所以要根据传递指定，不传的话，默认会是[2]
        if (this.isWebsocket) params.busiType = busiType;
        const rst = await this.fetchGenerator<T>(StatisticsUsageUrl.dedicatedLineList, {
            ...params,
        });

        return rst;
    }

    // 当前展示是否为带宽（流量）
    get showBandwidth() {
        return this.chartType === this.$t("statistics.dcdn.privateNetworkAcceleratorWhole.chartType");
    }

    // 获取 seriesData 所使用的 key
    get seriesDataKey() {
        return this.showBandwidth ? "bandwidth" : "flow";
    }

    // 标题1的前缀（不同子产品展示会有差异）
    get titlePrefix() {
        return this.isWebsocket ? this.$t("statistics.dcdn.privateNetworkAcceleratorWhole.dailyType") : "";
    }

    // 标题1的后缀（存在数据对比时需要）
    get titleSuffix() {
        return this.useCompare ? "1" : "";
    }

    // 获取天粒度
    get dailyList() {
        return this.isWebsocket ? [this.fetchData1, this.fetchData3, this.fetchData5] : [this.fetchData1];
    }

    // 根据最大值获取图表的单位规则 { 单位，缩进 }
    get chartUnitConfig() {
        // 单数据 or 多数据
        let max = 0,
            max1 = 0,
            max2 = 0,
            max3 = 0,
            max4 = 0,
            max5 = 0,
            max6 = 0;

        max1 = this.getMaxFromList(get(this.fetchData1, "5min", []), this.seriesDataKey);

        if (this.isWebsocket) {
            max3 = this.getMaxFromList(get(this.fetchData3, "5min", []), this.seriesDataKey);
            max5 = this.getMaxFromList(get(this.fetchData5, "5min", []), this.seriesDataKey);
        }
        if (this.useCompare) {
            max2 = this.getMaxFromList(get(this.fetchData2, "5min", []), this.seriesDataKey);
            if (this.isWebsocket) {
                max4 = this.getMaxFromList(get(this.fetchData4, "5min", []), this.seriesDataKey);
                max6 = this.getMaxFromList(get(this.fetchData6, "5min", []), this.seriesDataKey);
            }
        }
        max = Math.max(max1, max2, max3, max4, max5, max6);

        return this.showBandwidth ? this.getBandwidthUnitConfig(max) : this.getFlowUnitConfig(max);
    }

    dailyType(index: number) {
        return [
            `${this.$t("statistics.dcdn.privateNetworkAcceleratorWhole.dailyType")}`,
            "http(s)",
            "websocket",
        ][index];
    }

    // 1、数据请求
    protected async getData(params1: SearchParams, params2: SearchParams) {
        // 获取加速类型
        this.isWebsocket = params1.product?.[0] === ProductCodeEnum.Socket;
        // 有参数2，说明开启了对齐
        this.useCompare = !!params2;

        // 缓存参数
        this.searchParams2 = params2;

        // 天粒度回源数据合并到总数据中的处理方法
        // type DailyDataItem = { topMissBandwidth: number; topMissBandwidthTimestamp: number };
        // function _dailyDataCopy(a: DailyDataItem, b: DailyDataItem) {
        //     a.topMissBandwidth = b.topMissBandwidth;
        //     a.topMissBandwidthTimestamp = b.topMissBandwidthTimestamp;
        // }

        // 合并两组信息并去重
        function _dailyMerge(fetchData1: QueryFetchData, fetchData2: QueryFetchData) {
            if (!fetchData1?.daily?.length || !fetchData2?.daily?.length) return;

            fetchData1.daily = fetchData1.daily
                ?.concat(fetchData2.daily)
                .sort((a, b) => Number(new Date(a.date)) - Number(new Date(b.date)));
            for (let i = 1; i < fetchData1.daily?.length; i++) {
                if (fetchData1.daily[i].date === fetchData1.daily[i - 1].date) {
                    fetchData1.daily.splice(i, 1);
                }
            }
        }

        if (!this.useCompare) {
            //请求数据，趋势图和天粒度
            [this.fetchData1] = await Promise.all([this.localFetchGenerator<QueryFetchData>(params1)]);
            if (!this.fetchData1) this.fetchData1 = cloneDeep(defaultFetchData);

            if (this.isWebsocket) {
                [this.fetchData3, this.fetchData5] = await Promise.all([
                    this.localFetchGenerator<QueryFetchData>(params1, [0]),
                    this.localFetchGenerator<QueryFetchData>(params1, [2]),
                ]);

                if (!this.fetchData3) this.fetchData3 = cloneDeep(defaultFetchData);
                if (!this.fetchData5) this.fetchData5 = cloneDeep(defaultFetchData);
            }
        } else {
            // 记录时间差，防止清空时间后tooltip报错
            this.timeMinus = (params1.startTime - params2.startTime) * 1000;
            [this.fetchData1, this.fetchData2] = await Promise.all([
                this.localFetchGenerator<QueryFetchData>(params1),
                this.localFetchGenerator<QueryFetchData>(params2),
            ]);

            if (!this.fetchData1) this.fetchData1 = cloneDeep(defaultFetchData);
            if (!this.fetchData2) this.fetchData2 = cloneDeep(defaultFetchData);

            if (this.isWebsocket) {
                [this.fetchData3, this.fetchData4, this.fetchData5, this.fetchData6] = await Promise.all([
                    this.localFetchGenerator<QueryFetchData>(params1, [0]),
                    this.localFetchGenerator<QueryFetchData>(params2, [0]),
                    this.localFetchGenerator<QueryFetchData>(params1, [2]),
                    this.localFetchGenerator<QueryFetchData>(params2, [2]),
                ]);

                if (!this.fetchData3) this.fetchData3 = cloneDeep(defaultFetchData);
                if (!this.fetchData4) this.fetchData4 = cloneDeep(defaultFetchData);
                if (!this.fetchData5) this.fetchData5 = cloneDeep(defaultFetchData);
                if (!this.fetchData6) this.fetchData6 = cloneDeep(defaultFetchData);
            }
            // 合并两组信息并去重
            _dailyMerge(this.fetchData1, this.fetchData2);

            if (this.isWebsocket) {
                _dailyMerge(this.fetchData3, this.fetchData4);
                _dailyMerge(this.fetchData5, this.fetchData6);
            }
        }

        // 处理用于下载的数据
        this.downloadDataList = !this.useCompare
            ? get(this.fetchData1, "5min", [])
            : get(this.fetchData2, "5min", []).concat(get(this.fetchData1, "5min", []));
        if (this.isWebsocket) {
            this.downloadDataList2 = !this.useCompare
                ? get(this.fetchData3, "5min", [])
                : get(this.fetchData4, "5min", []).concat(get(this.fetchData3, "5min", []));
            this.downloadDataList3 = !this.useCompare
                ? get(this.fetchData5, "5min", [])
                : get(this.fetchData6, "5min", []).concat(get(this.fetchData5, "5min", []));
        }
    }

    // 2、数据处理
    get options() {
        // 有4种情况
        // 1)只有一条曲线
        // 2)有两条曲线，对比曲线
        // 2)有3条曲线，websocket曲线
        // 3)有6条曲线，包含对比曲线和websocket曲线

        // 12 的名字前缀、135 的名字后缀
        const { titlePrefix, titleSuffix } = this;

        // 获取变量，减少计算
        const { useCompare, isWebsocket, seriesDataKey, legend1Selected } = this;
        const { fetchData1, fetchData2, fetchData3, fetchData4, fetchData5, fetchData6 } = this;

        // 根据 switch 获取差异化数据
        const title = this.showBandwidth
            ? this.$t("statistics.dcdn.privateNetworkAcceleratorWhole.vchartOptions.title1")
            : this.$t("statistics.dcdn.privateNetworkAcceleratorWhole.vchartOptions.title2");
        const { unit, scale } = this.chartUnitConfig;
        const yAxisName = this.$t("statistics.dcdn.privateNetworkAcceleratorWhole.vchartOptions.yAxisName", {
            unit: unit,
        });

        const xAxisData: string[] = [];
        const seriesData: string[] = [];
        const fetchDataList = get(fetchData1, "5min", []) || [];

        // 填充 chart 曲线数据
        function _fillChartData(
            fetchDataList: Query5Min[],
            seriesData: string[],
            seriesDataKey: "bandwidth" | "flow",
            xAxisData?: string[]
        ) {
            fetchDataList
                .sort((a, b) => a.timestamp - b.timestamp)
                .forEach(item => {
                    // 需要的时候再处理
                    xAxisData && xAxisData.push(timeFormat(item.timestamp * 1000).replace(" ", "\n"));
                    seriesData.push(((item[seriesDataKey] || 0) / scale).toFixed(2));
                });
        }

        // 第一种情况，只有一条线（未对比，不是 websocket）
        _fillChartData(fetchDataList, seriesData, seriesDataKey, xAxisData);

        const space = nUserModule.lang === "en" ? " " : "";

        const options = {
            ...this.comOptions,
            tooltip: {
                trigger: "axis",
                formatter: (a: tooltipParam[]) =>
                    `${a[0].name}<br>${a[0].marker}${space}${title}: ${a[0].value}${unit}`,
            },
            xAxis: {
                type: "category", // 类目轴，从 data 中获取数据
                boundaryGap: false, // 坐标轴两边留白策略
                axisLabel: {
                    formatter: this.xAxisFormatterGenerator(),
                },
                data: xAxisData,
            },
            yAxis: {
                type: "value", // 数值轴
                name: yAxisName,
            },
            series: [
                {
                    name: `${this.$t(
                        "statistics.dcdn.privateNetworkAcceleratorWhole.dailyType"
                    )}${space}${title}${titleSuffix}`,
                    type: "line",
                    data: seriesData,
                    areaStyle: THEME_AREA_STYLE[themeColorArr[0]],
                },
            ],
        };
        // 但凡多一条线，都需要给第一条线增加图例
        if (useCompare || isWebsocket) {
            options.legend = {
                data: [
                    `${this.$t(
                        "statistics.dcdn.privateNetworkAcceleratorWhole.dailyType"
                    )}${space}${title}${titleSuffix}`,
                ],
            };
        }

        // 第二种情况，三条线，websockt
        if (isWebsocket) {
            const seriesData3: string[] = [];
            const fetchDataList3 = get(fetchData3, "5min", []);
            _fillChartData(fetchDataList3, seriesData3, seriesDataKey);
            const seriesData5: string[] = [];
            const fetchDataList5 = get(fetchData5, "5min", []);
            _fillChartData(fetchDataList5, seriesData5, seriesDataKey);

            // 增加 series
            options.series.push(
                {
                    name: `http(s) ${title}${titleSuffix}`,
                    type: "line",
                    data: seriesData3,
                    areaStyle: THEME_AREA_STYLE[themeColorArr[1]],
                },
                {
                    name: `websocket ${title}${titleSuffix}`,
                    type: "line",
                    data: seriesData5,
                    areaStyle: THEME_AREA_STYLE[themeColorArr[2]],
                }
            );

            // 增加图例
            options.legend.data.push(`http(s) ${title}${titleSuffix}`, `websocket ${title}${titleSuffix}`);
        }

        // 第三种情况，2条线，有对比
        if (useCompare) {
            const seriesData2: string[] = [];
            const fetchDataList2 = get(fetchData2, "5min", []);
            _fillChartData(fetchDataList2, seriesData2, seriesDataKey);

            // 增加 series
            options.series.push({
                name: `${this.$t("statistics.dcdn.privateNetworkAcceleratorWhole.dailyType")}${space}${title}2`,
                type: "line",
                data: seriesData2,
                areaStyle: THEME_AREA_STYLE[themeColorArr[3]],
            });

            // 增加图例
            options.legend.data.push(
                `${this.$t("statistics.dcdn.privateNetworkAcceleratorWhole.dailyType")}${space}${title}2`
            );

            // 第四种情况，6条线，有对比+weboskct
            if (isWebsocket) {
                const seriesData4: string[] = [];
                const fetchDataList4 = get(fetchData4, "5min", []);
                _fillChartData(fetchDataList4, seriesData4, seriesDataKey);
                const seriesData6: string[] = [];
                const fetchDataList6 = get(fetchData6, "5min", []);
                _fillChartData(fetchDataList6, seriesData6, seriesDataKey);

                // 增加 series
                options.series.push(
                    {
                        name: `http(s) ${title}2`,
                        type: "line",
                        data: seriesData4,
                        areaStyle: THEME_AREA_STYLE[themeColorArr[1]],
                    },
                    {
                        name: `websocket ${title}2`,
                        type: "line",
                        data: seriesData6,
                        areaStyle: THEME_AREA_STYLE[themeColorArr[2]],
                    }
                );

                // 增加图例
                options.legend.data.push(`http(s) ${title}2`, `websocket ${title}2`);
            }
        }
        // tooltip 叫繁琐，拎出来处理
        // 调整 tooltip ，动态控制 tooltip ，如果图例只选择一条时就只显示一组信息
        options.tooltip.formatter = (a: tooltipParam[]) => {
            // a[0].name带有换行符号，在firefox下new Date时会异常
            const arr1: string[] = [], arr2: string[] = []
            let name1 = "", name2 = ""
            const splitArr = this.isWebsocket ? [0, 1, 2] : [0]
            a.forEach(data => {
                const str = `<br>${data.marker}${data.seriesName}: ${data.value}${unit}`
                if (splitArr.includes(data.seriesIndex)) {
                    arr1.push(str)
                    !name1 && (name1 = timeFormat(
                        Number(new Date(Number(new Date(data.name.replace("\n", " ")))))
                    ));
                } else {
                    arr2.push(str)
                    !name2 && (name2 = timeFormat(
                        Number(new Date(Number(new Date(data.name.replace("\n", " "))) - (this.timeMinus! || 0)))
                    ));
                }
            })
            return name1 + arr1.join("") + (arr1.length && "<br>") + name2 + arr2.join("")
        };

        // 处理完后返回 options
        return options;
    }

    // 监听图例选择变化
    legendselectchanged({ selected }: { selected: { [legend: string]: boolean } }) {
        const title = this.showBandwidth
            ? this.$t("statistics.dcdn.privateNetworkAcceleratorWhole.vchartOptions.title1")
            : this.$t("statistics.dcdn.privateNetworkAcceleratorWhole.vchartOptions.title2");
        this.legend1Selected = selected[`${title}1`];
    }

    // 获取各种缩进计算的配置（根据最大值获取）
    get indentQueryBandwidthConfig() {
        const max = this.getMaxFromList(get(this.fetchData1, "daily", []), "topBandwidth");
        return this.getBandwidthUnitConfig(max, true);
    }
    get indentFlowConfig() {
        const max = this.getMaxFromList(get(this.fetchData1, "daily", []), "flow");
        return this.getFlowUnitConfig(max, true);
    }

    // 重写 excel 数据拼接方法
    protected tableToExcel() {
        const { showBandwidth, isWebsocket } = this;
        // 表头
        let str = `${this.$t("statistics.dcdn.privateNetworkAcceleratorWhole.tableToExcel.excelColumn1")},${showBandwidth
            ? `${this.$t("statistics.dcdn.privateNetworkAcceleratorWhole.tableToExcel.excelColumn2", {
                mbps: this.Mbps,
            })}`
            : `${this.$t("statistics.dcdn.privateNetworkAcceleratorWhole.tableToExcel.excelColumn3", {
                mb: this.MB,
            })}`
            }`;

        if (this.isWebsocket) {
            // str += `,${showBandwidth ? `http(s)带宽(${this.Mbps})` : `http(s)流量(${this.MB})`}`;
            str += `,${showBandwidth
                ? `${this.$t("statistics.dcdn.privateNetworkAcceleratorWhole.tableToExcel.excelColumn4", {
                    type: "http(s)",
                    mbps: this.Mbps,
                })}`
                : `${this.$t("statistics.dcdn.privateNetworkAcceleratorWhole.tableToExcel.excelColumn5", {
                    type: "http(s)",
                    mb: this.MB,
                })}`
                }`;
            // str += `,${showBandwidth ? `websocket带宽(${this.Mbps})` : `websocket流量(${this.MB})`}`;
            str += `,${showBandwidth
                ? `${this.$t("statistics.dcdn.privateNetworkAcceleratorWhole.tableToExcel.excelColumn4", {
                    type: "websocket",
                    mbps: this.Mbps,
                })}`
                : `${this.$t("statistics.dcdn.privateNetworkAcceleratorWhole.tableToExcel.excelColumn5", {
                    type: "websocket",
                    mb: this.MB,
                })}`
                }`;
        }

        str += "\n";

        // 输出格式
        const { downloadDataList, downloadDataList2, downloadDataList3 } = this;
        downloadDataList.forEach((item, index) => {
            str += `${timeFormat(item["timestamp"] * 1000)},`;
            str += (item[this.seriesDataKey] / Math.pow(this.scale, 2)).toFixed(2);
            if (isWebsocket) {
                str += ",";
                str += (downloadDataList2[index][this.seriesDataKey] / Math.pow(this.scale, 2)).toFixed(2);
                str += ",";
                str += (downloadDataList3[index][this.seriesDataKey] / Math.pow(this.scale, 2)).toFixed(2);
            }
            str += "\n";
            return str;
        }, "");

        // 增加峰值带宽和95峰值带宽
        str += `\n${this.$t("statistics.dcdn.privateNetworkAcceleratorWhole.tableToExcel.excelColumn6")}\n`;

        const {
            titleSuffix,
            fetchData1: _fetchData1,
            fetchData2: _fetchData2,
            fetchData3: _fetchData3,
            fetchData4: _fetchData4,
            fetchData5: _fetchData5,
            fetchData6: _fetchData6
        } = this;

        const { fetchData1, fetchData2, fetchData3, fetchData4, fetchData5, fetchData6 } = {
            fetchData1: this.processQueryFetchData(_fetchData1),
            fetchData2: this.processQueryFetchData(_fetchData2),
            fetchData3: this.processQueryFetchData(_fetchData3),
            fetchData4: this.processQueryFetchData(_fetchData4),
            fetchData5: this.processQueryFetchData(_fetchData5),
            fetchData6: this.processQueryFetchData(_fetchData6),
        };

        if (this.showBandwidth) {
            const { topBandwidth, top95Bandwidth } = fetchData1;
            str += `${this.$t("statistics.dcdn.privateNetworkAcceleratorWhole.tableToExcel.excelColumn7", {
                type: "",
                num: "",
                suffix: titleSuffix,
            })} ,${topBandwidth} ${this.Mbps} \n${this.$t(
                "statistics.dcdn.privateNetworkAcceleratorWhole.tableToExcel.excelColumn8",
                {
                    type: "",
                    num: "",
                    suffix: titleSuffix,
                }
            )},${top95Bandwidth} ${this.Mbps} \n`;

            if (this.isWebsocket) {
                str += `${this.$t(
                    "statistics.dcdn.privateNetworkAcceleratorWhole.tableToExcel.excelColumn7",
                    {
                        type: "http(s)",
                        num: "",
                        suffix: titleSuffix,
                    }
                )},${fetchData3.topBandwidth} ${this.Mbps} \n${this.$t(
                    "statistics.dcdn.privateNetworkAcceleratorWhole.tableToExcel.excelColumn8",
                    {
                        type: "http(s)",
                        num: "",
                        suffix: titleSuffix,
                    }
                )},${fetchData3.top95Bandwidth} ${this.Mbps} \n`;
                str += `${this.$t(
                    "statistics.dcdn.privateNetworkAcceleratorWhole.tableToExcel.excelColumn7",
                    {
                        type: "websocket",
                        num: "",
                        suffix: titleSuffix,
                    }
                )},${fetchData5.topBandwidth} ${this.Mbps} \n${this.$t(
                    "statistics.dcdn.privateNetworkAcceleratorWhole.tableToExcel.excelColumn8",
                    {
                        type: "websocket",
                        num: "",
                        suffix: titleSuffix,
                    }
                )},${fetchData5.top95Bandwidth} ${this.Mbps} \n`;
            }

            if (this.useCompare) {
                str += `${this.$t(
                    "statistics.dcdn.privateNetworkAcceleratorWhole.tableToExcel.excelColumn7",
                    {
                        type: "",
                        num: "2",
                        suffix: "",
                    }
                )},${fetchData2.topBandwidth} ${this.Mbps} \n${this.$t(
                    "statistics.dcdn.privateNetworkAcceleratorWhole.tableToExcel.excelColumn8",
                    {
                        type: "",
                        num: "2",
                        suffix: "",
                    }
                )},${fetchData2.top95Bandwidth} ${this.Mbps} \n`;

                if (this.isWebsocket) {
                    str += `${this.$t(
                        "statistics.dcdn.privateNetworkAcceleratorWhole.tableToExcel.excelColumn7",
                        {
                            type: "http(s)",
                            num: "2",
                            suffix: "",
                        }
                    )},${fetchData4.topBandwidth} ${this.Mbps} \n${this.$t(
                        "statistics.dcdn.privateNetworkAcceleratorWhole.tableToExcel.excelColumn8",
                        {
                            type: "http(s)",
                            num: "2",
                            suffix: "",
                        }
                    )},${fetchData4.top95Bandwidth} ${this.Mbps} \n`;
                    str += `${this.$t(
                        "statistics.dcdn.privateNetworkAcceleratorWhole.tableToExcel.excelColumn7",
                        {
                            type: "websocket",
                            num: "2",
                            suffix: "",
                        }
                    )},${fetchData6.topBandwidth} ${this.Mbps} \n${this.$t(
                        "statistics.dcdn.privateNetworkAcceleratorWhole.tableToExcel.excelColumn8",
                        {
                            type: "websocket",
                            num: "2",
                            suffix: "",
                        }
                    )},${fetchData6.top95Bandwidth} ${this.Mbps} \n`;
                }
            }
        } else {
            const { totalFlow, avgQueryFlow } = fetchData1;
            str += `${this.$t("statistics.dcdn.privateNetworkAcceleratorWhole.tableToExcel.excelColumn9", {
                type: "",
                num: "",
                suffix: titleSuffix,
            })},${totalFlow} ${this.MB} \n${this.$t(
                "statistics.dcdn.privateNetworkAcceleratorWhole.tableToExcel.excelColumn10",
                {
                    type: "",
                    num: "",
                    suffix: titleSuffix,
                }
            )},${avgQueryFlow} ${this.MB} \n`;

            if (this.isWebsocket) {
                str += `${this.$t(
                    "statistics.dcdn.privateNetworkAcceleratorWhole.tableToExcel.excelColumn9",
                    {
                        type: "http(s)",
                        num: "",
                        suffix: titleSuffix,
                    }
                )},${fetchData3.totalFlow} ${this.MB} \n${this.$t(
                    "statistics.dcdn.privateNetworkAcceleratorWhole.tableToExcel.excelColumn10",
                    {
                        type: "http(s)",
                        num: "",
                        suffix: titleSuffix,
                    }
                )},${fetchData3.avgQueryFlow} ${this.MB} \n`;
                str += `${this.$t(
                    "statistics.dcdn.privateNetworkAcceleratorWhole.tableToExcel.excelColumn9",
                    {
                        type: "websocket",
                        num: "",
                        suffix: titleSuffix,
                    }
                )}${titleSuffix},${fetchData5.totalFlow} ${this.MB} \n${this.$t(
                    "statistics.dcdn.privateNetworkAcceleratorWhole.tableToExcel.excelColumn10",
                    {
                        type: "websocket",
                        num: "",
                        suffix: titleSuffix,
                    }
                )},${fetchData5.avgQueryFlow} ${this.MB} \n`;
            }

            if (this.useCompare) {
                str += `${this.$t(
                    "statistics.dcdn.privateNetworkAcceleratorWhole.tableToExcel.excelColumn9",
                    {
                        type: "",
                        num: "2",
                        suffix: "",
                    }
                )},${fetchData2.totalFlow} ${this.MB} \n${this.$t(
                    "statistics.dcdn.privateNetworkAcceleratorWhole.tableToExcel.excelColumn10",
                    {
                        type: "",
                        num: "2",
                        suffix: "",
                    }
                )},${fetchData2.avgQueryFlow} ${this.MB} \n`;

                if (this.isWebsocket) {
                    str += `${this.$t(
                        "statistics.dcdn.privateNetworkAcceleratorWhole.tableToExcel.excelColumn9",
                        {
                            type: "http(s)",
                            num: "2",
                            suffix: "",
                        }
                    )},${fetchData4.totalFlow} ${this.MB} \n${this.$t(
                        "statistics.dcdn.privateNetworkAcceleratorWhole.tableToExcel.excelColumn10",
                        {
                            type: "http(s)",
                            num: "2",
                            suffix: "",
                        }
                    )},${fetchData4.avgQueryFlow} ${this.MB} \n`;
                    str += `${this.$t(
                        "statistics.dcdn.privateNetworkAcceleratorWhole.tableToExcel.excelColumn9",
                        {
                            type: "websocket",
                            num: "2",
                            suffix: "",
                        }
                    )},${fetchData6.totalFlow} ${this.MB} \n${this.$t(
                        "statistics.dcdn.privateNetworkAcceleratorWhole.tableToExcel.excelColumn10",
                        {
                            type: "websocket",
                            num: "2",
                            suffix: "",
                        }
                    )},${fetchData6.avgQueryFlow} ${this.MB} \n`;
                }
            }
        }

        this.downloadExcel({
            name: `${this.$t("statistics.dcdn.privateNetworkAcceleratorWhole.tableToExcel.excelName", {
                chartType: this.chartType,
            })}`,
            str,
        });
    }
    // 重写下载方法
    protected downloadExcel({ name, str }: { name: string; str: string }) {
        // 此时要用缓存的参数2判断是否启用了数据对比，以保证参数和数据一致
        const useCompare = !!this.searchParams2;

        // 存在对比的时候，是从2个时间范围内选择时间点
        let { startTime, endTime } = this.searchParams;
        if (useCompare) {
            startTime = startTime < this.searchParams2!.startTime ? startTime : this.searchParams2!.startTime;

            endTime = endTime > this.searchParams2!.endTime ? endTime : this.searchParams2!.endTime;
        }

        const t1 = timeFormat(+startTime)
            .replace(/-|\s|:/g, "")
            .slice(0, 12);
        const t2 = timeFormat(+endTime)
            .replace(/-|\s|:/g, "")
            .slice(0, 12);

        downloadCsv(`${name}${t1}-${t2}`, str);
    }


    protected downloadTable(index: number) {
        if (this.checkListIsEmpty(this.dailyList?.[index]?.daily)) return;

        let str = `${this.$t("statistics.usageQuery.BandwidthFlow.tableColumn1")},${this.$t('statistics.dcdn.bandwidthFlowWhole.tableColumn2', {
            type: index ? this.dailyType(index) : '',
            unit: this.wrapWithBrackets(this.MB),
        })},${this.$t('statistics.usageQuery.BandwidthFlow.tableColumn3', {
            unit: this.wrapWithBrackets(this.Mbps),
        })},${this.$t('statistics.usageQuery.BandwidthFlow.tableColumn4')}\n`;

        this.dailyList[index]?.daily.forEach((item => {
            str += item.date + ",";
            str += (+item.flow / Math.pow(this.scale, 2)).toFixed(2) + ",";
            str += (+item.topBandwidth / Math.pow(this.scale, 2)).toFixed(2) + ",";
            str += timeFormat(item.topBandwidthTime) + ",\n";
        }))

        this.downloadExcel({
            name: `${this.$t("statistics.common.tab[9]")}-${this.$t("statistics.dcdn.privateNetworkAcceleratorWhole.tableTip", {
                type: index ? this.dailyType(index) : ''
            })}`,
            str
        })
    }
}
</script>

<style lang="scss" scoped></style>
