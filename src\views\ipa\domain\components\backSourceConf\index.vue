<template>
    <div class="back-source-conf">
        <!-- 表单区域 -->
        <el-form-item label="端口转发">
            <el-button type="primary" @click="handleAddGroup">+ 添 加</el-button>
            <div class="dynamic-box">
                <el-card
                    v-for="(item, key) in data"
                    :key="getRenderKey(key, item)"
                    class="dynamic-item"
                    :class="`dynamic-item-scroll-` + key"
                >
                    <div v-if="data.length > 1" class="action-box">
                        <el-button type="text" @click="handleDeleteGroup(key)">
                            删 除
                        </el-button>
                    </div>
                    <!--  源站-->
                    <el-form-item
                        label="源站"
                        label-width="80px"
                        required
                        :prop="`${fatherProp}.${key}.detail`"
                        :class="transFormPropToClassName(`${fatherProp}.${key}.detail`)"
                        :rules="detailRules"
                    >
                        <source-station
                            :all-data="data"
                            :data="getSourceStationData(item)"
                            :page-type="pageType"
                            @add="handleOperateOrigin(key)"
                            @edit="(row, index) => handleOperateOrigin(key, row, index)"
                        />
                    </el-form-item>
                    <!--  端口信息-->
                    <el-form-item
                        label="端口信息"
                        label-width="80px"
                        class="no-margin"
                        required
                        :prop="`${fatherProp}.${key}`"
                        :class="transFormPropToClassName(`${fatherProp}.${key}`)"
                        :rules="portsInfoRules"
                    >
                        <dynamic-port
                            :data="getPortData(item, 'tcp_ports')"
                            :fields="tcpPortFields"
                            :father-prop="`${fatherProp}.${key}.tcp_ports`"
                            :rules-map="tcpPortRulesMap"
                        />
                        <dynamic-port
                            :data="getPortData(item, 'udp_ports')"
                            :fields="udpPortFields"
                            :father-prop="`${fatherProp}.${key}.udp_ports`"
                            :rules-map="udpPortRulesMap"
                        />
                        <dynamic-port
                            :data="getPortData(item, 'http_ports')"
                            :fields="httpPortFields"
                            :father-prop="`${fatherProp}.${key}.http_ports`"
                            :rules-map="httpRulesMap"
                        />
                        <dynamic-port
                            :data="getPortData(item, 'https_ports')"
                            :fields="httpsPortFields"
                            :father-prop="`${fatherProp}.${key}.https_ports`"
                            :rules-map="httpsRulesMap"
                        />
                        <!--  ftp控制端口-->
                        <el-form-item :prop="`${fatherProp}.${key}.ftp_ports`" :rules="ftpRules" class="no-margin">
                            <port-item
                                :fields="ftpPortFields"
                                :form="item['ftp_ports']"
                                :father-prop="`${fatherProp}.${key}.ftp_ports`"
                                :rules-map="ftpPortRulesMap"
                                @change="handleFtpChange(`${fatherProp}.${key}`)"
                            />
                        </el-form-item>
                    </el-form-item>
                </el-card>
                <!--  提示信息-->
                <div class="tip-info-box">
                    <span class="tip-label">提示</span>
                    <div class="tip-content">
                        <div class="form-item-tip">
                            <ct-svg-icon
                                icon-class="info-circle"
                                class-name="alert-icon icon-style"
                            />
                            http协议的端口请配置在http复用端口；https协议且支持sni的端口请配置在https复用端口，不支持sni的请配置在tcp请求端口；ftp仅支持被动模式的ftp服务，被动模式的ftp控制端口请配置在ftp控制端口，数据端口请配置在ftp数据端口。
                        </div>
                        <div class="form-item-tip">
                            <ct-svg-icon
                                icon-class="info-circle"
                                class-name="alert-icon icon-style"
                            />
                            请求端口和回源端口均支持配置多个，不连续的端口使用逗号分隔，连续的端口间使用'-'号分隔，如100,1000-2000,2050；ftp控制端口、ftp数据端口均只能配置一个。
                        </div>
                        <div class="form-item-tip" v-if="tcpPortLimit || udpPortLimit">
                            <div>
                                <span>
                                    <ct-svg-icon
                                        icon-class="info-circle"
                                        class-name="alert-icon icon-style"
                                    />
                                </span>
                                以下端口为平台占用端口，TCP请求端口、http复用端口、https复用端口、ftp控制端口、ftp数据端口不允许配置如下TCP端口；UDP请求端口不允许配置如下UDP端口：
                                <div class="port-tip-style mgt8">
                                    <div>TCP: {{ tcpPortLimit }}</div>
                                    <div>UDP: {{ udpPortLimit }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </el-form-item>
        <!-- 源站弹窗 -->
        <origin-dialog
            :visible="originDialog.visible"
            :operateType="originDialog.operateType"
            :addedOriginList="originDialog.addedList"
            :payload="originDialog.payload"
            :originType="originType"
            @cancel="originDialog.visible = false"
            @handle="checkOriginData"
        />
    </div>
</template>

<script>
import ctSvgIcon from "@/components/ctSvgIcon/index.vue";
import sourceStation from "./components/sourceStation.vue";
import dynamicPort from "./components/dynamicPort.vue";
import portItem from "./components/portItem.vue";
import OriginDialog from "../originDialog.vue";

import { cloneDeep, get, set, has } from "lodash-es";
import { DomainUrl } from "@/config/url/ipa/domain";
import { checkSinglePort } from "@/utils/pattern";
import { forwardOriginItem } from "./templateData";
import { transFormPropToClassName } from "@/utils/utils";


export default {
    components: {ctSvgIcon, sourceStation, dynamicPort, portItem, OriginDialog },
    props: {
        data: {
            type: Array,
            default: () => []
        },
        fatherProp: {
            type: String,
            default: "forward_origins"
        },
        // 回源策略
        originType: {
            type: Number,
            default: 1
        },
        // 接入方式
        accessMode: {
            type: Number,
            default: 1
        },
        // 页面类型，分新增和编辑。某些操作需要在编辑二次确认
        pageType: {
            type: String,
            default: "add"
        }
    },
    inject: {
        elForm: {
            default: "",
        },
    },
    data() {
        return {
            // 源站校验规则
            detailRules: [
                { required: true, message: "请输入源站", trigger: "blur", type: "array" },
                {
                    validator: (rule, value, callback) => {
                        if (!value.filter(itm => itm.role === 1).length) {
                            return callback(new Error("必须存在一个主源"));
                        }

                        callback();
                    },
                },
            ],

            // 端口信息校验
            portsInfoRules: [{ validator: this.checkPortsInfo }],
            // ftp端口组校验
            ftpRules: [{ validator: this.checkFtpPort }],

            // 源站弹窗配置维护
            originDialog: {
                visible: false, // 是否弹窗
                operateType: "create", // 弹窗类型
                idx: null,
                addedList: [],
            },
            currentIndex: null, // 记录当前操作的组 index

            tcpPortFields: [
                { label: "TCP请求端口", prop: "req", change: this.handleReqChange },
                { label: "TCP回源端口", prop: "origin", disabled: this.isOriginDisabled }
            ],
            udpPortFields: [
                { label: "UDP请求端口", prop: "req", change: this.handleReqChange },
                { label: "UDP回源端口", prop: "origin", disabled: this.isOriginDisabled }
            ],
            httpPortFields: [
                { label: "http复用端口", prop: "req", change: this.handleReqChange },
                { label: "http回源端口", prop: "origin", disabled: this.isOriginDisabled }
            ],
            httpsPortFields: [
                { label: "https复用端口", prop: "req", change: this.handleReqChange },
                { label: "https回源端口", prop: "origin", disabled: this.isOriginDisabled }
            ],
            ftpPortFields: [
                { label: "ftp控制端口", prop: "control", change: this.handleControlChange },
                { label: "ftp数据端口", prop: "data", disabled: data => Boolean(!data.control) },
            ],

            // tcp端口校验规则
            tcpPortRulesMap: {
                req: [
                    // 连续性端口格式校验，后端做
                    // 端口重复性校验，后端做
                    { validator: (rule, value, callback) => this.checkNoneDomainAccessPort(rule, value, callback, "TCP请求端口") },
                    { validator: this.checkSystemTcpPort }
                ],
                origin: [{ validator: this.checkCommonMultiPort }],
            },
            // udp端口校验规则
            udpPortRulesMap: {
                req: [
                    { validator: this.checkCommonMultiPort },
                    { validator: this.checkSystemUdpPort }
                ],
                origin: [{ validator: this.checkCommonMultiPort }],
            },
            // http端口校验规则
            httpRulesMap: {
                req: [
                    { validator: (rule, value, callback) => this.checkNoneDomainAccessPort(rule, value, callback, "http复用端口") },
                    { validator: this.checkSystemTcpPort }
                ],
                origin: [{ validator: this.checkCommonMultiPort }],
            },
            // https端口校验规则
            httpsRulesMap: {
                req: [
                    { validator: (rule, value, callback) => this.checkNoneDomainAccessPort(rule, value, callback, "https复用端口") },
                    { validator: this.checkSystemTcpPort }
                ],
                origin: [{ validator: this.checkCommonMultiPort }],
            },
            // ftp端口校验
            ftpPortRulesMap: {
                control: [
                    { validator: checkSinglePort },
                    { validator: (rule, value, callback) => this.checkSingleNoneDomainAccessPort(rule, value, callback, "ftp控制端口") },
                    { validator: this.checkSystemTcpPort }
                ],
                data: [
                    { validator: checkSinglePort },
                    { validator: (rule, value, callback) => this.checkSingleNoneDomainAccessPort(rule, value, callback, "ftp数据端口") },
                    { validator: this.checkSystemTcpPort }
                ]
            },

            // 端口校验规则
            portCheckRules: null,
        }
    },
    computed: {
        tcpPortLimit() {
            const data = get(this.portCheckRules, "port_tcp_checker", []) || [];
            return data.join(",");
        },
        udpPortLimit() {
            const data = get(this.portCheckRules, "port_udp_checker", []) || [];
            return data.join(",");
        },
    },
    watch: {
        /**
         * 监听回源策略变化，引起源站权重变化
         * @param val
         */
        originType(val) {
            const matched = [1, 3];
            if (!matched.includes(val)) return;

            this.data.forEach(groupItem => {
                const detail = get(groupItem, "detail", []) || [];
                detail.forEach(item => {
                    item.weight = 10;
                })
            })
        }
    },
    methods: {
        /**
         * 渲染key值，vue更新机制需要
         * @param key
         */
        getRenderKey(key, item) {
            return key + (item.renderId || '').toString()
        },
        /**
         * 添加组
         */
        handleAddGroup() {
            if (this.data.length >= 10) {
                this.$message.error("最多允许配置10组端口转发业务");
                return;
            }

            const info = cloneDeep(forwardOriginItem);
            info.renderId = +new Date();
            this.data.push(info);

            // 等待产品确认，是否需要自动定位到最新元素上
            this.$nextTick(() => {
                const key = this.data.length - 1;
                const ele = document.querySelector(`.dynamic-item-scroll-${key}`);
                ele && ele.scrollIntoView({ behavior: "smooth" });
            })
        },
        /**
         * 删除组
         */
        handleDeleteGroup(index) {
            this.data.splice(index, 1);
        },
        /**
         * 获取源站数据
         * @param item
         * @returns {*|*[]}
         */
        getSourceStationData(item) {
            return get(item, "detail", []) || [];
        },
        /**
         * 添加/编辑源站 回调函数
         * @param payload
         */
        checkOriginData(payload) {
            if (!payload) return;

            const data = get(this.data, `[${this.currentIndex}].detail`, []) || [];
            if (payload.type === "create") {
                data.push(payload.payload);
            }

            if (payload.type === "edit") {
                if (this.originDialog.idx === null) return;
                data.splice(this.originDialog.idx, 1, payload.payload);
                this.originDialog.idx = null;
            }

            this.originDialog.visible = false;

            // 等一个dom更新后回调
            this.$nextTick(() => {
                const prop = `${this.fatherProp}.${this.currentIndex}.detail`;
                this.elForm && this.elForm.validateField(prop);
            })
        },
        /**
         * 处理源站新增/编辑
         * @param index 当前处理的组index。这是因为后端数据结构变化，为了优化性能，弹窗提到组件外层。所以需要记录当前操作的组index，方便后续为数据赋值
         * @param row
         * @param idx
         */
        handleOperateOrigin(index, row, idx) {
            this.currentIndex = index;
            this.originDialog.payload = null;
            const currentDetail = get(this.data, `[${index}.detail]`);
            this.originDialog.addedList = cloneDeep(currentDetail);

            if (row) {
                this.originDialog.operateType = "edit";
                this.originDialog.payload = row;
                this.originDialog.idx = idx;
                // 去除本身那条数据
                this.originDialog.addedList.splice(idx, 1);
            } else {
                this.originDialog.operateType = "create";
            }
            this.originDialog.visible = true;
        },
        /**
         * 获取端口数据
         */
        getPortData(item, prop) {
            return get(item, prop, []) || [];
        },
        /**
         * 校验端口信息组
         */
        checkPortsInfo(rule, value, callback) {
            // 端口必填校验改为tcp请求端口、http复用端口、https复用端口、udp请求端口、ftp控制端口，五者至少配置一个，否则不允许提交。
            // 五组数据
            const arr = [
                { prop: "tcp_ports", field: "req" },
                { prop: "udp_ports", field: "req" },
                { prop: "http_ports", field: "req" },
                { prop: "https_ports", field: "req" },
                { prop: "ftp_ports", field: "control" },
            ];

            const res = arr.some(item => {
                const prop = item.prop;
                const field = item.field;
                const data = get(value, prop);
                // 数组只需要某个有值即可
                if (data instanceof Array) {
                    return data.some(info => Boolean(info[field]));
                }

                return Boolean(data[field]);
            })

            return res ? callback() : callback(new Error("TCP请求端口、UDP请求端口、http复用端口、https复用端口、ftp控制端口，五者至少配置一个"));
        },
        /**
         * 校验ftp端口
         */
        checkFtpPort(rule, value, callback) {
            const values = Object.values(value);
            const hasValue = values.some(item => item);
            if (!hasValue) {
                return callback();
            }

            const res = values.every(item => Boolean(item));
            return res ? callback() : callback(new Error("ftp控制端口和ftp数据端口要配必须同时配，不能只配一个"));
        },
        /**
         * 回源端口是否禁用
         * @param data 当前表单值
         * @returns {*}
         */
        isOriginDisabled(data) {
            return Boolean(!data.req)
        },
        /**
         * 请求端口改变
         */
        handleReqChange(val, data) {
            if (val) return;

            set(data, "origin", "");
        },
        /**
         * 处理控制端口变化
         */
        handleControlChange(val, data) {
            if (val) return;

            set(data, "data", "")
        },
        /**
         * 普通校验多个端口格式
         */
        checkCommonMultiPort(rule, value, callback) {
            if (!value) return callback();

            const info = this.checkMultiPort(value);
            return info.result === "success" ? callback() : callback(new Error(info.message || "端口输入有误"));
        },
        /**
         * 校验无域名接入方式，端口校验包含tcp请求端口、http/https复用端口
         */
        checkNoneDomainAccessPort(rule, value, callback, name) {
            if (!value) return callback();

            const conditions = [
                {
                    message: `接入方式为无域名接入时，${name}不支持80、8080、443、8443端`,
                    fn: port => {
                        if (["80","8080","443","8443"].includes(port) && this.accessMode === 2) {
                            return false;
                        }
                    }
                }
            ];

            const info = this.checkMultiPort(value, conditions);
            return info.result === "success" ? callback() : callback(new Error(info.message || "端口输入有误"));
        },
        /**
         * 检查单个端口-无域名接入方式
         */
        checkSingleNoneDomainAccessPort(rule, value, callback, name) {
            if (["80","8080","443","8443"].includes(value) && this.accessMode === 2) {
                return callback(new Error(`接入方式为无域名接入时，${name}不支持80、8080、443、8443端`));
            }

            return callback();
        },
        /**
         * 校验多个端口，包含多个、单个、连续性端口
         */
        checkMultiPort(value, conditions = []) {
            const reg = /^[0-9,-]*$/;

            // 提前退出
            if (!reg.test(value)) return { result: "error" };

            const arr = value.split(",");

            const pattern = /^([0-9]|[1-9]\d|[1-9]\d{2}|[1-9]\d{3}|[1-5]\d{4}|6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5])$/g


            // 检查函数
            const checkFn = item => {
                // 有一个不满足就退出
                if (!new RegExp(pattern).test(item))  return { result: "error" };

                // 额外条件判断
                for (const conditionItem of conditions) {
                    const res = conditionItem.fn(item);
                    if (res === false) {
                        return { result: "error", message: conditionItem.message }
                    }
                }
            }

            // 检查联系端口
            const checkContinuousPort = item => {
                const data = item.split("-");
                // 格式不满足
                if (data.length !== 2) {
                    return { result: "error" };
                }

                // 连个有一个不是端口，就报错
                const isValidate = data.every(port => {
                    return new RegExp(pattern).test(port)
                })
                if (!isValidate) {
                    return { result: "error" };
                }

                const left = Number(data[0]);
                const right = Number(data[1]);

                if (left >= right)  return { result: "error" };

                for(let i = left; i <= right; i++) {
                    // 额外条件判断
                    for (const conditionItem of conditions) {
                        const res = conditionItem.fn(i.toString());
                        if (res === false) {
                            return { result: "error", message: conditionItem.message }
                        }
                    }
                }
            }

            // 普通校验端口
            for (const item of arr) {
                // 普通的端口
                if (item.indexOf("-") === -1) {
                    const checkInfo = checkFn(item);
                    if (checkInfo) return checkInfo;
                } else {
                    // 连续端口
                    const checkInfo = checkContinuousPort(item);
                    if (checkInfo) return checkInfo;
                }
            }

            return { result: "success" }
        },
        /**
         * 检查tcp系统占用端口
         */
        async checkSystemTcpPort(rule, value, callback) {
            if (!this.portCheckRules) {
                await this.getDomainPortInfo();
            }

            const arr = get(this.portCheckRules, "port_tcp_checker", []) || [];
            const res = this.checkIsExistInSystemPort(value, arr);
            return res ? callback() : callback(new Error("端口已被系统占用，请更换端口"));
        },
        /**
         * 检查udp系统端口
         */
        async checkSystemUdpPort(rule, value, callback) {
            if (!this.portCheckRules) {
                await this.getDomainPortInfo();
            }

            const arr = get(this.portCheckRules, "port_udp_checker", []) || [];
            const res = this.checkIsExistInSystemPort(value, arr);
            return res ? callback() : callback(new Error("端口已被系统占用，请更换端口"));
        },
        /**
         * 检查是否存在-系统占用端口中
         * @param value 检查值
         * @param arr 系统端口
         */
        checkIsExistInSystemPort(value, forbidPort) {
            const values = (value || "").split(",");

            const res = values.every(item => {
                // 区间情况
                if (item.indexOf("-") > -1) {
                    const region = item.split("-");
                    const start = Number(region[0]);
                    const end = Number(region[1]);

                    // 禁用端口包含在区间中
                    return forbidPort.every(forbid => {
                        if (forbid >= start && forbid <= end) {
                            return false;
                        }
                        return true;
                    });
                }

                // 含有禁用数组的元素
                if (forbidPort.indexOf(Number(item)) > -1) {
                    return false;
                }

                return true;
            });

            return res;
        },
        /**
         * 获取域名端口校验规则
         */
        async getDomainPortInfo() {
            const { result } = await this.$ctFetch(DomainUrl.domainPortCheck);
            this.portCheckRules = result;
        },
        /**
         * ftp控制端口变化
         */
        handleFtpChange(prop) {
            // 由于dispatch函数是寻找最近的elFormItem组件。这里是跨层级了，中间多了一层=ftp控制端口和ftp数据端口要配必须同时配的判断层
            // 所以这里需要手动触发端口信息层的校验
            this.elForm && this.elForm.validateField(prop);
        },
        transFormPropToClassName,
        /**
         * 获取最终提交给后端的数据
         */
        getSubmitData() {
            const data = cloneDeep(this.data);
            const arr = data.map(item => {
                // 去除renderId，此renderId仅仅为了vue更新机制所用，并不参与后端交互
                if (has(item, "renderId")) {
                    delete item.renderId;
                }

                return Object.entries(item).reduce((info, [keyName, value]) => {
                    // 源站字段 不进行处理 直接返回
                    if (keyName === "detail") {
                        info[keyName] = value;
                        return info;
                    }

                    // ftp字段处理,根据后端需求，ftp_ports字段没有配置，则不传递该字段
                    // 扩展成对象类型，对对象判断每个属性值是否有内容 !(value instanceof Array)
                    if (keyName === "ftp_ports") {
                        if (Object.values(value).every(ftpValue => ftpValue)) {
                            info[keyName] = value;
                        }

                        return info;
                    }

                    // 数组情况
                    // 去除renderId，此renderId仅仅为了vue更新机制所用，并不参与后端交互
                    info[keyName] = value.reduce((arr, valueItem) => {
                        if (has(valueItem, "renderId")) {
                            delete valueItem.renderId;
                        }

                        // TCP、UDP、http、https端口组，任意有值即保留
                        if (valueItem.req || valueItem.origin) {
                            arr.push(valueItem);
                        }

                        return arr;
                    }, []);

                    return info;
                }, {});
            })

            return arr;
        },
    },
    created() {
        this.getDomainPortInfo();
    }
}
</script>

<style lang="scss" scoped>
.dynamic-item {
    margin-top: $margin-5x;
    position: relative;

    .action-box {
        position: absolute;
        top: 20px;
        right: 20px;
        z-index: 100;
    }
}

.tip-box {
    width: 700px;
}

.action-box {
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
}
.el-form-item {
    margin-bottom: 20px;

    // 结合v2.18.0全局报错样式优化需求，这里的error-msg都拥有自己的高度，所以可以取消所有的margin-bottom,只留下一个美观的margin-bottom
    &.no-margin {
        margin-bottom: 0;
    }
}

.el-card {
    border: 1px solid #d9d9d9;
    box-shadow: unset;
    &:hover {
        box-shadow: unset;
    }
}

.back-source-conf {
    ::v-deep .form-item-tip {
        font-size: 12px;
        color: $color-neutral-7;
        line-height: 18px;
        font-weight: 400;
        margin-bottom: $margin-2x;

        &:last-child {
            margin-bottom: 0;
        }

        .icon-tip {
            margin-right: $margin;
            font-size: 14px;
        }
    }
}

.tip-wrapper {
    margin-top: 20px;
}

.tip-info-box {
    display: flex;
    flex-direction: row;
    margin-top: $margin-5x;
    max-width: 800px;

    .tip-label {
        width: 100px;
        flex-shrink: 0;
        text-align: right;
        padding-right: $padding-4x;
        line-height: 18px;
    }

    .tip-content {
        flex: 1;
        overflow: hidden;
    }
}

</style>
