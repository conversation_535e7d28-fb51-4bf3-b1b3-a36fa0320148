/*
 * @Description:
 * @Author: wang yuegong
 */

export type SearchParams = {
    domainList?: string[]; // 根据业务要求，域名列表是必要参数
    httpCode?: string[]; // 状态码
    province?: string[];
    continent_code?: string[];
    continent_region_code?: string[];
    sortBy?: string; // 排序
    startTime: number; // 根据业务要求，时间是必要参数
    endTime: number;
    top?: number;
    status?: string;
    product: string[];
};

// ListTopUri 接口数据 list 单项
export interface ListTopUriItem {
    // uri: string; // uri值
    url: string; // url信息
    request: string; // 请求数
    requestPer: string; // 请求数占比
    flow: string; // 流量
    flowPer: string; // 流量占比
    rank: string; // 排名编号
}
// ListTopUri 接口数据
export interface ListTopUriFetchData {
    list: ListTopUriItem[];

    totalFlow: string; // 总流量
    totalRequest: string; // 总请求数
    // requestProportion: string; // 请求数占比
    // flowProportion: string; // 流量占比
}

// ListTopReferer 接口数据 list 单项
export interface ListTopRefererItem {
    referer: string; // referer
    request: string; // 请求数
    requestPer: string; // 请求数占比
    flow: string; // 流量
    flowPer: string; // 流量占比
    rank: string; // 排名编号
}
// ListTopReferer 接口数据
export interface ListTopRefererFetchData {
    list: ListTopRefererItem[];

    totalFlow: string; // 总流量
    totalRequest: string; // 总请求数
}

// ListTopDomain 接口数据 list 单项
export interface ListTopDomainItem {
    channel: string; // channel
    flow: string; // 流量
    flowPer: string; // 流量占比
    topBandwidth: string; // 峰值带宽
    topBandwidthTime: string; // 峰值带宽时刻
    request: string; // 请求数
    rank: string; // 排名编号
}
// ListTopDomain 接口数据
export interface ListTopDomainFetchData {
    list: ListTopDomainItem[];

    totalFlow: string; // 总流量
    totalRequest: string; // 总请求数
}

// ListTopIp 接口数据 list 单项
export interface ListTopIpItem {
    ip: string; // ip
    flow: string; // 流量
    request: string; // 请求数
    rank: string; // 排名编号
}
// ListTopIp 接口数据
export interface ListTopIpFetchData {
    list: ListTopIpItem[];

    totalFlow: string; // 总流量
    totalRequest: string; // 总请求数
}
