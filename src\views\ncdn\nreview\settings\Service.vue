<template>
    <section>
        <div class="emial-wrap">
            违规图片通知邮箱：
            <el-button type="text" @click="showEmailEdit" size="medium" class="email-btn">
                {{ !!emails ? "编辑" : "添加邮箱" }}
            </el-button>
            <div>
                <el-tag v-for="e in emails.split(';').filter(e => e)" :key="e" effect="plain">{{ e }}</el-tag>
            </div>
        </div>

        <el-dialog title="违规图片通知邮箱" :visible.sync="emailDialogVisible" :close-on-click-modal="false">
            <el-input
                type="textarea"
                :autosize="{ minRows: 6, maxRows: 8 }"
                :placeholder="'请输入有效邮箱，每个邮箱一行（回车换行)，最多可以设置' + emailMax + '个'"
                v-model="emailInput"
            />
            <div slot="footer" class="dialog-footer">
                <el-button @click="emailDialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="submitEmail">确 定</el-button>
            </div>
        </el-dialog>

        <div class="domain-wrap" v-loading="loading">
            <div class="domain-panel">
                <div>
                    <h4>未检测域名</h4>
                    <!-- <el-checkbox-group
                        class="product-code-enum"
                        v-model="checkedProductList"
                        @change="changeProductCode"
                    >
                        <el-checkbox
                            v-for="item in basicProductList"
                            :label="productCodeMap.get(item)"
                            :key="item"
                        ></el-checkbox>
                    </el-checkbox-group> -->
                </div>
                <el-checkbox-group
                    class="checkbox-group"
                    v-model="undetectedCheckedList"
                    @change="handleCheckedUndetectedChange"
                >
                    <el-checkbox
                        v-for="item in undetectedDomainList"
                        :label="item.domain"
                        :key="item.domain"
                        :disabled="item.hasOrder"
                        :title="item.hasOrder ? '已有正在进行中的工单' : ''"
                    >
                        {{ item.domain }}
                    </el-checkbox>
                </el-checkbox-group>

                <el-checkbox
                    :indeterminate="undetectedIsIndeterminate"
                    v-model="undetectedIsAllChecked"
                    @change="handleCheckAllUndetected"
                >
                    {{ undetectedCheckedList.length }}项
                </el-checkbox>
            </div>

            <div class="domian-btns">
                <el-button
                    type="primary"
                    :disabled="!emails || loading || !undetectedCheckedList.length || buttonDisabled"
                    @click="beforeSubmit(true)"
                >
                    添加 <i class="el-icon-arrow-right" />
                </el-button>
                <el-button
                    type="primary"
                    :disabled="!emails || loading || !detectedCheckedList.length || buttonDisabled"
                    @click="beforeSubmit(false)"
                >
                    <i class="el-icon-arrow-left" /> 取消
                </el-button>
            </div>

            <div class="domain-panel">
                <div>
                    <h4>检测域名</h4>
                    <!-- <el-checkbox-group
                        class="product-code-enum"
                        v-model="checkedProductList"
                        @change="changeProductCode"
                    >
                        <el-checkbox
                            v-for="item in basicProductList"
                            :label="productCodeMap.get(item)"
                            :key="item"
                        ></el-checkbox>
                    </el-checkbox-group> -->
                </div>
                <el-checkbox-group
                    class="checkbox-group"
                    v-model="detectedCheckedList"
                    @change="handleCheckedDetectedChange"
                >
                    <el-checkbox
                        v-for="item in detectedDomainList"
                        :label="item.domain"
                        :key="item.domain"
                        :disabled="item.hasOrder"
                        :title="item.hasOrder ? '已有正在进行中的工单' : ''"
                    >
                        {{ item.domain }}
                    </el-checkbox>
                </el-checkbox-group>

                <el-checkbox
                    :indeterminate="detectedIsIndeterminate"
                    v-model="detectedIsAllChecked"
                    @change="handleCheckAllDetected"
                >
                    {{ detectedCheckedList.length }}项
                </el-checkbox>
            </div>
        </div>

        <el-dialog
            title="提示"
            :visible.sync="dialogVisible"
            :modal-append-to-body="false"
            v-loading="loading"
        >
            <h4 class="dialog-title">
                {{
                    isUndetectedDialog
                        ? "确认为以下域名开通 CDN 内容审核服务"
                        : "确认取消以下域名的 CDN 内容审核服务"
                }}
            </h4>
            <ul class="dialog-list">
                <li
                    v-for="domain in isUndetectedDialog ? undetectedCheckedList : detectedCheckedList"
                    :key="domain"
                    class="dialog-item"
                >
                    {{ domain }}
                </li>
            </ul>
            <p class="dialog-tip">注：域名操作成功后，预计需要5-15分钟生效。</p>
            <span slot="footer">
                <el-button @click="dialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="submit(isUndetectedDialog)">确 定</el-button>
            </span>
        </el-dialog>

        <p class="domain-tip">
            注: CDN内容审核为增值服务，配置开启后将产生额外费用。
            <!-- <el-button type="text" @click="goBilling">
                点击查看费用详情
            </el-button> -->
        </p>
    </section>
</template>
<script lang="ts">
import { Component, Vue, Watch } from "vue-property-decorator";
import { nReviewUrl } from "@/config/url/ncdn/nreview";
import { email } from "@/config/patternValidator";
import { DomainModule } from "@/store/modules/domain";
import { DomainActionEnum, ProductCodeEnum, ProductType } from "@/config/map";
import { ProductModule } from "@/store/modules/ncdn/nproduct";

const emailReg = new RegExp(email);
// 域名仅限：静态、点播、下载、CDN加速、新的类型
const ProductCodeConfig = [
    ProductCodeEnum.Static,
    ProductCodeEnum.Download,
    ProductCodeEnum.Demand,
    ProductCodeEnum.CDN,
];

type DomainItem = {
    domain: string;
    hasOrder: boolean; // 是否有在途工单
};

/**
 * 业务逻辑梳理：
 *  1、从工作区域名列表中获取基础域名列表，需要满足加速类型条件+产品计费模式为按需计费的限制
 *      - 前置依赖1：获取域名列表数据 DomainModule[DomainActionEnum.Review].nativeList
 *      - 前置依赖2：获取产品列表，判断计费模式 ProductModule.canUseList
 *  2、从内容审核检测域名列表接口中，获取“已检测域名列表”。
 *      - 注：可能存在因计费产品变更导致的部分域名不属于基础域名列表范围内的情况
 *  3、以基础域名列表为基础，过滤掉已检测域名后，获得“未监测域名列表”
 *  4、使用“已检测”+“未检测”域名列表，请求在途工单数据，判断域名是否有在途工单
 */

@Component
export default class Service extends Vue {
    loading = false;
    emailMax = 10; // 最多10条邮箱
    emailInput = ""; // 输入框换行
    emails = ""; // 分号分割
    emailDialogVisible = false; // 显示邮箱设置界面
    buttonDisabled = false; // 添加、取消按钮是否禁用

    // checkedProductList: string[] = []; //勾选的产品列表
    // checkedProductCodeList: string[] = []; //勾选的产品的code列表
    // // 产品和code的映射关系,后续要修改成动态的映射关系
    // productCodeMap = new Map([
    //     ["001", "静态"],
    //     ["003", "下载"],
    //     ["004", "点播"],
    //     ["静态", "001"],
    //     ["下载", "003"],
    //     ["点播", "004"],
    // ]);

    undetectedDomainList: DomainItem[] = []; // 未检测域名列表（所有）
    undetectedCanCheckDomainList: string[] = []; // 未检测域名列表（可选，即没有在途工单）
    undetectedIsAllChecked = false; // 未检测域名都被选中
    undetectedIsIndeterminate = false; // 未检测域名未被都选中
    undetectedCheckedList: string[] = []; // 已选中的未检测域名

    detectedDomainList: DomainItem[] = []; // 已检测域名列表（所有）
    detectedCanCheckDomainList: string[] = []; // 已检测域名列表（可选，即没有在途工单）
    detectedIsAllChecked = false; // 已检测域名都被选中
    detectedIsIndeterminate = false; // 已检测域名未被都选中
    detectedCheckedList: string[] = []; // 已选中的已检测域名

    dialogVisible = false; // 确认操作弹窗
    isUndetectedDialog = true; // 弹窗类型，true 为未检测，false 为已检测

    // 可用加速类型列表（满足加速类型条件+产品计费模式为按需计费的限制）
    get basicProductList() {
        return ProductModule.canUseList
            .filter(
                item =>
                    ProductCodeConfig.join(",").includes(item.product_code) &&
                    item.product_type === ProductType.BSS
            )
            .map(item => item.product_code);
    }

    // 基础域名列表
    get basicDomainList() {
        const { basicProductList } = this;
        // if (this.checkedProductCodeList.length !== 0) {
        //     return DomainModule[DomainActionEnum.Review].nativeList
        //         .filter(
        //             item =>
        //                 basicProductList.includes(item.productCode) &&
        //                 this.checkedProductCodeList.includes(item.productCode)
        //         )
        //         .map(item => item.domain);
        // } else {
        //     return DomainModule[DomainActionEnum.Review].nativeList
        //         .filter(item => basicProductList.includes(item.productCode))
        //         .map(item => item.domain);
        // }
        return DomainModule[DomainActionEnum.Review].nativeList
            .filter(item => basicProductList.includes(item.productCode))
            .map(item => item.domain);
    }

    mounted() {
        // 获取邮箱列表
        this.getEmail();
        this.refreshDomain();
    }

    // 获取邮箱
    async getEmail() {
        const rst = await this.$ctFetch<{ notice_address: string }>(nReviewUrl.getSettingEmail);

        this.emails = rst.notice_address || "";
        this.emailInput = this.emails.replace(";", "\n");
    }

    // 编辑邮箱
    showEmailEdit() {
        this.emailInput = this.emails.replaceAll(";", "\n");
        this.emailDialogVisible = true;
    }

    // 提交邮箱设置
    async submitEmail() {
        if (!this.emailInput.trim()) return this.$message.error("请输入邮箱");

        // 数据格式化
        let emails = this.emailInput
            .trim()
            .split("\n")
            .filter(val => val);

        if (emails.length > this.emailMax) {
            return this.$message.error(`邮箱数量不能超过${this.emailMax}个`);
        }

        // 去重
        emails = Array.from(new Set(emails));

        // 检查邮箱格式
        if (emails.some(email => !emailReg.test(email))) {
            return this.$message.error("请输入正确格式的邮箱");
        }

        if (emails.join(";") === this.emails) {
            this.emailDialogVisible = false;
            return;
        }

        // 提交修改
        await this.$ctFetch(nReviewUrl.updateSettingEmail, {
            method: "POST",
            body: {
                noticeAddress: emails.join(";"),
            },
            headers: {
                "Content-Type": "application/json",
            },
        });

        this.$message.success("设置成功！");
        this.emails = emails.join(";");
        this.emailInput = emails.join("\n");
        this.emailDialogVisible = false;
    }

    // 初始化域名列表
    @Watch("basicDomainList")
    async refreshDomain() {
        // 清空已选项
        this.undetectedCheckedList = [];
        this.detectedCheckedList = [];
        // 将全选置为false
        this.detectedIsAllChecked = false;
        this.undetectedIsAllChecked = false;

        // if (this.basicDomainList.length === 0) return;
        if (this.basicDomainList.length === 0) {
            this.undetectedDomainList = [];
            this.detectedDomainList = [];
            return;
        }

        this.loading = true;

        // 获取已检测域名
        const domainRst = await this.$ctFetch<{ list: string[] }>(nReviewUrl.getSettingDomain);
        const detectedDomainList = domainRst.list || [];

        // 合并两种域名，查询在途工单
        const allDomain = Array.from(new Set([...detectedDomainList, ...this.basicDomainList]));
        const orderRst = await this.$ctFetch<{ list: string[] }>(nReviewUrl.getDomainOrderList, {
            method: "POST",
            data: {
                domainList: allDomain,
            },
            headers: {
                "Content-Type": "application/json",
            },
        });
        // 有在途工单的域名列表
        //const orderDomainList = (orderRst.list || []).map(order => order.domain);
        const orderDomainList = orderRst.list || [];
        // 根据已检测域名列表和在途工单域名列表，生成期望数据
        const detectedList: DomainItem[] = detectedDomainList.map(domain => ({
            domain,
            hasOrder: orderDomainList.includes(domain),
        }));

        // 排序，有在途工单的后置
        this.detectedDomainList = detectedList.sort((a, b) => +a.hasOrder - +b.hasOrder);
        // 可选域名列表需过滤掉有在途工单的
        this.detectedCanCheckDomainList = detectedList
            .filter(item => !item.hasOrder)
            .map(item => item.domain);

        // 基础域名排除已检测域名，即为未检测域名；再根据在途工单域名列表，生成期望数据
        const undetectedList: DomainItem[] = this.basicDomainList
            .filter(domain => !detectedDomainList.includes(domain))
            .map(domain => ({
                domain,
                hasOrder: orderDomainList.includes(domain),
            }));
        // 排序，有在途工单的后置
        this.undetectedDomainList = undetectedList.sort((a, b) => +a.hasOrder - +b.hasOrder);
        // 可选域名列表需过滤掉有在途工单的
        this.undetectedCanCheckDomainList = undetectedList
            .filter(item => !item.hasOrder)
            .map(item => item.domain);
    }

    // 全选未检测域名
    handleCheckAllUndetected(flag: boolean) {
        this.undetectedCheckedList = flag ? [...this.undetectedCanCheckDomainList] : [];
        this.undetectedIsIndeterminate = false;
    }
    // 判断未检测域名是否全选状态
    handleCheckedUndetectedChange(list: string[]) {
        const checkedCount = list.length;
        const allCount = this.undetectedCanCheckDomainList.length;
        this.undetectedIsAllChecked = checkedCount === allCount;
        this.undetectedIsIndeterminate = checkedCount > 0 && checkedCount < allCount;
    }
    // 全选已检测域名
    handleCheckAllDetected(flag: boolean) {
        this.detectedCheckedList = flag ? [...this.detectedCanCheckDomainList] : [];
        this.detectedIsIndeterminate = false;
    }
    // 判断已检测域名是否全选状态
    handleCheckedDetectedChange(list: string[]) {
        const checkedCount = list.length;
        const allCount = this.detectedCanCheckDomainList.length;
        this.detectedIsAllChecked = checkedCount === allCount;
        this.detectedIsIndeterminate = checkedCount > 0 && checkedCount < allCount;
    }

    // changeProductCode() {
    //     this.checkedProductList = [...new Set(this.checkedProductList)];
    //     this.checkedProductCodeList = [];
    //     for (const item of this.checkedProductList) {
    //         this.checkedProductCodeList.push(String(this.productCodeMap.get(item)));
    //     }
    //     //this.refreshDomain();
    // }

    // 准备提交
    beforeSubmit(isUndetected: boolean) {
        this.isUndetectedDialog = isUndetected;
        this.dialogVisible = true;
    }
    // 正式提交, isUndetected值为true时为新增，值为false时为删除
    async submit(isUndetected: boolean) {
        // 等待3秒延时过程中，添加、取消按钮不可用
        this.buttonDisabled = true;
        this.loading = true;
        const domainList = isUndetected ? this.undetectedCheckedList : this.detectedCheckedList;
        // 工单动作action为4时执行新增，为3时执行关闭
        const action = isUndetected ? "4" : "3";
        await this.$ctFetch(nReviewUrl.updateSettingDomain, {
            method: "POST",
            body: {
                data: {
                    domainList,
                    action,
                },
            },
            headers: {
                "Content-Type": "application/json",
            },
        });
        this.dialogVisible = false;
        setTimeout(() => {
            this.refreshDomain();
            this.buttonDisabled = false;
        }, 3000);
    }

    goBilling() {
        // 地址尚未决定
        // window.open("xx", "_blank");
    }
}
</script>

<style lang="scss" scoped>
$g-panel-width: 350px;

.emial-wrap {
    margin: 8px 0;

    .el-tag {
        margin-right: 8px;
        margin-bottom: 4px;
    }
}
.email-btn {
    &::after {
        content: "*";
        color: $color-danger;
        vertical-align: middle;
    }
}

// 域名相关
.domain-wrap {
    padding: 12px 0;
    @include g-display(block, flex);
    align-items: center;

    h4 {
        line-height: 36px;
    }

    .product-code-enum {
        height: 24px;
        font-size: 16px;
    }
}

// 域名列表面板
.domain-panel {
    overflow: hidden;
    width: $g-panel-width;
    > .el-checkbox {
        padding: 8px 12px;
        width: 100%;
        border: 1px solid $border-color;
        border-top-color: transparent;
        border-radius: 0 0 3px 3px;
    }
}

// 域名列表选择
.checkbox-group {
    overflow: auto;
    height: 290px;
    border: 1px solid $border-color;
    border-radius: 3px 3px 0 0;

    .el-checkbox {
        display: block;
        height: 26px;
        line-height: 26px;
        padding-left: 8px;
    }
}

.domian-btns {
    @include g-width($g-panel-width, 100px);
    text-align: center;

    .el-button {
        margin: 5px;
    }
}

.domain-tip {
    color: $neutral-7;
}

// 弹窗相关
.dialog-title {
    font-size: 14px;
}

.dialog-list {
    margin: 8px 0;
    overflow: auto;
    max-height: 350px;
}

.dialog-item {
    line-height: 1.5;

    &::before {
        content: "·";
        font-size: 14px;
        vertical-align: middle;
        font-weight: bold;
    }
}

.dialog-tip {
    color: $neutral-7;
    font-size: 12px;
}
</style>
