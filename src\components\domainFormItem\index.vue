<template>
    <div style="width: 100%;">
        <div v-if="!hasBelong" class="flex-row-style belong-box">
            <i class="el-icon-warning icon-warning" />
            <div>
                域名【www.test.com】需要完成归属权检验，点击<span @click="handleCheckBelong">前往校验</span>，
                若操作失败请
                <a
                    href="https://www.ctyun.cn/h5/wsc/worksheet/submit"
                    style="color: #FA8334;cursor:pointer;font-weight: bold;"
                    target="_blank"
                    >前往工单系统</a
                >
                提客户工单
            </div>
        </div>
        <el-form-item label="域名" :prop="prop" required :rules="rules" class="domain-form-item">
            <div class="flex-row-style">
                <el-input v-model="form[prop]" placeholder="请输入域名" @input="handleCommonChange" />
                <template v-if="recordLoading">
                    <i class="el-icon-loading" style="margin-left: 10px;" />
                </template>
            </div>
            <div slot="error" class="el-form-item__error" slot-scope="scope">
                <div v-html="scope.error"></div>
            </div>
        </el-form-item>
        <check-dialog
            ref="checkDialog"
            :domain="form[prop]"
            :visible.sync="dialogShow"
            @handle-verify="handleVerify"
        />
    </div>
</template>

<script>
import checkDialog from "./components/checkDialog";
import { genericIp } from "@/utils/pattern";
import { getDomainHasRecord, repeatValidate, domainBelongValidate } from "@/api";
import { get } from "lodash-es";

export default {
    name: "index",
    components: {
        checkDialog,
    },
    props: {
        form: {
            type: Object,
            default: () => {
                return {};
            },
        },
        prop: {
            type: String,
            default: "domains",
        },
    },
    data() {
        return {
            hasBelong: true,
            recordLoading: false,
            rules: [
                { required: true, message: "请填写域名" },
                { validator: this.checkDomain, trigger: "blur" },
            ],
            dialogShow: false,

            isVerifySuccess: false,
        };
    },
    methods: {
        /**
         * 检查域名
         */
        async checkDomain(rule, value, callback) {
            const pattern = /^(\*\.|\*)?([a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?\.)+[a-z]{2,6}$/g;
            const ipPattern = new RegExp(genericIp, "g");
            const domainRes = pattern.test(value);
            const ipRes = ipPattern.test(value);

            if (ipRes) {
                return callback(new Error("请输入域名，当前暂不支持IP输入"));
            }

            if (!domainRes) {
                return callback(new Error("请填写正确的域名"));
            }

            this.recordLoading = true;
            // 自动化域名归属校验
            const params = {
                domain: value,
            };

            const belongRes = await this.checkDomainBelong(params);

            if (belongRes === "error") {
                this.recordLoading = false;
                return callback(new Error("域名归属校验出错，请联系管理员"));
            }

            if (get(belongRes, "error")) {
                this.recordLoading = false;
                return callback(new Error(get(belongRes, "msg")));
            }

            if (!belongRes) {
                this.recordLoading = false;
                return callback(new Error("域名归属校验出错，请按提示步骤操作"));
            }

            // 检查域名是否重复
            try {
                const repeatParams = {
                    domain: value,
                };
                const repeatRes = await repeatValidate(repeatParams);
                const isRepeat = get(repeatRes, "repeat", true);
                if (isRepeat) {
                    this.recordLoading = false;
                    return callback(new Error("输入域名已存在，请确认"));
                }
            } catch (e) {
                this.$errorHandler(e);
                this.recordLoading = false;
                return callback(new Error("域名重复校验出错，请联系管理员"));
            }

            // 检查域名是否备案
            try {
                const params = {
                    domain: value,
                };
                const res = await getDomainHasRecord(params);
                const baFlag = get(res, "baFlag", false);
                if (!baFlag) {
                    this.recordLoading = false;
                    return callback(
                        new Error(
                            "<div>您的域名未备案，请及时进行备案，如有问题可提<a style='color: #FA8334;cursor:pointer;font-weight: bold;' href='https://www.ctyun.cn/h5/wsc/worksheet/submit' target='_blank'>工单咨询</a></div>"
                        )
                    );
                }
            } catch (e) {
                this.$errorHandler(e);
                this.recordLoading = false;
                return callback(new Error("域名备案校验出错，请联系管理员"));
            }

            this.recordLoading = false;
            return callback();
        },
        /**
         * 检查域名归属
         */
        async checkDomainBelong(params) {
            const belongRes = await domainBelongValidate(params).catch(err => {
                this.$errorHandler(err);
                return false;
            });
            if (get(belongRes, "mainDomainVerify") === true) {
                this.hasBelong = true;
                return true;
            }

            if (belongRes === false) {
                return "error";
            }

            if (!get(belongRes, "domainZone")) {
                return {
                    error: true,
                    msg: get(belongRes, "verifyDesc") || "域名校验出错，请联系管理员",
                };
            }

            this.hasBelong = false;
            this.dialogShow = true;

            this.$refs.checkDialog.initData(belongRes, this.isVerifySuccess);
            return false;
        },
        /**
         * 处理校验成功
         */
        handleVerify(isSuccess) {
            this.isVerifySuccess = isSuccess;
            if (!isSuccess) {
                return;
            }

            this.hasBelong = true;
            this.$emit("check-domain", true);
        },
        /**
         * 校验归属
         */
        handleCheckBelong() {
            this.dialogShow = true;
        },
        handleCommonChange(val) {
            this.hasBelong = true;
            this.isVerifySuccess = false;
        },
    },
};
</script>

<style scoped lang="scss">
.belong-box {
    font-size: 12px;
    margin-bottom: 10px;
    .icon-warning {
        width: 120px;
        color: #f56c6c;
        font-size: 16px;
        flex-shrink: 0;
        //margin-right: 20px;
        padding: 0 12px 0 0;
        text-align: right;
    }
    span {
        cursor: pointer;
        color: #fa8334;
        font-weight: bolder;
    }
}

.domain-form-item {
    //display: flex;
    //flex-direction: row;
    //align-items: center;
    .el-input {
        width: 100%;
        line-height: 40px;
    }
}
</style>
