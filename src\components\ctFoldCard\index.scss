.ct-fold-card {
    width: 100%;
    height: 100%;
    border-radius: 5px;

    .inside {
        font-size: 14px;
        color: rgb(199, 197, 197);
        width: 100%;
        height: 100%;
        padding: 15px 30px 15px 15px;
        border-radius: 5px;
        position: relative;
        background: white;
        background: linear-gradient(-45deg, transparent 2.18em, white 0);
        justify-content: space-between;

        &:before {
            content: "";
            display: block;
            width: 3em;
            height: 3em;
            position: absolute;
            background: linear-gradient(45deg, white 50%, transparent 0);
            right: 0;
            bottom: 0;
            border-bottom-left-radius: inherit;
            transform: translateY(0em) rotate(90deg);
            box-shadow: -0.2em 0.2em 0.3em -0.1em rgba(0, 0, 0, 0.15);
        }
    }

    .icon-box {
        width: 48px;
        height: 48px;
        border-radius: 48px;
    }

    .normal-icon {
        font-size: 48px;
        margin-right: 20px;
    }

    .title {
        font-size: 28px;
        font-family: PingFangSC-Semibold, PingFang SC;
        font-weight: 600;
        color: #333333;
    }

    .sub-title {
        font-size: 16px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #333333;
        line-height: 22px;
    }

    .unit {
        font-size: 14px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #333333;
        line-height: 20px;
    }
}
