/*
 * @Description: 统计分析 QueryDataList 接口类型注解，涉及页面：首页-7天趋势、统计分析-带宽流量、回源统计
 * @Author: wang yuegong
 */

// QueryFetchData 接口中按 5min 的统计数据
export interface Query5MinBase {
    queryBandwidth: number; // 带宽（用于带宽流量 tab）
    queryflow: number; // 流量
    flow: number;
    query: number; // 请求数（用于请求数 tab）
    missBandwidth: number; // 回源带宽（用于回源统计 tab）
    missflow: number; // 回源流量
    miss: number; // 回源请求
    timestamp: number; // 时间戳
    bandwidth: number;
    bandwidthTime: number;
    flowTime: number;
    speed: number; // 下载速度

    /* ===== 以下字段没用到 ===== */

    // Bandwidth: number; // 带宽
    // flow: number; // 流量
    // hitBandwidth: number; // 命中带宽
}

// 带宽流量用到的 5min
export type Query5Min = Pick<
    Query5MinBase,
    "queryBandwidth" | "queryflow" | "timestamp" | "flow" | "bandwidth" | "bandwidthTime" | "flowTime"
>;
// 回源统计用到的 5min
export type Miss5Min = Pick<
    Query5MinBase,
    "missBandwidth" | "missflow" | "timestamp" | "bandwidth" | "bandwidthTime" | "flow" | "flowTime"
>;
// 请求数用到的 5min
export type Request5Min = Pick<Query5MinBase, "query" | "miss" | "timestamp">;
// 下载速度用到的5min
export type Speed5Min = Pick<Query5MinBase, "speed" | "queryflow" | "timestamp">;

// QueryFetchData 接口中按天的统计数据（用于带宽流量 tab）
export interface QueryDaily {
    topQueryBandwidth: number; // 峰值带宽
    topBandwidth: number;
    top95Bandwidth: number;
    queryflow: number; // 流量
    flow: number;
    missflow: number; // 回源流量
    date: string;
    topMissBandwidth: number; // 回源峰值带宽，该字段前端定义，用于表格展示
    topTime: number;
    topMissTime: number; // 回源峰值时间，该字段前端定义，用于表格展示
    topBandwidthTime: number; // 虚拟专线 峰值时间点
    query: number; // 请求数
    miss: number; // 回源请求数
}

// 完整的 QueryFetchData 接口
export interface QueryFetchDataBase {
    "5min": Query5MinBase[]; // 5min 数据
    daily: QueryDaily[]; // 每日数据

    topBandwidth: number;
    top95Bandwidth: number;
    totalRequestValue: string; // 总请求数
    topBandwidthTime: number;
    totalFlow: number; // 总流量
    avgQueryFlow: number; // 平均查询流量

    avgMissFlow: number; // 平均回源流量

    totalQuery: number; // 总请求数
    maxQuery: number; // 最大请求数
    minQuery: number; // 最小查询请求数
    maxQueryTimestamp: string; // 最大请求数对应的时间，格式如 2020-12-21 23:55:00
    minQueryTimestamp: string; // 最小查询请求数对应的时间
    totalMiss: number; // 总回源数
    maxMiss: number; // 最大回源数
    minMiss: number; // 最小回源数
    dailyPeakMonthlyAverage: number;

    maxSpeed: number; // 最大下载速度
    maxSpeedTimestamp: number; // 最大下载速度对应的时间
}

// QueryFetchData 接口数据（用于带宽流量 tab）
export type QueryFetchData = Pick<
    QueryFetchDataBase,
    "daily" | "totalFlow" | "avgQueryFlow" | "topBandwidth" | "top95Bandwidth" | "dailyPeakMonthlyAverage" | "topBandwidthTime"
> & {
    "5min": Query5Min[]; // 缩小 5min 字段范围
};

// QueryFetchData 接口数据（用于回源统计 tab）
// TODO: 去掉没有用的类型
export type MissFetchData = Pick<
    QueryFetchDataBase,
    "daily" | "avgMissFlow" | "topBandwidth" | "totalFlow" | "topBandwidthTime" | "top95Bandwidth" | "avgQueryFlow"
> & {
    "5min": Miss5Min[]; // 缩小 5min 字段范围
};

// QueryFetchData 接口数据（用于请求数 tab）
export type RequestFetchData = Pick<
    QueryFetchDataBase,
    | "totalQuery"
    | "totalMiss"
    | "maxQuery"
    | "minQuery"
    | "maxQueryTimestamp"
    | "minQueryTimestamp"
    | "daily"
> & {
    "5min": Request5Min[]; // 缩小 5min 字段范围
};

// QueryFetchSpeedData 接口数据（用于下载速度 tab）
export type QueryFetchSpeedData = Pick<QueryFetchDataBase, "daily" | "maxSpeed" | "maxSpeedTimestamp"> & {
    "5min": Speed5Min[]; // 缩小 5min 字段范围
};
