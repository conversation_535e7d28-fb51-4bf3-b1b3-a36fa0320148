<template>
    <div class="ct-edit-wrapper">
        <el-form
            label-width="140px"
            label-position="left"
            :model="form"
            :rules="rules"
            ref="errorPageForm"
            :disabled="!isEdit || !isService || isLockErrorPage"
        >
            <div>
              <el-form-item
                prop="error_page"
                ref="errorPage"
                class="cache-table-style"
                :label="$t('domain.editPage.label39')"
              >
                <div class="ct-table-wrapper">
                    <el-table class="origin-table auto-table" :data="form.error_page">
                      <!-- 错误状态码 -->
                      <el-table-column prop="code" :label="$t('domain.editPage.label40')">
                        <template slot="header">
                          <div class="flex-row-style">
                            <span>{{ $t("domain.editPage.label40") }}</span>
                            <el-tooltip
                                class="item"
                                style="margin-left:4px"
                                effect="dark"
                                :content="$t('domain.editPage.tip30')"
                                placement="top"
                            >
                                <i class="el-icon-question" />
                            </el-tooltip>
                          </div>
                        </template>
                        <template slot-scope="scope">
                            <el-form-item
                              label=""
                              :prop="`error_page.` + scope.$index + `.code`"
                              :rules="rules.code"
                              class="aifs"
                            >
                                <el-input
                                    v-model="scope.row.code"
                                    class="input-style"
                                    @change="handleChange"
                                ></el-input>
                            </el-form-item>
                        </template>
                      </el-table-column>
                      <!-- 跳转页面 -->
                      <el-table-column prop="url" :label="$t('domain.editPage.label41')">
                        <template slot="header">
                          <div class="flex-row-style">
                            <span>{{ $t("domain.editPage.label41") }}</span>
                            <el-tooltip
                                class="item"
                                style="margin-left:4px"
                                effect="dark"
                                :content="$t('domain.editPage.tip31')"
                                placement="top"
                            >
                                <i class="el-icon-question" />
                            </el-tooltip>
                          </div>
                        </template>
                        <template slot-scope="scope">
                            <el-form-item
                              label=""
                              :prop="`error_page.` + scope.$index + `.url`"
                              :rules="rules.url"
                              class="aifs"
                            >
                                <el-input
                                    v-model="scope.row.url"
                                    class="input-style"
                                    @change="handleChange"
                                ></el-input>
                            </el-form-item>
                        </template>
                      </el-table-column>
                      <!-- 跳转状态码 -->
                      <el-table-column prop="location_code" :label="$t('domain.editPage.label35')" width="115">
                        <template slot-scope="scope">
                            <el-form-item
                              label=""
                              :prop="`error_page.` + scope.$index + `.location_code`"
                              :rules="rules.location_code"
                              class="aifs"
                            >
                                <el-select
                                  v-model="scope.row.location_code"
                                  clearable
                                  style="width:100%"
                                  @change="handleChange"
                              >
                                  <el-option
                                      v-for="i in locationCodeList"
                                      :key="i.value"
                                      :label="i.label"
                                      :value="i.value"
                                  ></el-option>
                                </el-select>
                            </el-form-item>
                        </template>
                      </el-table-column>
                      <!-- 优先级 -->
                      <el-table-column prop="priority" :label="$t('domain.detail.label48')" width="100">
                        <template slot="header">
                          <div class="flex-row-style">
                            <span>{{ $t("domain.detail.label48") }}</span>
                            <el-tooltip
                                class="item"
                                style="margin-left:4px"
                                effect="dark"
                                :content="$t('domain.editPage.tip32')"
                                placement="top"
                            >
                                <i class="el-icon-question" />
                            </el-tooltip>
                          </div>
                        </template>
                        <template slot-scope="scope">
                            <el-form-item
                              label=""
                              :prop="`error_page.` + scope.$index + `.priority`"
                              :rules="rules.priority"
                              class="aifs"
                            >
                                <el-input
                                    v-model.number="scope.row.priority"
                                    class="input-style"
                                    @change="handleChange"
                                ></el-input>
                            </el-form-item>
                        </template>
                      </el-table-column>
                      <!-- 操作 -->
                      <el-table-column :label="$t('domain.operate')" width="80">
                          <template slot-scope="scope">
                              <el-button
                                  type="text"
                                  @click="
                                      onOperator(
                                          scope.row,
                                          'delete',
                                          'error_page',
                                          scope.$index
                                      )
                                  "
                                  >{{ $t("domain.delete") }}</el-button
                              >
                          </template>
                      </el-table-column>
                    </el-table>
                    <div class="flex-row-style button-box">
                        <el-button
                            class="btn"
                            type="text"
                            @click="onOperator(null, 'create', 'error_page')"
                        >
                            + {{ addButtonText }}
                        </el-button>
                    </div>
                </div>
              </el-form-item>
            </div>

        </el-form>
    </div>
</template>

<script>
import componentMixin from "@/views/domainConfig/componentMixin";
import i18n from "@/i18n/index";
import { cloneDeep } from "lodash-es";
import { checkArrayRepeat, errorPageCodeValidator } from "@/utils/validator.utils";

export default {
    name: "errorCode",
    components: {},
    mixins: [componentMixin],
    props: {
        datas: Object,
        showForStaticAndIcdn: Boolean,
        isLockErrorPage: Boolean,
    },
    data() {
        return {
            addButtonText: i18n.t("domain.editPage.label10"),
            currentType: "create",
            form: {
              error_page: [], // 错误页面重定向
            },
            locationCodeList: [
              { label: "301", value: "301" },
              { label: "302", value: "302" },
            ],
            ipValidateResultMap: {},
            rules: {
                code: [
                    // { required: true, message: this.$t("domain.editPage.tip40"), trigger: "blur" },
                    {
                        validator: (rule, value, callback) => {
                            if (!this.showForStaticAndIcdn || this.isLockErrorPage) callback();
                            // let currentCode = [];
                            // let repeatCode = "";
                            const code = value.replace(/,$/gi, "");
                            // const bufferCode = cloneDeep(this.form.error_page);
                            //获取当前的code码
                            // bufferCode.forEach(item => {
                            //     currentCode = currentCode.concat(item.code.split(","));
                            // });
                            // repeatCode = checkArrayRepeat(currentCode);
                            if (
                                (!value)
                            ) {
                                return callback(new Error(this.$t("domain.editPage.tip40")));
                            }
                            //检查状态码重复性
                            // if (repeatCode.length > 0) {
                            //     callback(new Error(`${i18n.t("domain.detail.tip26")}${repeatCode}`));
                            // }
                            const checkList = value && value?.split(",")
                            const disabled = []
                            checkList.map(item => {
                                if (disabled.includes(item)) {
                                    callback(new Error(`${i18n.t("domain.detail.tip26")}${item}`));
                                } else {
                                    disabled.push(item)
                                }
                            })
                            // 可填写值：300～599 且非499，多个以英文逗号分隔。
                            if (!errorPageCodeValidator(code.split(","))) {
                                callback(new Error(this.$t("domain.editPage.tip30")));
                            }
                            const codeList = value && value.split(",")
                            const pattern = /^[0-9,]*$/;
                            const regex = /^(?:\d+,)*\d+$/;
                            let count = 0;
                            for (let i = 0; i < codeList.length; i++) {
                                if (!pattern.test(codeList[i])) {
                                    count++;
                                }
                            }
                            if (count > 0) return callback(new Error(this.$t("domain.editPage.tip30")));
                            if (!regex.test(value)) return callback(new Error(this.$t("domain.editPage.tip44")));
                            callback();
                        },
                        trigger: ["blur", "change"],
                    },
                ],
                url: [
                    // {
                    //     required: true,
                    //     pattern: /^(https?):\/\/[\w-]+(\.[\w-]+)+([（）()\u4E00-\u9FA5\w\-.,@?^=%&:/~+#]*[()（）\u4E00-\u9FA5\w\-@?^=%&~+#/])?$/,
                    //     message: this.$t("domain.editPage.tip43"),
                    //     trigger: "blur"
                    // },
                    {
                        validator: (rule, value, callback) => {
                            if (!this.showForStaticAndIcdn || this.isLockErrorPage) callback();
                            const urlReg = /^(https?):\/\/[\w-]+(\.[\w-]+)+([（）()\u4E00-\u9FA5\w\-.,@?^=%&:/~+#]*[()（）\u4E00-\u9FA5\w\-@?^=%&~+#/])?$/;
                            if (value === "" || value === null || value === undefined) callback(this.$t("domain.editPage.tip41"));
                            if (value.length < 16) callback(this.$t("domain.editPage.tip31"));
                            if (!urlReg.test(value)) {
                                return callback(new Error(i18n.t("domain.editPage.tip43")));
                            }
                            else callback();
                        },
                        trigger: ["blur", "change"],
                    },
			    ],
                location_code: [
                    {
                        validator: (rule, value, callback) => {
                            if (!this.showForStaticAndIcdn || this.isLockErrorPage) callback();
                            if (value === "" || value === null || value === undefined) callback(this.$t("domain.editPage.tip42"));
                            else callback();
                        },
                        trigger: ["blur", "change"],
                    },
			    ],
                priority: [
                    {
                        validator: (rule, value, callback) => {
                            if (!this.showForStaticAndIcdn || this.isLockErrorPage) callback();
                            if (value === "" || value === null || value === undefined) callback(this.$t("domain.detail.placeholder22"));
                            const num = /^[0-9]*$/;
                            if (!num.test(value)) {
                                return callback(new Error(i18n.t("domain.detail.placeholder75")));
                            }
                            if (value > 100) callback(this.$t("domain.detail.placeholder76"));
                            if (value < 1) callback(this.$t("domain.detail.placeholder77"));
                            else callback();
                        },
                        trigger: ["blur", "change"],
                    },
                ],
            },
        };
    },
    computed: {},
    watch: {
        "datas.error_page": {
            deep: true,
            handler(val) {
                this.init(val);
            },
            immediate: true,
        },
    },
    methods: {
      init(v) {
        this.form.error_page = cloneDeep(v);
      },
      handleChange() {
        this.$emit("onChange", this.form.error_page);
      },
      async onOperator(row, currentType, tabName, i) {
        this.currentType = currentType;
        if (currentType === "create") {
            const defaultFormMap = {
                error_page: { code: "", url: "", location_code: "302", priority: 10 },
            };
            row = defaultFormMap[tabName];
        }
        if (currentType === "delete") {
            const msgMap = {
                error_page: this.$t("domain.editPage.tip20"),
            };

            // 二次确认弹窗
            await this.$confirm(msgMap[tabName], this.$t("domain.delete"), {
                type: "warning",
            });
            this.form[tabName].splice(i, 1);
            this.$emit("onChange", this.form.error_page);
        } else {
            this.form[tabName].push(row);
        }
      },
    },
};
</script>

<style lang="scss" scoped>
@import "@/views/domainConfig/commonStyle.scss";
.ct-edit-wrapper {
    ::v-deep {
        .el-form--label-left .el-form-item__label {
            text-align: right;
        }
    }
}
.aifs {
    ::v-deep {
        .el-form-item__error {
            align-items: flex-start;
        }
    }
}
</style>
