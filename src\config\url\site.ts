import { PROXY_PREFIX } from "./_PREFIX";
// mock
const MOCK_URL = "https://yapi.ctcdn.cn/mock/737/aocdn";

export const siteUrl = {
    // 源站配置(查询)
    domainDetail: `${PROXY_PREFIX}/v1/domain/detail`,
    // 源站配置(提交)
    domainUpdate: `${PROXY_PREFIX}/v1/domain/update`,
    // 证书选择下拉框
    certSelector: `${PROXY_PREFIX}/v1/cert/selector`,
    // xos源站后缀列表
    xosSuffix: `${PROXY_PREFIX}/v1/domain/xstore-xos/suffix`,
    // 获取xos列表
    xosList: `${PROXY_PREFIX}/v1/domain/xstore-xos/list`,
    // 工单是否在途
    isExistOrder: `${PROXY_PREFIX}/v1/domain/exist/order`,
    // 判断是否是其他公司云备源
    checkCtBucketName: `${PROXY_PREFIX}/v1/checkCtBucketName`,
    // 获取云备源
    getBackupSource: `${PROXY_PREFIX}/v1/getCtBucketNames`,
};
