<template>
    <div
        v-loading="loading"
        class="micro-app-box"
        :class="currentMicroAppClassName"
        element-loading-spinner="el-icon-loading"
        element-loading-text="微应用加载中..."
    >
        <router-view></router-view>
    </div>
</template>

<script>
import { MicroAppModule } from "@/store/modules/microApp";

export default {
    name: "index",
    props: {
        containerId: {
            type: String,
            default: "microAppBox",
        },
    },
    computed: {
        // 当前微应用类名
        currentMicroAppClassName() {
            // const microName = AppModule.currentMicroApp;
            const microName = "udfApp";
            switch (microName) {
                case "udfApp":
                    return "ct-udf-micro-app";
            }

            return "";
        },
        loading() {
            return MicroAppModule.microAppLoading;
        },
    },
};
</script>

<style scoped lang="scss">
.micro-app-box {
    width: 100%;
    height: 100%;
}
</style>
