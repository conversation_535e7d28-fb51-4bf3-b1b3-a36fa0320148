// export * from "@cdnplus/common/types/common";
// banner模型
export type bannerData = {
    title: string;
    href: string;
};
export interface ProductSelectOptions {
    product_cname: string;
    product_code: string;
};

export interface SelectOptions {
    label: string;
    value: string;
}

// 后端框架 boolean 值是 string 类型表示
export type AlogicBoolean = "true" | "false";
// 通用的状态值
export type AlogicState = "online" | "offline";

// pager 参数
export type AlogicPager = {
    total?: number;
    sizes?: number[];
    page?: number;
    pageSize?: number;
};

// 万进制
export type TenThousandUnit = "" | "万" | "亿";
// 千进制
export type ThousandUnit = "" | "K" | "KK";

// 通用的 banner 列表
export type BaseBannerItem = {
    displayOrder: string; // 排序
    title?: string; // 标题
    subtitle: string; // 副标题，默认 "-"
    href: string; // 链接，默认 "#"，部分情况下会作为 ucode 使用
    viewer: string; // 渲染类型，目前用到的仅 "buttons"
};
