// ctFetch 请求参数 options 声明
interface CtFetchOptions {
    cache?: boolean; // 是否缓存请求数据
    encodeParams?: boolean; // 是否对参数进行转码
    clearEmptyParams?: boolean; // 是否清空空字段
    data?: {
        // get 请求的数据
        [key: string]: any | undefined;
    };
    method?: string; // 请求类型 "GET" | "POST"
    transferType?: string; // 设置 Content-Type 的标识，"json" | "form" | "url" | "ajson"
    clearQsWithPost?: boolean; // 是否清空 post 的 query
    wrapperDataWithPost?: boolean; // 是否将 post 请求中的 workspaceId 移到 body.data 下，不需要的需要手动关闭
    headers?: {
        // 如果只是修改 content-type 可以使用 transferType 字段控制
        [key: string]: any;
    };
    body?: {
        // post 请求的数据，标准应该放在 data 中
        data?: {
            [key: string]: any;
        };
        // 如果没有放在 data 中，可以通过 wrapperDataWithPost 控制是否自动追加到 body.data 下，项目中默认打开了
        [key: string]: any;
        // 当使用 file 时，需要根据需要关闭 wrapperDataWithPost
        file?: Blob;
    };
    signal?: any; // 请求取消控制器订阅
    parserType?: "json" | "text" | "blob" | "formData"  // parserType值可以是json(默认) text blob formData
}

// 响应数据
interface CtFetchResponse {
    code: string;
    reason: string;
    serial: string;
    data: {
        [propName: string]: any;
    };
    url?: string; // 业务侧将请求 url 组装到数据中，用于错误处理
    hashRoute?: string; // 为了便于处理，自定义一个 hashRoute 配置，用于判断请求是否当前页面发起
}

// ctFetch.config 配置的参数（非全部，按需添加声明）
interface CtFetchConfigOptios {
    // 为了便于处理，自定义一个 hashRoute 配置，用于判断请求是否当前页面发起
    hashRoute?: string;
    // 详见 node_modules\alogic-base-web\src\ctFetch\config.js
    // 请求超时时间
    timeout?: number;
    // 请求路径前缀
    prefix?: string;
    mergeDataStrategy?: string; // 可能弃用了
    // 要传输的数据
    data?: {
        [key: string]: any;
    };
    // POST方法中，包裹body中的data字段： {body: {data: {} } }
    wrapperDataWithPost?: boolean;
    // 是否对参数进行encode
    encodeParams?: boolean;
    // POST请求 是否清空 查询字符串
    clearQsWithPost?: boolean;
    // 是否清空 空参数
    clearEmptyParams?: boolean;
    // 设置 post 数据格式
    transferType?: "url" | "json" | "form";
}

// ctFetch 请求拦截器处理函数的参数
interface CtFetchReqInterceptorCbOptions {
    url: string; // 无参数
    rawUrl: string; // 有参数
    params: {
        [param: string]: any;
    };
    config: CtFetchConfigOptios;
}

// ctFetch 请求拦截器 use 方法参数中的回调处理函数
interface CtFetchReqInterceptorCb {
    (options: CtFetchReqInterceptorCbOptions): boolean;
}

// ctFetch 响应拦截器处理函数的参数
interface CtFetchResInterceptorCbOptions {
    data: CtFetchResponse | CtFetchResInterceptorCbOptions; // 当属于 ctFetch 内置错误时，相当于多了一层
    url: string;
    config?: CtFetchConfigOptios; // 当属于 ctFetch 内置错误时，没有 config 字段
}

// ctFetch 拦截器 use 方法参数中的回调处理函数
interface CtFetchResInterceptorCb {
    (options: CtFetchResInterceptorCbOptions): boolean;
}
