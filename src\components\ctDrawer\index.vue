<template>
    <el-drawer
        direction="rtl"
        :visible.sync="insideShow"
        :close-on-press-escape="false"
        :show-close="false"
        :wrapperClosable="wrapperClosable"
        custom-class="common-drawer"
        v-bind="$attrs"
        v-on="$listeners"
        @close="handleClose"
    >
        <!--标题-->
        <template slot="title">
            <slot name="title">
                <div class="flex-row-style g-dialog-title">
                    <span class="title">{{ title }}</span>
                    <i class="el-icon-close icon-close" @click="handleClose" />
                </div>
            </slot>
        </template>
        <!--主体-->
        <div class="wrapper-main">
            <div class="content-box">
                <slot></slot>
            </div>
            <div v-if="widthFooter" class="flex-row-style wrapper-footer">
                <el-button @click="handleClose">{{ cancelText }}</el-button>
                <el-button :loading="loading" type="primary" @click="onSubmit">{{ submitText }}</el-button>
            </div>
        </div>
    </el-drawer>
</template>

<script>
export default {
    props: {
        visible: {
            type: Boolean,
            required: true,
            default: false,
        },
        title: {
            type: String,
            required: false,
            default: "",
        },
        widthFooter: {
            type: Boolean,
            default: true,
        },
        wrapperClosable: {
            type: Boolean,
            default: true,
        },
        loading: {
            type: Boolean,
            default: false,
        },
        submitText: {
            type: String,
            default: "确 定",
        },
        cancelText: {
            type: String,
            default: "取 消",
        },
    },
    data() {
        return {
            insideShow: false,
        };
    },
    watch: {
        visible: {
            handler(val) {
                this.insideShow = val;
            },
            immediate: true,
        },
    },
    methods: {
        /**
         * 提交
         */
        onSubmit() {
            this.$emit("submit");
            // this.handleClose();
        },
        /**
         * 关闭
         */
        handleClose() {
            this.$emit("update:visible", false);
            this.$emit("close", false);
        },
    },
};
</script>

<style scoped lang="scss">
.g-dialog-title {
    justify-content: space-between;
    font-size: 14px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: rgba(0, 0, 0, 0.6);
    line-height: 20px;
}

.wrapper-main {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;

    .content-box {
        width: 100%;
        flex: 1;
        padding: 0 20px;
        overflow: auto;
    }
}

.wrapper-footer {
    flex-shrink: 0;
    justify-content: flex-end;
    padding: 20px;
}

.icon-close {
    cursor: pointer;
}
</style>
