<!-- 批量编辑 check 组件，选中后，可以编辑 -->
<template>
    <div class="batch-edit-checked">
        <el-checkbox class="batch-edit-checked-checkbox" :value="checked" @change="handleChange" :disabled="disabled">
        </el-checkbox>
        <slot></slot>
    </div>
</template>

<script lang="ts">
import { Component, Vue, Model, Prop } from "vue-property-decorator";

@Component({
    name: "BatchEditChecked",
})
export default class BatchEditChecked extends Vue {
    @Model('input', { type: Boolean, default: false }) readonly checked!: boolean;
    @Prop({ type: Boolean, default: false }) disabled!: boolean;
    handleChange(value: boolean): void {
        this.$emit("input", value);
    }
}
</script>

<style lang="scss" scoped>
.batch-edit-checked {
    display: flex;
    align-items: center;
    gap: 12px;
    width: 100%;

    &-checkbox {
        align-self: flex-start;
        padding-top: 7px;
    }
}
</style>
