<template>
    <!-- <div class="domain-config-style">
        <ct-info-card id="basicInfo" title="基础信息">
            <basic-info />
        </ct-info-card>
        <router-view></router-view>
    </div> -->
    <router-view></router-view>
</template>

<script>
// import basicInfo from "@/views/domainConfig/basicInfo";
// import ctInfoCard from "@/components/ctInfoCard/index.vue";
import { SecurityAbilityModule } from "@/store/modules/securityAbility";

export default {
    name: "index",
    components: {
        // basicInfo,
        // ctInfoCard,
    },
    mounted() {
        window.custom.emit("cdnRenderReady", true);
    },
    beforeDestroy() {
        SecurityAbilityModule.CLEAR_ALL_SECURITY_DATA();
    },
};
</script>

<style scoped lang="scss">
// .domain-config-style {
//     width: 100%;
//     height: 100%;
//     overflow: hidden;
// }
</style>
