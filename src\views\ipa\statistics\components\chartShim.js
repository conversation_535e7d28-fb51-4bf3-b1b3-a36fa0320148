import { downloadCsv } from "@/utils";
import { cloneDeep } from "lodash-es";
import i18n from "@/i18n";
import { ScaleModule } from "@/store/modules/scale";

/**
 * 原有图表文件的垫片
 */
export default {
    data() {
        return {
            emitOnMounted: true,
            // 各个图表需要用到的查询参数，不单独拆分
            queryForm: {
                bandwidthType: 0,
                flowType: 0,
                connectionNumberType: i18n.t("statistics.eas.tab[2]"),
                currentType: "bandwidth",
            },
            compareConfig: {
                useCompare: false, // 是否使用对比
                visible: false,
                timeRangeArr: [null, null],
                // notShowWord: false,
            },
            // 当前查询请求的发送编号, 发送请求前会自增1, 请求结束后会判断该编号, 如果不是最新的, 则不进行后续操作
            loadingIndex: 1,
        };
    },

    mounted() {
        // 在当前组件初始化完成后，通知父组件，进行一次查询
        if (this.emitOnMounted) {
            this.$ctBus.$emit("childPaneMounted");
        }
    },

    methods: {
        updateParamsButNotTime(payload) {
            // 只有在当前图表组件在对比状态下需要去更新，其余情况交给updateAgency去处理
            // if (this.compareConfig.visible) {
            this.reqParamsFromParentBackup = {
                ...this.reqParamsFromParentBackup,
                ...payload,
            };
            // }
        },
        // bandwidthChartChange(payload, type, useCompare) {
        //     this.compareConfig.visible = false;
        //     this.updateAgency(null, type, useCompare);
        // },
        // flowChartChange(payload, type, useCompare) {
        //     this.compareConfig.visible = false;
        //     this.updateAgency(null, type, useCompare);
        // },
        /**
         *
         * @param {*} payload 父组件传递下来查询参数
         * @param {*} type 当前更新的图表
         */
        updateAgency(payload, type, compareConfig) {

            if (payload) this.reqParamsFromParentBackup = { ...payload };
            // 原有查询方法的代理，组合父组件和当前组件的查询参数
            const reqParam = {
                ...this.reqParamsFromParentBackup,
                account_id: this.$store.state.user.userInfo.userId,
                start_time: this.reqParamsFromParentBackup?.timeRange
                    ? Math.floor(this.reqParamsFromParentBackup.timeRange[0] / 1000)
                    : undefined,
                end_time: this.reqParamsFromParentBackup.timeRange
                    ? Math.floor(this.reqParamsFromParentBackup.timeRange[1] / 1000)
                    : undefined,
            };

            if (type === "bandwidthChart" || type === "flowChart") {
                this.compareConfig = {
                    ...this.compareConfig,
                    ...compareConfig,
                }
                reqParam.type = payload.type;
            }

            if (["domainRanking", "topClientIp"].includes(type)) {
                delete reqParam.type;
            }
            delete reqParam.timeRange;
            this.initData(reqParam, this.compareConfig.useCompare, reqParam.timeCompare);

            const hasTable = ['bandwidthChart', 'flowChart', 'connectionChart'].includes(type);
            const tableType = type === 'bandwidthChart' ? 'bandwidth' : type === 'flowChart' ? 'flow' : 'connection';
            hasTable &&
                this.$refs[`${tableType}Table`]?.initTable(tableType, reqParam, this.compareConfig.useCompare, reqParam.timeCompare);

        },
        handleTimeCompare() {
            this.compareConfig.visible = true;
        },
        resetDataCompare() {
            this.compareConfig.useCompare = false;
            this.updateAgency();
        },
        handleSetDataCompareChart(payload) {
            this.compareConfig.useCompare = true;
            // this.compareConfig.notShowWord = true;
            this.compareConfig.timeRangeArr = cloneDeep(payload);
            this.updateAgency(null, this.currentCmp || null, true);
        },
        handleDownloadAgency() {
            this.download();
        },
        // 通用下载方法，如果需要定制则重写
        downloadExcelForUser({ name, str, instType }) {
            const timeRange = this.reqParamsFromParentBackup.timeRange;
            let t1 = this.$dayjs(timeRange[0]).format("YYYY-MM-DD HH:mm:ss");
            let t2 = this.$dayjs(timeRange[1]).format("YYYY-MM-DD HH:mm:ss");
            let titleName = "统计域名";
            let titleValue = this.reqParamsFromParentBackup.domain;
            if (instType === 2) {
                titleName = "统计实例";
                titleValue = this.instName
            }

            // 通用的输出内容
            let preStr = `开始时间:${t1}\n` + `结束时间:${t2}\n`;
            preStr += `${titleName}:,${titleValue}\n`;
            preStr += `导出时间:,${new Date().toLocaleString()}\n\n`;

            t1 = t1.replace(/-|\s|:/g, "").slice(0, 12);
            t2 = t2.replace(/-|\s|:/g, "").slice(0, 12);

            downloadCsv(`${name}${t1}-${t2}`, preStr + str);
        },
        // 通用下载方法，如果需要定制则重写
        downloadExcel({ name, str }) {
            const timeRange = this.reqParamsFromParentBackup.timeRange;
            let t1 = this.$dayjs(timeRange[0]).format("YYYY-MM-DD HH:mm:ss");
            let t2 = this.$dayjs(timeRange[1]).format("YYYY-MM-DD HH:mm:ss");
            // 说明：由于下载时需要用到请求参数 searchParams 中的数据，所以需要在 getData 中主动缓存使用
            t1 = t1.replace(/-|\s|:/g, "").slice(0, 12);
            t2 = t2.replace(/-|\s|:/g, "").slice(0, 12);

            downloadCsv(`${name}${t1}-${t2}`, str);
        },
        timeFormatFilter(data) {
            return this.$dayjs(data).format("YYYY-MM-DD HH:mm:ss");
        },
    },

    computed: {
        // ***单位换算：获取进制基数
        scale() {
            return ScaleModule.scale;
        },
        MB() {
            return this.scale === 1024 ? "MiB" : "MB";
        },
        GB() {
            return this.scale === 1024 ? "GiB" : "GB";
        },
        Mbps() {
            return this.scale === 1024 ? "Mibps" : "Mbps";
        }
    }
};
