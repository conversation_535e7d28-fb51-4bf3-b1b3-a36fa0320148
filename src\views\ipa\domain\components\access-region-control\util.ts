export type AccessRegionControlListItem = {
    country: string[];
    vendor: string[];
    area_province: string[];
    priority: number;
};

export type AccessRegionControlForm = {
    switch: 1 | 2;
    type: 1 | 2;
    list: AccessRegionControlListItem[];
};

export type AccessRegionCountryItem = {
    label: string;
    value: string;
    children?: AccessRegionCountryItem[];
};

export type AccessRegionISPItem = {
    isp: string;
    isp_c: string;
};

export type AccessRegionProvinceItem = {
    province: string;
    province_c: string;
};

export type OrigianlAccessRegionControlListItem = {
    continent: string;
    continentCn: string;
    continentId: number;
    zones: {
        continentId: number;
        countries: {
            country: string;
            countryCn: string;
            countryEn: string;
            countryId: number;
            countryNum: string;
            zoneId: number;
        }[];
        zone: string;
        zoneCn: string;
        zoneId: number;
    }[];
};

export type AccessRegionControlFormData = {
    switch: 1 | 2;
    type?: 1 | 2;
    list?: {
        country: string;
        vendor: string;
        area_province: string;
    }[];
};

export const defaultAccessRegionControlListItem = (): AccessRegionControlListItem => ({
    country: [],
    vendor: [],
    area_province: [],
    priority: 10,
});

export const defaultAccessRegionControlForm = (): AccessRegionControlForm => ({
    switch: 2,
    type: 1,
    list: [defaultAccessRegionControlListItem()],
});

export const parseAccessRegionControlList = (str: string) => {
    try {
        return str?.split("|").filter(itm => !!itm);
    } catch (error) {
        return [];
    }
};
