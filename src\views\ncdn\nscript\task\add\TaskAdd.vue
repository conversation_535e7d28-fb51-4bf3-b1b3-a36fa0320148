<template>
    <ct-section-wrap :headerText="getHeaderText">
        <ct-box :class="['fix-box', 'table-scroll-wrap']">
            <div class="task-form" v-loading="loading">
                <el-form
                    :model="form"
                    status-icon
                    :rules="rules"
                    ref="taskForm"
                    label-width="100px"
                    class="demo-ruleForm"
                >
                    <el-row>
                        <el-col :span="14">
                            <el-form-item label="脚本名称" prop="task_name">
                                <el-input
                                    v-if="type === 'add'"
                                    v-model="form.task_name"
                                    placeholder="2-64位，只支持小写字母、数字、下划线，开头结尾只允许小写字母和数字"
                                ></el-input>
                                <span v-else>{{ form.task_name }}</span>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item
                                ><el-button type="text" @click="getDictionaryList"
                                    >查看已创建的全局字典</el-button
                                ></el-form-item
                            >
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="20">
                            <el-form-item label="脚本内容" prop="src_code">
                                <div>
                                    <ct-monaco
                                        v-model.trim="form.src_code"
                                        :minimap="false"
                                        auto-size
                                        language="lua"
                                    />
                                </div>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
            <div class="dictionary-flootr">
                <el-button @click="goBack()">{{ isDisabled ? "返回" : "取消" }}</el-button>
                <el-button type="primary" v-if="!isDisabled" :disabled="!isContentModified" @click="submit">确认提交</el-button>
            </div>
            <dictionary-dialog :detailVisible="detailVisible" @cancel="cancel" />
        </ct-box>
    </ct-section-wrap>
</template>

<script lang="ts">
import { Component, Mixins, Vue, Ref } from "vue-property-decorator";
import { ntaskUrl } from "@/config/url/ncdn/nscript";
import { easyClone, getImgSize } from "@/utils";
import { deploymentStatusMap, GetCtiamButtonAction } from "@/config/map";
import { dictionaryName } from "@/config/npattern";
import DictionaryDialog from "../dialog/DictionaryViewDialog.vue";
// 校验的表单类型
import { ElForm } from "element-ui/types/form";
import { isEqual } from "lodash-es";
import { CtiamCode } from "@/utils/ctFetch/errorConfig";
import { checkCtiamButtonAuth } from "@/store/modules/menu";

const dictionaryNameReg = new RegExp(dictionaryName);
type updateReq = {
    taskId?: string;
    taskName: string;
    srcCode: string;
};
@Component({ components: { DictionaryDialog } })
export default class AddTask extends Mixins(Vue) {
    private deploymentStatusMap = deploymentStatusMap;
    private loading = false;
    private detailVisible = false;
    private form = {} as any;
    private oldForm = {} as any;
    private rules = {
        // eslint-disable-next-line @typescript-eslint/camelcase
        task_name: [
            { required: true, message: "请输入脚本名称", trigger: "blur" },
            { validator: this.validateDictionary, trigger: "blur" },
        ],
        // eslint-disable-next-line @typescript-eslint/camelcase
        src_code: [{ required: true, message: "请输入脚本内容", trigger: "blur" }],
    };
    get getHeaderText() {
        const type = this.$route.query.operateType;
        return type === "edit"
            ? "编辑全局task脚本"
            : type === "add"
            ? "添加全局task脚本"
            : "查看全局task脚本";
    }
    get isContentModified() {
        return !isEqual(this.form?.src_code, this.oldForm?.src_code);
    }
    get isDisabled() {
        return this.$route.query.operateType === "detail";
    }
    get type() {
        return this.$route.query.operateType;
    }
    get getTaskId() {
        return this.$route.query.taskId;
    }
    validateDictionary(rule: any, value: string, callback: Function) {
        if (!dictionaryNameReg.test(value)) {
            return callback(
                new Error(
                    "脚本名称格式错误，请输入2-64位,只支持小写字母、数字、下划线，开头结尾只允许小写字母和数字"
                )
            );
        }
        callback();
    }
    private async getDictionaryList() {
        await checkCtiamButtonAuth(GetCtiamButtonAction("udfDictView"));
        this.detailVisible = true;
    }
    private cancel() {
        this.detailVisible = false;
    }
    mounted() {
        this.initForm();
    }
    async initForm() {
        if (this.type !== "add") {
            try {
                this.loading = true;
                const res = await this.$ctFetch<{ result: {} }>(ntaskUrl.TaskDetail, {
                    method: "POST",
                    body: {
                        data: { taskId: this.getTaskId },
                    },
                    headers: {
                        "Content-Type": "application/json",
                    },
                });
                this.form = easyClone(res.result);
                this.oldForm = easyClone(this.form);
            } catch (error) {
                const isCtiamError = CtiamCode.includes((error as any)?.data?.code);
                if (isCtiamError) {
                    this.$errorHandler(error);
                } else {
                    this.$message.error((error as any).data.reason);
                }
            } finally {
                this.loading = false;
            }
        }
    }
    private goBack() {
        this.$router.push({
            name: "nscript.task",
            query: {
                isRefresh: "fasle",
            },
        });
    }

    @Ref("taskForm") readonly taskFormRef!: ElForm;
    private async submit() {
        try {
            await this.$ctUtil.formValidate2Promise(this.taskFormRef);
            await this.taskFormRef.validate();
            const operationType = this.getHeaderText.split("全局task脚本")[0];
            if (getImgSize(btoa(encodeURIComponent(this.form.src_code))) > 5120) {
                return this.$message.warning("脚本内容不能超过5MB！");
            }
            if (JSON.stringify(this.form) === JSON.stringify(this.oldForm)) {
                this.$router.push({
                    name: "nscript.task",
                    query: {
                        isRefresh: "fasle",
                    },
                });
                return;
            }
            await this.$confirm(`此操作将对全局task脚本进行${operationType}操作，是否继续？`, `提示`, {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            });
            this.loading = true;
            const params = {
                taskId: this.getTaskId,
                // eslint-disable-next-line @typescript-eslint/camelcase
                taskName: String(this.form.task_name),
                // eslint-disable-next-line @typescript-eslint/camelcase
                srcCode: btoa(encodeURIComponent(this.form.src_code)),
            } as updateReq;
            if (this.type === "add") delete params.taskId;
            const reqUrl = this.type === "add" ? ntaskUrl.AddTask : ntaskUrl.UpdateTask;
            await this.$ctFetch(reqUrl, {
                method: "POST",
                body: { data: params },
                headers: {
                    "Content-Type": "application/json",
                },
            });
            this.$message.success("全局task脚本下发成功");
            this.$router.push({ name: "nscript.task", query: { isRefresh: "true" } });
        } catch (error) {
            const isCtiamError = CtiamCode.includes((error as any)?.data?.code);
            if (isCtiamError) {
                this.$errorHandler(error);
            } else {
                const errData = error as any;
                if (errData.data && errData.data.reason) {
                    return this.$message.error(errData.data.reason);
                }
            }
        } finally {
            this.loading = false;
        }
    }
}
</script>

<style lang="scss" scoped>
.ct-section-wrap {
    ::v-deep .el-scrollbar__wrap .el-scrollbar__view {
        height: auto !important;
    }
}

@media screen and (min-width: 992px) {
    .el-form {
        ::v-deep .el-form-item__content {
            width: 100%;
            .el-input {
                width: 90%;
            }
        }
    }
}

.el-form {
    ::v-deep .el-row:nth-last-child(1) {
        .el-form-item__label {
            float: none;
            display: inline-block;
        }
        .el-form-item__content {
            margin-left: 30px !important;
        }
    }
}

.dictionary-flootr {
    margin-top: 20px;
    display: flex;
    justify-content: center;
    align-content: center;
}
</style>
