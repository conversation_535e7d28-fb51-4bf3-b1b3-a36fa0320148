<!--
* 特殊后缀结尾的域名展示的提示，用法请全局搜索 domain-ends-tip
* 如果是不相关的提示，不要引入新的判断条件，建议拷贝一份，修改后使用，避免污染公共组件
-->
<template>
    <el-tooltip
        v-if="endsup && addon"
        effect="dark"
        placement="top"
        popper-class="aocdn-ignore-lock-tip-wrapper"
    >
        <div slot="content" class="lock-tip">
            <span>{{ tipContent }}</span>
        </div>
        <slot></slot>
    </el-tooltip>
    <div v-else-if="addon">
        <slot></slot>
    </div>
</template>

<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";

@Component({
    name: "DomainEndsTip",
})
export default class DomainEndsTip extends Vue {
    /**
     * 是否是特殊后缀结尾的域名
     */
    @Prop({
        type: Boolean,
        default: false,
    })
    endsup!: boolean;
    /**
     * v-if 条件
     */
    @Prop({
        type: Boolean,
        default: true,
    })
    addon!: boolean;

    get tipContent() {
        return this.$t("domain.editPage.tip27");
    }
}
</script>
