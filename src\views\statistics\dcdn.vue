<template>
    <ct-section-wrap :headerText="headerText" :headerTip="headerTip" class="statistics-entry-wrapper">
        <div class="statistics-entry">
            <el-card>
                <nested-menu
                    v-model="currentMenu"
                    :menuList="getMenuList"
                    :searchbarLoading="searchBarLoading"
                >
                    <transition name="search-bar-fade" mode="out-in">
                        <component
                            class="statistics-search-bar"
                            :is="getSearchBar"
                            @search="getData"
                            @download="download"
                            ref="search"
                            :paramFlag="getSearchParamsFlag"
                            @initialized="onSearchBarInitialized"
                            :periodOptions="getPeriodOptions"
                            :childPanelMounted="childPanelMounted"
                            :currentMenu="currentMenu"
                        />
                    </transition>
                </nested-menu>
            </el-card>
            <chart-wrapper :useWrapper="useWrapper">
                <transition name="component-fade" mode="out-in">
                    <component
                        :is="getChart"
                        ref="childPane"
                        class="child-pane"
                        @childPanelMounted="onChildPanelMounted"
                        :currentMenu="currentMenu"
                    />
                </transition>
            </chart-wrapper>
        </div>
    </ct-section-wrap>
</template>

<script lang="tsx">
import { Component } from "vue-property-decorator";
import EntryMixin from "./entryMixin";
import type { MenuListItem } from "./components/statistics.d";
import { cloneDeep } from "lodash-es";
import ChartWrapper from "./components/chartWrapper.vue";
import { nUserModule } from "@/store/modules/nuser";

@Component({
    components: { ChartWrapper }
})
export default class StatisticsEntry extends EntryMixin {
    get dcdnUsageComponentList(): MenuListItem[] {
        return [
            {
                name: this.$t("statistics.common.tab[0]") as string,
                prop: "BandwidthFlowWhole",
            }, {
                name: this.$t("statistics.common.tab[1]") as string,
                prop: "MissWhole",
            }, {
                name: this.$t("statistics.common.tab[2]") as string,
                prop: "RequestWhole",
            }, {
                name: this.$t("statistics.common.tab[3]") as string,
                prop: "Hit",
            }, {
                name: this.$t("statistics.common.tab[4]") as string,
                prop: "StatusCode",
            },{
                name: this.$t("statistics.common.tab[5]") as string,
                prop: "BackToOriginStatusCode",
            }, {
                name: this.$t("statistics.common.tab[6]") as string,
                prop: "PvUv",
            }, {
                name: this.$t("statistics.common.tab[7]") as string,
                prop: "Provider",
            }, {
                name: this.$t("statistics.common.tab[9]") as string,
                prop: "PrivateNetworkAcceleratorWhole",
            }
        ]
    }
    get isFcdnCtyunCtclouds() {
        return nUserModule.isFcdnCtyunCtclouds;
    }

    get getMenuList() {
        let defaultMenuList: MenuListItem[] = cloneDeep(this.defaultMenuList).map((item: MenuListItem) => {
            if (item.prop === "usage" && item.children) return { ...item, children: this.dcdnUsageComponentList };
            else return item;
        })

        // 虚拟专线不显示
        if (!this.isDedicatedLineShow && defaultMenuList[0].children) {
            defaultMenuList[0].children = defaultMenuList[0].children.filter(item => item.prop !== "PrivateNetworkAcceleratorWhole");
        }

        // 国际站默认不展示 地区运营商 和 下载速度
        if (this.isCtclouds && defaultMenuList[0].children) {
            defaultMenuList[0].children = defaultMenuList[0].children.filter(item => item.prop !== "DownloadSpeed");
        }

        if (this.isFcdnCtyunCtclouds) {
            const defaultMenuListByFilter = this.filterCtiamAuthTabs(defaultMenuList);
            // ctyun（国内+国际） 接入了 ctiam 权限管控，需要过滤一级菜单tab权限
            defaultMenuList = this.filterAuthTab(this.getAuthTabs, defaultMenuListByFilter);
        } else {
            defaultMenuList = this.filterAuthTab(this.getAuthTabs, defaultMenuList);
        }

        this.setCanAuthorizedTab(defaultMenuList);

        return defaultMenuList;
    }

    get getSearchParamsFlag() {
        const paramsMap: Record<string, string> = {
            BandwidthFlowWhole:
                "dcdnProduct, label, multipleDomain, isp, area, timePicker, timeCompare, protocol, ipProtocol,abroad, timeGranularity",
            MissWhole: "dcdnProductOther, label, multipleDomain, area, timePicker,abroad, timeGranularity",
            RequestWhole: "dcdnProduct, label, multipleDomain, isp, area, timePicker, ipProtocol,abroad, timeGranularity",
            Hit:
                "dcdnProductOther, label, multipleDomain, timePicker, isp, area, protocol, ipProtocol,abroad, timeGranularity",
            StatusCode:
                "dcdnProduct, label, multipleDomain, isp, area, timePicker, protocol, ipProtocol,abroad, timeGranularity",
            BackToOriginStatusCode:
                "dcdnProduct, label, multipleDomain, isp, area, timePicker, protocol, ipProtocol,abroad, timeGranularity",
            PvUv: "dcdnProduct, label, multipleDomain, area, timePicker,abroad",
            Provider: "dcdnProduct, label, multipleDomain, isp, area, timePicker, download,abroad, ipProtocol",
            PrivateNetworkAcceleratorWhole:
                "dcdnProduct, label, multipleDomain, timePicker, timeCompare, timeGranularity, isOnlyShow5Min, timeRangeHalfYear",
            HotUrl: "dcdnProduct, label, multipleDomain, code, sort, timePicker, download, hourly",
            HotUrlMiss: "dcdnProduct, label, multipleDomain, code, sort, timePicker, download, hourly",
            HotReferer: "dcdnProduct, label, multipleDomain, sort, timePicker, download, hourly",
            DomainRank: "dcdnProduct, label, multipleDomain, sort, timePicker, download",
            TopIp: "dcdnProduct, label, multipleDomain, area, code, sort, timePicker, download, hourly",
        };

        return paramsMap[this.getSubMenu] || "";
    }
}
</script>

<style scoped lang="scss">
@import "./index.scss";
</style>
