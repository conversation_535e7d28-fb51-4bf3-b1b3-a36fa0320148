import { NEW_PREFIX } from "../_PREFIX";
// import { LOGIC_ROUTE_PREFIX } from "@cdnplus/common/config/url/_PREFIX";
export const nCertificateUrl = {
    // createCert: NEW_PREFIX + "/cert/create",
    createDomainCert: NEW_PREFIX + "/cert/domain/create",
    // deleteCert: NEW_PREFIX + "/cert/delete",
    // updateCert: LOGIC_ROUTE_PREFIX + "/cert/Update",
    updateCert: NEW_PREFIX + "/cert/update",
    certList: NEW_PREFIX + "/cert/list",
    certStatistics: NEW_PREFIX + "/cert/statistics",
    // listDomainByCert: NEW_PREFIX + "/cert/domain/list",
    // batchAssociate: NEW_PREFIX + "/cert/domain/batchAssociate",
    // listOption: NEW_PREFIX + "/cert/domain/list/option",
    chains: NEW_PREFIX + "/cert/verify/chains",
    // getCertById: NEW_PREFIX + "/cert/getByCertId",
    // deploys: NEW_PREFIX + "/cert/deploys",
    // certCheckDomain: NEW_PREFIX + "/cert/update/check/domain", // 证书更新授权域名校验接口
    // updateAndDeploy: NEW_PREFIX + "/cert/updateAndDeploy", // 更新接口update 和 部署接口deploys 合二为一
};
