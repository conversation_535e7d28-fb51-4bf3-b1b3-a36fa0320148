<?xml version="1.0" encoding="UTF-8"?>
<svg width="58px" height="58px" viewBox="0 0 58 58" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>编组 69</title>
    <defs>
        <rect id="path-1" x="0" y="0" width="40" height="40" rx="10"></rect>
        <filter x="-38.8%" y="-28.8%" width="177.5%" height="177.5%" filterUnits="objectBoundingBox" id="filter-2">
            <feOffset dx="0" dy="4" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="4.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.931097561   0 0 0 0 0.93655934   0 0 0 0 1  0 0 0 1 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="IP应用加速概览" transform="translate(-421.000000, -394.000000)">
            <g id="编组-11" transform="translate(391.000000, 399.000000)">
                <g id="编组-69" transform="translate(39.000000, 0.000000)">
                    <g id="矩形">
                        <use fill="black" fill-opacity="1" filter="url(#filter-2)" xlink:href="#path-1"></use>
                        <use fill="#FFFFFF" fill-rule="evenodd" xlink:href="#path-1"></use>
                    </g>
                    <g id="路由器_router-one-2" transform="translate(11.000000, 11.000000)">
                        <polygon id="路径" stroke="#A39DF7" stroke-width="1.5" fill="#A39DF7" fill-rule="nonzero" stroke-linejoin="round" points="2.7 9 0 15.3 18 15.3 15.3 9"></polygon>
                        <path d="M2.7,0 L2.7,9 L2.7,0 Z" id="路径" fill="#A39DF7"></path>
                        <line x1="2.7" y1="0" x2="2.7" y2="9" id="路径" stroke="#A39DF7" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></line>
                        <path d="M15.3,0 L15.3,9 L15.3,0 Z" id="路径" fill="#A39DF7"></path>
                        <line x1="15.3" y1="0" x2="15.3" y2="9" id="路径" stroke="#A39DF7" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></line>
                        <path d="M9,0 L9,9 L9,0 Z" id="路径" fill="#A39DF7"></path>
                        <line x1="9" y1="0" x2="9" y2="9" id="路径" stroke="#A39DF7" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></line>
                        <polyline id="路径" stroke="#A39DF7" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" points="0 15.3 0 18 18 18 18 15.3"></polyline>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>