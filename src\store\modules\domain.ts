/*
 * @Description: 获取指定权限的域名列表
 * @Author: wang y<PERSON>gong
 */

import { Module, Action, Mutation, getModule } from "vuex-module-decorators";

import store from "../index";
import { ctFetch } from "@/utils";
import { DomainListUrl } from "@/config/url";
import { SelectOptions } from "@/types/common";
import { DomainStatusMap } from "@/config/map";
import { DomainItem } from "@/types/domain";
import { DomainAction, DomainEditIconShow } from "../types";
import { DomainActionEnum, getDomainAction } from "../config";
import i18n from "../../i18n/index";
import { BaseDomain } from "./submodules/baseDomain";
import { ProductModule } from "./ncdn/nproduct";
import { StatisticsModule } from "./statistics";

export interface DomainListParams {
    action: string;
    from: string;
    history?: boolean;
}

@Module({ dynamic: true, store, name: "domain" })
class Domain extends BaseDomain {
    public domain_batch_icon_show: DomainEditIconShow = {
        basic: false,
        origin: false,
        cache: false,
        deny: false,
        sourceStation: false,
        httpReqHeader: false,
        filetypeTtl: false,
        errorCode: false,
        respHeaders: false,
        refererChain: false,
        ipBlackWhiteList: false,
    };
    public domain_edit_icon_show: DomainEditIconShow = {
        basicConfig: false,

        originConfig: false,
        sourceStation: false,
        httpReqHeader: false,
        uriRewrite: false,
        bucketOrigin: false,
        backOrigin: false,

        httpsConfig: false,
        cacheConfig: false,
        filetypeTtl: false,
        errorCode: false,
        respHeaders: false,
        cachekeyArgs: false,

        denyConfig: false,
        refererChain: false,
        ipBlackWhiteList: false,
        userAgent: false,
        urlAuth: false,

        fileHandle: false,
        fileCompress: false,
        udfScript: false,
        businessScript: false,
    };
    public domain_edit_submit: DomainEditIconShow = {
        basicConfig: false,

        sourceStation: false,
        httpReqHeader: false,
        uriRewrite: false,
        bucketOrigin: false,
        backOrigin: false,

        httpsConfig: false,

        filetypeTtl: false,
        errorCode: false,
        respHeaders: false,
        cachekeyArgs: false,

        refererChain: false,
        ipBlackWhiteList: false,
        userAgent: false,
        urlAuth: false,

        fileCompress: false,
        businessScript: false,
        // 基础配置页面：加速类型、域名类型、ipv6开关之一有改动
        partChange: false,
        // 加速区域有改动
        areaScopeChange: false,
    };

    public domainOrderStatus = 0;
    public createDomainStage: any = {
        data: {},
        isFromDetail: false,
    };

    @Mutation
    public SET_CREATE_DOMAIN_STATE_DATA(payload: { key: string; data: any }) {
        const { key, data } = payload;
        this.createDomainStage[key] = data;
    }
    @Mutation
    private SET_DOMAIN_BATCH_ICON_SHOW(name: any) {
        this.domain_batch_icon_show[name] = true;
    }
    @Mutation
    private SET_DOMAIN_EDIT_ICON_SHOW(name: any) {
        this.domain_edit_icon_show[name] = true;
    }
    @Action
    public async nGetDomainEditIconShow(name: any) {
        this.SET_DOMAIN_BATCH_ICON_SHOW(name);
        this.SET_DOMAIN_EDIT_ICON_SHOW(name);
    }
    @Mutation
    private SET_ALL_EDIT_ICON_FALSE() {
        this.domain_edit_icon_show["basicConfig"] = false;

        this.domain_edit_icon_show["originConfig"] = false;
        this.domain_edit_icon_show["sourceStation"] = false;
        this.domain_edit_icon_show["httpReqHeader"] = false;
        this.domain_edit_icon_show["uriRewrite"] = false;
        this.domain_edit_icon_show["bucketOrigin"] = false;
        this.domain_edit_icon_show["backOrigin"] = false;

        this.domain_edit_icon_show["httpsConfig"] = false;
        this.domain_edit_icon_show["cacheConfig"] = false;
        this.domain_edit_icon_show["filetypeTtl"] = false;
        this.domain_edit_icon_show["errorCode"] = false;
        this.domain_edit_icon_show["respHeaders"] = false;
        this.domain_edit_icon_show["cachekeyArgs"] = false;

        this.domain_edit_icon_show["denyConfig"] = false;
        this.domain_edit_icon_show["refererChain"] = false;
        this.domain_edit_icon_show["ipBlackWhiteList"] = false;
        this.domain_edit_icon_show["userAgent"] = false;
        this.domain_edit_icon_show["urlAuth"] = false;

        this.domain_edit_icon_show["fileHandle"] = false;
        this.domain_edit_icon_show["fileCompress"] = false;
        this.domain_edit_icon_show["udfScript"] = false;
        this.domain_edit_icon_show["businessScript"] = false;
    }
    @Action
    public async nGetDomainAllEditIcon() {
        this.SET_ALL_EDIT_ICON_FALSE();
    }
    // 域名编辑提交按钮
    @Mutation
    private SET_DOMAIN_EDIT_SUBMIT({ name, isChange }: { name: string; isChange: boolean }) {
        this.domain_edit_submit[name] = isChange;
    }
    // 批量修改
    @Mutation
    private SET_ALL_BATCH_ICON_FALSE() {
        this.domain_batch_icon_show["basic"] = false;

        this.domain_batch_icon_show["origin"] = false;
        this.domain_batch_icon_show["sourceStation"] = false;
        this.domain_batch_icon_show["httpReqHeader"] = false;

        this.domain_batch_icon_show["cache"] = false;
        this.domain_batch_icon_show["filetypeTtl"] = false;
        this.domain_batch_icon_show["errorCode"] = false;
        this.domain_batch_icon_show["respHeaders"] = false;

        this.domain_batch_icon_show["deny"] = false;
        this.domain_batch_icon_show["refererChain"] = false;
        this.domain_batch_icon_show["ipBlackWhiteList"] = false;
    }
    @Action
    public async nGetDomainAllBatchIcon() {
        this.SET_ALL_BATCH_ICON_FALSE();
    }
    @Mutation
    private SET_DOMAIN_LIST({
        action,
        list,
        requestTime,
    }: {
        action: DomainAction;
        list: DomainItem[];
        requestTime?: number;
    }) {
        const shouldAddStatusOnLabel =
            [
                DomainActionEnum.Data,
                getDomainAction("Label"),
                getDomainAction("Log"),
                getDomainAction("Report"),
            ].includes(action) || action.includes("statisticAnalysis");
        this[action].nativeList = list.map(
            (item): DomainItem => {
                item.label = shouldAddStatusOnLabel
                    ? `${item.domain} (${i18n.t(
                          `${DomainStatusMap[item.status as keyof typeof DomainStatusMap]}`
                      )})`
                    : item.domain;
                return item;
            }
        );
        this[action].list = list.map(d => d.domain);
        this[action].options = list.map(
            (item): SelectOptions => ({
                label: shouldAddStatusOnLabel
                    ? `${item.domain} (${i18n.t(
                          `${DomainStatusMap[item.status as keyof typeof DomainStatusMap]}`
                      )})`
                    : item.domain,
                value: item.domain,
            })
        );
        this[action].requestTime = requestTime ?? 0;
    }

    @Mutation
    private SET_LOADING({ action, loading }: { action: DomainAction; loading: boolean }) {
        this[action].loading = loading;
    }

    @Mutation
    public setDomainOrderStatus(status: number) {
        this.domainOrderStatus = status;
    }

    @Action
    public async GetDomainList(
        { action, shouldCheckCache }: { action: DomainAction; shouldCheckCache?: boolean } = {
            action: DomainActionEnum.Domain,
            shouldCheckCache: false,
        }
    ) {
        try {
            // 为了降低用户焦虑，仅在列表为空时，提供 loading 状态
            if (this[action].list.length === 0) {
                this.SET_LOADING({
                    action,
                    loading: true,
                });
            }
            // history 的赋值逻辑，引入 ctiam 之后，应该与引入之前保持一致
            // Home: 概览页面用的是 Data，使用 Home 替代 Data，所以 Home 也应该传 history 为 true
            const needHistory = [
                DomainActionEnum.Data,
                getDomainAction("Home"),
                getDomainAction("Label"),
                getDomainAction("Log"),
                getDomainAction("Report"),
            ];
            const prefix = window.__POWERED_BY_QIANKUN__ ? "accessone" : "ctcdn";
            const history = needHistory.includes(action) || action.startsWith(`${prefix}:statisticAnalysis`);

            if (shouldCheckCache) {
                // 校验缓存是否有效，有效则不再请求
                if (
                    this[action]?.requestTime &&
                    (this[action].requestTime || 0) + StatisticsModule.cacheTtl * 1000 > Date.now()
                ) {
                    return this[action].nativeList;
                } else {
                    this.SET_DOMAIN_LIST({
                        action,
                        list: [],
                    });
                    this.SET_LOADING({
                        action,
                        loading: true,
                    });
                }
            }

            const params = {
                action: action,
                // 域名数据来源
                from:
                    action === DomainActionEnum.Order || action === getDomainAction("DomainLog")
                        ? "order"
                        : "scc",
                // 是否查看历史域名
                history,
            } as DomainListParams;

            if (window.__POWERED_BY_QIANKUN__) params.action = "accessone_domain";

            if (!history) delete params.history;
            const { list = [] }: { list: DomainItem[] } = await ctFetch(DomainListUrl, {
                data: params,
            });

            this.SET_DOMAIN_LIST({
                action,
                // list: list.filter(d => d.enable === "true"), // 后端已过滤
                list,
                requestTime: +new Date(),
            });

            !window.__POWERED_BY_QIANKUN__ && ProductModule.GetAllProduct(list);

            return list;
        } catch (e) {
            store.$errorHandler(e);
            return [];
        } finally {
            this.SET_LOADING({
                action,
                loading: false,
            });
        }
    }
}

export const DomainModule = getModule(Domain);
