<template>
    <el-descriptions v-bind="$attrs" :label-style="labelStyle">
        <el-descriptions-item
            v-for="(item, key) in columnArr"
            :key="key"
            :label="item.label"
            :span="item.span || 1"
            :label-class-name="item.labelClassName"
            :content-style="item.contentStyle"
        >
            <template v-if="item.render">
                <slot :name="item.prop" :data="item" />
            </template>
            <template v-else-if="item.isShow">
                <!-- 域名类型 -->
                <el-select
                    v-model="domain_type"
                    class="select"
                    size="small"
                    ref="domain_type"
                    popper-class="editor-select-popover-class"
                    @change="domain_type_change"
                    @focus="domain_type_focus(domain_type)"
                    :disabled="!isEdit || disableDomainTypeChange"
                >
                    <el-option
                        v-for="item in domain_type_list"
                        v-show="item.id !== '190'"
                        :key="item.id"
                        :label="item.displayValue"
                        :value="item.id"
                    />
                </el-select>
                <div v-if="showDomainTypeTip" class="origin-tip-header domain-type-tip">
                    <span>
                        <ct-svg-icon icon-class="info-circle" class-name="icon-column-label" />
                    </span>
                    <span class="origin-tip">
                        {{ $t("domain.changeDomainType") }}
                    </span>
                </div>
            </template>
            <template v-else>
                {{ renderValue(item) }}
                <template v-if="item.isTree && item.list && item.list.length">
                    <ct-descriptions
                        :columns="item.list"
                        :form="form"
                        v-bind="item.config"
                        class="child-des"
                    />
                </template>
            </template>
        </el-descriptions-item>
    </el-descriptions>
</template>

<script>
import { mapGetters } from "vuex";
import { has, get } from "lodash-es";
import { nBasicUrl } from "@/config/url/ncdn/ndomain";
import { ProductCodeMap } from "@/config/map";
import { getI18nLabel } from "@/store/modules/ncdn/nproduct";
import { ctFetch } from "@/utils";
import { SecurityAbilityModule } from "@/store/modules/securityAbility";
import componentMixin from "@/views/domainConfig/componentMixin";
import ctSvgIcon from "@/components/ctSvgIcon";
import isWholeStationToWebSocketMessage from "./components/isWholeStationToWebSocketMessage.vue"
import isUploadToWebSocketMessage from "./components/isUploadToWebSocketMessage.vue"
import isWebSocketToUploadMessage from "./components/isWebSocketToUploadMessage.vue"
import isWholeStationToUploadMessage from "./components/isWholeStationToUploadMessage.vue"

export default {
    name: "ctDescriptions",
    mixins: [componentMixin],
    components: {
        ctSvgIcon,
    },
    props: {
        columns: {
            type: Array,
            default: () => [],
        },
        form: {
            type: Object,
            default: () => {
                return {};
            },
        },
        labelWidth: {
            type: [String, Number],
            default: 140,
        },
        defaultData: {
            type: Object,
            default: () => {
                return {};
            },
        }
    },
    data() {
        return {
            domain_type: "",
            domain_type_list: [],
            isWhole: false,
            hasOverseaProduct: false,
            area_scope: null,
            old_domain_type: "",
        };
    },
    computed: {
        /**
         * 原来ncdn子产品为全站并且开通了海外产品，就不支持变更
         * 现在fcdn改为只要当前为全站，并且加速区域为国内，就可以变更
         * 至于另外两种子产品，原先是支持变更的，这次没有影响到
         */
        disableDomainTypeChange() {
            return this.isWhole && this.area_scope !== 1;
        },
        showDomainTypeTip() {
            return this.disableDomainTypeChange && this.isEdit;
        },
        // 展示列表
        columnArr() {
            const arr = this.columns.filter(item => {
                if (!has(item, "isShow")) {
                    return item;
                }

                const isShow = item.isShow;
                if (typeof isShow === "function") {
                    return item.isShow(item);
                }

                return item.isShow;
            });

            return arr;
        },
        ...mapGetters({
            propsMap: "propsMap",
            multiplePropsMap: "multiplePropsMap",
        }),
        labelStyle() {
            return {
                width: this.labelWidth + "px",
            };
        },
        temp_domain_type() {
            return SecurityAbilityModule.domain_type;
        },
        isEdit() {
            return SecurityAbilityModule.isEdit;
        }
    },
    watch: {
        temp_domain_type: {
            deep: true,
            async handler(val) {
                this.domain_type = val;
            },
            immediate: true,
        },
        defaultData: {
            handler(val) {
                if (!val) {
                    return;
                }
                this.isWhole = val.domain_type === "006";
                this.area_scope = val.area_scope;
            },
        }
    },
    created() {
        if (!window.__POWERED_BY_QIANKUN__) {
            this.queryProp("domain_type");
        }
        
    },
    methods: {
        /**
         * 渲染值
         */
        renderValue(item) {
            if (has(item, "value")) {
                return item.value;
            }

            const prop = item.prop;
            const value = get(this.form, prop);

            if (item.formatter && typeof item.formatter === "function") {
                return item.formatter({ val: value, form: this.form, info: item, prop: item.prop });
            }

            if (item.isMap || item.mapProp) {
                const mapProp = item.mapProp || item.prop;
                const dictionaryMap = item.isMultipleMap ? this.multiplePropsMap : this.propsMap;
                // 数组情况
                if (value instanceof Array) {
                    const res = value.reduce((pre, item) => {
                        const val = get(get(dictionaryMap, mapProp), item);
                        val && pre.push(val);
                        return pre;
                    }, []);

                    return res.join("；") || "-";
                }

                return get(get(dictionaryMap, mapProp), value, "-");
            }

            if (value || value === 0) {
                return value;
            }

            return "-";
        },
        domain_type_change(val) {
            // SecurityAbilityModule.SET_DOMAIN_TYPE(val);
            this.openDialog(val, this.old_domain_type)
        },
        domain_type_focus(oldVal) {
            this.old_domain_type = oldVal
        },
        async openDialog(val, oldVal) {
            if (val === "006" || val === "" || val === undefined) {
                SecurityAbilityModule.SET_DOMAIN_TYPE(val);
                this.old_domain_type = val;
            } else {
                let message = ""
                if (oldVal === "006" && val === "105") {
                    // 全站加速 修改为 全站加速-websocket加速
                    message = <isWholeStationToWebSocketMessage />
                } else if (oldVal === "104" && val === "105") {
                    // 全站加速-上传加速 修改为 全站加速-websocket加速
                    message = <isUploadToWebSocketMessage />
                } else if (oldVal === "105" && val === "104") {
                    // 全站加速-websocket加速 修改为 全站加速-上传加速
                    message = <isWebSocketToUploadMessage />
                } else if (oldVal === "006" && val === "104") {
                    // 全站加速 修改为 全站加速-上传加速
                    message = <isWholeStationToUploadMessage />
                }
                await this.$confirm(message, this.$t("domain.list.note"), {
                    confirmButtonText: this.$i18n.t("common.dialog.submit"),
                    cancelButtonText: this.$i18n.t("common.dialog.cancel"),
                    dangerouslyUseHTMLString: true,
                    type: "warning",
                    beforeClose: async (action, instance, done) => {
                        if (action !== "confirm") {
                            this.domain_type = oldVal
                            done();
                            return;
                        }

                        try {
                            instance.confirmButtonLoading = true;
                            SecurityAbilityModule.SET_DOMAIN_TYPE(val);
                            this.old_domain_type = val;
                            setTimeout(() => {
                                done();
                                setTimeout(() => {
                                    instance.confirmButtonLoading = false;
                                }, 300);
                            }, 3000);
                        } catch (e) {
                            this.$errorHandler(e);
                            done();
                            return;
                        } finally {
                            instance.confirmButtonLoading = false;
                            instance.cancelButtonLoading = false;
                        }

                        done();
                        return;
                    }
                });
            }
        },
        async queryProp(params) {
            try {
                let rst = {};
                const data = {
                    prop: params,
                };
                rst = await ctFetch(nBasicUrl.getProp, {
                    data,
                });

                if (rst) {
                    if (params === "domain_type") {
                        const domain_type_list = []
                        rst.forEach(item => {
                            let obj = {};
                            if (ProductCodeMap[item.id]) {
                                obj = {
                                    id: item.id,
                                    displayValue: getI18nLabel(item.id),
                                };
                            }
                            domain_type_list.push(obj);
                        });
                        if (!rst.some(itm => itm.id === "104") && this.domain_type === "104") {
                            const dataObj = {
                                id: "104",
                                displayValue: "全站加速-上传加速",
                            }
                            domain_type_list.push(dataObj);
                        }
                        if (!rst.some(itm => itm.id === "105") && this.domain_type === "105") {
                            const dataObj = {
                                id: "105",
                                displayValue: "全站加速-websocket加速",
                            }
                            domain_type_list.push(dataObj);
                        }
                        // fcdn的新增和修改页面：域名类型只需要以下这三种域名类型：1.全站加速；2.全站加速-上传加速；3.全站加速-websocket加速；其余的域名类型都不需要展示
                        const whole_station_products = ["006", "104", "105"]
                        this.domain_type_list = domain_type_list.filter(item => whole_station_products?.includes(item.id))
                    }
                }
            } catch (error) {
                this.$errorHandler(error);
            }
        },
    },
};
</script>

<style scoped lang="scss">
::v-deep.no-label-style {
    margin-right: unset;

    &:after {
        content: "" !important;
    }
}

::v-deep.el-descriptions {
    .el-descriptions__body {
        .el-descriptions-row {
            .el-descriptions-item__label {
                white-space: nowrap;
                width: unset;
            }
        }
    }
}

.child-des {
    margin-top: 10px;
}
.select {
    width: 300px;
}
.tips {
    padding-top: 4px;
}
.domain-type-tip {
    color: #7c818c;
    margin-top: 6px;
}
</style>
