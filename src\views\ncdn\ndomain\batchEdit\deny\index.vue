<template>
    <div style="height: 100%">
        <el-tabs
            v-if="showCurrentTab"
            v-model="subActiveTab"
            :tab-position="'top'"
            class="domain-edit-menu">
            <el-tab-pane
                v-for="(item, key) in subTabChildren"
                :key="item.prop + key"
                :label="item.label"
                :name="item.prop"
                :ref="item.prop"
                :disabled="item.disabled"
            >
                <span slot="label">
                    <i class="el-icon-warning" v-show="domain_batch_icon_show[item.prop]" />
                    {{ item.name }}
                </span>
            </el-tab-pane>
        </el-tabs>
        <el-form
            name="refererChain"
            ref="refererChainForm"
            :model="refererChainForm"
            class="wrap-content"
            :rules="rules"
            label-width="138px"
            v-show="subActiveTab === 'refererChain'"
            label-position="left"
        >
            <p class="label-name">{{ $t("domain.detail.label14") }}</p>
            <!-- Referer防盗链 开关 -->
            <el-form-item :label="$t('domain.create.referer')" prop="referer">
                <el-switch
                    v-model="referer"
                    active-value="on"
                    inactive-value="off"
                    @change="referer_switch_change"
                ></el-switch>
            </el-form-item>
            <el-form-item
                :label="$t('domain.type')"
                prop="domainList"
                v-if="referer === 'on'"
                :rules="rules.domainList"
            >
                <div class="radio-row">
                    <el-radio-group v-model="refererType">
                        <el-radio label="allow">{{ $t("domain.detail.trustlist") }}</el-radio>
                        <el-radio label="block">{{ $t("domain.detail.blocklist") }}</el-radio>
                    </el-radio-group>
                </div>
                <div>
                    <el-input
                        v-model="refererChainForm.domainList"
                        type="textarea"
                        :rows="5"
                        :placeholder="refererPlaceholder"
                        @change="onRefererChange"
                    />
                </div>
            </el-form-item>
            <!-- 空Referer -->
            <p class="label-name" v-if="referer === 'on'">{{ $t("domain.batch.emptyReferer") }}</p>
            <!-- 是否允许空referer访问 -->
            <el-form-item
                :label="$t('domain.create.referer2')"
                prop="allow_empty"
                label-width="140px"
                v-if="referer === 'on'"
            >
                <el-switch v-model="allow_empty" active-value="on" inactive-value="off"></el-switch>
            </el-form-item>
        </el-form>
        <!-- IP黑/白名单 -->
        <el-form
            name="ipBlackWhiteList"
            ref="ipBlackWhiteListForm"
            :model="ipBlackWhiteListForm"
            class="wrap-content"
            :rules="rules"
            label-width="138px"
            label-position="left"
            v-show="subActiveTab === 'ipBlackWhiteList'"
        >
            <p class="label-name">{{ $t("domain.detail.label15") }}</p>
            <el-form-item :label="$t('domain.create.ip')" prop="ip_switch">
                <el-switch v-model="ip_switch" active-value="on" inactive-value="off"></el-switch>
            </el-form-item>

            <el-form-item :label="$t('domain.type')" prop="ip" v-if="ip_switch === 'on'" :rules="rules.ip">
                <div>
                    <div class="radio-row">
                        <el-radio-group v-model="ipType">
                            <el-radio label="allow">{{ $t("domain.detail.trustlist") }}</el-radio>
                            <el-radio label="block">{{ $t("domain.detail.blocklist") }}</el-radio>
                        </el-radio-group>
                    </div>
                    <div>
                        <el-input
                            v-model="ipBlackWhiteListForm.ip"
                            type="textarea"
                            :rows="3"
                            :placeholder="ipPlaceholder"
                            @change="onIpChange"
                        />
                    </div>
                </div>
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
import minxin from "@/components/simpleform/minxin";
import commonPattern from "@/config/npattern";
import gatewayMapMix from "../gatewayMapMinxin";
import { DomainModule } from "@/store/modules/domain";

export default {
    name: "deny",
    components: {},
    mixins: [minxin, gatewayMapMix],
    props: ["isTabValid", "showCurrentTab"],
    data() {
        return {
            domain_batch_icon_show: DomainModule.domain_batch_icon_show,
            gatewayTabs: [],
            subActiveTab: "refererChain",
            type: "1",
            text: "",
            maxNum: 400,
            isLimitNum: false, // 是否限制黑白名单ip个数
            referer: "off",
            ip_switch: "off",
            allow_empty: "off",
            form: {},
            refererChainForm: {
                domainList: "",
            },
            refererType: "allow",
            ipType: "allow",
            ipBlackWhiteListForm: {
                ip: "",
            },
            rules: {
                domainList: [{ required: false, validator: this.valid_domain_list, trigger: "blur" }],
                ip: [{ required: false, validator: this.valid_ip, trigger: "blur" }],
            },
        };
    },
    computed: {
        subTabChildren() {
            return this.gatewayTabs.find(item => item.prop === "deny").children;
        },
        formData() {
            const returnData = {
                white_referer: {},
                black_referer: {},
                ip_white_list: "",
                ip_black_list: "",
            };
            // Referer防盗链
            if (this.referer === "on") {
                if (this.refererType === "allow") {
                    returnData.white_referer.allow_empty = this.allow_empty;
                    if (this.domainList && this.domainList.length > 0) {
                        returnData.white_referer.allow_list = this.domainList;
                    } else {
                        returnData.white_referer.allow_list = [];
                    }
                    delete returnData.black_referer;
                }
                if (this.refererType === "block") {
                    returnData.black_referer.allow_empty = this.allow_empty;
                    if (this.domainList && this.domainList.length > 0) {
                        returnData.black_referer.allow_list = this.domainList;
                    } else {
                        returnData.black_referer.allow_list = [];
                    }
                    delete returnData.white_referer;
                }
            }
            // IP黑白名单
            if (this.ip_switch === "on") {
                if (this.ipType === "allow") {
                    if (this.ipBlackWhiteListForm.ip !== "" && this.ipBlackWhiteListForm.ip !== null) {
                        returnData.ip_white_list = this.ipBlackWhiteListForm.ip
                            .split("\n")
                            .map(i => i.trim())
                            .join(",");
                        delete returnData.ip_black_list;
                    }
                }
                if (this.ipType === "block") {
                    if (this.ipBlackWhiteListForm.ip !== "" && this.ipBlackWhiteListForm.ip !== null) {
                        returnData.ip_black_list = this.ipBlackWhiteListForm.ip
                            .split("\n")
                            .map(i => i.trim())
                            .join(",");
                        delete returnData.ip_white_list;
                    }
                }
            }
            return returnData;
        },
        refererPlaceholder() {
            const { maxNum } = this;
            return this.$t("domain.detail.tip38", { maxNum: maxNum });
        },
        ipPlaceholder() {
            const { maxNum, isLimitNum } = this;
            if (isLimitNum) {
                return this.$t("domain.detail.tip39", { maxNum: maxNum });
            } else {
                return this.$t("domain.detail.tip40");
            }
        },
        domainList() {
            // 需要过滤空表内容
            return this.refererChainForm.domainList
                .split("\n")
                .map(i => i.trim())
                .filter(i => i);
        },
        ipList() {
            const { ipBlackWhiteListForm = {} } = this;
            const { ip = "" } = ipBlackWhiteListForm;
            return ip.split("\n");
        },
    },
    watch: {
        isTabValid: {
            deep: true,
            handler() {
                this.gatewayTabs.forEach(item => {
                    if (item.label === this.$t("domain.create.accessControl")) {
                        item.children.forEach(e => {
                            if (this.domain_batch_icon_show[e.prop]) {
                                this.$nextTick(() => {
                                    this.subActiveTab = e.prop;
                                });
                            }
                        });
                    }
                });
            },
            immediate: true,
        },
    },
    methods: {
        valid_domain_list(rule, value, callback) {
            if (!value) {
                return callback();
            }
            let { pattern } = commonPattern.referer;
            pattern = `${pattern}|^localhost$`;
            const referReg = new RegExp(pattern);
            const { maxNum, domainList } = this;
            if (!this.refererType && this.refererChainForm.domainList) {
                return callback(new Error(this.$t("domain.detail.tip42")));
            } else if (domainList && domainList.length > maxNum) {
                return callback(new Error(this.$t("domain.detail.tip43", { maxNum: maxNum })));
            } else {
                for (let i = 0; i < domainList.length; i++) {
                    const item = domainList[i].trim();
                    // 输入的不是合格 domain
                    if (!referReg.test(item)) {
                        return callback(new Error(this.$t("domain.batch.tip3")));
                    }
                }

                // 检查重复
                const hasRepeat = new Set(domainList).size !== domainList.length;
                if (hasRepeat) {
                    return callback(new Error(this.$t("domain.detail.tip44")));
                }
                return callback();
            }
        },
        valid_ip(rule, value, callback) {
            // 支持 ip 段
            const ipReg =
                /^((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)(\/(\d|[1-2]\d|3[0-2]))?$|^([\da-fA-F]{1,4}:){6}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)(\/([1-9]?\d|(1([0-1]\d|2[0-8]))))?$|^::([\da-fA-F]{1,4}:){0,4}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)(\/([1-9]?\d|(1([0-1]\d|2[0-8]))))?$|^([\da-fA-F]{1,4}:):([\da-fA-F]{1,4}:){0,3}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)(\/([1-9]?\d|(1([0-1]\d|2[0-8]))))?$|^([\da-fA-F]{1,4}:){2}:([\da-fA-F]{1,4}:){0,2}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)(\/([1-9]?\d|(1([0-1]\d|2[0-8]))))?$|^([\da-fA-F]{1,4}:){3}:([\da-fA-F]{1,4}:){0,1}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)(\/([1-9]?\d|(1([0-1]\d|2[0-8]))))?$|^([\da-fA-F]{1,4}:){4}:((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)(\/([1-9]?\d|(1([0-1]\d|2[0-8]))))?$|^([\da-fA-F]{1,4}:){7}[\da-fA-F]{1,4}(\/([1-9]?\d|(1([0-1]\d|2[0-8]))))?$|^:((:[\da-fA-F]{1,4}){1,6}|:)(\/([1-9]?\d|(1([0-1]\d|2[0-8]))))?$|^[\da-fA-F]{1,4}:((:[\da-fA-F]{1,4}){1,5}|:)(\/([1-9]?\d|(1([0-1]\d|2[0-8]))))?$|^([\da-fA-F]{1,4}:){2}((:[\da-fA-F]{1,4}){1,4}|:)(\/([1-9]?\d|(1([0-1]\d|2[0-8]))))?$|^([\da-fA-F]{1,4}:){3}((:[\da-fA-F]{1,4}){1,3}|:)(\/([1-9]?\d|(1([0-1]\d|2[0-8]))))?$|^([\da-fA-F]{1,4}:){4}((:[\da-fA-F]{1,4}){1,2}|:)(\/([1-9]?\d|(1([0-1]\d|2[0-8]))))?$|^([\da-fA-F]{1,4}:){5}:([\da-fA-F]{1,4})?(\/([1-9]?\d|(1([0-1]\d|2[0-8]))))?$|^([\da-fA-F]{1,4}:){6}:(\/([1-9]?\d|(1([0-1]\d|2[0-8]))))?$/;

            const { ipList, maxNum, ipBlackWhiteListForm, isLimitNum, ipType } = this;

            if (!ipType && ipBlackWhiteListForm.ip) {
                return callback(new Error(this.$t("domain.detail.tip42")));
            } else if (!ipBlackWhiteListForm.ip.trim()) {
                return callback(new Error(this.$t("domain.detail.tip45")));
            } else if (ipList && ipList.length > maxNum && isLimitNum) {
                return callback(new Error(this.$t("domain.detail.tip46", { maxNum: maxNum })));
            } else {
                for (let i = 0; i < ipList.length; i++) {
                    const item = ipList[i].trim();
                    // 输入的不是 ip
                    if (!ipReg.test(item)) {
                        return callback(new Error(this.$t("domain.detail.tip47")));
                    }
                }

                // 检查重复
                const hasRepeat = new Set(ipList).size !== ipList.length;
                if (hasRepeat) {
                    return callback(new Error(this.$t("domain.detail.tip48")));
                }
                return callback();
            }
        },
        onIpChange(val) {
            if (this.ip_switch === "on" && val.type === "block") {
                this.ipBlackWhiteListForm.ip_black_list = val.ip;
                delete this.ipBlackWhiteListForm.ip_white_list;
            } else if (this.ip_switch === "on" && val.type === "allow") {
                this.ipBlackWhiteListForm.ip_white_list = val.ip;
                delete this.ipBlackWhiteListForm.ip_black_list;
            }
        },
        onRefererChange(val) {
            if (this.referer === "on" && this.refererType === "allow") {
                this.refererChainForm.white_referer = {
                    allow_empty: this.allow_empty,
                    allow_list: val.domainList,
                };
                delete this.refererChainForm.black_referer;
            }
            if (this.referer === "on" && this.refererType === "block") {
                this.refererChainForm.black_referer = {
                    allow_empty: this.allow_empty,
                    allow_list: val.domainList,
                };
                delete this.refererChainForm.white_referer;
            }
        },
        referer_switch_change(val) {
            if (val === "on") {
                this.$set(this, "allow_empty", "on");
            }
        },
    },
    created() {
        this.gatewayTabs = [...this.defaultTabs];
    },
};
</script>

<style lang="scss" scoped>
@import "@/components/index.scss";
.el-icon-warning {
    color: $color-danger;
}
.wrap-content {
    height: 100%;
    overflow-y: auto;
}
::v-deep {
    .el-tabs__header {
        margin: 0;
    }
}
.label-name {
    font-size: 14px;
    font-weight: bold;
    margin: 20px 0 8px 0;
    width: 600px;
    overflow: hidden;
}
</style>
