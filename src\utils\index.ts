// import * as CommonUtils from "@cdnplus/common/utils";
import Utils from "alogic-base-web/src/util/util.js";
import { formItemValidate2Promise, formValidate2Promise } from "./logic";

// // 1、导出通用工具函数
// export * from "@cdnplus/common/utils";
export * from "./logic";
export { default as Dom } from "./logic/dom";

// 默认导出
// export default {
//     ...CommonUtils,
// };
export * from "./ctFetch";
export { default as downloadCsv } from "./logic/downloadCsv";
// 动态加载 ccs 脚本
export const loadCss = (url: string) => {
    if (!url) {
        return Promise.reject("缺少目标 url");
    }

    return new Promise((resolve, reject) => {
        const head = document.getElementsByTagName("head")[0];
        const link = document.createElement("link");
        link.type = "text/css";
        link.rel = "stylesheet";
        link.href = url;
        head.appendChild(link);

        link.addEventListener("load", function() {
            resolve(`${url} 加载成功`);
        });
        link.addEventListener("error", function() {
            reject(`${url} 加载失败`);
        });
    });
};

export function loadJs(url: string) {
    if (!url) {
        return Promise.reject("缺少目标 url");
    }

    return new Promise((resolve, reject) => {
        const body = document.getElementsByTagName("body")[0];
        const script = document.createElement("script");
        script.type = "text/javascript";
        script.src = url;
        body.appendChild(script);

        script.addEventListener("load", function() {
            resolve(`${url} 加载成功`);
        });
        script.addEventListener("error", function() {
            reject(`${url} 加载失败`);
        });
    });
}

export const getImgSize = (str: string) => {
    const strLength = str.length;
    const fileLength = parseInt(String(strLength - (strLength / 8) * 2));
    let size = "";
    size = (fileLength / 1024).toFixed(2);
    return parseInt(size);
};

/**
 * @description: 通过某个字段搜索出某个元素在对象数组里的位置
 * @param {Array} arr 要搜索的数组
 * @param {String} field 搜索字段名
 * @param {any} val 搜索字段的值
 * @return {Number}
 */
export const getIndexByField = (arr: any, field: any, val: string) => {
    if (!(arr instanceof Array) || typeof field !== "string") return -1;
    for (let index = 0; index < arr.length; index++) {
        if (arr[index][field] === val) return index;
    }
    return -1;
};

export function withResolver<T>() {
    let resolve!: (value: T | PromiseLike<T>) => void;
    let reject!: (reason?: any) => void;
    const promise = new Promise<T>((_resolve, _reject) => {
      resolve = _resolve;
      reject = _reject;
    });
   
    return { promise, resolve, reject };
}

const {
    throttle,
    fastCopy,
    isEmptyObject,
    serializeObj,
    appendQuery,
    getUrlParamsObject,
    clearEmptyInObj,
    debounce,
} = Utils;

/**
 * 3、快捷导出 alogic-base-web/util 中常用的工具函数
 */
export {
    throttle,
    fastCopy,
    isEmptyObject,
    serializeObj,
    appendQuery,
    getUrlParamsObject,
    clearEmptyInObj,
    debounce,
};
/**
 * 4、默认导出，使用情况较少
 */
export default {
    ...Utils,
    formItemValidate2Promise,
    formValidate2Promise
};
