<template>
    <el-transfer
        v-model="valueInside"
        :data="data"
        @change="handleChange"
        v-bind="$attrs"
        v-on="$listeners"
    />
</template>

<script>
export default {
    props: {
        value: {
            type: Array,
            default: () => [],
        },
        data: {
            type: Array,
            default: () => [],
        },
    },
    data() {
        return {
            valueInside: [],
        };
    },
    watch: {
        value: {
            handler(val) {
                this.valueInside = val;
            },
            immediate: true,
        },
    },
    methods: {
        handleChange(val) {
            this.$emit("change", val);
        },
    },
};
</script>

<style scoped lang="scss">
::v-deep .el-transfer {
    display: flex;
    flex-direction: row;
    .el-transfer__buttons {
        padding: unset;
    }
}
</style>
