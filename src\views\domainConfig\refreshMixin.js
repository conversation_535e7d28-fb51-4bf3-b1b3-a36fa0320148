// import { cloneDeep } from "lodash-es";

// 10s刷新混入
export default {
    data() {
        return {
            timer: null,
            orderTimer: null,
            refreshInterval: 10 * 1000, // 默认10s
            refreshOrderInterval: 1000, // 默认1s
            workOrderVisible: false,
            isQueryDomainDetail: false,
            is_exist: false,
        };
    },
    beforeDestroy() {
        this.handleClearTimer();
        this.handleClearOrderTimer();
    },
    methods: {
        /**
         * 清除定时器
         * 
         * 本方法用于清除并重置定时器如果定时器不存在，则不执行任何操作
         * 这有助于防止内存泄漏，并确保在组件销毁或不再需要定时器时，定时器能够被正确清除
         */
        handleClearTimer() {
            if (!this.timer) {
                return;
            }

            clearInterval(this.timer);
            this.timer = null;
        },
        // 如果返回：在途工单 或者 弹窗关闭，不需要调用handleRefresh，否则需要调用handleRefresh
        // 该方法作用：没有返回在途工单，10秒后自动关闭弹窗
        // val：为true：代表有：在途工单；false：代表没有在途工单
        handleRefresh(val) {
            this.handleClearTimer();
            if (val || !this.workOrderVisible) {
                return;
            }
            this.timer = setInterval(() => {
                // 10秒后，自动关闭弹窗
                this.workOrderVisible = false;
                this.isQueryDomainDetail = true;
            }, this.refreshInterval);
        },
        /**
         * 清除订单相关的定时器
         * 此方法用于在不需要时清除可能存在的订单定时器，以防止潜在的内存泄漏
         */
        handleClearOrderTimer() {
            if (!this.orderTimer) {
                return;
            }

            clearInterval(this.orderTimer);
            this.orderTimer = null;
        },

        /**
         * 定时调用：查询在途工单接口
         * 此函数用于定期刷新在途工单信息，以保持数据的实时性
         */
        handleOrderRefresh() {
            this.handleClearTimer();
            if (!this.workOrderVisible) {
                return;
            }
            this.orderTimer = setInterval(() => {
                // 每隔1秒，调用一次查在途工单接口
                this.handleExistOrder();
            }, this.refreshOrderInterval);
        },
    },
};
