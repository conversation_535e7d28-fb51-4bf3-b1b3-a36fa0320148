import { nUserModule } from "@/store/modules/nuser";
import { domainLangAutoComple } from "./i18n";

export interface Url {
    fcdnCtcloud?: string,
    fcdnCtyun?: string,
    a1Ctyun?: string,
    a1Ctcloud?: string,
}

export type UrlTransformerType = (url: string) => string;

function getCurrentUrl(url: Url) {
    const isCtcloud = nUserModule.isCtclouds;
    const isFcdn = !window.__POWERED_BY_QIANKUN__;
    const { fcdnCtcloud, fcdnCtyun, a1Ctyun, a1Ctcloud } = url;

    if (isCtcloud) {
        if (isFcdn) return fcdnCtcloud;
        else return a1Ctcloud;
    } else {
        if (isFcdn) return fcdnCtyun;
        else return a1Ctyun;
    }
}

/**
 * 将国际站中文链接转化为国际站英文链接
 * @param url 
 * @returns 
 */
 function ctcloudUrlTransformer(url: string): string {
    // 国际站的链接有两种格式，一种是语言在后面通过查询参数传递，另一种是语言在/document/后面
    if (url.includes('document/zh-cn/')) {
        return url.replace('document/zh-cn/', 'document/');
    } else if (url.includes('lang=zh-cn')) {
        return url.replace('lang=zh-cn', 'lang=en-us');
    }
    return url;
}

type langEnumValues = "zh-cn" | "en-us";

/**
 * 自动转换链接
 * @param url 输入每个站点的***中文链接***，根据当前环境、系统、语言自动切换
 * @returns string 当前环境、语言、系统适用的链接
 */
const urlTransformer = (url: Url): string => {
    const lang: langEnumValues = nUserModule.lang === "en" ? "en-us" : "zh-cn";
    const isCtcloud = nUserModule.isCtclouds;

    const currentUrl = getCurrentUrl(url) || "";

    // 如果当前语言是中文，直接返回当前链接
    if (lang === "zh-cn") {
        return currentUrl;
    }

    if (isCtcloud) {
        return ctcloudUrlTransformer(currentUrl);
    } else {
        // ctyun 目前暂无英文链接，先返回中文链接
        return currentUrl;
    }
}

export const commonLinks = {
    orderLink: urlTransformer({
        fcdnCtcloud: "https://www.esurfingcloud.com/contactus/zh-cn/fromIndex?lang=zh-cn",
        fcdnCtyun: "https://www.ctyun.cn/console/smartservice/ticket/workorder/submit",
        a1Ctyun: "https://www.ctyun.cn/console/smartservice/ticket/workorder/submit",
        a1Ctcloud: "https://www.esurfingcloud.com/contactus/zh-cn/fromIndex?lang=zh-cn"
    })
}

export default urlTransformer;


export function getMenuDomain() {
    const lang = nUserModule.lang;
    if (nUserModule.isCtyun) {
        return domainLangAutoComple("ctiam.fcdn.main", lang);
    }
    if (nUserModule.isCtclouds && lang === "zh") {
        return "ctiam.fcdn.main.ctclouds";
    }
    if (nUserModule.isCtclouds && lang === "en") {
        return "ctiam.fcdn.main.ctclouds.e";
    }

    return domainLangAutoComple("new.fcdn.main", lang);
}

export function getAnnouncementDomain() {
    const lang = nUserModule.lang;
    if (nUserModule.isCtyun && lang === "zh") {
        return "cdn.notice";
    }
    if (nUserModule.isCtyun && lang === "en") {
        return "cdn.notice.ctyun.en";
    }
    if (nUserModule.isCtclouds && lang === "zh") {
        return "cdn.notice.ctclouds";
    }
    if (nUserModule.isCtclouds && lang === "en") {
        return "cdn.notice.en";
    }
    return "cdn.notice";
}
