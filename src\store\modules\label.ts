/*
 * @Description: 域名 label 数据
 * @Author: wang yuegong
 */
import { Module, Action, Mutation, getModule } from "vuex-module-decorators";

import store from "../index";
import { ctFetch } from "@cdnplus/common/utils";
import { LabelUrl } from "@/config/url/label";

import {
    InterfaceLabelItem,
    LabelItem,
    OneLable,
    DomainMapLabel,
    DomainMapLabelJr,
    LabelMapDomain,
} from "../types";
import { BaseLabel } from "./submodules/baseLabel";
import { DomainActionEnum } from "../config";
import { StatisticsModule } from "./statistics";

@Module({ dynamic: true, store, name: "label" })
class Label extends BaseLabel {
    static getLabelMap(nativeList: InterfaceLabelItem[]) {
        const labelList: LabelItem[] = [];
        const _domainMapLabel: {
            [domain: string]: OneLable[];
        } = {}; // 中间状态
        const domainMapLabel: DomainMapLabel = {};
        const domainMapLabelJr: DomainMapLabelJr = {};
        const lableMapDomain: LabelMapDomain = {};
        const subtleLabelMap: Map<string, Map<string, string>> = new Map();
        const labelGroupMap: Map<string, string> = new Map();

        nativeList.forEach(({ name: key, labelId: keyId, children = [] }) => {
            labelGroupMap.set(keyId, key);
            if (!subtleLabelMap.get(keyId)) subtleLabelMap.set(keyId, new Map());

            // 简化版的树结构
            labelList.push({
                label: key,
                value: keyId,
                children: children.map(item => ({
                    label: item.name,
                    value: item.labelId,
                })),
            });
            // 域名已绑定的标签信息映射关系
            children.forEach(({ name: value, labelId: valueId, domainList = [] }) => {
                const currentLabelGroup = subtleLabelMap.get(keyId);
                currentLabelGroup?.set(value, valueId);

                domainList.forEach(domain => {
                    // 待存储的两种数据
                    const data1 = { keyId, key, valueId, value };
                    const data2 = valueId;

                    // 如果已存在则直接追加，如果不存在则先创建
                    if (_domainMapLabel[domain]) {
                        _domainMapLabel[domain].push(data1);
                        domainMapLabelJr[domain].push(data2);
                    } else {
                        _domainMapLabel[domain] = [data1];
                        domainMapLabelJr[domain] = [data2];
                    }
                });

                // 标签已绑定的域名列表映射关系
                lableMapDomain[valueId] = domainList;
            });
        });

        // 二次格式化
        Object.keys(_domainMapLabel).forEach(domain => {
            // 先根据 keyId 生成一组映射关系
            const map: {
                [keyId: string]: LabelItem;
            } = {};

            _domainMapLabel[domain].forEach(({ key, keyId, value, valueId }) => {
                // 按照 keyId 分组
                if (map[keyId]) {
                    map[keyId].children.push({
                        label: value,
                        value: valueId,
                    });
                } else {
                    map[keyId] = {
                        label: key,
                        value: keyId,
                        children: [{ label: value, value: valueId }],
                    };
                }
            });

            // 再把映射关系转换成数组
            domainMapLabel[domain] = Object.values(map);
        });

        return {
            labelList,
            domainMapLabel,
            domainMapLabelJr,
            lableMapDomain,
            subtleLabelMap,
            labelGroupMap,
        };
    }

    @Mutation
    private SET_LABEL_LOADING(loading: boolean) {
        this.loading = loading;
    }

    @Mutation
    SET_LABEL_LIST(nativeList: InterfaceLabelItem[]) {
        this.nativeList = nativeList;
    }

    @Mutation
    FORMAT_LABLE_LIST(nativeList: InterfaceLabelItem[]) {
        const {
            labelGroupMap,
            labelList,
            lableMapDomain,
            subtleLabelMap,
            domainMapLabel,
            domainMapLabelJr,
        } = Label.getLabelMap(nativeList);

        // 增加一条过滤规则，没有 children 的不予展示，没有显示的必要，因为筛选也好、绑定也好，都要基于 children 中的 labelId 来
        this.labelList = labelList.filter(item => item.children.length > 0);
        this.domainMapLabel = domainMapLabel;
        this.domainMapLabelJr = domainMapLabelJr;
        this.lableMapDomain = lableMapDomain;

        this.subtleLabelMap = subtleLabelMap;
        this.labelGroupMap = labelGroupMap;
    }

    @Mutation
    private SET_CTIAM_LABEL_REQUEST_TIME(action: DomainActionEnum) {
        (this as any)[action].requestTime = Date.now();
    }

    @Mutation
    private SET_CTIAM_LABEL_LIST(payload: { action: DomainActionEnum; nativeList: InterfaceLabelItem[] }) {
        (this as any)[payload.action].nativeList = payload.nativeList;
    }

    @Mutation
    private FORMAT_CTIAM_LABEL_LIST({
        action,
        nativeList,
    }: {
        action: DomainActionEnum;
        nativeList: InterfaceLabelItem[];
    }) {
        const {
            labelGroupMap,
            labelList,
            lableMapDomain,
            subtleLabelMap,
            domainMapLabel,
            domainMapLabelJr,
        } = Label.getLabelMap(nativeList);

        // 增加一条过滤规则，没有 children 的不予展示，没有显示的必要，因为筛选也好、绑定也好，都要基于 children 中的 labelId 来
        (this as any)[action].labelList = labelList.filter(item => item.children.length > 0);
        this.domainMapLabel = domainMapLabel;
        this.domainMapLabelJr = domainMapLabelJr;
        this.lableMapDomain = lableMapDomain;

        this.subtleLabelMap = subtleLabelMap;
        this.labelGroupMap = labelGroupMap;
    }

    @Action
    public async GetLabelList(options: { cache: boolean; action: string; preserveData?: boolean }) {
        // 更改缓存规则，当需要缓存（cache = true）且数据不为空时，终止后续请求
        if (options.cache && this.nativeList.length > 0) return;

        // 根据preserveData选项决定是否清空数据
        if (!options.preserveData) {
            this.SET_LABEL_LIST([]);
            this.FORMAT_LABLE_LIST([]);
        }

        try {
            this.SET_LABEL_LOADING(true);

            const rst = await ctFetch<{ list?: InterfaceLabelItem[] }>(LabelUrl.tree, {
                data: {
                    action: options.action,
                },
            });
            this.SET_LABEL_LIST(rst.list || []);
            this.FORMAT_LABLE_LIST(rst.list || []);
        } catch (e) {
            store.$errorHandler(e);
        } finally {
            this.SET_LABEL_LOADING(false);
        }
    }

    @Action
    public async GetCtiamLabelList(action: DomainActionEnum) {
        // 如果当前已经有数据，且在有效期内，则不再请求
        if (
            (this as any)[action].nativeList.length &&
            (this as any)[action].requestTime &&
            Date.now() - (this as any)[action].requestTime < StatisticsModule.cacheTtl * 1000
        ) {
            return;
        }

        // 清空数据
        this.SET_LABEL_LOADING(true);
        this.SET_CTIAM_LABEL_LIST({ action, nativeList: [] });
        this.FORMAT_CTIAM_LABEL_LIST({ action, nativeList: [] });
        try {
            const rst = await ctFetch<{ list?: InterfaceLabelItem[] }>(LabelUrl.tree, {
                data: {
                    action,
                },
            });
            this.SET_CTIAM_LABEL_LIST({ action, nativeList: rst.list || [] });
            this.FORMAT_CTIAM_LABEL_LIST({ action, nativeList: rst.list || [] });
            this.SET_CTIAM_LABEL_REQUEST_TIME(action);
        } catch (e) {
            store.$errorHandler(e);
        } finally {
            this.SET_LABEL_LOADING(false);
        }
    }
}

export const LabelModule = getModule(Label);
