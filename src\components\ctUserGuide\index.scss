.my-picker__popper {
    display: inline-block;
    background: white;
    padding: 10px;
    border-radius: 4px;
    position: relative;
    margin-left: 15px;
    margin-top: -5px;
    transition: all ease-in-out 0.3s;
    &:before {
        content: "";
        width: 0;
        height: 0;
        border-width: 8px;
        border-style: solid;
        border-color: transparent white transparent transparent;
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        left: -16px;
    }

    .header {
        justify-content: space-between;
        padding: 10px 0;

        span {
            font-size: 20px;
            color: $text-color-title;
            font-weight: bolder;
        }

        .el-icon-close {
            font-size: 16px;
            cursor: pointer;
        }
    }

    .guide-main {
        width: 100%;
        color: $text-color-title;
        font-size: 14px;

        .text-main {
            padding: 10px 0 20px 0;
        }
    }

    .footer {
        justify-content: space-between;

        .icon-step {
            width: 5px;
            height: 5px;
            border-radius: 5px;
            background-color: #adafb7;
            margin-right: 8px;
            &.active {
                background-color: $theme-color;
                width: 15px;
            }
        }

        & > .icon-step:last-child {
            margin-right: unset;
        }
    }
}

.popper-overlay {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    overflow: auto;
    margin: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 2000;
}
