<template>
    <div>
        <div class="header-button">
            <el-button type="primary" size="medium" @click="addCache">{{ $t("domain.add2") }}</el-button>
        </div>
        <div class="tips-div">
            <p>{{ $t("domain.detail.tip10") }}</p>
            <p>{{ $t("domain.detail.tip11") }}</p>
            <p>{{ $t("domain.detail.tip12") }}</p>
        </div>
        <div class="list">
            <el-row v-for="(c, index) in cacheList" :key="index" :gutter="15">
                <el-col :xs="12" :sm="8" :lg="8">
                    <span :class="`label${labelSuffix}`" v-html="$t('simpleForm.alogicCache.Type')"></span>
                    <el-select v-model="c.mode" @change="clearCacheName(index, c.mode)">
                        <el-option
                            v-for="opt in cacheModeOptions"
                            :key="opt.value"
                            :label="opt.label"
                            :value="opt.value"
                            :disabled="opt.disabled"
                        />
                    </el-select>
                </el-col>
                <el-col :xs="12" :sm="8" :lg="8">
                    <span
                        :class="`label${labelSuffix}`"
                        v-html="$t('simpleForm.alogicCache.Content')"
                    ></span>
                    <el-input
                        v-model="c.file_type"
                        :disabled="c.mode === 2 || c.mode === 3"
                        :placeholder="c.mode | inputPlaceholder"
                        @focus="showNameSet(c, index)"
                    />
                </el-col>
                <el-col :xs="12" :sm="8" :lg="8">
                    <span :class="`label${labelSuffix}`" style="width: auto">{{
                        $t("domain.detail.label34")
                    }}</span>
                    <span>
                        <el-switch
                            v-model="c.cache_with_args"
                            :active-value="0"
                            :inactive-value="1"
                        ></el-switch>
                    </span>
                </el-col>

                <el-col :xs="12" :sm="8" :lg="8">
                    <span :class="`label${labelSuffix}`">{{ $t("domain.detail.label32") }}</span>
                    <el-select v-model="c.cache_type">
                        <el-option
                            v-for="opt in cacheTypeMapOptions"
                            :key="opt.value"
                            :label="opt.label"
                            :value="opt.value"
                        />
                    </el-select>
                </el-col>
                <el-col :xs="12" :sm="8" :lg="8" v-if="c.cache_type !== 1">
                    <span :class="`label${labelSuffix}`">{{ $t("simpleForm.alogicCache.CacheTime") }}</span>
                    <el-input v-model="c.time" @input="setTll(c)" class="input-with-select">
                        <el-select v-model="c.timeType" slot="append" @change="setTll(c)">
                            <el-option
                                v-for="opt in cacheTtlMapOptions"
                                :key="opt.value"
                                :label="opt.label"
                                :value="opt.value"
                            />
                        </el-select>
                    </el-input>
                </el-col>
                <el-col :xs="11" :sm="7" :lg="7">
                    <span
                        :class="`label${labelSuffix}`"
                        v-html="$t('simpleForm.alogicCache.Priority')"
                    ></span>
                    <el-input v-model="c.priority" placeholder="1~100" />
                </el-col>
                <!-- 将删除按钮和权重输入框放在同一行，避免删除按钮独占一行造成布局混乱 -->
                <el-col :xs="1" :sm="1" :lg="1">
                    <el-button @click="delCache(index)" icon="el-icon-remove-outline" type="text" />
                </el-col>
            </el-row>
        </div>
        <el-dialog
            :title="$t('domain.detail.cacheModeMap.0')"
            :close-on-click-modal="false"
            :modal-append-to-body="false"
            :visible.sync="dialogVisible"
        >
            <div class="extensions" v-for="(val, key) in extensionsOptions" :key="val.title">
                <div class="extension-title">
                    <el-checkbox
                        v-model="extensionAllSelected[key]"
                        @change="checkAllChange(key)"
                        :label="val.title"
                    />
                </div>
                <div class="extension-list">
                    <el-checkbox-group v-model="extensionSelected[key]" @change="checkSingleChange(key)">
                        <el-checkbox v-for="extension in val.list" :label="extension" :key="extension" />
                    </el-checkbox-group>
                </div>
            </div>
            <div class="extensions">
                <div class="extension-title">{{ $t("domain.detail.label40") }}</div>
                <div class="extension-list">
                    <el-input
                        type="textarea"
                        :rows="2"
                        :placeholder="$t('domain.detail.placeholder1')"
                        v-model="extensionOther"
                    />
                </div>
            </div>
            <div slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">{{
                    $t("simpleForm.alogicCache.Cancel")
                }}</el-button>
                <el-button type="primary" @click="setName">{{ $t("simpleForm.alogicCache.OK") }}</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
/* eslint-disable @typescript-eslint/camelcase */
/* eslint-disable @typescript-eslint/no-empty-function */
// import mixin from "../../editor.mixin";
import cacheMixin from "./mixin";
import { extensionsOptions, defaultCacheList1, defaultCacheList2 } from "./config";
import { nUserModule } from '@/store/modules/nuser';

// 生成反向映射表
const extensionsMap = {};
Object.keys(extensionsOptions).forEach(key => {
    extensionsOptions[key].list.forEach(k => {
        extensionsMap[k] = key;
    });
});

export default {
    name: "n-alogic-cache-table",
    mixins: [cacheMixin],
    props: {
        productCode: String,
        filetypeTtl: Array,
        isRecreate: Boolean,
    },
    computed: {
        isEn() {
            return nUserModule.lang === "en";
        },
        labelSuffix() {
            return this.isEn ? '-en' : '';
        }
    },
    data() {
        return {
            activeNameIndex: -1,
            extensionAllSelected: {
                dynamic: false,
                image: false,
                style: false,
                av: false,
                download: false,
                page: false,
            },
            extensionsOptions,
            extensionSelected: {
                dynamic: [],
                image: [],
                style: [],
                av: [],
                download: [],
                page: [],
            },
            extensionOther: "", // 选择框中的补充选项
            followingListMounted: false, // 跟踪内容是否已经初始化完成
        };
    },
    methods: {
        addCache() {
            this.cacheList.push({ ...this.defaultCacheData });
        },
        // 展示可用的二次输入界面
        showNameSet(cache, index) {
            // 如果缓存类型为后缀名，则唤起选择输入界面
            if (cache.mode === 0) {
                // 清掉缓存
                Object.keys(this.extensionAllSelected).forEach(key => {
                    this.extensionAllSelected[key] = false;
                    this.extensionSelected[key] = [];
                });
                // 把当前的数据全部写入
                const other = [];
                cache.file_type
                    .split(",")
                    .filter(val => val)
                    .forEach(c => {
                        if (extensionsMap[c]) {
                            this.extensionSelected[extensionsMap[c]].push(c);
                        } else {
                            other.push(c);
                        }
                    });
                this.extensionOther = other.join(",");
                // 判断是否全选
                Object.keys(this.extensionSelected).forEach(key => {
                    if (new Set(this.extensionSelected[key]).size === extensionsOptions[key].list.length) {
                        this.extensionAllSelected[key] = true;
                    }
                });

                this.dialogVisible = true;
                this.activeNameIndex = index;
            }
        },
        // 全选按钮
        checkAllChange(key) {
            // 不需要额外的操作，点击时，val 已经反转
            const val = this.extensionAllSelected[key];
            this.extensionSelected[key] = val ? extensionsOptions[key].list : [];
        },
        // 检查是否全选
        checkSingleChange(key) {
            const val = this.extensionSelected[key];
            this.extensionAllSelected[key] = val.length === extensionsOptions[key].list.length;
        },
        // 确定选择
        setName() {
            const extentionReg = new RegExp(this.rules.file_type[1].pattern);
            if (!extentionReg.test(this.extensionOther)) {
                this.$message.error(this.$t("domain.detail.tip30"));
                return;
            }
            this.dialogVisible = false;
            const extensions = Object.keys(this.extensionSelected)
                .reduce((rst, key) => {
                    if (this.extensionSelected[key].length > 0) {
                        return rst + "," + this.extensionSelected[key].join(",");
                    }
                    return rst;
                }, "")
                .slice(1);

            // 没有更多，则直接用计算出的结果；如果有更多，则进行拼接
            this.cacheList[this.activeNameIndex].file_type = !this.extensionOther
                ? extensions
                : `${extensions}${extensions ? "," : ""}${this.extensionOther}`;
        },

        delCache(index) {
            this.cacheList.splice(index, 1);
        },
        // 切换类型时，清空输入
        clearCacheName(index, mode) {
            // 首页和全部文件给一个默认值，且设定为不可修改
            this.cacheList[index].file_type = mode === 2 || mode === 3 ? "/" : "";
        },
    },

    watch: {
        productCode: {
            handler(val) {
                if ((this.followingListMounted && this.isRecreate) || !this.isRecreate) {
                    if (val === "006") {
                        this.cacheList = JSON.parse(JSON.stringify(defaultCacheList2));
                    } else {
                        this.cacheList = JSON.parse(JSON.stringify(defaultCacheList1));
                    }
                }
                this.followingListMounted = true;
            },
            deep: true,
        },
        cacheList: {
            handler(val) {
                this.$emit("on-change", val);
            },
            deep: true,
        },
        // 重新发起时，会传入filetypeTtl回填
        filetypeTtl: {
            handler(val) {
                this.init(val);
            },
            deep: true,
        },
    },
};
</script>

<style lang="scss" scoped>
.header-button {
    .tips {
        margin-left: 10px;
    }
}
.tips-div {
    font-size: 12px;
    line-height: 12px;
    margin-top: 8px;
    p {
        margin-top: 4px;
        color: $neutral-7;
    }
}
.list {
    margin-top: 20px;

    .el-row {
        margin-bottom: 8px;
    }

    .el-col {
        display: flex;
        align-items: center;

        .label {
            margin-right: 8px;
            font-size: 12px;
            width: 50px;
        }

        > .el-input,
        > .el-select,
        > .el-switch,
        > .el-button {
            width: auto;
            flex: 1;
        }

        > .el-button {
            height: 36px;
        }
    }
}

.label-en {
    width: 60px;
    margin-right: 8px;
    font-size: 12px;
}

// 弹层
::v-deep .el-dialog {
    width: 60%;
    min-width: 340px;
}

.extension-title {
    // float: left;
    display: inline-block;
    width: 155px;
    vertical-align: center;
    ::v-deep .el-checkbox {
        display: grid;
        grid-template-columns: 8px auto;
        gap: 8px;
        align-items: end;
        margin-top: 10px;
        .el-checkbox__label {
            white-space: pre-wrap;
        }
        .el-checkbox__input {
            align-self: start;
            padding-top: 2px;
        }
    }
}

.extension-list {
    overflow: hidden;
    display: inline-block;
    width: calc(100% - 155px);
    vertical-align: top;
    white-space: initial;

    ::v-deep .el-checkbox {
        width: 70px;
    }
}

.extensions {
    margin-bottom: 5px;
    padding: 5px 0;
    border-bottom: 1px solid #ccc;
    line-height: 36px;
}

.input-with-select {
    ::v-deep {
        .el-input-group__append {
            .el-select {
                // 时分秒
                .el-input {
                    width: 105px;
                }
                // .el-input__inner {
                //     background-color: transparent;
                //     border-color: transparent;
                // }
            }
        }
    }
}
</style>
