<template>
    <div class="order-confirm">
        <el-dialog :title="title" :visible="visible" width="30%" @close="handleClose">
            <el-radio-group v-if="haveUrl" v-model="selected">
                <el-radio :label="cdnUrl">增配CDN加速边缘函数</el-radio>
                <el-radio :label="icdnUrl">增配全站加速边缘函数</el-radio>
            </el-radio-group>
            <p v-else> 如未开通CDN加速、全站加速，请先开通后再增配边缘函数 </p>

            <div slot="footer">
                <el-button @click="handleClose">取消</el-button>
                <el-button type="primary" @click="goTo">确定</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from "vue-property-decorator";
import ctSvgIcon from "@/components/ctSvgIcon/index.vue";

@Component({
    name: "OrderConfirm",
    components: { ctSvgIcon },
})
export default class OrderConfirm extends Vue {
    @Prop({ default: false }) visible!: boolean;
    @Prop({ default: "" }) icdnUrl!: string;
    @Prop({ default: "" }) cdnUrl!: string;

    protected selected = "";

    handleClose() {
        this.$emit("close", false);
    }

    @Watch("visible")
    onVisibleChange(visible: boolean) {
        if (visible) {
            this.selected = this.cdnUrl;
        }
    }

    get haveUrl() {
        return this.cdnUrl && this.icdnUrl;
    }

    get title() {
        return this.haveUrl ? "请选择增配的产品" : "提示";
    }

    protected goTo() {
        if (this.haveUrl) {
            window.open(this.selected);
            this.handleClose();
        } else {
            this.handleClose();
        }
    }
}
</script>
