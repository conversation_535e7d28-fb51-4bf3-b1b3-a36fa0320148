<template>
    <el-dialog
        :visible="visible"
        :title="$t('log.download.tip1')"
        :before-close="handleClose"
        :close-on-press-escape="false"
        :close-on-click-modal="false"
        width="800px"
    >
        <el-table :data="downloadResults" max-height="400">
            <el-table-column prop="url" label="URL"></el-table-column>
            <el-table-column prop="success" :label="$t('billing.common.itm4')" :width="isEn ? 200 : 130">
                <template slot-scope="scope">
                    <el-tag v-if="!scope.row.loading" :type="scope.row.success ? 'success' : 'danger'">
                        {{ scope.row.success ? $t('log.download.tip2') : $t('log.download.tip3') }}
                    </el-tag>
                    <i v-else class="el-icon-loading"></i>
                </template>
            </el-table-column>
        </el-table>
        <span slot="footer" class="dialog-footer">
            <el-button @click="handleClose">{{ $t("common.dialog.close") }}</el-button>
        </span>
    </el-dialog>
</template>

<script lang="ts">
import { getLang } from "@/utils";
import { DownloadResult } from "@/utils/download";
import { Component, Prop, Vue } from "vue-property-decorator";

@Component
export default class BatchDownloadResultDialog extends Vue {
    @Prop({ default: false }) visible!: boolean;
    @Prop({ default: () => [] }) downloadResults!: DownloadResult[];

    handleClose() {
        this.$emit("close");
    }

    get isEn() {
        return getLang() === "en";
    }
}
</script>

<style scoped>
.dialog-footer {
    text-align: right;
}
</style>
