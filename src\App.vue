<script lang="tsx">
import { VNode } from "vue";
import { Component, Vue, Watch } from "vue-property-decorator";
import { Dom } from "./utils";
import CtLeftRight from "@/components/common/layout/skeleton/LeftRight.vue"; // 与cdn一致
import CtTip from "@cdnplus/common/components/mod/CtTip.mod.vue";
import { nUserModule } from "@/store/modules/nuser"; // 已合并
import { MenuModule } from "@/store/modules/menu"; // 已合并
import { LogoutUrl } from "@/config/url/basic";
import { DomainActionEnum, ProductCodeEnum } from "./config/map";
import actions from "@/microApp/actions";
import { SecurityAbilityModule } from "@/store/modules/securityAbility";
import { get } from "lodash-es";
import { AppModule } from "@/store/modules/app";

import { ProductModule } from "@/store/modules/ncdn/nproduct"; // cdn独有
import { StatisticsModule } from "@/store/modules/statistics"; // 已合并
import { LANG_COOKIE_KEY } from "./i18n";
import { getMenuDomain } from "./utils/logic/url";
import { DomainModule } from "./store/modules/domain";
import { IpaConfigModule } from "./store/modules/ipa/config";
import { ctFetch } from "@/utils";
import { basicPackage } from "@/config/url";
import { MicroAppModule } from "./store/modules/microApp";

// 属于全站加速的产品
const wholeList = [ProductCodeEnum.Whole, ProductCodeEnum.Upload, ProductCodeEnum.Socket] as const;

@Component({
    name: "App",
    components: {
        CtTip,
        CtLeftRight,
    },
})
export default class App extends Vue {
    private lastWorkspaceId = "";
    private consoleInstance: any;

    private get workspaceId(): string {
        return this.$route.query.workspaceId as string;
    }

    private get isVip() {
        return nUserModule.isVip;
    }

    private get userLang() {
        return nUserModule.lang;
    }

    private get userInfo() {
        return nUserModule.userInfo;
    }

    private get menuList() {
        return MenuModule.menuList;
    }

    private get pathMap() {
        return MenuModule.pathMap;
    }

    private get isCtclouds() {
        return nUserModule.isCtclouds;
    }
    get isFcdnCtyunCtclouds() {
        return nUserModule.isFcdnCtyunCtclouds;
    }

    get isIpa() {
        return window.location.hash.includes("productType=eas");
    }

    // private get userDomains() {
    //     return UserVersionModule.userDomains;
    // }

    private mounted() {
        this.fixRouterIE();
        // 根据 layout 的设置处理语言
        if (
            window.CtcloudLayout &&
            window.CtcloudLayout.consoleLayout &&
            typeof window.CtcloudLayout.config === "function"
        ) {
            const local = window.CtcloudLayout.config().config.operate.currentLocale;
            local && nUserModule.SET_LANG(local);
        }
        // 非qiankun环境，不进行监听
        if (!window.__POWERED_BY_QIANKUN__) {
            MenuModule.SET_PERM_STRICT(true);

            // 执行 layout 更新 menu 的需要
            this.$ctBus.$on("menulist:update", async () => {
                // 新控制台的菜单配置参数
                await MenuModule.GetMenuList(getMenuDomain());
            });
            return;
        }

        // qiankun环境
        // 注册全局观察者函数
        actions.onGlobalStateChange((state: any) => {
            const currentDomainInfo = state.currentDomainInfo;
            SecurityAbilityModule.SET_SECURITY_DOMAIN_INFO(currentDomainInfo);

            const data = get(state, "breadcrumbData", []) || [];
            AppModule.SET_BREADCRUMB_DATA(data);
        }, true);
    }

    // 查询用户所有产品（包括有域名无产品的情况）
    @Watch("workspaceId", { immediate: true })
    private async getAllProduct(w: string) {
        if (!w || this.$route.meta.withoutAuth) return;
        this.isIpa ? IpaConfigModule.GetIpaBasicConfig() : StatisticsModule.GetBasicConfig();

        // 获取子账号信息
        StatisticsModule.GetChildAccount();

        const isFcdnCtyunCtclouds = nUserModule.isFcdnCtyunCtclouds;
        const isPoweredByQiankun = window.__POWERED_BY_QIANKUN__;

        if (isFcdnCtyunCtclouds) {
            await Promise.all([ProductModule.nGetProductInfo(), MenuModule.GetCtiamAuthList()]);
        } else {
            if (!isPoweredByQiankun) await ProductModule.nGetProductInfo();
            await DomainModule.GetDomainList({ action: DomainActionEnum.Data });
        }

        if (isPoweredByQiankun) return;

        if (!ProductModule.productOptions.some(item => wholeList.includes(item.value as any))) {
            // MenuModule.DEL_FILTER_ARR("statistics.dcdn");
            MenuModule.ADD_FILTER_ARR("statistics.dcdn");
            MenuModule.SET_MENU_LIST();
            MenuModule.SET_PERM_LIST();
        }
    }

    @Watch("$route")
    async onRouteChange() {
        this.isVip && this.updateAlogicLayoutMenu();
        StatisticsModule.SET_PV_UV_CHARTTYPE("PV");
        // 为避免界面白屏阻塞，将原本router.beforeEach中 检查产品、菜单权限是否需要跳转到forbidden页面的判断逻辑转移到app.vue
        // 白屏优化开关开启的情况，走新逻辑
        if (!window.__POWERED_BY_QIANKUN__ && StatisticsModule.loadOptEnable) {
            if (this.$route.name === "interceptor" || this.$route.name === "errorPage") {
                return;
            }
            MenuModule.SET_PERM_STRICT(true);
            AppModule.SET_CONTENT_PRODUCT_LOADING(true);
            AppModule.SET_CONTENT_MENU_LOADING(true);
            const promises = [this.productAuthCheck(), MenuModule.GetMenuList(getMenuDomain())];
            if (this.$route.name === "udfMicroApp*") {
                promises.push(MicroAppModule.checkeeEdgeFunctionStatus());
            }
            await Promise.allSettled(promises);
            AppModule.SET_CONTENT_PRODUCT_LOADING(false);
            AppModule.SET_CONTENT_MENU_LOADING(false);
            this.menuAuthCheck();
        }
    }

    private menuAuthCheck() {
        if (MenuModule.permStrict) {
            // 如果启用严格模式
            // 1、判断 route.meta.perm 是否存在于 permList
            // 如果没有设置 perm 则认为不需要鉴权
            // 不需要鉴权：!perm || MenuModule.permList.some(p => p === perm)
            const perm = this.$route?.meta?.perm;
            if (!(!perm || MenuModule.permList.some(p => p === perm))) {
                // 2、不存在则跳到第一个有权限的路由
                const firstMenu = MenuModule.menuList[0];
                if (firstMenu) {
                    const path =
                        (firstMenu?.hrefLocal || firstMenu?.items?.[0].hrefLocal)?.replace("#", "") || "";
                    this.$router.push(path);
                } else {
                    this.$router.push({ name: "forbidden", query: { type: "auth" } });
                }
            }
        }
    }

    private async productAuthCheck() {
        try {
            const { workspaceId } = this.$route.query;
            const { order } = await ctFetch<{
                order: boolean;
            }>(basicPackage, {
                cache: true,
                data: {
                    language: "zh",
                    workspaceId: workspaceId as string,
                },
            });
            if (this.$route.name !== "forbidden" && !order) {
                this.$router.push({ name: "forbidden", query: { type: "service" } });
            }
        } catch (err) {
            window.console.warn(err);
        }
    }

    // 执行初始化
    private updated() {
        if (window.__POWERED_BY_QIANKUN__) {
            // aocdn
            this.$nextTick(() => {
                this.isVip ? this.initAlogicLayout() : this.initCtcloudLayout();
            });
        } else {
            // cdn
            this.$nextTick(() => {
                this.isVip
                    ? this.initAlogicLayout()
                    : this.isCtclouds
                    ? this.initCtcloudLayout()
                    : this.initCtyunLayout();
            });
        }
    }

    //修复IE下router bug
    private fixRouterIE() {
        const routerShim = () => {
            const currentPath = window.location.hash.slice(1);

            if (this.$route.path !== currentPath) {
                this.$router.push(currentPath);
            }
        };
        //IE 下，放宽判断条件，win7 版本下的 ie10，不识别 -ms-ime-align
        if ("-ms-scroll-limit" in document.documentElement.style) {
            window.addEventListener("hashchange", routerShim, false);
        }
    }

    // 初始化 ctyun layout
    private async initCtyunLayout() {
        if (window.__POWERED_BY_QIANKUN__) {
            return;
        }
        if (!window.CtcloudLayout || !window.CtcloudLayout.consoleLayout) return;
        if (this.consoleInstance) return;

        const langArr = ["en-us", "zh-cn"];
        let locale = (localStorage.getItem(LANG_COOKIE_KEY) || "zh-cn").toLocaleLowerCase();
        if (!langArr.includes(locale)) {
            locale = "zh-cn";
        }
        window.CtcloudLayout.consoleLayout.init({
            showLocaleMenu: true,
            locale,
        });
        // 注册语言切换事件
        window.CtcloudLayout.consoleLayout.updateTopMenuClick(item => {
            if (item.isLocale) {
                const menuCode = item.menuCode;
                if (!langArr.includes(menuCode)) {
                    // 不是语言切换，不刷新
                    return;
                }
                localStorage.setItem(LANG_COOKIE_KEY, menuCode);
                window.location.reload();
            } else {
                window.open(item.href, "_blank");
            }
        });

        this.consoleInstance = true;
        // this.trackUserInfo();
    }

    // 初始化 vip-cdn 的 header
    private initAlogicLayout() {
        if (!window.AlogicLayout) return;

        if (window.__POWERED_BY_QIANKUN__) {
            return;
        }
        const { consoleContainer } = window.AlogicLayout;

        if (!this.consoleInstance) {
            const instance = consoleContainer.init({
                baseNode: Dom.getEle("#cdn-console-container") as HTMLElement,
            });

            this.consoleInstance = instance;
        }
    }
    // 初始化普通 cdn 的 header
    private initCtcloudLayout() {
        console.log("国际站-initCtcloudLayout");
        if (window.__POWERED_BY_QIANKUN__) {
            return;
        }

        if (!window.CtcloudLayout || !window.CtcloudLayout.consoleLayout) return;

        if (!this.consoleInstance) {
            // 获取菜单后，主动修改注销路由
            const promise = this.$ctFetch<{ list: any[] }>("/gw/v1/portal/menu/GetTree", {
                data: {
                    domain: "console.dropdown",
                    locale:
                        typeof window.CtcloudLayout.config === "function"
                            ? window.CtcloudLayout.config().config.operate?.currentLocale || ""
                            : "",
                },
            })
                .then(data => {
                    return data.list
                        .filter(item => item.enable === "true")
                        .map(item => {
                            return item.menuCode === "logout"
                                ? {
                                      ...item,
                                      href: LogoutUrl,
                                      hrefLocal: LogoutUrl,
                                  }
                                : item;
                        });
                })
                .catch(() => []);

            window.CtcloudLayout.getPublicInfo();
            window.CtcloudLayout.consoleLayout.init({
                getDropdownMenuPromise: promise,
            });
            // window.CtcloudLayout.fixedSidebarAd.init();
            this.consoleInstance = true;
        }
        // this.trackUserInfo();
    }

    // 触发 layout 的更新
    private updateAlogicLayoutMenu() {
        if (!window.AlogicLayout) {
            return;
        }

        const { workspaceId, lastWorkspaceId } = this;
        if (lastWorkspaceId !== workspaceId || !workspaceId) {
            // 工作区 id 默认 0
            this.lastWorkspaceId = workspaceId || "0";

            // 非qiankun环境，获取layout菜单
            if (!window.__POWERED_BY_QIANKUN__) {
                const { consoleContainer } = window.AlogicLayout;

                // 更新layout中菜单，顶部菜单，和头像下拉菜单，以及侧边栏菜单
                consoleContainer.updateMenu({
                    // TODO 这里要修改成 osp 菜单管理中配置的头部菜单的菜单代码，注意配置一定是 xxx.header
                    // topic: "xxx",
                    topic: "cdn",
                    workspaceId: this.workspaceId,
                });

                // 侧边栏高亮
                consoleContainer.match({ domain: "cdn" });
            }
        }
    }

    private render(): VNode {
        // aocdn 子应用访问
        if (window.__POWERED_BY_QIANKUN__) {
            return <router-view />;
        }

        this.$ctBus.$emit("outlink:update");

        // cdn 独立访问
        if (this.$route.path === "/") {
            return (
                <div>
                    <router-view />
                </div>
            );
        }

        return this.isVip ? (
            <div id="cdn-console-container">
                <ct-left-right menuList={this.menuList} pathMap={this.pathMap} />
            </div>
        ) : (
            <div id="ctcloud-console">
                <ct-left-right menuList={this.menuList} pathMap={this.pathMap} />
            </div>
        );
    }
}
</script>

<style lang="scss" scoped>
#ctcloud-console-container {
    height: 100%;
}
#cdn-console-container {
    height: 100%;
}
</style>
