<template>
    <el-dialog
        :title="$t('ipManagement.sendList.btn3')"
        :visible.sync="dialogVisible"
        :modal-append-to-body="false"
        :close-on-click-modal="false"
        :before-close="cancel"
        width="600px"
    >
        <el-form label-width="120px">
            <el-form-item :label="$t('ipManagement.sendList.label1')" prop="name">
                <el-input type="textarea" :value="secretKey" rows="5" disabled />
            </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
            <slot name="footer">
                <el-button @click="cancel">
                    {{ $t("ipManagement.planList.btns[7]") }}
                </el-button>
                <el-button type="primary" @click="copy">
                    {{ $t("ipManagement.planList.btns[8]") }}
                </el-button>
            </slot>
        </div>
    </el-dialog>
</template>

<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";
import { write } from "clipboardy";

@Component
export default class OperationFeedbackDialog extends Vue {
    @Prop({ default: true, type: Boolean }) private dialogVisible!: boolean;
    @Prop({ default: "" }) private secretKey!: string;

    /**
     * 复制解析key
     */
    private copy() {
        write(this.secretKey);
        this.$message.success(this.$t("ipManagement.planList.copySuccess") as string);
    }

    /**
     * 关闭当前弹窗
     */
    private cancel() {
        this.$emit("cancel");
    }
}
</script>
