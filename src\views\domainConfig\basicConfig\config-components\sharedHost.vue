<template>
    <!-- 共享缓存，使用可插拔配置方式，无功能锁 -->
    <module-wrapper module-name="shared_host">
        <lock-tip :lock="isLockSharedHost">
            <el-form
                ref="shared_host_form"
                :model="sharedForm"
                :rules="rules"
                label-width="140px"
                class="shared-host-wrapper"
                :disabled="!isEdit || !isService || isLockSharedHost">
                <el-form-item :label="$t('domain.sharedHost.title')" prop="shared_cache_enable">
                    <el-switch
                        v-model="sharedForm.shared_cache_enable"
                        :active-value="'on'"
                        :inactive-value="'off'"
                        @change="handleChange"
                    />
                    <span class="tips">
                        <ct-svg-icon icon-class="info-circle" class-name="ct-sort-drag-icon"></ct-svg-icon>
                        {{ $t("domain.sharedHost.tips") }}
                    </span>
                </el-form-item>

                <div v-if="sharedForm.shared_cache_enable === 'on'" class="switch-wrapper">
                    <el-form-item
                        :label="$t('domain.sharedHost.cacheName')"
                        prop="shared_host"
                        :rules="rules.shared_host"
                    >
                        <el-select
                            v-model="sharedForm.shared_host"
                            :placeholder="$t('domain.sharedHost.selectCacheName')"
                            filterable
                            :loading="allDomainListLoading"
                            @change="handleSharedHostChange"
                            style="width: 360px;"
                        >
                            <el-option v-for="itm in canChooseDomainList" :label="itm.domain" :key="itm.domain" :value="itm.domain"></el-option>
                        </el-select>
                    </el-form-item>
                </div>
            </el-form>
        </lock-tip>
    </module-wrapper>
</template>

<script>
import { registerConfigModule } from '@/registry/configModuleRegistry';
import componentMixin from "@/views/domainConfig/componentMixin";
import ModuleWrapper from '@/views/domainConfig/basicConfig/components/ModuleWrapper.vue';
import lockTip from "@/views/domainConfig/components/lockTIp.vue";
import { isStaticProduct } from '@/utils/product';
import { nDomainUrl } from "@/config/url";
import ctSvgIcon from "@/components/ctSvgIcon";
import { SecurityAbilityModule } from "@/store/modules/securityAbility";

// 注册配置项
registerConfigModule('shared_host', {
    fields: {
        shared_cache_enable: 'off',
        shared_host: null,
    },
    useCustomLock: thisData => {
        return thisData.isLockSharedHost;
    },
    onModuleChange(form, val) {
        return {
            shared_cache_enable: val.shared_cache_enable,
            shared_host: val.shared_host
        };
    },
    toApi(form, { isLocked }) {
        // 如果有功能锁，返回空对象，不做任何修改
        if (isLocked) {
            return {};
        }
        // 正常情况下的处理逻辑
        return {
            shared_host: form.shared_host
        };
    },
    // 添加展示条件配置
    displayCondition(form) {
        // 静态加速开关必须为开启状态
        return form.statics_ability === 'on';
    },
    // 本配置项无须锚点
    // anchor: {
    //     prop: '#div_sharedHost',
    //     label: 'domain.sharedHost.title',
    //     position: {
    //         type: 'after',
    //         target: '#div6_1'  // 插入到div6_1之后
    //     },
    //     // 其他的特殊展示条件(如有)
    //     anchorCondition() {
    //         return !this.isLockSharedHost;
    //     }
    // }
});

export default {
    mixins: [componentMixin],
    name: 'SharedHost',
    components: {
        ModuleWrapper,
        lockTip,
        ctSvgIcon,
    },
    props: {
        formDatas: {
            type: Object,
            required: true
        },
        isLockSharedHost: {
            type: Boolean,
            default: false
        },
        product_code: String,
        domain_type: String,
    },
    data() {
        return {
            allDomainList: [],
            allDomainListLoading: false,
            sharedForm: {
                shared_cache_enable: 'off',
                shared_host: null
            },
            rules: {
                shared_host: [
                    { required: true, message: this.$t('domain.sharedHost.selectCacheName'), trigger: 'change' }
                ]
            },
            isInnerChange: false, // 内部更新标记
        };
    },
    watch: {
        formDatas: {
            deep: true,
            handler(val) {
                // 避免从 prop 更新触发新的 onModuleChange，阻断循环调用
                if (this.isInnerChange) {
                    this.isInnerChange = false;
                    return;
                }
                // 更新内部表单
                this.sharedForm.shared_cache_enable = val.shared_host ? 'on' : 'off';
                this.sharedForm.shared_host = val.shared_host || null;
            },
            immediate: true
        },
        product_code: {
            handler(val) {
                if (!val) {
                    return;
                }
                this.getDomainList();
            },
        },
    },
    computed: {
        /**
         * 获取同产品类型域名
         * 如：
         * 静态的域名只能配静态、下载、点播、CDN加速；
         * 全站、边缘安全与加速只能同产品类型
         */
         canChooseDomainList() {
            const isStatic = isStaticProduct(this.product_code); // 当前域名是否为静态类域名
            const list = this.allDomainList
                .filter(itm => {
                    // 已启用的域名 或者 配置中（更新）
                    return (
                        itm.enable === "true" && (itm.status === 4 || (itm.status === 3 && [5, 6, 7].includes(itm.action)))
                    );
                }).filter(itm => {
                    // 根据当前域名的产品类型过滤
                    if (isStatic) return isStaticProduct(itm.productCode);
                    else {
                        return this.product_code === itm.productCode;
                    }
                })

            this.$emit("onSharedHostCanChooseList", list);
            return list;
        },
    },
    methods: {
        handleChange() {
            this.isInnerChange = true; // 标记这是一个内部更新
            if (this.sharedForm.shared_cache_enable === 'off') {
                this.sharedForm.shared_host = ''; // 共享缓存域名，开关关闭把shared_host置为空字符，传空字符代表删除
            }

            const originalConf = SecurityAbilityModule.securityOriginForm;
            if (this.sharedForm.shared_cache_enable === 'on' && !!originalConf?.shared_host) {
                this.sharedForm.shared_host = originalConf?.shared_host;
            }

            this.$emit('onModuleChange', {
                shared_cache_enable: this.sharedForm.shared_cache_enable,
                shared_host: this.sharedForm.shared_host
            });
        },
        handleSharedHostChange() {
            this.isInnerChange = true; // 标记这是一个内部更新
            this.$emit('onModuleChange', {
                shared_cache_enable: this.sharedForm.shared_cache_enable,
                shared_host: this.sharedForm.shared_host
            });
        },
        async getDomainList() {
            this.allDomainListLoading = true;
            const rst = await this.$ctFetch(nDomainUrl.domainList, {
                data: {
                    pageIndex: 1,
                    pageSize: 1000,
                },
            }).catch(() => {
                this.allDomainListLoading = false;
            });
            this.allDomainList = rst?.list || [];
            this.allDomainListLoading = false;
        },
        init(thisData) {
            return {
                shared_host: thisData.shared_host || "",
                shared_cache_enable: thisData.shared_host ? 'on' : 'off',
            }
        },
    }
};
</script>

<style lang="scss" scoped>
.shared-host-wrapper {
    .tips {
        color: $color-neutral-7;
        font-weight: 400;
        margin-left: $margin-3x;
        .svg-icon {
            font-size: 14px;
            margin-right: 4px;
        }
    }
}
</style>
