.page-wrapper {
    height: 100%;
    overflow: auto;
}

.statistics-entry {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.child-pane::v-deep {
    .title-wrap {
        margin: 0 0 16px 0;
    }

    .operate-btn {
        margin-bottom: 16px;
    }

    // 统计数字
    .total {
        margin: 4px 0;
        width: 100%;
        text-align: left;
        vertical-align: top;
        font-size: 14px;
        color: #333333;

        .tip {
            display: inline-block;
            // color: #606266;
            @include g-width(100%, 50%, 50%, 35%);
        }

        .num {
            margin-left: 4px;
            //font-size: 24px;
        }

        .date {
            // color: #666666;
            // font-size: 12px;
            margin-left: 4px;
        }
    }

    // 下载按钮
    .download {
        float: right;
        margin: 10px 0;
    }

    // 内容区
    .chart {
        clear: both;
        width: 100%;
        height: 280px;
    }

    .load {
        margin-left: 10px;
        cursor: pointer;
    }

    .no-data {
        font-size: 15px;
        color: #666;
        line-height: 280px;
        text-align: center;
    }

    // 图表上方的提示语
    .chart-tip {
        display: inline-block;
        clear: both;

        .title-tip {
            margin-left: 20px;
            flex: 1;
            color: $color-master;
            font-size: 12px;
        }
    }

    .ct-tip.chart-tip {
        border-left-width: 2px;
    }

    .ct-tip {
        border-left: 5px solid #3d73f5;
    }
}

.statistics-search-bar::v-deep {

    // ========== 处理多选输入框折行问题 start ==========
    .el-select__tags>span {
        width: 100%;

        &>span:first-child {
            display: inline-block;
            max-width: calc(100% - 20px);

            span {
                display: inline-block;
                width: calc(100% - 15px);
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                vertical-align: middle;
            }
        }
    }

    .el-select__tags .el-select__input {
        display: none\0;
    }

    // ========== 处理多选输入框折行问题 end ==========
}

.search-bar-fade-enter-active,
.search-bar-fade-leave-active {
    transition: opacity .1s ease;
}

.search-bar-fade-enter,
.search-bar-fade-leave-to {
    opacity: 0;
}

.component-fade-enter-active,
.component-fade-leave-active {
    transition: opacity .3s ease;
}

.component-fade-enter,
.component-fade-leave-to {
    opacity: 0;
}

.statistics-entry-wrapper::v-deep {
    >.el-scrollbar {
        flex: unset;
        max-height: unset;

        &>.el-scrollbar__wrap {
            margin-bottom: unset !important;

            &>.el-scrollbar__view {
                overflow-y: unset;
            }
        }

        &>.el-scrollbar__bar {
            >.el-scrollbar__thumb {
                // cute包用important覆盖了滚动条背景色，所以这里需要使用important覆盖回去
                background-color: rgba(102, 102, 102, .3) !important;
            }
        }
    }

    .ct-section-header {
        overflow: unset;
    }

    .usage-download-icon {
        margin-left: 14px;
        color: #606266;
        font-size: 14px;
        vertical-align: middle;
        cursor: pointer;

        &:hover {
            color: $color-master;
        }
    }
}
