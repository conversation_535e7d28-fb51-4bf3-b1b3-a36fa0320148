/**
 * @Description: 使用 CtPager + CtTable 的 mixin
 *      - 涉及基础的查询、分页功能
 *      - 组件需定义： searchParams 查询参数、 searchUrl / dynamicSearchUrl 查询路由(必要)
 *      - 接口需满足： { data: { list: [], total: 0 }, reason: "" } 标准结构
 * @Author: wang yuegong
 */

import { Component, Vue } from "vue-property-decorator";
@Component
export default class TableAndPagerActionMixin extends Vue {
    protected usePost = false; // 是否使用 post 请求
    protected prefetch = false; // 预请求
    protected loading = false;
    protected getResData = false; // 获取接口返回的数据，true:list false:result
    protected refresh = false; // 使用 ctpager 的刷新为 boolean 控制
    protected pageNum = 1;
    protected pageSize = 10;
    protected total = 0;
    protected dataList: any[] = []; // 接口查询的完整结果
    protected searchUrl = ""; // 静态请求 url ，优先级高于动态请求 url

    // 当请求 url 需要动态生成时，使用该计算属性
    get dynamicSearchUrl() {
        return "";
    }

    // 调用方处理，除 pageIndex 和 pageSize 之外的参数
    get searchParams() {
        return {};
    }

    mounted() {
        if (this.prefetch) this.refresh = true;
    }

    // 返回 true 则继续执行查询，返回 false 则拦截
    beforeSearch() {
        return true;
    }

    // 拉取数据后执行
    // eslint-disable-next-line @typescript-eslint/no-empty-function
    afterSearch() {}

    // 格式化 list 数据，有需要的话就重写；默认是添加 index
    dataListFormat(list: any[]) {
        return list.map((item, idx) => ({
            index: (this.pageNum - 1) * this.pageSize + idx + 1,
            ...item,
        }));
    }

    // 查询
    protected async search({ page = 1, pageSize = 10 }) {
        // 必须有明确返回值，该值作为 total 使用
        if (!this.beforeSearch()) return 0;
        const params = Object.assign({ pageIndex: page, pageSize }, this.searchParams);

        this.loading = true;
        const getParams = { data: params };
        const postParams = {
            method: "POST",
            clearQsWithPost: false,
            body: {
                data: params,
            },
            headers: {
                "Content-Type": "application/json",
            },
        };
        const { total = 0, list = [], result = [] } = await this.$ctFetch(
            this.searchUrl || this.dynamicSearchUrl, // 静态 url 优先级高于动态 url
            this.usePost ? postParams : getParams
        );
        this.total = total;
        // 由于格式化数据需要使用 pageNum ，所以在数据清洗前先缓存
        this.pageNum = page;
        this.pageSize = pageSize;
        this.dataList = this.getResData ? this.dataListFormat(result) : this.dataListFormat(list);

        this.loading = false;
        this.afterSearch && this.afterSearch();
        return this.total;
    }
}
