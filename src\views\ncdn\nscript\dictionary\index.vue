<template>
    <ct-section-wrap
        headerText="全局字典"
        headerTip="全局字典用来定义一块共享内存区域，全局task脚本可以把结果保存到全局字典中，业务脚本可以从全局字典读取数据。"
    >
        <template #headerBtn>
            <el-button @click="getDictData" :disabled="loading">刷新</el-button>
            <el-button v-if="AddDictShow" type="primary" @click="detailDictionary({}, 'add')"
                >添加全局字典</el-button
            >
        </template>
        <ct-box :class="['fix-box', 'table-scroll-wrap']">
            <div v-if="dictTableShow">
                <ct-table
                    :dataList="dictionaryData"
                    :columnConfig="columnConfig"
                    :tableConfig="{ stripe: false }"
                    v-loading="loading"
                >
                    <template slot="dict_num-slot" slot-scope="{ scope }">
                        <span>
                            {{ scope.row.dict_num }}
                        </span>
                    </template>

                    <template slot="status-slot" slot-scope="{ scope }">
                        <span :class="'status-' + scope.row.deploy_status">
                            {{ statusFormmter(scope.row.deploy_status) }}
                        </span>
                    </template>
                    <template slot="operation-slot" slot-scope="{ scope }">
                        <el-button type="text" @click="detailDictionary(scope.row, 'detail')">查看</el-button>
                        <el-button
                            type="text"
                            v-if="showButton('edit', scope.row.deploy_status)"
                            @click="detailDictionary(scope.row, 'edit')"
                            >编辑</el-button
                        >
                        <el-button
                            type="text"
                            v-if="showButton('delete', scope.row.deploy_status)"
                            @click="handleClick('delete', scope.row)"
                            >删除</el-button
                        >
                        <el-button
                            type="text"
                            v-if="showButton('retry', scope.row.deploy_status)"
                            @click="handleClick('retry', scope.row)"
                            >失败重试</el-button
                        >
                    </template>
                </ct-table>
            </div>
        </ct-box>
    </ct-section-wrap>
</template>

<script lang="ts">
import { Component, Mixins, Vue } from "vue-property-decorator";
import { ndictionary } from "@/config/url/ncdn/nscript";
import { nUserModule } from "@/store/modules/nuser";
import { deploymentStatusMap, GetCtiamButtonAction } from "@/config/map";
import { CtiamCode } from "@/utils/ctFetch/errorConfig";
import { checkCtiamButtonAuth } from "@/store/modules/menu";

@Component
export default class Dictionary extends Mixins(Vue) {
    protected searchUrl = ndictionary.getInfos;
    private deploymentStatusMap = deploymentStatusMap;
    private dictionaryData = [] as any;
    private loading = false;
    private AddDictShow = false;
    private dictTableShow = false;
    get columnConfig() {
        return [
            {
                type: "slot",
                name: "dict_num-slot",
                baseConfig: {
                    label: "字典数量",
                    align: "left",
                },
            },
            {
                type: "slot",
                name: "status-slot",
                baseConfig: {
                    label: "部署状态",
                    align: "left",
                },
            },
            {
                prop: "update_time",
                label: "修改时间",
                align: "left",
            },
            {
                type: "slot",
                name: "operation-slot",
                baseConfig: {
                    label: "操作",
                    minWidth: "80",
                    align: "left",
                },
            },
        ];
    }
    get userId() {
        return String(nUserModule.userInfo.userId);
    }
    get searchParams() {
        return {
            $user: this.userId,
            workspaceId: String(this.$route.query.workspaceId),
        };
    }
    created() {
        this.initForm();
    }
    protected async getDictData() {
        const { searchParams, searchUrl } = this;
        this.loading = true;
        const res = await this.$ctFetch<{ result: {} }>(searchUrl, {
            method: "POST",
            body: {
                data: searchParams,
            },
            headers: {
                "Content-Type": "application/json",
            },
        });
        this.loading = false;

        return res;
    }
    private async initForm() {
        const res = await this.getDictData();
        if (!res) return;

        this.dictionaryData = Object.keys(res?.result).length === 0 ? [] : [{ ...res?.result }];
        const isDataLength = this.dictionaryData.length > 0;
        this.AddDictShow = !isDataLength;
        this.dictTableShow = isDataLength;
    }
    private async detailDictionary(row: any, type: string) {
        if (type === "add") {
            await checkCtiamButtonAuth(GetCtiamButtonAction("udfDictAdd"));
        }
        if (type === "detail") {
            await checkCtiamButtonAuth(GetCtiamButtonAction("udfDictView"));
        }
        if (type === "edit") {
            await checkCtiamButtonAuth(GetCtiamButtonAction("udfDictEdit"));
        }

        this.$router.push({
            name: `nscript.dictionary.${type}`,
            query: {
                userId: this.userId,
                operateType: type,
            },
        });
    }
    private async handleClick(type: string, row: any) {
        if (type === "delete") {
            await checkCtiamButtonAuth(GetCtiamButtonAction("udfDictDelete"));
        }
        if (type === "retry") {
            await checkCtiamButtonAuth(GetCtiamButtonAction("udfDictRetry"));
        }

        try {
            const operationType = type === "delete" ? "删除" : "失败重试";
            await this.$confirm(`此操作将对全局字典进行${operationType}操作，是否继续？`, `提示`, {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            });
            this.loading = true;
            const reqUrl = type === "delete" ? ndictionary.delete : ndictionary.retry;
            await this.$ctFetch(reqUrl, {
                method: "POST",
                body: {
                    data: Object.assign({}, this.searchParams, { id: row.id }),
                },
                headers: {
                    "Content-Type": "application/json",
                },
            });
            this.$message.success("操作成功！");
            this.initForm();
        } catch (error) {
            const isCtiamError = CtiamCode.includes((error as any)?.data?.code);
            if (isCtiamError) {
                this.$errorHandler(error);
            } else {
                const errData = error as any;
                if (errData.data && errData.data.reason) {
                    return this.$message.error(errData.data.reason);
                }
            }
        } finally {
            this.loading = false;
        }
    }
    private statusFormmter(status: number) {
        return this.deploymentStatusMap[status as keyof typeof deploymentStatusMap];
    }
    private showButton(type: string, status: number) {
        switch (type) {
            case "edit":
                return [-1, 3, 4, 7].includes(status);
            case "delete":
                return [-1, 3, 4].includes(status);
            case "retry":
                return [-1, 4, 7].includes(status);
            default:
                break;
        }
    }
}
</script>

<style lang="scss" scoped>
// 状态颜色
.status-3 {
    color: $g-color-green;
}
.status--1,
.status-4,
.status-7 {
    color: $g-color-red;
}
.status-0,
.status-1,
.status-2,
.status-8 {
    color: $g-color-gray;
}
</style>
