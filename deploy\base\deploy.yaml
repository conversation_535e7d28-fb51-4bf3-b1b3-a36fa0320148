apiVersion: apps/v1
kind: Deployment
metadata:
  namespace: {{NAMESPACE}}
  name: {{POD_PRE}}-{{POD_POST}}-web
  labels:
    app: {{POD_PRE}}-{{POD_POST}}-web
spec:
  minReadySeconds: 10
  progressDeadlineSeconds: 20
  strategy:
    rollingUpdate:
      maxSurge: 1
  replicas: 1
  revisionHistoryLimit: 1
  selector:
    matchLabels:
      app: {{POD_PRE}}-{{POD_POST}}-web
  template:
    metadata:
      labels:
        app: {{POD_PRE}}-{{POD_POST}}-web
    spec:
      dnsConfig:
        options:
          - name: single-request-reopen
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: {{NODE_LABEL_KEY}}
                    operator: In
                    values:
                      - "{{NODE_LABEL_VAL}}"
      restartPolicy: Always
      hostAliases:
        - ip: "{{IAM_PORTAL_HOST_IP}}"
          hostnames:
            - "{{INGRESS_IAM}}"
      containers:
        - name: {{POD_PRE}}-{{POD_POST}}-web
          image: {{imageUrl}}
          imagePullPolicy: Always
          ports:
            - containerPort: 80
          resources:
            requests:
              memory: "1024Mi"
              cpu: "500m"
            limits:
              memory: "8192Mi"
              cpu: "4000m"
          env:
            - name: KETTY_HOST
              valueFrom:
                fieldRef:
                  fieldPath: spec.nodeName
            - name: KETTY_IP
              valueFrom:
                fieldRef:
                  fieldPath: status.podIP
      imagePullSecrets:
        - name: harboreg
