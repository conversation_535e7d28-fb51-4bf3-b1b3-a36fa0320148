
.table-scroll-wrap {
    height: calc(100% - 54px);
}

.packet-slider {
    width: 80%;
}
.resource-package {
    ::v-deep {
        .el-slider__button {
            background-color: transparent;
            border: none;
        }
        .deep-color .el-slider__runway {
            border-radius: 3px;
            background-color: $color-neutral-5;
        }

        // 过期
        .deep-color .el-slider__runway.disabled .el-slider__bar {
            border-radius: 3px;
            background-color: $color-neutral-5;
        }

        // 使用中
        .theme-color .el-slider__runway.disabled .el-slider__bar {
            border-radius: 3px;
            background-color: $color-master;
        }

        // 其余状态为浅灰色
        .el-slider__runway.disabled .el-slider__bar {
            border-radius: 3px;
            background-color: $color-neutral-1;
        }

        .el-slider__runway {
            margin: 8px 0 8px 0;
        }

        .el-slider__button-wrapper {
            outline: none;
        }
    }
}
