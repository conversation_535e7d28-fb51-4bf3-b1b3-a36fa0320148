<template>
    <div class="normal-form-item" :class="{ 'flex-row': flexType === 'row' }">
        <template v-for="(item, key) in fields">
            <el-form-item
                v-if="isShowItem(item)"
                :key="key"
                :label="item.label"
                :prop="itemProp(item)"
                :required="item.required"
                :rules="rules(item)"
                :label-width="labelWidth || item.labelWidth"
                v-bind="item.config"
                :style="item.style || { width: item.width || width }"
                :class="[
                    { 'direction-row': item.tipDirection === 'row', 'no-margin': item.noMargin },
                    item.customClass,
                ]"
            >
                <!-- 表单项头部自定义重写 -->
                <template v-if="item.labelRender">
                    <div slot="label">
                        <slot :name="item.prop + '_label'" :data="item" />
                    </div>
                </template>
                <!-- 表单项头部含提示语 -->
                <template v-if="item.labelTip">
                    <div slot="label" class="flex-row-style">
                        <el-tooltip
                            v-if="item.labelTipPosition === 'left'"
                            effect="dark"
                            :content="item.labelTip"
                            placement="top"
                        >
                            <i class="el-icon-warning-outline icon-label-tip left" />
                        </el-tooltip>
                        <span>{{ item.label }}</span>
                        <el-tooltip
                            v-if="item.labelTipPosition !== 'left'"
                            effect="dark"
                            :content="item.labelTip"
                            placement="top"
                        >
                            <i class="el-icon-warning-outline icon-label-tip" />
                        </el-tooltip>
                    </div>
                </template>
                <!-- 特殊渲染-->
                <template v-if="item.render">
                    <slot :name="item.prop" :data="item" />
                </template>
                <template v-if="item.type && !item.render">
                    <el-tooltip
                        v-if="contentTip(item)"
                        effect="dark"
                        :content="contentTip(item)"
                        placement="top"
                    >
                        <form-content v-if="item.type && !item.render" :form="form" :data="item" />
                    </el-tooltip>
                    <form-content v-else :form="form" :data="item" />
                </template>
                <!-- tip渲染-->
                <div v-if="item.tip" class="tip-div">
                    <template v-if="item.tipRender">
                        <slot :name="item.prop + '_tip'" :data="item"></slot>
                    </template>
                    <template v-else-if="item.toolTip">
                        <el-tooltip effect="dark" placement="top" :content="item.tip">
                            <i class="el-icon-warning" />
                        </el-tooltip>
                    </template>
                    <template v-else>
                        {{ item.tip }}
                    </template>
                </div>
                <div slot="error" class="el-form-item__error" v-if="item.selfError" slot-scope="scope">
                    <div v-html="scope.error"></div>
                </div>
            </el-form-item>
        </template>
    </div>
</template>

<script>
import { get, set } from "lodash-es";
import { mapGetters } from "vuex";
import formContent from "@/components/formContent";

export default {
    name: "normalFormItem",
    components: {
        formContent,
    },
    props: {
        fields: {
            type: Array,
            required: true,
            default: () => [],
        },
        labelWidth: {
            type: String,
            required: false,
            default: "",
        },
        form: {
            type: Object,
            required: true,
            default: () => {
                return {};
            },
        },
        propFather: {
            type: String,
            required: false,
            default: "",
        },
        flexType: {
            type: String,
            required: false,
            default: "",
        },
        currentIndex: {
            type: [Number, null],
            required: false,
            default: null,
        },
        width: {
            type: String,
            default: "",
        },
    },
    computed: {
        ...mapGetters({
            optionsMap: "optionsMap",
        }),
        /**
         * 表单prop
         */
        itemProp() {
            return item => {
                let str = item.prop;
                if (this.currentIndex !== null) {
                    str = `${this.currentIndex}.${item.prop}`;
                }

                if (this.propFather) {
                    str = `${this.propFather}.${str}`;
                }
                return str;
            };
        },
        /**
         * 是否展示item
         */
        isShowItem() {
            return item => {
                // eslint-disable-next-line no-prototype-builtins
                if (!item.hasOwnProperty("isShow")) {
                    return this.isShowByRelateParams(item);
                }

                // isShow的等级依然是最高的
                if (typeof item.isShow === "function") {
                    return item.isShow(item);
                }

                return item.isShow;
            };
        },
        /**
         * 规则集合
         */
        rules() {
            return item => {
                if (!item.required && !item.rules) {
                    return null;
                }

                let ary = [];
                if (item.required && item.prop) {
                    const inputAry = ["input", "textarea"];
                    const type = inputAry.includes(item.type) ? "请输入" : "请选择";
                    ary.push({ required: true, message: type + item.label });
                }

                if (item.rules && item.rules instanceof Array) {
                    ary = ary.concat(item.rules);
                }

                return ary;
            };
        },
    },
    methods: {
        /**
         * 根据关联字段是否展示表单
         * 因为有些表单项是根据其他表单项的值才渲染出来，为了方便做了进一步封装
         * @param item 表单配置项
         */
        isShowByRelateParams(item) {
            const relateProp = get(item, "relateProp");
            if (!relateProp) {
                return true;
            }

            const relateValue = get(item, "relateValue");
            if (!relateValue) {
                return this.form[relateProp];
            }

            return get(this.form, relateProp) === relateValue;
        },
        /**
         * 是否禁用
         */
        isDisabled(item) {
            // eslint-disable-next-line no-prototype-builtins
            if (item.hasOwnProperty("disabled") && typeof item.disabled === "function") {
                return item.disabled(this.form);
            }

            return item.disabled;
        },
        /**
         * 是否有内容提示
         */
        contentTip(item) {
            if (!item.contentTip) {
                return false;
            }

            if (typeof item.contentTip === "function") {
                return item.contentTip();
            }

            return true;
        },
        get,
    },
};
</script>

<style scoped lang="scss">
.flex-row {
    display: flex;
    flex-direction: row;
    align-items: center;
}

::v-deep .el-form-item {
    display: flex;
    flex-direction: row;

    .el-form-item__label {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: flex-end;
        line-height: 32px;
        height: 32px;
        white-space: nowrap;
        flex-shrink: 0;
    }

    .el-form-item__content {
        margin-left: unset !important;
        flex: 1;

        .el-input {
            width: 100%;
        }
        .el-select {
            width: 100%;
        }
    }
    &.no-margin {
        margin-bottom: unset !important;
    }
}
.flex-row {
    /*::v-deep.el-form-item__label {*/
    /*    margin-left: 10px;*/
    /*}*/
    ::v-deep .el-form-item {
        margin-right: 10px;
    }
}
.direction-row {
    ::v-deep .el-form-item__content {
        display: flex;
        flex-direction: row;
        align-items: center;
    }
}

.icon-label-tip {
    font-size: 16px;
    color: $theme-color;
    margin-left: 5px;
    &.left {
        margin-left: unset;
        margin-right: 5px;
    }
}
</style>
