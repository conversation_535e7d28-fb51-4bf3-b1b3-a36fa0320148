<!-- 排序等级表格 -->
<template>
    <el-table :data="data" v-bind="$attrs" v-on="$listeners">
        <template v-for="(column, columnIndex) in columns">
            <!-- index渲染-->
            <el-table-column
                v-if="column.type === 'index'"
                type="index"
                v-bind="column"
                :key="column.prop || 'index' + columnIndex"
                :align="column.align || 'center'"
            >
                <template slot-scope="{ $index }">
                    <div class="index-box" :class="'index-box' + $index">{{ $index + 1 }}</div>
                </template>
            </el-table-column>
            <!-- 百分比渲染-->
            <el-table-column
                v-else-if="column.type === 'percent'"
                v-bind="column"
                :key="column.prop || 'percent' + columnIndex"
                :align="column.align || 'center'"
                :label="columnLabel(column)"
            >
                <template slot-scope="{ row, $index }">
                    <el-progress :percentage="renderPercent(column, row)" :color="customColor($index)" />
                </template>
            </el-table-column>
            <!-- 特殊渲染-->
            <el-table-column
                v-else-if="column.render"
                v-bind="column"
                :key="column.prop || columnIndex"
                :align="column.align || 'center'"
                :label="columnLabel(column)"
            >
                <template slot-scope="{ row }">
                    <slot v-if="!column.renderFunction" :name="column.prop" :data="row"></slot>
                </template>
            </el-table-column>
            <!-- 普通渲染-->
            <el-table-column
                v-else
                v-bind="column"
                :key="column.prop || columnIndex"
                :align="column.align || 'center'"
                :label="columnLabel(column)"
            >
                <template slot-scope="{ row, $index }">
                    <template v-if="column.formatter">
                        <template v-if="column.type === 'number'">
                            <div class="number-box" :class="'number-box' + $index">
                                {{ column.formatter(row, column) }}
                            </div>
                        </template>
                        <template v-else>
                            {{ column.formatter(row, column) }}
                        </template>
                    </template>
                    <template v-else>
                        <template v-if="column.type === 'rank'">
                            <div class="number-box" :class="'number-box' + $index">
                                {{ row[column.prop] | formatter }}
                            </div>
                        </template>
                        <template v-else>
                            {{ row[column.prop] | formatter }}
                        </template>
                    </template>
                </template>
            </el-table-column>
        </template>
        <!-- 空值重写-->
        <template slot="empty">
            <slot name="empty">
                <ct-empty :description="emptyText">
                    <template slot="description">
                        <slot name="emptyDescription"></slot>
                    </template>
                </ct-empty>
            </slot>
        </template>
    </el-table>
</template>

<script>
import ctEmpty from "@/components/ctEmpty";

export default {
    name: "index",
    components: {
        ctEmpty,
    },
    props: {
        data: {
            type: Array,
            required: true,
            default: () => [],
        },
        // 表格列信息
        columns: {
            type: Array,
            required: false,
            default: () => [],
        },
        emptyText: {
            type: String,
            default: "暂无数据",
        },
    },
    // 默认空时样式为-
    filters: {
        formatter(value) {
            if (value || value === 0) {
                return value;
            } else {
                return "-";
            }
        },
    },
    computed: {
        customColor() {
            return index => {
                const map = {
                    0: "#ff4040",
                    1: "#fe863f",
                    2: "#f2c351",
                };
                return map[index] || "#38a8f6";
            };
        },
    },
    methods: {
        /**
         * 列表label
         */
        columnLabel(item) {
            if (!item.label) {
                return "";
            }

            if (typeof item.label === "function") {
                return item.label(item);
            }

            return item.label;
        },
        /**
         * 渲染百分比
         */
        renderPercent(column, row) {
            if (column.formatter && typeof column.formatter === "function") {
                return column.formatter(row);
            }

            return row[column.prop];
        },
    },
};
</script>

<style scoped lang="scss">
@import "./index.scss";
</style>
