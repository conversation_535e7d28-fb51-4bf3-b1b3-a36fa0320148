<template>
    <el-form
        :model="form"
        :label-width="labelWidth"
        class="form-wrapper"
        label-position="right"
        v-bind="$attrs"
        v-on="$listeners"
    >
        <div
            v-for="(rowItem, rowKey) in formFields"
            :key="rowKey"
            class="wrapper-form-block"
            :class="{ 'wrapper-form-margin': !margin }"
        >
            <el-form-item
                v-for="(item, index) in rowItem"
                :key="index"
                :label="item.label"
                :style="item.style"
            >
                <template v-if="item.type === 'operation'" slot="label">
                    <span></span>
                </template>
                <div :style="item.contentStyle">
                    <!-- 普通输入框-->
                    <el-input
                        v-if="item.type === 'input'"
                        v-model.trim="form[item.prop]"
                        :placeholder="item.placeholder ? item.placeholder : '请输入'"
                        clearable
                        v-bind="item.config"
                        :style="{ width: item.width }"
                        @change="handleCommonChange($event, item)"
                    />
                    <!-- 普通选择框-->
                    <el-select
                        v-if="item.type === 'select'"
                        v-model="form[item.prop]"
                        placeholder="请选择"
                        :clearable="item.clearable !== false"
                        :style="{ width: item.width }"
                        v-bind="item.config"
                        @change="handleCommonChange($event, item)"
                    >
                        <el-option
                            v-for="optionItem in listAry(item)"
                            :key="optionItem.value"
                            :label="optionItem.label"
                            :value="optionItem.value"
                        />
                    </el-select>
                    <!-- 普通时间框-->
                    <el-date-picker
                        v-if="item.type === 'time'"
                        v-model="form[item.prop]"
                        placeholder="选择日期"
                        :type="item.timeType || 'date'"
                        :style="{ width: item.width }"
                        v-bind="item.config"
                    />
                    <!-- 自定义时间框-->
                    <time-picker
                        v-if="item.type === 'customTime'"
                        ref="customTime"
                        v-model="form[item.prop]"
                        v-bind="item.config"
                    />
                    <!-- 单选框-->
                    <el-radio-group v-if="item.type === 'radio'" v-model="form[item.prop]">
                        <el-radio-button
                            v-for="optionItem in listAry(item)"
                            :key="optionItem.value"
                            :label="optionItem.value"
                            >{{ optionItem.label }}</el-radio-button
                        >
                    </el-radio-group>
                    <!-- ct选择框-->
                    <ct-select
                        v-if="item.type === 'ctSelect'"
                        v-model="form[item.prop]"
                        :data="listAry(item)"
                        v-bind="item.config"
                        :style="{ width: item.width }"
                        @change="handleCommonChange($event, item)"
                    />
                    <!-- 自定义渲染-->
                    <div v-if="item.render && !item.type">
                        <slot :name="item.prop" :data="item" />
                    </div>
                    <!-- 提示语-->
                    <div v-if="item.tip" class="tip-div">{{ item.tip }}</div>
                    <!-- 按钮区域-->
                    <div
                        v-if="item.type === 'operation'"
                        class="flex-row-style btn-div"
                        :style="{ width: item.width }"
                    >
                        <el-button
                            v-for="(buttonItem, buttonIndex) in item.list"
                            :key="buttonIndex"
                            :loading="buttonItem.loading"
                            :type="buttonItem.type || 'primary'"
                            v-bind="buttonItem.config"
                            @click="handleBtnClick(buttonItem)"
                        >
                            {{ buttonItem.label }}
                        </el-button>
                    </div>
                </div>
            </el-form-item>
        </div>
    </el-form>
</template>

<script>
import timePicker from "@/components/timePicker";
import ctSelect from "@/components/ctSelect";
import { mapGetters } from "vuex";
import { get } from "lodash-es";

export default {
    components: {
        timePicker,
        ctSelect,
    },
    props: {
        // 传入的表单字段
        fields: {
            type: Array,
            required: true,
            default: () => [],
        },
        // 分块大小
        chunkNumber: {
            type: Number,
            required: false,
            default: 3,
        },
        form: {
            type: Object,
            required: true,
            default: () => {
                return {};
            },
        },
        margin: {
            type: Boolean,
            required: false,
            default: true,
        },
        labelWidth: {
            type: String,
            default: "80px",
        },
    },
    computed: {
        ...mapGetters({
            optionsMap: "optionsMap",
        }),
        /**
         * 列表数据展示
         */
        listAry() {
            return item => {
                if (item.list) {
                    return item.list;
                }

                if (item.listFilter && typeof item.listFilter === "function") {
                    return item.listFilter(item);
                }

                if (item.listProp) {
                    return get(this.optionsMap, item.listProp, []);
                }

                const ary = get(this.optionsMap, item.prop, []);
                return ary;
            };
        },
        /**
         * 根据item中的row控制样式
         */
        formFields() {
            const map = {};
            this.fields.forEach(item => {
                if (!map[item.row]) {
                    map[item.row] = [];
                }
                map[item.row].push(item);
            });
            return map;
        },
    },
    methods: {
        /**
         * 提交表单
         */
        onSubmit() {
            this.$emit("search", true);
        },
        /**
         * 按钮点击
         */
        handleBtnClick(item) {
            if (item.fn && typeof item.fn === "function") {
                item.fn(item);
            }
        },
        handleCommonChange(val, item) {
            if (item.event && typeof item.event === "function") {
                item.event(val);
            }
        },
        getTime() {
            const item = get(this.$refs.customTime, [0]);
            if (item) {
                item.outsideChangeTime();
            }
        },
        setDefaultTime(val) {
            const item = get(this.$refs.customTime, [0]);
            if (item) {
                item.setDefaultValue(val);
                item.outsideChangeTime();
            }
        },
    },
};
</script>

<style scoped lang="scss">
@import "./index.scss";
</style>
