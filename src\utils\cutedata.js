// import { span } from "@cutedesign/aiops-web";

// /**
//  * 手动埋点方法
//  *
//  * @param {any} e - 事件标识
//  */
// export async function triggerSpan(e) {
//     span(e);
// }

// // 手动埋点方法(完整版)
// export async function triggerSpanObject(e, spanAttributes) {
//     // 创建一个埋点配置对象
//     const spanOptions = {
//         spanName: e,
//         spanAttributes: {
//             ...spanAttributes,
//         },
//     };
//     span(spanOptions);
// }
