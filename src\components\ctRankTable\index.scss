::v-deep.el-table th {
    padding: 10px 0;
    background-color: #FAFAFA;
}

::v-deep .el-table__body{
    td {
        height: 50px;
    }
}

::v-deep .el-table th > .cell {
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.85);
    line-height: 22px;
}


$color-set: (
    "0": (
        background: #ffefef,
        textColor: #ff0000,
    ),
    "1": (
        background: #ffeadb,
        textColor: #e28000,
    ),
    "2": (
        background: #fff1cb,
        textColor: #e9a400,
    ),
);

.index-box {
    width: 26px;
    height: 26px;
    border-radius: 6px 6px 6px 6px;
    line-height: 26px;
    text-align: center;
    margin-right: 13px;
    background: #f3f3f3;
    color: #999999;
    @each $key, $val in $color-set {
        &.index-box#{$key} {
            background: map-get($val, background);
            color: map-get($val, textColor);
        }
    }
}

.number-box {

}

::v-deep .el-progress {
    white-space: nowrap;
}
