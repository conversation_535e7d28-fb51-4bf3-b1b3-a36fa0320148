<template>
    <el-table :data="fetchData" v-loading="loading" :element-loading-text="$t('statistics.common.table.loading')"
        :empty-text="$t('common.table.empty')">
        <el-table-column :label="$t('statistics.rank.domainRank.tableColumn1')" prop="rank" width="80" />
        <el-table-column :label="$t('statistics.rank.domainRank.tableColumn2')" prop="channel" min-width="120" />
        <el-table-column :label="$t('statistics.rank.domainRank.tableColumn3')" prop="flow" width="120" />
        <el-table-column :label="$t('statistics.rank.domainRank.tableColumn4')" prop="flowPer" width="180" />
        <el-table-column :label="$t('statistics.rank.domainRank.tableColumn5')" prop="topBandwidth" width="180" />
        <el-table-column :label="$t('statistics.rank.domainRank.tableColumn6')" prop="topBandwidthTime" width="180"
            :formatter="formatterTime" />
        <el-table-column :label="$t('statistics.rank.domainRank.tableColumn7') + getTimesUnit()" prop="request" width="180" />
    </el-table>
</template>

<script lang="ts">
import { Component } from "vue-property-decorator";
import { mixins } from "vue-class-component";
import { StatisticsRankUrl } from "@/config/url/statistics";
import { SearchParams, ListTopDomainFetchData, ListTopDomainItem } from "@/types/statistics/rank";
import { timeFormat } from "@/filters";
import ChartMixin from "../rankMixin";
import { ProductModule } from "@/store/modules/ncdn/nproduct";
import { ProductCodeEnum } from "@/config/map";
import { convertBandwidthB2P, convertFlowB2P } from "@/utils";

@Component({
    name: "DomainRank",
})
export default class DomainRank extends mixins(ChartMixin) {
    fetchData: ListTopDomainItem[] = [];
    downloadDataList: ListTopDomainItem[] = [];
    private totalRequest = "";
    private totalFlow = "";

    formatterTime(row: any, column: any, cellValue: string) {
        return timeFormat(cellValue);
    }

    get productOptions() {
        const list = ProductModule.allProductOptions.filter(opt => {
            //隐藏全站加速的二级菜单，全站加速
            return opt.value !== ProductCodeEnum.Upload && opt.value !== ProductCodeEnum.Socket;
        });
        return list;
    }
    // 接口获取数据
    protected async getData(params: SearchParams) {
        // delete params.domainList;
        if (!(params.product && params.product.length > 0)) {
            params.product = this.productOptions.map(item => {
                return item.value;
            });
        }

        const rst = await this.fetchGenerator<ListTopDomainFetchData>(StatisticsRankUrl.topDomainList, {
            ...params,
        }) || {
            list: [],
            totalRequest: 0,
            totalFlow: 0,
        };
        rst.list = rst.list || [];

        this.downloadDataList = rst.list
            .map(item => ({
                ...item,
                flow: `${((+item.flow) / Math.pow(this.scale, 2)).toFixed(2)}${this.MB}`,
                topBandwidth: `${((+item.topBandwidth) / Math.pow(this.scale, 2)).toFixed(2)}${this.Mbps}`,
            }))
            .sort((a, b) => +a.rank - +b.rank);
        this.fetchData = rst.list
            .map(item => ({
                ...item,
                flow: convertFlowB2P(item.flow, this.scale).result,
                topBandwidth: convertBandwidthB2P(item.topBandwidth, this.scale).result,
            }))
            .sort((a, b) => +a.rank - +b.rank);

        this.totalRequest = rst.totalRequest;
        this.totalFlow = rst.totalFlow;
    }

    // 重写 excel 数据拼接方法
    protected tableToExcel() {
        let str = "";

        str = `${this.$t("statistics.rank.domainRank.tableToExcel.excelColumn1", { flowUnit: this.MB, bandUnit: this.Mbps })}\n`;

        this.downloadDataList.forEach(item => {
            const topBandwidthTime = timeFormat(item.topBandwidthTime);
            str += `${item.channel},${item.flow},${item.flowPer},${item.topBandwidth},${topBandwidthTime},${item.request}\n`;
        });

        //增加峰值带宽和95峰值带宽
        str += `\n${this.$t("statistics.rank.common.tableToExcel.excelColumn2")}\n${this.$t(
            "statistics.rank.common.tableToExcel.excelColumn3",
            {
                num: this.totalRequest + this.$t("statistics.dcdn.backToOriginStatusCode.totalTipUnit"),
            }
        )}\n${this.$t("statistics.rank.common.tableToExcel.excelColumn4", {
            flow: `${(+this.totalFlow / Math.pow(this.scale, 2)).toFixed(2)}${this.MB}`,
        })}\n`;

        //表格CSV格式和内容
        this.downloadExcel({
            name: `${this.$t("statistics.rank.domainRank.tableToExcel.excelName")}`,
            str,
        });
    }
}
</script>

<style lang="scss" scoped></style>
