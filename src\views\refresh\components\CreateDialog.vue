<template>
    <div>
        <el-dialog
            :title="$t('refresh.create.title')"
            :close-on-click-modal="false"
            :visible.sync="addVisible"
            :before-close="cancel"
            class="add-dialog"
        >
            <el-form :model="addTask" ref="addForm" class="refresh-form" :label-width="isEn ? '120px' : '80px'" label-position="left">
                <el-form-item :label="$t('refresh.create.operationLabel')" prop="optype" required>
                    <el-select v-model="addTask.optype" class="refresh-form--selector">
                        <el-option value="1" :label="$t('refresh.common.tab[0]')" />
                        <el-option value="2" :label="$t('refresh.common.tab[1]')" />
                        <el-option value="4" :label="$t('refresh.common.tab[2]')" />
                        <el-option value="3" :label="$t('refresh.common.tab[3]')" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <div class="form-info">
                        <i class="el-alert__icon el-icon-warning-outline"></i>
                        <div>
                            <div class="tips-warn" v-for="tip in $t(tips[addTask.optype], { size: valuesSize}).split('\n')" :key="tip">
                                {{ tip }}
                            </div>
                        </div>
                    </div>
                </el-form-item>
                <el-form-item>
                    <div class="area-container">
                        <el-input
                            type="textarea"
                            :spellcheck="false"
                            :placeholder="placeholders[addTask.optype]"
                            v-model="addTask.content"
                            :autosize="{ minRows: 10, maxRows: 10 }"
                        ></el-input>
                        <span class="count-span">{{ values.length + "/" + valuesSize }}</span>
                    </div>
                </el-form-item>
            </el-form>
            <div v-loading="loading" slot="footer">
                <el-button class="dialog-btn" @click="cancel">{{ $t("refresh.create.cancel") }} </el-button>
                <el-button type="primary" class="dialog-btn" @click="submit">{{ $t("refresh.create.confirm") }}</el-button>
            </div>
        </el-dialog>
        <operation-feedback-dialog
            v-if="showFeedback"
            :domain-list="disabledList"
            :has-continue="hasContinue"
            @cancel="showFeedback = false"
            @continue="operateContinue"
        />
    </div>
</template>

<script lang="ts">
import { Component, Prop, Vue, Watch } from "vue-property-decorator";
import pattern from "@/config/pattern";
import { refreshUrl } from "@/config/url";
import { DomainModule } from "@/store/modules/domain";
import { DomainActionEnum, getCtiamAction } from "@/config/map";
import { nUserModule } from "@/store/modules/nuser";
import OperationFeedbackDialog from "./OperationFeedbackDialog.vue";
type taskParam = { optype: string; content: string };

@Component({
    name: "refreshCreate",
    components: {
        OperationFeedbackDialog,
    }
})
export default class UpdateDialog extends Vue {
    private pattern = pattern;
    private tips = {
        1: "refresh.create.tip[0]",
        2: "refresh.create.tip[1]",
        3: "refresh.create.tip[2]",
        4: "refresh.create.tip[3]",
    };
    private loading = false;
    private showFeedback = false;
    private disabledList: Array<any> = [];
    private enabledList: Array<any> = [];
    private hasContinue = false;
    // 弹框显示标志位
    @Prop({ default: false, type: Boolean }) private addVisible!: boolean;
    // 加载状态标志位
    @Prop({ default: false, type: Boolean }) private dialogLoading!: boolean;
    @Prop({ default: { optype: "", content: "" } }) private addTask!: taskParam;
    // onst lowerObj: Record<string, unknown> = {};
    // @Prop({
    //     default: () => {
    //         return [];
    //     },
    //     type: Array,
    // })

    @Watch("dialogLoading")
    onDialogLoadingChange(val: boolean) {
        this.loading = val;
    }

    get isEn() {
        return nUserModule.lang === "en";
    }

    get placeholders() {
        const url = nUserModule.isCtclouds ? "www.esurfingcloud.com" : "www.ctyun.com.cn";
        return {
            1: this.$t("refresh.create.placeholder[0]", { url,  }),
            2: this.$t("refresh.create.placeholder[1]", { url }),
            3: this.$t("refresh.create.placeholder[2]", { url }),
            4: this.$t("refresh.create.placeholder[3]", { url }),
        };
    }

    get values() {
        // 处理换行、所有空格、过滤空数据
        return this.addTask.content
            .split("\n")
            .map((val: string) => val.replace(/\s*/g, ""))
            .filter((val: any) => val);
    }
    get domainList() {
        return DomainModule[DomainActionEnum.Refresh].list;
    }
    get wildDomainList() {
        // 以*开头的泛域名，移除第一位
        return this.domainList.filter(d => d.startsWith("*")).map(d => d.slice(1));
    }
    get rules() {
        const { url, catalogue, prefetch, regUrl } = this.pattern;
        return [url, catalogue, prefetch, regUrl][+this.addTask.optype - 1];
    }
    get remainLength() {
        return this.values.length < this.valuesSize ? this.valuesSize - this.values.length : 0;
    }

    get valuesSize() {
        if (this.addTask.optype === "4") {
            // 正则刷新，底层限制10个
            return 10;
        }
        return this.addTask.optype === "1" ? 1000 : 50;
    }

    get isFcdnCtyunCtclouds() {
        return nUserModule.isFcdnCtyunCtclouds;
    }

    patternCheck() {
        // 保证每一个URL的格式正确
        return this.values.every((val: string) => {
            const p = new RegExp(this.rules.pattern);
            return p.test(val);
        });
    }
    private async submit() {
        if (this.values.length === 0) return this.$message.error(this.$t("refresh.create.msg1") as string);

        if (this.values.length > this.valuesSize) return this.$message.error(this.$t("refresh.create.msg2", { size: this.valuesSize}) as string);

        if (!this.patternCheck()) {
            const msg = `${this.$t(this.rules.message)}` || "";
            this.$message.error(msg);
            return
        }
        this.loading = true;
        let action = "";
        if (window.__POWERED_BY_QIANKUN__) {
            action = "accessone_domain";
        } else {
            if (this.isFcdnCtyunCtclouds) {
                action = getCtiamAction("RefreshPreloadLabel");
            } else {
                action = DomainActionEnum.Refresh;
            }
        }
        const rst: { [key: string]: string }[] = await this.$ctFetch(refreshUrl.checkDomainPrivilege, {
            method: "POST",
            body: {
                data: {
                    urls: this.values,
                    action: action,
                },
            },
        });
        this.loading = false;
        this.disabledList = [];
        this.enabledList = [];
        this.hasContinue = false;
        // 如果鉴权不通过，则报错提示
        rst.forEach(item => {
            if (item.enable === "true") {
                this.enabledList.push(item)
            } else if (item.enable === "false") {
                this.disabledList.push(item)
            }
        })
        if (this.enabledList.length) {
            this.hasContinue = true;
        }
        if (this.disabledList.length) {
            this.showFeedback = true;
        } else {
            this.$emit("submit", this.enabledList)
        }
        return;
    }
    private operateContinue() {
        this.$emit("submit", this.enabledList)
    }
    private cancel() {
        this.$emit("cancel");
    }
}
</script>

<style lang="scss" scoped>
.el-form-item {
    margin-bottom: 0;
}
.form-info {
    display: flex;
    .el-alert__icon {
        color: #ff842e;
        margin-right: 8px;
    }
    font-size: 12px;
    color: #333333;
    line-height: 18px;
}
.add-dialog {
    .form-info {
        margin: 10px 0;
        display: flex;
        .el-alert__icon {
            color: #7C818C;
            margin-right: 8px;
        }
        font-size: 12px;
        color: #7C818C;
        line-height: 18px;
    }
    .tips {
        display: inline-block;
        font-size: 12px;
        color: #7C818C;
    }
    .tips + .tips {
        margin-left: 20px;
    }

    .dialog-icon {
        margin-left: 140px;
        color: #333;
        font-size: 18px;
    }

    .alert-wrapper {
        margin-bottom: 16px;
    }

    .dialog-btn {
        width: 100px;
    }

    ::v-deep .el-dialog {
        @include g-width(90%, 60%, 45%);
    }

    .refresh-form {
        .refresh-form--selector {
            width: 100%;
            max-width: none;
        }
        .seperat-label {
            margin-bottom: 0;
        }
        .area-container {
            position: relative;
            margin-bottom: 20px;
            .count-span {
                position: absolute;
                bottom: 0;
                right: 20px;
                color: #999;
            }
        }
        ::v-deep {
            .el-textarea {
                width: 100%;
                & > textarea {
                   padding-right: 60px;
                }
            }
            .el-form-item__label {
                display: inline-block;
                @include g-width(100%, 160px, 160px);
                @include g-media-attr(
                    (
                        attr: text-align,
                        md: right,
                        sm: right,
                        xs: left,
                    )
                );
            }

            .el-form-item__content {
                @include g-width(100%, auto, auto);
                @include g-media-attr(
                    (
                        attr: margin-left,
                        md: 160px,
                        sm: 160px,
                        xs: 0,
                    )
                );
            }
        }
    }
}
</style>
