<template>
    <section class="domain-batchEdit">
        <ct-breadcrumb />
        <ct-section-header class="batch-edit-header" :title="$t('domain.batch.title')">
            <template #button>
                <el-button
                    @click="() => (active -= 1)"
                    :disabled="active === ACTIVE_ENUM.DOMAIN_LIST || batchLoading"
                    >{{ $t("domain.back") }}</el-button
                >
                <el-button @click="stepNext" type="primary" :disabled="batchLoading">{{
                    active === ACTIVE_ENUM.DOMAIN_LIST
                        ? $t("domain.batch.next")
                        : $t("common.提交")
                }}</el-button>
            </template>
        </ct-section-header>
        <div class="batch-edit-step">
            <div class="step-container">
                <el-steps :active="active" finish-status="success" align-center>
                    <el-step :title="$t('domain.batch.title1')"></el-step>
                    <el-step :title="$t('domain.batch.title2')"></el-step>
                    <!-- <el-step :title="$t('domain.batch.title3')"></el-step> -->
                </el-steps>
            </div>
        </div>

        <div :class="{ showDomain: ifShowDomain, notShowDoamin: !ifShowDomain }">
            <el-table
                v-if="active === ACTIVE_ENUM.DOMAIN_LIST"
                :empty-text="$t('common.table.empty')"
                :data="domainList"
                style="overflow: auto"
            >
                <el-table-column type="index" :label="$t('domain.list.tableLabel1')" width="80" />
                <el-table-column prop="domain" min-width="160" :label="$t('domain.list.tableLabel2')" />
                <el-table-column prop="cname" min-width="160" label="CNAME" />
                <el-table-column
                    prop="productName"
                    min-width="120"
                    :formatter="productFormatter"
                    :label="$t('domain.list.tableLabel3')"
                />
                <el-table-column prop="areaScope" min-width="130" :label="$t('domain.list.tableLabel4')" />
                <el-table-column prop="status" min-width="90" :label="$t('domain.list.tableLabel5')" />
                <el-table-column prop="insertDate" min-width="160" :label="$t('domain.list.tableLabel6')" />
            </el-table>
        </div>

        <form-wrapper
            :domain-list="domainList.map(itm => itm.domain)"
            ref="formRef"
            v-show="active === ACTIVE_ENUM.FORM"
            v-loading="batchLoading"
        />

        <!-- <div v-if="active === 2" class="step3-div" v-loading="batchLoading"></div> -->

        <batch-edit-validation-modal
            v-model="showValidationModal"
            :validation-error="validateArrFromBe"
            :title="validationTitle"
        />

        <failed-domain-dialog
            :visible="showFailedDomainDialog"
            :msg-list="failedDomainList"
        />
    </section>
</template>

<script lang="ts">
import { Component, Vue, Ref } from "vue-property-decorator";
import FormWrapper from "./FormWrapper.vue";
import BatchEditValidationModal from "./components/validationModal.vue";
import FailedDomainDialog from "./components/failedDomainDialog.vue";
import { ProductCodeMap } from "@/store/config/product";
import { DomainStatusMap } from "@/config/map";
import { timeFormat } from "@cdnplus/common/filters/index";
import { getI18nLabel } from "@/store/modules/ncdn/nproduct";
import { N_URL_FORM_OPERATION_BATCHSUBMIT as BATCH_SUBMIT_URL } from "@/config/url";

interface DomainList {
    domain: string;
    cname: string;
    productName: string;
    areaScope: string;
    status: string;
    subProductCode: string;
    productCode: string;
    insertDate: string;
}

@Component({
    components: {
        FormWrapper,
        BatchEditValidationModal,
        FailedDomainDialog,
    },
})
export default class BatchEditNew extends Vue {
    ACTIVE_ENUM = {
        DOMAIN_LIST: 0,
        FORM: 1,
    };
    areaScopeMap = new Map([
        [1, this.$t("domain.areaScope[0]") as string],
        [2, this.$t("domain.areaScope[1]") as string],
        [3, this.$t("domain.areaScope[2]") as string],
    ]);
    batchLoading = false;
    active = this.ACTIVE_ENUM.DOMAIN_LIST;
    domainList: DomainList[] = []; // 批量修改的域名列表
    showValidationModal = false;
    validationTitle = "";
    validateArrFromBe: { name: string; pass: string; note: string }[] = [];
    showFailedDomainDialog = false;
    failedDomainList: string[] = [];
    @Ref("formRef") formRef!: FormWrapper;

    get ifShowDomain() {
        return this.active === this.ACTIVE_ENUM.DOMAIN_LIST;
    }

    mounted() {
        this.domainList = JSON.parse(window.sessionStorage.getItem("batchEdit") || "[]");
        this.transformDomainList(this.domainList);
    }
    productFormatter(row: any) {
        //如果为二级产品则返回二级产品名称
        return getI18nLabel(row.subProductCode || row.productCode);
    }
    // 步骤条下一步
    async stepNext() {
        if (this.active === this.ACTIVE_ENUM.FORM) {
            const { atLeastOneChecked, allValid, message } = await this.formRef.handleValidate();
            if (!atLeastOneChecked) {
                this.$message.error(`${this.$t("domain.batch.请至少修改一项配置后继续操作")}`);
                return;
            }
            if (!allValid) {
                this.$message.error(message || (this.$t("domain.detail.tip101") as string));
                return;
            }

            this.handleSubmit();
        }

        this.active = this.active > this.ACTIVE_ENUM.DOMAIN_LIST ? this.active : ++this.active;
    }
    async handleSubmit() {
        await this.$confirm(`${this.$t("domain.batch.tip1")}`, this.$t("common.dialog.submit") as string, {
            confirmButtonText: this.$t("common.dialog.submit") as string,
            cancelButtonText: this.$t("common.dialog.cancel") as string,
            type: "warning",
        });

        const payload = {
            domain_list: this.domainList.map(item => item.domain),
            ...this.formRef.formData,
        };

        this.batchLoading = true;
        this.$ctFetch<{
            failedDomainList: string[];
            validate: { name: string; pass: string; note: string }[];
        }>(BATCH_SUBMIT_URL, {
            method: "POST",
            body: payload,
            clearQsWithPost: false,
            clearEmptyParams: false,
            headers: {
                "Content-Type": "application/json",
            },
        })
            .then(async res => {
                if (res.failedDomainList.length === 0 || !res.failedDomainList) {
                    this.$message.success(this.$t("common.message.success") as string);
                    setTimeout(() => {
                        this.$router.push({
                            name: "ndomain.list",
                        });
                    }, 1000);
                } else if (res.failedDomainList && res.failedDomainList.length > 0) {
                    this.showFailedDomainDialog = true;
                    this.failedDomainList = res.failedDomainList;
                } else {
                    this.validateArrFromBe = res.validate;
                    this.validationTitle = `${this.$t("domain.list.batchEditBtn")}${this.$t(
                        "domain.create.tip30"
                    )}`;
                    this.showValidationModal = true;
                }
            })
            .catch(err => {
                // validate 字段当前后端返回的都是空数组
                const validate = err.data && err.data.data && err.data.data.validate;
                let v = true;
                if (validate) {
                    v = validate.reduce((acc: boolean, cur: { name: string; pass: string; note: string }) => {
                        return acc && cur.pass === "true";
                    }, true);
                    if (!v) {
                        this.validateArrFromBe = validate;
                        this.validationTitle = `${this.$t("domain.list.batchEditBtn")}${this.$t(
                            "domain.create.tip30"
                        )}`;
                        this.showValidationModal = true;
                    }
                }
                if (v) {
                    // 默认展示接口报错信息
                    this.$errorHandler(err);
                }
            })
            .finally(() => {
                this.batchLoading = false;
            });
    }
    transformDomainList(domainList: DomainList[]) {
        for (let i = 0; i < domainList.length; i++) {
            // 加速类型名称
            domainList[i].productName = domainList[i].subProductCode
                ? ProductCodeMap[domainList[i].subProductCode as keyof typeof ProductCodeMap]
                : ProductCodeMap[domainList[i].productCode as keyof typeof ProductCodeMap];
            // 加速区域
            domainList[i].areaScope = this.areaScopeMap.get(Number(domainList[i].areaScope)) as string;
            // 状态
            domainList[i].status = this.$t(`${(DomainStatusMap as any)[domainList[i].status]}`) as string;
            // 创建时间
            domainList[i].insertDate = timeFormat(domainList[i].insertDate);
        }
    }
}
</script>

<style lang="scss" scoped>
.domain-batchEdit {
    height: 100%; // 定高，出现滚动条
    display: flex;
    flex-direction: column;

    .batch-edit-step {
        padding: 20px;
        background: #fff;
        display: flex;
        justify-content: center;
        align-items: center;
        .step-container {
            width: 80%;
        }
    }

    .showDomain {
        overflow: hidden;
        flex: 1;
        margin: 20px;
        padding: 10px;
        display: flex;
        background: #fff;
        .el-table::before {
            z-index: 0;
        }
    }

    .notShowDomain {
        display: none;
    }

    .step3-div {
        overflow: hidden;
        flex: 1;
        margin: 10px;
        padding: 10px;
        display: flex;
        background: #fff;
    }

    // 报错提示 icon
    .el-icon-warning {
        color: $g-color-red;
    }

    // 步骤条样式后续优化
    ::v-deep {
        .el-step__title {
            font-size: 14px;
            line-height: 34px;
        }
    }

    .batch-edit-header {
        overflow: unset;
    }
}
</style>
