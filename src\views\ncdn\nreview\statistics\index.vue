<template>
    <ct-section-wrap headerText="审核分析" headerTip="支持一年内、最长时间跨度为一个月的检测数据查询。">
        <ct-box>
            <!-- 概览 -->
            <div class="overview" v-loading="todayLoading">
                <div class="block">
                    <p class="title">今日已审核图片量</p>
                    <p class="number">{{ todayData.total_count }}</p>
                </div>
                <div class="block">
                    <p class="title">今日审核疑似违规图片量</p>
                    <p class="number">{{ todayData.suspected_count }}</p>
                </div>
                <div class="block">
                    <p class="title">今日审核违规图片量</p>
                    <p class="number">{{ todayData.violated_count }}</p>
                </div>
                <div class="block">
                    <p class="title">今日审核违规检出率</p>
                    <p class="number">{{ violatedRate }}<span class="percent">%</span></p>
                </div>
                <div class="block">
                    <p class="title">今日审核域名数量</p>
                    <p class="number">{{ domainCount }}</p>
                </div>
            </div>

            <search-bar
                @search="getData"
                @domain-init="getTodayData"
                ref="search"
                paramFlag="multipleDomain, timePicker"
                class="search-bar"
            />

            <div class="chart-wrap">
                <div class="title-wrap">
                    <div class="total">
                        <div class="tip">图片量</div>
                    </div>
                </div>
                <v-chart
                    class="chart"
                    v-loading="loading"
                    element-loading-text="正在拼命绘图中..."
                    autoresize
                    theme="cdn"
                    :options="options"
                />
            </div>

            <ct-table
                :dataList="dataList"
                :columnConfig="conlumnConfig"
                :tableConfig="{ stripe: false }"
                v-loading="loading"
            />
        </ct-box>
    </ct-section-wrap>
</template>
<script lang="ts">
/* eslint-disable @typescript-eslint/camelcase */
import { Mixins, Component, Ref } from "vue-property-decorator";
import { THEME_AREA_STYLE } from "@/config/echart/cdn-theme";
import { nReviewUrl } from "@/config/url/ncdn/nreview";
import { IdentificationData } from "@/types/review/index";
import SearchBar from "./SearchBar.vue";
import ChartMixin from "./chartMixin";
import ErrorHandleMixin from "@cdnplus/common/mixins/errorHandle";
import { SearchParams } from "@/types/statistics/usage";
import { timeFormat } from "@/filters";

@Component({
    components: {
        SearchBar,
    },
})
export default class Index extends Mixins(ChartMixin, ErrorHandleMixin) {
    private todayLoading = false;
    private todayData = {
        normal_count: 0,
        suspected_count: 0,
        violated_count: 0,
        total_count: 0,
    };
    private domainCount = 0;
    protected fetchData: IdentificationData[] = [];
    protected downloadDataList: IdentificationData[] = []; // 用于下载的数据
    private dataList: IdentificationData[] = [];
    @Ref("search") readonly searchBar!: SearchBar;

    private get violatedRate() {
        return this.todayData.total_count === 0
            ? 0
            : Math.round((this.todayData.violated_count * 100) / this.todayData.total_count);
    }
    // 2、数据处理
    private get options() {
        const yAxisName = "单位：张";

        const xAxisData: string[] = [];
        const normalData: number[] = [];
        const suspectedData: number[] = [];
        const violatedData: number[] = [];
        const totalData: number[] = [];
        const fetchDataList = this.fetchData || [];
        fetchDataList
            .sort((a, b) => +new Date(a.event_time) - +new Date(b.event_time))
            .forEach(item => {
                xAxisData.push(item.event_time.replace(" ", "\n"));
                normalData.push(item.normal_count || 0);
                suspectedData.push(item.suspected_count || 0);
                violatedData.push(item.violated_count || 0);
                totalData.push(item.total_count || 0);
            });

        let count = 0;

        const options = {
            ...this.comOptions,
            toolbox: {},
            tooltip: {
                trigger: "axis",
            },
            xAxis: {
                type: "category", // 类目轴，从 data 中获取数据
                axisLabel: {
                    formatter(val: string): string | undefined {
                        // 间隔一个取标签
                        count += 1;
                        // x 轴均为日期，不显示数据前面的年份
                        if (count % 2 === 1) return val.slice(5, 16);
                    },
                },
                data: xAxisData,
            },
            yAxis: {
                type: "value", // 数值轴
                name: yAxisName,
            },
            series: [
                {
                    name: "正常图片量",
                    type: "line",
                    data: normalData,
                    areaStyle: THEME_AREA_STYLE["#6BE16B"],
                    itemStyle: {
                        color: "#6BE16B",
                    },
                },
                {
                    name: "疑似违规图片量",
                    type: "line",
                    data: suspectedData,
                    areaStyle: THEME_AREA_STYLE["#FFAA29"],
                    itemStyle: {
                        color: "#FFAA29",
                    },
                },
                {
                    name: "违规图片量",
                    type: "line",
                    data: violatedData,
                    areaStyle: THEME_AREA_STYLE["#FF0505"],
                    itemStyle: {
                        color: "#FF0505",
                    },
                },
                {
                    name: "审核总量",
                    type: "line",
                    data: totalData,
                    areaStyle: THEME_AREA_STYLE["#358AE2"],
                    itemStyle: {
                        color: "#358AE2",
                    },
                },
            ],
            legend: {
                data: ["正常图片量", "疑似违规图片量", "违规图片量", "审核总量"],
            },
        };

        return options;
    }

    private get conlumnConfig() {
        return [
            {
                prop: "event_time",
                label: "日期",
                minWidth: "120",
                align: "left",
                formatter: (row: any, column: any, cellValue: string) => cellValue.substring(0, 10),
            },
            {
                prop: "total_count",
                label: "审核总量",
                minWidth: "120",
                align: "left",
            },
            {
                prop: "suspected_count",
                label: "疑似违规图片量",
                minWidth: "120",
                align: "left",
            },
            {
                prop: "violated_count",
                label: "违规图片量",
                minWidth: "120",
                align: "left",
            },
            {
                prop: "normal_count",
                label: "正常图片量",
                minWidth: "120",
                align: "left",
            },
            // 违规图片总量 = 疑似违规图片量 + 违规图片量
            {
                label: "违规图片总量",
                minWidth: "120",
                align: "left",
                formatter: (row: IdentificationData) => row.suspected_count + row.violated_count,
            },
        ];
    }

    private async created() {
        this.getDomainCount();
    }
    private async getTodayData() {
        // mounted子组件才能拿到ref
        try {
            const today = new Date();
            today.setMilliseconds(0);
            today.setSeconds(0);
            today.setMinutes(0);
            today.setHours(0);
            this.todayLoading = true;
            const { list = [] } = await this.getIdentification({
                domainList: [...this.searchBar.domainParams],
                granularity: "1d",
                startTime: +today,
                endTime: +today + 86399999,
            });
            Object.assign(this.todayData, list[0]);
        } catch (err) {
            this.handle(err);
        } finally {
            this.todayLoading = false;
        }
    }
    protected getIdentification({
        domainList,
        granularity = "5m",
        startTime,
        endTime,
    }: {
        domainList: string[];
        granularity?: "5m" | "1d";
        startTime: number;
        endTime: number;
    }) {
        return this.$ctFetch<{ list: IdentificationData[] }>(nReviewUrl.reviewStatistic, {
            method: "POST",
            transferType: "json",
            clearEmptyParams: false,
            body: {
                data: {
                    domainList,
                    granularity,
                    startTime: timeFormat(startTime),
                    endTime: timeFormat(endTime),
                },
            },
            headers: {
                "Content-Type": "application/json",
            },
        });
    }
    protected async getData(params: SearchParams) {
        this.loading = true;
        const { list: chartList } = await this.getIdentification(params);
        const { list: tableList } = await this.getIdentification({ ...params, granularity: "1d" });
        this.dataList = tableList || [];
        // 处理用于下载的数据
        this.downloadDataList = this.fetchData = chartList;
    }

    private async getDomainCount() {
        try {
            const authDate = new Date();
            const { count } = await this.$ctFetch(nReviewUrl.domainCount, {
                data: {
                    authDate: `${authDate.getFullYear()}${("0" + (authDate.getMonth() + 1)).slice(-2)}${(
                        "0" + authDate.getDate()
                    ).slice(-2)}`,
                },
            });
            this.domainCount = count;
        } catch (err) {
            this.handle(err);
        }
    }
}
</script>

<style lang="scss" scoped>
.overview {
    border: 1px;
    box-shadow: 0 0 16px rgba(100, 110, 144, 0.2);
    height: 80px;
    width: 100%;

    .block {
        display: inline-flex;
        flex-flow: column;
        width: 20%;
        height: 80px;
        //border: 1px solid #000000;
        border-right: none;
        text-align: center;
        justify-content: center;
        vertical-align: middle;
        .title {
            font-size: 12px;
            margin-bottom: 8px;
        }
        .number {
            color: $color-master;
            font-size: 24px;
            font-weight: bold;
            .percent {
                font-size: 12px;
            }
        }
    }
}

.chart {
    clear: both;
    width: 100%;
    height: 280px;
}
.tip {
    color: #333333;
}
</style>
