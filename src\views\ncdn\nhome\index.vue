<template>
    <ct-section-wrap>
        <div class="wrap">
            <div class="col-left">
                <flow-summary class="summary" :onInit="onInit" />
                <chart class="chart" :onInit="onInit" />
                <more class="more" />
            </div>
            <div class="col-right">
                <total class="total" />
                <domain class="domain" />
                <billing v-if="overviewNewBilling" class="billing" />
                <old-billing v-else class="billing" />
            </div>
        </div>
    </ct-section-wrap>
</template>
<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import FlowSummary from "./components/FlowSummary.vue";
import Chart from "./components/Chart.vue";
import More from "./components/More.vue";
import Total from "./components/Total.vue";
import Domain from "./components/Domain.vue";
import Billing from "./components/Billing.vue";
import OldBilling from "./components/OldBilling.vue";
import { ProductModule } from "@/store/modules/ncdn/nproduct";
import { DomainModule } from "@/store/modules/domain";
import { DomainActionEnum, getDomainAction } from "@/store/config";
import { ScaleModule } from "@/store/modules/scale";
import { StatisticsModule } from "@/store/modules/statistics";
import { nUserModule } from "@/store/modules/nuser";

@Component({
    components: { FlowSummary, Chart, More, Total, Domain, Billing, OldBilling },
})
export default class Home extends Vue {
    onInit = true; // 是否已经初始化
    get isFcdnCtyunCtclouds() {
        return nUserModule.isFcdnCtyunCtclouds;
    }
    get overviewNewBilling() {
        return StatisticsModule.overviewNewBilling;
    }

    async mounted() {
        ProductModule.nGetProductInfo();
        const tasks = [];
        if (!this.isFcdnCtyunCtclouds) {
            !DomainModule[DomainActionEnum.Domain].list.length &&
                tasks.push(DomainModule.GetDomainList({ action: DomainActionEnum.Domain })); // Domain数据貌似在概览模块没有用到，ctiam管控下不默认调用，此处保留调用是为了保持原逻辑
            !DomainModule[DomainActionEnum.Data].list.length &&
                tasks.push(DomainModule.GetDomainList({ action: DomainActionEnum.Data }));
        } else {
            !DomainModule[getDomainAction("Home")].list.length &&
                tasks.push(DomainModule.GetDomainList({ action: getDomainAction("Home") }));
        }

        await Promise.allSettled(tasks);

        this.isFcdnCtyunCtclouds && (await ScaleModule.GetScaleHome()); // 改成概览页面的进制获取专用函数
        !this.isFcdnCtyunCtclouds && (await ScaleModule.GetScale());
        await StatisticsModule.GetBasicConfig();
        await StatisticsModule.GetChildAccount();

        this.onInit = false;
    }
}
</script>

<style lang="scss" scoped>
.wrap {
    @include g-media-attr((attr: "height",
            md: 100%,
            sm: 100%,
            xs: auto,
        ));
    min-height: 740px;
    overflow: hidden;

    .ct-box {
        min-height: auto;
        margin-bottom: 20px;
    }
}

.col-left {
    float: left;
    @include g-mg-bt(0, -20px);
    @include g-width(100%, 60%);
    @include g-height(auto, 100%);

    .summary {
        // margin-bottom: 0;
        height: 150px;
    }

    .more {
        @include g-height(350px, calc(100% - 450px));
    }
}

.col-right {
    display: inline-block;
    @include g-mg-bt(-10px, -20px);
    @include g-width(100%, calc(40% - 20px));
    @include g-mg-lf(0, 20px);
    @include g-height(auto, 100%);

    .total {
        height: 120px;
    }

    .domain {
        height: 300px;
    }

    .billing {
        @include g-height(400px, calc(100% - 460px));
    }
}

::v-deep {
    .el-radio-button__inner {
        width: 80px;
        padding: 7px 15px;
    }

    .el-loading-mask {
        z-index: 99; // 首页蒙层的层级特殊处理下，不遮挡 layout 的下拉框
    }
}
</style>
