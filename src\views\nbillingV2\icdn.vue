<template>
    <ct-section-wrap v-loading="icdnLoading.ondemand" class="billing-wrap" :headerText="headerTitle">
        <el-card class="billing-wrap-card">
            <span class="billing-wrap-card--title">
                {{ $t("billingDetail.detailTitle[0]") }}
            </span>
            <div class="billing-wrap-card--tip" v-if="isCtclouds">
                <p>{{ $t("billingDetail.detailTip[0]") }}</p>
                <p>{{ $t("billingDetail.detailTipCtcloud.tip1") }}</p>
                <p>{{ $t("billingDetail.detailTipCtcloud.tip2") }}</p>
            </div>
            <div class="billing-wrap-card--tip" v-else-if="vipBillingOpt">
                <p>{{ $t("billingDetail.detailTip[0]") }}</p>
                <p>{{ $t("billingDetail.detailTipNew.tip1") }}</p>
                <p>{{ $t("billingDetail.detailTipNew.tip2") }}</p>
                <p>{{ $t("billingDetail.detailTipNew.tip3") }}</p>
            </div>
            <div class="billing-wrap-card--tip" v-else>
                <p>{{ $t("billingDetail.detailTip[0]") }}</p>
                <p>{{ $t("billingDetail.detailTip[1]") }}</p>
                <p>{{ $t("billingDetail.detailTip[4]", { idx: 2 }) }}</p>
            </div>
            <el-button
                v-if="icdnOderEnable && orderEnable"
                type="primary"
                class="billing-wrap-card--btn"
                @click="openUrl(ondemandUrl, 'subscribe')"
                >{{ $t("billingDetail.icdn.ondemandOderBtn") }}</el-button
            >
            <p v-if="icdnOderEnable" class="billing-wrap-card--btn-tip">
                <ct-svg-icon icon-class="info-circle" class-name="alert-icon icon-style" />
                {{ $t("billingDetail.icdn.ondemandOderTip") }}
            </p>
            <el-table
                class="span-table"
                :highlight-current-row="false"
                :span-method="spanMethod"
                :data="ondemandDataList"
            >
                <el-table-column
                    :label="$t('billingDetail.ondemandOderTable.columns[0]')"
                    :width="isEn ? 150 : 120"
                >
                    <template slot-scope="{ row }">
                        {{ $t(getItemLabel(row.productCode)) }}
                    </template>
                </el-table-column>
                <el-table-column :label="$t('billingDetail.ondemandOderTable.columns[1]')" width="150">
                    <template slot-scope="{ row }">
                        {{ getOnDemandAcceArea(row) }}
                    </template>
                </el-table-column>
                <el-table-column :label="$t('billingDetail.ondemandOderTable.columns[2]')" width="150">
                    <template slot-scope="{ row }">
                        <el-tag size="mini" type="info">
                            {{ $t(`billingDetail.ondemandOderTable.serverType[${row.serverType}]`) }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column
                    :label="$t('billingDetail.ondemandOderTable.columns[3]')"
                    :min-width="isEn ? 230 : 180"
                >
                    <template slot-scope="{ row }">
                        {{
                            $t(getItemLabel(row.productCode)) +
                                "（" +
                                $t(
                                    `billingDetail.productName['${row.resourceType
                                        .replace(/_PT/, "")
                                        .replace(/_10MS/, "")}']`
                                ) +
                                "）" +
                                overseaRegionMap(row.overseaRegion) +
                                (showbillingType(row.resourceType) && row.billingType?.billingType
                                    ? `: ${$t(
                                          `billingDetail.billingTypeName['${row.billingType.billingType}']`
                                      )}`
                                    : "")
                        }}
                        <div v-if="Array.isArray(row.appendResource)">
                            <div v-for="(resource, index) in row.appendResource" :key="index">
                                {{ resource.resourceType }}
                                {{ resource.billingType ? `: ${resource.billingType}` : "" }}
                            </div>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column :label="$t('billingDetail.ondemandOderTable.columns[4]')" width="240">
                    <template slot-scope="{ row }">
                        <div>
                            {{ $t("billingDetail.packageOderTable.label[1]") + "：" }}
                            {{ row.billingType?.effDate }}
                        </div>
                        <div>
                            {{ $t("billingDetail.packageOderTable.label[2]") + "：" }}
                            {{ row.billingType?.expDate }}
                        </div>
                        <div v-if="checkExpired(row)">
                            <el-tooltip
                                :content="$t('billingDetail.packageOderTable.expireInfo[0]')"
                                placement="top"
                            >
                                <ct-svg-icon icon-class="info-circle" class-name="alert-icon icon-style" />
                            </el-tooltip>
                            {{ $t("billingDetail.packageOderTable.label[3]") }}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column :label="$t('billingDetail.ondemandOderTable.columns[5]')" width="120">
                    <template slot-scope="{ row }">
                        <span
                            class="status-icon"
                            :class="row.billingType?.status === 1 ? 'status-1' : 'status-0'"
                        />
                        {{ $t(`billingDetail.ondemandOderTable.status[${row.billingType?.status - 1}]`) }}
                    </template>
                </el-table-column>
                <el-table-column
                    v-if="orderEnable && vipBillingOpt"
                    :label="$t('billingDetail.ondemandOderTable.columns[6]')"
                    :width="isEn ? '220' : '180'"
                >
                    <template slot-scope="{ row }">
                        <div v-if="!row.isPt">
                            <el-button
                                class="table-btn"
                                v-if="row.opt?.unsubscribe"
                                :disabled="!row.opt?.unsubscribe?.enable"
                                type="text"
                                @click="openUrl(row.opt?.unsubscribe?.url, 'unsubscribe')"
                            >
                                {{ $t("billingDetail.tableOperate[0]") }}
                            </el-button>
                            <el-button
                                class="table-btn"
                                v-if="row.opt?.billingTypeChange"
                                :disabled="!row.opt?.billingTypeChange?.enable"
                                type="text"
                                @click="handleChangeBilling(dataMap[row.productCode], 'changeMethod')"
                            >
                                {{ $t("billingDetail.tableOperate[1]") }}
                            </el-button>
                            <el-button
                                class="table-btn"
                                v-if="row.opt?.billingArea && !isCtclouds"
                                :disabled="!row.opt?.billingArea?.enable"
                                type="text"
                                @click="openUrl(row.opt?.billingArea?.url, 'changeArea')"
                            >
                                {{ $t("billingDetail.tableOperate[2]") }}
                            </el-button>
                            <!-- 增减配内容审核 -->
                            <el-button
                                class="table-btn"
                                v-if="row.opt?.contentReview && !isCtclouds"
                                :disabled="!row.opt?.contentReview?.enable"
                                type="text"
                                @click="openUrl(row.opt?.contentReview?.url, 'addAudit')"
                            >
                                {{ row.opt?.contentReview?.name }}
                            </el-button>
                            <!-- 增减配高性能网络 -->
                            <el-button
                                class="table-btn"
                                v-if="row.opt?.premiumNetwork"
                                :disabled="!row.opt?.premiumNetwork?.enable"
                                type="text"
                                @click="openUrl(row.opt?.premiumNetwork?.url, 'addPremium')"
                            >
                                {{ row.opt?.premiumNetwork?.name }}
                            </el-button>
                            <!-- 增减配边缘函数 -->
                            <el-button
                                class="table-btn"
                                v-if="row.opt?.edgeFunction && !isCtclouds"
                                :disabled="!row.opt?.edgeFunction?.enable"
                                type="text"
                                @click="openUrl(row.opt?.edgeFunction?.url, 'edgeFunction')"
                            >
                                {{ row.opt?.edgeFunction?.name }}
                            </el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
        </el-card>
        <el-card class="billing-wrap-card">
            <span class="billing-wrap-card--title">
                {{ $t("billingDetail.detailTitle[1]") }}
            </span>
            <div class="billing-wrap-card--tip">
                <p>{{ $t("billingDetail.detailTip[5]") }}</p>
                <p>
                    {{
                        $t(
                            "billingDetail.【退款说明】资源包仅支持在购买时长≤7天且未进行任何使用的情况下申请退款，除此之外均不符合退款条件，不予以退款。"
                        )
                    }}
                </p>
                <!-- <p>{{ $t("billingDetail.detailTip[6]") }}</p> -->
                <p>{{ $t("billingDetail.detailTip[7]") }}</p>
            </div>
            <el-button
                v-if="orderEnable"
                type="primary"
                class="billing-wrap-card--btn"
                @click="openUrl(packageUrl, 'addCdnPackage')"
                >{{ $t("billingDetail.icdn.packageOderBtn") }}</el-button
            >
            <el-table
                v-loading="icdnLoading.package"
                :data="packageDataList"
                @filter-change="filterChange"
                @sort-change="sortTable"
            >
                <el-table-column
                    :label="$t('billingDetail.packageOderTable.columns[0]')"
                    prop="resource_packet_name"
                    min-width="200"
                />
                <el-table-column
                    :label="$t('billingDetail.packageOderTable.columns[1]')"
                    prop="billing_area"
                    min-width="210"
                >
                    <template slot-scope="{ row }">
                        {{
                            $t(`billingDetail.packageOderTable.billingArea[${row.billing_area}]`) +
                                overseaRegionMap(row.oversea_region)
                        }}
                    </template>
                </el-table-column>
                <el-table-column :label="$t('billingDetail.packageOderTable.columns[2]')" min-width="400">
                    <template slot-scope="{ row }">
                        <el-progress :show-text="false" :percentage="packagePercent(row)" />
                        <i18n path="billingDetail.packageOderTable.label[0]">
                            {{ convertUnit(row, true) + " / " + convertUnit(row) }}
                        </i18n>
                    </template>
                </el-table-column>
                <el-table-column
                    :label="$t('billingDetail.packageOderTable.columns[3]')"
                    sortable="custom"
                    min-width="230"
                    prop="eff_date"
                >
                    <template slot-scope="{ row }">
                        <div>
                            {{ $t("billingDetail.packageOderTable.label[1]") + "：" }}
                            {{ row.start_time }}
                        </div>
                        <div>
                            {{ $t("billingDetail.packageOderTable.label[2]") + "：" }}
                            {{ row.end_time }}
                        </div>
                        <!-- 资源包有效期<=60天且状态为”使用中“或”冻结“ -->
                        <div v-if="effDateLe60(row) && [2, 7].includes(row.status)">
                            <el-tooltip
                                :content="$t('billingDetail.packageOderTable.expireInfo[1]')"
                                placement="top"
                            >
                                <ct-svg-icon icon-class="info-circle" class-name="alert-icon icon-style" />
                            </el-tooltip>
                            {{ $t("billingDetail.packageOderTable.label[3]") }}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column
                    :label="$t('billingDetail.packageOderTable.columns[4]')"
                    prop="status"
                    column-key="status"
                    :filters="statusFilters"
                    width="140"
                >
                    <template slot-scope="{ row }">
                        <span class="status-icon" :class="`status-${row.status - 1}`" />
                        {{ $t(`billingDetail.packageOderTable.status[${row.status - 1}]`) }}
                    </template>
                </el-table-column>
                <el-table-column
                    :label="$t('billingDetail.packageOderTable.columns[5]')"
                    width="100"
                    v-if="orderEnable && vipBillingOpt"
                >
                    <template slot-scope="{ row }">
                        <el-button
                            :disabled="row.status === 6"
                            type="text"
                            @click="openUrl(row.refund_url, 'unsubscribePackage')"
                        >
                            {{ $t("billingDetail.tableOperate[0]") }}
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
            <div class="pager">
                <el-pagination
                    :small="isXs"
                    :layout="isXs ? 'prev, pager, next' : 'total, sizes, prev, pager, next,jumper'"
                    :total="total"
                    :current-page.sync="pageNum"
                    :page-size.sync="pageSize"
                    :page-sizes="[10, 30, 50, 100]"
                    :hide-on-single-page="false"
                    @size-change="handlePageSizeChange"
                    @current-change="handleCurrentPageChange"
                />
            </div>
        </el-card>
        <billing-type-dialog
            v-if="showChangeBilling"
            type="ICDN"
            :data="currentRow"
            @cancel="closeChangeBilling"
        />
    </ct-section-wrap>
</template>
<script lang="ts">
import { Component } from "vue-property-decorator";
import { ScreenModule } from "@/store/modules/screen";
import ctSvgIcon from "@/components/ctSvgIcon/index.vue";
import { newBillingUrl } from "@/config/url";
import { ShowProductMap, GetCtiamButtonAction, CtiamButtonActionKey } from "@/config/map";
import BillingTypeDialog from "./components/BillingTypeDialog.vue";
import ConfigMixin from "./configMixin";
import { checkCtiamButtonAuth } from "@/store/modules/menu";

const OpenUrlTypeMap: Record<string, CtiamButtonActionKey> = {
    subscribe: "icdnSubscribe",
    unsubscribe: "icdnUnsubscribe",
    changeMethod: "icdnChangeBillingMethod",
    changeArea: "icdnChangeAccelerationRegion",
    addAudit: "cdnAddOrReduceContentAudit", // 全站目前没有内容审核
    addPremium: "icdnAddOrReducePremiumNetworkService",
    addCdnPackage: "billingIcdnPackageSubscribe",
    unsubscribePackage: "icdnPackageUnsubscribe",
    edgeFunction: "billingIcdnEdgeFunction",
};

@Component({
    name: "icdnAcceleration",
    components: {
        ctSvgIcon,
        BillingTypeDialog,
    },
})
export default class IcdnAcceleration extends ConfigMixin {
    get headerTitle() {
        return this.$t("billingDetail.icdn.title");
    }
    get screenWidth() {
        return ScreenModule.width;
    }
    get isXs() {
        return this.screenWidth < 600;
    }
    get ondemandUrl() {
        return `${this.icdnProductOrderBtn}`;
    }
    get packageUrl() {
        return `${this.icdnPacketOrderBtn}`;
    }
    private icdnOderEnable = false;
    private ShowProductMap = ShowProductMap;
    private showChangeBilling = false;
    private ondemandDataList: any = [];
    private packageDataList: any = [];
    private total = 0;
    private pageNum = 1;
    private pageSize = 10;
    private icdnLoading = {
        ondemand: false,
        package: false,
    };
    private optList: any = [];
    private spanArrForProduct: any = [];
    private spanArrForServer: any = [];
    private spanArrForFs: any = []; // 增值服务-边缘函数合并
    private currentRow: any = {};
    private statusFilters = [
        { text: this.$t("billingDetail.packageOderTable.status[1]"), value: 2 },
        { text: this.$t("billingDetail.packageOderTable.status[2]"), value: 3 },
        { text: this.$t("billingDetail.packageOderTable.status[3]"), value: 4 },
        { text: this.$t("billingDetail.packageOderTable.status[4]"), value: 5 },
        { text: this.$t("billingDetail.packageOderTable.status[5]"), value: 6 },
        { text: this.$t("billingDetail.packageOderTable.status[6]"), value: 7 },
    ];

    async mounted() {
        try {
            this.icdnLoading.ondemand = true;
            await Promise.all([this.getOndemandDataList(), this.getConfig()]);
        } catch (e) {
            this.$errorHandler(e);
        } finally {
            this.icdnLoading.ondemand = false;
        }
        this.getPackageDataList();
    }

    // 加速类型
    private getItemLabel(itemValue: string): string {
        for (const key of ShowProductMap.keys()) {
            if (key.includes(itemValue)) {
                return ShowProductMap.get(key) as string;
            }
        }
        return "";
    }

    // 跳转链接
    private async openUrl(url: string, type: keyof typeof OpenUrlTypeMap) {
        await checkCtiamButtonAuth(GetCtiamButtonAction(OpenUrlTypeMap[type]), "", {
            urlProcessor: () =>
                ["unsubscribe", "changeArea", "addPremium", "addAudit", "unsubscribePackage"].includes(type)
                    ? new URL(url).pathname + "?" + new URL(url).searchParams
                    : url,
        });
        url += `&lang=${this.isCtclouds ? this.lang : "zh-cn"}`;
        window.open(url);
    }

    // 获取按需列表
    private async getOndemandDataList() {
        try {
            const res: any = await this.$ctFetch(newBillingUrl.icdnOndemandList);
            const productList = res.productList.filter((val: any) => {
                val.serverType = this.baseResourceType.includes(val.resourceType?.replace(/_PT/, "")) ? 0 : 1;
                // 全站加速特殊处理
                if (val.resourceType.replace(/_PT/, "") === "ICDN") {
                    val.appendResource = [
                        {
                            resourceType: this.$t("billingDetail.productName.ICDN_UPLOAD"),
                            billingType: this.$t(
                                `billingDetail.billingTypeName['${val.billingType?.billingType}']`
                            ),
                        },
                        {
                            resourceType: this.$t("billingDetail.productName.ICDN_WEBSOCKET"),
                            billingType: this.$t(
                                `billingDetail.billingTypeName['${val.billingType?.billingType}']`
                            ),
                        },
                        {
                            resourceType: this.$t("billingDetail.productName.ICDN_STAHTTPS_REQ"),
                            billingType: "",
                        },
                        { resourceType: this.$t("billingDetail.productName.ICDN_DYN_REQ"), billingType: "" },
                    ];
                }
                const resourceTypeWithoutPT = val.resourceType.replace(/_PT/, "");
                return !["ICDN_WEBSOCKET", "ICDN_UPLOAD"].includes(resourceTypeWithoutPT);
            });
            this.icdnOderEnable = res.orderBtn;
            this.optList = res.optList;
            this.setData(productList);
        } catch (err) {
            this.$errorHandler(err);
        }
    }

    // 列表、合并表格等数据整理
    private setData(data: any[]) {
        this.spanArrForProduct = [];
        this.spanArrForServer = [];
        this.spanArrForFs = [];
        this.dataMap = {};
        this.ondemandDataList = [];
        // 合成map并排序
        data.forEach(val => {
            if (!this.dataMap[val.productCode]) {
                this.dataMap[val.productCode] = {
                    base: [],
                    extensions: [],
                };
            }
            if (val.serverType === 0) {
                this.dataMap[val.productCode].base.push(val);
            } else {
                this.dataMap[val.productCode].extensions.push(val);
            }
        });

        for (const key in this.dataMap) {
            this.sortOnDemandDataList(this.dataMap[key].base, true);
            this.sortOnDemandDataList(this.dataMap[key].extensions);
        }

        // 装载数组
        Object.values(this.dataMap).forEach((item: any) => {
            const baseItem = item.base[0] || item.extensions[0];
            if (baseItem) {
                const opt = this.optList.find((val: any) => val.productCode === baseItem?.productCode);
                opt && (baseItem.opt = opt);
                baseItem.overseas = [];
            }
            const firstIndexForProduct = this.spanArrForProduct.length;
            let firstIndexForServer = this.spanArrForServer.length;
            item.base.forEach((val: any) => {
                this.ondemandDataList.push(val);
                // 有一项产品是非内地则认定为全球
                /_ABROAD/.test(val.resourceType) &&
                    (this.ondemandDataList[firstIndexForProduct].billing_area = [this.isCtclouds ? 2 : 1]);
                /_PT/.test(val.resourceType) && (this.ondemandDataList[firstIndexForProduct].isPt = true);
                // 收集已订购海外区域
                if (val.overseaRegion) {
                    baseItem.overseas.push(val.overseaRegion);
                }
                this.spanArrForProduct.push(0);
                this.spanArrForProduct[firstIndexForProduct]++;
                this.spanArrForServer.push(0);
                this.spanArrForServer[firstIndexForServer]++;
                this.spanArrForFs.push(1);
            });
            firstIndexForServer = this.spanArrForServer.length;
            item.extensions.forEach((val: any) => {
                this.ondemandDataList.push(val);
                // 有一项产品是非内地则认定为全球
                /_ABROAD/.test(val.resourceType) &&
                    (this.ondemandDataList[firstIndexForProduct].billing_area = [this.isCtclouds ? 2 : 1]);
                /_PT/.test(val.resourceType) && (this.ondemandDataList[firstIndexForProduct].isPt = true);
                this.spanArrForProduct.push(0);
                this.spanArrForProduct[firstIndexForProduct]++;
                this.spanArrForServer.push(0);
                this.spanArrForServer[firstIndexForServer]++;
                if (["_FS_10MS"].find(type => val.resourceType.includes(type))) {
                    this.spanArrForFs.push(3);
                } else if (["_FS_50MS", "_FS_100MS"].find(type => val.resourceType.includes(type))) {
                    this.spanArrForFs.push(0);
                } else {
                    this.spanArrForFs.push(1);
                }
            });

            this.ondemandDataList[firstIndexForProduct].billing_area = [0].concat(
                this.ondemandDataList[firstIndexForProduct].billing_area || []
            );
        });
    }

    // 合并表格方法
    private spanMethod({ row, column, rowIndex, columnIndex }: any) {
        // 加速类型、加速区域、操作 使用spanArrForProduct合并
        if ([0, 1, 6].includes(columnIndex)) {
            const _row = this.spanArrForProduct[rowIndex];
            const _col = _row > 0 ? 1 : 0;
            return { rowspan: _row, colspan: _col };
        }
        // 服务类型 使用spanArrForServer合并
        if ([2].includes(columnIndex)) {
            const _row = this.spanArrForServer[rowIndex];
            const _col = _row > 0 ? 1 : 0;
            return { rowspan: _row, colspan: _col };
        }
        // 合并边缘函数
        if (
            ["_FS_10MS", "_FS_50MS", "_FS_100MS"].find(type => row.resourceType.includes(type)) &&
            [3, 4, 5].includes(columnIndex)
        ) {
            const _row = this.spanArrForFs[rowIndex];
            return { rowspan: _row, colspan: 1 };
        }
    }

    // 获取资源包列表
    private async getPackageDataList() {
        try {
            this.icdnLoading.package = true;
            const res: any = await this.$ctFetch(newBillingUrl.icdnPackageList, {
                data: {
                    pageIndex: this.pageNum,
                    pageSize: this.pageSize,
                    ...this.sortFlag,
                    ...this.filterFlag,
                },
            });
            this.total = res.total;
            this.packageDataList = res.list;
        } catch (err) {
            this.$errorHandler(err);
        } finally {
            this.icdnLoading.package = false;
        }
    }
    // 过滤操作
    private filterFlag: any = {};
    private filterChange(data: any) {
        this.filterFlag = data;
        this.pageNum = 1;
        this.getPackageDataList();
    }

    // 排序操作
    private sortFlag = {
        fieldSort: "",
        sortOrder: "",
    };
    private sortTable({ prop, order }: { prop: string; order: string }) {
        // 存在排序则处理参数
        if (order) {
            this.sortFlag = {
                sortOrder: order === "ascending" ? "asc" : "desc",
                fieldSort: prop,
            };
        } else {
            this.sortFlag = {
                fieldSort: "",
                sortOrder: "",
            };
        }
        this.getPackageDataList();
    }

    // 检查按需是否临期 & 没有待生效的计费方式
    private checkExpired(row: any) {
        // 待生效的计费方式不显示临期提示
        if (row?.billingType?.status === 2) return false;

        const cur = new Date();
        const expire = new Date(cur.getTime() + 7 * 24 * 60 * 60 * 1000);
        const end = new Date(row.billingType?.expDate);
        const onExpired = cur < end && expire > end;

        const sameProduct = this.ondemandDataList.find((itm: any) => {
            if (itm?.billingType?.status !== 2) return false;
            return itm.resourceId === row.resourceId;
        });

        // 如果存在相同的待生效产品，判断待生效产品是否临期
        if (sameProduct) {
            const sameCur = new Date();
            const sameExpire = new Date(sameCur.getTime() + 7 * 24 * 60 * 60 * 1000);
            const sameEnd = new Date(sameProduct.billingType?.expDate);
            return sameCur < sameEnd && sameExpire > sameEnd;
        }

        return onExpired;
    }

    // 资源包剩余率
    private packagePercent(row: any) {
        const { packet_size, used_num } = row;
        return ((packet_size - +used_num) / packet_size) * 100;
    }
    // 变更计费方式
    private async handleChangeBilling(row: any, type: keyof typeof OpenUrlTypeMap) {
        await checkCtiamButtonAuth(GetCtiamButtonAction(OpenUrlTypeMap[type]));
        this.currentRow = row;
        this.showChangeBilling = true;
    }
    // 关闭变更计费弹窗
    private closeChangeBilling(refresh = false) {
        refresh && this.getOndemandDataList();
        this.showChangeBilling = false;
        this.currentRow = {};
    }
    private handlePageSizeChange(pageSize: number) {
        this.pageSize = pageSize;
        this.pageNum = 1;
        this.getPackageDataList();
    }
    private handleCurrentPageChange(page: number) {
        this.pageNum = page;
        this.getPackageDataList();
    }
}
</script>

<style scoped lang="scss">
@import "./index.scss";
</style>
