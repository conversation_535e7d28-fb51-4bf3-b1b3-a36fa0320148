/*
 * @Description: 错误处理函数
 * @Author: wang yuegong
 *  功能：
 *      1、错误信息格式化，统一输出为 { code: '', reason: '' } 格式（错误信息的类型：ctFetch 错误、js 执行错误）
 *      2、格式化时，如果判定为 IgnoreCode 范围，则返回 null
 *      3、格式化后的信息，如果非空则存入队列，并触发展示
 *      4、展示读取队列中所有数据，做列表展示；展示中二次触发时，更新展示
 *      5、关闭错误提示时，清空队列
 *      6、增补功能：url路径（插件中如何获取）、报错对应的处理动作（code 关联）、只展示当前路由产生的错误信息（配合 ctFetch 拦截器使用）
 */
import Vue from "vue";
import i18n from "./i18n";
import CtErrorModal from "../../components/mod/CtErrorModal.vue";
import { ErrorMsg, CtFetchMsg } from "./errorTypes";
import { IgnoreCode, DefaultModalBtnConfig, ReasonCanUseHtmlCode, CtiamCode } from "./errorConfig";
import { PopupManager } from "element-ui/lib/utils/popup";

// 错误信息队列
const errQueue: ErrorMsg[] = [];

// 错误处理组件
const Modal = Vue.extend(CtErrorModal);
let ModalInstance: null | Vue;

// forbidden页面对弹窗的特殊处理监听器
let forbiddenHandler: ((instance: Vue) => void) | null = null;

// 添加实例监听器-用于forbidden页面对弹窗的特殊处理
export function addModalInstanceListener(listener: (instance: Vue) => void) {
    forbiddenHandler = listener;
    // 如果实例已存在，立即调用
    if (ModalInstance) {
        Vue.nextTick(() => forbiddenHandler?.(ModalInstance!));
    }
}

// 移除实例监听器
export function removeModalInstanceListener(listener: (instance: Vue) => void) {
    if (forbiddenHandler === listener) {
        forbiddenHandler = null;
    }
}

// 获取当前页面的 hash 路由
export function getHashRoute() {
    return window.location.hash.split("?")[0];
}

// 展示错误信息
function showErr() {
    // 实例不存在时创建
    if (!ModalInstance) {
        ModalInstance = new Modal({
            i18n,
        });
        document.body.appendChild(ModalInstance.$mount().$el);

        // 监听弹窗关闭
        ModalInstance.$on("err.close", function() {
            // 清空错误信息队列
            errQueue.length = 0;
        });

        // 通知forbidden页面处理器
        Vue.nextTick(() => forbiddenHandler?.(ModalInstance!));
    }

    // 过滤非当前路由的报错
    const currentHashRoute = getHashRoute();
    const errQueueShowList = errQueue.filter(err => !err.hashRoute || err.hashRoute === currentHashRoute);

    // 传入错误列表，开始展示弹窗
    if (errQueueShowList.length > 0) {
        (ModalInstance as any).open(errQueueShowList);
        // 通知forbidden页面处理器
        Vue.nextTick(() => forbiddenHandler?.(ModalInstance!));
    }
}

// 错误数据格式化，三种报错的格式各不相同
function errFormat(err: Error & CtFetchMsg): ErrorMsg {
    // 类型1：接口响应错误（格式为 ctFetch 内置错误，见 node_modules\alogic-base-web\src\ctFetch\err.js ）
    if (err.data) {
        if (CtiamCode.includes(err.data.code)) {
            return {
                ...err.data,
                isCtiamCode: true,
            };
        }
        return {
            ...err.data,
            reasonUseHtml: ReasonCanUseHtmlCode.includes(err.data.code),
        };
    }

    // 类型2：类型1的扩展，eg：网络异常类
    if (err.code) {
        return err;
    }

    // 类型3：js 执行错误，Error 对象
    if (err.name) {
        return {
            code: `js.${err.name}`,
            reason: `${err.name}: ${err.message}` || (i18n.t("jsError") as string),
            stack: err.stack?.split("\n")[1]?.trim(), // 只展示第一条堆栈信息
        };
    }

    return err;
}

export function errorHandler(err: Error & CtFetchMsg) {
    // 弹窗是基于单例模式进行封装的，理想是一个系统中只存在一个errorHandler弹窗
    // 作为子应用时，报错信息交由主应用。
    if ((window as any).__POWERED_BY_QIANKUN__) {
        const microAppCustomEvent = new CustomEvent("microAppEvent", {
            detail: {
                event: "errorHandler",
                data: {
                    err: err,
                    zIndex: PopupManager.zIndex, // 当前弹窗层级
                }
            },
        });
        window.dispatchEvent(microAppCustomEvent);
        return;
    }

    // 错误数据格式化
    const _err = errFormat(err);

    // 如果需要处理错误，未被忽略
    if (!IgnoreCode.includes(_err.code)) {
        // 处理方案按钮：1、来自于业务中主动 catch 时配置的方案（复杂） 2、通过 babel 插件自动 catch 时预置的方案（简单）
        const _btnConfig = err.btnConfig || DefaultModalBtnConfig[_err.code];
        // 如果有按钮配置则添加
        _btnConfig && (_err.btnConfig = _btnConfig);

        // 当 hashRoute 匹配时才存入队列
        const currentHashRoute = getHashRoute();
        if (!_err.hashRoute || _err.hashRoute === currentHashRoute) {
            // 错误信息存入队列
            errQueue.push(_err);
        }

        // 执行错误展示
        errQueue.length > 0 && showErr();
    }
}
