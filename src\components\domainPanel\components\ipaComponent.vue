<template>
    <div class="box-component">
        <div class="flex-row-style space-between">
            <div
                v-loading="numberLoading"
                v-for="(item, key) in numberData"
                :key="key"
                class="horizontal-vertical-center domain-number-box"
                @click="handleChangeCDNFilterData(item)"
            >
                <p class="number-style">{{ renderCountNumber(item.prop) }}</p>
                <div class="flex-row-style">
                    <span>{{ item.label }}</span>
                </div>
            </div>
        </div>
        <!--  过滤区域-->
        <ct-filter-box ref="filterBox" :data="cdnFilterData" default-type="domain" :form.sync="form" />
    </div>
</template>

<script>
import commonMixin from "../commonMixin";
import { get } from "lodash-es";

export default {
    name: "cdnComponent",
    mixins: [commonMixin],
    data() {
        return {
            form: {
                domain: "",
                inst_name: "",
                status: "",
                access_mode: 0,
                // page: 1,
                // page_size: 1000,
            },
            cdnFilterData: [
                {
                    label: "域名",
                    value: "domain",
                    type: "input",
                    config: { placeholder: "搜索关键词", clearable: true },
                },
                {
                    label: "实例",
                    value: "inst_name",
                    type: "input",
                    config: { placeholder: "搜索关键词", clearable: true },
                },
                { label: "域名状态", value: "status", type: "select" },
            ],

            numberData: [
                { label: "已启用", prop: "enabledCount", value: 4 },
                { label: "配置中", prop: "deployingCount", value: 3 },
            ],
        };
    },
    methods: {
        /**
         * 处理改变CDN过滤数据
         */
        handleChangeCDNFilterData(item) {
            const value = item.value;
            if (!this.$refs.filterBox) {
                return;
            }

            this.$refs.filterBox.currentType = "status";
            this.$refs.filterBox.handleCurrentTypeChange(true);
            this.$refs.filterBox.formInside.status = value;
            this.$refs.filterBox.handleChange();
        },
        renderCountNumber(prop) {
            return get(this.numberTotalData, prop, 0) || 0;
        },
    },
};
</script>

<style scoped lang="scss">
@import "./common.scss";
</style>
