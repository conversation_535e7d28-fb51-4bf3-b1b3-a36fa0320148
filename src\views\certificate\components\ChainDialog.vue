<template>
    <el-dialog
        :title="$t('certificate.chain.title')"
        :visible.sync="chainVisible"
        :close-on-click-modal="false"
        :before-close="beforeClose"
        append-to-body
        class="chain-dialog"
    >
        <h3 class="err-header">
            <i class="el-icon-warning" />
            <template>
                {{ $t("certificate.chain.reason") }}
            </template>
        </h3>
        <div class="err-more">
            <div class="err-more-title" @click="toggleMore">
                {{ $t("certificate.chain.detail") }}：<i
                    class="el-icon-arrow-down"
                    :class="showMore ? 'is-reverse' : ''"
                />
            </div>
            <el-table
                :empty-text="$t('common.table.empty')"
                v-show="showMore"
                :data="chainDetail.data.metadata"
                class="err-more-msg"
            >
                <el-table-column
                    :label="$t('certificate.chain.label1')"
                    prop="name"
                    :min-width="isEn ? '120' : '80'"
                >
                    <template slot-scope="{ row }">
                        {{ row.name.subject.common_name }}
                    </template>
                </el-table-column>
                <el-table-column
                    :label="$t('certificate.chain.label2')"
                    prop="name"
                    :width="isEn ? '120' : '80'"
                >
                    <template slot-scope="{ row }">
                        {{ $t(typsMap[row.type]) }}
                    </template>
                </el-table-column>
                <el-table-column :label="$t('certificate.chain.label3')" prop="name" width="120">
                    <template slot-scope="{ row }">
                        {{ $t(statusMap[row.status]) }}
                    </template>
                </el-table-column>
                <el-table-column
                    :label="$t('certificate.chain.label4')"
                    prop="name"
                    :min-width="isEn ? '160' : '80'"
                >
                    <template slot-scope="{ row }">
                        {{ row.message }}
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <div slot="footer" class="dialog-footer">
            <el-button @click="close"> {{ $t("common.dialog.cancel") }} </el-button>
            <el-button type="primary" @click="ensure">
                {{ isAddCert ? $t("certificate.chain.ensure") : $t("domain.batch.next") }}
            </el-button>
        </div>
    </el-dialog>
</template>
<script lang="ts">
import { nUserModule } from "@/store/modules/nuser";
import { Component, Prop, Vue } from "vue-property-decorator";

@Component({})
export default class ChainDialog extends Vue {
    // 弹框显示标志位
    @Prop({ default: false, type: Boolean }) private chainVisible!: boolean;
    // 错误详情
    @Prop({ default: { reason: "", data: { metadata: [] } }, type: Object }) private chainDetail!: object;
    @Prop({ default: true, type: Boolean }) private isAddCert!: boolean;
    showMore = false; // 错误详情是否展示
    pageSize = 1;
    // 证书类型映射
    typsMap = {
        1: "certificate.chain.certTypes[0]",
        2: "certificate.chain.certTypes[1]",
        3: "certificate.chain.certTypes[2]",
    };

    // 任务状态映射
    statusMap = {
        "1": "certificate.chain.status[0]",
        "2": "certificate.chain.status[1]",
        "-1": "certificate.chain.status[2]",
    };

    // 关闭时的处理
    beforeClose() {
        this.showMore = false; // 默认不展示详情
        this.$emit("certClose");
    }

    close() {
        this.beforeClose();
    }

    // 用户指定的确定按钮
    ensure() {
        // 触发完执行关闭操作
        this.$emit("chainEnsure");
        this.close();
    }

    toggleMore() {
        this.showMore = !this.showMore;
    }
    get isEn() {
        return nUserModule.lang === "en";
    }
}
</script>

<style lang="scss" scoped>
::v-deep {
    .el-dialog {
        max-width: 800px;
    }
    .el-dialog__footer {
        text-align: right;
    }
}
.chain-dialog {
    ::v-deep {
        .err-header {
            margin-bottom: 10px;
            font-size: 14px;
            line-height: 20px;
            font-weight: bold;
            word-break: break-word;

            .el-icon-warning {
                margin-right: 4px;
                color: $color-warning;
                font-size: 14px;
                vertical-align: middle;
            }
        }

        .err-more {
            font-size: 12px;
        }

        // 更多信息的标题
        .err-more-title {
            margin-left: 5%;
            color: $color-info;
            cursor: pointer;

            &:hover {
                color: $color-info-hover;
            }
        }
        .err-more-msg {
            color: $color-info;

            .err-msg-key {
                color: $color-info;
            }

            .err-msg-value {
                color: $color-info;
            }
        }

        // 更多信息箭头
        .el-icon-arrow-down {
            transition: transform 0.3s;
            transform: rotate(0deg);

            &.is-reverse {
                transform: rotate(180deg);
            }
        }
    }
}
</style>
