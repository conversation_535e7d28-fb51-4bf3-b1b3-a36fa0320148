# HostSelect 组件

一个用于选择主机配置的 Vue 组件，支持加速域名、源站域名和自定义域名的选择，并提供表单校验功能。

## 功能特性

- 支持三种类型选择：加速域名、源站域名、自定义域名
- 加速域名和源站域名支持下拉选择（带搜索功能）
- 自定义域名支持手动输入
- 支持占位符和禁用状态
- 智能占位符：根据选择的类型自动显示相应的占位符文本
- 内置表单校验：支持 reqHost 和 originHost 类型的格式校验
- 冲突检测：防止两种 host 同时配置
- 下拉选项校验：确保选择的值在可选项中
- 智能初始化：根据传入的 value 自动匹配对应的类型
- 自动选择：当加速域名只有一个选项时，切换到该类型会自动选中
- 输入框提示：为自定义输入框添加了 tooltip 提示

## 参数说明

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| value | String | '' | 当前选中的值，支持 v-model |
| accelerateDomains | Array | [] | 加速域名列表 |
| originDomains | Array | [] | 源站域名列表 |
| disabled | Boolean | false | 是否禁用组件 |
| hostType | String | 'reqHost' | host类型，可选值：'reqHost'、'originHost' |
| otherHostValue | String | '' | 另一个host的值，用于冲突检测 |
| label | String | '' | 表单项标签 |
| prop | String | '' | 表单项属性名 |
| labelWidth | String | '' | 标签宽度 |
| rules | Object | '' | 校验规则 |

## Events

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| input | 值变化时触发，支持 v-model | (value: string) |
| change | 值变化时触发 | ({ type: string, value: string }) |

## 使用示例

### 基础用法（作为表单项）

```vue
<template>
  <el-form :model="form" ref="form">
    <!-- reqHost 配置 -->
    <host-select
      v-model="form.req_host"
      :accelerate-domains="accelerateDomains"
      :origin-domains="originDomains"
      host-type="reqHost"
      :other-host-value="form.origin_host"
      label="回源HOST"
      prop="req_host"
      label-width="120px"
      :rules="rules"
      @change="handleReqHostChange"
    />

    <!-- originHost 配置 -->
    <host-select
      v-model="form.origin_host"
      :accelerate-domains="accelerateDomains"
      :origin-domains="originDomains"
      host-type="originHost"
      :other-host-value="form.req_host"
      label="指定源站回源HOST"
      prop="origin_host"
      label-width="120px"
      :rules="rules"
      @change="handleOriginHostChange"
    />
  </el-form>
</template>

<script>
import HostSelect from '@/components/hostSelect/hostSelect.mod.vue'

export default {
  components: {
    HostSelect
  },
  data() {
    return {
      form: {
        req_host: '',
        origin_host: ''
      },
      accelerateDomains: ['cdn1.example.com', 'cdn2.example.com'],
      originDomains: ['origin1.example.com', 'origin2.example.com']
    }
  },
  methods: {
    handleReqHostChange(data) {
      console.log('ReqHost changed:', data)
    },
    handleOriginHostChange(data) {
      console.log('OriginHost changed:', data)
    }
  }
}
 </script>
 ```

## 校验规则

组件内置了以下校验规则：

1. **下拉选项校验**：当选择加速域名或源站域名时，确保选择的值在对应的可选项列表中
2. **格式校验**：
   - `reqHost` 类型：使用 `checkReqHost` 函数进行格式校验
   - `originHost` 类型：使用 `checkOriginHost` 函数进行格式校验
3. **冲突检测**：防止 `reqHost` 和 `originHost` 同时配置

## 注意事项

1. 组件已包含 `el-form-item`，可直接在 `el-form` 中使用
2. 需要传入 `hostType` 来区分是 `reqHost` 还是 `originHost`
3. 传入 `otherHostValue` 用于冲突检测
4. 组件会自动处理表单校验，无需额外配置校验规则
 5. 事件回调中的 `change` 事件会返回包含 `type` 和 `value` 的对象
