<template>
    <el-dialog
        :title="$t('domain.list.note')"
        :append-to-body="true"
        :close-on-click-modal="false"
        :visible.sync="dialogVisible"
        :before-close="submit"
        class="domain-count-limit-dialog"
        width="520px"
        :show-close="true"
    >
        <el-alert title="" type="info" :closable="false" show-icon class="ct-alert">
            <template slot="title">
                <div>
                    <span>{{ $t("domain.list.domainCountLimitTip.tip1") }}</span>
                    <a @click="$docHelp(domainCountLimitLink)" class="aocdn-ignore-link">
                        {{ $t("domain.list.domainCountLimitTip.tip2") }}
                    </a>
                </div>
            </template>
        </el-alert>
        <div slot="footer" class="btns">
            <el-button type="primary" @click="submit">{{ $t("common.dialog.submit") }}</el-button>
        </div>
    </el-dialog>
</template>

<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";

@Component({})
export default class UpdateDialog extends Vue {
    @Prop({ default: false, type: Boolean }) private dialogVisible!: boolean;
    @Prop({ default: "", type: String }) private domainCountLimitLink!: string;

    private async submit() {
        this.$emit("submit");
    }
}
</script>

<style lang="scss" scoped>
.domain-count-limit-dialog {
    .ct-alert {
        ::v-deep {
            .el-alert {
                align-items: center;
                padding: 12px;
            }
            a {
                color: $color-master;
            }
        }
    }
}
</style>
