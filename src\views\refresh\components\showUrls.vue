<template>
    <el-dialog
        :visible="dialogVisible"
        title="内容"
        width="1000px"
        :before-close="handleClose"
        :close-on-click-modal="false"
    >
        <div v-for="(url, index) in urls" :key="index" class="url-item">
            {{ url }}
        </div>
        <span slot="footer" class="dialog-footer">
            <el-button @click="handleClose">关闭</el-button>
        </span>
    </el-dialog>
</template>

<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";

@Component
export default class ShowScheduelUrlList extends Vue {
    @Prop({ type: Array, required: true }) urls!: string[];
    @Prop({ type: Boolean, required: true }) dialogVisible!: false;

    handleClose() {
        this.$emit("close");
    }
}
</script>

<style scoped>
.url-item {
    margin-bottom: 8px;
    word-break: break-all;
    font-size: 14px;
}
</style>
