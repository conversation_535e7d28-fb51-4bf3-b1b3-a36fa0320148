<template>
    <div class="backorigin-time-wrapper">
        <el-form
            ref="backoriginTimeForm"
            label-width="140px"
            label-position="left"
            :model="form"
            :rules="rules"
            :disabled="!isEdit || !isService || isLockBackOriginTime"
        >
            <div v-if="temp_domain_detail.use_ecgw === 1">
                <el-form-item :label="$t('domain.editPage.label5')">
                    <el-switch
                        v-model="form.backorigin_switch"
                        :active-value="1"
                        :inactive-value="0"
                        @change="onSwitch"
                    ></el-switch>
                    <span class="tips">
                        <ct-svg-icon icon-class="info-circle" class-name="icon-column-label" />
                        {{ $t("domain.editPage.tip2") }}
                    </span>
                </el-form-item>
                <div v-if="form.backorigin_switch" class="switch-wrapper">
                    <el-form-item
                        :label="$t('domain.editPage.label6')"
                        prop="backup_origin_timeout"
                        :rules="rules.backup_origin_timeout"
                    >
                        <el-input v-model="form.backup_origin_timeout" class="data-input" @blur="onTimeoutBlur">
                            <template slot="append">{{ $t("simpleForm.alogicCacheMixin.CacheTtlMap.5") }}</template>
                        </el-input>
                    </el-form-item>
                    <el-form-item
                        :label="$t('domain.editPage.label7')"
                        prop="backup_origin_resptimeout"
                        :rules="rules.backup_origin_timeout"
                    >
                        <el-input
                            v-model="form.backup_origin_resptimeout"
                            class="data-input"
                            @blur="onResTimeoutBlur"
                        >
                            <template slot="append">{{ $t("simpleForm.alogicCacheMixin.CacheTtlMap.5") }}</template>
                        </el-input>
                    </el-form-item>
                </div>
            </div>
        </el-form>
    </div>
</template>

<script>
import componentMixin from "@/views/domainConfig/componentMixin";
import ctSvgIcon from "@/components/ctSvgIcon";
import { SecurityAbilityModule } from "@/store/modules/securityAbility";

export default {
    mixins: [componentMixin],
    props: {
        datas: Object,
        isLockBackOriginTime: Boolean,
        temp_domain_detail: Object,
    },
    components: {
        ctSvgIcon,
    },
    data() {
        return {
            form: {
                backorigin_switch: 0,
                backup_origin_timeout: "", // 回源连接超时时间
                backup_origin_resptimeout: "", // 回源请求超时时间
            },
            rules: {
                backup_origin_timeout: [
                    {
                        required: false,
                        validator: this.validateNum,
                        trigger: "blur",
                    },
                ],
            },
        };
    },
    watch: {
        datas: {
            deep: true,
            handler(val) {
                this.init(val);
            },
            immediate: true,
        },
    },
    methods: {
        // 回源连接超时时间和回源请求超时时间必填一个，失焦时，触发另一个的校验
        onTimeoutBlur() {
            this.$refs.backoriginTimeForm.validateField("backup_origin_resptimeout");
            this.$emit("onChange", this.form);
        },
        onResTimeoutBlur() {
            this.$refs.backoriginTimeForm.validateField("backup_origin_timeout");
            this.$emit("onChange", this.form);
        },
        validateNum(rule, value, callback) {
            if (this.isLockBackOriginTime) callback();
            const num = /^[0-9]*$/;
            const isEmptyButZero = v => v !== 0 && !v;
            if (isEmptyButZero(value)) {
                if (
                    isEmptyButZero(this.form.backup_origin_timeout) &&
                    isEmptyButZero(this.form.backup_origin_resptimeout)
                ) {
                    return callback(new Error(this.$t("domain.detail.placeholder41")));
                } else callback();
            } else if (!num.test(value) || value > 300) {
                return callback(new Error(this.$t("domain.detail.placeholder42")));
            } else {
                callback();
            }

        },
        onSwitch(v) {
            if (v === 0) {
                this.form.backup_origin_timeout = "";
                this.form.backup_origin_resptimeout = "";
            }

            const originalForm = SecurityAbilityModule.securityBasicConfigOriginForm;
            if (
                (v && originalForm?.backup_origin_timeout) ||
                originalForm?.backup_origin_resptimeout
            ) {
                this.form.backup_origin_timeout = originalForm?.backup_origin_timeout;
                this.form.backup_origin_resptimeout = originalForm?.backup_origin_resptimeout;
            }

            this.$emit("onChange", this.form);
        },
        init(v) {
            if (!v) return;
            this.form.backup_origin_timeout = v.backup_origin_timeout;
            this.form.backup_origin_resptimeout = v.backup_origin_resptimeout;
            this.form.backorigin_switch = v.backorigin_switch;
        },
    },
};
</script>

<style lang="scss" scoped>
.data-input {
    width: 395px;
}
.tips {
    .question-circle {
        font-size: $text-size-md;
        cursor: pointer;
        margin-right: $margin;
    }
    font-size: 12px;
    color: $color-neutral-7;
    font-weight: 400;
    margin-left: $margin;
}
</style>
