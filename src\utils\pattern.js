import { url, domain, ip, genericDomain, port } from "@cdnplus/common/config/pattern";

export * from "@cdnplus/common/config/pattern";

// url验证
// export const url = /^(((ht|f)tps?)?:\/\/(([a-zA-Z0-9]+-?)+[a-zA-Z0-9]+\.)+[a-zA-Z]+)(:\d+)?(\/.*)?(\?.*)?(#.*)?$/;
// 身份证检验
export const idCard = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
// referer检验
export const regUrl = /(http|ftp|https):\/\/[\w\-_]+(\.[\w\-_]+)+([\w\-.,@?^=%&amp;:/~+#]*[\w\-@?^=%&amp;/~+#])?/;
// 手机号码检验
export const regPhoneNum = /^1[3|4|5|6|7|8|9][0-9]\d{8}$/;
// 身份证检验
export const regIdCard = /(^\d{15}$)|(^\d{17}(\d|X)$)/;
// 银行卡检验
export const regBankCard = /^\d+$/;
// 空行校验
export const blankRow = /\n(\n)*( )*(\n)*\n/g;
// 两位小数
export const decimals = /^(([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/g;
// 邮箱
// eslint-disable-next-line no-useless-escape
export const email = /^([a-zA-Z]|[0-9])(\w|\-)+@[a-zA-Z0-9]+\.([a-zA-Z]{2,4})$/;
// 校验ip或ip+端口
export const genericIp = `^(${ip.replace(/\^|\$/g, "")})(:${port})?$`;
// ipv6
export const ipv6Pattern = /^([\da-fA-F]{1,4}:){6}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^::([\da-fA-F]{1,4}:){0,4}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^([\da-fA-F]{1,4}:):([\da-fA-F]{1,4}:){0,3}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^([\da-fA-F]{1,4}:){2}:([\da-fA-F]{1,4}:){0,2}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^([\da-fA-F]{1,4}:){3}:([\da-fA-F]{1,4}:){0,1}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^([\da-fA-F]{1,4}:){4}:((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^([\da-fA-F]{1,4}:){7}[\da-fA-F]{1,4}$|^:((:[\da-fA-F]{1,4}){1,6}|:)$|^[\da-fA-F]{1,4}:((:[\da-fA-F]{1,4}){1,5}|:)$|^([\da-fA-F]{1,4}:){2}((:[\da-fA-F]{1,4}){1,4}|:)$|^([\da-fA-F]{1,4}:){3}((:[\da-fA-F]{1,4}){1,3}|:)$|^([\da-fA-F]{1,4}:){4}((:[\da-fA-F]{1,4}){1,2}|:)$|^([\da-fA-F]{1,4}:){5}:([\da-fA-F]{1,4})?$|^([\da-fA-F]{1,4}:){6}:$/;

/**
 * 检查手机号码格式
 * @param rule
 * @param value
 * @param callback
 * @returns {*}
 */
export function checkPhone(rule, value, callback) {
    if (!value) {
        return callback();
    }

    if (regPhoneNum.test(value)) {
        return callback();
    }
    return callback(new Error("请输入正确的手机号码"));
}

/**
 * 检查身份证格式
 * @param rule
 * @param value
 * @param callback
 * @returns {*}
 */
export function checkIdCard(rule, value, callback) {
    if (!value) {
        return callback();
    }

    if (idCard.test(value)) {
        return callback();
    }
    return callback(new Error("请输入正确的手机号码"));
}

/**
 * 检查url格式
 * @param rule
 * @param value
 * @param callback
 * @returns {*}
 */
export function checkUrl(rule, value, callback) {
    if (!value) {
        return callback();
    }

    const pattern = new RegExp(url, "g");
    if (pattern.test(value)) {
        return callback();
    }
    return callback(new Error("请输入正确URL格式"));
}

/**
 * 检查referer
 * @param rule
 * @param value
 * @param callback
 * @returns {*}
 */
export function checkReferer(rule, value, callback) {
    const pattern = regUrl;
    if (pattern.test(value)) {
        return callback();
    }
    return callback(new Error("请填写正确的referer"));
}

/**
 * 检查domain
 * @param rule
 * @param value
 * @param callback
 * @returns {*}
 */
export function checkDomain(rule, value, callback) {
    const pattern = new RegExp(domain, "g");
    if (pattern.test(value)) {
        return callback();
    }

    return callback(new Error("请填写正确的域名"));
}

/**
 * 检验银行卡
 * @param rule
 * @param value
 * @param callback
 * @returns {*}
 */
export function checkBankCard(rule, value, callback) {
    if (!value) {
        return callback();
    }

    if (regBankCard.test(value)) {
        return callback();
    }

    return callback(new Error("请填写正确的银行卡格式"));
}

/**
 * 检查ip格式
 * @param rule
 * @param value
 * @param callback
 * @returns {*}
 */
export function checkIp(rule, value, callback) {
    const pattern = new RegExp(ip, "g");
    if (pattern.test(value)) {
        return callback();
    }

    return callback(new Error("请填写正确的IP"));
}

/**
 * 检查域名和泛域名
 * @param {*} rule
 * @param {*} value
 * @param {*} callback
 * @returns
 */
export function checkGenericDomain(rule, value, callback) {
    if (!value) {
        return callback();
    }
    if (value) {
        const dommainPattern = new RegExp(domain, "g");
        const genericDomainPattern = new RegExp(genericDomain, "g");
        if (dommainPattern.test(value) || genericDomainPattern.test(value)) {
            return callback();
        }

        return callback(new Error("请填写正确的域名"));
    }
}

/**
 * 检验端口号
 * @param rule
 * @param value
 * @param callback
 * @returns {*}
 */
export function checkSinglePort(rule, value, callback) {
    if (!value && !rule.required) {
        return callback();
    }

    if (!value && rule.required) {
        return callback(new Error("请填写端口"));
    }

    const pattern = /^([0-9]|[1-9]\d|[1-9]\d{2}|[1-9]\d{3}|[1-5]\d{4}|6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5])$/g

    return pattern.test(value) ? callback() : callback(new Error("端口输入有误"));
}

/**
 * 连续告警次数配置检验
 */
export function checkAlarmTimesSetting(rule, value, callback) {
    if (!value) {
        return callback(new Error("请输入连续告警次数"));
    }

    if (isNaN(Number(value))) {
        return callback(new Error("请输入数字"));
    }

    if (Number(value) <= 0 || Number(value) > 100) {
        return callback(new Error("请输入1-100的整数"));
    }

    const num = /^[1-9]{1}[0-9]*$/;
    if (!num.test(value)) {
        return callback(new Error("只能输入正整数"));
    }

    return callback();
}

/**
 * 校验回源host的格式 domain/ip/domain:port/ip:port
 * @param {string} host
 * @returns {boolean}
 */
export function checkReqHost(host) {
    const domainPattern = /^(\*\.|\*)?([a-z0-9]([a-z0-9-_]{0,61}[a-z0-9])?\.)+[a-z]{2,6}$/;
    const domainAndPortPattern = `^(${domainPattern.source.replace(/\^|\$/g, "")})(:${port})?$`;
    const ipAndPortPattern = genericIp; // ipv4, ipv6, ipv4+端口
    const ipv6AndPortPattern = `^\\[(${ipv6Pattern.source.replace(/\^|\$/g, "")})](:${port})$` // ipv6+端口

    return [domainPattern, domainAndPortPattern, ipAndPortPattern, ipv6AndPortPattern]
        .map(reg => new RegExp(reg).test(host))
        .some(Boolean);
}

/**
 * 指定源站回源host的格式 domain/ip
 * @param {string} host
 * @returns {boolean}
 */
export function checkOriginHost(host) {
    const domainPattern = /^(\*\.|\*)?([a-z0-9]([a-z0-9-_]{0,61}[a-z0-9])?\.)+[a-z]{2,6}$/;
    const ipPattern = `^(${ip.replace(/\^|\$/g, "")})$`;; // ipv4, ipv6, ipv4+端口

    return [domainPattern, ipPattern]
        .map(reg => new RegExp(reg).test(host))
        .some(Boolean);
}
