<template>
    <div class="container">
        <el-radio-group v-model="currentPeriodLocal" :size="size">
            <el-radio-button
                v-for="period in periodHourOptions"
                :key="period"
                :label="period"
                v-show="period !== '-1' || currentPeriodLocal !== '-1'"
            >
                <!-- 自定义的 radio 表现比较特殊 -->
                {{
                    periodLabelMap[period]
                        ? periodLabelMap[period]
                        : $t("common.ctHourPicker.label3", { number: period })
                }}
                <i class="el-icon-date" v-if="period === '-1'" />
            </el-radio-button>
        </el-radio-group>
        <el-date-picker
            v-if="currentPeriodLocal === '-1'"
            :size="size"
            v-model="timeRangeLocal"
            :type="type"
            @blur="pickerOptions.reset()"
            :default-time="['00:00:00', new Date().toLocaleTimeString()]"
            :start-placeholder="$t('common.datePicker.start')"
            :end-placeholder="$t('common.datePicker.end')"
            :picker-options="pickerOptions"
        />
    </div>
</template>

<script lang="ts">
import { Component, Prop, Watch, Vue } from "vue-property-decorator";

import { DatePickerOptions } from "element-ui/types/date-picker";

import { getAm0 as get0 } from "../../utils";
import { inRange } from "lodash-es";

interface MyDatePickerOptions extends DatePickerOptions {
    reset(): void;
}

@Component({
    name: "CtHourPicker",
    model: {
        prop: "hourRange",
        event: "select",
    },
})
export default class CtHourPicker extends Vue {
    /**
     *
     * -1：自定义，1：近1小时，2：近2小时，X：近X小时
     */
    @Prop({ default: () => ["1", "2", "3", "-1"], type: Array }) private periodHourOptions!: string[];
    // 默认选择的时间段，如果不在 periodHourOptions 范围内则无效
    @Prop({ default: "1", type: String }) private currentPeriod!: string;
    @Prop({ default: 365, type: Number }) private maxDayBeforeNow!: number;
    @Prop({ default: 31 * 24 * 60 * 60 * 1000, type: Number }) private maxTimeRangeLength!: number;
    @Prop({ default: "small", type: String }) private size!: string;
    @Prop({ default: () => [], type: Array }) private hourRange!: [Date?, Date?];
    @Prop({ default: "datetimerange", type: String }) private type!: string;

    private periodLabelMap: { [key: string]: string } = {
        1: `${this.$t("common.ctHourPicker.label1")}`,
        2: `${this.$t("common.ctHourPicker.label2")}`,
        3: `${this.$t("common.ctHourPicker.label3")}`,
        "-1": `${this.$t("common.ctHourPicker.label4")}`,
    };
    private currentPeriodLocal = ""; // 当前选中的快捷选项
    private repeatInterval: number | null = null;
    private minDate: null | number = null;
    private timeRangeLocal?: null | [Date?, Date?] = null;
    private pickerOptions: MyDatePickerOptions = {
        // 设置禁用时间
        disabledDate: (time: Date) => {
            // 获取今天0点数据
            const today0 = get0(new Date());
            // 禁用大于今天时间23：59：59
            if (+time > +new Date(+today0 + 24 * 60 * 60 * 1000 - 1)) return true;

            // 禁用小于设置的最早期限的，由于今天会算1天，需要倒推 x-1 天
            if (+time < +today0 - (this.maxDayBeforeNow - 1) * 24 * 60 * 60 * 1000) return true;
            //当最小时间为null的时候则关闭禁用，点清空时会变为0，所以0也要判断
            if (this.minDate === null || this.minDate === 0) return false;

            // 由于现在选择时间后，默认会调整到当日23:59:59，跨度30天即为31天，需要减掉 1s
            const oneMonth = this.maxTimeRangeLength - 1000;
            // 超出当前选择时间前后31天的不可选择，time为选择框中出现的日期

            return +time > this.minDate + oneMonth || +time < this.minDate - oneMonth;
        },
        onPick: ({ minDate }) => {
            this.minDate = +minDate;
        },
        reset: () => {
            if (!this.hourRange || this.hourRange.length === 0) this.minDate = null;
        },
    };

    private created() {
        // 处理默认选中，如果 入参 存在在 options 则使用，否则取默认
        if (this.currentPeriod && this.periodHourOptions.includes(this.currentPeriod)) {
            this.currentPeriodLocal = this.currentPeriod;
        } else {
            this.currentPeriodLocal = this.periodHourOptions[0];
        }
    }

    @Watch("currentPeriod")
    private onCurrentPeriodChange(period: string) {
        if (period && period !== this.currentPeriodLocal && this.periodHourOptions.includes(period)) {
            this.currentPeriodLocal = period;
        }
    }

    @Watch("currentPeriodLocal")
    private onCurrentPeriodLocalChange(period: string) {
        // 特定情况下需要获取当前的快捷选项，外面使用 .sync 更新
        if (this.currentPeriod !== period) this.$emit("update:currentPeriod", period);

        if (period === "-1") return; // 自定义不作任何处理，保留快捷的选择结果

        const currentTime = new Date();
        const hourToTimestap = 1 * 60 * 60 * 1000;
        // const today0 = get0(new Date());
        // const today24 = new Date(+today0 + 24 * 60 * 60 * 1000 - 1000);
        let start, end;
        switch (period) {
            case "1": // 近1小时
                start = new Date(new Date().getTime() - hourToTimestap);
                end = currentTime;
                break;
            case "2": // 近2小时
                start = new Date(new Date().getTime() - 2 * hourToTimestap);
                end = currentTime;
                break;
            case "3": // 近3小时
                start = new Date(new Date().getTime() - 3 * hourToTimestap);
                end = currentTime;
                break;
            default: {
                // 默认：近1小时
                start = new Date(new Date().getTime() - hourToTimestap);
                end = new Date();
            }
        }

        this.timeRangeLocal = [start, end];
    }
    @Watch("timeRangeLocal")
    private onTimeRangeLocalChange(val: any) {
        const todayNow = new Date();

        if (val?.[1] > todayNow) {
            val[1] = todayNow;
        }
        if (val?.[0] > todayNow) {
            // 如果开始时间超过当前时间，则默认选择的开始时间为当前时间的前1小时
            val[0] = new Date(+val[1] - 1 * 3600 * 1000);
        }

        this.$emit("select", val);
    }
    /**
     * 1分钟粒度，30s 刷新一次
     * 避免用户在选择时间范围后，过了很久再点击查询，但是结束时间还是一开始选中时设置的结束时间
     */
    private repeatUpdateTimeRange() {
        this.repeatInterval = window.setInterval(() => {
            if (this.currentPeriod === "-1") {
                return;
            }

            const todayNow = new Date();
            const endTimeIsInRange = inRange(this.hourRange[1], +todayNow - 1000 * 60, todayNow);

            // 同步调整起始时间
            const startTime = new Date(+todayNow - 1000 * 3600 * +this.currentPeriod);

            if (endTimeIsInRange)
                this.$emit("select", [startTime, todayNow]);
        }, 1000 * 30);
    }

    mounted() {
        this.repeatUpdateTimeRange();
    }

    beforeDestroy() {
        clearInterval(this.repeatInterval as number);
    }
}
</script>

<style lang="scss" scoped>
.container {
    display: inline-block;
}

.el-date-editor {
    margin-left: 8px;
    vertical-align: middle;
}
.search-bar .el-range-editor {
    margin: 0 10px 0 0;
}
</style>
