<template>
    <div class="bs-header">
        <div class="bs-header-left">
            <div class="bs-header-left__tyy"></div>
            <div class="bs-header-left__divider"></div>
            <div class="bs-header-left__ypt"></div>
        </div>
        <div class="bs-header-right">
            <div class="bs-header-right__name">{{ userInfo.userName || userInfo.name }}</div>
            <el-button type="text" class="bs-header-right__logout" @click="logout">注销</el-button>
        </div>
    </div>
</template>
<script>
import { Common } from "@/config/url";
import <PERSON><PERSON> from "js-cookie";
export default {
    name: "bsHeader",
    computed: {
        userInfo() {
            return this.$store.state.user.userInfo;
        },
    },
    methods: {
        logout() {
            this.$ctFetch(Common.cancellation).then(() => {
                // 注销后跳转到登录页
                const parentUrl = Cookie.get("referer");
                window.location.href = parentUrl || "https://www.ctyun.cn/h5/auth/";
            });
        },
    },
};
</script>
<style lang="scss" scoped>
.bs-header {
    width: 100%;
    height: 50px;
    line-height: 50px;
    color: #a8adaf;
    font-weight: 400;
    display: flex;
    justify-content: space-between;
    background-color: #424550;
    &-left {
        width: 250px;
        height: 100%;
        padding-left: 20px;
        display: flex;
        justify-content: space-between;
        &__tyy {
            width: 106px;
            height: 100%;
            background-position: 50%;
            background-repeat: no-repeat;
            background-size: 99px 28px;
            background-image: url(../../assets/images/天翼云.png);
        }
        &__divider {
            border-right: 1px solid hsla(0, 0%, 96.1%, 0.4);
            height: 20px;
            margin-top: 15px;
        }
        &__ypt {
            width: 106px;
            height: 100%;
            background-position: 50%;
            background-repeat: no-repeat;
            background-size: 99px 28px;
            background-image: url(../../assets/images/CDN云平台.png);
        }
    }
    &-right {
        display: flex;
        padding-right: 20px;
        &__name {
            padding-right: 20px;
        }
    }
}
</style>
