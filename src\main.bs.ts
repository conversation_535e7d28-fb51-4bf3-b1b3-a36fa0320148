import Vue from "vue";
import { sync } from "vuex-router-sync";
import { render } from "@/microApp/lifeCycle";
import CuteComponent from "@cutedesign/ui";

import store from "@/store";
import router, { utils as routerUtils } from "@/router";
import utils, { redirectEntryUrl } from "./utils";
import { errorHandler } from "./utils/ctFetch/errorHandler";
import ctFetch from "@/utils/https.js";
import CtBreadcrumb from "@/components/CtBreadcrumb-mod.vue";
import { ScreenModule } from "@/store/modules/screen";

import dayjs from "dayjs";
import xss from "xss";
import "@/assets/iconfont/iconfont.css";
import "@/assets/fonts/iconfont.css";
import "@/icons";
import * as filters from "@/filters";

// 全局组件;
import "@cdnplus/common/components/index";
import "./components/index";
Vue.component("CtBreadcrumb", CtBreadcrumb);

// 静态资源
import "@/assets/css/common.scss";
import "@cdnplus/common/assets/icon/iconfont.css";
import "@cutedesign/ui/lib/index.css";

import * as echarts from "echarts";
import urlTransformer from "./utils/logic/url";

Vue.prototype.$echarts = echarts;
// 增加bus通信
Vue.prototype.$bus = new Vue();
// 增加时间工具
Vue.prototype.$dayjs = dayjs;
// 放置xss攻击工具
Vue.prototype.xss = xss;
// 链接转换
Vue.prototype.$urlTransformer = urlTransformer;
Vue.prototype.$PoweredByQiankun = window.__POWERED_BY_QIANKUN__

class Main {
    constructor() {
        redirectEntryUrl();
        this.init();
    }

    init() {
        console.log('bs--inint');
        // 初始化配置
        this.initialCfg();
        // 注册 vue 插件之列的
        this.registerVue();
        //初始化Vue
        this.instanceVue();
        // 其他事件
        this.otherEvents();
    }

    //初始化配置
    initialCfg() {
        Vue.config.productionTip = false;

        // vuex-router同步
        sync(store, router);

        const plugins = {
            // 事件总线
            $ctBus: new Vue(),
            // 数据请求
            $ctFetch: ctFetch,
            // 数据异常处理
            $errorHandler: errorHandler,
            // 工具链
            $ctUtil: {
                ...utils,
                ...routerUtils,
            },
        };
        // 增加到原型链
        Object.assign(Vue.prototype, plugins);
    }

    //初始化Vue
    async instanceVue() {
        // 实例化vue-非qiankun
        if (!(window as any).__POWERED_BY_QIANKUN__) {
            // bs无需加载layout
            render({});
        }
    }

    // vue 相关注册

    registerVue() {
        Vue.use(CuteComponent);

        // 注册全局过滤器
        Object.keys(filters).forEach(key => {
            Vue.filter(key, (filters as { [key: string]: Function })[key]);
        });
    }

    // 其他
    otherEvents() {
        const resizeFn = () => {
            const screenWidth = document.documentElement.clientWidth;
            ScreenModule.SET_SCREEN_SIZE(screenWidth);
        };
        resizeFn();
        window.addEventListener(
            "resize",
            utils.debounce(resizeFn, {
                delay: 100,
            })
        );
    }
}

new Main();
