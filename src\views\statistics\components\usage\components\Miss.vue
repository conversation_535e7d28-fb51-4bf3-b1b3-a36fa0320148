<template>
    <div class="chart-wrap">
        <div class="title-wrap">
            <div class="operate-btn">
                <el-radio-group v-model="chartType" size="small">
                    <el-radio-button :label="$t('statistics.usageQuery.miss.radioBtn1')"></el-radio-button>
                    <el-radio-button :label="$t('statistics.usageQuery.miss.radioBtn2')"></el-radio-button>
                </el-radio-group>
            </div>
            <template v-if="showBandwidth">
                <div class="total">
                    <div class="tip">
                        {{ $t("statistics.common.vchartTip1", { type: "", num: "" }) }}：
                        <span class="num">
                            {{ fetchData.topBandwidth | convertBandwidthB2P(scale) }}
                        </span>
                        <span class="date">{{ fetchData.topBandwidthTime | timeFormat }}</span>
                    </div>
                    <div class="tip">
                        {{ $t("statistics.common.vchartTip2", { type: "", num: "" }) }}：
                        <span class="num">
                            {{ fetchData.top95Bandwidth | convertBandwidthB2P(scale) }}
                        </span>
                    </div>
                </div>
            </template>
            <template v-else>
                <div class="total">
                    <div class="tip">
                        {{ $t("statistics.common.vchartTip3", { type: "", num: "" }) }}：
                        <span class="num">{{ fetchData.totalFlow | convertFlowB2P(scale) }}</span>
                    </div>
                </div>
            </template>
        </div>
        <v-chart class="chart" v-loading="loading" :element-loading-text="$t('statistics.common.chart.loading')"
            autoresize theme="cdn" :options="options" />
        <ct-tip>
            {{ $t("statistics.dcdn.missWhole.tableTip") }}
            <el-tooltip effect="dark" :content="$t('statistics.common.searchDownloadContent')" placement="right">
                <i class="el-icon-download usage-download-icon" @click="downloadTable"></i>
            </el-tooltip>
        </ct-tip>

        <el-table :data="fetchData.daily" v-loading="loading"
            :element-loading-text="$t('statistics.common.table.loading')" :empty-text="$t('common.table.empty')">
            <el-table-column :label="$t('statistics.dcdn.missWhole.tableColumn1')" prop="date"
                :sortable="true"></el-table-column>
            <el-table-column :label="$t('statistics.dcdn.missWhole.tableColumn2', { unit: indentFlowConfig.unit })">
                <template slot-scope="{ row }">
                    {{ (row.flow / indentFlowConfig.scale).toFixed(2) }}
                </template>
            </el-table-column>
            <el-table-column :label="$t('statistics.dcdn.missWhole.tableColumn3', { unit: indentMissBandwidthConfig.unit })
                ">
                <template slot-scope="{ row }">
                    {{ (row.topBandwidth / indentMissBandwidthConfig.scale).toFixed(2) }}
                </template>
            </el-table-column>
            <el-table-column :label="$t('statistics.dcdn.missWhole.tableColumn4')">
                <template slot-scope="{ row }">
                    {{ row.topTime | timeFormat }}
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<script lang="ts">
import { Component } from "vue-property-decorator";
import { mixins } from "vue-class-component";
import { StatisticsUsageUrl } from "@/config/url/statistics";
import { timeFormat } from "@/filters/index";
import { SearchParams } from "@/types/statistics/usage";
import { MissFetchData } from "@/types/statistics/usage";
import { THEME_AREA_STYLE } from "@/config/echart/cdn-theme";
import ChartMixin from "../chartMixin";
import { cloneDeep } from "lodash-es";

type tooltipParam = { name: string; marker: string; value: string | number };

const defaultFetchData: MissFetchData = {
    "5min": [],
    daily: [],
    topBandwidth: 0,
    avgQueryFlow: 0,
    totalFlow: 0,
    topBandwidthTime: 0,
    top95Bandwidth: 0,
    avgMissFlow: 0,
};

@Component({
    name: "Miss",
})
export default class Miss extends mixins(ChartMixin) {
    chartType = `${this.$t("statistics.usageQuery.miss.chartType")}`; //
    // 接口数据
    fetchData: MissFetchData = cloneDeep(defaultFetchData);

    protected downloadDataList: MissFetchData["5min"] = []; // 用于下载的数据

    get scale() {
        return 1000;
    }

    // 当前展示是否为带宽（流量）
    get showBandwidth() {
        return this.chartType === `${this.$t("statistics.usageQuery.miss.chartType")}`;
    }

    // 获取 seriesData 所使用的 key
    get seriesDataKey() {
        return this.showBandwidth ? "bandwidth" : "flow";
    }
    private async localFetchGenerator<T>(url: string, params: SearchParams) {
        const rst = await this.fetchGenerator<T>(url, params);
        return rst;
    }
    // 1、数据请求
    protected async getData(params: SearchParams) {
        this.fetchData = await this.localFetchGenerator(StatisticsUsageUrl.queryMissList, {
            ...params,
        });

        if (!this.fetchData) this.fetchData = cloneDeep(defaultFetchData);

        // 处理用于下载的数据
        this.downloadDataList = this.fetchData["5min"];
    }

    // 获取各种缩进计算的配置（根据最大值获取）
    get indentMissBandwidthConfig() {
        const max = this.getMaxFromList(this.fetchData.daily, "topBandwidth");
        return this.getBandwidthUnitConfig(max, true);
    }
    get indentFlowConfig() {
        const max = this.getMaxFromList(this.fetchData.daily, "flow");
        return this.getFlowUnitConfig(max, true);
    }

    get chartMissBandwidthConfig() {
        const max = this.getMaxFromList(this.fetchData["5min"], "bandwidth");
        return this.getBandwidthUnitConfig(max, true);
    }

    get chartMissFlowConfig() {
        const max = this.getMaxFromList(this.fetchData["5min"], "flow");
        return this.getFlowUnitConfig(max, true);
    }

    // 2、数据处理
    get options() {
        const { seriesDataKey } = this;
        // 根据 switch 获取差异化数据
        const title = this.showBandwidth
            ? `${this.$t("statistics.usageQuery.miss.chartOptions.title1")}`
            : `${this.$t("statistics.usageQuery.miss.chartOptions.title2")}`;

        const { unit, scale } = this.showBandwidth ? this.chartMissBandwidthConfig : this.chartMissFlowConfig;
        const yAxisName = `${this.$t("statistics.usageQuery.miss.chartOptions.yAxisName", { unit: unit })}`;

        const xAxisData: string[] = [];
        const seriesData: string[] = [];
        const fetchDataList = this.fetchData["5min"] || [];
        fetchDataList
            .sort((a, b) => a.bandwidthTime - b.bandwidthTime)
            .forEach(item => {
                xAxisData.push(timeFormat(item.bandwidthTime * 1000).replace(" ", "\n"));
                seriesData.push(((item[seriesDataKey] || 0) / scale).toFixed(2));
            });

        const options = {
            ...this.comOptions,
            tooltip: {
                trigger: "axis",
                formatter: (a: tooltipParam[]) =>
                    `${a[0].name}<br>${a[0].marker}${title}: ${a[0].value}${unit}`,
            },
            xAxis: {
                type: "category", // 类目轴，从 data 中获取数据
                boundaryGap: false, // 坐标轴两边留白策略
                axisLabel: {
                    formatter: this.xAxisFormatterGenerator(),
                },
                data: xAxisData,
            },
            yAxis: {
                type: "value", // 数值轴
                name: yAxisName,
            },
            series: [
                {
                    type: "line",
                    data: seriesData,
                    areaStyle: THEME_AREA_STYLE["#358AE2"],
                },
            ],
        };

        return options;
    }

    // 重写 excel 数据拼接方法
    protected tableToExcel() {
        let str = `${this.$t("statistics.usageQuery.miss.tableToExcel.excelColumn1")},${this.showBandwidth
            ? `${this.$t("statistics.usageQuery.miss.tableToExcel.excelColumn2", { mbps: this.Mbps })}`
            : `${this.$t("statistics.usageQuery.miss.tableToExcel.excelColumn3", { mb: this.MB })}`
            }\n`;

        // 输出格式
        str += this.downloadDataList.reduce((str, item) => {
            str += `${timeFormat(item["bandwidthTime"] * 1000)},`;
            str += (item[this.seriesDataKey] / Math.pow(this.scale, 2)).toFixed(2) + "\n";
            return str;
        }, "");

        // 增加峰值带宽和95峰值带宽
        str += `\n${this.$t("statistics.usageQuery.miss.tableToExcel.excelColumn4")}\n`;

        const { fetchData: _fetchData } = this;
        const fetchData = this.processMissFetchData(_fetchData);
        const { topBandwidth, totalFlow, avgMissFlow } = fetchData;

        if (this.showBandwidth) {
            str += `${this.$t("statistics.usageQuery.miss.tableToExcel.excelColumn5")},${topBandwidth} ${this.Mbps
                } \n`;
        } else {
            str += `${this.$t("statistics.usageQuery.miss.tableToExcel.excelColumn6")},${totalFlow} ${this.MB
                } \n ${this.$t("statistics.usageQuery.miss.tableToExcel.excelColumn7")},${avgMissFlow} ${this.MB
                } \n`;
        }

        this.downloadExcel({
            name: `${this.$t("statistics.usageQuery.miss.tableToExcel.excelName", {
                chartType: this.chartType,
            })}`,
            str,
        });
    }

    protected downloadTable() {
        if (this.checkListIsEmpty(this.fetchData?.daily)) return;

        let str = `${this.$t("statistics.usageQuery.BandwidthFlow.tableColumn1")},${this.$t('statistics.dcdn.missWhole.tableColumn2', { unit: this.MB })},${this.$t('statistics.dcdn.missWhole.tableColumn3', { unit: this.Mbps })},${this.$t('statistics.dcdn.missWhole.tableColumn4')}\n`;

        this.fetchData.daily.forEach((item => {
            str += item.date + ",";
            str += (+item.flow / Math.pow(this.scale, 2)).toFixed(2) + ",";
            str += (+item.topBandwidth / Math.pow(this.scale, 2)).toFixed(2) + ",";
            str += timeFormat(item.topTime) + ",\n";
        }))

        this.downloadExcel({
            name: `${this.$t("statistics.common.tab[1]")}-${this.$t("statistics.dcdn.missWhole.tableTip")}`,
            str
        })
    }
}
</script>

<style lang="scss" scoped></style>
