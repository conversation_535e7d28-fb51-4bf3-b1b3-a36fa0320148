@charset "UTF-8";
@font-face {
  font-family: cute-icons-2; /* Project id 4094550 */
  src: url('./iconfont.woff2') format('woff2'),
       url('./iconfont.woff') format('woff'),
       url('./iconfont.ttf') format('truetype');
}

/* [class*=' cute-icon-'],
[class^='cute-icon-'] {
  font-family: 'cute-icons-2' !important;
  speak: none;
  font-style: normal;
  font-weight: 400;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  vertical-align: baseline;
  display: inline-block;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.cute-icons-2 {
  font-family: 'cute-icons-2' !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
} */
