<template>
    <div class="url-auth-wrapper">
        <el-form
            ref="urlAuthForm"
            label-width="140px"
            label-position="left"
            :model="form"
            :rules="rules"
            :disabled="!isEdit || !isService || urlAuthFormDisabled || isLockUrlAuth"
        >
            <div>
              <el-form-item
                :label="$t('domain.detail.label17')"
                prop="url_auth.switch"
            >
                <span slot="label"
                    >{{ $t("domain.detail.label17") }}
                    <span>
                        <el-tooltip placement="top">
                            <div slot="content">
                                {{ $t("domain.editPage.tip10") }}
                                <a
                                    :underline="false"
                                    class="word-wrap aocdn-ignore-link"
                                    style="color:#3d73f5"
                                    @click="$docHelp(learnLink)"
                                    >{{ $t("domain.detail.tip23") }}
                                </a>
                            </div>
                            <ct-svg-icon
                                icon-class="question-circle"
                                class-name="ct-sort-drag-icon"
                            ></ct-svg-icon></el-tooltip></span
                ></span>
                <el-switch
                    v-model="form.url_auth.switch"
                    :active-value="true"
                    :inactive-value="false"
                    @change="url_auth_switch_change"
                ></el-switch>
              </el-form-item>

              <div v-if="form.url_auth.switch" class="switch-wrapper">
                <el-form-item :label="$t('domain.detail.label55')" prop="type">
                    <el-radio-group v-model="form.url_auth.type" @change="oAuthChange">
                        <el-radio :label="1">
                            {{ $t("domain.detail.authType[0]") }}
                        </el-radio>
                        <el-radio :label="2">
                            {{ $t("domain.detail.authType[1]") }}
                        </el-radio>
                        <el-radio :label="3">
                            {{ $t("domain.detail.authType[2]") }}
                        </el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item
                    :label="$t('domain.detail.label56')"
                    prop="url_auth.key"
                    :rules="rules.url_auth_key"
                >
                    <el-input
                        v-model.trim="form.url_auth.key"
                        :placeholder="$t('domain.detail.placeholder23')"
                        class="input-wrapper-url-auth"
                        @change="handleChange"
                    ></el-input>
                </el-form-item>
                <el-form-item :label="$t('domain.detail.label57')" prop="delim_char">
                    <el-input
                        v-model.trim="form.url_auth.delim_char"
                        :placeholder="$t('domain.detail.placeholder28')"
                        class="input-wrapper-url-auth"
                        @change="handleChange"
                    ></el-input>
                </el-form-item>
                <el-form-item
                    v-if="form.url_auth.type === 3"
                    :label="$t('domain.detail.label58')"
                    prop="url_auth.timestamp_key"
                    :rules="rules.timestamp_key"
                >
                    <el-input
                        v-model.trim="form.url_auth.timestamp_key"
                        class="input-wrapper-url-auth"
                        @change="handleChange"
                    ></el-input>
                </el-form-item>
                <el-form-item
                    v-if="form.url_auth.type === 1 || form.url_auth.type === 3"
                    :label="
                        form.url_auth.type === 1
                            ? $t('domain.detail.label59')
                            : $t('domain.detail.label60')
                    "
                    prop="url_auth.auth_key"
                    :rules="rules.auth_key"
                >
                    <el-input
                        v-model.trim="form.url_auth.auth_key"
                        class="input-wrapper-url-auth"
                        @change="handleChange"
                    ></el-input>
                </el-form-item>
                <el-form-item
                    :label="$t('domain.detail.label61')"
                    prop="url_auth.time_limit"
                    :rules="rules.time_limit"
                >
                    <el-input
                        v-model.number="form.url_auth.time_limit"
                        maxlength="16"
                        class="input-wrapper-url-auth"
                        @change="handleChange"
                    >
                        <template slot="append">{{ $t("simpleForm.alogicCacheMixin.CacheTtlMap.5") }}</template>
                    </el-input>
                </el-form-item>
              </div>
            </div>

        </el-form>
    </div>
</template>

<script>
import componentMixin from "@/views/domainConfig/componentMixin";
import ctSvgIcon from "@/components/ctSvgIcon";
import { cloneDeep } from "lodash-es";
import { nUserModule } from "@/store/modules/nuser";
import urlTransformer from "@/utils/logic/url";
import { SecurityAbilityModule } from "@/store/modules/securityAbility";

export default {
    name: "urlAuth",
    components: { ctSvgIcon },
    mixins: [componentMixin],
    props: {
        datas: Object,
        urlAuthFormDisabled: Boolean,
        isLockUrlAuth: Boolean,
    },
    data() {
        return {
            form: {
                // URL鉴权
                url_auth: {
                    switch: false,
                },
            },
            // learnLink: "https://www.ctyun.cn/document/10065985/10339350",
            rules: {
                // URL鉴权
                url_auth_key: [{ required: true, trigger: "blur", validator: this.valid_key }],
                auth_key: [
                        // { required: true, trigger: "blur", message: this.$t("domain.detail.placeholder24") },
                        { required: true, validator: this.valid_auth_key, trigger: "blur" },
                    ],
                time_limit: [{ required: true, type: "number", trigger: "blur", validator: this.valid_time }],
                timestamp_key: [
                        // { required: true, trigger: "blur", message: this.$t("domain.detail.placeholder25") },
                        { required: true, validator: this.valid_timestamp_key, trigger: "blur" },
                    ],
            },
        };
    },
    computed: {
        isCtclouds() {
            return nUserModule.isCtclouds;
        },
        learnLink() {
            return urlTransformer(
                {
                    fcdnCtcloud: "https://www.esurfingcloud.com/document/zh-cn/10015932/20688157",
                    fcdnCtyun: "https://www.ctyun.cn/document/10015932/10639830",
                    a1Ctyun: "https://www.ctyun.cn/document/10065985/10339350",
                    a1Ctcloud: "https://www.esurfingcloud.com/document/zh-cn/20689737/20689818"
                }
            )
        },
    },
    watch: {
        datas: {
            deep: true,
            handler(val) {
                this.init(val);
            },
            immediate: true,
        },
    },
    methods: {
        init(val) {
            if (!val) return
            this.form.url_auth = cloneDeep(val?.url_auth);
        },
        handleChange() {
            this.$emit("onChange", this.form.url_auth);
        },
        // URL鉴权开关
        url_auth_switch_change(v) {
            this.form.url_auth = {
                type: 1, // 鉴权类型 1鉴权A，2鉴权B，3鉴权C，0未知类型
                key: "",
                timestamp_key: "timestamp",
                auth_key: "auth_key",
                time_limit: null, // 鉴权URL有效时长
                delim_char: "",
                switch: !!v,
            };

            const originalForm = SecurityAbilityModule.securityBasicConfigOriginForm;
            if (v && originalForm?.url_auth?.switch) {
                this.form.url_auth = cloneDeep(originalForm?.url_auth);
            }

            if (!v && !originalForm?.url_auth?.switch) {
                this.form.url_auth = cloneDeep(originalForm?.url_auth);
            }

            this.$emit("onChange", this.form.url_auth);
        },
        // 根据不同 type 设置初始值
        oAuthChange(v) {
            this.form.url_auth.key = "";
            this.form.url_auth.delim_char = v === 1 ? "-" : "";
            this.form.url_auth.time_limit = null;

            if (v === 1) {
                this.form.url_auth.auth_key = "auth_key";
            } else if (v === 3) {
                this.form.url_auth.timestamp_key = "timestamp";
                this.form.url_auth.auth_key = "auth_key";
            }
            this.$emit("onChange", this.form.url_auth);
        },
        // URL鉴权校验-方法
        valid_key(rule, value, callback) {
            if (this.urlAuthFormDisabled || this.isLockUrlAuth) return callback();
            if (this.form.type === 0) {
                return callback();
            }
            const keys = value?.split(",");
            let errNum = 0;
            keys.forEach(k => {
                if (
                    k.length < 1 ||
                    k.length > 128 ||
                    k.indexOf("$") > -1 ||
                    k.indexOf("、") > -1 ||
                    k.indexOf("，") > -1 ||
                    k.indexOf("；") > -1 ||
                    k.indexOf(";") > -1 ||
                    k.indexOf(".") > -1
                )
                    errNum++;
            });
            if (!value) {
                return callback(new Error(this.$t("domain.detail.tip51")));
            }
            // else if (keys.length > 3) {
            //     return callback(new Error(this.$t("domain.detail.tip52")));
            // }
            else if (errNum > 0) {
                return callback(new Error(this.$t("domain.detail.tip53")));
            } else {
                return callback();
            }
        },
        valid_auth_key(rule, value, callback) {
            if (this.urlAuthFormDisabled || this.isLockUrlAuth) callback();
            if (!value) {
                return callback(new Error(this.$t("domain.detail.placeholder24")));
            } else {
                callback();
            }
        },
        valid_time(rule, value, callback) {
            const num = /^[0-9]*$/;
            if (this.urlAuthFormDisabled || this.isLockUrlAuth) return callback();
            if (this.form.type === 0) {
                return callback();
            }
            if (
                (Number(value) < 1 || Number(value) > 99999999 || !num.test(value))
            ) {
                return callback(new Error(this.$t("domain.detail.tip54")));
            } else {
                return callback();
            }
        },
        valid_timestamp_key(rule, value, callback) {
            if (this.urlAuthFormDisabled || this.isLockUrlAuth) callback();
            if (!value) {
                return callback(new Error(this.$t("domain.detail.placeholder25")));
            } else {
                callback();
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.dynamic-box {
    .tips {
        font-size: 12px;
        color: $color-neutral-7;
        font-weight: 400;
        margin-left: $margin-3x;
    }
}
.websocket-timeout-input {
    width: 395px;
}
.input-wrapper-url-auth {
    width: calc(100% - 120px);
}
</style>
