/*
 * @Description: 统计分析相关 map 映射配置
 * @Author: wang <PERSON><PERSON>
 */

// 请求数命名转换
export const requestType: Readonly<{
    [key: string]: string;
}> = {
    all: "common.requestType[0]", // 总请求数
    httpStatic: "common.requestType[1]", // 静态http请求数
    httpDynamic: "common.requestType[2]", // 动态http请求数
    httpsStatic: "common.requestType[3]", // 静态https请求数
    httpsDynamic: "common.requestType[4]", // 动态https请求数
    // websocket: "websocket请求数",  // 仅 全站加速-websocket加速使用到
};

// QPS命名转换
export const qpsType: Readonly<{
    [key: string]: string;
}> = {
    allQPS: "common.qpsType[0]", // 总QPS
    httpsDynamicQPS: "common.qpsType[1]", // 动态https QPS
    httpsStaticQPS: "common.qpsType[2]", // 静态https QPS
    httpDynamicQPS: "common.qpsType[3]", // 动态http QPS
    httpStaticQPS: "common.qpsType[4]", // 静态http QPS
    // websocketQPS: "websocket QPS",  // 仅 全站加速-websocket加速使用到
};

// 全站加速的一个参数，不传表示总，0表示http，2表示websocket，1表示上传
// 使用意义1：当选择上传加速时，需要传递 [1]
// 使用意义2：带宽流量中需要细分数据统计，其他 tab 不需要细分（也就是不需要传递）
export const busiType: Readonly<{
    [key: string]: number;
}> = {
    "006": 0, //全站加速
    "104": 1, //上传加速
    "105": 2, //websocket加速
};
