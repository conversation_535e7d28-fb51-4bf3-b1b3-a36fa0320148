<template>
    <div class="input-wrapper">
        <el-input
            class="domain-input"
            v-model="localModel"
            :placeholder="$t('simpleForm.domainInput.placeholder')"
            :disabled="disabled"
            :readonly="readonly"
            :type="type"
            :autosize="{ minRows: 2, maxRows: 6 }"
            size="medium"
        />
        <div class="validate-div" v-if="validateText || validateTip">
            <span class="validate-span">{{ validateText || validateTip }}</span>
            <a v-if="showOrderLink" class="validate-a" :href="worksheetLink" target="_blank">
                {{ $t("simpleForm.domainInput.orderLink") }}
            </a>
            <el-button v-if="showDomainVerifyBtn" type="text" @click="showDomainVerify = true">
                {{ $t("simpleForm.domainInput.verify") }}
            </el-button>
        </div>

        <!-- 域名归属权认证 -->
        <el-dialog
            :title="$t('simpleForm.domainInput.ownershipVerify')"
            :append-to-body="true"
            :close-on-click-modal="false"
            :visible.sync="showDomainVerify"
        >
            <i class="el-icon-warning-outline" />
            {{ $t("simpleForm.domainInput.ownershipVerifyTip", { model }) }}
            <a class="validate-a" :href="worksheetLink" target="_blank">
                {{ $t("simpleForm.domainInput.serviceTicket") }}
            </a>
            {{ $t("simpleForm.domainInput.ownershipVerifyTip2") }}
            <el-tabs v-model="domainVerifyTab">
                <el-tab-pane :label="$t('simpleForm.domainInput.dnsVerify.label')" name="dns">
                    <p class="verify-tip">
                        {{ $t("simpleForm.domainInput.dnsVerify.itm1") }}
                        <a class="validate-a aocdn-ignore-link" @click="$docHelp(verifyLink)">
                            {{ $t("simpleForm.domainInput.verifyLink") }}
                        </a>
                    </p>
                    <el-descriptions direction="vertical" border>
                        <el-descriptions-item :label="$t('simpleForm.domainInput.descriptions.itm1')">{{
                            zoneType
                        }}</el-descriptions-item>
                        <el-descriptions-item :label="$t('simpleForm.domainInput.descriptions.itm2')">{{
                            zoneHost
                        }}</el-descriptions-item>
                        <el-descriptions-item :label="$t('simpleForm.domainInput.descriptions.itm3')">{{
                            zoneRecord
                        }}</el-descriptions-item>
                    </el-descriptions>
                    <p class="verify-tip">{{ $t("simpleForm.domainInput.dnsVerify.itm2") }}</p>
                    <p class="verify-tip">{{ $t("simpleForm.domainInput.dnsVerify.itm3") }}</p>

                    <el-button
                        type="primary"
                        @click="submitDomainVerify(1)"
                        size="medium"
                        :loading="verifyLoading"
                    >
                        {{ $t("simpleForm.domainInput.verifyText") }}
                    </el-button>
                    <p class="verify-tip" v-if="showVerifyRst1">
                        {{ $t("simpleForm.domainInput.verifyRst1") }}
                        <span class="verify-rst">
                            {{ verifyRst1 }}
                        </span>
                    </p>
                </el-tab-pane>
                <el-tab-pane :label="$t('simpleForm.domainInput.fileVerify.label')" name="file">
                    <p class="verify-tip">
                        {{ $t("simpleForm.domainInput.fileVerify.itm1", { domainZone }) }}
                        <a class="validate-a aocdn-ignore-link" @click="$docHelp(verifyLink)">
                            {{ $t("simpleForm.domainInput.verifyLink") }}
                        </a>
                    </p>
                    <p class="verify-tip">
                        {{ $t("simpleForm.domainInput.fileVerify.itm2", { filename, zoneRecord }) }}
                    </p>
                    <p class="verify-tip">
                        {{ $t("simpleForm.domainInput.fileVerify.itm3", { filename, domainZone: domainZone }) }}
                    </p>
                    <p class="verify-tip">{{ $t("simpleForm.domainInput.fileVerify.itm4") }}</p>

                    <el-button
                        type="primary"
                        @click="submitDomainVerify(2)"
                        size="medium"
                        :loading="verifyLoading"
                    >
                        {{ $t("simpleForm.domainInput.verifyText") }}
                    </el-button>
                    <p class="verify-tip" v-if="showVerifyRst2">
                        {{ $t("simpleForm.domainInput.verifyRst2") }}
                        <span class="verify-rst">
                            {{ verifyRst2 }}
                        </span>
                    </p>
                </el-tab-pane>
            </el-tabs>

            <div slot="footer">
                <el-button @click="showDomainVerify = false" size="medium">{{
                    $t("common.dialog.close")
                }}</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script type="text/javascript">
import { NEW_PREFIX } from "@/config/url/_PREFIX";
import { nUserModule } from "@/store/modules/nuser";
import { StatisticsModule } from "@/store/modules/statistics";
import { domainEndsWithSpecialSuffix } from '@/utils/product';
import { debounce } from "lodash-es";
const domainValidateUrl = NEW_PREFIX + "/domain/validate";
const verifyUrl = NEW_PREFIX + "/domain/domainZoneVerify";

export default {
    name: "n-alogic-domain-input",
    props: {
        domain: String,
        i: Number,
    },
    data() {
        return {
            isBatchCreate: this.$route.query.type === "batch",
            model: "",
            type: "text",
            readonly: false,
            disabled: false,
            oldValue: "", // 用户聚焦输入框时的值
            localModel: "", // 本地显示文本
            validateText: "", // 报错信息（本地版本，替换到统一的 danger tip）
            validateTip: "", // 提示信息，只展示在 tip 栏，但不算报错
            showOrderLink: false, // 当有报错信息时，是否展示工单链接

            // 第一层：域名归属权
            showDomainVerifyBtn: false, // 是否展示进入验证额按钮
            showDomainVerify: false, // 是否展示归属权验证弹窗
            domainVerifyTab: "dns",
            domainZone: "", // 域名校验失败时，客户主域名zone
            zoneType: "", // 主域名校验失败时，生成的记录类型，如TXT
            zoneHost: "", // 主域名校验失败时，生成的主机记录，如dnsverify
            zoneRecord: "", // 主域名校验失败时，生成的记录值
            verifyLoading: false,
            showVerifyRst1: false, // 展示 dns 验证结果
            verifyRst1: "", //
            showVerifyRst2: false, // 展示 文件 验证结果
            verifyRst2: "", //
            filename: "dnsverify.txt",

            lastIsRepeat: false, // 上一次是否为重叠域名，缓存标识，来确认是否需要重载页面

            debounceFn: debounce(this.handleLocalModelChange, 500)
        };
    },
    computed: {
        isCtclouds() {
            return nUserModule.isCtclouds;
        },
        worksheetLink() {
            return this.isCtclouds
                ? nUserModule.lang === "zh"
                    ? "https://www.esurfingcloud.com/contactus/fromIndex?lang=zh-cn&type=aftersale"
                    : "https://www.esurfingcloud.com/contactus/fromIndex?lang=en-us&type=aftersale"
                : "https://www.ctyun.cn/h5/wsc/worksheet/submit";
        },
        verifyLink() {
            return this.isCtclouds
                ? nUserModule.lang === "zh"
                    ? "https://www.esurfingcloud.com/document/zh-cn/10015932/10032771"
                    : "https://www.esurfingcloud.com/document/10015932/10032771"
                : "https://www.ctyun.cn/document/10015932/10032771";
        },
        xosDefaultAccelerateSuffix() {
            return StatisticsModule.xosDefaultAccelerateSuffix;
        },
        zosDefaultAccelerateSuffix() {
            return StatisticsModule.zosDefaultAccelerateSuffix;
        },
        // 域名是否以.ctyuncs.cn / zoscdn.cn为后缀
        isDomainEndsWithCtyun() {
            const rst = domainEndsWithSpecialSuffix(this.localModel, [this.xosDefaultAccelerateSuffix, this.zosDefaultAccelerateSuffix]);
            return rst;
        },
    },
    watch: {
        localModel: {
            handler: function(newVal){
                this.debounceFn(newVal);
            },
        },
        domain: function(v) {
            this.localModel = v;
        },
    },

    methods: {
        handleLocalModelChange(newVal) {
            this.model = newVal;
            this.validateProcedure();
        },
        async validateProcedure() {
            const result = {
                valid: true,
                msg: "",
                dom: this.isBatchCreate ? "domains" : "domain",
                i: null,
            };
            this.$emit("domainValidating", true);
            await this.handleChange();
            if (this.validateText) {
                result.valid = false;
                result.msg = this.validateText.split("，")[0];
            }
            this.$emit("domainChange", this.model, result.valid, this.i);
            return Promise.resolve(result);
        },
        // 内部控制保存信息
        async handleChange() {
            this.showVerifyRst1 = false;
            this.showVerifyRst2 = false;
            this.showDomainVerifyBtn = false;
            this.showDomainVerify = false;
            this.showOrderLink = false;

            // 校验前先不要清空标识，保留上次的，下面的步骤按需清空对应标识，确保提示语不会出现丢失

            // 基础校验1：不能为空
            if (this.model && this.model.trim() === "") {
                this.validateText = this.$t("simpleForm.domainInput.empty");
                return;
            }
            // 基础校验2：域名格式校验
            const rtgValidator = new RegExp(/^(\*\.|\*)?([a-z0-9]([a-z0-9-_]{0,61}[a-z0-9])?\.)+[a-z0-9-]{2,32}$/);
            if (!rtgValidator.test(this.model)) {
                this.validateText = this.$t("simpleForm.domainInput.invalid");
                return;
            }
            if (this.isDomainEndsWithCtyun) {
                let suffix = '';
                [this.xosDefaultAccelerateSuffix, this.zosDefaultAccelerateSuffix].forEach(e => {
                    if (!e) return;
                    if (suffix) suffix += `,`;
                    suffix += `${e}`;
                })
                this.validateText = this.$t("domain.editPage.tip29", { xosDefaultAccelerateSuffix: suffix });
                return;
            }

            // 注意，此接口返回的两个字段：isExist和recordStatus均为String类型
            this.validateText = this.$t("simpleForm.domainInput.validating");
            const result = await this.$ctFetch(domainValidateUrl, {
                method: "POST",
                data: {
                    domain: this.model,
                    operationType: "create",
                },
                headers: {
                    "Content-Type": "application/json",
                },
            });

            /**
             * 如果为BLACKLIST，页面提示verify_desc返回的错误（“该域名存在历史违规行为记录，目前不允许接入”）
             * 如果为TOP_ERROR，页面提示verify_desc返回的错误
             * 如果为UNVERIFIED，页面提示仍保持已有提示：
             */
            const { verifyCode, verifyDesc } = result;
            if (verifyCode === "BLACKLIST") {
                this.validateText = verifyDesc || this.$t("simpleForm.domainInput.blacklist");
                return;
            }

            if (verifyCode === "TOP_ERROR") {
                this.validateText = verifyDesc || this.$t("simpleForm.domainInput.topError");
                return;
            }

            // 第一层：域名归属权
            const { mainDomainVerify } = result;
            // 主域名校验，校验不通过则返回false,isExist和recordStatus不返回了
            if (mainDomainVerify === false) {
                this.showDomainVerifyBtn = true;
                this.showOrderLink = false;
                // this.validateText = verifyDesc || "域名归属权检查不通过";
                this.validateText = this.$t("simpleForm.domainInput.verifyFailed", { model: this.model });

                this.domainZone = result.domainZone;
                this.zoneType = result.zoneType;
                this.zoneHost = result.zoneHost;
                this.zoneRecord = result.zoneRecord;
                return;
            }

            // 仅域名归属权情况会出现进入验证环节，避免频繁请求或其他情况而导致的显示错误
            this.showDomainVerifyBtn = false;
            // 第二层：域名重复（非叠加域名）
            const { otherAccountExist, ownAccountExist } = result;
            const isRepeat = result.isRepeat === true;
            if (otherAccountExist === 1) {
                this.showOrderLink = true;
                this.validateText = this.$t("simpleForm.domainInput.repeat");
                return;
            }
            // 自己账号 CDN 产品线已有，且非叠加域名，则直接报错
            if (ownAccountExist === 1 && !isRepeat) {
                this.showOrderLink = true;
                this.validateText = this.$t("simpleForm.domainInput.existDomain");
                return;
            }

            // 第四层：是否有在途工单
            const { orderIsExist } = result;
            if (orderIsExist === true) {
                this.showOrderLink = true;
                this.validateText = this.$t("simpleForm.domainInput.orderExist");
                return;
            }

            // 第五层：是否为叠加域名
            const alllowRepeat = result.repeatSwitch === true; // 是否开启叠加域名
            if (isRepeat && !alllowRepeat) {
                this.showOrderLink = true;
                this.validateText = this.$t("simpleForm.domainInput.repeatProduct");
                return;
            }

            this.validateText = "";

            if (isRepeat !== this.lastIsRepeat) {
                // 重载界面
                this.$router.push({
                    name: "ndomain.create",
                    query: {
                        operationId: "isRepeat", // 叠加域名，展示项有不同，只展示：加速域名、加速类型、加速区域、域名类型
                        domain: this.model,
                    },
                });
            }
            // 缓存此次请求结果，用于下次比对
            this.lastIsRepeat = isRepeat;
        },

        // 用户手动验证归属
        async submitDomainVerify(verifyType) {
            try {
                this.verifyLoading = true;
                const { verifyResult, verifyResultDesc } = await this.$ctFetch(verifyUrl, {
                    data: {
                        domain: this.model,
                        verifyType, // 解析方法，1（DNS解析验证） ；2（文件验证）
                    },
                });

                this[`showVerifyRst${verifyType}`] = true;
                this[`verifyRst${verifyType}`] =
                    verifyResult === "true"
                        ? this.$t("simpleForm.domainInput.validSuccess")
                        : `${this.$t("simpleForm.domainInput.validFailed")}${verifyResultDesc}`;
                this.validateText =
                    verifyResult === "true" ? "" : this.$t("simpleForm.domainInput.ownershipFailed");
            } catch (err) {
                this.$message.error(err?.data?.reason || err?.reason || err);
            } finally {
                this.verifyLoading = false;
            }
        },
    },
    mounted() {
        this.localModel = this.domain;
    },
};
</script>

<style lang="scss" scoped>
@import "../variable.scss";

.input-wrapper {
    width: 100%;
    .domain-input {
        width: #{$wrapper-width}px;
        margin-top: 2px;
    }
    .validate-div {
        margin-top: -8px;
        margin-bottom: -10px;
        .validate-span {
            line-height: 12px;
            font-size: 12px;
            color: $color-danger;
        }
        // .validate-a {
        //     color: $g-color-yellow;
        //     text-decoration: underline;
        //     &:hover {
        //         color: #fb9c5d;
        //     }
        // }
    }
}
.validate-a {
    color: $color-master;
    text-decoration: underline;
    font-size: 12px;
    &:hover {
        color: $color-master-hover;
    }
}

.verify-tip {
    line-height: 32px;
}

.verify-rst {
    color: $color-danger;
}

::v-deep {
    .el-descriptions__body .el-descriptions__table .el-descriptions-row .el-descriptions-item__cell {
        text-align: left;
    }
}
</style>
