<template>
    <section class="search-section">
        <search-bar-row :span="4" class="mb-8">
            <el-select
                v-if="useProduct || useDcdnProduct || useProductOther"
                v-model="product"
                multiple
                filterable
                collapse-tags
                :placeholder="$t('statistics.common.searchPlaceholder[0]')"
                key="product"
            >
                <el-option
                    v-for="(item, index) in productOptions"
                    :key="index"
                    :label="$t(item.label)"
                    :value="item.value"
                />
            </el-select>
            <label-select v-if="useLabel" v-model="selectedLabel" :labelList="labelList" />
            <el-select
                v-if="useDomain"
                v-model="domain"
                filterable
                clearable
                :placeholder="$t('statistics.common.searchPlaceholder[1]')"
                key="domain"
            >
                <el-option :label="$t('statistics.common.domainSelectOption')" value="all" />
                <el-option
                    v-for="item in domainOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                />
            </el-select>
            <domain-select
                v-if="useMultipleDomain"
                v-model="multipleDomain"
                :domainOptions="domainOptions"
                :multiple="true"
                key="multipleDomain"
                :loading="domainOptionsLoading"
            />
            <el-select
                v-if="useCode"
                v-model="code"
                multiple
                filterable
                collapse-tags
                :placeholder="$t('statistics.common.searchPlaceholder[6]')"
                key="code"
            >
                <el-option v-for="item in codeOptions" :key="item" :label="item" :value="item" />
            </el-select>
            <el-select
                v-if="useSingleCode"
                v-model="singleCode"
                filterable
                clearable
                collapse-tags
                :placeholder="$t('statistics.common.searchPlaceholder[6]')"
                key="singleCode"
            >
                <el-option v-for="item in codeOptions" :key="item" :label="item" :value="item" />
            </el-select>
            <area-select
                v-if="useArea"
                :multiple="true"
                :oversea="true"
                v-model="area"
                :area-list="areaOptions"
                :is-global="isGlobal"
                key="area"
            />
        </search-bar-row>
        <search-bar-row fullwidth>
            <el-radio-group v-if="useSort" v-model="sortBy">
                <el-radio-button label="flow">{{
                    $t("statistics.rank.searchBar.areaSelectRadioText1")
                }}</el-radio-button>
                <el-radio-button label="request">{{
                    $t("statistics.rank.searchBar.areaSelectRadioText2")
                }}</el-radio-button>
            </el-radio-group>
            <ct-time-picker
                v-if="useTimePicker"
                v-model="timeRange"
                :current-period.sync="currentPeriod"
                :periodOptions="periodOptions"
                :maxDayBeforeNow="90"
                type="datetimerange"
                style="margin-right: 8px"
                key="timePicker"
                size="medium"
                :isHourly="useHourly"
            />
            <el-tooltip :content="$t('statistics.rank.timeSelect')" v-if="useHourly">
                <ct-svg-icon
                    icon-class="question-circle"
                    class-name="ct-sort-drag-icon"
                    style="font-size: 14px; align-self: center; margin-right: 8px"
                ></ct-svg-icon>
            </el-tooltip>
            <el-button type="primary" @click="() => beforeSearch(true)">{{
                $t("statistics.common.searchBtn")
            }}</el-button>
            <el-button class="reset-btn" @click="resetFilter">{{ $t("common.search.reset") }}</el-button>
            <el-button
                v-if="useDownload"
                class="el-icon-download download-btn"
                style="float: right"
                @click="download"
            />
        </search-bar-row>
        <div class="ct-select-group"></div>
    </section>
</template>

<script lang="ts">
import { Component, Prop, Watch, Vue } from "vue-property-decorator";
import { DomainModule } from "@/store/modules/domain";
import { LabelModule } from "@/store/modules/label";
import { ProductModule } from "@/store/modules/ncdn/nproduct";
import { StatisticsModule } from "@/store/modules/statistics";
import { SearchParams } from "@/types/statistics/rank";
import { DomainActionEnum, DomainActionKey, getDomainAction, ProductCodeEnum } from "@/config/map";
import AreaSelect from "@/components/areasSelect/index.vue";
import DomainSelect from "@/components/domainsSelect/index.vue";
import CtTimePicker from "@/components/ctTimePicker/index.vue";
import LabelSelect from "@/components/lableSelect/index.vue";
import SearchBarRow from "../../searchBarRow.vue";
import ctSvgIcon from "@/components/ctSvgIcon/index.vue";
import { getAm0 } from "@/utils";
import { nUserModule } from "@/store/modules/nuser";
import { CurrentMenu } from "../../statistics";
import { MainMenu } from "@/views/statistics/entryMixin";
import { cloneDeep } from "lodash-es";
import { LabelItem } from "@/store/types";

@Component({
    components: { AreaSelect, DomainSelect, LabelSelect, SearchBarRow, CtTimePicker, ctSvgIcon },
})
export default class SearchBar extends Vue {
    // 所需查询条件的标识，使用 "domain, timepicker" 的方式传入，需要哪个就配置哪个
    // ps：根据业务需要，域名、时间都是必须要的
    @Prop({ default: "domain, timePicker", type: String }) private paramFlag!: string;
    // time-picker 的快捷配置项
    @Prop({ default: () => ["0", "1", "7", "30", "-1"], type: Array }) periodOptions!: string[];
    // 是否使用自动发起请求，默认不自动发起
    @Prop({ default: false, type: Boolean }) private autoFetch!: boolean;
    @Prop({ default: false, type: Boolean }) private childPanelMounted!: boolean;
    @Prop({ default: { main: "", sub: "" }, type: Object }) private currentMenu!: CurrentMenu<MainMenu>;

    // 从 paramFlag 中获取各条件是否启用
    // 说明：使用 watch 可以阻断不必要的计算属性监听
    useProduct = false; // 1、全部产品（默认）
    useDcdnProduct = false;
    useProductOther = false;
    useDomain = false; // 默认是多选
    selectedLabel: string[] = []; // 默认标签不选
    useMultipleDomain = false; // 单选和多选分开2个组件，便于分开存储数据
    useCode = false;
    useSingleCode = false;
    useArea = false;
    useSort = false;
    useTimePicker = false;
    useDownload = false;
    useHourly = false;
    isDcdn = false;
    initialized = false;

    @Watch("paramFlag", { immediate: true })
    onParamFlag(flag: string) {
        this.useProduct = flag.includes("product");
        this.useProductOther = flag.includes("productOther");
        this.useDcdnProduct = flag.includes("dcdnProduct");
        this.useDomain = flag.includes("domain");
        this.useMultipleDomain = flag.includes("multipleDomain");
        this.useCode = flag.includes("code");
        this.useSingleCode = flag.includes("singleCode");
        this.useArea = flag.includes("area");
        this.useSort = flag.includes("sort");
        this.useTimePicker = flag.includes("timePicker");
        this.useDownload = flag.includes("download");
        this.useHourly = flag.includes("hourly");
    }

    @Watch("$route", { immediate: true })
    onRouteChange() {
        // 判断普通用量分析还是全站加速用量分析
        this.isDcdn = this.$route.name === "statistics.dcdn";
    }

    product: string[] = [];
    domain = "all"; // 选择框选中的域名（单选）
    multipleDomain = []; // 选择框选中的域名（多选）
    code: string[] = []; // 状态码
    singleCode = "";
    area: any = {
        province: [],
        continent: [],
        continentRegion: [],
    }; // 选择的范围
    sortBy = "flow"; // 排序
    timeRange: null | Date[] = null; // 时间
    currentPeriod = "1"; // 当前 ct-time-picker 选择的时间段
    stashedSearchParams: any = null; // 暂存的查询参数

    get domainAction() {
        let action = DomainActionEnum.Data;

        if (nUserModule.isFcdnCtyunCtclouds) {
            const sub = this.currentMenu.sub;
            action = getDomainAction(
                (!sub?.endsWith("Whole") && this.isDcdn ? `${sub}Whole` : sub) as DomainActionKey
            );
        }

        return action;
    }

    // 全部的域名列表
    get domainList() {
        return DomainModule[this.domainAction].nativeList.filter(domain => {
            return this.productOptions.some(product => product.value === domain.productCode);
        });
    }
    get domainOptionsLoading() {
        return DomainModule[this.domainAction].loading;
    }
    get useLabel() {
        return this.paramFlag.includes("label");
    }
    // 标签列表
    get labelList() {
        if (nUserModule.isFcdnCtyunCtclouds) {
            return (LabelModule as any)[this.domainAction].labelList;
        }

        return LabelModule.labelList;
    }

    get onLabelLoading() {
        return LabelModule.loading;
    }

    // 域名和标签的绑定关系
    get domainMapLabel() {
        return LabelModule.domainMapLabelJr;
    }

    get productOptions() {
        const { useProduct, useDcdnProduct } = this;
        const options = ProductModule.allProductOptions;
        if (window.__POWERED_BY_QIANKUN__) {
            return StatisticsModule.allProductOptions.map(item => {
                return {
                    label: item.product_cname,
                    value: item.product_code,
                };
            });
        }

        const list = options.filter(opt => {
            if (!opt.label || !opt.value) return false;

            const isDcdnOptions =
                opt.value === ProductCodeEnum.Upload ||
                opt.value === ProductCodeEnum.Whole ||
                opt.value === ProductCodeEnum.Socket;
            // 筛选全站加速和普通用量分析的产品下拉选择
            if (useProduct) return !isDcdnOptions;
            if (useDcdnProduct) return isDcdnOptions;
        });
        return list;
    }

    //具体状态码作为过滤条件，底层还未支持，先功能预留
    get codeOptions() {
        return this.useCode
            ? ["2XX", "3XX", "4XX", "5XX"]
            : [
                  "200",
                  "201",
                  "202",
                  "203",
                  "204",
                  "205",
                  "206",
                  "300",
                  "301",
                  "302",
                  "303",
                  "304",
                  "305",
                  "306",
                  "307",
                  "400",
                  "401",
                  "402",
                  "403",
                  "404",
                  "405",
                  "406",
                  "407",
                  "408",
                  "409",
                  "410",
                  "411",
                  "412",
                  "413",
                  "414",
                  "415",
                  "416",
                  "417",
                  "500",
                  "501",
                  "502",
                  "503",
                  "504",
                  "505",
              ];
    }

    get areaOptions() {
        return StatisticsModule.areaOptions;
    }

    get isGlobal() {
        return StatisticsModule.isGlobal;
    }

    get childAccount() {
        return StatisticsModule.childAccount;
    }

    get domainCountLimit() {
        return StatisticsModule.domainCountLimit;
    }

    // 根据加速类型筛选展示域名列表
    get domainOptions() {
        const { product, selectedLabel, domainMapLabel } = this;
        return (
            this.domainList
                // 过滤逻辑：如果产品未选择，则默认所有域名；若产品有选择，则根据产品进行过滤
                .filter(item => {
                    if (!product?.length) return true;
                    // 当加速类型不为空，则返回选择的加速类型对应的域名
                    return product.includes(item.productCode);
                })
                // 过滤逻辑2：label，全选则不过滤，非全选则查看域名是否在绑定了这些标签
                .filter(item => {
                    // 没有已选标签就不过滤了
                    if (selectedLabel.length === 0) {
                        return true;
                    } else {
                        return selectedLabel.some(labelId => domainMapLabel[item.domain]?.includes(labelId));
                    }
                })
                .map(item => ({
                    // 从接口原生数据中获取 options
                    label: item.label,
                    value: item.domain,
                }))
        );
    }

    // 已选的域名列表（在此处理单选/多选差异）
    get domainParams() {
        const { useDomain, useMultipleDomain } = this;
        const { domain, multipleDomain } = this;
        const allDomains = this.domainOptions.map(d => d.value);
        // 子账号
        if (this.childAccount) {
            if (useDomain) {
                // 单选
                return domain === "all" ? allDomains : [domain];
            } else if (useMultipleDomain) {
                // 若客户未选择域名，且可选择的域名数量大于100时，则不再传递域名给后端
                if (multipleDomain.length > 0) {
                    return multipleDomain;
                } else {
                    if (allDomains.length <= this.domainCountLimit) {
                        return allDomains;
                    } else {
                        return [];
                    }
                }
            }
        } else {
            if (useDomain) {
                // 单选
                return domain === "all" ? allDomains : [domain];
            } else if (useMultipleDomain) {
                // 全选时，不传递具体域名
                if (multipleDomain.length === 0 || multipleDomain.length === allDomains.length) {
                    return [];
                }
                // 若客户未选择域名，且可选择的域名数量大于等于100时，则不再传递域名给后端
                if (multipleDomain.length > 0) {
                    return multipleDomain;
                } else {
                    if (allDomains.length < this.domainCountLimit) {
                        return allDomains;
                    } else {
                        return [];
                    }
                }
            } else {
                // 默认是选中所有
                return allDomains;
            }
        }
        return allDomains;
    }

    // 请求参数
    get searchParams() {
        const { useCode, useSingleCode, useArea, useSort, useTimePicker } = this;
        const { product, domainParams, code, singleCode, area, sortBy, timeRange } = this;
        const params: SearchParams = {
            product: [],
            domainList: [],
            startTime: 0,
            endTime: 0,
        };

        params.product = [...product]; // 根据业务要求，产品是必传参数
        params.domainList = [...domainParams]; // 根据业务要求，域名是必传参数
        if (useCode) params.httpCode = [...code];
        if (useSingleCode) params.httpCode = [singleCode];
        if (useArea) {
            // tag-area更改标注
            params.province = this.area.province;
            params.continent_code = this.area.continent;
            params.continent_region_code = this.area.continentRegion;
            // 查询全部地区(-1)则默认不传province参数
            if (params.province?.includes("-1")) {
                params.province = [];
            }
        }
        if (useSort) params.sortBy = sortBy;
        if (useTimePicker) {
            if (!timeRange) {
                params.startTime = 0;
                params.endTime = 0;
            } else {
                const [startTime, endTime] = timeRange;

                params.startTime = Math.floor(+startTime / 1000);
                params.endTime = Math.floor(+endTime / 1000);
            }
        }
        // 不传递域名参数domainList时，需要传递product
        if (!(params.product && params.product.length > 0)) {
            const isFcdnCtyunCtclouds = nUserModule.isFcdnCtyunCtclouds;
            if (!(params.domainList.length > 0)) {
                params.product = this.productOptions.map(item => {
                    return item.value;
                });
            } else if (isFcdnCtyunCtclouds) {
                // fcdn ctyun（国际+国内） 必传 product
                params.product = this.domainList
                    .filter(itm => params.domainList?.includes(itm.domain))
                    .map(itm => itm.productCode);
                params.product = [...new Set(params.product)];
            }
        }

        return params;
    }

    get canRequest() {
        return (
            this.initialized &&
            this.childPanelMounted &&
            // ctiam环境需要等待标签加载完成
            (nUserModule.isFcdnCtyunCtclouds ? !this.onLabelLoading : true)
        );
    }

    @Watch("currentMenu", { immediate: true, deep: true })
    async onCurrentMenuChange() {
        StatisticsModule.SET_CURRENT_ACTION(this.domainAction);
        if (nUserModule.isFcdnCtyunCtclouds) {
            this.initialized = false;
            // 暂存查询参数
            this.stashedSearchParams = this.stashedSearchParams
                ? cloneDeep(this.stashedSearchParams)
                : cloneDeep({
                      label: this.selectedLabel,
                      multipleDomain: this.multipleDomain,
                  });

            await Promise.all([
                DomainModule.GetDomainList({ action: this.domainAction, shouldCheckCache: true }),
                LabelModule.GetCtiamLabelList(this.domainAction),
            ]);
        }
    }

    @Watch("canRequest", { immediate: true })
    onCanRequestChange(val: boolean) {
        if (!val) return;
        this.$nextTick(() => {
            if (nUserModule.isFcdnCtyunCtclouds) {
                if (this.stashedSearchParams?.label) {
                    this.selectedLabel = (this.labelList as LabelItem[])
                        .map(item => item.children)
                        .flat()
                        .filter(item => this.stashedSearchParams.label.includes(item.value))
                        .map(itm => itm.value) as any;
                }
                this.$nextTick(() => {
                    if (this.stashedSearchParams?.multipleDomain) {
                        this.multipleDomain = this.domainOptions
                            .filter(item => this.stashedSearchParams.multipleDomain.includes(item.value))
                            .map(item => item.value) as any;
                    }
                    // 清空暂存的查询参数
                    this.stashedSearchParams = null;
                    this.beforeSearch();
                });
            } else {
                this.beforeSearch();
            }
        });
    }

    @Watch("product")
    onProductChange() {
        // 切换产品时清空域名
        this.multipleDomain = [];
        this.domain = "all";
    }

    @Watch("domainParams", { immediate: true })
    onDomainParamsChange(newArr: [], oldArr: []) {
        // 首次进入则进行请求
        if (
            (newArr.length !== 0 && (!oldArr || oldArr.length === 0)) ||
            (this.domainOptions || []).map(d => d.value).length > 0
        ) {
            if (this.initialized === true) {
                return;
            }
            this.initialized = true;
            this.$emit("initialized");
        }
    }

    // 查询方法
    // 子账号查询时，有以下四种情况（主账号不变，和原有逻辑保持一致）
    // 1、未做选择时超过100，触发查询需要提示
    // 2、已选择超过100 ，触发查询需要提示
    // 3、不管总域名数有没有超过100，选择的域名数小于等于100 ，需要传参，但是不需要提示
    // 4、总域名数小于100，未选择，点查询需要传参，但是不需要提示  传的是域名列表的所有域名
    async beforeSearch(triggerByUser = false) {
        // 当不是全选域名且选则的域名数量多于100时，拦截请求
        if (
            (this.searchParams.domainList as string[]).length > this.domainCountLimit &&
            (this.searchParams.domainList as string[]).length !== this.domainOptions.length &&
            !this.childAccount
        ) {
            this.$message.error(`${this.$t("common.searchBar.errMsg1", { count: this.domainCountLimit })}`);
            return;
        }
        // 选择了标签时，必须选择域名
        if (this.selectedLabel.length > 0 && this.multipleDomain.length === 0) {
            this.$message.error(`${this.$t("common.searchBar.errMsg2")}`);
            return;
        }
        // 没有可选域名，不允许查询
        if (!this.domainOptions.length && this.childAccount) {
            this.$message.error(`${this.$t("statistics.common.chart.errMsg[0]")}`);
            return;
        }

        if (this.selectedLabel.length > 0 && this.multipleDomain.length > this.domainCountLimit) {
            this.$message.error(`${this.$t("common.searchBar.errMsg3", { count: this.domainCountLimit })}`);
            return;
        }
        if (this.selectedLabel.length > 0) {
            this.searchParams.domainList = this.multipleDomain;
        }
        // 选择使用了时间则需有数据
        if (this.useTimePicker && !this.searchParams.startTime) {
            this.$message.error(`${this.$t("common.searchBar.errMsg4")}`);
            return;
        }
        // 未做选择时域名总数超过100，触发查询需要提示
        if (
            this.childAccount &&
            (this.searchParams.domainList as string[]).length === 0 &&
            this.domainOptions.length > this.domainCountLimit
        ) {
            this.$message.error(`${this.$t("common.searchBar.errMsg5", { count: this.domainCountLimit })}`);
            return;
        }
        // 选中的域名数超过100个，触发查询需要提示
        if (this.childAccount && (this.searchParams.domainList as string[]).length > this.domainCountLimit) {
            this.$message.error(`${this.$t("common.searchBar.errMsg5", { count: this.domainCountLimit })}`);
            return;
        }

        // 查询前先去获取scale
        const scaleDomainList = this.searchParams.domainList?.length
            ? this.searchParams.domainList
            : this.domainOptions.map(item => item.value);

        // 广播给父组件去执行查询
        this.$emit("search", {
            searchParams1: this.searchParams,
            triggerByUser,
            scaleDomainList,
        });
    }

    resetFilter() {
        this.product = [];
        this.domain = "all";
        this.multipleDomain = [];
        this.code = [];
        this.singleCode = "";
        this.selectedLabel = [];
        this.area = {
            province: [],
            continent: [],
            continentRegion: [],
        };
        this.sortBy = "flow";
        this.timeRange = [getAm0(new Date()), new Date()];
        this.currentPeriod = "1";

        this.beforeSearch(true);
    }

    //表格下载
    download() {
        this.$emit("download");
    }
}
</script>

<style lang="scss" scoped>
.mb-8 {
    margin-bottom: 8px;
}

::v-deep .el-select__tags {
    .el-select__input {
        margin-left: 8px !important;
    }
}

::v-deep {
    .el-date-editor--datetimerange.el-input,
    .el-date-editor--datetimerange.el-input__inner {
        width: 336px !important;
    }
}

.reset-btn {
    margin-left: 8px;
}
.download-btn {
    margin-left: 8px;
}
</style>
