import { downloadCsv } from "@/utils";

export default {
    data() {
        return {
            defaultSeriesConf: {
                type: "line",
                smooth: true,
                symbol: "none",
                areaStyle: {
                    color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        {
                            offset: 0,
                            color: "rgb(235, 241, 253)",
                        },
                        {
                            offset: 1,
                            color: "rgb(255, 255, 255)",
                        },
                    ]),
                },
                lineStyle: {
                    color: "#78A1DA",
                },
            },
            toolBox: {
                toolbox: {
                    emphasis: {
                        iconStyle: {
                            borderColor: "#3d73f5",
                        },
                    },
                    // 工具箱
                    feature: {
                        myCSV: {
                            title: `${this.$t("statistics.common.chart.toolBox[0]")}`,
                            icon:
                                "image://data:image/png;base64,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",
                            onclick: this.handleExportCSV,
                        },
                        dataZoom: {
                            title: {
                                zoom: `${this.$t("statistics.common.chart.toolBox[1]")}`,
                                back: `${this.$t("statistics.common.chart.toolBox[2]")}`,
                            },
                            yAxisIndex: "none",
                        },
                        restore: {
                            title: `${this.$t("statistics.common.chart.toolBox[3]")}`,
                        },
                    },
                },
                dataZoom: [
                    // 导航条，同时支持侧边栏和坐标系内置
                    {
                        type: "slider",
                        filterMode: "filter",
                        labelFormatter: "",
                    },
                    { type: "inside" },
                ],
                grid: {
                    // 容器各个方向的留白
                    left: "5%",
                    right: "6%",
                    top: "15%",
                    bottom: "14%",
                    containLabel: true,
                },
            },
        };
    },
    methods: {
        handleExportCSV() {
            this.tableToExcel();
        },
        // 重写下载方法
        downloadExcel({ name, str }) {
            downloadCsv(`${name}`, str);
        },
    },
};
