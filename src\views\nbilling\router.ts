import { ProductModule } from "@/store/modules/ncdn/nproduct"; // cdn独有
import { RouteConfig } from "vue-router";
import { nUserModule } from "@/store/modules/nuser"; // 已合并
const indexRouter: RouteConfig = {
    path: "/billing",
    name: "billing",
    component: () => import("./index.vue"),
    meta: {
        domain: "billing",
        ucode: "billing", // 配合 el-menu active
        breadcrumb: {
            type: "toolTip",
            title: "$t('billing.title')",
            tips: [
                '$t("billing.tips.tip1")',
                '$t("billing.tips.tip2")',
                '$t("billing.tips.tip3")',
                '$t("billing.tips.tip4")',
            ],
            route: ["billing"],
        },
        perm: "billing",
    },
    beforeEnter(to, from, next) {
        // 不阻塞页面加载
        nUserModule.GetUserInfo();
        ProductModule.nGetProductInfo();
        next();
    },
};

export default indexRouter;
