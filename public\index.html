<!DOCTYPE html>
<html lang="zh">
    <head>
        <meta charset="utf-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width,initial-scale=1.0" />
        <title><%= htmlWebpackPlugin.options.title %></title>
        <script>
            if (window.location.href.includes('ctyun') || window.location.href.includes('cloud')) {
                var script = document.createElement('script');
                script.src = "/cta/ctcloud-analysis.min.js";
                document.getElementsByTagName('head')[0].appendChild(script);
            }
        </script>
    </head>

    <body class="aocdn-micro-app">
        <noscript>
            <strong>请开启浏览器的Javascript功能，以便能正常访问网站！</strong>
        </noscript>

        <script>
            // 只在fcdn初始化，aocdn不需要初始化
            if(!window.__POWERED_BY_QIANKUN__) {
                window.CtcloudLayout = {
                    preset: {
                        autoLoad: window.location.origin.includes('cloud'),
                        operate: {
                            isHash: true,
                            currentLocale: window.location.href.indexOf("lang") === -1 ? { "en": "en-us", "zh":"zh-cn" }[localStorage.getItem("ctc_lang")] : "" || ""
                        },
                        urlPrefix: '/ctyun_ncdn'
                    },
                };
                // CtcloudLayout版本更新需要替换（为了和主应用变量名称一致，主应用为v2）
                window.CtcloudLayoutV2 = {
                    preset: {
                        autoLoad: window.location.origin.includes('cloud'),
                        operate: {
                            isHash: true,
                            currentLocale: window.location.href.indexOf("lang") === -1 ? { "en": "en-us", "zh":"zh-cn" }[localStorage.getItem("ctc_lang")] : "" || ""
                        },
                        urlPrefix: '/ctyun_ncdn'
                    },
                };
            }
        </script>

        <div id="app"></div>
    </body>
</html>
