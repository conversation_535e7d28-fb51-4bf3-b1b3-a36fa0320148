export enum SchduelRefreshTypeEnum {
    dir = "dir",
    url = "url",
    re = "re",
}

export enum SchduelTaskTypeEnum {
    appoint = 1,
    loop = 2,
}

export enum SchduelFrequencyEnum {
    cron_everyday = "cron_everyday",
    cron_week = "cron_week",
    cron_monday = "cron_monday",
}

export type SchduelTaskParam = {
    refresh_type: SchduelRefreshTypeEnum; // 刷新类型："dir", "url", "re"
    task_type: SchduelTaskTypeEnum; // 任务类型：1 预约，2 循环
    plan_datetime: string; // 预约时yyyy-MM-dd HH:mm:ss
    loop_date: Date[]; // 前端使用，循环时选择的日期,[eff_date, exp_date]
    eff_date: string; // 循环时yyyy-MM-dd
    exp_date: string; // 循环时yyyy-MM-dd
    plan_time: string; // 循环时HH:mm:ss
    loop_frequency: SchduelFrequencyEnum; // 前端使用，循环频率
    cron_everyday: 0 | 1; // 循环时选择哪个传哪个，为每天，0 无效，1生效
    cron_week: number[]; // 循环时选择哪个传哪个，1~7多选，逗号分割，传空字符串为无效
    cron_monday: number[]; // 循环时选择哪个传哪个，1~31多选，逗号分割，传空字符串为无效
    content: string; // 刷新内容
};

export type RefreshCheckResultItem = {
    domain: string;
    enable: "true" | "false";
    url: string;
};

export const getDefaultSchedualTask = (): SchduelTaskParam => {
    return {
        refresh_type: SchduelRefreshTypeEnum.url,
        task_type: SchduelTaskTypeEnum.appoint,
        plan_datetime: "",
        loop_date: [],
        eff_date: "",
        exp_date: "",
        plan_time: "23:59:59",
        loop_frequency: SchduelFrequencyEnum.cron_everyday,
        cron_everyday: 0,
        cron_week: [],
        cron_monday: [],
        content: "",
    };
};
