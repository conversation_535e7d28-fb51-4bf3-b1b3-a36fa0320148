<template>
    <ct-section-wrap :headerText="headerText" :headerTip="headerTip" class="ipa-statistics-section-wrapper">
        <div class="statistics-scroll-wrapper">
            <el-card>
                <nested-menu v-model="currentMenu" :menuList="getMenuList">
                    <!-- 搜索栏 -->
                    <el-form
                        ref="form"
                        :model="form"
                        label-width="40px"
                        label-position="left"
                        inline
                        class="search-form-wrapper"
                    >
                        <el-col v-show="activeModule === 'usage'">
                            <el-form-item :label="$t('statistics.common.searchLabel1')">
                                <el-select
                                    v-model="form.usage.type"
                                    @change="typeChange"
                                    :placeholder="$t('label.select')"
                                    class="short-select-wrapper"
                                >
                                    <el-option
                                        :label="$t('statistics.rank.domainRank.tableColumn2')"
                                        :value="1"
                                    ></el-option>
                                    <el-tooltip
                                        effect="dark"
                                        placement="right"
                                        :content="$t('statistics.eas.tip2')"
                                        v-if="!hasInstName"
                                    >
                                        <el-option
                                            :disabled="!hasInstName"
                                            :label="$t('log.list.label4')"
                                            :value="2"
                                        ></el-option>
                                    </el-tooltip>
                                    <el-option
                                        v-if="hasInstName"
                                        :label="$t('log.list.label4')"
                                        :value="2"
                                    ></el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label-width="12px">
                                <!-- 域名 实例 下拉框 -->
                                <domain-select
                                    class="search-form-wrapper-domain"
                                    :placeholder="usageDomainPlaceholder"
                                    v-model="form.usage.domain"
                                    :domainOptions="usageDomainInstNameOptions"
                                    :domainType="domainType"
                                    :multiple="true"
                                    key="multipleDomain"
                                />
                            </el-form-item>
                            <el-form-item label-width="12px">
                                <el-select
                                    v-model="form.usage.isp"
                                    multiple
                                    collapse-tags
                                    :placeholder="$t('statistics.common.searchPlaceholder[2]')"
                                >
                                    <el-option
                                        v-for="(item, index) in ispList"
                                        :key="index"
                                        :label="item.label"
                                        :value="item.value"
                                    >
                                    </el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label-width="12px">
                                <area-select
                                    :multiple="true"
                                    :area-list="areaOptions"
                                    v-model="form.usage.area"
                                    :is-global="isGlobal"
                                    key="area"
                                    :is-from-ipa="true"
                                    class="search-form-wrapper-area"
                                ></area-select>
                            </el-form-item>
                            <el-form-item label-width="12px" v-show="isBwOrFlow">
                                <el-select
                                    v-show="getSubMenu === 'bandwidthChart'"
                                    v-model="form.usage.bandwidthType"
                                    collapse-tags
                                    :placeholder="$t('statistics.eas.tip3')"
                                >
                                    <el-option
                                        v-for="(item, index) in bandwidthList"
                                        :key="index"
                                        :label="item.label"
                                        :value="item.value"
                                    >
                                    </el-option>
                                </el-select>
                                <el-select
                                    v-show="getSubMenu === 'flowChart'"
                                    v-model="form.usage.flowType"
                                    collapse-tags
                                    :placeholder="$t('statistics.eas.tip4')"
                                >
                                    <el-option
                                        v-for="(item, index) in flowList"
                                        :key="index"
                                        :label="item.label"
                                        :value="item.value"
                                    >
                                    </el-option>
                                </el-select>
                            </el-form-item>
                            <div class="search-form-wrapper-time">
                                <el-form-item :label="$t('statistics.user.tip16')">
                                    <ct-time-picker
                                        v-show="!form.usage.timeCompareShow || !isBwOrFlow"
                                        v-model="form.usage.timeRange"
                                        :periodOptions="['0', '1', '7', '30', '-1']"
                                        :maxDayBeforeNow="365"
                                        type="datetimerange"
                                        key="timePicker"
                                        size="medium"
                                    />
                                    <ct-time-compare
                                        v-show="isBwOrFlow"
                                        :class="{ 'compare-show': !form.usage.timeCompareShow }"
                                        :compareShow.sync="form.usage.timeCompareShow"
                                        v-model="form.usage.timeCompare"
                                        :maxDayBeforeNow="365"
                                        key="timeCompare"
                                        size="medium"
                                    />
                                </el-form-item>
                                <el-form-item label-width="0">
                                    <el-button type="primary" @click="handleUpdateView">{{
                                        $t("statistics.common.searchBtn")
                                    }}</el-button>
                                    <el-tooltip
                                        v-if="getSubMenu === 'regionISP'"
                                        effect="dark"
                                        :content="$t('statistics.common.searchDownloadContent')"
                                        placement="right"
                                    >
                                        <i
                                            class="el-icon-download ipa-download-icon"
                                            @click="handleDownloadRegionTable"
                                        ></i>
                                    </el-tooltip>
                                </el-form-item>
                            </div>
                        </el-col>
                        <el-col v-show="activeModule === 'hot'">
                            <div>
                                <el-form-item>
                                    <el-select
                                        class="short-select-wrapper"
                                        v-model="form.hot.type"
                                        @change="onHotTypeChange"
                                        :placeholder="$t('label.select')"
                                    >
                                        <el-option
                                            :label="$t('statistics.rank.domainRank.tableColumn2')"
                                            :value="1"
                                        ></el-option>
                                        <el-tooltip
                                            effect="dark"
                                            placement="right"
                                            :content="$t('statistics.eas.tip2')"
                                            v-if="!hasInstName"
                                        >
                                            <el-option
                                                :disabled="!hasInstName"
                                                label="实例"
                                                :value="2"
                                            ></el-option>
                                        </el-tooltip>
                                        <el-option
                                            v-if="hasInstName"
                                            :label="$t('log.option2')"
                                            :value="2"
                                        ></el-option>
                                    </el-select>
                                </el-form-item>
                                <el-form-item>
                                    <!-- 域名 实例 下拉框 -->
                                    <domain-select
                                        class="search-bar-wrapper-domain"
                                        :placeholder="domainPlaceholder"
                                        v-model="form.hot.domain"
                                        :domainOptions="getDomainOrInstOption"
                                        :domainType="domainType"
                                        :multiple="true"
                                        key="multipleDomain"
                                        :max-length="2"
                                    />
                                </el-form-item>
                                <div v-show="getSubMenu === 'topClientIp'" style="display: inline-block">
                                    <el-form-item>
                                        <area-select
                                            :multiple="true"
                                            :area-list="areaOptions"
                                            v-model="form.hot.area"
                                            :is-global="isGlobal"
                                            key="area"
                                            :is-from-ipa="true"
                                        ></area-select>
                                    </el-form-item>
                                </div>
                            </div>
                            <el-form-item label-width="0">
                                <el-radio-group v-model="form.hot.sorted_by">
                                    <el-radio-button label="flow">{{
                                        $t("statistics.rank.searchBar.areaSelectRadioText1")
                                    }}</el-radio-button>
                                    <el-radio-button label="request">{{
                                        $t("statistics.eas.tip28")
                                    }}</el-radio-button>
                                </el-radio-group>
                            </el-form-item>
                            <el-form-item label-width="0">
                                <ct-time-picker
                                    v-model="form.hot.timeRange"
                                    :periodOptions="['0', '1', '7', '30', '-1']"
                                    :maxDayBeforeNow="90"
                                    type="datetimerange"
                                    key="timePicker"
                                    size="medium"
                                />
                            </el-form-item>
                            <el-form-item label-width="0">
                                <el-button type="primary" @click="handleUpdateView">{{
                                    $t("statistics.common.searchBtn")
                                }}</el-button>
                                <el-tooltip
                                    effect="dark"
                                    :content="$t('statistics.common.searchDownloadContent')"
                                    placement="right"
                                >
                                    <i
                                        class="el-icon-download ipa-download-icon"
                                        @click="handleDownloadHotTable"
                                    ></i>
                                </el-tooltip>
                            </el-form-item>
                        </el-col>
                        <el-col v-show="activeModule === 'user'">
                            <el-form-item label-width="0">
                                <el-select
                                    v-model="form.user.type"
                                    class="short-select-wrapper"
                                    @change="user_type_change"
                                    :placeholder="$t('label.select')"
                                    style="margin-right:20px"
                                >
                                    <el-option
                                        :label="$t('statistics.rank.domainRank.tableColumn2')"
                                        :value="1"
                                    ></el-option>
                                    <el-tooltip
                                        effect="dark"
                                        placement="right"
                                        :content="$t('statistics.eas.tip2')"
                                        v-if="!hasInstName"
                                    >
                                        <el-option
                                            :disabled="!hasInstName"
                                            :label="$t('log.option2')"
                                            :value="2"
                                        ></el-option>
                                    </el-tooltip>
                                    <el-option
                                        v-if="hasInstName"
                                        :label="$t('log.option2')"
                                        :value="2"
                                    ></el-option>
                                </el-select>
                                <el-select
                                    v-model="form.user.domain"
                                    :placeholder="$t('common.searchBar.errMsg2')"
                                    filterable
                                    v-if="form.user.type !== 2"
                                >
                                    <el-option :label="$t('statistics.common.domainSelectOption')" value="" />
                                    <el-option
                                        v-for="item in domainOptions"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    />
                                </el-select>
                                <!-- 实例 下拉框 -->
                                <el-select
                                    v-model="form.user.domain"
                                    filterable
                                    v-if="form.user.type === 2"
                                    :placeholder="$t('statistics.eas.tip5')"
                                >
                                    <el-option :label="$t('statistics.eas.tip6')" value="" />
                                    <el-option
                                        v-for="opt in instNameOptions"
                                        v-show="opt.label !== '-'"
                                        :key="opt.domain"
                                        :label="opt.label"
                                        :value="opt.value"
                                    />
                                </el-select>
                            </el-form-item>
                            <el-form-item label-width="0">
                                <ct-time-picker
                                    v-model="form.user.timeRange"
                                    :periodOptions="['0', '1', '7', '30', '-1']"
                                    :maxDayBeforeNow="365"
                                    type="datetimerange"
                                    key="timePicker"
                                    size="medium"
                                />
                            </el-form-item>
                            <el-form-item label-width="0">
                                <el-button type="primary" @click="handleUpdateView">{{
                                    $t("statistics.common.searchBtn")
                                }}</el-button>
                            </el-form-item>
                        </el-col>
                    </el-form>
                    <!-- 操作按钮 -->
                    <!-- <el-button @click="resetQuery">重置</el-button> -->
                </nested-menu>
            </el-card>
            <div class="cmp-wrapper">
                <transition name="component-fade" mode="out-in">
                    <component
                        :is="getChart"
                        ref="childPane"
                        class="child-pane"
                        :instNameOptionsAll="instNameOptionsAll"
                    />
                </transition>
            </div>
        </div>
    </ct-section-wrap>
</template>

<script lang="ts">
import DomainSelect from "@/components/domainsSelect/index.vue";
import AreaSelect from "@/components/areasSelect/index.vue";
import NestedMenu from "@/views/statistics/components/nestedMenu.vue";
import { DomainUrl } from "@/config/url/ipa/domain";
import { statistics as StatisticsUrl } from "@/config/url/ipa/statistics";
import { getAm0 as get0, getLang, isTimeInMonth } from "@/utils";
import { cloneDeep, has } from "lodash-es";
import { Component, Ref, Vue } from "vue-property-decorator";
import CtTimePicker from "@/components/ctTimePicker/index.vue";
import CtTimeCompare from "@/components/ctTimeCompare/index.vue";
import { nUserModule } from "@/store/modules/nuser";
import { IpaConfigModule } from "@/store/modules/ipa/config";
import { get } from "lodash-es";

/**
 * ipa 统计分析的组织调用逻辑：
 * 当前文件作为最上层，提供搜索栏，搜索框所用到的字段各个tab是相互隔离的，查询时会通过ref调用当前激活的tab的unifyUpdateView方法
 * 具体的图表组织在对应的tab文件夹中，集成于对应tab的index.vue中，由对应的tab统一管理
 * 在对应tab的unifyUpdateView中，接收最上层搜索框传来的参数
 */

type DomainResultItem = {
    domain: string;
    inst_name: string;
};

type LabelValueItem = {
    label: string;
    value: string | number;
    domain?: string;
};

type MainMenu = "usage" | "hot" | "user";

type IspResultItem = {
    en_name: string;
    cn_name: string;
    code: string;
};

@Component({
    name: "ipa-statistics",
    components: {
        DomainSelect,
        AreaSelect,
        NestedMenu,
        CtTimePicker,
        CtTimeCompare,
    },
})
export default class IpaStatistics extends Vue {
    domainOptions: LabelValueItem[] = [];
    instNameOptions: LabelValueItem[] = [];
    instNameOptionsAll: LabelValueItem[] = [];
    ispList: LabelValueItem[] = [];
    areaOptions = [];
    isGlobal = true;
    currentMenu: {
        main: MainMenu;
        sub: string;
    } = {
        main: "usage",
        sub: "bandwidthChart",
    };
    form: {
        usage: {
            [key: string]: any;
        };
        hot: {
            [key: string]: any;
        };
        user: {
            [key: string]: any;
        };
    } = {
        usage: {
            domain: [],
            isp: [],
            area: {
                province: [],
                continent: [],
                continentRegion: [],
            },
            // tag-area更改标注
            timeRange: null,
            type: 1,
            timeCompareShow: false,
            bandwidthType: 0,
            flowType: 0,
        },
        hot: {
            sorted_by: "flow",
            type: 1,
            domain: [],
            area: {
                province: [],
                continent: [],
                continentRegion: [],
            },
        },
        user: {
            domain: "",
            type: 1,
        },
    };
    // 当前是否在初始化数据
    onInit = true;

    flowList = [
        `${this.$t("statistics.eas.flowList[0]")}`,
        `${this.$t("statistics.eas.flowList[1]")}`,
        `${this.$t("statistics.eas.flowList[2]")}`,
    ].map((label, value) => ({ label, value }));
    bandwidthList = [
        `${this.$t("statistics.eas.bandwidthList[0]")}`,
        `${this.$t("statistics.eas.bandwidthList[1]")}`,
        `${this.$t("statistics.eas.bandwidthList[2]")}`,
    ].map((label, value) => ({ label, value }));

    @Ref("childPane") readonly childPane: any;

    get headerText() {
        return {
            usage: `${this.$t("statistics.common.usage")}`,
            hot: `${this.$t("statistics.common.rank")}`,
            user: `${this.$t("statistics.common.user")}`,
        }[this.currentMenu.main];
    }

    get headerTip() {
        return {
            usage: `${this.$t("statistics.dcdn.headerTip")}`,
            hot: `${this.$t("statistics.rank.headerTip")}`,
            user: `${this.$t("statistics.user.headerTip")}`,
        }[this.currentMenu.main];
    }

    get getDomainOrInstOption() {
        return this.form.hot.type === 1 ? this.domainOptions : this.instNameOptions;
    }

    get domainPlaceholder() {
        let placeholder = `${this.$t("statistics.common.domainSelectOption")}`;
        if (this.form.hot.type === 2) {
            placeholder = `${this.$t("statistics.eas.tip6")}`;
        }
        return placeholder;
    }

    get isCtcloud() {
        return nUserModule.isCtclouds;
    }

    get getMenuList() {
        const defaultMenuList = [
            {
                name: `${this.$t("statistics.common.usage")}`,
                prop: "usage",
                children: [
                    {
                        name: `${this.$t("statistics.eas.tab[0]")}`,
                        prop: "bandwidthChart",
                    },
                    {
                        name: `${this.$t("statistics.eas.tab[1]")}`,
                        prop: "flowChart",
                    },
                    {
                        name: `${this.$t("statistics.eas.tab[2]")}`,
                        prop: "connectionChart",
                    },
                    {
                        name: `${this.$t("statistics.eas.tab[3]")}`,
                        prop: "regionISP",
                    },
                ],
            },
            {
                name: `${this.$t("statistics.common.rank")}`,
                prop: "hot",
                children: [
                    {
                        name: `${this.$t("statistics.eas.tab[4]")}`,
                        prop: "domainRanking",
                    },
                    {
                        name: `${this.$t("statistics.eas.tab[5]")}`,
                        prop: "topClientIp",
                    },
                ],
            },
            {
                name: `${this.$t("statistics.common.user")}`,
                prop: "user",
            },
        ];

        if (this.isCtcloud) {
            defaultMenuList.pop();
        }

        return defaultMenuList;
    }

    get getMainMenu() {
        return this.currentMenu.main || "usage";
    }

    get getSubMenu() {
        return this.currentMenu.sub || "";
    }

    get getChart() {
        return this.currentMenu.sub
            ? () => import(`./components/${this.getMainMenu}/${this.getSubMenu}.vue`)
            : () => import(`./components/${this.getMainMenu}/index.vue`);
    }

    get activeModule(): MainMenu {
        return this.currentMenu.main;
    }
    get usageDomainPlaceholder() {
        let message = `${this.$t("statistics.common.domainSelectOption")}`;
        if (this.form.usage.type === 2) {
            message = `${this.$t("statistics.eas.tip6")}`;
        }
        return message;
    }

    get usageDomainInstNameOptions() {
        let data = cloneDeep(this.domainOptions);
        if (this.form.usage.type === 2) {
            data = cloneDeep(this.instNameOptions);
        }
        return data;
    }
    get domainType() {
        let data = "domain";
        if (this.form.usage.type === 2) {
            data = "inst_name";
        }
        return data;
    }

    get lang() {
        return getLang();
    }

    get isBwOrFlow() {
        return (
            this.getMainMenu === "usage" &&
            (this.getSubMenu === "bandwidthChart" || this.getSubMenu === "flowChart")
        );
    }

    get hasInstName() {
        let data = false;
        data = this.instNameOptionsAll?.some(item => item.label !== "-");
        return data;
    }

    mounted() {
        Promise.all([this.getIspList(), this.getDomainList(), this.getAreaList()])
            .then(() => {
                this.onInit = false;
                this.handleUpdateView();
            })
            .catch(() => {
                this.onInit = false;
            })
            .finally(() => {
                this.$ctBus.$on("childPaneMounted", this.handleUpdateView);
            });
    }

    beforeDestroy() {
        this.$ctBus.$off("childPaneMounted", this.handleUpdateView);
    }

    onHotTypeChange() {
        this.form.hot.domain = [];
    }

    resetQuery() {
        this.$set(this.form, this.activeModule, {
            ...(this.$options as any).data().form[this.activeModule],
            timeRange: this.getTodayArr(),
        });
        // 重置当前子组件的查询选项
        this.childPane?.resetQuery();
        this.handleUpdateView();
    }
    getTodayArr() {
        const today0 = get0(new Date());
        const today24 = new Date(+today0 + 24 * 60 * 60 * 1000 - 1000);
        return [today0, today24];
    }

    // 域名限制数量
    get domainCountLimit() {
        return IpaConfigModule.ipaDomainSelectorLimit;
    }

    /**
     * 处理域名限制的逻辑
     */
    private isDomainLimit(form: Record<string, any>) {
        // 通用判断 当数组情况，实例 或者 域名 情况下通传数组
        const length = get(form, "domain.length");
        if (length > this.domainCountLimit) {
            return true;
        }

        // 根据后端要求，全选和不选，表示不传递日志这个字段，所以不需要判断大小
        // const domain = form.domain;
        // const type = form.type; // type 1 域名 2 实例
        // 当domain 为空字符或者空数组，表示数组初始化全选;当 form 没有domain 字段表示 域名情况下，全选了。
        // if(domain === "" || get(domain, "length") === 0 || !has(form, "domain")) {
        //     const length = type === 1 ? this.domainOptions.length : this.instNameOptions.length;
        //     return length > this.domainCountLimit
        // }

        return false;
    }

    // 组装查询参数，并传递给子组件
    handleUpdateView() {
        if (this.onInit) return;

        const payload: {
            [key: string]: any;
        } = {
            ...this.form[this.activeModule],
        };

        if (this.isBwOrFlow) {
            payload.type = payload[this.getSubMenu === "bandwidthChart" ? "bandwidthType" : "flowType"];
            delete payload.bandwidthType;
            delete payload.flowType;
        }

        if (payload.timeRange === null) {
            // 默认查询今天
            // payload.timeRange = this.getTodayArr();
            // 提示选择时间
            this.$message.error(`${this.$t("log.info3")}`);
            return;
        }

        // 用户分析 域名需要转为数组格式
        if (this.activeModule === "user") {
            payload.domain = payload.domain && [payload.domain];
        }

        // hot
        if (this.activeModule === "hot") {
            if (this.currentMenu.sub === "domainRanking") {
                delete payload.area;
            }

            if (payload.domain?.length === this.domainOptions.length && this.form.hot.type === 1) {
                delete payload.domain;
            }
            if (payload.domain?.length === 0 && this.form.hot.type === 2) {
                payload.domain = this.instNameOptions.map(itm => itm.value);
            }
        } else {
            if ((payload.domain && payload.domain.length === 0) || (payload && payload.domain === "")) {
                if (this.form.usage.type === 2 && this.activeModule === "usage") {
                    payload.domain = this.instNameOptions.map(itm => itm.value);
                }
                if (this.form.user.type === 2 && this.activeModule === "user") {
                    payload.domain = this.instNameOptions.map(itm => itm.value);
                }
            }
            if (
                payload.domain &&
                payload.domain.length === this.domainOptions.length &&
                this.form.usage.type === 1
            ) {
                delete payload.domain;
            }
        }

        // 数量判断放在这里
        const isLimit = this.isDomainLimit(payload);
        if (isLimit) {
            this.$message.error(
                `当前域名/实例超过${this.domainCountLimit}个，请先选择${this.domainCountLimit}个以内域名/实例`
            );
            return;
        }

        // 区域选择涉及参数调整
        payload.province = payload.area?.province;
        payload.continent_code = payload.area?.continent;
        payload.continent_region_code = payload.area?.continentRegion;
        // 查询全部地区(-1)则默认不传province参数
        if (payload.province?.includes("-1")) {
            payload.province = [];
        }
        delete payload.area;

        // 删除无用参数
        delete payload.timeCompareShow;

        // 校验时间范围是否在一个月内
        if (payload.timeCompare) {
            if (
                (payload.timeCompare &&
                    !isTimeInMonth(payload.timeCompare[0][0] / 1000, payload.timeCompare[0][1] / 1000)) ||
                (payload.searchParams2 &&
                    !isTimeInMonth(payload.timeCompare[1][0] / 1000, payload.timeCompare[1][1] / 1000))
            ) {
                return this.$message.error(this.headerTip);
            }
        } else if (payload.timeRange) {
            if (
                payload.timeRange &&
                !isTimeInMonth(payload.timeRange[0] / 1000, payload.timeRange[1] / 1000)
            ) {
                return this.$message.error(this.headerTip);
            }
        }

        // 调用二级组件的统一代理方法
        this.$nextTick(() => {
            this.childPane?.updateAgency(payload, this.getSubMenu || this.getMainMenu, {
                useCompare: this.form[this.activeModule].timeCompareShow,
                timeRangeArr: this.form[this.activeModule].timeRange,
            });
        });
    }
    // 获取域名列表
    async getDomainList() {
        const { result } = await this.$ctFetch<{ result: DomainResultItem[] }>(DomainUrl.domainListNew, {
            method: "GET",
            transferType: "json",
            data: {
                account_id: this.$store.state.user.userInfo.userId,
            },
            cache: true,
        });
        this.domainOptions = result.map(item => {
            return {
                label: item.domain,
                value: item.domain,
            };
        });
        let instNameOptions = [];
        instNameOptions = result?.map(item => {
            return {
                label: item.inst_name,
                value: item.domain,
            };
        });
        this.instNameOptionsAll = cloneDeep(instNameOptions);
        this.instNameOptions = instNameOptions?.filter(item => item.label !== "-");
    }
    // 获取运营商列表
    async getIspList() {
        const { result } = await this.$ctFetch<{
            result: IspResultItem[];
        }>(StatisticsUrl.ispList, {
            method: "GET",
            transferType: "json",
            data: {
                account_id: this.$store.state.user.userInfo.userId,
            },
            cache: true,
        });

        // 涉及【运营商】选择时，下拉选项框隐藏“多线”和“BGP”选项
        const ispOptions = result
            .filter((item: IspResultItem) => !["007", "008"].includes(`${item.code}`))
            .map(item => {
                return {
                    label: this.lang === "en" ? item.en_name : item.cn_name,
                    value: Number(item.code),
                };
            });
        // ’其他‘选项默认放到最后
        const others = ispOptions.filter(isp => +isp.value === 100);
        const exceptOthers = ispOptions.filter(isp => +isp.value !== 100);
        this.ispList = [...exceptOthers, ...others];
    }
    async getAreaList() {
        const { result } = await this.$ctFetch<{
            result: {
                children: [];
                isGlobal: boolean;
            };
        }>(StatisticsUrl.areaList, {
            method: "GET",
            transferType: "json",
            cache: true,
        });
        this.areaOptions = result.children;
        this.isGlobal = result.isGlobal;
    }
    typeChange() {
        this.form.usage.domain = [];
    }
    user_type_change() {
        this.form.user.domain = "";
    }
    // 下载地区运营商表格
    handleDownloadRegionTable() {
        this.$ctBus.$emit("ipa:download:regionISP");
    }
    // 下载热门分析表格
    handleDownloadHotTable() {
        this.$ctBus.$emit(`ipa:download:hot:${this.getSubMenu}`);
    }
}
</script>

<style lang="scss" scoped>
.statistics-scroll-wrapper {
    height: 100%;
}

.search-form-wrapper {
    ::v-deep .el-form-item {
        margin-bottom: 12px !important;
    }

    &-domain {
        ::v-deep {
            .domain-select__item {
                line-height: 1.5 !important;
            }
        }
    }

    &-time {
        display: flex;
        flex-wrap: wrap;

        &-tip {
            color: $text-color-feature;
            align-self: center;
            font-size: 12px;
            margin-bottom: 12px;
        }
    }
}

.compare-show {
    margin-left: 12px;
}

.cmp-wrapper {
    margin-top: 20px;

    ::v-deep .usage-download-icon {
        margin-left: 14px;
    }
}

.ipa-statistics-section-wrapper {
    ::v-deep .ipa-download-icon {
        margin-left: 12px;
        color: #606266;
        font-size: 14px;
        vertical-align: middle;
        cursor: pointer;

        &:hover {
            color: $color-master;
        }
    }

    ::v-deep.el-scrollbar__wrap {
        margin-bottom: 0 !important;
    }
}

.cmp-wrapper {
    ::v-deep.el-card {
        max-height: 600px !important;
    }
}

.short-select-wrapper {
    width: 92px;
}

.component-fade-enter-active,
.component-fade-leave-active {
    transition: opacity 0.3s ease;
}

.component-fade-enter,
.component-fade-leave-to {
    opacity: 0;
}
</style>
