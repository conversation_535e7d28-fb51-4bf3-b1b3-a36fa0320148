{"title": "Billing Details", "tips": {"tip1": "1. Three billing modes are available on the official website: pay-by-daily-traffic, pay-by-daily-peak-bandwidth, and pay-by-traffic-package. If you want to change the billing mode, contact your account manager to change it offline.", "tip2": "2. For more information about billing modes, go to the document center on the official website.", "tip3": "3. Changes to the billing mode take effect on the next day or month. For the pay-by-daily-traffic, pay-by-daily-peak-bandwidth, and pay-by-traffic-package billing modes, the change takes effect at 00:00 on the next day.", "tip4": "4. For CDN billing, bytes are converted into Gbit/s according to the base 1000 system."}, "tabs": {"tab1": "Pay-As-You-Go", "tab2": "Resource Plans", "tab3": "History"}, "productTable": {"column1": "Acceleration Type", "column2": "Billing Mode", "column3": "Effective At", "column4": "Expire On"}, "noProductTip": {"part1": "Pay-as-you-go is not activated. ", "part2": "Go to activate a pay-as-you-go CDN service."}, "billingMethod": {"type1": "Traffic", "type2": "Daily peak bandwidth"}, "btn": {"btn1": "Change"}, "billingNote": {"title": "Tips for changing the billing method:", "note1": "1. On the eSurfing Cloud CDN console, you can choose to pay by \"traffic\" or \"daily bandwidth peak\" for pay-as-you-go products. Note: CDN acceleration Global(excluding Chinese mainland) does not support billing method altering currently.", "note2": "2. When the billing method is changed from \"daily bandwidth peak\" to \"traffic\", it takes effect at 00:00 the next day.", "note3": "3. When the billing method is changed from \"traffic\" to \"daily bandwidth peak\", it takes effect at 00:00 the next day. If a traffic package has already been ordered, the remaining amount of the traffic package will be frozen. During the freezing period, the validity period of the traffic package will not be extended. After the billing method is restored to pay-by-traffic, the traffic package can continue to be deducted.", "note4": "4. Before the billing method change takes effect, you can change it multiple times, and the final change will apply.", "note5": "2. Among eSurfing Cloud CDN products, it is mandatory to keep identical billing method for ICDN(Chinese mainland), ICDN-upload acceleration and ICDN-websocket acceleration services. If you change the billing method of anyone of them, it will be applied to the other two services at the same time. "}, "confirmTip": {"title": "Reminder", "btn1": "Go", "btn2": "Cancel", "tip1": "Pay-as-you-go is not activated. Go to activate a pay-as-you-go CDN service."}, "changeProduct": {"tip": "Are you sure to change the billing mode?<br /> After your change request is submitted, the new billing mode will apply at 00:00 on the next day. Please operate with caution.", "msg": "The billing mode change request is submitted."}, "common": {"itm1": "OK", "itm2": "Cancel", "itm3": "Note", "itm4": "Status", "itm5": "Reset", "itm6": "No.", "itm7": "Unknown"}, "resourcePackage": {"optionLabel": "All Acceleration Types", "allStatus": "All Status", "package": "Resource Package", "usage": "Usage", "already": "Used: ", "total": "Total: ", "used": "Not used", "activate": "Activated but not used"}, "productStatusMap": {"itm1": "In Service", "itm2": "Expired", "itm3": "Unsubscribed", "itm4": "Frozen (Paused)", "itm5": "Deleted", "itm6": "Closed", "itm7": "Freezing", "itm8": "Recovering", "itm9": "Deleting (Closing)", "itm10": "Other"}, "packageStatusOptions": {"itm1": "Not used", "itm2": "In use", "itm3": "Traffic run out", "itm4": "Expired", "itm5": "Disable", "itm6": "Unsubscribe", "itm7": "Freeze"}, "history": {"methodRecord": "Records of Pay-as-you-go Services", "submit": "Submitted On"}, "productUnitMap": {"1": "{count} times", "2": "{count}GB | {count}GB"}}