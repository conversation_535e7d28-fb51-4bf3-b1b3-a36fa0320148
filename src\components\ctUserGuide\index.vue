<template>
    <div class="ct-user-guide">
        <Popper ref="popper" v-model="showPopper" placement="right" />
        <!--            <div ref="fly-piece" v-show="showPopper" class="my-picker__popper">你看，我弹出来了</div>-->
        <transition name="dialog-fade">
            <div v-show="showPopper" class="popper-overlay">
                <div v-show="showPopper" ref="fly-piece" class="my-picker__popper" :style="boxStyle">
                    <!--  头部-->
                    <slot name="header">
                        <div class="flex-row-style header">
                            <span>欢迎使用天翼云网站安全监测服务！</span>
                            <i class="el-icon-close" @click="handleClose" />
                        </div>
                    </slot>
                    <!--  主体-->
                    <slot>
                        <div class="guide-main">
                            <div class="text-main">
                                {{ currentShowText }}
                            </div>
                            <div class="flex-row-style footer">
                                <div v-if="renderData.length" class="flex-row-style step-index">
                                    <template v-if="renderData.length > 1">
                                        <span
                                            v-for="(item, key) in renderData"
                                            :key="key"
                                            class="icon-step"
                                            :class="{ active: currentIndex >= key }"
                                        />
                                    </template>
                                </div>
                                <el-button type="primary" @click="handleNext">
                                    {{ buttonText }}
                                </el-button>
                            </div>
                        </div>
                    </slot>
                </div>
            </div>
        </transition>
    </div>
</template>

<script>
import Popper from "./popper";
import { get } from "lodash-es";

export default {
    name: "index",
    components: {
        Popper,
    },
    props: {
        width: {
            type: [String, Number],
            default: 400,
        },
        renderData: {
            type: Array,
            default: () => [],
        },
        positionList: {
            type: Array,
            default: () => [],
        },
    },
    data() {
        return {
            showPopper: false,
            currentIndex: 0,
            currentSubIndex: 0,
            domList: [],
        };
    },
    computed: {
        boxStyle() {
            return {
                width: this.width + "px",
            };
        },
        currentItem() {
            return get(this.renderData, this.currentIndex);
        },
        currentData() {
            return get(this.currentItem, "data", []) || [];
        },
        currentShowText() {
            return get(get(this.currentData, this.currentSubIndex), "text");
        },
        buttonText() {
            const dataLength = this.renderData.length - 1;
            const subDataLength = this.currentData.length - 1;
            if (this.currentIndex === dataLength && this.currentSubIndex === subDataLength) {
                return "开始使用";
            }

            return "下一步";
        },
        currentReferenceDom() {
            const nodeFilter = get(this.currentItem, "nodeFilter");
            if (nodeFilter && typeof nodeFilter === "function") {
                return nodeFilter();
            }

            const node = get(this.currentItem, "node");
            if (node) {
                const dom = document.querySelector(node);
                return dom;
            }

            const nodeIndex = get(this.currentItem, "nodeIndex");
            const dom = get(this.positionList, nodeIndex);
            return dom;
        },
    },
    watch: {
        showPopper: {
            handler(val) {
                if (!val) {
                    this.currentIndex = 0;
                    this.currentSubIndex = 0;
                }
            },
        },
        // 必须监听当前节点内容变化后，待dom更新后再去调用popper，否则当高度变化时，会引起位置显示的bug
        currentSubIndex: {
            handler() {
                this.$nextTick(() => {
                    this.updatePosition();
                });
            },
        },
        currentIndex: {
            handler() {
                this.$nextTick(() => {
                    this.$emit("dom-change", this.currentReferenceDom);
                    this.updatePosition();
                });
            },
            immediate: true,
        },
        currentReferenceDom: {
            handler(dom) {
                if (!dom) {
                    return;
                }

                this.domList.push(dom);
            },
            immediate: true,
        },
    },
    methods: {
        /**
         * 打开用户向导
         */
        openUserGuide() {
            this.$nextTick(() => {
                const dom = this.currentReferenceDom;
                if (!dom) {
                    return;
                }

                this.$refs.popper.popperElm = this.$refs["fly-piece"];
                this.$refs.popper.referenceElm = dom;
                this.showPopper = true;
            });
        },
        /**
         * 更新位置,支持外部调用方法
         */
        updatePosition(el) {
            const dom = this.currentReferenceDom || el;
            if (!dom) {
                return;
            }

            this.$refs.popper.popperElm = this.$refs["fly-piece"];
            this.$refs.popper.referenceElm = dom;
            this.$refs.popper.updateDom(dom);
        },
        /**
         * 处理下一步
         */
        handleNext() {
            let subIndex = this.currentSubIndex + 1;
            if (subIndex < this.currentData.length) {
                this.currentSubIndex = subIndex;
                return;
            }

            subIndex = 0;
            this.currentSubIndex = subIndex;
            const currentIndex = this.currentIndex + 1;
            if (currentIndex < this.renderData.length) {
                this.currentIndex = currentIndex;
                return;
            }

            this.handleClose(true);
        },
        /**
         * 处理关闭
         */
        async handleClose(isEnd) {
            if (isEnd === true) {
                this.showPopper = false;
                this.$emit("close", this.domList);
                return;
            }

            await this.$confirm("确定退出用户向导吗？", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            });

            this.showPopper = false;
        },
    },
};
</script>

<style scoped lang="scss">
@import "./index.scss";
</style>
