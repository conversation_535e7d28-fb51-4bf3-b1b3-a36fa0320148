<template>
    <div>
        <!-- 内容区域-->
        <div class="add-origin-btn">
            <el-button type="primary" :disabled="isAddDisabled" @click="handleAdd">+ 添 加</el-button>
        </div>
        <div class="form-item-tip">
            <ct-svg-icon icon-class="info-circle" class-name="alert-icon"></ct-svg-icon>
            支持IP或域名，最多可添加60个
        </div>
        <el-table :data="data" header-align="left">
            <el-table-column type="index" label="序号" width="80px"> </el-table-column>
            <el-table-column label="源站" prop="address"></el-table-column>
            <el-table-column
                label="角色"
                prop="role"
                :formatter="row => ['', '主源', '备源'][row.role]"
            ></el-table-column>
            <el-table-column label="层级" prop="level"></el-table-column>
            <el-table-column label="权重" prop="weight"></el-table-column>
            <el-table-column label="操作">
                <template v-slot="{ row, $index }">
                    <el-button type="text" @click="handleEdit(row, $index)">修改</el-button>
                    <el-button type="text" @click="handleDelete($index, row)">删除</el-button>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<script>
import ctSvgIcon from "@/components/ctSvgIcon/index.vue";
import { get } from "lodash-es";

export default {
    components: { ctSvgIcon },
    props: {
        allData: {
            type: Array,
            default: () => []
        },
        data: {
            type: Array,
            default: () => []
        },
        // 页面类型，分新增和编辑。某些操作需要在编辑二次确认
        pageType: {
            type: String,
            default: "add"
        },
    },
    inject: {
        elForm: {
            default: "",
        },
    },
    computed: {
        isAddDisabled() {
            // 源站个数总和上限校验（60个），是所有转发组总和
            const totalLength = this.allData.reduce((total, item) => {
                const length = get(item, "detail.length") || 0;
                return total + length;
            }, 0);

            return this.elForm && this.elForm.disabled || totalLength >= 60
        },
    },
    methods: {
        /**
         * 添加源站
         */
        handleAdd() {
            this.$emit("add");
        },
        /**
         * 编辑
         */
        handleEdit(row, $index) {
            this.$emit("edit", row, $index);
        },
        /**
         * 删除
         */
        async handleDelete(index, row) {
            await this.$confirm(`确认要删除所选择的源站 ${row.address} 吗？`, "删除", {
                confirmButtonText: "确定",
                type: "warning",
            })

            this.data.splice(index, 1);
        }
    }
}
</script>

<style scoped lang="scss">
.form-item-tip {
    margin-top: $margin;
}
</style>
