<!--
 * @Description: 用户分析
 * @Author: wang yuegong
-->
<template>
    <div class="user-wrapper">
        <!-- 访问用户区域 -->
        <user-area ref="userArea" class="user-area" @downloadExcel="downloadExcel" />

        <!-- 独立 ip (uv) -->
        <user-uv ref="userUv" class="user-uv" @downloadExcel="downloadExcel" />

        <!-- 运营商 -->
        <user-isp ref="userIsp" class="user-isp" @downloadExcel="downloadExcel" />
    </div>
</template>
<script lang="ts">
import { Component, Vue, Ref } from "vue-property-decorator";
import { SearchParams } from "@/types/statistics/user";
import { timeFormat } from "@/filters/index";
import { downloadCsv } from "@/utils";
import UserArea from "./components/UserArea.vue";
import UserUv from "./components/UserUv.vue";
import UserIsp from "./components/UserIsp.vue";

@Component({
    name: "User",
    components: { UserArea, UserUv, UserIsp },
})
export default class User extends Vue {
    searchParams: SearchParams = {
        startTime: 0,
        endTime: 0,
        domainList: [],
        product: [],
    }

    @Ref("userArea") readonly areaCom!: UserArea;
    @Ref("userUv") readonly uvCom!: UserUv;
    @Ref("userIsp") readonly ispCom!: UserIsp;

    childPaneStartLoading() {
        this.areaCom.loading = true;
        this.uvCom.loading = true;
        this.ispCom.loading = true;
    }

    async beforeGetData(params: SearchParams) {
        this.searchParams = params;
        this.areaCom.getData(params);
        this.uvCom.getData(params);
        this.ispCom.getData(params);
    }

    // 通用下载方法，如果需要定制则重写
    downloadExcel({ name, str }: { name: string; str: string }) {
        let t1 = timeFormat(+this.searchParams.startTime);
        let t2 = timeFormat(+this.searchParams.endTime);

        // 通用的输出内容
        let preStr = `${this.$t("order.startTime")}:,${t1}\n` + `${this.$t("order.endTime")}:,${t2}\n`;
        preStr += `${this.$t("statistics.provider.statisticsDomain")}:,${this.searchParams.domainList!.length === 1 ? this.searchParams.domainList : `${this.$t("statistics.provider.all")}`
            }\n`;
        preStr += `${this.$t("statistics.eas.tip27")}:,${new Date().toLocaleString()}\n\n`;

        t1 = t1.replace(/-|\s|:/g, "").slice(0, 12);
        t2 = t2.replace(/-|\s|:/g, "").slice(0, 12);

        downloadCsv(`${name}${t1}-${t2}`, preStr + str);
    }
}
</script>

<style lang="scss" scoped>
// 各子模块
.user-wrapper {
    ::v-deep {
        .user-section {
            margin-bottom: 10px;
            display: inline-block;
            @include g-width(100%, 100%, 50%);
            vertical-align: middle;
        }

        .download-icon {
            margin-left: 14px;
            color: #606266;
            font-size: 14px;
            vertical-align: middle;
            cursor: pointer;

            &:hover {
                color: $color-master;
            }
        }
    }
}

.user-uv,
.user-isp {
    height: 378px;
}

.user-area {
    min-height: unset !important;
    margin-bottom: 20px;
}
</style>
