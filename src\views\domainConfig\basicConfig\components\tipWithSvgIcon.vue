<!--
    使用说明：
    1. 使用时，需要传入text属性，或者在组件内部使用slot传入内容
    2. 组件内部会显示一个svg图标，以及传入的text内容，图标默认使用info-circle，如果需要使用其他图标，请传入icon-class属性
-->
<template>
    <span class="tip-with-svg-icon" :style="{ paddingLeft: paddingLeft }">
        <ct-svg-icon :icon-class="iconClass" class="tip-icon" />
        <span class="tip-text">
            <slot>{{ text }}</slot>
        </span>
    </span>
</template>

<script lang="ts">
import ctSvgIcon from "@/components/ctSvgIcon/index.vue";
import { Vue, Component, Prop } from "vue-property-decorator";

@Component({
    components: {
        ctSvgIcon,
    },
})
export default class TipWithSvgIcon extends Vue {
    @Prop({ type: String, required: false }) text?: string;
    @Prop({ type: String, required: false, default: "info-circle" }) iconClass?: string;
    @Prop({ type: String, required: false, default: "0" }) paddingLeft?: string;
}
</script>

<style lang="scss" scoped>
.tip-with-svg-icon {
    display: inline-block;
    height: 18px;
    font-size: 12px;
    color: $color-neutral-7;
    line-height: 18px;
    font-weight: 400;
}
.tip-icon {
    color: $text-color-light;
    margin-right: $margin;
    font-size: $text-size-md;
    line-height: 18px;
    height: 14px;
    width: 14px;
}
</style>
