import { RouteConfig } from "vue-router";
import { DomainModule } from "@/store/modules/domain";
import { DomainActionEnum, getDomainAction } from "@/config/map";
import { nUserModule } from "@/store/modules/nuser";

const statisticsRouter: RouteConfig = {
    path: "/nreview",
    name: "nreview",
    component: () => import("./index.vue"),
    redirect: {
        name: "nreview.statistics",
    },
    meta: {
        breadcrumb: {
            title: "内容审核",
            route: ["nreview"],
        },
    },
    children: [
        // {
        //     path: "settings",
        //     name: "nreview.settings",
        //     component: Settings,
        //     meta: {
        //         breadcrumb: {
        //             title: "设置",
        //             route: ["nreview", "nreview.settings"],
        //         },
        //         perm: "nreview.settings",
        //     },
        // },
        {
            path: "statistics",
            name: "nreview.statistics",
            component: () => import("./statistics/index.vue"),
            meta: {
                breadcrumb: {
                    title: "审核分析",
                    route: ["nreview", "nreview.statistics"],
                },
                perm: "nreview.statistics",
            },
            beforeEnter(to, from, next) {
                // 不阻塞页面加载
                const isFcdnCtyunCtclouds = nUserModule.isFcdnCtyunCtclouds;
                if (isFcdnCtyunCtclouds) {
                    DomainModule.GetDomainList({ action: getDomainAction("ContentAuditStatistics") });
                } else {
                    DomainModule.GetDomainList({ action: DomainActionEnum.Review });
                }
                next();
            },
        },
        {
            path: "violations",
            name: "nreview.violations",
            component: () => import("./violations/index.vue"),
            meta: {
                breadcrumb: {
                    title: "违规图片",
                    route: ["nreview", "nreview.violations"],
                },
                perm: "nreview.violations",
            },
            beforeEnter(to, from, next) {
                // 不阻塞页面加载
                const isFcdnCtyunCtclouds = nUserModule.isFcdnCtyunCtclouds;
                if (isFcdnCtyunCtclouds) {
                    DomainModule.GetDomainList({ action: getDomainAction("ContentAuditViolations") });
                } else {
                    DomainModule.GetDomainList({ action: DomainActionEnum.Review });
                }
                next();
            },
        },
        {
            path: "log",
            name: "nreview.log",
            component: () => import("./log/index.vue"),
            meta: {
                breadcrumb: {
                    title: "审核日志",
                    route: ["nreview", "nreview.log"],
                },
                perm: "nreview.log",
            },
            beforeEnter(to, from, next) {
                // 不阻塞页面加载
                const isFcdnCtyunCtclouds = nUserModule.isFcdnCtyunCtclouds;
                if (isFcdnCtyunCtclouds) {
                    DomainModule.GetDomainList({ action: getDomainAction("ContentAuditLog") });
                } else {
                    DomainModule.GetDomainList({ action: DomainActionEnum.Review });
                }
                next();
            },
        },
        {
            path: "disable",
            name: "nreview.disable",
            component: () => import("./disable.vue"),
            meta: {
                breadcrumb: {
                    title: "内容审核",
                    // 为避免点击面包屑CDN内容审核时跳转到无权访问的页面，因此route中不包含review字段
                    route: ["nreview.disable"],
                },
                perm: "nreview.disable",
            },
        },
    ],
};

export default statisticsRouter;
