/*
 * @Description: 校验规则（正则、提示语），考虑多个正则的拼接需求，调整提供方式
 * @Author: wang y<PERSON>
 */

import commonPattern from "@cdnplus/common/config/pattern";

// 1、导出通用的正则表达式
export * from "@cdnplus/common/config/pattern";
// 私有正则
// 域名
export const email = "^\\w+(?:\\.{0,1}[\\w-]+)*@[a-zA-Z0-9]+(?:[-.][a-zA-Z0-9]+)*\\.[a-zA-Z]+$";

// 2、导出通用的 validator
export default {
    ...commonPattern,

    // 私有 validator
};
