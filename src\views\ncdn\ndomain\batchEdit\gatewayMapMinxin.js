// 批量修改
export default {
  data() {
      return {
          defaultTabs: [
            //   {
            //       label: this.$t("domain.detail.tab2"),
            //       prop: "basic",
            //       name: this.$t("domain.detail.tab2"),
            //       data: "basicData",
            //       error: false,
            //   },
              {
                  label: this.$t("domain.detail.tab3"),
                  prop: "origin",
                  name: this.$t("domain.detail.tab3"),
                  data: "originData",
                  error: false,
                  children: [
                      {
                          label: this.$t("domain.detail.tab4"),
                          prop: "sourceStation",
                          name: this.$t("domain.detail.tab4"),
                          data: "sourceStationData",
                          error: false,
                      },
                      {
                          label: this.$t("domain.detail.label26"),
                          prop: "httpReqHeader",
                          name: this.$t("domain.detail.label26"),
                          data: "httpReqHeaderData",
                          error: false,
                      },
                  ],
              },
              {
                  label: this.$t("domain.detail.tab6"),
                  prop: "cache",
                  name: this.$t("domain.detail.tab6"),
                  data: "cacheData",
                  error: false,
                  children: [
                      {
                          label: this.$t("domain.detail.tab7"),
                          prop: "filetypeTtl",
                          name: this.$t("domain.detail.tab7"),
                          data: "filetypeTtlData",
                          error: false,
                      },
                      {
                          label: this.$t("domain.detail.tab8"),
                          prop: "errorCode",
                          name: this.$t("domain.detail.tab8"),
                          data: "errorCodeData",
                          error: false,
                      },
                      {
                          label: this.$t("domain.detail.tab9"),
                          prop: "respHeaders",
                          name: this.$t("domain.detail.tab9"),
                          data: "respHeadersData",
                          error: false,
                      },
                  ],
              },
              {
                  label: this.$t("domain.create.accessControl"),
                  prop: "deny",
                  name: this.$t("domain.create.accessControl"),
                  data: "denyData",
                  error: false,
                  children: [
                      {
                          label: this.$t("domain.create.referer"),
                          prop: "refererChain",
                          name: this.$t("domain.create.referer"),
                          data: "refererChainData",
                          error: false,
                      },
                      {
                          label: this.$t("domain.detail.label15"),
                          prop: "ipBlackWhiteList",
                          name: this.$t("domain.detail.label15"),
                          data: "ipBlackWhiteListData",
                          error: false,
                      },
                  ],
              },
          ],
      };
  },
};
