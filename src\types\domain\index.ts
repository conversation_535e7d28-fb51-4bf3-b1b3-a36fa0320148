import { OperationItem, ProductCode } from "@/store/types";

export * from "@cdnplus/common/types/domain";

// 直播加速产品
export const LiveDomainTypeEnum = <const>{
    Pull: "1",
    Push: "2",
};

export type LiveDomainType = typeof LiveDomainTypeEnum[keyof typeof LiveDomainTypeEnum];

// 域名 item
export interface DomainItem {
    domain: string;
    domainType?: number | string; // 直播产品的域名类型
    enable: string;
    status: number; // 域名状态
    cname: string;
    subProductCode?: ProductCode; // 二级产品
    productCode: ProductCode; // 产品 code
    productName: string; // 产品中文名称
    insertDate: string; // 创建时间
    operation: OperationItem[]; // 操作按钮信息
    billingMode?: number; //
    recordNum?: string; // 备案号
    createId: string;
    signature: string; // 签名
    areaScope: number; // 加速区域（针对于CDN加速），1、国内；2、海外；3、国内+海外；缺省：1
    index: number; // 编号
    label?: string; //统计分析和日志下载域名搜索框显示处理
    hasOrder: boolean;
    antiFraudFlag: number; // 0是正常域名，1是反诈域名
    ipv6_switch: number; // ipv6开关 1开 2关
    creating: string;
    origins?: {
        origin: string;
        role: string;
        weight: number;
        origin_host: string;
    }[];
    http_origin_port?: number;
    https_origin_port?: number;
    req_host?: string; // 回源host
    follow_request_backport?: number; // 是否跟随请求端口回源 1是 2否
}
