import { get } from "lodash-es";
export default {
    data() {
        return {
            // 源站？下拉选项
            levelList: [
                {
                    value: 1,
                    label: "1",
                },
            ],
            levelList5: [
                {
                    value: 1,
                    label: "1",
                },
                {
                    value: 2,
                    label: "2",
                },
                {
                    value: 3,
                    label: "3",
                },
                {
                    value: 4,
                    label: "4",
                },
                {
                    value: 5,
                    label: "5",
                },
            ],
            access_mode_label(access_mode) {
                let name = "";
                switch (access_mode) {
                    case 1:
                        name = "域名接入方式";
                        break;
                    case 2:
                        name = "无域名接入方式";
                        break;
                    default:
                        name = "";
                        break;
                }
                return name;
            },
            // 端口校验规则
            portCheckRules: null,
            rules: {},
        };
    },

    computed: {},

    filters: {},
    created() {
        // this.getDomainPortInfo();
    },

    methods: {
        // 检查TCP端口
        async validateTcpPort(rule, value, callback) {
            if (!this.portCheckRules) {
                await this.getDomainPortInfo();
            }

            const forbidPort = get(this.portCheckRules, "port_tcp_checker", []);
            const startPort = get(this.portCheckRules, "port_tcp_forbid_start");
            const endPort = get(this.portCheckRules, "port_tcp_forbid_end");
            const values = value.split(",");

            const res = values.every(item => {
                // 区间情况
                if (item.indexOf("-") > -1) {
                    const region = item.split("-");
                    const start = Number(region[0]);
                    const end = Number(region[1]);
                    if (start >= startPort && start <= endPort) {
                        return false;
                    }

                    if (end >= startPort && end <= endPort) {
                        return false;
                    }

                    // 禁用端口包含在区间中
                    return forbidPort.every(forbid => {
                        if (forbid >= start && forbid <= end) {
                            return false;
                        }
                        return true;
                    });
                }

                // 非区间,落于禁用区间
                if (Number(item) >= startPort && Number(item) <= endPort) {
                    return false;
                }

                // 含有禁用数组的元素
                if (forbidPort.indexOf(Number(item)) > -1) {
                    return false;
                }

                return true;
            });
            if (!res) {
                return callback(new Error(`端口已被系统占用，请更换端口`));
            }

            return callback();
        },
        // 检查UDP端口
        async validateUdpPort(rule, value, callback) {
            if (!value) {
                return callback();
            }

            if (!this.portCheckRules) {
                await this.getDomainPortInfo();
            }

            const forbidPort = get(this.portCheckRules, "port_udp_checker", []);
            const values = value.split(",");
            const res = values.every(item => {
                // 区间情况
                if (item.indexOf("-") > -1) {
                    const region = item.split("-");
                    const start = Number(region[0]);
                    const end = Number(region[1]);

                    // 禁用端口包含在区间中
                    return forbidPort.every(forbid => {
                        if (forbid >= start && forbid <= end) {
                            return false;
                        }
                        return true;
                    });
                }

                // 含有禁用数组的元素
                if (forbidPort.indexOf(Number(item)) > -1) {
                    return false;
                }

                return true;
            });
            if (!res) {
                return callback(new Error(`端口已被系统占用，请更换端口`));
            }

            return callback();
        },
    },
};
