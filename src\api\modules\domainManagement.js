import $https from "@/utils/request";
import { ROUTE_PREFI } from "@/config/url/_PREFIX";

// const preFix = "/mock";
const preFix = ROUTE_PREFI;

// 域名列表
export function queryDomainList(params) {
    return $https({
        url: `${preFix}/ctaccessone/domainManage/queryDomainList`,
        method: "get",
        params,
    });
}

// 查询域名详情
export function getDomainInfo(params) {
    return $https({
        url: `${preFix}/aocdn/domainManage/getDomainInfo`,
        params,
    });
}

// 域名新增
export function addDomain(params) {
    return $https({
        url: `${preFix}/aocdn/domainManage/addDomain`,
        params,
    });
}

// HTTPS状态修改
export function updateHttpsStatus(params) {
    return $https({
        url: `${preFix}/aocdn/domainManage/updateHttpsStatus`,
        params,
    });
}

// 域名删除-单条
export function deleteDomain(params) {
    return $https({
        url: `${preFix}/aocdn/domainManage/deleteDomain`,
        params,
    });
}

// 域名状态修改-停用/启用
export function updateDomainStatus(params) {
    return $https({
        url: `${preFix}/aocdn/domainManage/updateDomainStatus`,
        params,
    });
}

// 校验域名是否备案
export function getDomainHasRecord(params) {
    return $https({
        url: `${preFix}/aocdn/domainManage/getDomainHasRecord`,
        // url: `/mock/aocdn/domainManage/getDomainHasRecord`,
        params,
        config: {
            cache: true,
        },
        method: "get",
    });
}

// 域名是否重复查询接口
export function repeatValidate(params) {
    return $https({
        url: `${preFix}/aocdn/domainManage/repeatValidate`,
        params,
        config: {
            cache: true,
        },
        method: "get",
    });
}

// 域名归属校验
export function domainBelongValidate(params) {
    return $https({
        url: `${preFix}/aocdn/domainManage/belongValidate`,
        params,
        method: "get",
    });
}

// 域名归属验证
export function verifyDomainBelong(params) {
    return $https({
        url: `${preFix}/aocdn/domainManage/domainZoneValidate`,
        params,
        method: "get",
    });
}
