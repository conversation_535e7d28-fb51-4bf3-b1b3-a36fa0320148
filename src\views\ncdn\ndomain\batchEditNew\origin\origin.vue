<template>
    <batch-edit-checked v-model="checked">
        <el-form
            class="origin-wrapper"
            label-width="140px"
            label-position="right"
            :model="form"
            :rules="rules"
            ref="formRef"
            :disabled="!checked"
        >
            <el-form-item
                :label="$t('domain.create.originServer')"
                prop="originAll"
                :rules="rules.originAll"
                ref="origin"
                class="ct-table-form-item table-form-item-style"
            >
                <span slot="label"
                    >{{ $t("domain.create.originServer") }}
                    <span>
                        <el-tooltip placement="top" :content="noMoreThanSixtyDomainTip">
                            <ct-svg-icon
                                icon-class="question-circle"
                                class-name="ct-sort-drag-icon"
                            ></ct-svg-icon></el-tooltip></span
                ></span>

                <div class="ct-table-wrapper">
                    <el-table class="origin-table auto-table" :data="form.origin">
                        <!-- 源站类型：用于区分：普通源站和xos源站 -->
                        <el-table-column :label="$t('domain.create.originType')" width="136">
                            <template slot="header">
                                <span>{{ $t("domain.create.originType") }}</span>
                            </template>
                            <template>
                                {{ $t("domain.create.xos0") }}
                            </template>
                        </el-table-column>
                        <el-table-column
                            prop="origin"
                            :label="$t('domain.create.originServer')"
                            min-width="200"
                        >
                            <template #default="scope">
                                <el-form-item
                                    label-width="0"
                                    :prop="`origin.` + scope.$index"
                                    key="origin"
                                    :rules="rules.origin"
                                >
                                    <el-input
                                        v-model="scope.row.origin"
                                        placeholder=""
                                        class="input-style"
                                    ></el-input>
                                </el-form-item>
                            </template>
                        </el-table-column>
                        <el-table-column prop="role" :label="$t('domain.create.level')">
                            <template #default="scope">
                                <el-form-item label-width="0" :prop="`origin.` + scope.$index + `.role`">
                                    <el-select v-model="scope.row.role" class="input-style">
                                        <el-option value="master" :label="$t('domain.create.primary')" />
                                        <el-option value="slave" :label="$t('domain.create.secondary')" />
                                    </el-select>
                                </el-form-item>
                            </template>
                        </el-table-column>
                        <el-table-column prop="origin" :label="$t('domain.create.weight')">
                            <template #default="scope">
                                <el-form-item
                                    label-width="0"
                                    :prop="`origin.` + scope.$index + `.weight`"
                                    :rules="rules.weight"
                                    class="weight-wrapper"
                                >
                                    <el-input
                                        v-model="scope.row.weight"
                                        :placeholder="$t('domain.detail.placeholder20')"
                                        class="input-style"
                                    ></el-input>
                                </el-form-item>
                            </template>
                        </el-table-column>
                        <!-- 指定源站回源HOST -->
                        <el-table-column
                            prop="origin_host"
                            :label="$t('domain.create.originHost')"
                            min-width="200"
                            v-if="isNewEcgw"
                        >
                            <template #default="scope">
                                <host-select-mod
                                    v-model.trim="scope.row.origin_host"
                                    label-width="0"
                                    :prop="`origin.` + scope.$index + `.origin_host`"
                                    host-type="originHost"
                                    :accelerate-domains="domainList"
                                    :origin-domains="form.origin.map(itm => itm.origin).filter(itm => !!itm)"
                                    :rules="[
                                        {
                                            validator: valid_origin_host,
                                            trigger: 'blur',
                                        },
                                    ]"
                                    @blur="onOriginHostBlur"
                                ></host-select-mod>
                            </template>
                        </el-table-column>
                        <el-table-column :label="$t('domain.operate')" width="80">
                            <template #default="scope">
                                <el-button
                                    type="text"
                                    @click="onOperator(scope.row, 'delete', 'origin', scope.$index)"
                                    :disabled="!checked"
                                    >{{ $t("domain.delete") }}</el-button
                                >
                            </template>
                        </el-table-column>
                    </el-table>
                    <div class="flex-row-style button-box">
                        <el-button
                            class="btn"
                            type="text"
                            @click="onOperator(null, 'create', 'origin')"
                            :disabled="form.origin.length >= 60 || !checked"
                        >
                            + {{ $t("domain.editPage.label20") }}
                        </el-button>
                    </div>
                </div>
            </el-form-item>
        </el-form>
    </batch-edit-checked>
</template>

<script lang="ts">
import { Component, Prop, Mixins } from "vue-property-decorator";
import validFieldMixin from "@/views/domainConfig/validFieldMixin";
import componentMixin from "@/views/domainConfig/componentMixin";
import ctSvgIcon from "@/components/ctSvgIcon/index.vue";
import { cloneDeep } from "lodash-es";
import { ip, domain } from "@cdnplus/common/config/pattern";
import BatchItemMixin from "../mixins/batch.mixin";
import { reservedIp } from "@/config/npattern";

const urlReg = new RegExp(domain);
const ipReg = new RegExp(ip);
const reservedIpReg = new RegExp(reservedIp);

const weightPattern = "^([1-9][0-9]?|100)$";
const weightReg = new RegExp(weightPattern);

interface OriginItem {
    origin: string;
    role: string;
    weight: string;
    origin_host: string;
}

@Component({
    name: "BatchEditOrigin",
    components: {
        ctSvgIcon,
    },
    mixins: [validFieldMixin, componentMixin],
})
export default class BatchEditOrigin extends Mixins(BatchItemMixin) {
    @Prop({ type: Boolean, default: true }) isNewEcgw!: boolean;
    @Prop({ type: Array, default: () => [] }) domainList!: string[];

    checked = false;
    isReqHostCheck = false;
    form: {
        origin: OriginItem[];
        origin_host_http: Record<string, string>;
        origin_host_type: 1 | 0;
    } = {
        origin: [],
        origin_host_http: {},
        origin_host_type: 0,
    };
    currentType = "";
    rules = {
        originAll: [{ required: true, validator: this.validateOriginAll, trigger: "blur, change" }],
        role: [{ required: true, message: this.$t("domain.detail.tip74"), trigger: "change" }],
        weight: [
            { required: true, message: this.$t("domain.detail.placeholder20"), trigger: "blur" },
            {
                validator: (rule: any, value: any, callback: any) => {
                    if (value === "" || value === null || value === undefined) {
                        callback(this.$t("domain.detail.placeholder20"));
                    } else if (!weightReg.test(value)) {
                        callback(this.$t("domain.create.tip17"));
                    } else callback();
                },
                trigger: ["blur", "change"],
            },
        ],
        origin: [
            { required: true, message: this.$t("domain.detail.tip73"), trigger: "blur" },
            {
                validator: (rule: any, value: any, callback: any) => {
                    const { origin } = value;

                    if (origin === "" || origin === null || origin === undefined) {
                        callback(this.$t("domain.detail.placeholder80"));
                    } else if (!ipReg.test(origin) && !urlReg.test(origin)) {
                        callback(this.$t("domain.detail.tip89"));
                    } else if (reservedIpReg.test(origin)) {
                        callback(this.$t("domain.create.tip16"));
                    } else callback();
                },
                trigger: ["blur", "change"],
            },
        ],
    };

    onBatchEditOriginHostChange(isReqHostCheck: boolean) {
        this.isReqHostCheck = isReqHostCheck;
        if (this.checked)
            this.formRef.validateField(this.form.origin.map((_, idx) => `origin.${idx}.origin_host`));
    }

    mounted() {
        this.$ctBus.$on("batchEditOriginHost", this.onBatchEditOriginHostChange);
    }

    beforeDestroy() {
        this.$ctBus.$off("batchEditOriginHost", this.onBatchEditOriginHostChange);
    }

    get noMoreThanSixtyDomainTip() {
        return this.$t("domain.create.tip4");
    }
    get formData() {
        const origin_host_http: Record<string, string> = {};
        const { origin } = this.form;
        for (let i = 0; i < origin.length; i += 1) {
            if (origin[i].origin_host) origin_host_http[origin[i].origin] = origin[i].origin_host;
        }

        return {
            origin: cloneDeep(origin),
            origin_host_http,
            origin_host_type: Object.keys(origin_host_http).length ? 1 : 0,
            xos_origin_is: 0,
            zos_origin: { switch: 0 },
        };
    }

    async onOperator(row: OriginItem | null, currentType: string, tabName: "origin", i = -1) {
        this.currentType = currentType;
        const getTime = new Date().getTime();

        // 添加
        if (currentType === "create") {
            const defaultFormMap = {
                origin: { origin: "", role: "master", weight: "10", origin_host: "" },
            };
            row = defaultFormMap[tabName];
        }
        // 删除
        if (currentType === "delete") {
            const msgMap = {
                origin: this.$t("domain.editPage.tip20"),
            };

            // 二次确认弹窗
            await this.$confirm(msgMap[tabName] as string, this.$t("domain.delete") as string, {
                type: "warning",
            });
            this.form[tabName].splice(i, 1);
        } else {
            this.form[tabName].push(row as OriginItem);
        }
    }
    // 校验：指定源站回源HOST
    async valid_origin_host(rule: any, value: any, callback: any) {
        if (value === "" || value === null || value === undefined) return callback();
        if (value && this.isReqHostCheck) {
            return callback(new Error(this.$t("domain.create.tip20") as string));
        }
        return callback();
    }
    validateOriginAll(rule: any, value: any, callback: any) {
        let count = 0;
        const temp_origin = this.form.origin.map(item => item.origin);
        const origin_set = new Set(temp_origin);

        for (let i = 0; i < this.form.origin.length; i++) {
            if (this.form.origin[i].role === "master") {
                count++;
            }
        }

        // 源站地址不能和加速域名重复
        if (this.domainList?.length && this.form.origin?.some(o => this.domainList.includes(o.origin))) {
            callback(this.$t("domain.create.tip13"));
        }
        if (origin_set.size !== temp_origin.length) {
            callback(this.$t("domain.create.tip14"));
        } else if (count < 1) {
            callback(this.$t("domain.create.tip18"));
        } else {
            callback();
        }
        callback();
    }
    onOriginHostBlur() {
        this.$ctBus.$emit("batchEditOriginList", this.checked ? this.form.origin : []);
    }

    checkedWatchCallback(val?: boolean) {
        this.onOriginHostBlur();
    }
}
</script>

<style lang="scss" scoped>
@import "../commonStyle.scss";

.origin-wrapper {
    width: 100%;
}
.question-circle {
    font-size: $text-size-md;
    cursor: pointer;
}
.icon-column-label {
    color: $text-color-light;
    margin-left: $margin-2x;
    font-size: $text-size-md;
    line-height: 18px;
    height: 14px;
    width: 14px;
}
.tips {
    font-size: 12px;
    color: $color-neutral-7;
    font-weight: 400;
    margin-left: $margin;
}
.weight-wrapper {
    ::v-deep {
        .el-form-item__error {
            align-items: normal !important;
        }
    }
}
</style>
<style lang="scss">
.el-autocomplete-aocdn-width-adjust {
    width: 360px !important;
}
</style>
