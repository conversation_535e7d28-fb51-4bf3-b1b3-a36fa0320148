<template>
    <section class="domain-batchEdit">
        <ct-breadcrumb />
        <ct-section-header :title="$t('domain.batch.title')">
            <template #button>
                <el-button @click="stepPre" :disabled="active === 0">{{ $t("domain.back") }}</el-button>
                <el-button @click="stepNext" type="primary">{{ $t("domain.batch.next") }}</el-button>
            </template>
        </ct-section-header>
        <div class="batch-edit-step">
            <div class="step-container">
                <el-steps :active="active" finish-status="success" align-center>
                    <el-step :title="$t('domain.batch.title1')"></el-step>
                    <el-step :title="$t('domain.batch.title2')"></el-step>
                    <el-step :title="$t('domain.batch.title3')"></el-step>
                </el-steps>
            </div>
        </div>

        <div :class="{ showDomain: ifShowDomain, notShowDoamin: !ifShowDomain }">
            <el-table
                v-if="active === 0"
                :empty-text="$t('common.table.empty')"
                :data="domainList"
                style="overflow: auto"
            >
                <el-table-column type="index" :label="$t('domain.list.tableLabel1')" width="80" />
                <el-table-column prop="domain" min-width="160" :label="$t('domain.list.tableLabel2')" />
                <el-table-column prop="cname" min-width="160" label="CNAME" />
                <el-table-column prop="productName" min-width="120" :formatter="productFormatter" :label="$t('domain.list.tableLabel3')" />
                <el-table-column prop="areaScope" min-width="130" :label="$t('domain.list.tableLabel4')" />
                <el-table-column prop="status" min-width="90" :label="$t('domain.list.tableLabel5')" />
                <el-table-column prop="insertDate" min-width="160" :label="$t('domain.list.tableLabel6')" />
            </el-table>
        </div>

        <form-wrapper
            v-show="active === 1"
            :root-screen="rootScreen"
            :ifsubmit="ifsubmit"
            @batchLoading="handleBatchLoading"
            :isTabValid="isTabValid"
        />

        <div v-if="active === 2" class="step3-div" v-loading="batchLoading"></div>
    </section>
</template>

<script>
import FormWrapper from "./FormWrapper.vue";
import { ProductCodeMap } from "@/store/config/product";
import { DomainStatusMap } from "@/config/map";
import { timeFormat } from "@cdnplus/common/filters/index";
import { getI18nLabel } from "@/store/modules/ncdn/nproduct";

export default {
    components: {
        FormWrapper,
    },
    data() {
        this.areaScopeMap = new Map([
            [1, this.$t("domain.areaScope[0]")],
            [2, this.$t("domain.areaScope[1]")],
            [3, this.$t("domain.areaScope[2]")],
        ]);
        return {
            batchLoading: false,
            active: 0,
            rootScreen: false,
            domainList: [], // 批量修改的域名列表
            test: true,
            ifsubmit: false, // 触发提交表单
            isTabValid: false,
        };
    },
    computed: {
        ifShowDomain() {
            return this.active === 0;
        },
    },
    mounted() {
        this.domainList = window.sessionStorage.getItem("batchEdit");
        this.domainList = JSON.parse(this.domainList);
        this.transformDomainList(this.domainList);
    },
    methods: {
        productFormatter(row) {
            //如果为二级产品则返回二级产品名称
            return getI18nLabel(row.subProductCode || row.productCode);
        },
        beforeRouteEnter(to, from, next) {
            next(vm => {
                vm.rootScreen = from.fullPath === "/";
            });
        },
        handleBatchLoading(val) {
            this.batchLoading = val;
        },
        // 步骤条上一步
        async stepPre() {
            this.isTabValid = false;
            if (this.active === 1) {
                this.active--;
            }
            if (this.active === 2) {
                this.active--;
                this.ifsubmit = false;
            }
            this.isTabValid = true;
        },
        // 步骤条下一步
        async stepNext() {
            this.active = this.active > 1 ? this.active : ++this.active;
            this.isTabValid = false;
        },
        transformDomainList(domainList) {
            for (let i = 0; i < domainList.length; i++) {
                // 加速类型名称
                domainList[i].productName = domainList[i].subProductCode
                    ? ProductCodeMap[domainList[i].subProductCode]
                    : ProductCodeMap[domainList[i].productCode];
                // 加速区域
                domainList[i].areaScope = this.areaScopeMap.get(domainList[i].areaScope);
                // 状态
                domainList[i].status = this.$t(`${DomainStatusMap[domainList[i].status]}`);
                // 创建时间
                domainList[i].insertDate = timeFormat(domainList[i].insertDate);
            }
        },
    },
    watch: {
        async active(val) {
            if (val === 2) {
                await this.$confirm(`${this.$t("domain.batch.tip1")}`, this.$t("common.dialog.submit"), {
                    confirmButtonText: this.$t("common.dialog.submit"),
                    cancelButtonText: this.$t("common.dialog.cancel"),
                    type: "warning",
                })
                    .then(() => {
                        // 触发提交
                        this.ifsubmit = true;
                    })
                    .catch(() => {
                        this.active = 1;
                    });
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.domain-batchEdit {
    height: 100%; // 定高，出现滚动条
    display: flex;
    flex-direction: column;

    .batch-edit-step {
        padding: 20px;
        background: #fff;
        display: flex;
        justify-content: center;
        align-items: center;
        .step-container {
            width: 80%;
        }
    }

    .showDomain {
        overflow: hidden;
        flex: 1;
        margin: 20px;
        padding: 10px;
        display: flex;
        background: #fff;
        .el-table::before {
            z-index: 0;
        }
    }

    .notShowDomain {
        display: none;
    }

    .step3-div {
        overflow: hidden;
        flex: 1;
        margin: 10px;
        padding: 10px;
        display: flex;
        background: #fff;
    }

    // 报错提示 icon
    .el-icon-warning {
        color: $g-color-red;
    }

    // 步骤条样式后续优化
    ::v-deep {
        .el-step__title {
            font-size: 14px;
            line-height: 34px;
        }
        // .el-step__icon.is-text {
        //     border: 1px solid;
        // }
        // .el-step__head.is-process {
        //     color: $g-color-black;
        // }
        // .el-step__title.is-process {
        //     color: $g-color-black;
        // }
    }
}
</style>
