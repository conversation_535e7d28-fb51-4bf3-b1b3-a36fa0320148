@import './commonStyle.scss';

.common-wrapper {
  min-width: 851px;
  // max-width: calc(100% - 120px);
  min-height: 250px;
  background: url("../images/3.png") 0 center no-repeat;
  background-size: cover;
  padding-top: 1px;
  .title-box {
    opacity: 0.85;
    // font-family: element-icons;
    font-size: 20px;
    color: $color-neutral-10;
    letter-spacing: 0;
    width: 80px;
    line-height: 20px;
    font-weight: 600;
    margin: 52px 0 0 68px;
  }
  .content-box {
    width: 493.64px;
    height: 48px;
    // font-family: element-icons;
    font-size: 12px;
    color: $color-neutral-9;
    letter-spacing: 0;
    text-align: justify;
    line-height: 24px;
    font-weight: 400;
    margin: 22px 0 0 68px;
  }
  .btn-style {
    // width: 112px;
    height: 36px;
    margin: 28px 0 0 68px;
  }
}
.socket-style {
  min-width: 851px;
  // max-width: 88%;
  // max-width: calc(100% - 120px);
  min-height: 250px;
  background: url("../images/2.png") 0 center no-repeat;
  background-size: cover;
  padding-top: 1px;
  .socket-title {
    opacity: 0.85;
    // font-family: element-icons;
    font-size: 20px;
    color: $color-neutral-10;
    letter-spacing: 0;
    width: 104px;
    line-height: 20px;
    font-weight: 600;
    margin: 52px 0 0 68px;
  }
  .socket-content {
    width: 520px;
    height: 48px;
    // font-family: element-icons;
    font-size: 12px;
    color: $color-neutral-9;
    letter-spacing: 0;
    text-align: justify;
    line-height: 24px;
    font-weight: 400;
    margin: 22px 0 0 68px;
  }
  .btn-style {
    // width: 112px;
    height: 36px;
    margin: 52px 0 0 68px;
  }
}
.ipv6-container {
  .ipv6-style {
    min-width: 851px;
    // max-width: 88%;
    // max-width: calc(100% - 120px);
    min-height: 250px;
    background: url("../images/1.png") 0 center no-repeat;
    background-size: cover;
    padding-top: 1px;
    .title-box {
      opacity: 0.85;
      // font-family: element-icons;
      font-size: 20px;
      color: $color-neutral-10;
      letter-spacing: 0;
      width: 124px;
      line-height: 20px;
      font-weight: 600;
      margin: 52px 0 0 68px;
    }
    .content-box {
      width: 520px;
      height: 48px;
      // font-family: element-icons;
      font-size: 12px;
      color: $color-neutral-9;
      letter-spacing: 0;
      text-align: justify;
      line-height: 24px;
      font-weight: 400;
      margin: 22px 0 0 68px;
    }
    .btn-style {
      // width: 112px;
      height: 36px;
      margin: 28px 0 0 68px;
    }
  }
  .ipv6-wrap {
    margin: 20px 0 0 0px;
    .label-style {
      display: inline-block;
      height: 36px;
      // font-family: element-icons;
      font-size: 12px;
      color: $color-neutral-10;
      letter-spacing: 0;
      text-align: right;
      line-height: 18px;
      font-weight: 400;
      &:before {
        content: '*';
        color: rgb(245, 108, 108);
        margin-right: 4px;
      }
    }
    .net-label-style {
      margin-left: -24px;
    }
    .switch-style {
      position: relative;
      left: 20px;
    }
    .icon-style {
      font-size: 14px;
      margin-left: $margin-2x;
      // display: block;
      // margin-left: 46px;
      // margin-top: 22px;
    }
  }
  .out-chain-wrapper {
    width: 700px;
    padding: 8px;
    background: $color-neutral-1;
    margin-left: 140px;
    .out-chain-item-wrapper {
      padding-top: 12px;
      padding-bottom: 12px;
    }
    .out-chain-input-style {
      width: 282px;
    }
  }
  .ipv6-content {
    width: 700px;
    background: $color-neutral-1;
    margin-top: 0px;
    margin-left: 140px;
    padding: 8px 8px 1px 8px;
    .text {
      width: 72px;
      height: 18x;
      font-size: 12px;
      color: $color-neutral-10;
      font-weight: 400;
    }
    .text2 {
      display: inline-block;
      width: 96px;
      height: 18x;
      font-size: 12px;
      color: $color-neutral-10;
      font-weight: 400;

    }
    .question-style {
      font-size: $text-size-md;
      color: $color-neutral-9;
      margin-left: -12px;
      margin-right: 28px;
      cursor: pointer;
    }
    .input-style {
      max-width: 250px;
    }
  }
}
