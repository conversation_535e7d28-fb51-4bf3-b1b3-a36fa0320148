<!--
 * @Description: 布局为了方便通用的 标题+说明+右侧按钮这种布局，如果不是的话建议单独处理
 * @Author: wang yuegong
-->

<template>
    <header class="ct-section-header">
        <div class="header-title">
            <slot name="title">
                <span class="header-h3">{{ title }}</span>
                <span v-if="!!tip && !isHtmlTag" class="header-tip pl-8">{{ tip }}</span>
                <span v-if="!!tip && isHtmlTag" class="header-tip pl-8" v-html="tip"></span>
                <span class="header-tip pl-8"><slot name="tip"></slot></span>
            </slot>
        </div>

        <div class="header-btn">
            <slot name="button"></slot>
        </div>
    </header>
</template>
<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";

@Component({
    name: "CtSectionHeader",
})
export default class CtSectionHeader extends Vue {
    @Prop({ type: String }) private title!: string;
    @Prop({ type: String }) private tip?: string;
    @Prop({ type: Boolean, default: false }) private isHtmlTag?: boolean;
}
</script>
<style lang="scss" scoped>
.ct-section-header {
    padding: 0 $common-space-5x; // 左右间距固定，方便 slot 中处理左右边距
    overflow: hidden;
    display: flex;
    align-items: flex-end; // 按钮下端对齐
    background-color: $g-color-white;
    box-shadow: 0 0 8px 0 rgba(100, 110, 144, 0.1);
    border-left: 1px solid $color-neutral-2;

    .header-title {
        flex: 1;
        margin-right: $common-space-4x;
        padding: $common-space-4x 0;
        // height: 56px;
    }
    .header-h3 {
        position: relative;
        margin: 8px 0;
        font-size: $text-size-md;
        color: #333;
        font-weight: 700;
        // line-height: 1;
        // text-indent: 8px;
        // padding-left: 16px;

        // &::before {
        //     content: "";
        //     position: absolute;
        //     left: 0;
        //     width: 0;
        //     height: 16px;
        //     border-left: 5px solid $color-master;
        // }
    }

    .header-tip {
        color: $text-color-light;
        font-size: $text-size-sm;
        line-height: 1.5;
        word-break: break-all; // 打断换行
    }
    
    .pl-8 {
        padding-left: 8px;
    }

    .header-btn {
        @include g-media-attr(
            (
                attr: "text-align",
                sm: left,
                xs: right,
            )
        );

        .el-button {
            margin-bottom: 12px; // 边距加在按钮上，可以避免无按钮时占位
        }
    }
}
</style>
