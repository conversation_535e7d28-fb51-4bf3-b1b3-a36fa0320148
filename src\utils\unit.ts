import { ScaleModule } from "@/store/modules/scale";

/**
 * 将数值除以指定的缩放比例
 *
 * @param num 可以是数字类型或字符串类型的数值
 * @param scale 缩放比例，默认为1000，即除以1000
 * @param times 指定需要进行几次这样的缩放，默认为2次
 * @returns 返回一个字符串，表示按比例缩小后的数值，保留两位小数
 */
export function divideScale(num: number | string, scale = 0, times = 0): string {
    // 这里实际上改变了传入的scale值，将其设置为ScaleModule中的scale属性
    scale = scale || ScaleModule.scale;
    times = times || 2;

    // 将输入的字符串转换为数字，如果转换失败则默认为0
    const _num = typeof num === "string" ? isNaN(Number(num)) ? 0 : Number(num) : num;

    // 返回计算结果，结果保留两位小数
    return (_num / scale ** times).toFixed(2);
}
