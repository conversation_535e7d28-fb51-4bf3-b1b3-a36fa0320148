import { VuexModule, Module, Mutation, getModule } from "vuex-module-decorators";
import store from "../index";

export interface ScaleState {
    scale: number;
}

interface CdnFormValidate {
    ctBackupSource: boolean;
}

@Module({ dynamic: true, store, name: "cdnConfig", namespaced: true })
class CdnConfig extends VuexModule {
    public showAlarm: {
        domainList: boolean;
        home: boolean;
    } = {
        domainList: false,
        home: false,
    };
    public isCtBackupSourceSelected = false; // 是否选择了Aone云备源
    public cdnFormValidate: CdnFormValidate = { // 用于处理异步的表单验证，确保在验证完成前，不允许提交
        ctBackupSource: false,
    };

    @Mutation
    public SET_SHOW_ALARM({ key, value }: { key: "domainList" | "home"; value: boolean }) {
        this.showAlarm[key] = value;
    }

    @Mutation
    public SET_IS_CT_BACKUP_SOURCE_SELECTED(value: boolean) {
        this.isCtBackupSourceSelected = value;
    }

    @Mutation
    public SET_IS_WAITING_FORM_VALIDATE(value: {
        key: keyof CdnFormValidate;
        value: boolean
    }) {
        const { key, value: _value } = value;
        this.cdnFormValidate[key] = _value;
    }

    /**
     * 是否等待表单验证, 只要有一个字段为true，就允许提交
     */
    get isWaitingFormValidate() {
        return Object.values(this.cdnFormValidate).some(value => value);
    }
}

export const CdnConfigModule = getModule(CdnConfig);
