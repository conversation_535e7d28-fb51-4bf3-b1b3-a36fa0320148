<template>
    <ct-section-wrap headerText="你需要输入更多的信息才能进入页面">
        <ct-box>
            <ct-tip v-if="!loading && workspaceList.length > 0"> 请选择一个工作区进入 </ct-tip>
            <ct-tip v-else-if="!loading">
                <div>暂无工作区，请前往 <a :href="WorkspaceListUrl">IAM个人中心</a> 进行操作</div>
            </ct-tip>

            <el-table
                v-if="workspaceList.length > 0"
                :data="workspaceList"
                stripe
                :show-header="false"
                v-loading="loading"
            >
                <el-table-column min-width="225px">
                    <template slot-scope="scope">
                        <div class="brief">
                            <img v-if="scope.row.iconPath" :src="scope.row.iconPath" />
                            <i v-else class="el-icon-user" />
                            <div class="brief-left">
                                <p class="brief-name">{{ scope.row.name }}</p>
                                <p class="brief-descr" v-if="scope.row.ucode">{{ scope.row.ucode }}</p>
                            </div>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column min-width="180px">
                    <template slot-scope="scope">
                        <el-tag class="w-tag" type="primary">{{ scope.row.userType }}</el-tag>
                        <el-tag
                            class="w-tag"
                            v-if="scope.row.userState === 'black'"
                            type="info"
                            title="您已被拉入黑名单"
                        >
                            黑名单
                        </el-tag>
                        <el-tag
                            class="w-tag"
                            v-if="scope.row.state === 'frozen'"
                            type="info"
                            title="工作区已冻结"
                        >
                            冻结
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column min-width="250px">
                    <template slot-scope="{ row }">
                        <el-button type="success" @click="goInside(row)" size="small">进入</el-button>
                    </template>
                </el-table-column>
            </el-table>
        </ct-box>
    </ct-section-wrap>
</template>

<script lang="ts">
import { Component, Vue } from "vue-property-decorator";

interface WorkspaceItem {
    name: string;
    ucode: string;
    userState: string;
    workspaceId: string;
    iconPath: string;
}

@Component({
    name: "interceptor",
})
export default class extends Vue {
    private loading = false;
    private workspaceList: WorkspaceItem[] = [];

    private WorkspaceListUrl = `/${process.env.PKG_NAME}/ctyun/wlist`;

    private created() {
        this.getMyWorkspaceId();
    }

    private async getMyWorkspaceId() {
        this.loading = true;
        const { list = [] }: { list: WorkspaceItem[] } = await this.$ctFetch("/iam/gw/self/workspace/List", {
            data: {
                offset: 0,
                limit: 999,
            },
            cache: true,
        });

        this.workspaceList = list.filter(item => ["member", "manager", "owner"].indexOf(item.userState) >= 0);

        // 仅有一个工作区的用户（包括子账户）给与主动跳转
        if (this.workspaceList.length === 1) {
            this.goInside(this.workspaceList[0]);
        }
    }
    goInside(row: WorkspaceItem) {
        // 不允许通过浏览器后退重新进入这个页面
        this.$router.replace({
            path: "/",
            query: {
                workspaceId: row.workspaceId,
            },
        });
    }
}
</script>

<style lang="scss" scoped>
.chooser-block {
    margin-top: 20px;
}
.redirect-button {
    margin-left: 20px;
}

::v-deep {
    .el-table td {
        padding: 12px 0;
    }
}

.ct-tip {
    border-left: 5px solid #3d73f5 !important;
}

.brief {
    display: table;
    & > img {
        width: 50px;
        height: 50px;
        min-width: 50px;
        border-radius: 50%;
        display: table-cell;
        border: none;
        vertical-align: middle;
    }
    .el-icon-user {
        font-size: 50px;
        display: table-cell;
        vertical-align: middle;
    }
    .brief-left {
        @include g-pd-lf(10px, 10px, 20px);
        display: table-cell;
        vertical-align: middle;
        line-height: 18px;
        .brief-name {
            margin: 0;
        }
    }
    .brief-descr {
        margin-top: 2px;
    }
}
</style>
