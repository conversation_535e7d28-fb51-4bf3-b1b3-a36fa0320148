import i18n from "../i18n/index";

// 域名
export const domain = "^([a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\\.)+[a-zA-Z]{2,6}$";
// 字典名称正则
export const dictionaryName = "^[a-z0-9]{1}([a-z0-9_]{0,62})[a-z0-9]{1}$";

// 泛域名
export const genericDomain = "^(\\*\\.|\\*)?([a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?\\.)+[a-z]{2,6}$";
// ip 只支持 ipv4
export const ipv4 = "^((25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(25[0-5]|2[0-4]\\d|[01]?\\d\\d?)$";
// ipv4 中局域网地址/私有地址（简单处理，先判断为ip，再判断是否为局域网ip ，包括：10.xx 192.168.xx 172.16.xx-172.31.xx）
export const lanIp = "^10\\.|^192\\.168\\.|^172\\.(?:(?:1[6-9])|(?:2[0-9])|(?:3[0-1]))\\.";
// ipv4 中保留地址（先判断为ip，再判断保留地址，包括：环回地址 127.xx 、链路本地地址 169.254.xx 、组播地址 224.xx-239.xx 、 test-nest 地址 *********-192.xx）
export const reservedIp = `${lanIp}|^127\\.|^169\\.254\\.|^2(?:(?:2[4-9])|(?:3[0-9]))\\.|^192\\.0\\.2\\.`;
// ip 支持 ipv4/ipv6
export const ip =
    "^((25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(25[0-5]|2[0-4]\\d|[01]?\\d\\d?)$|^([\\da-fA-F]{1,4}:){6}((25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(25[0-5]|2[0-4]\\d|[01]?\\d\\d?)$|^::([\\da-fA-F]{1,4}:){0,4}((25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(25[0-5]|2[0-4]\\d|[01]?\\d\\d?)$|^([\\da-fA-F]{1,4}:):([\\da-fA-F]{1,4}:){0,3}((25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(25[0-5]|2[0-4]\\d|[01]?\\d\\d?)$|^([\\da-fA-F]{1,4}:){2}:([\\da-fA-F]{1,4}:){0,2}((25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(25[0-5]|2[0-4]\\d|[01]?\\d\\d?)$|^([\\da-fA-F]{1,4}:){3}:([\\da-fA-F]{1,4}:){0,1}((25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(25[0-5]|2[0-4]\\d|[01]?\\d\\d?)$|^([\\da-fA-F]{1,4}:){4}:((25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(25[0-5]|2[0-4]\\d|[01]?\\d\\d?)$|^([\\da-fA-F]{1,4}:){7}[\\da-fA-F]{1,4}$|^:((:[\\da-fA-F]{1,4}){1,6}|:)$|^[\\da-fA-F]{1,4}:((:[\\da-fA-F]{1,4}){1,5}|:)$|^([\\da-fA-F]{1,4}:){2}((:[\\da-fA-F]{1,4}){1,4}|:)$|^([\\da-fA-F]{1,4}:){3}((:[\\da-fA-F]{1,4}){1,3}|:)$|^([\\da-fA-F]{1,4}:){4}((:[\\da-fA-F]{1,4}){1,2}|:)$|^([\\da-fA-F]{1,4}:){5}:([\\da-fA-F]{1,4})?$|^([\\da-fA-F]{1,4}:){6}:$";
// 端口号
export const port = "(?:[1-9]\\d{0,3}|[1-5]\\d{4}|6[0-4]\\d{4}|65[0-4]\\d{2}|655[0-2]\\d|6553[0-5])";
// url 带协议
export const url =
    "((ht|f)tps?):\\/\\/[\\w-]+(\\.[\\w-]+)+([()（）\\u4E00-\\u9FA5\\w\\-.,@?^=%&:/~+#]*[()（）\\u4E00-\\u9FA5\\w\\-@?^=%&~+#/])?";
// url 带协议，支持域名后正则
export const regUrl = "((ht|f)tps?):\\/\\/[\\w-]+(\\.[\\w-]+)+\\S*";

export default {
    domain: {
        pattern: `^${domain}$`,
        message: "common.patternMsg[0]",
    },
    ip: {
        pattern: `^${ip}$`,
        message: "common.patternMsg[1]",
    },
    port: {
        pattern: `^${port}$`,
        message: "common.patternMsg[2]",
    },
    referer: {
        // 支持 泛域名、泛域名+端口号、ip、ip+端口号
        pattern: `^(${genericDomain.replace(/\^|\$/g, "")}|${ip.replace(/\^|\$/g, "")})(:${port})?$`,
        message: "common.patternMsg[3]",
    },

    // 刷新预取
    url: {
        pattern: `^${url}$`,
        message: "common.patternMsg[4]",
    },
    catalogue: {
        pattern: `^${url}\\/$`,
        message: "common.patternMsg[5]",
    },
    prefetch: {
        pattern: `^${url}$`,
        message: "common.patternMsg[4]",
    },
    regUrl: {
        pattern: `^${regUrl}$`,
        message: "common.patternMsg[4]",
    },
};
