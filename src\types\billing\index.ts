/*
 * @Description: 计费详情相关
 * @Author: wang yuegong
 */
import { ProductCodeMap } from "@/config/map";
import { PacketTypeMap, PacketStatusMap } from "@/config/map/billing";
import { UserChargingTypeEnum } from "@/config/map/billing";


export interface FlowPacketItem {
    status: keyof typeof PacketStatusMap; // 流量包状态
    product_code: keyof typeof ProductCodeMap; // 产品码
    resource_type: keyof typeof PacketTypeMap; // 资源类型
    start_time: string; // 生效时间
    end_time: string;
    packet_id: string; // 包编号
    packet_size: number; // 包大小, 若资源类型为流量包，单位为GB，其他请求包单位为次
    used_num: string; // 包已用，同上
}

export interface HistoryData {
    billingTypeCname: string;
    createTime: string;
    effDate: string;
    resourceTypeCname: string;
    resourceType: string;
}

export interface FlowProductData {
    account_id: string;
    billing_type: number;
    billing_type_cname: string;
    product_cname: string;
    product_code: string;
    product_type: string;
    status: number;
    resource_id: string;
}
// 用户付费类型
export type UserChargingType = typeof UserChargingTypeEnum[keyof typeof UserChargingTypeEnum];