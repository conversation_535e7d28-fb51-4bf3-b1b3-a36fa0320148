import ctFilterBox from "@/components/ctFilterBox";
import ctEmpty from "@/components/ctEmpty";
import { SecurityAbilityModule } from "@/store/modules/securityAbility";

export default {
    components: { ctFilterBox, ctEmpty },
    computed: {
        // 面板数据加载效果
        numberLoading() {
            return SecurityAbilityModule.securityPanelDataLoading;
        },
        // 面板总数据
        numberTotalData() {
            return SecurityAbilityModule.securityPanelData;
        },
        domainLoading() {
            return SecurityAbilityModule.securityDomainLoading;
        },
        domainList() {
            return SecurityAbilityModule.securityDomainList;
        },
    },
    watch: {
        form() {
            this.$emit("form-change", this.form);
        },
    },
};
