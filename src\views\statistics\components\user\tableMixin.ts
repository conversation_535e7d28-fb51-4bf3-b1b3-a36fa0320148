/*
 * @Description: area 和 isp 公用的逻辑
 * @Author: wang yuegong
 */

import { Component, Vue } from "vue-property-decorator";
import { ScaleModule } from "@/store/modules/scale";
import { convertBandwidthB2P, convertFlowB2P } from "@/utils";
import { divideScale } from "@/utils/unit";
import { AreaFetchDataItem } from "@/types/statistics/user";
import i18n from "@/i18n";
import { nUserModule } from "@/store/modules/nuser";

// 输出映射
const userTabMap = {
    topBandwidth: i18n.t("statistics.dcdn.bandwidthFlowWhole.radioBtn1"),
    flow: i18n.t("statistics.dcdn.bandwidthFlowWhole.radioBtn2"),
    request: i18n.t("statistics.rank.common.tableColumn5"),
    bandwidth: i18n.t("statistics.dcdn.bandwidthFlowWhole.radioBtn1"),
    requestCnt: i18n.t("statistics.rank.common.tableColumn5"),
};

@Component
export default class ChartMixin extends Vue {
    currentType: "topBandwidth" | "flow" | "request" | "bandwidth" | "requestCnt" = "topBandwidth"; // 快捷切换
    fetchDataList: any[] = []; // 请求的列表数据

    // 获取进制基数
    get scale() {
        return ScaleModule.scale;
    }

    get MB() {
        return this.scale === 1024 ? "MiB" : "MB";
    }
    get Mbps() {
        return this.scale === 1024 ? "Mibps" : "Mbps";
    }

    get convertScale() {
        return {
            bandwidth: {
                bps: 1,
                Kbps: this.scale,
                Mbps: Math.pow(this.scale, 2),
                Gbps: Math.pow(this.scale, 3),
                Tbps: Math.pow(this.scale, 4),
                Pbps: Math.pow(this.scale, 5),
            },
            flow: {
                B: 1,
                KB: this.scale,
                MB: Math.pow(this.scale, 2),
                GB: Math.pow(this.scale, 3),
                TB: Math.pow(this.scale, 4),
                PB: Math.pow(this.scale, 5),
            },
        };
    }

    // 从数组中获取指定字段的最大值（用于生成合理的缩进规则）
    protected getMaxFromList(list: any[], key: string) {
        let max = 0;
        list?.map(item => +item[key]).forEach(num => (max = max > num ? max : num));
        return max;
    }

    // 获取数据处理配置 {"label":"带宽","unit":"(Mbps)","scale":1}
    get dataFormatConfig() {
        const { currentType } = this;
        let unit = "";
        let scale = 1;
        // 使用第一条数据，获取缩进和单位
        const data = this.fetchDataList.length === 0 ? 0 : this.getMaxFromList(this.fetchDataList, this.currentType);
        if (currentType === "topBandwidth" || currentType === "bandwidth") {
            const { unit: bUnit, _unit } = convertBandwidthB2P(data, this.scale);
            unit = bUnit;
            scale = this.convertScale.bandwidth[_unit];
        } else if (currentType === "flow") {
            const { unit: fUnit, _unit } = convertFlowB2P(data, this.scale);
            unit = fUnit;
            scale = this.convertScale.flow[_unit];
        } else if (currentType === "request" || currentType === "requestCnt") {
            unit = nUserModule.lang === "en" ? "" : "次";
        }

        return {
            label: userTabMap[this.currentType],
            unit,
            scale,
        };
    }

    // 表格中显示单位
    get tableUnit() {
        const { currentType } = this;
        if (nUserModule.lang === "en") {
            let unit = ""
            if (currentType === "request" || currentType === "requestCnt") {
                unit = ""
            } else {
                unit = ` (${this.dataFormatConfig.unit})`;
            }
            return `${userTabMap[currentType]}${unit}`;
        } else {
            const unit = currentType !== "request" && currentType !== "requestCnt" ? this.dataFormatConfig.unit : "次"
            return `${userTabMap[currentType]}(${unit})`;
        }
    }

    // 处理展示数据（通用处理）
    get showDataListBase() {
        const { currentType } = this;
        const { scale } = this.dataFormatConfig;
        return this.fetchDataList
            .map(item => {
                const currentData =
                    currentType !== "request" && currentType !== "requestCnt"
                        ? (+item[currentType] / scale).toFixed(2)
                        : item[currentType];

                return {
                    ...item,
                    currentData,
                };
            })
            .sort((a, b) => b[currentType] - a[currentType]); // 倒叙排列展示
    }


    processFBRDataFromB2M(list: AreaFetchDataItem[]): AreaFetchDataItem[] {
        return list.map(item => ({
            ...item,
            bandwidth: divideScale(item.bandwidth),
            flow: divideScale(item.flow),
        }))
    }
}
