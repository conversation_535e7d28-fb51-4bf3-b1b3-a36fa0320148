import { start } from "qiankun";
import { AppModule } from "@/store/modules/app";

export default {
    created() {
        AppModule.SET_CURRENT_MICRO_APP(this.$options.name);
    },
    computed: {
        loading() {
            return AppModule.microAppLoading;
        },
    },
    mounted() {
        if (!window.qiankunStarted) {
            window.qiankunStarted = true;
            start({
                singular: false,
                prefetch: arr => {
                    for (const item of arr) {
                        if (item.name !== AppModule.currentMicroApp) {
                            return false;
                        }
                    }
                },
            });
        }
    },
};
