<template>
    <div>
        <div class="radio-row">
            <el-radio-group v-model="objectModel.type">
                <el-radio label="allow">{{ $t("simpleForm.blockTable.allow") }}</el-radio>
                <el-radio label="block">{{ $t("simpleForm.blockTable.block") }}</el-radio>
            </el-radio-group>
        </div>
        <div>
            <el-input
                class="textarea-content"
                v-model="objectModel.ip"
                type="textarea"
                :rows="3"
                :placeholder="placeholder"
            />
        </div>
    </div>
</template>

<script>
// import mixin from "../../editor.mixin";
export default {
    // mixins: [mixin],
    name: "n-alogic-ip-block-table",
    props: {
        ipData: Object,
    },
    data() {
        return {
            objectModel: {
                type: "allow",
                ip: "",
            },
            isLimitNum: false, // 是否限制黑白名单ip个数
        };
    },
    computed: {
        placeholder() {
            const { maxNum, isLimitNum } = this;
            if (isLimitNum) {
                return this.$t("domain.detail.tip39", { maxNum });
            } else {
                return this.$t("domain.detail.tip40");
            }
        },
        maxNum() {
            const { objectModel } = this;
            let num = 50;
            if (objectModel.type === "block") {
                num = 200;
            }
            return num;
        },
        ipList() {
            const { objectModel } = this;
            const { ip } = objectModel;
            return ip.split("\n");
        },
    },
    methods: {
        validateProcedure() {
            // 支持 ip 段
            const ipReg =
                /^((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)(\/(\d|[1-2]\d|3[0-2]))?$|^([\da-fA-F]{1,4}:){6}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)(\/([1-9]?\d|(1([0-1]\d|2[0-8]))))?$|^::([\da-fA-F]{1,4}:){0,4}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)(\/([1-9]?\d|(1([0-1]\d|2[0-8]))))?$|^([\da-fA-F]{1,4}:):([\da-fA-F]{1,4}:){0,3}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)(\/([1-9]?\d|(1([0-1]\d|2[0-8]))))?$|^([\da-fA-F]{1,4}:){2}:([\da-fA-F]{1,4}:){0,2}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)(\/([1-9]?\d|(1([0-1]\d|2[0-8]))))?$|^([\da-fA-F]{1,4}:){3}:([\da-fA-F]{1,4}:){0,1}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)(\/([1-9]?\d|(1([0-1]\d|2[0-8]))))?$|^([\da-fA-F]{1,4}:){4}:((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)(\/([1-9]?\d|(1([0-1]\d|2[0-8]))))?$|^([\da-fA-F]{1,4}:){7}[\da-fA-F]{1,4}(\/([1-9]?\d|(1([0-1]\d|2[0-8]))))?$|^:((:[\da-fA-F]{1,4}){1,6}|:)(\/([1-9]?\d|(1([0-1]\d|2[0-8]))))?$|^[\da-fA-F]{1,4}:((:[\da-fA-F]{1,4}){1,5}|:)(\/([1-9]?\d|(1([0-1]\d|2[0-8]))))?$|^([\da-fA-F]{1,4}:){2}((:[\da-fA-F]{1,4}){1,4}|:)(\/([1-9]?\d|(1([0-1]\d|2[0-8]))))?$|^([\da-fA-F]{1,4}:){3}((:[\da-fA-F]{1,4}){1,3}|:)(\/([1-9]?\d|(1([0-1]\d|2[0-8]))))?$|^([\da-fA-F]{1,4}:){4}((:[\da-fA-F]{1,4}){1,2}|:)(\/([1-9]?\d|(1([0-1]\d|2[0-8]))))?$|^([\da-fA-F]{1,4}:){5}:([\da-fA-F]{1,4})?(\/([1-9]?\d|(1([0-1]\d|2[0-8]))))?$|^([\da-fA-F]{1,4}:){6}:(\/([1-9]?\d|(1([0-1]\d|2[0-8]))))?$/;

            const result = {
                valid: true,
                msg: "",
                dom: "ip_type",
            };
            // let list = this.ipList;
            const { ipList, maxNum, objectModel, isLimitNum } = this;

            if (!objectModel.type && objectModel.ip) {
                result.valid = false;
                result.msg = this.$t("domain.detail.tip42");
            } else if (!objectModel.ip.trim()) {
                result.valid = false;
                result.msg = this.$t("domain.detail.tip45");
            } else if (ipList.length > maxNum && isLimitNum) {
                result.valid = false;
                result.msg = this.$t("domain.detail.tip46", {
                    maxNum,
                });
            } else {
                for (let i = 0; i < ipList.length; i++) {
                    const item = ipList[i].trim();
                    // 输入的不是 ip
                    if (!ipReg.test(item)) {
                        result.valid = false;
                        result.msg = this.$t("domain.detail.tip47");
                        break;
                    }
                }

                // 检查重复
                const hasRepeat = new Set(ipList).size !== ipList.length;
                if (hasRepeat) {
                    result.valid = false;
                    result.msg = this.$t("domain.detail.tip48");
                }
            }

            return Promise.resolve(result);
        },
    },
    watch: {
        objectModel: {
            handler(val) {
                if (val.ip === "" && !val.type) {
                    this.model = "";
                } else {
                    this.model = {
                        type: val.type,
                        ip: this.ipList.join(","),
                    };
                    this.$emit("onChange", this.model);
                }
            },
            deep: true,
        },
    },
    mounted() {
        if (this.ipData) {
            this.objectModel = {
                type: this.ipData.type || "allow",
                ip: this.ipData.ip.split(",").join("\n"),
            };
        }
    },
};
</script>

<style lang="scss" scoped>
.textarea-content {
    width: 70%;
}
</style>
