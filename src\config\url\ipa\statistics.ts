/*
 * @Description: 统计分析
 */

import { IPA_PREFIX } from "../_PREFIX";

export const statistics = {
    // 带宽列表查询
    bandWidthList: IPA_PREFIX + "/v1/statistics/bandwidth/list",
    // 流量列表查询
    flowList: IPA_PREFIX + "/v1/statistics/flow/list",
    // 流量带宽统计查询（颗粒度天）
    statisticsDay: IPA_PREFIX + "/v1/statistics/flow_bandwidth/days/stats",

    // 连接数列表查询
    connectionsList: IPA_PREFIX + "/v1/statistics/connections/list",
    // 连接数统计查询（颗粒度天）
    statisticsconnectionsDay: IPA_PREFIX + "/v1/statistics/connections/days",

    // 地区运营商指标列表查询
    operatorMetricsList: IPA_PREFIX + "/v1/statistics/region_isp/data_list",

    // 热门分析===================================================
    // 域名排行列表
    topDomainList: IPA_PREFIX + "/v1/statistics/top_domain",
    // ip排行列表
    ipRanking: IPA_PREFIX + "/v1/statistics/top_ip",

    // 公共======================================================
    // 获取运营商列表
    ispList: IPA_PREFIX + "/v1/isp/list",
    // 地区查询列表
    areaList: IPA_PREFIX + "/v1/area/list",
    // 获取用户进制
    getscale: IPA_PREFIX + "/v1/scale/get",
};

// 统计分析-用户分析
export const StatisticsUserUrl = {
    // 访问用户区域分布数据（按省份分类查询带宽、流量与请求数）
    areaDataList: IPA_PREFIX + "/v1/statistics/province_flow_connections",
    // 地区列表
    areaBaseList: IPA_PREFIX + "/v1/area/list",
    // 国家列表
    countryBaseList: IPA_PREFIX + "/v1/listCountry",
    // 独立 ip 数据
    uvDataList: IPA_PREFIX + "/v1/statistics/dedicated_ip_count",
    // 访问运营商数据（按运营商分类查询带宽、流量与请求数）
    ispDataList: IPA_PREFIX + "/v1/statistics/isp_flow_connections",
    // 运营商列表
    ispBaseList: IPA_PREFIX + "/v1/isp/list",
};
