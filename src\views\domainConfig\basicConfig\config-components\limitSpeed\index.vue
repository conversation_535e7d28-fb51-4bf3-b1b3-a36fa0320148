<template>
    <div class="ct-edit-wrapper">
        <el-form
            label-width="140px"
            label-position="left"
            :model="form"
            ref="limitSpeedForm"
            :disabled="!isEdit || !isService || lock"
        >
            <el-form-item :label="$t('domain.detail.limitSpeed.tip6')" prop="limit_speed_const" class="cache-table-style">
                <div class="ct-table-wrapper">
                    <el-table :data="form.limit_speed_const">
                        <el-table-column prop="mode" :label="$t('domain.type')">
                            <template slot-scope="scope">{{ conditionModeMap[scope.row.mode] }}</template>
                        </el-table-column>
                        <el-table-column prop="content" :label="$t('domain.content')" />
                        <el-table-column prop="rate" :label="$t('domain.detail.limitSpeed.tip2')">
                        </el-table-column>
                        <el-table-column
                            prop="unit"
                            :label="$t('home.chart.unit')"
                            :formatter="unitFormatter"
                        />
                        <el-table-column prop="weight" :label="$t('domain.detail.label48')" />
                        <el-table-column :label="$t('domain.operate')" width="120">
                            <template slot-scope="scope">
                                <el-button
                                    type="text"
                                    :disabled="!isEdit || !isService || lock"
                                    @click="handleOperate(scope.row, 'edit', scope.$index)"
                                    >{{ $t("domain.modify") }}</el-button
                                >
                                <el-button
                                    type="text"
                                    :disabled="!isEdit || !isService || lock"
                                    @click="handleOperate(scope.row, 'delete', scope.$index)"
                                    >{{ $t("domain.delete") }}</el-button
                                >
                            </template>
                        </el-table-column>
                    </el-table>
                    <div class="flex-row-style button-box">
                        <el-button
                            class="btn"
                            type="text"
                            :disabled="!isEdit || !isService || lock"
                            @click="handleOperate(null, 'create')"
                        >
                            + {{ $t("domain.editPage.label10") }}
                        </el-button>
                    </div>
                </div>
            </el-form-item>
        </el-form>

        <limit-speed-dialog
            :dialogInfo="dialogInfo"
            @cancel="dialogInfo.visible = false"
            :unitOptions="unitOptions"
            @submit="handleDialogConfirm"
        />
    </div>
</template>

<script>
import componentMixin from "@/views/domainConfig/componentMixin";
import { cloneDeep } from "lodash-es";
import { conditionModeMap } from "@/components/commonCondition/dict";
import limitSpeedDialog from "./dialog.vue";

export default {
    name: "LimitSpeed",
    mixins: [componentMixin],
    props: {
        value: Array,
        lock: Boolean,
    },
    components: { limitSpeedDialog },
    data() {
        return {
            form: {
                limit_speed_const: [],
            },
            dialogInfo: {
                visible: false,
                type: "create",
                form: {},
                list: [],
                currentIndex: null,
            },
            conditionModeMap,
        };
    },
    watch: {
        value: {
            handler(val) {
                if (!val) return;
                this.init(val);
            },
            immediate: true,
        },
    },
    methods: {
        init(v) {
            this.form.limit_speed_const = cloneDeep(v);
        },
        async handleOperate(row, currentType, i) {
            this.currentType = currentType;

            const defaultForm = {
                id: `limit_speed_const_${new Date().getTime()}`,
                mode: null,
                content: "",
                unit: "b/s",
                rate: null,
                weight: 10,
            };

            if (currentType === "delete") {
                // 二次确认弹窗
                await this.$confirm(`${this.$t("domain.editPage.tip20")}`, this.$t("domain.delete"), {
                    type: "warning",
                });
                this.form.limit_speed_const.splice(i, 1);
                this.$emit("input", this.form.limit_speed_const);
                return;
            }

            let rowData = null;
            if (currentType === "create") {
                rowData = defaultForm;
                this.dialogInfo.type = "create";
            }

            if (currentType === "edit") {
                rowData = {
                    ...defaultForm,
                    ...row
                };
                this.dialogInfo.type = "edit";
                this.dialogInfo.currentIndex = i;
            }

            this.dialogInfo.form = rowData;
            this.dialogInfo.list = cloneDeep(this.form.limit_speed_const);
            this.dialogInfo.visible = true;
        },
        handleDialogConfirm(val) {
            this.dialogInfo.visible = false;
            if (this.dialogInfo.type === "create") {
                this.form.limit_speed_const.push(val);
            } else {
                this.form.limit_speed_const.splice(this.dialogInfo.currentIndex, 1, val);
            }
            this.$emit("input", this.form.limit_speed_const);
        },
        unitFormatter(row) {
            return this.unitOptions.find(item => item.value === row.unit).label;
        },
    },
    computed: {
        unitOptions() {
            return [
                { value: "b/s", label: "B/s" },
                { value: "Kb/s", label: "KB/s" },
                { value: "Mb/s", label: "MB/s" },
            ];
        },
    },
};
</script>

<style lang="scss" scoped>
@import "@/views/domainConfig/commonStyle.scss";
</style>
