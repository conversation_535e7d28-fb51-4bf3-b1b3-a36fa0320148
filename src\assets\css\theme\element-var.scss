/*
* element-ui scss 源文件路径：
*       node_modules\element-ui\packages\theme-chalk\src
*       node_modules\element-ui\packages\theme-chalk\src\common\var.scss
* 可以在该目录下先找到对应组件的某些样式是否可通过变量控制
*/

@import '../_var.scss';
/* 改变主题色变量 */
$--color-primary: $color-master;

/* 改变 icon 字体路径变量，必需 */
$--font-path: '~element-ui/lib/theme-chalk/fonts';

/* ----修改主题配置 start---- */
@import '~element-ui/packages/theme-chalk/src/common/var.scss';

$--border-radius-base: 2px;
// 调整 button 圆角 2px
$--button-border-radius: $--border-radius-base;
$--button-medium-border-radius: $--border-radius-base;
$--button-small-border-radius: $--border-radius-base;
$--button-mini-border-radius: $--border-radius-base;
// 按钮组
$--radio-button-checked-font-color: $--color-primary;
$--radio-button-checked-background-color: #fff;
// 调整输入框
$--input-border-radius: $--border-radius-base;
// 调整 tool-tip
$--tooltip-fill: rgba(0, 0, 0, 0.6);
$--tooltip-border-color: rgba(0, 0, 0, 0.5);

/* ----修改主题配置 end---- */

// @import "~element-ui/packages/theme-chalk/src/index";
