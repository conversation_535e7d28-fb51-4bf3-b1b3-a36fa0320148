<template>
    <el-dialog
        :visible="visible"
        :title="$t('domain.changeAcceArea.title')"
        :show-close="false"
    >
        <div class="block-wrapper">
            <div class="block-wrapper-label">{{ $t("domain.list.tableLabel4") }}:</div>
            <el-radio-group v-model="model" class="aocdn-change-acce-area-radio-group">
                <el-radio
                    v-for="o in areaList"
                    :key="o.value"
                    :disabled="o.disabled"
                    :label="o.value"
                >
                    <div v-html="o.displayValue" class="radio-display"></div>
                </el-radio>
            </el-radio-group>
        </div>
        <div class="divider"></div>
        <div class="block-wrapper">
            <div class="block-wrapper-label">
                {{ $t("domain.list.note") }}:
            </div>
            <div class="option-note">
                <!-- 已开通海外 -->
                <template v-if="hasOverseaProduct">
                    <!-- 国际站 -->
                    <template v-if="isCtclouds">
                        <div class="option-note-item">
                            {{ $t("domain.changeAcceArea.ctcloud.tip1") }}
                            <div class="sub-option-note-item" v-html="$t('domain.changeAcceArea.ctcloud.tip1-1', { link: realnameLink})">
                            </div>
                            <div class="sub-option-note-item">
                                {{ $t("domain.changeAcceArea.ctcloud.tip1-2") }}
                            </div>
                        </div>
                        <div class="option-note-item">
                            {{ $t("domain.changeAcceArea.ctcloud.tip2") }}
                        </div>
                        <div
                            class="option-note-item"
                            v-html="
                                $t('domain.changeAcceArea.ctcloud.tip3', { link: getUrl })
                            "
                        ></div>
                    </template>
                    <!-- 国内站 -->
                    <template v-else>
                        <div class="option-note-item">
                            {{ $t("domain.changeAcceArea.ctyun.tip1") }}
                        </div>
                        <div class="option-note-item">
                            {{ $t("domain.changeAcceArea.ctyun.tip2") }}
                        </div>
                        <div
                            class="option-note-item"
                            v-html="
                                $t('domain.changeAcceArea.ctyun.tip3', { link: getUrl })
                            "
                        ></div>
                    </template>
                </template>
                <!-- 未开通海外 -->
                <template v-else>
                    <template v-if="isOld">
                        <div v-if="salesChannelLine === salesChannelLineEnum.onLine">
                            <!-- 国际站 isCtclouds -->
                            <p v-if="isCtclouds">
                                {{ $t("simpleForm.acceArea.isCtcCloudTip.tip1") }}
                                <el-button
                                    @click="changeType"
                                    class="aocdn-ignore-link"
                                    size="mini"
                                    type="text"
                                >
                                    {{ $t("domain.create.tip3-1") }} </el-button
                                >{{ $t("simpleForm.acceArea.isCtcCloudTip.tip2")
                                }}<a
                                    target="_blank"
                                    :href="overseasCdnAcceleratesLink"
                                    class="aocdn-ignore-link"
                                >
                                    {{ $t("simpleForm.acceArea.isCtcCloudTip.tip3") }}</a
                                >{{ $t("simpleForm.acceArea.isCtcCloudTip.tip4") }}
                                <a target="_blank" :href="learnLink" class="aocdn-ignore-link">
                                    {{ $t("domain.detail.tip23") }}</a
                                >
                            </p>
                            <!-- ctyun/vip -->
                            <p v-if="!isCtclouds && isOld2">
                                <span class="option-note">
                                    {{ $t("simpleForm.acceArea.isCtcCloudTipOld.tip1") }}
                                </span>
                                <a
                                    target="_blank"
                                    :href="overseasCdnAcceleratesLink"
                                    class="aocdn-ignore-link"
                                >
                                    {{
                                        $t("simpleForm.acceArea.isCtcCloudTipOld.tip2")
                                    }}</a
                                >
                                <span class="option-note">{{
                                    $t("simpleForm.acceArea.isCtcCloudTipOld.tip3")
                                }}</span>
                                <a
                                    class="aocdn-ignore-link"
                                    @click="$docHelp('https://www.ctyun.cn/document/10015932/10129365')"
                                >
                                    {{ $t("domain.detail.tip23") }}</a
                                >
                            </p>
                        </div>
                        <p v-else>
                            {{ $t("simpleForm.acceArea.productTip.common.tip2") }}
                        </p>
                    </template>
                    <template v-else>
                        <!-- cdn加速 & 全站 -->
                        <!-- <div v-html="note"></div> -->
                        <i18n :path="note">
                            <a target="_blank" class="aocdn-ignore-link" :href="overseasCdnAcceleratesLink">{{ $t("simpleForm.acceArea.productTip.008.tip2") }}</a>
                            <a class="aocdn-ignore-link" @click="$docHelp('https://www.ctyun.cn/document/10015932/10071342')">{{ $t("simpleForm.acceArea.productTip.common.tip1") }}</a>
                            <a target="_blank" class="aocdn-ignore-link" :href="stationAcceleratesLink">{{ $t("simpleForm.acceArea.productTip.006.tip2") }}</a>
                            <a class="aocdn-ignore-link" @click="$docHelp('https://www.ctyun.cn/document/10006847/10096615')">{{ $t("simpleForm.acceArea.productTip.common.tip1") }}</a>
                            <a target="_blank" class="aocdn-ignore-link" href="${overseasCdnAcceleratesLink}">{{ $t("simpleForm.acceArea.productTip.004.tip2") }}</a>
                            <a target="_blank" class="aocdn-ignore-link" href="${overseasCdnAcceleratesLink}">{{ $t("simpleForm.acceArea.productTip.014.tip2") }}</a>
                        </i18n>
                    </template>
                    <p v-html="acceTips"></p>
                </template>
            </div>
        </div>
        <div slot="footer" v-loading="loading">
            <el-button @click="$emit('close')">{{
                $t("common.dialog.cancel")
            }}</el-button>
            <el-button type="primary" :disabled="disableSubmit" @click="submit">{{
                $t("common.dialog.submit")
            }}</el-button>
        </div>

        <!-- 域名个数限制-弹窗 -->
        <domainCountLimitDialog
            :dialogVisible="domainCountLimitDialogVisible"
            :domainCountLimitLink="domainCountLimitLink"
            @submit="domainCountLimitSubmit"
        ></domainCountLimitDialog>
    </el-dialog>
</template>

<script>
import { nDomainUrl } from "@/config/url";
import { NEW_PREFIX } from "@/config/url/_PREFIX";
import { nUserModule } from "@/store/modules/nuser";
import urlTransformer from "@/utils/logic/url";
import domainCountLimitDialog from "@/views/ncdn/ndomain/list/components/domainCountLimitDialog.vue";
import { CODE_QUOTA_EXCEEDED_2396 } from "@/utils/ctFetch/errorConfig";

const checkOverseaProductUrl = NEW_PREFIX + "/product/package/checkOverseaProduct";
// 域名校验接口
const domainValidateUrl = NEW_PREFIX + "/domain/validate";
// 判断是否开通海外加速
export default {
    name: "changeAcceAreaDialog",
    components: {
        domainCountLimitDialog,
    },
    props: {
        row: {
            type: Object,
            default: () => ({}),
        },
        visible: {
            type: Boolean,
            default: false,
        },
        changeAccelerationType: Function,
    },
    data() {
        return {
            model: "",
            hasMainLandProduct: true, // 是否开通内地
            hasOverseaProduct: false, // 是否开通海外
            loading: false,
            // 产品线分类，1：线下，2：线上
            salesChannelLineEnum: {
                Offline: 1,
                onLine: 2,
            },
            salesChannelLine: null,
            domainCountLimitLink: "",
            domainCountLimitDialogVisible: false,
        };
    },
    watch: {
        async visible(val) {
            if (!val) {
                return;
            }
            this.loading = true;
            await Promise.all([this.init(), this.checkOverseaProduct()])
                .then(() => {
                    this.loading = false;
                })
                .catch(() => {
                    this.loading = false;
                });
        },
    },
    computed: {
        // 泛静态产品
        isOld() {
            return /^(001|003|004|014)$/.test(this.row?.productCode);
        },
        // 泛静态产品去掉014
        isOld2() {
            return /^(001|003|004)$/.test(this.row?.productCode);
        },
        learnLink() {
            return nUserModule.lang === "zh"
                ? "https://www.esurfingcloud.com/document/zh-cn/10015932/20688150"
                : "https://www.esurfingcloud.com/document/10015932/20688150";
        },
        cdnAcceleratesLink() {
            return this.isCtclouds
                ? nUserModule.lang === "zh"
                    ? "https://www.esurfingcloud.com/products/zh-cn/10015926"
                    : "https://www.esurfingcloud.com/products/10015926"
                : "https://www.ctyun.cn/products/cdnjs";
        },
        overseasCdnAcceleratesLink() {
            return this.isCtclouds
                ? nUserModule.lang === "zh"
                    ? " https://www.esurfingcloud.com/bac/product/ops?custId=0&siteId=ctyun&productId=10015926&operationId=1003017103&lang=zh-cn"
                    : "https://www.esurfingcloud.com/bac/product/ops?custId=0&siteId=ctyun&productId=10015926&operationId=1003017103&lang=en-us"
                : nUserModule.lang === "zh"
                    ? "https://cdn.ctyun.cn/h5/norder/?product=008&action=0&lang=zh-cn"
                    : "https://cdn.ctyun.cn/h5/norder/?product=008&action=0&lang=en-us";
        },
        stationAcceleratesLink() {
            return this.isCtclouds
                ? nUserModule.lang === "zh"
                    ? "https://www.esurfingcloud.com/bac/product/ops?custId=0&siteId=ctyun&productId=10006828&operationId=1000682801&lang=zh-cn"
                    : "https://www.esurfingcloud.com/bac/product/ops?custId=0&siteId=ctyun&productId=10006828&operationId=1000682801&lang=en-us"
                : "https://www.ctyun.cn/h5/bac/product/ops?custId=0&siteId=ctyun&productId=10006828&operationId=1000682801";
        },
        note() {
            const { hasOverseaProduct, salesChannelLine } = this;
            return hasOverseaProduct
                ? this.$t("simpleForm.acceArea.billingNote")
                : this.getNoHasOverseaProductTip(this.row?.productCode, salesChannelLine);
        },
        isCtclouds() {
            return nUserModule.isCtclouds;
        },
        getUrl() {
            // 国际站没有工单链接，直接用国内的
            return this.isCtclouds
                ? nUserModule.lang === "zh"
                    ? "https://www.esurfingcloud.com/contactus/zh-cn/fromIndex"
                    : "https://www.esurfingcloud.com/contactus/fromIndex"
                : "https://www.ctyun.cn/console/smartservice/ticket/workorder/submit";
        },
        realnameLink() {
            return nUserModule.lang === "zh"
                ? "https://www.esurfingcloud.com/console/info/realname?lang=zh-cn"
                : "https://www.esurfingcloud.com/console/info/realname";
        },
        originScope() {
            return this.row.areaScope + "";
        },
        disableSubmit() {
            return this.model === this.originScope || !this.model;
        },
        domain() {
            return this.row?.domain || "";
        },
        areaList() {
            return [
                {
                    value: "1",
                    displayValue: this.$t("simpleForm.location.chinaMainland"),
                    // 只要开通内地，就能选择内地（不再限制产品）
                    disabled: !this.hasMainLandProduct,
                },
                {
                    value: "2",
                    displayValue: this.$t("simpleForm.location.globalExcludingMainland"),
                    // 只要开通海外，就能选择海外（不再限制产品）
                    // 增加条件：重叠域名暂时不支持海外
                    disabled: !this.hasOverseaProduct || this.isRepeat,
                },
                {
                    value: "3",
                    displayValue: this.$t("simpleForm.location.global"),
                    // 当同时开通内地、海外，才允许选全球（不再限制产品）
                    disabled: !(this.hasOverseaProduct && this.hasMainLandProduct),
                },
            ];
        },
        acceTips() {
            const ctcloudLink =
                nUserModule.lang === "zh"
                    ? "https://www.esurfingcloud.com/console/info/realname?lang=zh-cn"
                    : "https://www.esurfingcloud.com/console/info/realname?lang=en-us";
            return this.isCtclouds
                ? this.$t("simpleForm.acceArea.acceTipsCtclouds", { ctcloudLink })
                : this.$t("simpleForm.acceArea.acceTipsNonCtclouds");
        },
        endSentence() {
            return nUserModule.lang === "en" ? '.' : '';
        },
    },
    methods: {
        async changeType() {
            const rst = await this.changeAccelerationType(this.row, true);
            if (rst) this.$emit("close");
        },
        getNoHasOverseaProductTip(type, salesChannelLine) {
            switch (type) {
                case "008":
                    return this.isCtclouds
                        ? "simpleForm.acceArea.productTip.008.tip1"
                        : salesChannelLine !== this.salesChannelLineEnum.onLine
                        ? "simpleForm.acceArea.productTip.008.tip4"
                        : "simpleForm.acceArea.productTip.common.tip2";
                case "006":
                    return this.isCtclouds
                        ? "simpleForm.acceArea.productTip.006.tip1"
                        : salesChannelLine === this.salesChannelLineEnum.onLine
                        ? "simpleForm.acceArea.productTip.006.tip4"
                        : "simpleForm.acceArea.productTip.common.tip2";
                case "001":
                case "003":
                case "004":
                    return salesChannelLine === this.salesChannelLineEnum.onLine
                        ? "simpleForm.acceArea.productTip.004.tip1"
                        : "simpleForm.acceArea.productTip.common.tip2";
                case "014":
                    return !this.isCtclouds
                        ? ""
                        : salesChannelLine === this.salesChannelLineEnum.onLine
                        ? "simpleForm.acceArea.productTip.014.tip1"
                        : "simpleForm.acceArea.productTip.common.tip2";
                default:
                    return ""
            }
        },
        // 判断是否开通海外加速
        async checkOverseaProduct() {
            // scc 分了一大堆的二级产品，前端不需要关注二级产品，只需要关注后端返回的这两个字段
            try {
                const {
                    hasOverseaProduct,
                    hasMainLandProduct,
                    salesChannelLine,
                } = await this.$ctFetch(checkOverseaProductUrl, {
                    data: {
                        productCode: this.row?.productCode,
                    },
                });
                this.hasOverseaProduct = hasOverseaProduct;
                this.hasMainLandProduct = hasMainLandProduct;
                this.salesChannelLine = salesChannelLine;
                this.$emit("hasOverseaProduct", this.hasOverseaProduct);
            } catch (e) {
                console.error(e);
            }
        },
        async checkRepeat() {
            if (!this.domain) return;
            // 检查域名是否为重叠域名
            await this.$ctFetch(domainValidateUrl, {
                method: "POST",
                data: {
                    domain: this.domain,
                    operationType: "update",
                },
                headers: {
                    "Content-Type": "application/json",
                },
            })
                .then(({ isRepeat }) => (this.isRepeat = isRepeat))
                .catch(() => {
                    // console.log(err);
                });
        },
        async submit() {
            this.loading = true;
            try {
                await this.$ctFetch(nDomainUrl.updateAreaOrProduct, {
                    method: "POST",
                    data: {
                        type: "areaScope",
                        domain: this.domain,
                        newAreaScope: +this.model,
                    },
                    headers: {
                        "Content-Type": "application/json",
                    },
                });
                this.$message.success(this.$i18n.t("domain.changeAcceArea.tip4"));
                this.$emit("closeWithRefresh");
            } catch (e) {
                const code = e?.data?.code;
                if (code === CODE_QUOTA_EXCEEDED_2396) {
                    this.domainCountLimitLink = e?.data?.reason;
                    this.domainCountLimitDialogVisible = true;
                } else {
                    this.$errorHandler(e);
                }
            } finally {
                this.loading = false;
            }
        },
        async init() {
            this.model = this.row.areaScope + "";
            await this.checkRepeat();
        },
        domainCountLimitSubmit() {
            this.domainCountLimitDialogVisible = false;
        },
    },
};
</script>

<style lang="scss">
.divider {
    margin-bottom: 20px;
}
.block-wrapper {
    display: grid;
    grid-template-columns: 120px 1fr;
}
.aocdn-change-acce-area-radio-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}
.sub-option-note-item {
    padding-left: 16px;
}
p, div, span {
    word-break: normal;
}
</style>
