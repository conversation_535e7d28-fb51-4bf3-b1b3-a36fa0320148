<template>
    <div class="access-region-control">
        <template v-if="accessControlRegionEnable">
            <el-form-item label="区域访问控制" prop="region_access_control.switch">
                <el-switch
                    v-model="form.switch"
                    :active-value="1"
                    :inactive-value="2"
                    @change="onSwitchChange"
                ></el-switch>
            </el-form-item>
            <template v-if="form.switch === 1">
                <el-form-item label="类型" prop="region_access_control.type">
                    <el-radio-group v-model="form.type">
                        <el-radio :label="2">白名单</el-radio>
                        <el-radio :label="1">黑名单</el-radio>
                    </el-radio-group>
                </el-form-item>

                <div v-for="(itm, idx) in form.list" :key="itm.id">
                    <div class="access-control-wrapper">
                        <el-form-item
                            label="国家或地区"
                            :prop="`region_access_control.list.${idx}.country`"
                            :rules="rules.country"
                            :class="{ 'access-control-item': true, 'cascader-disabled-wrapper': disabled }"
                        >
                            <el-cascader
                                v-model="itm.country"
                                :options="countryOptions"
                                :props="{ expandTrigger: 'hover', multiple: true, emitPath: false }"
                                :show-all-levels="false"
                                style="width: 100%"
                                clearable
                                filterable
                                :disabled="disabled"
                            >
                            </el-cascader>
                        </el-form-item>
                        <el-form-item
                            v-show="itm.country.includes(CHINA_CODE)"
                            label="运营商"
                            :prop="`region_access_control.list.${idx}.vendor`"
                            class="access-control-item"
                        >
                            <el-select
                                v-model="itm.vendor"
                                style="width: 100%"
                                clearable
                                filterable
                                multiple
                                :loading="loadingProvider.vendor"
                                :disabled="disabled"
                            >
                                <el-checkbox
                                    style="padding: 4px 18px;"
                                    :value="getIspCheckboxState[idx].value"
                                    :indeterminate="getIspCheckboxState[idx].indeterminate"
                                    @input="() => handleCheckAll('isp', idx)"
                                    >全选</el-checkbox
                                >
                                <el-option
                                    v-for="item in ispOptions"
                                    :key="item.isp"
                                    :label="item.isp_c"
                                    :value="item.isp"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item
                            label="省份"
                            v-show="itm.country.includes(CHINA_CODE)"
                            :prop="`region_access_control.list.${idx}.area_province`"
                            class="access-control-item"
                        >
                            <el-select
                                v-model="itm.area_province"
                                style="width: 100%"
                                clearable
                                filterable
                                multiple
                                placeholder="请选择省份"
                                :loading="loadingProvider.province"
                                :disabled="disabled"
                            >
                                <el-checkbox
                                    style="padding: 4px 18px;"
                                    :value="getPronvinceCheckboxState[idx].value"
                                    :indeterminate="getPronvinceCheckboxState[idx].indeterminate"
                                    @input="() => handleCheckAll('province', idx)"
                                    >全选</el-checkbox
                                >
                                <el-option
                                    v-for="item in provinceOptions"
                                    :key="item.province"
                                    :label="item.province_c"
                                    :value="item.province"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                    </div>
                    <div class="access-control-wrapper">
                        <el-form-item
                            :prop="`region_access_control.list.${idx}.priority`"
                            :rules="rules.priority"
                            class="access-control-item"
                        >
                            <span slot="label"
                                >优先级
                                <el-tooltip
                                    effect="dark"
                                    content="优先级数值越大优先级越高，优先级数值相同的顺序越靠上优先级越高。"
                                >
                                    <i class="el-icon-question"></i>
                                </el-tooltip>
                            </span>
                            <el-input v-model.number="form.list[idx].priority" style="width: 100%"></el-input>
                        </el-form-item>
                        <el-form-item>
                            <el-button
                                icon="el-icon-plus"
                                type="primary"
                                plain
                                circle
                                style="padding:1px"
                                @click="handleOperate('add')"
                                :disabled="disabled"
                            ></el-button>
                            <el-button
                                icon="el-icon-minus"
                                type="primary"
                                plain
                                style="padding:1px"
                                circle
                                :disabled="disabled || form.list.length === 1"
                                @click="handleOperate('del', idx)"
                            ></el-button>
                        </el-form-item>
                    </div>
                </div>
            </template>
        </template>
    </div>
</template>

<script lang="ts">
import { Component, Prop, Vue, Watch } from "vue-property-decorator";
import {
    AccessRegionControlForm,
    AccessRegionControlFormData,
    AccessRegionCountryItem,
    AccessRegionISPItem,
    AccessRegionProvinceItem,
    defaultAccessRegionControlForm,
    defaultAccessRegionControlListItem,
    OrigianlAccessRegionControlListItem,
} from "./util";
import { DomainUrl } from "@/config/url/ipa/domain";
import { get } from "lodash-es";
import { IpaConfigModule } from "@/store/modules/ipa/config";
import { cloneDeep } from "lodash-es";

@Component({
    name: "AccessRegionControl",
})
export default class AccessRegionControl extends Vue {
    @Prop({ type: Object, default: () => defaultAccessRegionControlForm() })
    defaultValue!: AccessRegionControlForm;
    @Prop({ type: Boolean, default: false }) readonly disabled!: boolean;

    protected form: AccessRegionControlForm = defaultAccessRegionControlForm();
    protected countryOptions: AccessRegionCountryItem[] = [];
    protected ispOptions: AccessRegionISPItem[] = [];
    protected provinceOptions: AccessRegionProvinceItem[] = [];
    protected loadingProvider = {
        country: false,
        province: false,
        vendor: false,
    };
    protected CHINA_CODE = "CN"; // 中国区域代码

    private mounted() {
        this.fetchOptionData();
    }

    get rules() {
        return {
            country: [{ required: true, message: "请选择国家或地区", trigger: "blur" }],
            priority: [
                { required: true, message: this.$t("domain.detail.placeholder22"), trigger: "blur" },
                { trigger: "blur", pattern: /^[0-9]*$/, message: this.$t("domain.detail.placeholder21") },
                {
                    validator: (rule: any, value: number, callback: Function) => {
                        if (value > 100 || value < 1) {
                            callback(new Error(this.$t("domain.detail.placeholder21") as string));
                        } else callback();
                    },
                    trigger: ["blur", "change"],
                },
            ],
        };
    }

    get getIspCheckboxState() {
        return this.form.list.map((itm, i) => {
            const checkedCount = itm.vendor.length;
            return {
                indeterminate: checkedCount > 0 && checkedCount < this.ispOptions.length,
                value: checkedCount === this.ispOptions.length,
            };
        });
    }

    get getPronvinceCheckboxState() {
        return this.form.list.map((itm, i) => {
            const checkedCount = itm.area_province.length;
            return {
                indeterminate: checkedCount > 0 && checkedCount < this.provinceOptions.length,
                value: checkedCount === this.provinceOptions.length,
            };
        });
    }

    @Watch("form", { deep: true })
    protected onFormChange() {
        this.$emit("change", this.form);
    }

    @Watch("defaultValue", { immediate: true })
    protected onDefaultValueChange() {
        this.form = this.defaultValue;
    }

    /**
     * 获取表单数据, 供父组件在提交时调用
     */
    get formData(): AccessRegionControlFormData | null {
        if (!this.accessControlRegionEnable) {
            return null;
        }

        const form = cloneDeep(this.form) as AccessRegionControlForm;

        if (form.switch === 2) {
            return { switch: 2 };
        }
        return {
            ...form,
            list: form.list.map(item => {
                return {
                    priority: item.priority,
                    country: item.country.join("|"),
                    area_province: item.country.includes(this.CHINA_CODE) ? item.area_province.join("|") : "",
                    vendor: item.country.includes(this.CHINA_CODE) ? item.vendor.join("|") : "",
                };
            }),
        };
    }

    get accessControlRegionEnable() {
        return IpaConfigModule.ipaRegionAccessControlEnable;
    }

    protected handleOperate(type: "add" | "del", idx: number) {
        if (type === "add") {
            this.form.list.push(defaultAccessRegionControlListItem());
        } else {
            this.form.list.splice(idx, 1);
        }
    }

    protected onSwitchChange() {
        if (this.form.switch === 2) {
            this.form = {
                ...defaultAccessRegionControlForm(),
                switch: 2,
            };
        } else {
            if (!this.form.list.length) {
                this.form.list.push(defaultAccessRegionControlListItem());
            }
        }
    }

    protected handleCheckAll(type: "isp" | "province", idx: number) {
        if (type === "isp") {
            const { value: checked, indeterminate} = this.getIspCheckboxState[idx];
            this.form.list[idx].vendor = !checked || indeterminate ? this.ispOptions.map(itm => itm.isp) : [];
        } else {
            const { value: checked, indeterminate} = this.getPronvinceCheckboxState[idx];
            this.form.list[idx].area_province = !checked || indeterminate ? this.provinceOptions.map(itm => itm.province) : [];
        }
    }

    private fetchData<T>(url: string) {
        return this.$ctFetch<T>(url, { cache: true });
    }

    /**
     * 获取下拉框数据: 国家&地区、省份、运营商
     */
    private async fetchOptionData() {
        this.loadingProvider = {
            country: true,
            province: true,
            vendor: true,
        };

        // 国家&地区
        this.fetchData<{
            result: OrigianlAccessRegionControlListItem[];
        }>(DomainUrl.regionList)
            .then(rst => {
                this.countryOptions = rst.result.map(itm => {
                    const regionItem: AccessRegionCountryItem = {
                        label: itm.continentCn,
                        value: `${itm.continentId}`,
                        children: [],
                    };

                    if (get(itm, ["zones", "length"], 0)) {
                        regionItem.children = itm.zones.map(zone => {
                            const zoneItm: AccessRegionCountryItem = {
                                label: zone.zoneCn,
                                value: `${zone.zoneId}`,
                                children: [],
                            };

                            if (get(zone, ["countries", "length"], 0)) {
                                zoneItm.children = zone.countries.map(country => {
                                    return {
                                        label: country.countryCn,
                                        value: `${country.country}`,
                                    };
                                });
                            }

                            return zoneItm;
                        });
                    }

                    regionItem.children = regionItem?.children?.filter(itm => itm?.children?.length) || [];

                    return regionItem;
                });
            })
            .catch(() => {
                console.warn("获取国家&地区数据失败");
            })
            .finally(() => {
                this.loadingProvider.country = false;
            });
        // 运营商
        this.fetchData<{
            result: AccessRegionISPItem[];
        }>(DomainUrl.regionISP)
            .then(rst => {
                this.ispOptions = rst.result;
            })
            .catch(() => {
                console.warn("获取运营商数据失败");
            })
            .finally(() => {
                this.loadingProvider.vendor = false;
            });
        // 省份
        this.fetchData<{
            result: AccessRegionProvinceItem[];
        }>(DomainUrl.regionProvince)
            .then(rst => {
                this.provinceOptions = rst.result;
            })
            .catch(() => {
                console.warn("获取省份数据失败");
            })
            .finally(() => {
                this.loadingProvider.province = false;
            });
    }
}
</script>

<style lang="scss" scoped>
.access-control-wrapper {
    display: flex;
    flex-wrap: wrap;
    padding-right: 20px;

    .access-control-item {
        min-width: 450px;
        max-width: 33.33%;
        flex: 1;
    }
}
.cascader-disabled-wrapper {
    ::v-deep {
        .el-cascader__tags {
            cursor: auto !important;
            .el-tag > span {
                color: #333 !important;
            }
        }
    }
}
</style>
