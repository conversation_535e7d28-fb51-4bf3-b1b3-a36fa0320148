<template>
    <div class="chart-wrap">
        <div class="title-wrap">
            <div class="operate-btn">
                <el-radio-group v-model="chartType" size="small">
                    <el-radio-button :label="$t('statistics.dcdn.requestWhole.radioBtn1')"></el-radio-button>
                    <el-radio-button label="QPS"></el-radio-button>
                </el-radio-group>
            </div>
        </div>
        <v-chart class="chart" v-loading="loading" :element-loading-text="$t('statistics.common.chart.loading')"
            autoresize theme="cdn" :options="options" />

        <ct-tip>
            {{ $t("statistics.dcdn.requestWhole.tableTip") }}
            <el-tooltip effect="dark" :content="$t('statistics.common.searchDownloadContent')" placement="right">
                <i class="el-icon-download usage-download-icon" @click="downloadTable"></i>
            </el-tooltip>
        </ct-tip>
        <el-table :data="fetchData.daily" stripe v-loading="loading"
            :element-loading-text="$t('statistics.common.table.loading')" :empty-text="$t('common.table.empty')"
            show-summary :summary-method="getSummaries">
            <el-table-column :label="$t('statistics.dcdn.requestWhole.tableColumn1')" type="index" width="100" />
            <el-table-column :label="$t('statistics.dcdn.requestWhole.tableColumn2')" prop="date"></el-table-column>
            <el-table-column v-for="(item, index) in currentTypeList" :key="index" :label="item.label">
                <template slot-scope="{ row }">
                    {{ row[item.value] }}
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<script lang="ts">
import { Component } from "vue-property-decorator";
import { mixins } from "vue-class-component";
import { StatisticsUsageUrl } from "@/config/url/statistics";
import { timeFormat } from "@/filters/index";
import { SearchParams } from "@/types/statistics/usage";
import { RequestQpsFetchData, RequestQps5Min } from "@/types/statistics/usage";
import { requestType, qpsType } from "@/config/map";
import { themeColorArr, THEME_AREA_STYLE } from "@/config/echart/cdn-theme";
import { ProductCodeEnum } from "@/config/map";
import ChartMixin from "../chartMixin";
import { cloneDeep } from "lodash-es";

type tooltipParam = { name: string; marker: string; value: string | number; seriesName: string };

const defaultFetchData: RequestQpsFetchData = {
    "5min": [],
    daily: [],
    total: {
        httpDynamic: 0,
        httpStatic: 0,
        httpsDynamic: 0,
        httpsStatic: 0,
        websocket: 0,
        all: 0,
        timestamp: 0,
        allQPS: 0,
        httpDynamicQPS: 0,
        httpStaticQPS: 0,
        httpsDynamicQPS: 0,
        httpsStaticQPS: 0,
        websocketQPS: 0,
        optimizeDynamic: 0,
        optimizeDynamicQPS: 0,
    },
    totalHttpDynamic: "0",
    totalHttpStatic: "0",
    totalHttpsDynamic: "0",
    totalHttpsStatic: "0",
    scale: 1,
};

@Component({
    name: "RequestWhole",
})
export default class RequestWhole extends mixins(ChartMixin) {
    isWebsocket = false; // 是否为websocket产品
    chartType = `${this.$t("statistics.dcdn.requestWhole.radioBtn1")}`; //
    currentType: Array<keyof RequestQps5Min> = [];

    // 接口数据
    fetchData: RequestQpsFetchData = cloneDeep(defaultFetchData);
    // 用于下载的数据
    protected downloadDataList: RequestQpsFetchData["5min"] = [];

    // 当前展示是否为请求（命中）
    get showHit() {
        return this.chartType === `${this.$t("statistics.dcdn.requestWhole.radioBtn1")}`;
    }
    // A1控制台支持：动态优化请求数，CDN控制台不支持：动态优化请求数
    get currentRequestType() {
        if (window.__POWERED_BY_QIANKUN__) {
            return {
                ...requestType,
                optimizeDynamic: "common.requestType[5]",
            }
        }
        return requestType
    }
    // A1控制台支持：动态优化 QPS，CDN控制台不支持：动态优化 QPS
    get currentQpsType() {
        if (window.__POWERED_BY_QIANKUN__) {
            return {
                ...qpsType,
                optimizeDynamicQPS: "common.qpsType[5]",
            }
        }
        return qpsType
    }

    // 按需增加数据类型
    get localRequestType(): {
        [key in keyof RequestQps5Min]: string;
    } {
        return this.isWebsocket
            ? {
                ...this.qpsTypeList(this.currentRequestType),
                websocket: `${this.$t("statistics.dcdn.requestWhole.localRequestType.websocket")}`,
            }
            : this.qpsTypeList(this.currentRequestType);
    }
    get localQpsType() {
        return this.isWebsocket
            ? {
                ...this.qpsTypeList(this.currentQpsType),
                websocket: "websocket QPS",
            }
            : this.qpsTypeList(this.currentQpsType);
    }
    qpsTypeList(qpsData: object) {
        const listContent = JSON.parse(JSON.stringify(qpsData));
        Object.keys(listContent).forEach(item => {
            listContent[item] = `${this.$t(listContent[item])}`;
        });
        return listContent;
    }
    // 当前数据的种类
    get currentTypeList() {
        const typeContent: any[] = [];
        const { localRequestType } = this;
        (Object.keys(localRequestType) as Array<keyof RequestQps5Min>).forEach(item => {
            typeContent.push({
                label: `${this.$t(localRequestType[item])}${this.getTimesUnit()}`,
                value: item,
            });
        });

        return typeContent;
    }

    // 获取 seriesData 所使用的 key
    get seriesDataKey() {
        return this.showHit ? "all" : "allQPS";
    }
    private async localFetchGenerator<T>(url: string, params: SearchParams) {
        const rst = await this.fetchGenerator<T>(url, params);
        return rst;
    }
    // 1、数据请求
    protected async getData(params: SearchParams) {
        this.isWebsocket = params.product?.[0] === ProductCodeEnum.Socket;

        // 当 websocket 时，需要传递 [2] ，这个因为数据比较复杂，需要后端将其解析为全部
        if (params.product?.[0] === ProductCodeEnum.Socket) params.busiType = [2];
        this.fetchData = await this.localFetchGenerator(StatisticsUsageUrl.qpsDcdnList, {
            ...params,
        });

        if (!this.fetchData) this.fetchData = cloneDeep(defaultFetchData);

        // 处理用于下载的数据
        this.downloadDataList = this.fetchData["5min"];
    }

    // 2、数据处理
    get options() {
        const { localRequestType, localQpsType, showHit } = this;
        const seriesList: any[] = [];
        const xAxisData: string[] = [];
        // 获取计算的缩进单位和进制
        const scale = this.queryUnit[0];
        const unit = this.queryUnit[1];
        this.currentType = Object.keys(showHit ? localRequestType : localQpsType) as Array<
            keyof RequestQps5Min
        >;
        // 配置多条曲线样式
        this.currentType.forEach((type, index) => {
            // 由于需要处理渐变色，所以不再简单的提供数据，而是生成完整配置
            seriesList[index] = {
                name: showHit ? localRequestType[type] : localQpsType[type],
                type: "line",
                data: [],
                areaStyle: THEME_AREA_STYLE[themeColorArr[index]],
            };
        });
        // 曲线数值填充
        (this.fetchData["5min"] || [])
            .sort((a, b) => +a.timestamp - +b.timestamp)
            .forEach(item => {
                xAxisData.push(timeFormat(+item.timestamp * 1000).replace(" ", "\n"));
                this.currentType.forEach((type, index) => {
                    seriesList[index].data.push(((item[type] || 0) / scale).toFixed(scale === 10000 ? 4 : 2));
                });
            });

        // 捕捉的曲线点浮框样式
        const options = {
            ...this.comOptions,
            tooltip: {
                trigger: "axis",
                formatter: (a: tooltipParam[]) => {
                    let tips = a[0].name + "<br>";
                    for (let index = 0; index < a.length; index++) {
                        const { marker, seriesName, value } = a[index];
                        tips += `${marker} ${seriesName} : ${(+value * scale).toFixed(showHit ? 0 : 2)} ${showHit ? `${this.$t("statistics.dcdn.requestWhole.vchartOptions.toolTips")}` : ""
                            }<br>`;
                    }
                    return tips;
                },
            },
            legend: {},
            xAxis: {
                type: "category", // 类目轴，从 data 中获取数据
                boundaryGap: false, // 坐标轴两边留白策略
                axisLabel: {
                    formatter: this.xAxisFormatterGenerator(),
                },
                data: xAxisData,
            },
            yAxis: {
                type: "value", // 数值轴
                name: showHit ? `${this.$t("statistics.dcdn.requestWhole.vchartOptions.yAxisName")}` : "",
                axisLabel: {
                    formatter(val: string): string | undefined {
                        return `${val}${unit}`;
                    },
                },
            },
            series: seriesList,
        };

        return options;
    }

    // 根据数据获取缩进规则
    get queryUnit(): [number, string] {
        const data = this.getMaxFromList(this.fetchData["5min"], this.seriesDataKey);
        if (+data < 10000) {
            return [1, ""];
        } else {
            return [10000, " W"];
        }
    }
    // 天粒度汇总
    getSummaries() {
        const { localRequestType } = this;
        const totalArr = [`${this.$t("statistics.dcdn.requestWhole.summariesName")}`, ""];
        const requestDailyType = Object.keys(localRequestType) as Array<keyof RequestQps5Min>;
        requestDailyType.forEach(item => {
            totalArr.push(this.fetchData.total[item] + "");
        });
        return totalArr;
    }

    // 重写 excel 数据拼接方法
    protected tableToExcel() {
        const { localRequestType, localQpsType } = this;

        let str = `${this.$t("statistics.dcdn.requestWhole.tableToExcel.excelColumn2")},`;
        this.currentType.forEach(item => {
            str += (this.showHit ? localRequestType : localQpsType)[item] + ",";
        });
        str += "\n";
        // 输出格式
        str += this.fetchData["5min"].reduce((str, item) => {
            str += timeFormat(+item["timestamp"] * 1000) + ",";
            this.currentType.forEach(type => (str += item[type].toFixed(this.showHit ? 0 : 2) + ","));

            str += "\n";

            return str;
        }, "");

        //增加总请求数、请求数峰值、请求数谷值
        if (this.showHit) {
            str += `\n${this.$t("statistics.dcdn.requestWhole.tableToExcel.excelColumn1")}\n`;
            const requestDailyType = Object.keys(localRequestType) as Array<keyof RequestQps5Min>;
            requestDailyType.forEach(item => {
                str += `${localRequestType[item]},${this.fetchData.total[item]} \n`;
            });
        }

        this.downloadExcel({
            name: `${this.$t("statistics.usageQuery.BandwidthFlow.tableToExcel.excelName", {
                chartType: this.chartType,
            })}`,
            str,
        });
    }

    protected downloadTable() {
        if (this.checkListIsEmpty(this.fetchData?.daily)) return;

        let str = `${this.$t("statistics.usageQuery.BandwidthFlow.tableColumn1")}`;
        this.currentTypeList.forEach(current => {
            str += `,${current.label}`
        })
        str += "\n";

        this.fetchData.daily.forEach(((item: any) => {
            str += item.date;
            this.currentTypeList.forEach(current => {
                str += `,${item[current.value]}`
            })
            str += "\n";
        }))

        this.downloadExcel({
            name: `${this.$t("statistics.common.tab[2]")}-${this.$t("statistics.dcdn.requestWhole.tableTip")}`,
            str
        })
    }
}
</script>
<style lang="scss" scoped>
.chart-wrap .total {
    .tip {
        @include g-width(100%, 50%, 20%);

        +.tip {
            @include g-width(100%, 50%, 40%);
        }
    }
}
</style>
