<template>
    <component
        :is="curComponent"
        v-bind="$attrs"
        :area-list="areaList"
        :is-from-ipa="isFromIpa"
        @select="handleSelect"
    />
</template>

<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";
import DomesticAreaSelector from "./DomesticAreaSelector.vue";
import WorldwideAreaSelector from "./WorldwideAreaSelector.vue";

@Component({
    name: "AreaSelector",
    model: {
        prop: "selectedArea",
        event: "select",
    },
    components: {
        DomesticAreaSelector,
        WorldwideAreaSelector,
    },
})
export default class AreaSelector extends Vue {
    @Prop({ default: () => [] }) private areaList!: Array<any>;
    @Prop({ default: false }) private isGlobal!: boolean;
    @Prop({ default: false }) private isFromIpa!: boolean;

    get curComponent() {
        return this.isGlobal ? "WorldwideAreaSelector" : "DomesticAreaSelector";
    }

    private handleSelect(val: any) {
        this.$emit("select", val);
        this.$emit("change", val);
    }
}
</script>
