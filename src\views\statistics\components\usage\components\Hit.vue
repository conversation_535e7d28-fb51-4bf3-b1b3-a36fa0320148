<template>
    <div class="chart-wrap">
        <div class="operate-btn">
            <el-radio-group v-model="chartType" size="small">
                <el-radio-button :label="$t('statistics.dcdn.hit.radioBtn1')"></el-radio-button>
                <el-radio-button :label="$t('statistics.dcdn.hit.radioBtn2')"></el-radio-button>
            </el-radio-group>
        </div>

        <div class="total">
            <div class="tip">
                {{
                    $t("statistics.dcdn.hit.totalTip", {
                        totalTipPrefix: showFlow
                            ? `${$t("statistics.dcdn.hit.totalTipPrefix1")}`
                            : `${$t("statistics.dcdn.hit.totalTipPrefix2")}`,
                    })
                }}：
                <span class="num"> {{ peakData[showFlow ? "hitFlowPer" : "hitPer"] | toFixed2 }} % </span>
                <span class="date" v-if="peakData.timestamp">
                    {{ peakData["timestamp"] | timeFormat }}
                </span>
            </div>
        </div>

        <v-chart class="chart" v-loading="loading" :element-loading-text="$t('statistics.common.chart.loading')"
            autoresize theme="cdn" :options="options" />
    </div>
</template>

<script lang="ts">
import { Component } from "vue-property-decorator";
import { mixins } from "vue-class-component";
import { StatisticsUsageUrl } from "@/config/url/statistics";
import { timeFormat } from "@/filters/index";
import { HitCnt, SearchParams } from "@/types/statistics/usage";
import { HitFetchData } from "@/types/statistics/usage";
import { THEME_AREA_STYLE } from "@/config/echart/cdn-theme";
import ChartMixin from "../chartMixin";
import { cloneDeep } from "lodash-es";

const defaultFetchData: HitFetchData = {
    hitcnt: [],
    topFlowHit: 0,
    topRequestHit: 0,
    flowTime: 0,
    requestTime: 0,
};

@Component({
    name: "Hit",
    filters: {
        toFixed2(val: number) {
            return (val || 0).toFixed(2);
        }
    }
})
export default class Hit extends mixins(ChartMixin) {
    chartType = `${this.$t("statistics.dcdn.hit.seriesName1")}`;
    // 接口数据
    fetchData: HitFetchData = cloneDeep(defaultFetchData);
    protected downloadDataList: HitFetchData["hitcnt"] = []; // 用于下载的数据

    // 峰值数据
    get peakData() {
        const { hitcnt } = this.fetchData;
        const key = this.showFlow ? "hitFlowPer" : "hitPer";
        let defaultData: HitCnt = {
            timestamp: 0,
            reqCnt: 0,
            hitReqCnt: 0,
            hitPer: 0,
            flow: 0,
            hitFlow: 0,
            hitFlowPer: 0,
        };

        if (hitcnt.length > 0) {
            hitcnt.forEach(item => {
                if (item[key] > defaultData[key]) {
                    defaultData = { ...item };
                }
            });
        }

        return defaultData;
    }

    // 当前展示是否为流量
    get showFlow() {
        return this.chartType === `${this.$t("statistics.dcdn.hit.seriesName1")}`;
    }
    private async localFetchGenerator<T>(url: string, params: SearchParams) {
        const rst = await this.fetchGenerator<T>(url, params);
        return rst;
    }
    // 1、数据请求
    protected async getData(params: SearchParams) {
        this.fetchData = await this.localFetchGenerator(StatisticsUsageUrl.hitList, params);

        if (!this.fetchData) this.fetchData = cloneDeep(defaultFetchData);

        // 处理用于下载的数据
        this.downloadDataList = this.fetchData["hitcnt"];
    }

    // 2、数据处理
    get options() {
        const xAxisData: string[] = [];
        const seriesData: string[] = [];
        const fetchDataList = this.fetchData["hitcnt"];

        const { showFlow } = this;

        fetchDataList
            .sort((a, b) => a.timestamp - b.timestamp)
            .forEach(item => {
                xAxisData.push(timeFormat(item.timestamp * 1000).replace(" ", "\n"));
                seriesData.push((item[showFlow ? "hitFlowPer" : "hitPer"] || 0).toFixed(2));
            });

        const options = {
            ...this.comOptions,
            tooltip: {
                trigger: "axis",
            },
            xAxis: {
                type: "category", // 类目轴，从 data 中获取数据
                boundaryGap: false, // 坐标轴两边留白策略
                axisLabel: {
                    formatter: this.xAxisFormatterGenerator(),
                },
                data: xAxisData,
            },
            yAxis: {
                type: "value", // 数值轴
                name: `${this.$t("statistics.dcdn.hit.vchartOptions.yAxisName")}`,
            },
            series: [
                {
                    name: showFlow
                        ? `${this.$t("statistics.dcdn.hit.seriesName1")}`
                        : `${this.$t("statistics.dcdn.hit.seriesName2")}`,
                    type: "line",
                    smooth: true, // 平滑曲线
                    data: seriesData,
                    areaStyle: THEME_AREA_STYLE["#358AE2"],
                },
            ],
        };

        return options;
    }

    // 重写 excel 数据拼接方法
    protected tableToExcel() {
        const { showFlow } = this;
        let str = showFlow
            ? `${this.$t("statistics.dcdn.hit.tableToExcel.excelColumn1")}\n`
            : `${this.$t("statistics.dcdn.hit.tableToExcel.excelColumn2")}\n`;

        // 输出格式

        str += this.downloadDataList.reduce((str, item) => {
            str += timeFormat(item["timestamp"] * 1000) + ",";
            if (showFlow) {
                str += (item["hitFlow"] / 1).toFixed(2) + ",";
                str += (item["flow"] / 1).toFixed(2) + ",";
                str += (item["hitFlowPer"] / 1).toFixed(2) + "\n";
            } else {
                str += (item["hitReqCnt"] / 1).toFixed(0) + ",";
                str += (item["reqCnt"] / 1).toFixed(2) + ",";
                str += (item["hitPer"] / 1).toFixed(2) + "\n";
            }
            return str;
        }, "");

        this.downloadExcel({
            name: showFlow
                ? `${this.$t("statistics.dcdn.hit.tableToExcel.excelName1")}`
                : `${this.$t("statistics.dcdn.hit.tableToExcel.excelName2")}`,
            str,
        });
    }
}
</script>

<style lang="scss" scoped>
.total .tip {
    width: 100%;
}
</style>
