<template>
    <el-dialog
        :title="title"
        :visible.sync="visible"
        :append-to-body="true"
        custom-class="ecs-validation-error-modal"
        width="620px"
    >
        <div v-for="(v, index) in validationError" :key="index" class="validation-row flex-box">
            <span class="label-span">{{ v.name }}:</span>
            <div class="flex-box">
                <i class="el-icon-success" v-if="v.pass === 'true'"></i>
                <i class="el-icon-warning" v-else-if="v.pass === 'exception'"></i>
                <i class="el-icon-error" v-else></i>
                <span v-html="v.note"></span>
            </div>
        </div>
        <span slot="footer" class="dialog-footer">
            <el-button type="primary" @click="visible = false">{{ $t("common.dialog.submit") }}</el-button>
        </span>
    </el-dialog>
</template>

<script type="text/javascript">
export default {
    props: {
        value: {
            required: true,
            type: Boolean,
        },
        validationError: {
            required: true,
            type: Array,
        },
        title: {
            required: true,
            type: String,
        },
    },
    data() {
        return {
            visible: this.value,
        };
    },
    watch: {
        value(val) {
            this.visible = val;
        },
        visible(val) {
            this.$emit("input", val);
        },
    },
};
</script>

<style lang="scss" scoped>
::v-deep .ecs-validation-error-modal {
    .flex-box {
        display: flex;
        flex-direction: row;

        i {
            line-height: 26px;
        }
    }

    .validation-row {
        line-height: 26px;
        margin-bottom: 8px;

        .label-span {
            display: inline-block;
            width: 220px;
            margin-right: 8px;
            vertical-align: middle;
            flex-shrink: 0;
        }

        i {
            margin: 0 8px 0 20px;
            &.el-icon-success {
                color: $color-success;
            }
            &.el-icon-error {
                color: $color-danger;
            }
            &.el-icon-warning {
                color: $color-warning;
                font-size: 18px;
            }
        }

        a:hover,
        a:active,
        a:link,
        a:visited,
        strong {
            color: $color-master;
        }
    }
}
</style>
