import { utils, WorkBook, writeFile } from "xlsx";

/**
 * 导出 excel 文件
 * @param sheets 格式：{ sheet名称： sheet数组 }
 * @param fileName 文件名
 */
export function exportExcelFile(sheets: { [key: string]: any[] }, fileName = "example.xlsx") {
    const workBook = Object.keys(sheets).reduce<WorkBook>(
        (workBook, sheetName) => {
            const workSheet = utils.aoa_to_sheet(sheets[sheetName]);
            workBook.SheetNames.push(sheetName);
            workBook.Sheets[sheetName] = workSheet;

            return workBook;
        },
        { SheetNames: [], Sheets: {} }
    );

    return writeFile(workBook, fileName);
}
