<template>
    <div class="plan-wrapper">
        <el-alert type="warning" show-icon :closable="false">
            <i18n slot="title" path="ipManagement.planList.info">
                <a :href="orderLink" target="__blank" class="aocdn-ignore-anchor">{{ $t("ipManagement.planList.info-1") }}</a>
            </i18n>
        </el-alert>
        <div class="search-bar-wrapper">
            <div>
                <el-tooltip
                    effect="light"
                    placement="top-start"
                    popper-class="aocdn-ignore-ip-management-tooltip"
                >
                    <template slot="content">
                        <i class="el-alert__icon cute-icon-warning-circle-fill el-icon-warning aocdn-ignore-ip-management-tip-icon"></i>
                        <span class="test-style" v-html="diabledCreateInfo" />
                    </template>
                    <el-button
                        type="primary"
                        :loading="createLoading"
                        :disabled="!isWhitelistEnable || !isWhitelistUser || !isNotificationConfig"
                        @click="openCreateDialog"
                    >
                        {{ $t("ipManagement.planList.btns[0]") }}
                    </el-button>
                </el-tooltip>
                <el-tooltip effect="light" placement="top" :disabled="isWhitelistUser" popper-class="aocdn-ignore-ip-management-tooltip">
                    <template slot="content">
                        <i class="el-alert__icon cute-icon-warning-circle-fill el-icon-warning aocdn-ignore-ip-management-tip-icon"></i>
                        <span v-html="$t('ipManagement.tip', { orderLink })" />
                    </template>
                    <el-button
                        :disabled="!multipleSelection.length || !isWhitelistUser"
                        @click="deletePlan(multipleSelection)"
                    >
                        {{ $t('ipManagement.planList.btns[1]') }}
                    </el-button>
                </el-tooltip>
            </div>
            <div class="search-bar">
                <label>{{ $t('ipManagement.planList.formTitle[0]') }}</label>
                <el-select v-model="filters.product" filterable>
                    <el-option
                        v-for="opt in productOptions"
                        :key="opt.value"
                        :label="opt.label"
                        :value="opt.value"
                    />
                </el-select>
                <label>{{ $t('ipManagement.planList.formTitle[1]') }}</label>
                <el-input v-model="filters.domain" />
                <el-button type="primary" plain @click="getList">
                    {{ $t('ipManagement.planList.btns[2]') }}
                </el-button>
                <el-button @click="reset" style="margin-right: 0px;">{{ $t('ipManagement.planList.btns[3]') }}</el-button>
            </div>
        </div>
        <el-table
            ref="planList"
            :data="showList"
            @selection-change="handleSelectionChange"
            v-loading="loading"
        >
            <el-table-column type="selection" :selectable="row => row.status !== 2"></el-table-column>
            <el-table-column :label="$t('ipManagement.planList.tableTitle[0]')" show-overflow-tooltip>
                <template slot-scope="{ row }">
                    {{ configNameOptions[row.product] }}
                </template>
            </el-table-column>
            <el-table-column
                prop="domain"
                :label="$t('ipManagement.planList.tableTitle[1]')"
                show-overflow-tooltip
            />
            <el-table-column prop="create_time" :label="$t('ipManagement.planList.tableTitle[2]')">
                <template slot-scope="{ row }">
                    {{ timeFormat(row.create_time) }}
                </template>
            </el-table-column>
            <el-table-column :label="$t('ipManagement.planList.tableTitle[3]')">
                <template slot-scope="{ row }">
                    {{ statusMap[row.status] }}
                </template>
            </el-table-column>
            <el-table-column
                :label="$t('ipManagement.planList.tableTitle[4]')"
                prop="operation"
                min-width="80"
            >
                <template slot-scope="{ row }">
                    <div class="icon-style">
                        <el-button v-if="row.status !== 2" type="text" @click="viewIpDetails(row)">
                            {{ $t('ipManagement.planList.btns[4]') }}
                        </el-button>
                        <el-tooltip effect="dark" :disabled="isWhitelistUser" popper-class="aocdn-ignore-ip-management-tooltip">
                            <template slot="content">
                                <span v-html="$t('ipManagement.tip', { orderLink })" />
                            </template>
                            <el-button
                                v-if="row.status !== 2"
                                type="text"
                                :disabled="!isWhitelistUser"
                                @click="deletePlan(row)"
                            >
                                {{ $t('ipManagement.planList.btns[5]') }}
                            </el-button>
                        </el-tooltip>
                    </div>
                </template>
            </el-table-column>
        </el-table>
        <div class="pager">
            <el-pagination
                :small="isXs"
                :layout="isXs ? 'prev, pager, next' : 'total, sizes, prev, pager, next,jumper'"
                :total="total"
                :current-page.sync="pageNum"
                :page-size.sync="pageSize"
                :page-sizes="[10, 30, 50, 100]"
                :hide-on-single-page="false"
                @size-change="handlePageSizeChange"
                @current-change="handleCurrentPageChange"
            ></el-pagination>
        </div>
        <create-plan-dialog
            v-if="dialog.create"
            :disabled-domain-list="disabledDomainList"
            :white-list-limit="whiteListLimit"
            @cancel="dialog.create = false"
            @submit="afterCreatePlan"
        />
        <back-origin-ip-dialog v-if="dialog.view" :ip-info="ipInfo" @cancel="dialog.view = false" />
        <operation-feedback-dialog
            v-if="dialog.feedback"
            :status="feedbackStatus"
            :domain-list="failedDomainList"
            @cancel="dialog.feedback = false"
        />
    </div>
</template>

<script lang="ts">
import { Component, Mixins, Prop } from "vue-property-decorator";
import { ScreenModule } from "@/store/modules/screen";
import TableAndPagerActionMixin from "@/views/log/tableAndPagerAction";
import { commonLinks } from "@/utils/logic/url";
import { PlanItem } from "@/types/ipManagement";
import CreatePlanDialog from "./components/CreatePlanDialog.vue";
import BackOriginIpDialog from "./components/BackOriginIpDialog.vue";
import OperationFeedbackDialog from "./components/OperationFeedbackDialog.vue";
import { StatusMap } from "@/config/map";
import { ipManagementUrl } from "@/config/url/ipManagement";
import { timeFormat } from "@/filters/index";
import errorHandler from "@/utils"
import { DomainModule } from "@/store/modules/domain";
import { DomainActionEnum } from "@/config/map";

@Component({
    name: "PlanList",
    components: {
        CreatePlanDialog,
        BackOriginIpDialog,
        OperationFeedbackDialog,
    },
})
export default class PlanList extends Mixins(TableAndPagerActionMixin) {
    @Prop({ default: false, type: Boolean }) private isWhitelistUser!: boolean;
    @Prop({ default: false, type: Boolean }) private isWhitelistEnable!: boolean;
    @Prop({ default: 20 }) private whiteListLimit!: number;
    private createLoading = false;
    private timeFormat = timeFormat;
    private statusMap = StatusMap;
    private dialog = {
        create: false,
        view: false,
        feedback: false,
    };
    private filters = {
        product: "020",
        domain: "",
    };
    private filterList: Array<PlanItem> = [];
    private multipleSelection: Array<PlanItem> = [];
    private failedDomainList: Array<string> = [];
    private feedbackStatus = "";
    private ipInfo = "";
    private configNameOptions: any = {
        "020": this.$t("ipManagement.sendList.configNameOption1"),
    };
    private isNotificationConfig = false;
    private disabledDomainList: Array<string> = [];
    private styleString = `.test-style {
        color: yellow
    }`

    get screenWidth() {
        return ScreenModule.width;
    }
    get isXs() {
        return this.screenWidth < 600;
    }
    get orderLink() {
        return commonLinks.orderLink;
    }
    get diabledCreateInfo() {
        if (!this.isWhitelistUser) {
            return this.$t("ipManagement.tip", { orderLink: this.orderLink });
        } else if (!this.isWhitelistEnable) {
            return this.$t('ipManagement.planList.tip');
        } else if (!this.isNotificationConfig) {
            return this.$t("ipManagement.planList.info2", { orderLink: this.orderLink });
        }
        return this.$t("ipManagement.tip1");
    }
    get productOptions() {
        return [{ label: this.$t("ipManagement.planList.productOption[0]"), value: "020" }];
    }
    // 最终展示的分页数据
    get showList() {
        // 过滤完成后，再分页
        const start = (this.pageNum - 1) * this.pageSize;
        const end = this.pageNum * this.pageSize;
        const showList = this.filterList.slice(start, end);
        this.total = this.filterList.length;
        return showList;
    }

    mounted() {
        this.getList();
        this.getConfigDetail();
    }

    /**
     * 查询回源IP方案列表
     */
    private searchList() {
        const searchVal = this.filters.domain.trim();
        if (searchVal) {
            this.filterList = this.dataList.filter((item: PlanItem) => item.domain.includes(searchVal));
        } else {
            this.filterList = this.dataList;
        }
    }

    /**
     * 获取回源IP方案列表
     */
    private async getList() {
        this.loading = true;
        this.dataList = await this.$ctFetch(ipManagementUrl.GetWhitelist, {
            data: {
                product: this.filters.product,
            },
        });
        this.disabledDomainList = this.dataList.map(item => item.domain);
        const nativeList = DomainModule[DomainActionEnum.Data].nativeList.map(item => item.domain)
        this.dataList = this.dataList
            .filter(item => nativeList.includes(item.domain))
            .sort((a, b) => {
                const isConfiguring = (a: PlanItem) => a.status === 2;
                return +isConfiguring(b) - +isConfiguring(a) || +b.create_time - +a.create_time;
            });
        this.searchList();
    }

    /**
     * 获取配置项
     */
    private async getConfigDetail() {
        try {
            this.createLoading = true
            const { email, voice_phone, msg_phone } = (await this.$ctFetch(ipManagementUrl.GetNotify, {
                data: {
                    product: this.filters.product,
                    masking: true,
                },
            })) as any;
            this.isNotificationConfig = !!(email || voice_phone || msg_phone);
        } catch (e) {
            errorHandler(e);
        } finally {
            this.createLoading = false
        }
    }

    /**
     * 重置筛选条件
     */
    private reset() {
        this.filters = {
            product: "020",
            domain: "",
        };
        (this.$refs.planList as any).clearSelection();
        this.getList();
    }

    /**
     * 打开配置方案弹窗
     */
    private openCreateDialog() {
        this.dialog.create = true;
    }

    /**
     * 查看回源IP
     * @param row 待查看方案信息
     */
    private async viewIpDetails(row: PlanItem) {
        this.ipInfo = await this.getIp(row);
        this.dialog.view = true;
    }

    /**
     * 获取IP信息
     */
    private async getIp(row: PlanItem) {
        this.loading = true;
        const { info } = (await this.$ctFetch(ipManagementUrl.IpDetail, {
            data: { id: row.id },
        })) as any;
        return info;
    }

    /**
     * 配置方案回调
     * @param obj 回调对象
     * @param faildList 失败域名列表
     */
    private afterCreatePlan(obj: any) {
        this.dialog.create = false;
        if (obj.status === "success") {
            this.$message.success(this.$t("ipManagement.planList.operation.success") as string);
        } else if (obj.status === "failed") {
            this.$message.error(this.$t("ipManagement.planList.operation.failed") as string);
        } else {
            this.feedbackStatus = obj.status;
            this.failedDomainList = obj.failedList;
            this.dialog.feedback = true;
        }
        this.getList();
    }

    /**
     * 删除方案
     * @param data 待删除的方案信息
     */
    private async deletePlan(data: any) {
        if (data.length > this.whiteListLimit) {
            this.$message.error(this.$t("ipManagement.planList.validInfo[2]", { count: this.whiteListLimit }) as string)
            return
        }
        await this.$confirm(
            this.$t("ipManagement.planList.confirmInfo") as string,
            this.$t("ipManagement.planList.btns[5]") as string,
            {
                confirmButtonText: this.$t("ipManagement.planList.btns[6]") as string,
                cancelButtonText: this.$t("ipManagement.planList.btns[7]") as string,
                type: "warning",
            }
        );
        if (!Array.isArray(data)) {
            data = [data];
        }

        this.loading = true;
        const { failed_list } = (await this.$ctFetch(ipManagementUrl.WhitelistDelete, {
            method: "POST",
            data: {
                product: "020",
                domains: data.map((item: PlanItem) => item.domain),
            },
            headers: {
                "Content-Type": "application/json",
            },
        })) as any;
        if (failed_list.length === 0) {
            this.$message.success(this.$t("ipManagement.planList.operation.success") as string);
        } else if (failed_list.length === data.length) {
            this.$message.error(this.$t("ipManagement.planList.operation.failed") as string);
        } else {
            this.feedbackStatus = "partialSuccess";
            this.failedDomainList = failed_list;
            this.dialog.feedback = true;
        }
        this.getList();
    }

    /**
     * 表格勾选回调
     * @param selection 当前勾选项
     */
    private handleSelectionChange(selection: Array<PlanItem>) {
        this.multipleSelection = selection;
    }

    /**
     * 当前分页size变化回调
     * @param pageSize 待更改的分页size
     */
    private handlePageSizeChange(pageSize: number) {
        this.pageSize = pageSize;
        this.pageNum = 1;
    }
    /**
     * 当前分页变化回调
     * @param page 待跳转的分页
     */
    private handleCurrentPageChange(page: number) {
        this.pageNum = page;
    }
}
</script>

<style lang="scss" scoped>
.plan-wrapper {
    .info {
        color: $color-neutral-9;
    }
    .search-bar-wrapper {
        margin-top: var(--margin-3x);
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
        align-items: center;
        gap: 12px;
        .search-bar {
            display: flex;
            gap: var(--margin-3x);
            flex-wrap: wrap;
            align-items: center;
            > div {
                flex-shrink: 0;
                display: flex;
                align-items: center;
                flex-wrap: nowrap;
                label {
                    font-size: 12px;
                }
            }
        }
    }
}
.aocdn-ignore-ip-management-tip-icon {
    color: $color-warning;
    font-size: 14px;
    margin-right: 4px;
}
</style>
<style lang="scss">
.aocdn-ignore-ip-management-tooltip {
	a {
		color: #3d73f5;
	}
}
</style>
