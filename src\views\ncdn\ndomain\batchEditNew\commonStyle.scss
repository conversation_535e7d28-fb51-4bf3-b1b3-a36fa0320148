.ct-section-wrap {
    height: 100%; // 定高，出现滚动条
    display: flex;
    flex-direction: column;

    ::v-deep .el-scrollbar {
        flex: 1; // 自适应高度
    }

    ::v-deep .el-scrollbar__wrap {
        scroll-behavior: smooth;

        .el-scrollbar__view {
            padding: 0; // 内容区固定一个间距出来
            height: 100%;
        }

        overflow-x: hidden;
    }

    ::v-deep {
        .box.form-box {
            margin-bottom: 8px;
        }

        .el-scrollbar {
            margin-bottom: 0 !important;
        }
    }
}

.simple-create-form {
    ::v-deep {
        .el-form-item__error {
            padding: 6px 0;
            width: 100%;
            // width: 200px;
        }
    }

    .prompt {
        display: inline-block;
        color: $color-neutral-7;
        font-size: 12px;
        line-height: 12px;
        font-weight: normal;
        margin-left: 4px;
    }

    .tooltips {
        font-size: 12px;
        line-height: 12px;
        margin-top: 8px;
        color: $color-neutral-7;
    }

    .tooltips-error {
        color: $theme-color !important;
    }

    .select-action-wrapper {
        .select.el-select {
            width: 360px;
            margin-right: 8px;
        }
    }
}

.wrap {
    .http-div {
        ::v-deep {
            .el-form-item.el-form-item--medium {
                .el-form-item__label {
                    width: 45px !important;
                }
            }

            .el-form-item__content {
                margin-left: 46px !important;
            }
        }
    }

    .https-div {
        ::v-deep {
            .el-form-item.el-form-item--medium {
                .el-form-item__label {
                    width: 68px !important;
                }
            }

            .el-form-item__content {
                margin-left: 68px !important;
            }
        }
    }

    .input-box {
        width: 142px !important;
    }
}

.origin-port-wrapper {
    .http-port-wrapper {
        .http-port-label {
            ::v-deep {
                .el-form-item__label {
                    width: 36px !important;
                }

                .el-form-item__content {
                    margin-left: 60px !important;
                    margin-right: 100px !important;
                }

                .el-form-item__error {
                    padding: 6px 0;
                    width: 300px !important;
                }
            }
        }
    }

    ::v-deep {
        .el-form-item__error {
            padding: 6px 0;
            width: 300px !important;
        }
    }

    .https-port-wrapper {
        margin-left: 180px !important;

        ::v-deep {
            .el-form-item.el-form-item--medium {
                .el-form-item__label {
                    margin-left: 46px !important;
                    width: 34px !important;
                }
            }

            .el-form-item__content {
                margin-left: 111px !important;
            }
        }
    }

    .https-port-wrapper2 {
        ::v-deep {
            .el-form-item.el-form-item--medium {
                .el-form-item__label {
                    width: 45px !important;
                }
            }

            .el-form-item__content {
                margin-left: 56px !important;
            }
        }
    }

    .input-box {
        width: 142px !important;
    }
}

.res-header-style {
    display: flex;
    width: 100px;
    margin-left: $margin-9x;

    .question-style {
        margin-left: $margin-2x;
    }
}

.req-header-style {
    display: flex;
    width: 120px;
    margin-left: $margin-3x;

    .question-style {
        margin-left: $margin-2x;
    }
}

.dynamic-wrap {
    margin: 20px 0 0 0;

    .dynamic-style {
        width: 48px;
        height: 18px;
        // font-family: element-icons;
        font-size: 12px;
        color: $color-neutral-10;
        letter-spacing: 0;
        text-align: right;
        line-height: 18px;
        font-weight: 400;
        margin-left: 32px;
        margin-right: 28px;
    }

    .dynamic-box {

        // .icon-tip {
        //   font-size: 14px;
        //   color: $color-neutral-7;
        //   cursor: pointer;
        //   margin-left: 20px;
        // }
        .tips {
            font-size: 12px;
            color: $color-neutral-7;
            font-weight: 400;
            margin-left: $margin-3x;
        }
    }
}

.tooltips {
    .icon-tip {
        font-size: 14px;
        color: $color-neutral-7;
        cursor: pointer;
        margin-right: $margin-2x;
    }

    .tip-style {
        width: 252px !important;
        height: 18px !important;
        // font-family: element-icons;
        font-size: 12px;
        color: $color-neutral-7;
        line-height: 18px;
        font-weight: 400;
    }

    .btn-bg {
        color: $color-master;
        cursor: pointer;
        height: 18px !important;
        // font-family: element-icons;
        font-size: 12px;
        color: $color-master;
        letter-spacing: 0;
        line-height: 18px;
        font-weight: 400;
    }
}

.dynamic-wrapper {
    // width: 758px;
    width: 700px;
    // height: 72px;
    background: $color-neutral-1;
    margin-top: 8px;
    margin-left: 140px;

    ::v-deep {
        .el-form-item.el-form-item--medium {
            line-height: 18px !important;
            // font-family: element-icons;
            font-size: 12px;
            color: $color-neutral-10;
            letter-spacing: 0;
            text-align: left;
            line-height: 18px;
            font-weight: 400;
            margin-bottom: 0px;

            .el-form-item__label {
                // width: 145px !important;
            }
        }

        .el-form-item__label {
            text-align: right;
            padding-right: 20px;
            // width: 145px !important;
        }
    }

    .question-style {
        font-size: $text-size-md;
        color: $color-neutral-9;
        cursor: pointer;
    }
}

.force-style {
    display: flex;
    flex-direction: column;
    justify-content: center;
    flex-wrap: wrap;
    width: 700px;
    height: 116px;
    background: $color-neutral-1;
    margin-left: 140px;

    .jump-style {
        margin-top: 10px;
    }

    ::v-deep {
        .el-form-item.el-form-item--medium {
            margin-bottom: 0px;
        }

        .el-form-item__error {
            padding-top: 0px !important;
        }
    }
}

// 用于el-table表格内：输入框/下拉框的宽度
.input-style {
    width: 100%;
}

.input-wrapper {
    width: 380px;

    ::v-deep {
        .el-input-group__append {
            padding: 0 22px 0 12px !important;
        }
    }
}

.question-circle {
    font-size: $text-size-md;
    cursor: pointer;
}

.collapse-style {
    ::v-deep {
        .el-collapse-item__header {
            // font-family: element-icons;
            font-size: 12px;
            color: $color-neutral-10;
            letter-spacing: 0;
            line-height: 18px;
            font-weight: 500;
            // width: calc(100% - 120px);
            border: 0;
        }

        .el-collapse-item__content {
            padding: 0px !important;
            border: 0;
        }

        .el-collapse-item__wrap {
            // margin-top: -20px;
            margin-left: -20px !important;
        }
    }
}

.ct-table-form-item {
    ::v-deep {
        .el-table {
            margin: 0;
        }

        .el-form-item {
            margin: 16px 0 20px 0;
        }

        .el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell {
            background: #fff !important;
        }

        .el-table--medium {
            .el-table__body {
                .el-table__row {
                    .el-table__cell {
                        // padding: 0;
                        // padding-bottom: 4px;
                        // vertical-align: top;
                    }
                }
            }
        }
    }

    .ct-sort-drag-icon {
        font-size: $text-size-md;
    }
}

.table-form-item-style {
    ::v-deep {
        .el-table {
            margin: 0;
        }

        .el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell {
            background: #fff !important;
        }

        // .el-table--medium {
        //   .el-table__body {
        //     .el-table__row {
        //       .el-table__cell {
        //         padding-bottom: 4px !important;
        //       }
        //     }
        //   }
        // }
    }
}

.cache-table-style {
    ::v-deep {
        .el-table {
            margin: 0;
        }

        .el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell {
            background: #fff !important;
        }
    }
}

.code-style {
    .el-form-item {
        // margin-bottom: $margin-6x;
    }

    ::v-deep {
        .el-form-item__error {
            // width: 300px;
            width: 100%;
        }
    }
}

.el-form {
    ::v-deep {

        // 表格模块样式
        .form-section+.form-section {
            border-top: 1px solid #e2e5ed;
        }

        .form-section.no-border {
            border: none;
        }

        // 表格自适应
        .auto-table {
            .el-table__row {
                .el-table__cell {
                    vertical-align: top;
                    padding: 4px 0;

                    &>* {
                        line-height: 32px;
                    }

                    &:first-child {
                        // .cell {
                        //   padding-left: 10px;
                        // }
                    }

                    &:last-child {
                        .cell {
                            padding-right: 10px;
                        }
                    }
                }

                .el-form-item {
                    // margin: 32px 0 32px 0 !important;
                    margin: 0;
                    line-height: 0;
                }

                //.el-form-item.is-error {
                //    margin-bottom: 32px;
                //}
            }
        }
    }

    // 被按钮控制显隐的模块样式
    .switch-wrapper {
        padding: 12px 12px 4px 12px;
        margin-bottom: 20px;
        // width: calc(100% - 261px);
        margin-left: 141px; // 由label-width决定
        background-color: #F7F8FA;
        overflow: auto;
    }
}

// 保存按钮样式
.submit {
    .footer-content {
        display: flex;
        justify-content: flex-end;

        .el-button {
            margin-left: 16px !important;
        }
    }
}

.basic-style {
    margin-bottom: $margin-5x;
}

// 回源端口样式
.port-label {
    margin-right: 10px;
}

.port-input {
    margin-right: 20px;
    width: 168px !important;
}

.required {
    &:before {
        content: "*";
        color: rgb(245, 108, 108);
        margin-right: 4px;
    }
}

.ct-sort-drag-icon {
    // font-size: 14px;
    font-size: $text-size-md;
}

.word-wrap {
    display: inline;
}

// 锚点样式
.anchor-style {
    width: 155px;
    position: relative;
    // top: 340px;
    right: 5px;
    // bottom: 0;
}

.line-style {
    width: calc(100% - 40px);
    margin-left: 20px;
    margin-top: 20px;
    border-bottom: 1px solid $color-neutral-3;
}

.icon-column-label {
    color: $text-color-light;
    margin-left: $margin-2x;
    font-size: $text-size-md;
    line-height: 18px;
    height: 14px;
    width: 14px;
}

.icon-column-label2 {
    margin-left: 0 !important;
}

.icon-column-label1 {
    color: $text-color-light;
    margin-right: $margin;
    font-size: $text-size-md;
    line-height: 18px;
    height: 14px;
    width: 14px;
}

.prompt {
    margin: 0px 0 0 12px !important;
    color: $neutral-7;
}

.error-prompt {
    color: $color-danger !important;
}

.https-tip {
    font-size: 12px;
    color: $color-neutral-7;
    font-weight: 400;
    margin-left: $margin-2x;
}

.ct-table-wrapper {
    // width: 800px !important;
    // width: calc(100% - 120px) !important;
}

.flex-row-style {
    display: flex;
    flex-direction: row;
    align-items: center;

    &.space-between {
        justify-content: space-between;
    }
}

.flex-row-style.button-box {
    background-color: #fff;
}

.button-box {
    // width: calc(100% - 120px);
    // width: 800px !important;
    width: 100%;
    justify-content: center;
    border-bottom: 1px solid $color-neutral-3;

    .el-button {
        margin: $margin-3x 0;
    }
}

.origin-tip-header {
    margin-bottom: 16px;
}

.origin-tip {
    color: $color-neutral-7;
    margin-left: 12px;
}
