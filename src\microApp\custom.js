export default class Custom {
    on(name, cb) {
        window.addEventListener(name, e => {
            cb(e.detail);
        });
    }
    emit(name, data) {
        const event = new CustomEvent(name, {
            detail: data,
        });
        window.dispatchEvent(event);
    }
    remove(name, cb) {
        window.removeEventListener(name, e => {
            cb(e.detail);
        });
    }
}
