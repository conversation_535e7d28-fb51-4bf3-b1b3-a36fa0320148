<template>
    <div class="ip-set-list">
        <div class="search-bar-wrapper">
            <div class="search-bar">
                <div class="search-bar-item">
                    <label>{{ $t("ipSet.form.IP集名称") }}</label>
                    <el-input
                        class="custom-input"
                        v-model="alias_name"
                        clearable
                        :placeholder="$t('ipSet.请输入IP集名称')"
                    />
                </div>
                <div class="search-bar-item">
                    <label>{{ $t("ipSet.common.域名") }}</label>
                    <el-input
                        class="custom-input"
                        v-model="domain"
                        clearable
                        :placeholder="$t('ipSet.请输入域名')"
                    />
                </div>
                <div class="search-bar-item">
                    <el-button type="primary" plain @click="searchUsage">{{ $t("ipSet.查询") }}</el-button>
                </div>
            </div>
        </div>
        <div class="table-wrapper">
            <el-table :data="usageList" v-loading="loading" height="calc(100% - 54px)">
                <el-table-column :label="$t('common.table.index')" type="index" width="60" />
                <el-table-column :label="$t('ipSet.form.IP集名称')" prop="alias_name" />
                <el-table-column
                    :label="$t('ipSet.common.域名')"
                    prop="domain"
                    align="left"
                    show-overflow-tooltip
                />
                <el-table-column :label="$t('common.table.operation')" width="100">
                    <template #default="{ row }">
                        <el-button type="text" @click="downloadUsage(row)">{{
                            $t("ipSet.operation.下载")
                        }}</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <div class="pager">
                <el-pagination
                    :small="isXs"
                    :layout="isXs ? 'prev, pager, next' : 'total, sizes, prev, pager, next, jumper'"
                    :total="total"
                    :current-page.sync="page"
                    :page-size="pageSize"
                    :page-sizes="[10, 30, 50, 100]"
                    :hide-on-single-page="false"
                    @size-change="handleSizeChange"
                    @current-change="handlePageChange"
                ></el-pagination>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import { ScreenModule } from "@/store/modules/screen";
import { IpSetUsageItem, IpSetTypeEnum } from "@/types/ipSet";
import { IpSetUrl } from "@/config/url/ipSet";
import { nUserModule } from "@/store/modules/nuser";
import { checkCtiamButtonAuth } from "@/store/modules/menu";
import { GetCtiamButtonAction } from "@/store/config";

@Component({})
export default class IpSetUsage extends Vue {
    // 数据
    private loading = false;
    private alias_name = "";
    private domain = "";
    private usageList: IpSetUsageItem[] = [];
    private total = 0;
    private page = 1;
    private pageSize = 10;

    // 计算属性
    get isXs() {
        return ScreenModule.width < 600;
    }

    // 生命周期钩子
    private created() {
        this.loadUsageData();
    }

    // 方法
    private async loadUsageData() {
        this.loading = true;
        const rst = await this.$ctFetch<{ list: IpSetUsageItem[]; total: number }>(IpSetUrl.usage, {
            method: "GET",
            transferType: "json",
            data: {
                pageIndex: this.page,
                pageSize: this.pageSize,
                alias_name: this.alias_name,
                domain: this.domain,
            },
        });
        this.usageList = rst.list;
        this.total = rst.total;
        this.loading = false;
    }
    /**
     * 重置分页并加载使用情况数据
     *
     * 此方法用于将当前页面重置为第一页，然后调用loadUsageData方法来加载使用情况数据
     * 为什么这么做：在进行搜索或重新加载数据时，确保从第一页开始是合理的默认行为
     */
    private searchUsage() {
        this.page = 1;
        this.loadUsageData();
    }
    /**
     * 处理每页条数变化的函数
     *
     * @param size 每页显示的条数
     */
    private handleSizeChange(size: number) {
        this.pageSize = size;
        this.loadUsageData();
    }
    /**
     * 当页面变化时调用此方法来加载新的页面数据
     * 此方法没有参数
     * 该方法不会返回任何值
     */
    private handlePageChange() {
        this.loadUsageData();
    }
    /**
     * 异步下载使用情况
     *
     * 此函数用于下载特定项目的使用情况数据它根据传入的行数据生成下载URL，并在新窗口中打开该URL
     * 以实现下载功能选择这种实现方式是因为它允许在不干扰当前页面状态的情况下触发文件下载
     *
     * @param row IpSetUsageItem类型的对象，包含需要下载的项目的唯一名称和工作空间ID
     */
    private async downloadUsage(row: IpSetUsageItem) {
        await checkCtiamButtonAuth(GetCtiamButtonAction("ipSetUsageDownload"));

        const url = `${window.location.origin}${IpSetUrl.download}?unique_name=${row.unique_name}&workspaceId=${nUserModule.workspaceId}`;
        window.open(url);
    }
}
</script>

<style lang="scss" scoped>
.ip-set-list {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.search-bar-wrapper {
    display: flex;
    justify-content: flex-end;
    flex-shrink: 0;
}

.search-bar {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
    flex-shrink: 0;
    &-item {
        display: flex;
        gap: 12px;
        align-items: center;
        label {
            flex-shrink: 0;
            font-size: 12px;
        }
    }
    .custom-input {
        ::v-deep {
            .el-input__inner {
                padding-right: 12px;
            }
        }
    }
}

.table-wrapper {
    flex: 1;
    min-height: 0;
    flex-direction: column;
}

.pager {
    display: flex;
    justify-content: flex-end;
    margin-top: 12px;
}
</style>
