import i18n from "@/i18n";

// 任务状态映射
export const TaskStatusMap2 = <const>{
    "1": i18n.t("refresh.status[0]"),
    "2": i18n.t("refresh.status[1]"),
    "3": i18n.t("refresh.status[2]"),
    "4": i18n.t("refresh.status[3]"),
    "5": i18n.t("refresh.status[4]"),
};

export const TaskStatusOptions2 = <const>[
    {
        label: i18n.t("refresh.status[0]"),
        value: "1",
    },
    {
        label: i18n.t("refresh.status[1]"),
        value: "2",
    },
    {
        label: i18n.t("refresh.status[2]"),
        value: "3",
    },
    {
        label: i18n.t("refresh.status[3]"),
        value: "4",
    },
];

export const TaskStatusMapSchedule = <const>{
    "0": "已停用",
    "1": "已启用",
    "2": "已过期",
};

export const TaskStatusOptionsSchedule = <const>[
    {
        label: "已停用",
        value: 0,
    },
    {
        label: "已启用",
        value: 1,
    },
    {
        label: "已过期",
        value: 2,
    },
];

// 任务类型枚举值
export const RefreshTypeEnum2 = <const>{
    UrlRefresh: "1", // url 刷新
    DirRefresh: "2", // 目录刷新
    UrlPrefetch: "3", // url 预取
    RegRefresh: "4", // 正则刷新
    Schedule: "5", // 正则刷新
};

// 类型枚举值对应的 type 参数
export const RefreshTypeMap2 = <const>{
    "1": "url",
    "2": "dir",
    "3": "url",
    "4": "re",
    "5": "schedule",
};
