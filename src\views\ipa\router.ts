import { RouteConfig } from "vue-router";
import { DomainModule } from "@/store/modules/domain";
import { DomainActionEnum } from "@/config/map";
import { ScaleModule } from "@/store/modules/scale";

const indexRouter: RouteConfig[] = [
    {
        path: "/eas",
        redirect: {
            name: "eas.overview",
        },
        component: () => import("./index.vue"),
        children: [
            {
                path: "statistics",
                name: "eas.statistics",
                component: () => import("./statistics/index.vue"),
                meta: {
                    breadcrumb: {
                        title: "$t('statistics.eas.tip1')",
                        route: ["home", "eas.statistics"],
                    },
                    perm: "eas.statistics",
                },
            },
        ],
        beforeEnter(to, from, next) {
            // 不阻塞页面加载
            DomainModule.GetDomainList({ action: DomainActionEnum.Refresh });
            ScaleModule.GetIpaScale()
            next();
        },
    },
    {
        path: "/overview/easOverview",
        name: "eas.overview",
        component: () => import("./overview/index.vue"),
        meta: {
            isSkipMicroApp: true,
            breadcrumb: {
                title: "$t('statistics.eas.tip7')",
                route: ["home", "eas.overview"],
            },
        },
        beforeEnter(to, from, next) {
            // 不阻塞页面加载
            ScaleModule.GetIpaScale()
            next();
        },
    },
];
export default indexRouter;
