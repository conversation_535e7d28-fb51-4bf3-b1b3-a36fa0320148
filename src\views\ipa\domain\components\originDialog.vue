<template>
    <el-dialog
        :title="dialogTitle"
        :visible="visible"
        append-to-body
        width="560px"
        @close="$emit('cancel')"
        :close-on-click-modal="false"
    >
        <!-- 宽度由50px改为52px，解决本地和线上继承字体不同造成的文案掉落现象-->
        <el-form :model="form" :rules="rules" ref="form" label-width="52px">
            <!-- IP或域名校验 -->
            <el-form-item label="源站" prop="address" :rules="rules.address">
                <el-input v-model="form.address"></el-input>
            </el-form-item>
            <el-form-item label="角色" required>
                <el-select v-model="form.role" @change="form.level = 1" style="width: 100%;">
                    <el-option label="主源" :value="1"></el-option>
                    <el-option label="备源" :value="2"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="层级" required>
                <el-select v-model="form.level" placeholder="请选择" style="width: 100%;">
                    <el-option
                        v-for="item in form.role === 1 ? [1] : [1, 2, 3, 4, 5]"
                        :key="item"
                        :label="item"
                        :value="item"
                    >
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="权重" prop="weight">
                <el-input v-model.number="form.weight" :disabled="weightDisabled"></el-input>
            </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
            <el-button @click="$emit('cancel')">取 消</el-button>
            <el-button type="primary" @click="addConfirm">确 定</el-button>
        </div>
    </el-dialog>
</template>
<script lang="ts">
import { Component, Prop, Watch, Vue } from "vue-property-decorator";
import ipRegex from "../utils/ip-regex";
import { Rules } from "@/types/element-ui/formRules";
import { ElForm } from "element-ui/types/form";
import { cloneDeep } from "lodash-es";

type OriginConf = {
    address: string;
    role: 1 | 2;
    level: 1 | 2 | 3 | 4 | 5;
    weight: number;
};

const getDefaultOrigin = (): OriginConf => {
    return {
        address: "",
        role: 1,
        level: 1,
        weight: 10,
    };
};

// IP或域名校验
const ipRegexp = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/;
const domainRegexp = /^(\*\.|\*)?([a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,6}$/;

const operateTypeMap = {
    edit: "编辑",
    create: "新增",
};

@Component({
    name: "domain-origin-dialog",
})
export default class OriginDialog extends Vue {
    @Prop({ type: Boolean }) private visible!: boolean;
    @Prop({ type: String }) private operateType!: keyof typeof operateTypeMap;
    @Prop({ type: Array, default: [] }) private addedOriginList!: OriginConf[];
    @Prop({ type: Object, default: null }) private payload!: OriginConf;
    @Prop({ type: Number }) private originType!: 1 | 2 | 3;

    private form: OriginConf = getDefaultOrigin();
    private rules: Rules = {
        // 源站？IP或域名校验
        address: [
            {
                required: true,
                validator: (rule, value, callback) => {
                    if (value) {
                        const isOringin =
                            ipRegexp.test(value as string) ||
                            domainRegexp.test(value as string) ||
                            ipRegex.v6({ exact: true }).test(value as string);
                        if (!isOringin) {
                            callback(new Error(`请输入正确的IP或域名`));
                        } else {
                            const dataSame = this.addedOriginList.filter(item => item.address === value);
                            if (dataSame.length > 0) {
                                callback(new Error(`源站录入重复`));
                            } else {
                                callback();
                            }
                        }
                    } else {
                        callback(new Error(`请输入源站`));
                    }
                },
            },
        ],
        // 源站？权重校验
        weight: [
            { required: true, message: "请输入权重", trigger: "blur" },
            {
                validator: (rule, value, callback) => {
                    if (value || value === 0) {
                        if (value < 1 || value > 100) {
                            callback(new Error(`权重输入在1-100`));
                        } else {
                            const ex = /^\d+$/;
                            if (ex.test(value as string)) {
                                callback();
                            } else {
                                callback(new Error(`权重输入只能为整数`));
                            }
                        }
                    } else {
                        callback(new Error(`请输入权重`));
                    }
                },
            },
        ],
    };

    get dialogTitle() {
        return `${operateTypeMap[this.operateType]}源站`;
    }

    /**
     * 权重禁用
     */
    get weightDisabled() {
        if (this.originType === 1 || this.originType === 3) {
            return true;
        }

        return false;
    }

    @Watch("visible")
    onVisible(val: boolean) {
        if (!val) return;
        this.form = cloneDeep(this.payload || getDefaultOrigin());
        (this.$refs.form as ElForm)?.resetFields();
    }

    addConfirm() {
        (this.$refs.form as ElForm).validate(valid => {
            if (!valid) return;
            this.$emit("handle", {
                type: this.operateType,
                payload: cloneDeep(this.form),
            });
        });
    }
}
</script>
<style lang="scss" scoped>
.el-form {
    ::v-deep {
        .el-form-item__content > .el-input,
        .el-form-item__content > .el-select {
            width: 100%;
        }
        .el-select__tags > span > span:first-child {
            width: auto;
        }
    }
    ::v-deep .el-select__tags > span > span:first-child span {
        width: auto;
    }
}
</style>
