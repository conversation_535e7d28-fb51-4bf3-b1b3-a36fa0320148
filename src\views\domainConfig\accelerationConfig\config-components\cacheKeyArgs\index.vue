<template>
    <div class="ct-edit-wrapper">
        <el-form
            label-width="140px"
            label-position="left"
            :model="form"
            :rules="rules"
            ref="cacheKeyArgsForm"
            :disabled="!isEdit || !isService || isLockCacheKeyArgs"
        >
            <div v-if="isNewEcgw">
              <el-form-item
                :label="$t('domain.detail.label12')"
                prop="cachekey_args"
                class="cache-table-style"
            >
                <div class="ct-table-wrapper">
                    <el-table :data="form.cachekey_args">
                        <el-table-column prop="mode" :label="$t('domain.type')">
                            <template slot-scope="scope">{{
                                type_list[scope.row.mode]
                            }}</template>
                        </el-table-column>
                        <el-table-column prop="content" :label="$t('domain.content')"> </el-table-column>
                        <el-table-column
                            :label="$t('domain.detail.label46')"
                            prop="ignore_params"
                            :formatter="ignoreParamsFormatter"
                            min-wiudth="60"
                        ></el-table-column>
                        <el-table-column :label="$t('domain.detail.label47')" prop="args" minWidth="110" />
                        <el-table-column :label="$t('domain.detail.label48')" prop="priority" minWidth="110" />
                        <el-table-column :label="$t('domain.operate')" width="120">
                            <template slot-scope="scope">
                                <el-button
                                    type="text"
                                    :disabled="
                                        !isEdit || !isService || isLockCacheKeyArgs
                                    "
                                    @click="
                                        handleCacheOper(
                                            scope.row,
                                            'edit',
                                            'cachekey_args',
                                            scope.$index
                                        )
                                    "
                                    >{{ $t("domain.modify") }}</el-button
                                >
                                <el-button
                                    type="text"
                                    :disabled="
                                        !isEdit || !isService || isLockCacheKeyArgs
                                    "
                                    @click="
                                        handleCacheOper(
                                            scope.row,
                                            'delete',
                                            'cachekey_args',
                                            scope.$index
                                        )
                                    "
                                    >{{ $t("domain.delete") }}</el-button
                                >
                            </template>
                        </el-table-column>
                    </el-table>
                    <div class="flex-row-style button-box">
                        <el-button
                            class="btn"
                            type="text"
                            :disabled="!isEdit || !isService || isLockCacheKeyArgs"
                            @click="handleCacheOper(null, 'create', 'cachekey_args')"
                        >
                            + {{ addButtonText }}
                        </el-button>
                    </div>
                </div>
              </el-form-item>
            </div>

        </el-form>
        <cachekey-args-dialog
          :dialogVisible="cachekeyArgsDialogVisible"
          :cachekeyArgsForm="cachekeyArgsForm"
          :from="currentType"
          :cachekey_args="form.cachekey_args"
          @cancel="cacheKeyCancel"
          @submit="cacheKeySubmit"
        />
    </div>
</template>

<script>
// import ctSvgIcon from "@/components/ctSvgIcon";
import componentMixin from "@/views/domainConfig/componentMixin";
import validFieldMixin from "@/views/domainConfig/validFieldMixin";
import i18n from "@/i18n/index";
import { cloneDeep } from "lodash-es";
import cachekeyArgsDialog from "@/views/domainConfig/accelerationConfig/components/cachekeyArgsDialog.vue";
import { lowerFirst } from '@/utils';

// 缓存配置-类型：字典翻译
const CacheModeMap = {
    0: i18n.t("domain.detail.cacheModeMap[0]"),
    1: i18n.t("domain.detail.cacheModeMap[1]"),
    2: i18n.t("domain.detail.cacheModeMap[2]"),
    3: i18n.t("domain.detail.cacheModeMap[3]"),
    4: i18n.t("domain.detail.cacheModeMap[4]"),
    // 5: "正则",
};

export default {
    name: "cacheKeyArgs",
    components: {
        // ctSvgIcon,
        cachekeyArgsDialog,
    },
    mixins: [
        componentMixin,
        validFieldMixin
    ],
    props: {
        datas: Object,
        isLockCacheKeyArgs: Boolean,
        isNewEcgw: Boolean,
    },
    data() {
        return {
            addButtonText: i18n.t("domain.editPage.label10"),
            form: {
                cachekey_args: [], // 缓存参数
            },
            // 缓存参数
            cachekeyArgsForm: {
                mode: 0,
                content: "",
                ignore_params: null,
                args: "",
                priority: 10,
            },
            currentType: "create",
            cachekeyArgsDialogVisible: false,
            currenCacheKeyIndex: "",
            cacheKeyDeleteIdList: [], // 缓存参数，点击删除时，存储该条数据的id
            cacheKeyIdList: [],
            rules: {},
        };
    },
    computed: {},
    watch: {
        "datas.cachekey_args": {
            deep: true,
            handler(val) {
                this.init(val);
            },
            immediate: true,
        },
    },
    methods: {
      init(v) {
          this.form.cachekey_args = cloneDeep(v);
      },
      async handleCacheOper(row, currentType, tabName, i) {
        this.currentType = currentType;

        this.currenCacheKeyIndex = i;
        const getTime = new Date().getTime();
        const defaultFormMap = {
            // 缓存key-缓存参数
            cachekey_args: {
                id: `cachekey_args_${getTime}`,
                mode: 0,
                content: "",
                ignore_params: null,
                args: "",
                priority: 10,
            },
        };
        if (currentType === "create") {
            this.cacheKeyIdList.push(defaultFormMap?.cachekey_args?.id);
            this.$emit("onCacheKeyIdList", this.cacheKeyIdList)

            row = defaultFormMap[tabName];
        }
        if (currentType === "delete") {
            const msgMap = {
                cachekey_args: this.$t("domain.editPage.tip20"),
            };

            // 二次确认弹窗
            await this.$confirm(msgMap[tabName], this.$t("domain.delete"), {
                type: "warning",
            });

            if (!this.cacheKeyDeleteIdList.includes(this.form.cachekey_args[i]?.id)) {
                this.cacheKeyDeleteIdList.push(this.form.cachekey_args[i]?.id);
                this.$emit("onCacheKeyDeleteIdList", this.cacheKeyDeleteIdList)
            }

            this.form[tabName].splice(i, 1);
            this.$emit("onChange", this.form.cachekey_args);
        } else {
            this.cachekeyArgsDialogVisible = true;
            const rowData = cloneDeep({
                ...defaultFormMap[tabName],
                ...row,
            });

            this.$set(this, "cachekeyArgsForm", rowData);
            if (
                !this.cacheKeyIdList.includes(this.form.cachekey_args[i]?.id) &&
                currentType === "edit"
            ) {
                this.cacheKeyIdList.push(this.form.cachekey_args[i]?.id);
                this.$emit("onCacheKeyIdList", this.cacheKeyIdList)
            }
        }
      },
      checkRepeatedContent(cacheList) {
        if (cacheList.length === 0) return "";

        const nameLsit = cacheList
            .map(item => item.content)
            .join(",")
            .split(",");
        const nameMap = {};
        for (let idx = 0; idx < nameLsit.length; idx += 1) {
            if (nameMap[nameLsit[idx]]) {
                return nameLsit[idx];
            } else {
                nameMap[nameLsit[idx]] = true;
            }
        }

        return "";
      },
      // 缓存key-缓存参数，弹窗点击确定事件
      async cacheKeySubmit() {
        // 进行重复性检查
        const { mode } = this.cachekeyArgsForm;
        const typeName = CacheModeMap[mode] || "";
        const typeList = this.form.cachekey_args
            .filter((i, index) => index !== this.currenCacheKeyIndex) // 先把指定的过滤掉
            .filter(item => item.mode === mode); // 再把不符合的类型过滤掉

        typeList.push(this.cachekeyArgsForm); // 再把当前的追进去进行全比对

        const repeatedName = this.checkRepeatedContent(typeList);
        if (repeatedName) {
            // await Promise.reject(`请避免出现重复${typeName}，请检查 ${repeatedName} ${typeName}`);
            await Promise.reject(this.$t("simpleForm.alogicCacheMixin.ValidationErrorMessages.DuplicateValidation", { repeatedName, type: lowerFirst(typeName) }));
        }

        if (this.currentType === "create") {
            this.form.cachekey_args.push({
                ...this.cachekeyArgsForm,
            });
        } else {
            this.$set(this.form.cachekey_args, this.currenCacheKeyIndex, this.cachekeyArgsForm);
        }

        // 弹窗点击确定后，需要将整个的cachekey_args值传给父组件
        this.$emit("onChange", this.form.cachekey_args);
        this.cachekeyArgsDialogVisible = false;
      },
      // 缓存参数：弹窗点击取消事件
      cacheKeyCancel(dialogKey) {
        this[dialogKey] = false;
      },
    },
};
</script>

<style lang="scss" scoped>
@import "@/views/domainConfig/commonStyle.scss";
.ct-edit-wrapper {
    ::v-deep {
        .el-form--label-left .el-form-item__label {
            text-align: right;
        }
    }
}
</style>
