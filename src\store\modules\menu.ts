// export * from "@cdnplus/common/store/modules/menu";

import { VuexModule, Module, Action, Mutation, getModule } from "vuex-module-decorators";

import store from "../index";
import { ctFetch, errorHandler } from "../../utils";
import { MenuUrl, CtiamMenuUrl, CtiamMenuAuthUrl, CtiamResourceCheckUrl } from "../../config/url";
import { MicroAppModule } from "@/store/modules/microApp";

import { MenuState, MenuListItem } from "../types";
import { nUserModule } from "@/store/modules/nuser";
import i18n from "@/i18n";
import { StatisticsModule } from "./statistics";

import { ProductModule } from "@/store/modules/ncdn/nproduct"; // cdn独有
import { ProductCodeEnum } from "@/config/map";
import { CODE_CTIAM_1601, CODE_CTIAM_1602 } from "@/utils/ctFetch/errorConfig";
// 属于全站加速的产品
const wholeList = [ProductCodeEnum.Whole, ProductCodeEnum.Upload, ProductCodeEnum.Socket] as const;

type CtiamAuthItem = {
    name: string;
    code: string;
    url?: string;
    domains?: string;
};

// 两个系统的 menu key 不同（默认用 items）
let MENU_KEY: "items" | "list" = "items";
function processData(menu: Array<MenuListItem> = []) {
    // 只要 online的数据
    menu = menu
        .filter(m => {
            // 处理二级
            if (m[MENU_KEY]) {
                m[MENU_KEY] = (m[MENU_KEY] as MenuListItem[])
                    .filter(n => n.state === "online" && n.enable === "true")
                    .map(m => ({
                        ...m,
                        items: m[MENU_KEY], // 修改字段名，配合全局使用
                    }));
            }
            return m.state === "online" && m.enable === "true";
        })
        .map(m => ({
            ...m,
            items: m[MENU_KEY], // 修改字段名
        }));

    // 存放路径 => 编号
    const pathMap: Map<string, string> = new Map();

    // 增加编号identifier
    menu.forEach((m, index) => {
        // 存在不为空的二级菜单
        if (m.items && m.items.length > 0) {
            m.items.forEach((n, idx: number) => {
                // 去掉链接中 # 之前的内容
                const nhref = (n.hrefLocal && n.hrefLocal.replace(/^.*#/, "")) || n.id;
                n.identifier = `${index}-${idx}`;
                pathMap.set(nhref, n.identifier);
            });
        }
        // 去掉链接中 # 之前的内容
        // 一级菜单可能不需要绑定路由，只做展示用，此时不会配置 hrefLocal
        const href = (m.hrefLocal && m.hrefLocal.replace(/^.*#/, "")) || m.id;
        m.identifier = `${index}`;
        pathMap.set(href, m.identifier);
    });
    return { menuList: menu, pathMap };
}

let menu: MenuListItem[] = [];
@Module({ dynamic: true, store, name: "menu" })
class Menu extends VuexModule implements MenuState {
    public filterArr: string[] = [];
    public menuList: MenuListItem[] = [];
    public pathMap = new Map();
    public permList: string[] = []; // 权限列表，即菜单 ucode ，当开启严格模式时，必须使用 route.meta.perm 匹配成功
    public permStrict = false; // 权限模式
    public ctiamButtonAuth: {
        requestTime: number;
        authList: CtiamAuthItem[];
    } = {
        requestTime: 0,
        authList: [],
    };

    @Mutation
    SET_CTIAM_BUTTON_AUTH(auth: { requestTime: number; authList: CtiamAuthItem[] }) {
        this.ctiamButtonAuth = auth;
    }

    @Mutation
    SET_PERM_STRICT(strict: boolean) {
        this.permStrict = strict;
    }

    @Mutation
    SET_PERM_LIST() {
        const permList: string[] = [];
        const list = JSON.parse(JSON.stringify(menu)) as MenuListItem[];
        // 是否有全站产品
        const hasWhole = ProductModule.productOptions.some(item => wholeList.includes(item.value as any));

        list.filter(menu => menu.state === "online" && menu.enable === "true").forEach(menu => {
            if (menu[MENU_KEY]) {
                (menu[MENU_KEY] as MenuListItem[])
                    .filter(n => {
                        if (n.ucode === "statistics.dcdn") {
                            // 全站菜单权限要增加是否有该全站产品的判断
                            return hasWhole && n.state === "online" && n.enable === "true";
                        }
                        return n.state === "online" && n.enable === "true";
                    })
                    .forEach(n => {
                        permList.push(n.menuCode || n.ucode);
                    });
            }

            permList.push(menu.menuCode || menu.ucode);
        });
        // 有涉及到路由鉴权的，需要设置菜单 ucode ，默认值 default 需要剔除
        this.permList = permList.filter(ucode => ucode !== "default");
    }

    @Mutation
    SET_MENU_LIST() {
        let list = JSON.parse(JSON.stringify(menu)) as MenuListItem[];

        // 按需过滤菜单
        list = list.filter(menu => {
            if (menu[MENU_KEY]) {
                menu[MENU_KEY] = (menu[MENU_KEY] as MenuListItem[]).filter(item => {
                    if (item.menuCode) {
                        return !this.filterArr.includes(item.menuCode);
                    } else {
                        return !this.filterArr.includes(item.ucode);
                    }
                });
            }

            if (menu.menuCode) {
                return !this.filterArr.includes(menu.menuCode);
            } else {
                return !this.filterArr.includes(menu.ucode);
            }
        });

        const { menuList, pathMap } = processData(list);
        if (nUserModule.isCtclouds) {
            // 国际站不展示“API文档”菜单
            this.menuList = menuList.filter(item => item.name !== "API文档");
        } else {
            this.menuList = menuList;
        }

        this.pathMap = pathMap;
    }

    @Mutation
    ADD_FILTER_ARR(filter: string) {
        if (!this.filterArr.includes(filter)) {
            this.filterArr.push(filter);
        }
    }

    @Mutation
    DEL_FILTER_ARR(filter: string) {
        const idx = this.filterArr.indexOf(filter);
        if (idx !== -1) {
            this.filterArr.splice(idx, 1);
        }
    }

    @Action
    public async GetMenuList(options: string | { domain: string; workspaceId: string }) {
        let domain, workspaceId;
        if (typeof options === "string") {
            domain = options;
        } else {
            domain = options.domain;
            workspaceId = options.workspaceId;
        }

        // 组装参数
        const { query } = store.state.route;
        const params = {
            workspaceId: workspaceId || (query.workspaceId as string),
            // 中文的菜单 domain 不需要修改，其他语言则默认追加 .lang 的后缀
            domain,
        };

        // 是否需要调用
        if (!params.workspaceId) {
            menu = [];
            this.SET_MENU_LIST();
            this.SET_PERM_LIST();
            return this.menuList;
        }

        const ctiamParams = {
            ...params,
            depth: 5,
        };

        const url = nUserModule.isFcdnCtyunCtclouds ? CtiamMenuUrl : MenuUrl;
        const data = nUserModule.isFcdnCtyunCtclouds ? ctiamParams : params;
        const rst = await ctFetch<{ items?: MenuListItem[]; list?: MenuListItem[] }>(url, {
            data: data,
            cache: true,
        });

        // MENU_KEY 二次修正
        if (rst.items) MENU_KEY = "items";
        if (rst.list) MENU_KEY = "list";

        menu = (rst[MENU_KEY] || []) as MenuListItem[];

        // 获取边缘函数菜单配置信息
        const promises = [MicroAppModule.checkeEdgeFunctionAccount()];
        !StatisticsModule.loadOptEnable && promises.push(MicroAppModule.checkeeEdgeFunctionStatus());
        await Promise.all(promises);

        if (!MicroAppModule.showEdgeFunctionMenu) {
            menu = menu.filter(item => item.ucode !== "udf.func");
        }
        this.SET_MENU_LIST();
        this.SET_PERM_LIST();
        return menu;
    }

    @Action
    public async GetCtiamAuthList() {
        if (
            this.ctiamButtonAuth.requestTime &&
            this.ctiamButtonAuth.requestTime + StatisticsModule.cacheTtl * 1000 > Date.now()
        ) {
            return;
        }

        const data = await ctFetch<CtiamAuthItem[]>(CtiamMenuAuthUrl);

        this.SET_CTIAM_BUTTON_AUTH({ requestTime: +new Date(), authList: data });
    }
}

export const MenuModule = getModule(Menu);

/**
 * 抛出按钮权限定制错误
 */
const throwButtonAuthError = (authItem: CtiamAuthItem, _options = {}) => {
    const options = Object.assign(
        {
            urlProcessor: (url: string) => url,
            codeProcessor: (code: string) => code,
        },
        _options
    );

    const url = options.urlProcessor(authItem.url || location.hash.replace(/^#/, ""));
    const data = {
        code: options.codeProcessor(CODE_CTIAM_1601),
        action: authItem.code,
        resource: i18n.t("common.ctiam.resource"),
        reason: i18n.t("common.ctiam.auth"),
        // des: "全站加速用量-用量分析-带宽流量",
        url,
        des: authItem.name,
        domains: authItem.domains || "",
    };

    return {
        code: options.codeProcessor(CODE_CTIAM_1601),
        reason: i18n.t("common.ctiam.auth"),
        url,
        data: {
            ...data,
            data: { ...data },
        },
    };
};

/**
 * 判断该用户是否有当前权限码的权限, 在authList中的都是没有权限的
 */
export const checkCtiamButtonAuth = async (
    auth: string,
    resource = "",
    options = {
        urlProcessor: (url: string) => url,
    }
) => {
    if (!nUserModule.isFcdnCtyunCtclouds) return true;
    await MenuModule.GetCtiamAuthList();
    const rst = MenuModule.ctiamButtonAuth.authList.find(itm => itm.code === auth);
    if (rst) {
        return Promise.reject(throwButtonAuthError(rst, options));
    }

    // 如果涉及资源权限需要再次调用接口校验
    if (resource) {
        const { resource: authRst, name } = await ctFetch<{
            resource: Record<string, boolean>;
            name: string;
        }>(CtiamResourceCheckUrl, {
            data: {
                action: auth,
                resource,
            },
        });
        if (Object.values(authRst || {}).some(v => !v)) {
            return Promise.reject(
                throwButtonAuthError(
                    {
                        name,
                        code: auth,
                        domains: Object.keys(authRst || {})
                            .filter(v => !authRst[v])
                            .join(","),
                    },
                    {
                        codeProcessor: () => CODE_CTIAM_1602,
                    }
                )
            );
        }
    }

    return true;
};
