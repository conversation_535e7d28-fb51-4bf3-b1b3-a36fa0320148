/*
 * @Description: 产品相关配置
 * @Author: wang yuegong
 */
// 产品 code 枚举
export const ProductCodeEnum = {
    Basic: "000", // 通用产品
    Static: "001", // 静态加速
    Dynamic: "002", // 动态加速
    Download: "003", // 下载加速
    DownloadIdlesse: "014", // 下载加速（闲时）
    Demand: "004", // 点播加速
    Live: "005", // 直播加速
    Whole: "006", // 全站加速
    Security: "007", // 安全加速
    CDN: "008", // CDN加速
    Upload: "104", //上传加速
    Socket: "105", //websocket加速
} as const;

// 加速类型
export const ProductType = {
    BSS: "bss_product", // 按需付费
    FLOW: "flow_packet", // 流量包
} as const;

// 产品可用状态
export const ProductCanUseStatus = {
    BSS: 1, // 按需产品状态1：服务中
    FLOW: 2, // 流量包产品状态2：使用中
} as const;

// 产品相关
export const ProductCodeMap = {
    "001": "静态加速",
    "002": "动态加速",
    "003": "下载加速",
    "004": "视频点播加速",
    "005": "视频直播",
    "006": "全站加速",
    "007": "安全加速",
    "008": "CDN加速",
    "104": "全站加速-上传加速",
    "105": "全站加速-websocket加速",
    "014": "下载加速(闲时)",
    "000": "未知",
    "190": "全站加速-虚拟专线-全球（不含中国内地）",
} as const;

// 流量产品的状态
export const ProductStatusMap = {
    1: "服务中",
    2: "已过期",
    3: "已退订",
    4: "已冻结（暂停）",
    5: "已删除",
    6: "已销户",
    7: "冻结中",
    8: "恢复中",
    9: "删除中（销户中）",
    10: "其他",
} as const;

// 流量包状态
export const PackageStatusMap = {
    "1": "开通未使用",
    "2": "使用中",
    "3": "流量用完",
    "4": "到期",
    "5": "关停",
    "6": "退订",
    "7": "冻结",
} as const;

// scc提供的加速类型 原始数据 ，名称和编码映射
export const ProductCodeNameMap = new Map([
    ["001", "静态加速(中国内地)"],
    ["124", "静态加速(全球不含中国内地)"],
    ["002", "动态加速"],
    ["003", "下载加速(中国内地)"],
    ["125", "下载加速(全球不含中国内地)"],
    ["004", "视频点播加速(中国内地)"],
    ["126", "视频点播加速(全球不含中国内地)"],
    ["005", "视频直播(中国内地)"],
    ["127", "视频直播(全球不含中国内地)"],
    ["006", "全站加速(中国内地)"],
    ["123", "全站加速(全球不含中国内地)"],
    ["007", "安全加速"],
    ["008", "CDN加速(中国内地)"],
    ["120", "CDN加速(全球不含中国内地)"],
    ["104", "全站加速-上传加速222"],
    ["105", "全站加速-websocket加速222"],
    ["014", "下载加速(闲时)(中国内地)"],
    ["128", "下载加速(闲时)(全球不含中国内地)"],
    ["000", "未知"],
]);

// 注：页面上的加速类型筛选框在进行展示时，中国内地和全球不含中国内地要统一展示
// 例如，CDN加速(中国内地)和CDN加速(全球不含中国内地)展示为一个加速类型：CDN加速
// 所以需要做一个加速类型名称和两种加速区域编码的映射，例如：[["008", "120"], "CDN加速"]，用以数据的转换
export const ShowProductMap = new Map([
    [["001", "124"], "common.productName[0]"],
    [["002"], "common.productName[1]"],
    [["003", "125"], "common.productName[2]"],
    [["004", "126"], "common.productName[3]"],
    [["005", "127"], "common.productName[4]"],
    [["006", "123"], "common.productName[5]"],
    [["007"], "common.productName[6]"],
    [["008", "120"], "common.productName[7]"],
    [["104"], "common.productName[8]"],
    [["105"], "common.productName[9]"],
    [["014", "128"], "common.productName[10]"],
    [["000"], "common.productName[11]"],
]);

// 加速类型编码和两种不同加速区域编码的映射，用以数据的转换
export const ProductItemValueMap = new Map([
    [["001", "124"], "001"],
    [["002"], "002"],
    [["003", "125"], "003"],
    [["004", "126"], "004"],
    [["005", "127"], "005"],
    [["006", "123"], "006"],
    [["007"], "007"],
    [["008", "120"], "008"],
    [["104"], "104"],
    [["105"], "105"],
    [["014", "128"], "014"],
    [["000"], "000"],
]);
