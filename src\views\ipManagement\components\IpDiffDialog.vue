<template>
    <el-dialog
        :title="$t('ipManagement.sendList.label3')"
        :close-on-click-modal="false"
        :modal-append-to-body="false"
        :visible.sync="diffVisible"
        :before-close="cancel"
        class="detail-dialog"
        append-to-body
    >
        <div class="detail-list" v-if="selectedAfterSort.length === 2">
            <div>
                <p class="diff-title">
                    <span>{{ $t("ipManagement.sendList.label2") }}：</span>
                    <span> {{ (selectedAfterSort[0].create_time * 1000) | timeFormat }} </span>
                </p>
                <p>
                    {{ selectedAfterSort[0].ip_infos }}
                </p>
            </div>
            <div class="diff-result-right">
                <p class="diff-title">
                    <span>{{ $t("ipManagement.sendList.label2") }}：</span>
                    <span> {{ (selectedAfterSort[1].create_time * 1000) | timeFormat }} </span>
                </p>
                <div class="diff-result">
                    <div class="diff-detail-title">{{ $t("ipManagement.sendList.add") }}：</div>
                    <div :class="{ 'diff-add': diffResult.addedIPs.length > 0 }">
                        {{ diffResult.addedIPs.toString() || $t("ipManagement.sendList.none") }}
                    </div>
                    <div class="diff-detail-title">{{ $t("ipManagement.sendList.unchange") }}：</div>
                    <div class="diff-unchange">
                        {{ diffResult.unchangedIPs.toString() || $t("ipManagement.sendList.none") }}
                    </div>
                    <div class="diff-detail-title">{{ $t("ipManagement.sendList.delete") }}：</div>
                    <div :class="{ 'diff-remove': diffResult.removedIPs.length > 0 }">
                        {{ diffResult.removedIPs.toString() || $t("ipManagement.sendList.none") }}
                    </div>
                </div>
            </div>
        </div>
    </el-dialog>
</template>

<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";
import { SendData } from "@/types/ipManagement";

@Component({})
export default class IpDiffDialog extends Vue {
    // 弹框显示标志位
    @Prop({ default: true, type: Boolean }) private diffVisible!: boolean;
    @Prop({ default: [], type: Array }) private tableSelected!: SendData[];
    get selectedAfterSort() {
        return this.tableSelected.sort((a, b) => a.create_time - b.create_time);
    }
    get diffResult() {
        if (this.selectedAfterSort.length !== 2) {
            return {
                addedIPs: [],
                removedIPs: [],
                unchangedIPs: [],
            };
        }
        return this.compareIPInfos(this.selectedAfterSort[0], this.selectedAfterSort[1]);
    }
    private compareIPInfos(configA: SendData, configB: SendData) {
        // 将ip_infos字符串转换为数组
        const ipArrayA = configA?.ip_infos.split(",").map((ip: string) => ip.trim());
        const ipArrayB = configB?.ip_infos.split(",").map((ip: string) => ip.trim());

        // 调用diffIPs函数比较IP地址
        return this.diffIPs(ipArrayA, ipArrayB);
    }
    /**
     * 比较两个IP地址数组，返回新增和被删除的IP列表。
     *
     * @param a - 初始IP地址数组。
     * @param b - 对比IP地址数组。
     * @returns 返回一个对象，包含新增和被删除的IP数组。
     */
    private diffIPs(
        a: string[],
        b: string[]
    ): {
        addedIPs: string[];
        removedIPs: string[];
        unchangedIPs: string[];
    } {
        // 使用 Set 来优化查找效率
        const setA = new Set(a);
        const setB = new Set(b);

        // 找出新增的IP地址，即存在于b中但不在a中的IP
        const addedIPs = b.filter(ip => !setA.has(ip));

        // 找出被删除的IP地址，即存在于a中但不在b中的IP
        const removedIPs = a.filter(ip => !setB.has(ip));

        // 找出不变的IP地址，即同时存在于a和b中的IP
        const unchangedIPs = a.filter(ip => setB.has(ip));

        // 返回包含新增、删除和不变IP的数组的对象
        return { addedIPs, removedIPs, unchangedIPs };
    }
    private cancel() {
        this.$emit("cancel");
    }
}
</script>

<style scoped lang="scss">
.detail-dialog {
    ::v-deep .el-dialog {
        @include g-width(90%, 50%, 50%);
    }

    .detail-list {
        display: flex;
        & > div {
            padding: 20px;
            border: 1px solid $border-color;
            width: 50%;
        }
        .diff-title {
            margin-bottom: 10px;
            font-size: 14px;
        }
        .diff-result-right {
            margin-left: 20px;
        }
        .diff-detail-title {
            font-weight: bold;
        }
        .diff-add,
        .diff-unchange,
        .diff-remove {
            margin-bottom: 5px;
        }
        .diff-add {
            color: $color-danger;
        }
        .diff-remove {
            color: $green-base;
            text-decoration: line-through;
        }
    }
}
</style>
