$label-width: 140;
$label-line-height: 36;
$label-font-size: 14;
$content-font-size: 14;
$tooltips-font-size: 12;
$label-icon-font-size: 18;
$wrapper-width: 360;
$active-color: $color-master-active;
$danger: $color-danger;

@mixin fix-element-ui {
    ::v-deep {
        // .el-input__inner {
        //     &:focus {
        //         border-color: $active-color;
        //     }
        // }

        // .el-select {
        //     // width: #{$wrapper-width}px;
        //     .el-input__inner {
        //         &:focus {
        //             border-color: $active-color;
        //         }
        //     }
        // }

        // .el-cascader {
        //   width: #{$wrapper-width}px;
        // }

        .el-date-editor.el-input,
        .el-date-editor.el-input__inner {
            width: 100%;
        }

        // .el-radio-button {
        //     .el-radio-button__inner {
        //         &:hover {
        //             color: $active-color;
        //         }
        //     }

        //     .el-radio-button__orig-radio:checked + .el-radio-button__inner {
        //         &:hover {
        //             color: #fff;
        //         }
        //     }
        // }

        // .el-input-group__append {
        //     color: $active-color;
        // }

        // .el-checkbox__input.is-checked .el-checkbox__inner,
        // .el-checkbox__input.is-indeterminate .el-checkbox__inner {
        //     background-color: $active-color;
        //     border-color: $active-color;
        // }

        // .el-checkbox__input.is-checked + .el-checkbox__label {
        //     color: $active-color;
        // }

        // .el-checkbox__inner:hover {
        //     border-color: $active-color;
        // }

        // .el-checkbox__inner:focus {
        //     border-color: $active-color;
        // }

        // .el-checkbox__input.is-focus .el-checkbox__inner {
        //     border-color: $active-color;
        // }

        // .el-select__tags-text {
        //     color: $active-color;
        // }

        // .el-slider__button {
        //     border-color: $active-color;
        // }

        // .tooltips {
        //     font-size: #{$tooltips-font-size}px;
        // }
    }
}
