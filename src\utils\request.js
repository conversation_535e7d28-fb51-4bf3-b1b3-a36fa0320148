import ctFetch from "./https";

export default function $https({ url, method = "POST", params = {}, config = {} }) {
    // const config = {
    //     method: method.toUpperCase(),
    // };
    config.method = method.toUpperCase();
    // 默认post请求
    if (method.toUpperCase() === "GET") {
        config.data = params;
    } else {
        config.body = params;
    }
    return new Promise((resolve, reject) => {
        ctFetch(url, config)
            .then(res => {
                resolve(res);
            })
            .catch(err => {
                reject(err);
            });
    });
}
