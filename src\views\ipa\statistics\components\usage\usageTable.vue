<template>
    <el-table :data="fetchData" v-loading="loading" :element-loading-text="$t('statistics.common.table.loading')">
        <!-- ***日期参数YAPI参数为date，实际接口参数为day -->
        <el-table-column :label="$t('statistics.dcdn.bandwidthFlowWhole.tableColumn1')"
            :prop="type !== 'connection' ? 'date' : 'day'" align="center" />

        <el-table-column :label="$t('statistics.eas.tip26')" prop="flow" v-if="type !== 'connection'" align="center">
            <template slot-scope="{ row }">
                <!-- ***B->GB -->
                {{ convertFlowB2P(row.flow, scale).result }}
            </template>
        </el-table-column>
        <el-table-column :label="$t('statistics.eas.tip24')" prop="connections" v-else align="center" />

        <el-table-column :label="$t('statistics.rank.domainRank.tableColumn5')" prop="peek_bandwidth"
            v-if="type !== 'connection'" align="center">
            <template slot-scope="{ row }">
                <!-- ***bps->Mbps -->
                {{ convertBandwidthB2P(row.peek_bandwidth, scale).result }}
            </template>
        </el-table-column>
        <el-table-column :label="$t('statistics.eas.tip25')" prop="peek_qps" v-else align="center" />

        <el-table-column :label="$t('statistics.usageQuery.BandwidthFlow.tableColumn4')" prop="timestamp"
            align="center">
            <template slot-scope="{ row }">
                <!-- ***s->ms -->
                {{ (row.timestamp * 1000) | timeFormat }}
            </template>
        </el-table-column>
    </el-table>
</template>
<script>
/* eslint-disable @typescript-eslint/camelcase */
import { statistics as StatisticsUrl } from "@/config/url/ipa/statistics";
import { ScaleModule } from "@/store/modules/scale";
import { convertFlowB2P, convertBandwidthB2P } from "@/utils";
import { timeFormat } from "@/filters/index";
import { downloadCsv } from "@/utils";

export default {
    data() {
        return {
            loading: false,
            type: "",
            fetchData: [],
            timeRange: [],
        };
    },
    filters: {
        timeFormat(timestamp) {
            return timeFormat(timestamp);
        },
    },
    computed: {
        // ***单位换算：获取进制基数
        scale() {
            return ScaleModule.scale;
        },
        MB() {
            return this.scale === 1024 ? "MiB" : "MB";
        },
        Mbps() {
            return this.scale === 1024 ? "Mibps" : "Mbps";
        }
    },
    methods: {
        convertFlowB2P,
        convertBandwidthB2P,
        async initTable(type, params, compareShow, timeArr) {
            this.type = type;

            this.loading = true;
            if (compareShow && timeArr && timeArr.length) {
                let res = [];
                const timeRange = [...timeArr[0], ...timeArr[1]].map(itm => +itm).sort((a, b) => a - b);
                this.timeRange = [timeRange[0], timeRange[timeRange.length - 1]];

                for (const item of timeArr) {
                    const startTime = this.$dayjs(item[0]).unix();
                    const endTime = this.$dayjs(item[1]).unix();
                    const obj = {
                        start_time: startTime,
                        end_time: endTime,
                    };
                    const query = Object.assign({}, params, obj);
                    const { result } = await this.reqTableData(type, query);
                    res = res.concat(result);
                }
                res?.sort((a, b) => a?.timestamp - b?.timestamp);
                for (let i = 1; i < res?.length; i++) {
                    if (res[i].date === res[i - 1].date) {
                        res.splice(i, 1);
                    }
                }
                this.fetchData = res;
                return;
            }

            const { result } = await this.reqTableData(type, params);
            this.timeRange = [params.start_time, params.end_time];
            this.fetchData = result;
            this.loading = false;
        },
        reqTableData(type, params) {
            const url =
                type === "connection" ? StatisticsUrl.statisticsconnectionsDay : StatisticsUrl.statisticsDay;
            return this.$ctFetch(url, {
                method: "POST",
                transferType: "json",
                data: params ? params : { account_id: this.$store.state.user.userInfo.userId },
            });
        },
        // 通用下载方法，如果需要定制则重写
        downloadExcel({ name, str }) {
            // 说明：由于下载时需要用到请求参数 searchParams 中的数据，所以需要在 getData 中主动缓存使用
            const t1 = timeFormat(+this.timeRange[0])
                .replace(/-|\s|:/g, "")
                .slice(0, 12);
            const t2 = timeFormat(+this.timeRange[1])
                .replace(/-|\s|:/g, "")
                .slice(0, 12);

            downloadCsv(`${name}${t1}-${t2}`, str);
        },
        downloadTable(label) {
            if (!Array.isArray(this.fetchData) || !this.fetchData.length) {
                this.$message(`${this.$t("statistics.common.chart.errMsg[2]")}`);
                return true;
            }

            let str = `${this.$t('statistics.dcdn.bandwidthFlowWhole.tableColumn1')},`

            if (this.type === 'connection') str += `${this.$t('statistics.eas.tip24')},${this.$t('statistics.eas.tip25')}`
            else str += `${this.$t('statistics.eas.tip26')}(${this.MB}),${this.$t('statistics.rank.domainRank.tableColumn5')}(${this.Mbps})`

            str += `,${this.$t('statistics.usageQuery.BandwidthFlow.tableColumn4')}\n`
            const scale = Math.pow(this.scale, 2);

            this.fetchData?.forEach(itm => {
                if (this.type === "connection") {
                    str += `${itm.day},${itm.connections},${itm.peek_qps}`
                } else {
                    str += `${itm.date},${(itm.flow / scale).toFixed(2)},${(itm.peek_bandwidth / scale).toFixed(2)}`
                }
                str += `,${timeFormat(itm.timestamp)}\n`
            });

            this.downloadExcel({ name: `${label}-${this.$t("statistics.eas.tip13")}`, str })
        }
    },
};
</script>
