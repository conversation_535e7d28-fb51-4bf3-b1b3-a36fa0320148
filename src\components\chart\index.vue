<!-- chart组件采用异步加载的形式，先生成图表，后获取数据后由api setOption 进行渲染 -->
<template>
    <div v-loading="loading" class="e-chart">
        <div v-show="showChart" class="wrapper-main" :id="id" />
        <div v-if="!showChart && noDataShow" class="wrapper-main no-data">
            <slot name="no-data">
                <ct-empty :description="emptyText" style="width: 100%;height: 100%;" />
            </slot>
            <!--            <svg-icon :icon-class="noDataIcon" class-name="no-data-icon"/>-->
            <!--            {{ noDataText }}-->
        </div>
    </div>
</template>

<script>
// import SvgIcon from "@/components/SvgIcon";
import ctEmpty from "@/components/ctEmpty";
import { debounce } from "@/utils";

export default {
    components: {
        ctEmpty,
        // SvgIcon,
    },
    props: {
        loading: {
            type: Boolean,
            required: true,
            default: true,
        },
        data: {
            type: [Boolean, Object, String, Number],
            required: false,
            default: false,
        },
        option: {
            type: [Object, Function],
            required: true,
            default: null,
        },
        id: {
            type: String,
            required: true,
            default: "",
        },
        noDataShow: {
            type: Boolean,
            required: false,
            default: true,
        },
        noDataText: {
            type: String,
            required: false,
            default: "暂无数据",
        },
        noDataIcon: {
            type: String,
            required: false,
            default: "nulldata",
        },
        emptyText: {
            type: String,
            default: "暂无数据",
        },
    },
    data() {
        return {
            chart: null,
        };
    },
    computed: {
        /**
         * 展示图表
         */
        showChart() {
            if (!this.loading && !this.data) {
                return false;
            }

            return true;
        },
    },
    mounted() {
        this.init();
    },
    beforeDestroy() {
        window.removeEventListener("resize", this.handleResize);
    },
    methods: {
        /**
         * 初始化
         */
        init() {
            if (!this.option) {
                return;
            }

            let option = null;
            // 判断是对象，则直接使用
            if (Object.prototype.toString.call(this.option) === "[object Object]") {
                option = this.option;
            }

            if (typeof this.option === "function") {
                option = this.option();
            }

            const dom = document.getElementById(this.id);
            if (!dom) {
                return;
            }

            this.chart = this.$echarts.init(dom);
            this.chart.setOption(option);
            this.$emit("ready", this.chart);
            window.addEventListener("resize", this.handleResize);

            // 设置一个宏任务，保证在元素显示时不会错位
            setTimeout(() => {
                this.chart.resize();
            }, 0);
        },
        /**
         * 处理图表复位
         */
        handleResize() {
            // 防抖动
            debounce(
                () => {
                    if (this.chart) {
                        this.chart.resize();
                    }
                },
                { delay: 100, context: this }
            )();
        },
    },
};
</script>

<style scoped lang="scss">
@import "./index.scss";
</style>
