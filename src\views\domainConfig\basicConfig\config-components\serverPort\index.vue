<template>
    <div class="bucket-origin-wrapper">
        <el-form
            label-width="140px"
            label-position="left"
            :model="form"
            :rules="rules"
            ref="serverPortForm"
            :disabled="!isEdit || !isService"
        >
            <!-- 服务端口启用：specialPortEnable 和 specialPortEnableBasic 都为true才可以启用 -->
            <div v-if="specialPortEnable && specialPortEnableBasic && area_scope === 1" class="server_port_wrapper">
                <el-form-item :label="$t('domain.editPage.label26')" class="is-required"> </el-form-item>

                    <el-form-item
                        v-if="requestProtocolIncludesHttp"
                        label="HTTP"
                        prop="http_server_port"
                        :rules="rules.http_server_port"
                        key="http_server_port"
                        class="server_http_port_wrapper"
                    >
                        <el-tooltip effect="dark" placement="top" :content="$t('domain.detail.placeholder81')">
                            <el-input
                                v-model="form.http_server_port"
                                class="port-input"
                                @change="handleChange"
                                :placeholder="$t('domain.detail.placeholder81')"
                            ></el-input>
                        </el-tooltip>
                    </el-form-item>


                    <el-form-item
                        v-if="requestProtocolIncludesHttps"
                        label="HTTPS"
                        prop="https_server_port"
                        :rules="rules.https_server_port"
                        key="https_server_port"
                        class="server_https_port_wrapper"
                        :class="[requestProtocolIncludesHttp && 'server_https_port_wrapper2']"
                    >
                        <el-tooltip effect="dark" placement="top" :content="$t('domain.detail.placeholder81')" :disabled="isLockRequestProtocol">
                            <lock-tip :lock="isLockRequestProtocol"> 
                                <el-input
                                    v-model="form.https_server_port"
                                    class="port-input"
                                    :disabled="isLockRequestProtocol"
                                    @change="handleChange"
                                    :placeholder="$t('domain.detail.placeholder81')"
                                ></el-input>
                            </lock-tip>
                        </el-tooltip>
                    </el-form-item>

                    <el-form-item label-width="320px" v-if="isServerPortChange">
                        <span slot="label">
                            <span>
                                <ct-svg-icon
                                    icon-class="info-circle"
                                    class-name="icon-column-label1"
                                />
                            </span>
                            {{ $t("domain.detail.placeholder82") }}
                        </span>
                    </el-form-item>
            </div>
            <!-- 服务端口没有启用：页面不可编辑，不需要校验，并且要有提示：特殊端口请提交工单或联系专属技术支持处理。 -->
            <div v-if="!(specialPortEnable && specialPortEnableBasic && area_scope === 1)" class="server_port_wrapper">
                <el-form-item :label="$t('domain.editPage.label26')" class="is-required"> </el-form-item>
                        <el-form-item
                            v-if="requestProtocolIncludesHttp"
                            label="HTTP"
                            class="server_http_port_wrapper"
                        >
                            <el-tooltip effect="dark" placement="top">
                                <div slot="content">
                                    <i18n v-if="isPoweredByQiankun" path="domain.detail.placeholder44">
                                        <a @click="$docHelp(versionCompareUrl)" class="aocdn-ignore-anchor">{{ $t("domain.detail.placeholder44-1") }}</a>
                                        <a :href="serviceTicketUrl" target="_blank" class="aocdn-ignore-anchor">{{ $t("domain.editPage.specialPortTip2.tip2") }}</a>
                                    </i18n>
                                    <span v-else>{{ $t("domain.detail.placeholder44-0") }}</span>
                                </div>
                                <el-input v-model="form.http_server_port" disabled class="port-input"></el-input>
                            </el-tooltip>

                        </el-form-item>

                        <el-form-item
                            v-if="requestProtocolIncludesHttps"
                            label="HTTPS"
                            class="server_https_port_wrapper"
                            :class="[requestProtocolIncludesHttp && 'server_https_port_wrapper2']"
                        >
                            <el-tooltip effect="dark" placement="top" :disabled="isLockRequestProtocol">
                                <div slot="content">
                                    <i18n v-if="isPoweredByQiankun" path="domain.detail.placeholder44">
                                        <a @click="$docHelp(versionCompareUrl)" class="aocdn-ignore-anchor">{{ $t("domain.detail.placeholder44-1") }}</a>
                                        <a :href="serviceTicketUrl" target="_blank" class="aocdn-ignore-anchor">{{ $t("domain.editPage.specialPortTip2.tip2") }}</a>
                                    </i18n>
                                    <span v-else>{{ $t("domain.detail.placeholder44-0") }}</span>
                                </div>
                                <lock-tip :lock="isLockRequestProtocol"> 
                                    <el-input v-model="form.https_server_port" disabled class="port-input"></el-input>
                                </lock-tip>
                            </el-tooltip>
                        </el-form-item>
            </div>
        </el-form>
    </div>
</template>

<script>
import validFieldMixin from "@/views/domainConfig/validFieldMixin";
import componentMixin from "@/views/domainConfig/componentMixin";
import { cloneDeep } from "lodash-es";
import { StatisticsModule } from "@/store/modules/statistics";
import { SecurityAbilityModule } from "@/store/modules/securityAbility";
import ctSvgIcon from "@/components/ctSvgIcon";
import { portRegex } from '@/utils/validator.utils';
import lockTip from "../../../components/lockTIp.vue";
import { commonLinks } from '@/utils/logic/url';

export default {
    name: "serverPort",
    components: {
        ctSvgIcon,
        lockTip
    },
    mixins: [validFieldMixin, componentMixin],
    props: {
        datas: Object,
        requestProtocolIncludesHttp: Boolean,
        requestProtocolIncludesHttps: Boolean,
        isServerPortChange: Boolean,
        isLockRequestProtocol: Boolean,
    },
    data() {
        return {
            form: {
                http_server_port: "",
                https_server_port: "",
            },
            area_scope: null,
            rules: {
                http_server_port: [
                    { required: true, validator: this.check_http_server_port, trigger: "blur" },
                ],
                https_server_port: [
                    { required: true, validator: this.check_https_origin_port, trigger: "blur" },
                ],
            },
        };
    },
    computed: {
        versionCompareUrl() {
            return this.$urlTransformer({
                a1Ctyun: "https://www.ctyun.cn/document/10065985/10197238",
                a1Ctcloud: "https://www.esurfingcloud.com/document/zh-cn/20689737/20689747"
            });
        },
        serviceTicketUrl() {
            return commonLinks.orderLink;
        },
        specialPortEnable() {
            return StatisticsModule.specialPortEnable;
        },
        specialPortEnableBasic() {
            return SecurityAbilityModule.specialPortEnable;
        },
        isPoweredByQiankun() {
            return window.__POWERED_BY_QIANKUN__;
        },
    },
    watch: {
        datas: {
            deep: true,
            handler(val) {
                this.init(cloneDeep(val));
            },
            immediate: true,
        },
    },
    methods: {
        init(v) {
            this.form.http_server_port = v.http_server_port;
            this.form.https_server_port = v.https_server_port;
            this.area_scope = v?.area_scope;
        },
        handleChange() {
            this.$refs.serverPortForm.validateField("http_server_port");
            this.$refs.serverPortForm.validateField("https_server_port");

            this.$emit("onChange", this.form);
        },
        // 特殊端口功能用
        async check_http_server_port(rule, value, callback) {
            if (!this.requestProtocolIncludesHttp) return callback();
            if (value === "" || value === null || value === undefined) {
                return callback(new Error(this.$t("domain.detail.tip90")));
            } else {
                const pattern = portRegex;
                const portList = value?.split(",");
                const https_server_port = this.form?.https_server_port || "";
                const list = (https_server_port && https_server_port?.split(",")) || [];
                const portMap = {};
                for (let i = 0; i < portList.length; i++) {
                    if (portMap[portList[i]]) {
                        return callback(new Error(this.$t("domain.detail.placeholder45")));
                    } else {
                        portMap[portList[i]] = true;
                    }
                    if (!pattern.test(portList[i])) {
                        return callback(new Error(this.$t("domain.detail.placeholder83")));
                    }
                    if (list?.some(itm => itm === portList[i])) {
                        return callback(new Error(this.$t("domain.detail.placeholder46")));
                    }
                }
                return callback();
            }
        },
        async check_https_origin_port(rule, value, callback) {
            if (!this.requestProtocolIncludesHttps) return callback();
            if (value === "" || value === null || value === undefined) {
                return callback(new Error(this.$t("domain.detail.tip92")));
            } else {
                const pattern = portRegex;
                const portList = value?.split(",");
                const http_server_port = this.form?.http_server_port || "";
                const list = (http_server_port && http_server_port?.split(",")) || [];
                const portMap = {};
                for (let i = 0; i < portList.length; i++) {
                    if (portMap[portList[i]]) {
                        return callback(new Error(this.$t("domain.detail.placeholder45")));
                    } else {
                        portMap[portList[i]] = true;
                    }
                    if (!pattern.test(portList[i])) {
                        return callback(new Error(this.$t("domain.detail.placeholder83")));
                    }
                    if (list?.some(itm => itm === portList[i])) {
                        return callback(new Error(this.$t("domain.detail.placeholder46")));
                    }
                }
                return callback();
            }
        },
    },
};
</script>

<style lang="scss" scoped>
@import "@/views/domainConfig/commonStyle.scss";
.bucket-origin-wrapper {
    ::v-deep {
        .el-form--label-left .el-form-item__label {
            text-align: right;
        }
    }
}
.server_port_wrapper {
    display: flex;
    justify-content: start;
    .server_http_port_wrapper {
        ::v-deep {
            .el-form-item__label {
                width: 48px !important;
            }
            .el-form-item__content {
                margin-left: 56px !important;
            }
        }
    }
    .server_https_port_wrapper {
        ::v-deep {
            .el-form-item__label {
                width: 48px !important;
            }
            .el-form-item__content {
                margin-left: 60px !important;
            }
        }
    }
    .server_https_port_wrapper2 {
        margin-left: 12px;
    }
    .co-red {
        margin-left: 4px;
        color: $color-ct !important;
    }
}
.hsts-bg {
    display: flex;
    flex-direction: column;
    justify-content: center;
    width: 70%;
    height: 108px;
    background: $color-neutral-1;
    margin-left: 140px;
    ::v-deep {
        .el-form-item.el-form-item--medium {
            margin-bottom: 0px;
        }
    }
}
.sub-domain-wrapper {
    margin-top: 20px;
}
.input-style {
    width: 380px;
}
.question-circle {
    font-size: $text-size-md;
    cursor: pointer;
}
.icon-column-label {
    color: $text-color-light;
    margin-left: $margin-2x;
    font-size: $text-size-md;
    line-height: 18px;
    height: 14px;
    width: 14px;
}
.tips {
    font-size: 12px;
    color: $color-neutral-7;
    font-weight: 400;
    margin-left: $margin;
}
</style>
