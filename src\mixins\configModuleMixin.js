import { ConfigModulesModule } from '@/store/modules/configModules';
import { getRegistryConfig } from '@/registry/configModuleRegistry';

export default {
    computed: {
        // 总开关状态
        isMasterSwitchEnabled() {
            return ConfigModulesModule.masterSwitch;
        },
    },

    methods: {
        // 合并注册中心的配置项字段的初始化
        mergeConfigModuleFields() {
            // 如果总开关关闭，不合并任何字段
            if (!this.isMasterSwitchEnabled) {
                return;
            }
            const registry = getRegistryConfig();
            if (!registry) return;

            Object.values(registry).forEach(cfg => {
                if (cfg.fields) {
                    this.form = {
                        ...this.form,
                        ...cfg.fields
                    };
                }
            });
        },

        // 初始化表单数据
        initConfigModules(val) {
            // 如果总开关开启，再初始化启用的配置项字段
            if (this.isMasterSwitchEnabled) {
                const registry = getRegistryConfig();
                if (!registry) return;

                Object.entries(registry).forEach(([module, cfg]) => {
                    if (this.isModuleEnabled(module)) {
                        const customInit = this.$refs[module]?.init
                        if (customInit) {
                            const initData = customInit(val);
                            Object.keys(cfg.fields).forEach(field => {
                                this.form[field] = initData?.[field] ?? cfg.fields[field];
                            });
                        } else {
                            Object.keys(cfg.fields).forEach(field => {
                                this.form[field] = val?.[field] ?? cfg.fields[field];
                            });
                        }
                    }
                });
            }
        },

        /**
         * 自动 onModuleChange，处理表单联动
         * 当模块状态发生变化时，此函数会被调用，以处理相应的表单联动逻辑
         * @param {string} module - 模块标识符，表示哪个模块的状态发生了变化
         * @param {any} val - 新的模块状态值
         */
        onModuleChange(module, val) {
            // 如果总开关关闭，不处理任何联动
            if (!this.isMasterSwitchEnabled) {
                return;
            }
            const cfg = getRegistryConfig(module);
            if (cfg?.onModuleChange) {
                // onModuleChange 会返回所有需要更新的字段（包括联动字段）
                const updates = cfg.onModuleChange(this.form, val);
                // 更新表单数据，触发各个组件的 watch
                Object.assign(this.form, updates);
            }
        },

        // 接口提交前的数据处理
        getApiData() {
            // 如果总开关关闭，不处理任何新增配置项的数据
            if (!this.isMasterSwitchEnabled) {
                return {};
            }
            const registry = getRegistryConfig();
            if (!registry) {
                return {};
            }

            const newModulesData = {};
            Object.entries(registry).forEach(([module, cfg]) => {
                // 检查模块是否启用且满足展示条件
                if (this.isModuleEnabled(module) && this.checkDisplayCondition(module)) {
                    // 使用 store 中的功能锁判断
                    const isLocked = cfg.useCustomLock ? cfg.useCustomLock(this) : ConfigModulesModule.isModuleLocked(module);
                    const componentForm = this.$refs[module]?.$refs[`${module}_form`];
                    if (componentForm) {
                        Object.assign(newModulesData, cfg.toApi(this.form, { isLocked, componentForm }));
                    }
                }
            });

            return newModulesData;
        },

        // 表单校验自动化
        renderConfigModuleValidate() {
            // 如果总开关关闭，不处理任何新增配置项的校验
            if (!this.isMasterSwitchEnabled) {
                return [];
            }

            const registry = getRegistryConfig();
            if (!registry) return [];
            const validateForm = [];
            Object.keys(registry)
                .filter(key => this.isModuleEnabled(key) && this.checkDisplayCondition(key))
                .forEach(key => {
                    const componentForm = this.$refs[key]?.$refs[`${key}_form`];
                    if (componentForm) {
                        validateForm.push(componentForm);
                    }
                });
            return validateForm;
        },

        // 判断模块是否启用
        isModuleEnabled(module) {
            return ConfigModulesModule.isModuleEnabled(module);
        },

        // 检查模块是否满足展示条件
        checkDisplayCondition(module) {
            const cfg = getRegistryConfig(module);
            if (!cfg?.displayCondition) {
                return true; // 如果没有配置展示条件，默认显示
            }
            return cfg.displayCondition(this.form, this);
        },

        // 加载配置项状态
        async loadConfigModules(domain) {
            // 只获取模块状态，组件注册由 gatewayDataMixin 处理
            await ConfigModulesModule.fetchModulesStatus(domain);
        },
    }
};
