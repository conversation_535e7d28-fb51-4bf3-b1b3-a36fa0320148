/*
 * @Description: tab 权限菜单（由 iam 提供，详见 docs\design\权限-IAM提供（资源级）.md）
        可以理解为特定需求的 menu 菜单，用于控制指定页面的 tabs ，满足指定 tab 项显示控制
 * @Author: wang yuegong
 */
import { VuexModule, Module, Action, Mutation, getModule } from "vuex-module-decorators";

import store from "../index";
import { ctFetch } from "@/utils";
import { MenuUrl, CtiamMenuUrl } from "@/config/url";
import { DomainParam, MenuListItem as AuthListItem } from "../types";
import { DomainParamEnum } from "../config";
import { MenuModule } from "@/store/modules/menu";
// 权限单元
export interface TabMenuUnit {
    [unit: string]: boolean;
}

export interface TabMenuState {
    [DomainParamEnum.UsageTab]: TabMenuUnit;
    [DomainParamEnum.RankTab]: TabMenuUnit;
}

const usageTab = {
    bandwidthFlow: true, // 带宽流量，默认打开
    bandwidthFlowWhole: true, // 带宽流量（全站加速），默认打开
    miss: true, // 回源统计，默认打开
    missWhole: true, // 回源统计（全站加速），默认打开
    request: true, // 请求数，默认打开
    requestWhole: true, // 请求数，默认打开
    hit: true, // 命中率，默认打开
    statusCode: true, // 状态码，默认打开
    backToOriginStatusCode: true, // 回源状态码，默认打开
    pvuv: true, // pvuv，默认关闭
    provider: true, // 地区运营商
    downloadSpeed: true,
    privateNetworkAccelerator: true,
    privateNetworkAcceleratorWhole: true,
}

const rankTab = {
    hotUrl: true, // 热门URL，默认打开
    hotUrlMiss: true, // 热门URL（回源），默认打开
    hotReferer: true, // 热门Referer，默认打开
    domainRank: true, // 域名排行，默认打开
    topIp: true, // TOP客户端IP，默认打开
}

const ctiamTab = {
    usage: false, // 用量分析，默认关闭
    usageWhole: false, // 用量分析（全站加速），默认关闭
    rank: false, // 热门分析，默认关闭
    rankWhole: false, // 热门分析（全站加速），默认关闭
    user: false, // 用户分析，默认关闭
    userWhole: false, // 用户分析（全站加速），默认关闭
};

@Module({ dynamic: true, store, name: "auth" })
class TabMenu extends VuexModule implements TabMenuState {
    // 通过创建 osp/oss 菜单，动态控制 tab 显隐，默认状态根据实际情况预设
    public [DomainParamEnum.UsageTab]: TabMenuUnit = { ...usageTab };
    public [DomainParamEnum.RankTab]: TabMenuUnit = { ...rankTab };
    public [DomainParamEnum.AocdnUsageTab]: TabMenuUnit = { ...usageTab };
    public [DomainParamEnum.AocdnRankTab]: TabMenuUnit = { ...rankTab };
    public [DomainParamEnum.Ctiam]: TabMenuUnit = { ...ctiamTab }; // 一级菜单

    @Mutation
    private SET_AUTH_LIST({ pageDomain, pageAuth }: { pageDomain: DomainParam; pageAuth: TabMenuUnit }) {
        // 采用合并方式，可以最小化配置 osp/oss
        this[pageDomain] = {
            ...this[pageDomain],
            ...pageAuth,
        };
    }

    @Action
    public async GetAuthList(pageDomain: DomainParam) {
        const { items: authList }: { items: AuthListItem[] } = await ctFetch(MenuUrl, {
            data: { domain: pageDomain },
        });

        if (!authList || !authList.length) return;
        const pageAuth: TabMenuUnit = {};

        // 遍历 list ，生成简化结果
        authList.forEach(auth => {
            pageAuth[auth.ucode] = auth.enable === "true" && auth.state === "online";
        });

        this.SET_AUTH_LIST({
            pageDomain,
            pageAuth,
        });
    }

    @Action
    public async GetCtiamAuthList(pageDomain: DomainParam) {
        if (!MenuModule.menuList || !MenuModule.menuList.length) return;

        const pageAuth: TabMenuUnit = {};

        // 遍历菜单，生成简化结果，取三级菜单为统计分析模块内页面一级tab的权限
        MenuModule.menuList.forEach((menu: any) => {
            if (menu.ucode === "statistics") {
                menu.items.forEach((sub: any) => {
                    sub.items.forEach((element: any) => {
                        pageAuth[element.ucode] = element.enable === "true" && element.state === "online";
                    });
                });
            }
        });
        console.log(pageDomain, "GetCtiamAuthList===pageAuth", pageAuth);

        this.SET_AUTH_LIST({
            pageDomain,
            pageAuth,
        });
    }
}

export const TabMenuModule = getModule(TabMenu);
