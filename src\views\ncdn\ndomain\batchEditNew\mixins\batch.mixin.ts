import { ElForm } from "@cutedesign/ui";
import { Vue, Component, Ref, Watch } from "vue-property-decorator";
import BatchEditChecked from "../components/batchEditChecked.vue";

@Component({
    name: "BatchItemMixin",
    components: {
        BatchEditChecked,
    },
})
export default class BatchItemMixin extends Vue {
    @Ref("formRef") formRef!: ElForm;

    checked = false;

    /**
     * 由于 checked 的 watch 在 mixin 中定义了，如果子组件也需要 watch checked，则需要重写此方法
     * @param val
     */
    checkedWatchCallback(val?: boolean) {
        // console.log("checkedWatchCallback = ", val, this.formRef);
    }

    @Watch("checked")
    onCheckedChange(val: boolean) {
        this.checkedWatchCallback(val);

        if (val) return;
        this.$nextTick(() => {
            this.formRef.clearValidate();
        });
    }

    get formData() {
        console.error("define formData in child");
        return {};
    }
}
