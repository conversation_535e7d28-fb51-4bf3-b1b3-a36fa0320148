// 执行完 pnpm i 后的任务
const shell = require("shelljs");
const os = require('os');

/**
 * 任务：关闭 windows 系统下的 git autocrlf
 * 原因：项目统一设置结尾样式为 lf ，配置见 .editorconfig
 * 验证：查看项目根目录下 /.git/config 文件是否有对应配置，或查看 git config --list --local
 */
try {
    // 如果系统换行符是 \r\n 则关闭自动转换
    const isCrlf = os.EOL === "\r\n";
    if(isCrlf) {
        shell.exec("git config --local core.autocrlf false");
        console.log("手动关闭 git autocrlf 配置成功！")
    }
} catch(e) {
    console.log("手动关闭 git autocrlf 配置失败：", e);
}

/**
 * 任务：安装已有packeges的补丁
 * 原因：有部分源码需要通过patch进行更改
 */
try {
    shell.exec("npx patch-package");
} catch(e) {
    console.log("安装失败：", e);
}
