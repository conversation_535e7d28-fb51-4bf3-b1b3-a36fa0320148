import { Component, Prop, Vue } from "vue-property-decorator";

import { ScaleModule } from "@/store/modules/scale";
import { convertBandwidthB2G, convertBandwidthB2P, convertFlowB2G, convertFlowB2P, downloadCsv } from "@/utils";
import { timeFormat } from "@/filters/index";
import { SearchParams } from "@/types/statistics/rank";
import { DomainModule } from "@/store/modules/domain";
import { CurrentMenu } from "../statistics";
import { MainMenu } from "../../entryMixin";
import { StatisticsModule } from "@/store/modules/statistics";

@Component
export default class RankMixin extends Vue {
    @Prop({ default: () => ({ main: "", sub: "" }), type: Object }) private currentMenu!: CurrentMenu<MainMenu>;

    loading = false;
    protected noData = true;
    // 缓存请求参数
    protected searchParams: SearchParams = {
        domainList: [],
        startTime: 0,
        endTime: 0,
        product: [],
    };
    // 接口数据
    protected fetchData: any[] = [];
    // 用于下载的数据
    protected downloadDataList: any[] = [];

    // 全部的域名列表
    get allDomainList() {
        // 由于需要进行二次判断过滤，选择使用接口原生数据
        // 由于需要进行二次判断过滤，选择使用接口原生数据
        return DomainModule[StatisticsModule.currentAction].nativeList;
    }
    // 获取进制基数
    get scale() {
        return ScaleModule.scale;
    }

    get MB() {
        return this.scale === 1024 ? "MiB" : "MB";
    }
    get Mbps() {
        return this.scale === 1024 ? "Mibps" : "Mbps";
    }

    mounted() {
        this.$nextTick(() => {
            this.$emit("childPanelMounted", true);
        });
    }

    beforeDestroy() {
        this.$emit("childPanelMounted", false);
    }

    childPaneStartLoading() {
        this.loading = true;
    }
    // 根据数据获取流量的{单位，缩进}配置
    // 统一拦截请求
    protected beforeGetData(params: SearchParams): void {
        // 无可选域名时，拦截请求
        if (this.allDomainList.length === 0) {
            this.$message.error(`${this.$t("statistics.common.chart.errMsg[0]")}`);
            return;
        }
        // 统一缓存请求参数
        this.searchParams = params;
        // 统一变更状态
        this.loading = true;
        this.noData = true;

        this.getData(params);
    }

    // 请求生成器（用于统一控制 fetch 配置参数）
    protected async fetchGenerator<T>(url: string, params: SearchParams) {
        const rst = await this.$ctFetch<T>(url, {
            method: "POST",
            // clearEmptyParams: false,
            body: {
                data: params,
            },
            headers: {
                "Content-Type": "application/json",
            },
        });

        return rst;
    }

    // 获取数据，各组件内部实现
    protected getData(params: SearchParams): void | Promise<void>;
    protected getData() {
        // eslint-disable-next-line no-console
        console.error(`${this.$t("statistics.common.chart.errMsg[1]")}`);
    }

    // 统一拦截下载
    protected beforeDownload() {
        // if (this.downloadDataList.length === 0) {
        //     this.$message.warning(`${this.$t("statistics.common.chart.errMsg[2]")}`);
        //     return;
        // }
        this.tableToExcel();
    }

    // 需要各组件内部实现
    protected tableToExcel() {
        // eslint-disable-next-line no-console
        console.error(`${this.$t("statistics.common.chart.errMsg[3]")}`);
        return;
    }

    // 通用下载方法，如果需要定制则重写
    protected downloadExcel({ name, str }: { name: string; str: string }) {
        // 说明：由于下载时需要用到请求参数 searchParams 中的数据，所以需要在 getData 中主动缓存使用
        const t1 = timeFormat(+this.searchParams.startTime)
            .replace(/-|\s|:/g, "")
            .slice(0, 12);
        const t2 = timeFormat(+this.searchParams.endTime)
            .replace(/-|\s|:/g, "")
            .slice(0, 12);

        downloadCsv(`${name}${t1}-${t2}`, str);
    }

    // 从数组中获取指定字段的最大值（用于生成合理的缩进规则）
    /**
     * 获取“次”的翻译，并包裹上括号
     */
    protected getTimesUnit() {
        const unit = this.$t("statistics.dcdn.backToOriginStatusCode.totalTipUnit");
        return unit ? `(${unit})` : unit;
    }
}
