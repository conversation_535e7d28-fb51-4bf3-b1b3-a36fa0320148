import { ScaleModule } from "@/store/modules/scale";
// import { downloadCsv } from "@/utils";
// import { timeFormat } from "@/filters/index";
// import { SearchParams } from "@scdn/types/statistics/rank";

export default {
    data() {
        return {
            loading: false,
            noData: true,
            searchParams: {
                /* eslint-disable */
                // productCode: [],
                // domainList: [],
                start_time: undefined,
                end_time: undefined,
            },
            fetchData: [],
            downloadDataList: [],
        };
    },
    computed: {
        // 获取进制基数
        scale() {
            return ScaleModule.scale;
        },
    },
    methods: {
        // 统一拦截请求
        beforeGetData(params) {
            // 在实际请求前判断是否有可用域名（不仅有 searchbar 触发，还可能有其他触发源）
            if (params.domainList.length === 0) {
                this.$message(`${this.$t("statistics.common.chart.errMsg[0]")}`);
                return;
            }

            // 统一缓存请求参数
            this.searchParams = params;
            // 统一变更状态
            this.loading = true;
            this.noData = true;

            this.getData(params);
        },

        // 请求生成器（用于统一控制 fetch 配置参数）
        fetchGenerator(url, SearchParams) {
            return this.$ctFetch(url, {
                method: "POST",
                transferType: "json",
                // clearEmptyParams: false,
                body: {
                    data: SearchParams,
                },
            });
        },

        // 获取数据，各组件内部实现
        // getData() {
        //     // eslint-disable-next-line no-console
        //     console.error("当前页面缺少 getData 方法");
        // },

        // 统一拦截下载
        beforeDownload() {
            if (this.downloadDataList.length === 0) {
                this.$message(`${this.$t("statistics.common.chart.errMsg[2]")}`);
                return;
            }
            this.tableToExcel();
        },

        // 需要各组件内部实现
        tableToExcel() {
            // eslint-disable-next-line no-console
            console.error(this.$t("statistics.common.chart.errMsg[1]"));
            return;
        },

        // 通用下载方法，如果需要定制则重写
        // downloadExcel({ name, str }) {
        //     // 说明：由于下载时需要用到请求参数 searchParams 中的数据，所以需要在 getData 中主动缓存使用
        //     const t1 = timeFormat(+this.searchParams.start_time)
        //         .replace(/-|\s|:/g, "")
        //         .slice(0, 12);
        //     const t2 = timeFormat(+this.searchParams.end_time)
        //         .replace(/-|\s|:/g, "")
        //         .slice(0, 12);

        //     downloadCsv(`${name}${t1}-${t2}`, str);
        // },

        // 从数组中获取指定字段的最大值（用于生成合理的缩进规则）
        getMaxFromList(list, key) {
            let max = 0;
            list.map(item => +item[key]).forEach(num => (max = max > num ? max : num));
            return max;
        },
    },
};
