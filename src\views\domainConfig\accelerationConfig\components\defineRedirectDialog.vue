<template>
    <el-dialog
        :title="dialogTitle"
        :append-to-body="true"
        :close-on-click-modal="false"
        :visible.sync="dialogVisible"
        :before-close="cancel"
        width="900px"
        class="define-redirect-dialog"
    >
        <el-form
            :rules="addRules"
            :model="defineRedirectForm"
            ref="defineRedirectForm"
            class="cache-form"
            label-width="160px"
        >
            <el-form-item :label="$t('domain.type')" prop="mode">
                <span slot="label">
                    {{ $t("domain.type") }}
                    <span>
                        <el-tooltip placement="top">
                            <div slot="content">
                                <span>{{ $t("domain.editPage.placeholder14") }}</span>
                            </div>
                            <ct-svg-icon
                                icon-class="question-circle"
                                class-name="ct-sort-drag-icon"
                            ></ct-svg-icon></el-tooltip></span
                ></span>
                <el-radio-group
                    v-model.number="defineRedirectForm.mode"
                    @change="typeChange"
                    class="cache-form--selector"
                >
                    <el-radio
                        v-for="opt in cacheModeOptions"
                        :key="opt.value"
                        :label="opt.value"
                        :disabled="opt.disabled"
                    >
                        {{ opt.label }}
                    </el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item
                key="content"
                :label="$t('domain.content')"
                prop="content"
                v-if="dialogVisible"
                :class="{'is-required': defineRedirectForm.mode !== '' && defineRedirectForm.mode !== null && defineRedirectForm.mode !== undefined}"
            >
                <el-input
                    v-model="defineRedirectForm.content"
                    :placeholder="defineRedirectPlaceholder()"
                    @focus="showNameSet(defineRedirectForm)"
                    :disabled="defineRedirectForm.mode === 2 || defineRedirectForm.mode === 3"
                    clearable
                ></el-input>
            </el-form-item>
            <el-form-item prop="pattern">
                <span slot="label">
                    {{ $t("domain.detail.label49") }}
                    <span>
                        <el-tooltip placement="top">
                            <div slot="content">
                                <span>{{ $t("domain.editPage.placeholder10") }}</span>
                            </div>
                            <ct-svg-icon
                                icon-class="question-circle"
                                class-name="ct-sort-drag-icon"
                            ></ct-svg-icon></el-tooltip></span
                ></span>
                <el-input v-model="defineRedirectForm.pattern"></el-input>
            </el-form-item>
            <el-form-item prop="replacement">
                <span slot="label">
                    {{ $t("domain.detail.label50") }}
                    <span>
                        <el-tooltip placement="top">
                            <div slot="content">
                                <span>{{ $t("domain.editPage.placeholder11") }}</span>
                            </div>
                            <ct-svg-icon
                                icon-class="question-circle"
                                class-name="ct-sort-drag-icon"
                            ></ct-svg-icon></el-tooltip></span
                ></span>
                <el-input v-model="defineRedirectForm.replacement"></el-input>
            </el-form-item>
            <el-form-item :label="$t('domain.editPage.label35')" prop="code">
                <el-radio-group v-model="defineRedirectForm.code">
                    <el-radio label="301">301</el-radio>
                    <el-radio label="302">302</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item :label="$t('domain.detail.label48')" prop="priority">
                <el-input
                    v-model.number="defineRedirectForm.priority"
                    maxlength="16"
                    clearable
                    placeholder="1~100"
                ></el-input>
            </el-form-item>
        </el-form>
        <div slot="footer">
            <el-button class="dialog-btn" @click="cancel">{{ $t("domain.cancel") }} </el-button>
            <el-button type="primary" class="dialog-btn" @click="submit">{{
                $t("common.dialog.submit")
            }}</el-button>
        </div>
        <el-dialog
            :title="$t('domain.detail.cacheModeMap.0')"
            :append-to-body="true"
            :close-on-click-modal="false"
            :modal-append-to-body="false"
            :visible.sync="extensionsDialogVisible"
            custom-class="aocdn-cache-extensions-dialog"
        >
            <div class="aocdn-cache-extensions" v-for="(val, key) in extensionsOptions" :key="val.title">
                <div class="extension-title">
                    <el-checkbox
                        v-model="extensionAllSelected[key]"
                        @change="checkAllChange(key)"
                        :label="val.title"
                    />
                </div>
                <div class="extension-list">
                    <el-checkbox-group v-model="extensionSelected[key]" @change="checkSingleChange(key)">
                        <el-checkbox v-for="extension in val.list" :label="extension" :key="extension" />
                    </el-checkbox-group>
                </div>
            </div>
            <div class="aocdn-cache-extensions">
                <div class="extension-title">{{ $t("domain.detail.label40") }}</div>
                <div class="extension-list">
                    <el-input
                        type="textarea"
                        :rows="2"
                        :placeholder="$t('domain.detail.placeholder1')"
                        v-model="extensionOther"
                    />
                </div>
            </div>
            <div slot="footer" class="dialog-footer">
                <el-button @click="extensionsDialogVisible = false">{{
                    $t("common.dialog.cancel")
                }}</el-button>
                <el-button type="primary" @click="setName">{{ $t("common.dialog.submit") }}</el-button>
            </div>
        </el-dialog>
    </el-dialog>
</template>

<script lang="ts">
import ctSvgIcon from "@/components/SvgIcon/index.vue";
import { Component, Prop, Vue, Watch } from "vue-property-decorator";
import pattern from "@/config/pattern";
import { formValidate2Promise } from "@/utils";
import { Form } from "element-ui";
import i18n from "@/i18n";
import { extensionsOptions } from "@/components/simpleform/nAlogicCacheTable/config";
import { nUserModule } from "@/store/modules/nuser";
import { ElForm } from "element-ui/types/form";
import { getConditionContentPlaceholder } from "@/components/commonCondition/utils";

const extensionsMap: any = {};
Object.keys(extensionsOptions).forEach((key: any) => {
    (extensionsOptions as any)[key].list.forEach((k: any) => {
        extensionsMap[k] = key;
    });
});

// 类型：字典翻译
const CacheModeMap: any = {
    0: i18n.t("domain.detail.cacheModeMap[0]"),
    1: i18n.t("domain.detail.cacheModeMap[1]"),
    2: i18n.t("domain.detail.cacheModeMap[2]"),
    3: i18n.t("domain.detail.cacheModeMap[3]"),
    4: i18n.t("domain.detail.cacheModeMap[4]"),
    // 5: i18n.t("domain.detail.cacheModeMap[5]"),
};
const cacheModeOptions = Object.keys(CacheModeMap).map((mode: any) => ({
    label: CacheModeMap[mode],
    value: mode * 1, // 需要是 number 类型
}));

type defineRedirectParam = {
    mode: string;
    content: string;
    priority: number;
    code: string;
    pattern: string;
    replacement: string;
};
const formValidate2Field = (form: Form) =>
    new Promise((resolve, reject) => {
        form.validate(valid => {
            if (valid) {
                resolve(true);
            }
        });
    });

@Component({
    components: {
        ctSvgIcon,
    },
})
export default class UpdateDialog extends Vue {
    private pattern = pattern;
    @Prop({ default: "create", type: String }) private from!: string;
    // 弹框显示标志位
    @Prop({ default: false, type: Boolean }) private dialogVisible!: boolean;
    @Prop({
        default: {
            id: "",
            mode: null,
            content: "",
            priority: 10,
            code: "302",
            pattern: "",
            replacement: "",
        },
    })
    private defineRedirectForm!: defineRedirectParam;
    @Prop({ required: true, type: Array }) define_redirect!: any[];
    // @Prop({ type: Array }) temp_define_redirect!: any[];

    extensionAllSelected: any = {
        dynamic: false,
        image: false,
        style: false,
        av: false,
        download: false,
        page: false,
    };
    extensionSelected: any = {
        dynamic: [],
        image: [],
        style: [],
        av: [],
        download: [],
        page: [],
    };
    extensionOther = "";
    extensionsDialogVisible = false;
    extensionsOptions: any = extensionsOptions;

    get dialogTitle() {
        const separate = nUserModule.lang === "en" ? " " : "";
        return `${
            this.from === "create" ? i18n.t("domain.add2") : i18n.t("domain.modify")
        }${separate}${i18n.t("domain.editPage.label36")}`;
    }

    get values() {
        // 处理换行、所有空格、过滤空数据
        return this.defineRedirectForm;
    }

    // 校验规则
    get addRules() {
        return {
            mode: [{ required: false, validator: this.validMode, trigger: "change" }],
            content: [
                {
                    validator: (rule: any, value: string, callback: Function) => {
                        const paths = value?.split(",");
                        const allPathPattern = new RegExp("^([A-Za-z0-9]|[!#$&'()*+,-./:;=?@_~])*$");
                        paths?.forEach(path => {
                            if (
                                parseInt(this.defineRedirectForm.mode) === 4 &&
                                (path.includes("?") || path.includes("？"))
                            )
                                callback(this.$t("domain.detail.placeholder48"));
                            if (
                                parseInt(this.defineRedirectForm.mode) === 4 &&
                                path.length > 0 &&
                                path[0] !== "/"
                            )
                                callback(this.$t("domain.detail.placeholder49"));
                        });
                        if (parseInt(this.defineRedirectForm.mode) === 0 && value === "")
                            callback(this.$t("domain.detail.placeholder9-1"));
                        if (
                            parseInt(this.defineRedirectForm.mode) === 0 &&
                            !/^\w{1,9}(,\w{1,9})*$/.test(value)
                        )
                            callback(this.$t("domain.detail.placeholder50"));
                        if (parseInt(this.defineRedirectForm.mode) === 1 && value === "")
                            callback(this.$t("domain.detail.placeholder11-1"));
                        if (
                            parseInt(this.defineRedirectForm.mode) === 1 &&
                            !/^((\/[\w-.]+)+,)*(\/[\w-.]+)+$/.test(value)
                        )
                            callback(this.$t("domain.detail.placeholder51"));
                        if (parseInt(this.defineRedirectForm.mode) === 2 && value === "")
                            callback(this.$t("domain.detail.placeholder52"));
                        if (parseInt(this.defineRedirectForm.mode) === 3 && value === "")
                            callback(this.$t("domain.detail.placeholder53"));
                        if (parseInt(this.defineRedirectForm.mode) === 4 && value.trim() === "")
                            callback(this.$t("domain.detail.placeholder13-1"));
                        if (parseInt(this.defineRedirectForm.mode) === 4 && !allPathPattern.test(value))
                            callback(this.$t("domain.detail.placeholder14"));
                        else callback();
                    },
                    trigger: ["blur", "change"],
                },
                // { required: true, message: this.$t("domain.detail.placeholder54"), trigger: "blur" },
            ],
            priority: [
                { required: true, message: this.$t("domain.detail.placeholder20"), trigger: "blur" },
                { trigger: "blur", pattern: /^[0-9]*$/, message: this.$t("domain.detail.placeholder65") },
                {
                    validator: (rule: any, value: number, callback: Function) => {
                        if (value > 100) callback(this.$t("domain.detail.placeholder66"));
                        if (value < 1) callback(this.$t("domain.detail.placeholder67"));
                        else callback();
                    },
                    trigger: ["blur", "change"],
                },
            ],
            replacement: [
                { required: true, message: this.$t("domain.detail.placeholder7"), trigger: "blur" },
                { validator: (r: any, v: string, c: Function) => { v.includes(" ") ? c(new Error(this.$t("domain.detail.tip78") as string)) : c() } }
            ],
            code: [{ required: true, message: this.$t("domain.detail.placeholder8"), trigger: "blur" }],
        };
    }
    // 动态计算选项
    get cacheModeOptions() {
        return cacheModeOptions?.map(option => {
            let disabled = false;

            // mode 2、3 只支持选一个
            if (option.value === 2 || option.value === 3) {
                disabled = this.define_redirect.some(item => item.mode === option.value);
            }

            return {
                disabled,
                ...option,
            };
        });
    }

    validMode(rule: any, value: any, callback: Function) {
        if ((value === "" || value === null || value === undefined) && this.defineRedirectForm.content) {
            return callback(new Error(this.$t("domain.htmlForbid.forbid5") as string));
        } else {
            return callback();
        }
    }

    private defineRedirectPlaceholder() {
        return getConditionContentPlaceholder(this.cacheModeOptions, parseInt(this.defineRedirectForm.mode));
    }
    private async typeChange(val: number) {
        if (val === 2 || val === 3) {
            this.defineRedirectForm.content = "/";
        } else {
            this.defineRedirectForm.content = "";
        }
        await formValidate2Field(this.$refs.defineRedirectForm as Form);
    }
    private async submit() {
        await formValidate2Promise(this.$refs.defineRedirectForm as Form);
        await this.$emit("submit");
        (this.$refs.defineRedirectForm as ElForm)?.resetFields();
    }
    private cancel() {
        this.$emit("cancel", "defineRedirectDialogVisible");
        (this.$refs.defineRedirectForm as ElForm)?.resetFields();
    }
    showNameSet(cache: any) {
        // 内容为后缀名才弹出弹框
        if (cache.mode !== 0) return;
        // 清掉缓存
        Object.keys(this.extensionAllSelected).forEach((key: any) => {
            this.extensionAllSelected[key] = false;
            this.extensionSelected[key] = [];
        });
        const other: any = [];
        // 过滤有复选框的后缀名
        cache.content
            .split(",")
            .filter((val: any) => val)
            .forEach((item: any) => {
                if (extensionsMap[item]) {
                    this.extensionSelected[extensionsMap[item]].push(item);
                } else {
                    other.push(item);
                }
            });
        // 其他后缀名
        this.extensionOther = other.join(",");
        // 全选按钮是否选中
        Object.keys(this.extensionSelected).forEach(key => {
            this.extensionAllSelected[key] =
                new Set(this.extensionSelected[key]).size === (extensionsOptions as any)[key].list.length;
        });
        this.extensionsDialogVisible = true;
    }
    // 全选按钮逻辑
    checkAllChange(key: any) {
        const val = this.extensionAllSelected[key];
        this.extensionSelected[key] = val ? (extensionsOptions as any)[key].list : [];
    }
    // 选项是否触发全选
    checkSingleChange(key: any) {
        const val = this.extensionSelected[key];
        this.extensionAllSelected[key] = val.length === (extensionsOptions as any)[key].list.length;
    }
    setName() {
        const extentionReg = new RegExp("^\\w{1,9}(?:,\\w{1,9})*$|^$");
        if (!extentionReg.test(this.extensionOther)) {
            (this.$message.error as any)(this.$t("domain.detail.tip30"));
            return;
        }
        this.extensionsDialogVisible = false;
        // 选中复选框的后缀
        const extensions = Object.keys(this.extensionSelected)
            .reduce((rst, key) => {
                if (this.extensionSelected[key].length > 0) {
                    return rst + "," + this.extensionSelected[key].join(",");
                }
                return rst;
            }, "")
            .slice(1);

        // 没有更多，则直接用计算出的结果；如果有更多，则进行拼接
        this.defineRedirectForm.content = !this.extensionOther
            ? extensions
            : `${extensions}${extensions ? "," : ""}${this.extensionOther}`;
    }
}
</script>

<style lang="scss" scoped>
.define-redirect-dialog {
    ::v-deep {
        .el-dialog__body {
            padding: 24px !important;
        }
    }
}
// 缓存 URL 弹窗的样式
::v-deep .el-dialog {
    width: 60%;
    min-width: 340px;
    .cache-with-args {
        .el-form-item__label {
            word-break: normal;
        }
    }
}
</style>
