# 使用 FROM 指定基础镜像，后面操作都是基于它进行定制
FROM harbor.ctyuncdn.cn/cdngslbconf/nginx1.12.5-x86-arm:1.1.0
MAINTAINER anylogic
# 设定环境变量，eg：ENV <key> <value>，ENV <key1>=<value1> <key2>=<value2>...
ENV NGINX_STATUS=http://localhost/status/format/json
# 执行目录创建的命令（等同于在终端操作的 shell 命令）
RUN mkdir -p /www/h5/fcdn && mkdir -p /usr/local/nginx/conf
# 复制
ADD config/nginx.conf /usr/local/nginx/conf
ADD dist1 /www/h5/fcdn

# 指定工作目录（必须提前创建），会在构建镜像的每一层中都存在（每条 RUN 都会新建一层，只有 WORKDIR 目录一直存在）
WORKDIR /www/h5/fcdn

# TODO 上面三个 xxx 路径需要按需修改，影响的是发布后的页面访问路径，建议使用当前控制台的代码
# 最终页面的访问地址（以测试环境为例）：https://www-test.ctcdn.cn/h5/xxx
