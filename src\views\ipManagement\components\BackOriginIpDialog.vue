<template>
    <el-dialog
        :title="$t('ipManagement.planList.btns[4]')"
        :visible.sync="dialogVisible"
        :modal-append-to-body="false"
        :close-on-click-modal="false"
        :before-close="cancel"
        width="600px"
    >
        <el-form label-width="120px">
            <el-form-item :label="$t('ipManagement.planList.formTitle[4]')" prop="name">
                <el-input type="textarea" :value="ipInfo" disabled />
            </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
            <slot name="footer">
                <el-button @click="cancel">
                    {{ $t("ipManagement.planList.btns[7]") }}
                </el-button>
                <el-button type="primary" @click="copy">
                    {{ $t("ipManagement.planList.btns[8]") }}
                </el-button>
            </slot>
        </div>
    </el-dialog>
</template>

<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";
import { write } from "clipboardy";

@Component
export default class BackOriginIpDialog extends Vue {
    @Prop({ default: true, type: Boolean }) private dialogVisible!: boolean;
    @Prop({ default: "" }) private ipInfo?: string;

    /**
     * 复制ip
     */
    private copy() {
        write(this.ipInfo || "");
        this.$message.success(this.$t("ipManagement.planList.copySuccess") as string);
    }

    /**
     * 关闭当前弹窗
     */
    private cancel() {
        this.$emit("cancel");
    }
}
</script>

<style lang="scss" scoped>
.el-form {
    ::v-deep .el-form-item__label {
        word-break: normal;
    }
}
</style>
