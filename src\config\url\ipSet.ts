import { PROXY_PREFIX } from "./_PREFIX";

export const IpSetUrl = {
    list: PROXY_PREFIX + "/v1/ipset/list",
    create: PROXY_PREFIX + "/v1/ipset/create",
    delete: PROXY_PREFIX + "/v1/ipset/delete",

    // IP集新增ip
    addIp: PROXY_PREFIX + "/v1/ipset/addIp",
    // IP集删除IP
    deleteIp: PROXY_PREFIX + "/v1/ipset/deleteIp",
    // IP集内ip列表查询接口
    listIp: PROXY_PREFIX + "/v1/ipset/ipList",
    // IP集下载
    download: PROXY_PREFIX + "/v1/ipset/download",
    // IP集使用情况
    usage: PROXY_PREFIX + "/v1/ipset/usage",
    // 获取可配置IP集域名列表
    listOption: PROXY_PREFIX + "/v1/ipset/domainList",
    // 绑定域名
    batchAssociate: PROXY_PREFIX + "/v1/ipset/bindDomain",
    // 重试
    retry: PROXY_PREFIX + "/v1/ipset/retry",
};
