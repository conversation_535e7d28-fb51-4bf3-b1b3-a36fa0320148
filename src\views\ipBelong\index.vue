<template>
    <ct-section-wrap :headerText="$t('ipBelong.title')" :headerTip="headerTip">
        <ct-box>
            <el-form class="ip-form form" :model="ipForm" :rules="ipRules" ref="ipForm">
                <div class="search-box">
                    <el-form-item :label="$t('ipBelong.index954818-1')" prop="ip">
                        <el-input
                            v-model="ipForm.ip"
                            :placeholder="$t('ipBelong.index954818-2')"
                            class="input-style"
                        ></el-input>
                        <el-button class="btn" type="primary" @click="submitIP">{{
                            $t("ipBelong.index954818-3")
                        }}</el-button>
                    </el-form-item>
                </div>
            </el-form>
            <el-table :data="dataList">
                <el-table-column label="IP" prop="ip" width="150"></el-table-column>
                <el-table-column :label="nodeTypeText" min-width="150" align="left">
                    <template slot-scope="{ row }">
                        {{
                            row.isCDNip === "true"
                                ? $t("ipBelong.index954818-4")
                                : $t("ipBelong.index954818-5")
                        }}
                    </template>
                </el-table-column>
                <el-table-column
                    :label="$t('ipBelong.index954818-6')"
                    prop="ipAddress"
                    min-width="160"
                    align="left"
                ></el-table-column>
            </el-table>
        </ct-box>
    </ct-section-wrap>
</template>

<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import { IpCheck } from "@/types/ipBelong";
import { formValidate2Promise } from "@/utils";
import { Form } from "element-ui";
import { ipBelongUrl } from "@/config/url/ipBelong";
import i18n from "@/i18n";
const ipRules = {
    ip: [
        { required: true, message: i18n.t("ipBelong.index954818-7"), trigger: "blur" },
        {
            pattern: "^((25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(25[0-5]|2[0-4]\\d|[01]?\\d\\d?)$",
            message: i18n.t("ipBelong.index954818-8"),
            trigger: "blur",
        },
    ],
};

@Component
export default class IpBelong extends Vue {
    dataList: IpCheck[] = [];
    private ipForm = {
        ip: "",
    };
    private ipRules = Object.freeze(ipRules);
    private loading = false;
    get headerTip() {
        return window.__POWERED_BY_QIANKUN__
            ? this.$t("ipBelong.index954818-9")
            : this.$t("ipBelong.index954818-10");
    }
    get nodeTypeText() {
        return window.__POWERED_BY_QIANKUN__
            ? this.$t("ipBelong.index954818-11")
            : this.$t("ipBelong.index954818-12");
    }
    private async submitIP() {
        await formValidate2Promise(this.$refs.ipForm as Form);
        this.dataList = [];
        this.loading = true;
        const rst = await this.$ctFetch<IpCheck>(ipBelongUrl.IpCheck, {
            data: { ipv4: this.ipForm.ip },
        });
        this.dataList = [rst];
    }
}
</script>

<style lang="scss" scoped>
.ip-form {
    ::v-deep {
        .el-form-item__error {
            margin-left: 82px !important;
        }
    }

    .el-button {
        @include g-mg-lf(0, 10px, 16px);
    }
}

.input-style {
    width: 20% !important;
}

::v-deep {
    .el-form .el-form-item__content > .el-input {
        @include g-width(100%, 300px, 300px);
    }
    .el-form {
        .el-form-item__label {
            @include g-width(100%, 100px, 100px);
        }

        .el-form-item__content {
            @include g-mg-lf(0, 100px, 100px);
        }
    }
}
</style>
