<template>
    <div class="search-row" :style="[!$slots.label ? { gridTemplateColumns: '1fr' } : {}]">
        <div class="search-label" v-if="$slots.label">
            <slot name="label"></slot>
        </div>
        <div class="search-content" :style="{ maxWidth: getContentWidth }">
            <slot></slot>
        </div>
    </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";

@Component({
    name: "SearchBarRow",
})
export default class SearchBarRow extends Vue {
    // 每行几个输入框
    @Prop({ default: 3, type: Number }) private span!: number;
    // 输入框默认宽度
    @Prop({ default: 184, type: Number }) private width!: number;
    // 输入框默认间距
    @Prop({ default: 8, type: Number }) private gap!: number;
    @Prop({ default: false, type: Boolean }) private fullwidth!: boolean;

    get getContentWidth() {
        if (this.fullwidth)
            return `calc(100% - 24px - 16px)`;
        return `${this.width * this.span + this.gap * (this.span - 1)}px`;
    }
}
</script>


<style lang="scss" scoped>
.search-row {
    display: grid;
    gap: 16px;
    grid-template-columns: 60px 1fr;

    .el-button {
        margin-top: 0;
        margin-bottom: 0;
        margin-right: 0;
    }

    .el-select {
        width: 184px;
    }

    .search-label {
        height: 32px;
        line-height: 32px;
        flex-shrink: 0;
        align-self: flex-start;
    }

    .search-content {
        display: flex;
        gap: 8px;
        flex-shrink: 0;
        flex-wrap: wrap;
        align-items: flex-start;
    }
}
</style>
