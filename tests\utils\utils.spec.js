import { describe, it, expect } from '@jest/globals';
import { translateDataToTree } from '@/utils/utils';

describe('translateDataToTree', () => {
    it('should handle empty data', () => {
        const data = [];
        const result = translateDataToTree(data);
        expect(result).toEqual([]);
    });

    it('should handle single top-level node', () => {
        const data = [
            { id: 1, dicParentId: null }
        ];
        const result = translateDataToTree(data);
        expect(result).toEqual([
            {
                id: 1,
                dicParentId: null,
                children: []
            }
        ]);
    });

    it('should handle multiple top-level nodes', () => {
        const data = [
            { id: 1, dicParentId: null },
            { id: 2, dicParentId: null }
        ];
        const result = translateDataToTree(data);
        expect(result).toEqual([
            {
                id: 1,
                dicParentId: null,
                children: []
            },
            {
                id: 2,
                dicParentId: null,
                children: []
            }
        ]);
    });

    it('should build a tree with children', () => {
        const data = [
            { id: 1, dicParentId: null },
            { id: 2, dicParentId: 1 },
            { id: 3, dicParentId: 2 }
        ];
        const result = translateDataToTree(data);
        expect(result).toEqual([
            {
                id: 1,
                dicParentId: null,
                children: [
                    {
                        id: 2,
                        dicParentId: 1,
                        children: [
                            {
                                id: 3,
                                dicParentId: 2,
                                children: []
                            }
                        ]
                    }
                ]
            }
        ]);
    });

    it('should handle undefined or null parent IDs as top-level nodes', () => {
        const data = [
            { id: 1, dicParentId: undefined },
            { id: 2, dicParentId: null },
            { id: 3, dicParentId: 1 }
        ];
        const result = translateDataToTree(data);
        expect(result).toEqual([
            {
                id: 1,
                dicParentId: undefined,
                children: [
                    {
                        id: 3,
                        dicParentId: 1,
                        children: []
                    }
                ]
            },
            {
                id: 2,
                dicParentId: null,
                children: []
            }
        ]);
    });

    it('should correctly identify and process orphans and top-level nodes', () => {
        const data = [
            { id: 1, dicParentId: null },
            { id: 2, dicParentId: 3 },
            { id: 4, dicParentId: 5 }
        ];
        const result = translateDataToTree(data);
        expect(result).toEqual([
            {
                id: 1,
                dicParentId: null,
                children: []
            }
        ]);
    });
});
