import { VuexModule, Module, Action, Mutation, getModule } from "vuex-module-decorators";

import store from "../index";
import { ctFetch } from "../../utils";
import { nscriptUrl } from "@/config/url/ncdn/nscript";
import { ScriptDomainItem } from "@/types/script";

@Module({ dynamic: true, store, name: "script" })
class Script extends VuexModule {
    // 获取作用域名数据
    public scriptDomainList = [] as ScriptDomainItem[];
    public scriptDomainAddList = [] as ScriptDomainItem[];

    @Mutation
    private SET_DICTIONARY_LIST(item: ScriptDomainItem[]) {
        this.scriptDomainList = item;
    }
    @Mutation SET_DICTIONARY_ADD_LIST(list: ScriptDomainItem[]) {
        this.scriptDomainAddList = list;
    }

    @Action
    public async nGetScriptDomainList(removeScriptDomain = false) {
        const rst = await ctFetch<{ result: string[] }>(nscriptUrl.ScriptDomainList, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: { removeScriptDomain },
        });
        const list = rst.result.map((item: string) => ({ label: item, value: item }));
        removeScriptDomain ? this.SET_DICTIONARY_ADD_LIST(list) : this.SET_DICTIONARY_LIST(list);
    }
}

export const nScriptModule = getModule(Script);
