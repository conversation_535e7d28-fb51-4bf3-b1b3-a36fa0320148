<template>
    <el-dialog
        class="dialog-wrap"
        :title="$t('billingDetail.dialog.title')"
        :close-on-click-modal="false"
        :visible="true"
        width="700px"
        @close="cancel(false)"
    > 
        <el-alert title="" type="info" :closable="false" show-icon style="margin-bottom: 16px">
            <template slot="title">
                <div>{{ $t("billingDetail.dialog.tip[0]") }}</div>
                <div>{{ $t("billingDetail.dialog.tip[1]") }}</div>
            </template>
        </el-alert>
        <el-form ref="form" label-position="top">
            <el-form-item v-for="(order, index) in orderList" :key="index">
                <template slot="label">
                    <span class="form-label">{{ order.title + getRegionStr(order) }}</span>
                </template>
                <el-radio-group v-model="order.billingType" :disabled="order.disabled">
                    <el-radio-button
                        v-for="(option, index) in order.resourceTypeOptions"
                        :key="index"
                        :label="option.value"
                        border
                    >
                        {{ option.label }}
                    </el-radio-button>
                </el-radio-group>
            </el-form-item>
        </el-form>
        <div v-loading="loading" slot="footer">
            <el-button class="dialog-btn" @click="cancel(false)">{{ $t("refresh.create.cancel") }} </el-button>
            <el-button type="primary" class="dialog-btn" :disabled="submitDisabled" @click="submit">{{ $t("refresh.create.confirm") }}</el-button>
        </div>
    </el-dialog>
</template>

<script lang="ts">
import { Component, Prop, Vue, Watch } from "vue-property-decorator";
import { newBillingUrl } from "@/config/url";
import { cloneDeep } from "lodash-es";
@Component({
    name: "BillingTypeDialog"
})
export default class UpdateDialog extends Vue {
    @Prop({default: () => { return {} }}) private data: any;
    @Prop({default: () => { return "CDN" }}) private type: any;
    private loading = false
    private orderList: any = []
    private originalOrderList: any = []
    private abroadList: any = []
    private mounted() {
        this.initOrderList();
    }
    private get mainProduct() {
        return this.data?.base?.find((item: any) => !item.resourceType.includes("_ABROAD"));
    }
    private get submitDisabled() {
        const billingTypeMap = this.originalOrderList.reduce((map: any, item: any) => {
            map[item.resourceId] = item.billingType;
            return map;
        }, {})
        return this.orderList.every((item: any) => {
            return billingTypeMap[item.resourceId] === item.billingType;
        });
    }
    private initOrderList() {
        const orderObj: any = {};
        const data = [...this.data?.base, ...this.data?.extensions];
        data.forEach(item => {
            if (!orderObj[item.resourceType]) {
                orderObj[item.resourceType] = {
                    billingType: item.billingType?.billingType,
                    resourceId: item.resourceId,
                    resourceType: item.resourceType
                }
            }
            // 装载海外区域字段
            if (item.overseaRegion) {
                if (Array.isArray(orderObj[item.resourceType].overseaRegion)) {
                    !orderObj[item.resourceType].overseaRegion.includes(item.overseaRegion) && orderObj[item.resourceType].overseaRegion.push(item.overseaRegion)
                } else {
                    orderObj[item.resourceType].overseaRegion = [item.overseaRegion]
                }
            }
            // 待生效计费类型覆盖当前值
            if (item.billingType?.status === 2) {
                orderObj[item.resourceType].billingType = item.billingType?.billingType;
            }
            switch(item.resourceType) {
                case "CDN_ACCE":
                case "ICDN":
                case "DOWNLOAD_ACCE":
                case "STATIC_ACCE":
                case "VOD_ACCE":
                    orderObj[item.resourceType].title = this.$t('billingDetail.dialog.label[0]')
                    orderObj[item.resourceType].resourceTypeOptions = [
                        { label: this.$t("billingDetail.billingTypeName.TRAFFIC"), value: "TRAFFIC" },
                        { label: this.$t("billingDetail.billingTypeName.DAY_PEAK"), value: "DAY_PEAK" }
                    ]
                    break
                case "CDN_ACCE_ABROAD":
                case "ICDN_ABROAD":
                case "DOWNLOAD_ACCE_ABROAD":
                case "STATIC_ACCE_ABROAD":
                case "VOD_ACCE_ABROAD":
                    orderObj[item.resourceType].title = this.$t('billingDetail.dialog.label[1]')
                    orderObj[item.resourceType].resourceTypeOptions = [
                        { label: this.$t("billingDetail.billingTypeName.TRAFFIC"), value: "TRAFFIC" },
                        { label: this.$t("billingDetail.billingTypeName.DAY_PEAK"), value: "DAY_PEAK" }
                    ]
                    break
                case "CDN_ACCE_VPL_A":
                case "ICDN_VPL_ABROAD":
                    orderObj[item.resourceType].title = this.$t('billingDetail.dialog.label[2]')
                    orderObj[item.resourceType].resourceTypeOptions = [
                        { label: this.$t("billingDetail.billingTypeName.TRAFFIC"), value: "TRAFFIC" },
                        { label: this.$t("billingDetail.billingTypeName.TP95"), value: "TP95" }
                    ]
                    break
            }
        })
        this.orderList = Object.values(orderObj)?.filter((item: any) => {
            // 非预期选项打上不可编辑标记
            if (item.resourceTypeOptions?.every((option: any) => item.billingType !== option.value)) {
                item.disabled = true
            }
            return !!item.title
        })

        this.originalOrderList = cloneDeep(this.orderList);
    }

    private getRegionStr(data: any) {
        const regions = [...(data?.overseaRegion || [])].filter(region => region !== "100")
        if (regions && regions.length) {
            return "_" + regions.map((code: any) => this.$t(`billingDetail.overseaRegionName[${code}]`)).join("、")
        } else {
            return "";
        }
    }
    private async submit() {
        const data = this.orderList
            .filter((item: any) => {
                return this.originalOrderList.some((itm: any) => {
                    return itm.resourceId === item.resourceId && itm.billingType !== item.billingType
                });
            })
            .map((data: any) => {
                const res: any = {
                    resourceId: data.resourceId,
                    resourceType: data.resourceType,
                    billingType: data.billingType,
                }
                data.overseaRegion && (res.overseaRegion = data.overseaRegion);
                return res;
            })
        // 存在全站加速主产品需要补充websocket加速、上传加速
        const icdnOder = data.find((order: any) => order.resourceType === "ICDN")
        if (icdnOder) {
            data.push(
                {
                    resourceId: icdnOder.resourceId,
                    resourceType: "ICDN_WEBSOCKET",
                    billingType: icdnOder.billingType,
                },
                {
                    resourceId: icdnOder.resourceId,
                    resourceType: "ICDN_UPLOAD",
                    billingType: icdnOder.billingType,
                }
            )
        }
        // 海外区域分散成单个产品
        const overseaOderIndex = data.findIndex((order: any) => Array.isArray(order.overseaRegion))
        if (overseaOderIndex !== -1) {
            data[overseaOderIndex].overseaRegion.forEach((region: any) => {
                data.push({
                    resourceId: data[overseaOderIndex].resourceId,
                    resourceType: data[overseaOderIndex].resourceType,
                    billingType: data[overseaOderIndex].billingType,
                    overseaRegion: region
                })
            })
            data.splice(overseaOderIndex, 1);
        }
        try {
            this.loading = true;
            await this.$ctFetch(this.type === "CDN" ? newBillingUrl.cdnBillingChange : newBillingUrl.icdnBillingChange, {
                method: "POST",
                body: {
                    resourceId: this.mainProduct.resourceId,
                    list: data
                }
            })
            this.cancel(true)
        } catch (e) {
            this.$errorHandler(e);
        } finally {
            this.loading = false;
        }
    }
    private cancel(refresh: boolean) {
        this.$emit("cancel", refresh);
    }
}
</script>

<style lang="scss" scoped>
.dialog-wrap {
    .el-form-item {
        margin-bottom: 21px;
    }
    .form-label {
        font-size: 14px;
        color: #333333;
        line-height: 22px;
        font-weight: 600;
    }
    ::v-deep .el-radio-group {
        .el-radio-button__inner {
            width: 200px;
        }
        .is-active .el-radio-button__inner {
            background-color: var(--color-master-bg-light-2);
            color: var(--color-master);
        }
    }
}

</style>
