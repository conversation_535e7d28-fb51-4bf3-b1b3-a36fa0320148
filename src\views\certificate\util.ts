import i18n from "@/i18n";

export type certParam = {
    certName: string;
    certs?: string;
    key?: string;
    algorithm_type?: number;
    certs_sign?: string;
    key_sign?: string;
};

export type domainParam = { domain: string };

export const getDefaultCertParam = (): certParam => {
    return {
        certName: "",
        certs: "",
        key: "",
        algorithm_type: 0,
        certs_sign: "",
        key_sign: "",
    };
};

export const certsRules = (isSM2: boolean) => {
    return {
        certs: [
            {
                required: true,
                message: isSM2
                    ? i18n.t("certificate.请输入PEM格式的加密证书公钥内容")
                    : i18n.t("certificate.update.label2Tip1"),
                trigger: "blur",
            },
            {
                max: 65535,
                message: i18n.t("certificate.update.label2Tip2"),
                trigger: ["blur", "change"],
            },
        ],
        key: [
            {
                required: true,
                message: isSM2
                    ? i18n.t("certificate.请输入PEM格式的加密证书私钥内容")
                    : i18n.t("certificate.update.label3Tip1"),
                trigger: "blur",
            },
            {
                max: 65535,
                message: i18n.t("certificate.update.label2Tip2"),
                trigger: ["blur", "change"],
            },
        ],
        certs_sign: [
            {
                required: true,
                message: i18n.t("certificate.请输入PEM格式的签名证书公钥内容"),
                trigger: "blur",
            },
            {
                max: 65535,
                message: i18n.t("certificate.update.label2Tip2"),
                trigger: ["blur", "change"],
            },
        ],
        key_sign: [
            {
                required: true,
                message: i18n.t("certificate.请输入PEM格式的签名证书私钥内容"),
                trigger: "blur",
            },
            {
                max: 65535,
                message: i18n.t("certificate.update.label2Tip2"),
                trigger: ["blur", "change"],
            },
        ],
    }
}
