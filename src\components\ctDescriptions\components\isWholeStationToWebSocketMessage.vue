<template>
    <div>
        <span v-if="isVip || isCtyun">
            <span>{{ i18n.t("domain.editPage.tip37") }}</span>
            <a class="aocdn-ignore-link" @click="$docHelp(webSocketAccelerationLink)">{{ i18n.t("domain.editPage.tip38") }}</a>
            <span>{{ i18n.t("domain.editPage.tip35") }}</span>
        </span>
        <span v-else>{{ i18n.t("domain.editPage.tip39") }}</span>
    </div>
</template>

<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import { nUserModule } from "@/store/modules/nuser";
import i18n from "@/i18n";
@Component({
    name: "isWholeStationToWebSocketMessage",
})
export default class extends Vue {
    private i18n = i18n;
    get isVip() {
        return nUserModule.isVip;
    }
    get isCtyun() {
        return nUserModule.isCtyun;
    }
     get webSocketAccelerationLink () {
        return "https://www.ctyun.cn/document/10006847/10094555"
    }
}
</script>

<style>

</style>