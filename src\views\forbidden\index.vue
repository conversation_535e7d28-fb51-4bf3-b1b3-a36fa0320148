<template>
    <div class="no-service-box">
        <div class="no-service-text" v-html="$t(tip, { cdnlink: cdnlink, icdnlink: icdnlink })"></div>
    </div>
</template>

<script lang="ts">
import { nUserModule } from "@/store/modules/nuser";
import { Component, Vue, Watch } from "vue-property-decorator";
import { getLang } from "@/utils";
import { MenuUrl, CtiamMenuUrl, basicPackage } from "@/config/url";
import { addModalInstanceListener, removeModalInstanceListener } from "@/utils/ctFetch/errorHandler";

@Component
export default class OrderDetail extends Vue {
    // 用于存储最后一次路由变化的时间戳
    private lastRouteChangeTime = 0;
    // 路由切换后的保护时间（毫秒）
    private readonly ROUTE_PROTECTION_TIME = 500;

    created() {
        // 记录初始路由变化时间
        this.lastRouteChangeTime = Date.now();
        // 添加错误弹窗实例监听器
        addModalInstanceListener(this.handleErrorModal);
    }

    beforeDestroy() {
        // 移除错误弹窗实例监听器
        removeModalInstanceListener(this.handleErrorModal);
    }

    get isEn() {
        return getLang() === "en";
    }

    get type() {
        return (this.$route.query.type as string) || "default";
    }

    get isCtclouds() {
        return nUserModule.isCtclouds;
    }

    get userInfo() {
        return nUserModule.userInfo;
    }

    get tip() {
        const tips: {
            [type: string]: string;
        } = {
            service: "common.forbidden.service",
            auth: "common.forbidden.auth",
            default: "common.forbidden.default",
            offline: "common.forbidden.offline",
        };
        // TODO: 联调时确认后端是否有返回 saleChannel 字段，否则会影响链接显示
        // userInfo.saleChannel === "2"  线上客户；没有该字段则认为是线下
        if (this.type === "service" && this.userInfo.saleChannel !== "2") {
            return tips["offline"];
        }

        return tips[this.type] || tips["default"];
    }

    get cdnlink() {
        const link = this.isCtclouds
            ? nUserModule.lang === "zh"
                ? "https://www.esurfingcloud.com/products/zh-cn/10015926"
                : "https://www.esurfingcloud.com/products/10015926"
            : "https://www.ctyun.cn/products/cdnjs";
        return link;
    }

    get icdnlink() {
        const link = this.isCtclouds
            ? nUserModule.lang === "zh"
                ? "https://www.esurfingcloud.com/products/zh-cn/10006828"
                : "https://www.esurfingcloud.com/products/10006828"
            : "https://www.ctyun.cn/products/icdn";
        return link;
    }

    @Watch("$route")
    private onRouteChange() {
        // 更新最后一次路由变化时间
        this.lastRouteChangeTime = Date.now();
    }

    /**
     * 处理错误弹窗的私有方法
     * 
     * 此方法用于决定何时何地应该显示错误弹窗，基于当前的错误信息和路由变化时间
     * 它还负责在某些条件下关闭错误弹窗
     * 
     * @param instance Vue实例，用于访问实例的属性和方法
     */
    private handleErrorModal(instance: Vue) {
        // 如果当前实例没有错误项，则不执行任何操作
        if (!(instance as any).currentErrItem) {
            return;
        }

        // 使用 nextTick 等待计算属性更新
        this.$nextTick(() => {
            // 确保实例还存在
            if (!instance || (instance as any)._isDestroyed) {
                return;
            }

            const errorItem = (instance as any).currentErrItem;
            console.log("errorItem:", errorItem);

            // 检查是否在路由保护时间内
            const timeSinceLastRouteChange = Date.now() - this.lastRouteChangeTime;
            if (timeSinceLastRouteChange < this.ROUTE_PROTECTION_TIME) {
                console.log("在路由保护时间内，暂不处理弹窗", {
                    timeSinceLastRouteChange,
                    protectionTime: this.ROUTE_PROTECTION_TIME
                });
                // 在保护时间结束后重试
                setTimeout(() => {
                    this.handleErrorModal(instance);
                }, this.ROUTE_PROTECTION_TIME - timeSinceLastRouteChange);
                return;
            }

            // 检查是否包含这三个接口的URL
            const menuUrl = nUserModule.isFcdnCtyunCtclouds ? CtiamMenuUrl : MenuUrl;
            const keepUrls = [menuUrl, basicPackage];

            // 从错误信息中获取 URL
            const errorUrl = errorItem.url || "";

            // 判断是否需要保留
            const shouldKeep = keepUrls.some(url => errorUrl.includes(url));

            // 记录处理结果
            const result = {
                errorUrl,
                keepUrls,
                shouldKeep,
                type: this.type,
                isCtclouds: this.isCtclouds,
                currentRoute: this.$route.path,
                timeSinceLastRouteChange
            };

            // 如果不是需要保留的弹窗，则关闭
            if (!shouldKeep && (instance as any).close) {
                console.log("关闭错误弹窗，处理结果：", result);
                try {
                    (instance as any).close();
                } catch (error) {
                    console.error("关闭弹窗时发生错误：", error);
                }
            } else {
                console.log("保留错误弹窗，处理结果：", result);
            }
        });
    }
}
</script>

<style lang="scss" scoped>
.no-service-box {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;

    .no-service-text {
        height: 100px;
        display: flex;
        justify-content: center;
        align-items: center;
        background: #fff;
        width: 80%;
        font-size: 18px;
        font-weight: 600;
        padding: 0 20px;
    }
}
</style>
