import { VuexModule, Module, Action, Mutation, getModule } from "vuex-module-decorators";

import store from "../index";
import { ctFetch } from "../../utils";
import { BasicUrl } from "../../config/url/basic";
import { errorHandler } from "../../utils/ctFetch/errorHandler";

// 0 未订购 1 已订购 2 已过期
type EdgeFunctionStatus = 0 | 1 | 2;

@Module({ dynamic: true, store, name: "microApp" })
class Script extends VuexModule {
    // 是否配置边缘函数
    public edgeFunctionAccount = false;
    // 是否订购边缘函数
    public edgeFunctionStatus: EdgeFunctionStatus = 0;
    // 是否显示边缘函数菜单
    public showEdgeFunctionMenu = false;
    public microAppLoading = false;
    // 当前需要显示成引导页的菜单path
    public edgeMicroRoutesUseSkeleton: string[] = [];
    public edgeOrderUrl = {
        cdnUrl: "",
        icdnUrl: "",
    };

    @Mutation
    private SET_EDGE_FUNCTION_ACCOUNT(val: boolean) {
        this.edgeFunctionAccount = val;
    }

    @Mutation
    private SET_EDGE_FUNCTION_STATUS(val: EdgeFunctionStatus) {
        this.edgeFunctionStatus = val;
    }

    @Mutation
    private SET_SHOW_EDGE_FUNCTION_MENU(val: boolean) {
        this.showEdgeFunctionMenu = this.showEdgeFunctionMenu || val;
    }

    @Mutation
    private SET_CDN_URL(urls: { cdnUrl: string; icdnUrl: string }) {
        this.edgeOrderUrl = urls;
    }

    /**
     * 根据给定的状态设置使用骨架屏的边缘微应用路由的 path。
     *
     * @param {EdgeFunctionStatus} status - 用于确定设置哪些路由的状态。
     *   - 1: 设置为空数组，表示当前为已启用，所有菜单均能访问。
     *   - 2: 设置路由包含 "/udfMicroApp/funcManage"。
     *   - 0/其他: 设置路由包含 "/udfMicroApp/overview" 和 "/udfMicroApp/funcManage"。
     */
    @Mutation SET_EDGE_MICRO_ROUTES_USE_SKELETON(status: EdgeFunctionStatus) {
        let routes: string[] = [];
        if (status === 1) {
            routes = [];
        } else if (status === 2) {
            routes = ["/udfMicroApp/funcManage"];
        } else {
            routes = ["/udfMicroApp/overview", "/udfMicroApp/funcManage"];
        }
        this.edgeMicroRoutesUseSkeleton = routes;
    }

    /**
     * 同步微应用加载状态
     * @param loading
     * @constructor
     */
    @Mutation
    SET_MICRO_APP_LOADING(loading: boolean) {
        this.microAppLoading = loading;
    }

    @Action
    public checkeEdgeFunctionAccount() {
        return ctFetch(BasicUrl.getConfig, { cache: true })
            .then((data: any) => {
                this.SET_EDGE_FUNCTION_ACCOUNT(!!data.edgeFunctionAccount);
                this.SET_SHOW_EDGE_FUNCTION_MENU(this.edgeFunctionAccount);
            })
            .catch(e => errorHandler(e));
    }

    @Action
    public checkeeEdgeFunctionStatus() {
        return ctFetch<{
            edgeFunctionStatus: EdgeFunctionStatus;
            cdnUrl: string;
            icdnUrl: string;
        }>(BasicUrl.checkEdgeFunction, { cache: true })
            .then(data => {
                this.SET_EDGE_FUNCTION_STATUS(data.edgeFunctionStatus); // microApp.edgeFunctionStatus 没有使用到
                this.SET_EDGE_MICRO_ROUTES_USE_SKELETON(data.edgeFunctionStatus);
                this.SET_CDN_URL({ cdnUrl: data.cdnUrl, icdnUrl: data.icdnUrl });
            })
            .catch(e => errorHandler(e));
    }
}

export const MicroAppModule = getModule(Script);
