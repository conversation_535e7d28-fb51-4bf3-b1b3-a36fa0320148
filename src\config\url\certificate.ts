import { PROXY_PREFIX } from "./_PREFIX";

export const CertificateUrl = {
    createCert: PROXY_PREFIX + "/v1/cert/create",
    deleteCert: PROXY_PREFIX + "/v1/cert/delete",
    updateCert: PROXY_PREFIX + "/v1/cert/update",
    certList: PROXY_PREFIX + "/v1/cert/list",
    certDeployList: PROXY_PREFIX + "/v1/cert/deploy/list",
    certDetail: PROXY_PREFIX + "/v1/cert/GetCert",
    listDomainByCert: PROXY_PREFIX + "/v1/cert/domain/list",

    batchAssociate: PROXY_PREFIX + "/v1/cert/domain/batchAssociate",
    listOption: PROXY_PREFIX + "/v1/cert/domain/list/option",
    chains: PROXY_PREFIX + "/v1/cert/verify/chains",

    certCheckDomain: PROXY_PREFIX + "/v1/cert/update/check/domain", // 证书更新授权域名校验接口
    getCertById: PROXY_PREFIX + "/v1/cert/getByCertId",
    updateAndDeploy: PROXY_PREFIX + "/v1/cert/updateAndDeploy", // 更新接口update 和 部署接口deploys 合二为一

    certDeployHistory: PROXY_PREFIX + "/v1/cert/deployHistory", // 证书部署历史
    certHistory: PROXY_PREFIX + "/v1/cert/history", // 证书历史版本查询
};
