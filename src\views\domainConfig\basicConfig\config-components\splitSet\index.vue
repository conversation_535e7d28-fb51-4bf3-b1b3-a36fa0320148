<template>
    <div>
        <el-form :model="form" ref="splitSetRefForm" label-width="141px" :disabled="disabled">
            <!-- 分片回源 -->
            <el-form-item prop="split_set" required>
                <span slot="label">
                    {{ $t("domain.editPage.split.tip1") }}
                    <span>
                        <el-tooltip placement="right">
                            <div slot="content">
                                <i18n path="domain.editPage.split.tip5"> </i18n>
                                <i18n v-if="!isCtclouds" path="domain.editPage.split.tip6">
                                    <a
                                        class="aocdn-ignore-link"
                                        @click="$docHelp(learnMoreLink)"
                                        >{{ $t("domain.editPage.split.tip6-1") }}</a
                                    >
                                </i18n>
                                <i18n tag="div" path="domain.editPage.split.tip7"></i18n>
                                <i18n tag="div" path="domain.editPage.split.tip8"></i18n>
                            </div>
                            <ct-svg-icon
                                icon-class="question-circle"
                                class-name="ct-sort-drag-icon"
                            ></ct-svg-icon>
                        </el-tooltip>
                    </span>
                </span>
                <el-radio-group v-model="form.split_set" @change="handleSplitSetChange">
                    <el-radio label="off">{{ $t("domain.editPage.split.tip2") }}</el-radio>
                    <el-radio label="on">{{ $t("domain.editPage.split.tip3") }}</el-radio>
                    <el-radio label="adjust">{{ $t("domain.editPage.split.tip4") }}</el-radio>
                </el-radio-group>
            </el-form-item>
            <div v-if="form.split_set !== 'off'" class="switch-wrapper">
                <common-condition v-model="form" :list="[]" :required="false"></common-condition>
            </div>
        </el-form>
    </div>
</template>

<script>
import commonCondition from "@/components/commonCondition/index.vue";
import ctSvgIcon from "@/components/ctSvgIcon/index.vue";
import { nUserModule } from "@/store/modules/nuser";
import urlTransformer from '@/utils/logic/url';

/**
 * 获取默认的分片回源配置
 *
 * @returns {Object} 默认的分割设置对象，包括 split_set、mode 和 content 属性
 */
const getDefaultSplitOrigin = () => ({
    split_set: "off",
    mode: null,
    content: "",
});

export default {
    name: "SplitOriginCmp",
    components: { commonCondition, ctSvgIcon },
    props: {
        value: {
            type: Object,
            default: getDefaultSplitOrigin(),
        },
        disabled: Boolean,
    },
    data() {
        return {
            form: getDefaultSplitOrigin(),
        };
    },
    computed: {
        isCtclouds() {
            return nUserModule.isCtclouds;
        },
        learnMoreLink() {
            return urlTransformer({
                a1Ctyun: "https://www.ctyun.cn/document/10065985/10847695",
                fcdnCtyun: "https://www.ctyun.cn/document/10015932/10639835"
            })
        },
    },
    watch: {
        value: {
            handler(val) {
                if (!val) return;
                this.form = val;
            },
            immediate: true,
        },
        form(val) {
            this.$emit("input", val);
        },
    },
    methods: {
        /**
         * 当分片回源设置的值为"off"时，重置condition。
         *
         * @param {string} val - 分片回源设置的值
         */
        handleSplitSetChange(val) {
            if (val === "off") {
                this.form.mode = null;
                this.form.content = "";
            }
        },
    },
};
</script>
<style lang="scss" scoped>
.input-box {
    width: 395px;
}
.ct-sort-drag-icon {
    font-size: 14px;
}
</style>
