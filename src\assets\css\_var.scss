// @author: <PERSON><PERSON><PERSON><PERSON>;
// @description: 变量、常量设置

// 大屏，适应宽屏PC screen >= 1200
$g-media-lg: 1200px;
// 中屏，适应PC screen >= 992px
$g-media-md: 992px;
// 小屏，适应各种pad，screen >= 768px
$g-media-sm: 768px;
// 移动端，适应 手机  screen <= 767px
$g-media-xs: 767px;


$g-header-height: 0px !default;


// 颜色区域: $g-color-颜色-修饰词（light,normal,strong）-用处/场景;
// light,lighter,lightest
// strong, stronger, strongest
// 页面黄色
$g-color-yellow: #FA8334 !default;
// hover页面黄色
$g-color-yellow-light: #EA782B !default;
// 新标准下的3中文本颜色 2020-02-28
$g-color-black-head: #292b32 !default; // 所有大标题、表单内容、面包屑、输入后文字
$g-color-black-title: #595c65 !default; // 所有表头、表单 lable
$g-color-gray-note: #8a8c93 !default; // 所有说明性内容
// 页面黑色
$g-color-black: #000 !default;
// 白色
$g-color-white: #FFF !default;
// 轻调色，灰色
$g-color-gray: #8D9296 !default;
// 背景灰色
$g-color-gray-bg: #F8F9F9 !default;
// 线条灰色
$g-color-gray-line: #E4E7ED !default;
// 正常色
$g-color-normal-font: #5D646F !default;
// 着重色
$g-color-strong-font: #2F2F2F !default;
// 绿色，代表成功
$g-color-green: #67C23A;
// 蓝色
$g-color-blue:#409eff;
// 黄色-level
$g-color-yellow-level: #f1bb12;
// 红色
$g-color-red:#f56c6c;

// =================================

// 小一号字体（注释类）
$g-fs-small: 12px !default;
// 一般字体（正文）
$g-fs-normal: 14px !default;
// 小标题字体（二级标题）
$g-fs-strong: 16px !default;
// 大标题字体（一级标题）
$g-fs-large: 21px !default;

// 小一号字体行高
$g-lh-small: 20px !default;
// 一般字体行高
$g-lh-normal: 24px !default;
// 小标题字体行高
$g-lh-strong: 26px !default;
// 大标题字体行高
$g-lh-large: 32px !default;

// 默认行高数字
$g-lh-normal-num: 1.8 !default;
// 着重行高数字
$g-lh-strong-num: 2 !default;
// 大的行高数字
$g-lh-large-num: 2.5 !default;

// 字重
$g-font-weight: 600 !default;

// =================================

// 业务变量
// 页面背景色
$g-page-bg-color: #F2F2F2;

// 内容区的主边距
$section-padding: 15px;

$cute-icons-2: "cute-icons-2";
