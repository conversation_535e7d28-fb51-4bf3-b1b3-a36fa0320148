<template>
    <div class="websocket-wrapper">
        <el-form
            ref="websocketForm"
            label-width="140px"
            :model="form"
            :rules="rules"
            :disabled="!isEdit || !isService || isLockWebSocket"
        >
            <div v-if="webSocketEnable && isShowWebsocket">
                <el-form-item v-if="isPoweredByQiankun" label="websocket" class="dynamic-box" prop="websocket_speed" :rules="rules.websocket_speed">
                    <el-switch
                        v-model="form.websocket_speed"
                        :active-value="1"
                        :inactive-value="0"
                        @change="onSwitch"
                        :disabled="!isPoweredByQiankun"
                    ></el-switch>
                    <span class="tips">{{ $t("domain.detail.placeholder37") }}</span
                    ><a v-if="show_url" :underline="false" class="word-wrap aocdn-ignore-link" style="color:#3d73f5" @click="$docHelp(websocket_url)">{{
                        $t("domain.detail.placeholder38")
                    }}</a>
                </el-form-item>
                <div v-if="form.websocket_speed" class="switch-wrapper">
                    <el-form-item
                        prop="backup_origin_timeout"
                        :rules="rules.backup_origin_timeout"
                        class="out-chain-item-wrapper"
                    >
                        <span slot="label">
                            {{ $t("domain.editPage.label6") }}
                            <span>
                                <el-tooltip placement="top" :content="$t('domain.detail.placeholder39')">
                                    >
                                    <ct-svg-icon
                                        icon-class="question-circle"
                                        class-name="ct-sort-drag-icon"
                                    ></ct-svg-icon>
                                </el-tooltip>
                            </span>
                        </span>
                        <el-input
                            v-model.number="form.backup_origin_timeout"
                            maxlength="16"
                            class="websocket-timeout-input"
                            @change="handleChange"
                        >
                            <template slot="append">{{ $t("simpleForm.alogicCacheMixin.CacheTtlMap.5") }}</template>
                        </el-input>
                    </el-form-item>
                </div>
                <!-- fcdn入口，接口返回websocket开关关闭时，提示 -->
                <el-form-item label-width="70px" v-if="!isPoweredByQiankun && !form.websocket_speed">
                    {{ $t('domain.websocketTip') }}
                </el-form-item>
            </div>
        </el-form>
    </div>
</template>

<script>
import componentMixin from "@/views/domainConfig/componentMixin";
import ctSvgIcon from "@/components/ctSvgIcon";
import { SecurityAbilityModule } from "@/store/modules/securityAbility";

export default {
    components: { ctSvgIcon },
    mixins: [componentMixin],
    props: {
        formDatas: Object,
        websocket_url: String,
        websocket_speed: Boolean,
        isLockWebSocket: Boolean,
        uploadSpeed: Number,
        webSocketEnable: Boolean,
        isShowWebsocket: Boolean,
        show_url: Boolean,
    },
    data() {
        return {
            form: {
                websocket_speed: 0, // websocket开关
                backup_origin_timeout: "",
            },
            upload_speed: 0, // 上传加速开关
            rules: {
                backup_origin_timeout: [
                    {
                        required: false,
                        validator: (rule, value, callback) => {
                            if (this.isLockWebSocket) callback();
                            const num = /^[0-9]*$/;
                            if (!value && value !== 0) {
                                callback();
                            } else if (!num.test(value) || value < 1 || value > 300) {
                                return callback(new Error(this.$t("domain.detail.placeholder40")));
                            } else callback();
                        },
                        trigger: "blur",
                    },
                ],
                websocket_speed: {
                    required: false,
                    validator: this.validateuploadSpeedORwebsocketSpeed,
                    trigger: ["blur", "change"],
                },
            },
        };
    },
    watch: {
        formDatas: {
            deep: true,
            handler(val) {
                this.form.websocket_speed = val.websocket_speed;
                this.form.backup_origin_timeout = val.websocket_time.backup_origin_timeout;
            },
            immediate: true,
        },
        uploadSpeed: {
            deep: true,
            handler(val) {
                this.upload_speed = val;
            },
            immediate: true,
        },
    },
    mounted() {
        this.$ctBus.$on("uploadSpeedChange", data => {
            this.upload_speed = data;
        });
    },
    methods: {
        onSwitch(v) {
            if (v === 0) {
                this.form.backup_origin_timeout = "";
            }

            const originalForm = SecurityAbilityModule.securityBasicConfigOriginForm;
            console.log('originalForm = ', originalForm)
            if (v && originalForm?.websocket_speed) {
                this.form.backup_origin_timeout = originalForm.websocket_time.backup_origin_timeout;
            }

            this.$emit("onChange", this.form);
        },
        handleChange() {
            this.$emit("onChange", this.form);
        },
        validateuploadSpeedORwebsocketSpeed(rule, value, callback) {
            if (this.isLockWebSocket) return callback();
            if (this.form.websocket_speed === 1 && this.upload_speed === 1) {
                return callback(new Error(this.$t("domain.detail.placeholder79")))
            } else {
                return callback();
            }
        },
    },
    beforeDestroy() {
        this.$ctBus.$off("uploadSpeedChange");
    },
};
</script>

<style lang="scss" scoped>
.dynamic-box {
    .tips {
        font-size: 12px;
        color: $color-neutral-7;
        font-weight: 400;
        margin-left: $margin-3x;
    }
}
.websocket-timeout-input {
    width: 395px;
}
</style>
