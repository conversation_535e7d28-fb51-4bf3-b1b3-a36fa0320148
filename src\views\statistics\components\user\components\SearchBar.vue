<template>
    <section class="statistics-search-bar">
        <search-bar-row fullwidth class="search-row">
            <ct-time-picker
                size="medium"
                v-model="timeRange"
                :periodOptions="['0', '1', '7', '30', '-1']"
                ref="timeRangeRef"
                :maxDayBeforeNow="365"
            />
            <div>
                <el-select
                    v-model="domain"
                    filterable
                    :loading="domainOptionsLoading"
                    :loading-text="$t('common.loading')"
                >
                    <el-option :label="$t('statistics.common.domainSelectOption')" value="all" />
                    <el-option
                        v-for="item in domainOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    />
                </el-select>
                <el-button type="primary" @click="() => beforeSearch(true)" class="ml-8">{{
                    $t("statistics.common.searchBtn")
                }}</el-button>
                <el-button class="ml-8" @click="resetFilter">{{ $t("common.search.reset") }}</el-button>
            </div>
        </search-bar-row>
    </section>
</template>

<script lang="ts">
import { Component, Vue, Watch } from "vue-property-decorator";
import { StatisticsModule } from "@/store/modules/statistics";
import { DomainModule } from "@/store/modules/domain";
import { DomainActionEnum, ProductCodeEnum } from "@/config/map";
import { SearchParams } from "@/types/statistics/user";
import SearchBarRow from "../../searchBarRow.vue";
import { ProductModule } from "@/store/modules/ncdn/nproduct";
import CtTimePicker from "@/components/ctTimePicker/index.vue";
import { cloneDeep } from "lodash-es";
import { nUserModule } from "@/store/modules/nuser";

function getTodayTimestampArray() {
    const date = new Date();

    // 获取今天0点的时间戳
    date.setHours(0, 0, 0, 0);
    const start = new Date(date);

    // 获取明天0点的时间戳
    date.setDate(date.getDate() + 1);
    const end = new Date(+date - 1);

    return [start, end];
}

@Component({
    components: { SearchBarRow, CtTimePicker },
})
export default class UserSearchBar extends Vue {
    domain = "all"; // 默认全部域名
    timeRange: null | Date[] = getTodayTimestampArray(); // 时间
    isDcdn = false;
    initialized = false; // 是否已经初始化

    get productOptions() {
        const options = ProductModule.allProductOptions;
        if (window.__POWERED_BY_QIANKUN__) {
            return StatisticsModule.allProductOptions.map(item => {
                return {
                    label: item.product_cname,
                    value: item.product_code,
                };
            });
        }

        const list = options.filter(opt => {
            if (!opt.label || !opt.value) return false;

            const isDcdnOptions =
                opt.value === ProductCodeEnum.Upload ||
                opt.value === ProductCodeEnum.Whole ||
                opt.value === ProductCodeEnum.Socket;
            // 筛选全站加速和普通用量分析的产品下拉选择
            if (this.isDcdn) return isDcdnOptions;
            else return !isDcdnOptions;
        });
        return list;
    }

    get domainAction() {
        if (nUserModule.isFcdnCtyunCtclouds) {
            return this.isDcdn ? DomainActionEnum.UserWhole : DomainActionEnum.User;
        }
        return DomainActionEnum.Data;
    }

    get domainList() {
        return DomainModule[this.domainAction].nativeList.filter(domain => {
            return this.productOptions.some(product => product.value === domain.productCode);
        });
    }

    get domainOptionsLoading() {
        return DomainModule[this.domainAction].loading;
    }

    // 全部的域名列表
    get domainOptions() {
        return this.domainList.map(item => ({
            label: item.label,
            value: item.domain,
        }));
    }

    get childAccount() {
        return StatisticsModule.childAccount;
    }

    get domainCountLimit() {
        return StatisticsModule.domainCountLimit;
    }

    @Watch("domainOptions", { immediate: true })
    onDomainParamsChange(newArr: [], oldArr: []) {
        // 首次进入则进行请求
        if (
            (newArr.length !== 0 && (!oldArr || oldArr.length === 0)) ||
            (this.domainOptions || []).map(d => d.value).length > 0
        ) {
            if (this.initialized === true) {
                return;
            }
            this.$nextTick(this.beforeSearch);
            this.initialized = true;
            this.$emit("initialized");
        }
    }

    @Watch("$route", { immediate: true })
    onRouteChange() {
        // 判断普通用量分析还是全站加速用量分析
        this.isDcdn = this.$route.name === "statistics.dcdn";
    }

    mounted() {
        StatisticsModule.SET_CURRENT_ACTION(this.domainAction);
        if (nUserModule.isFcdnCtyunCtclouds && !DomainModule[this.domainAction].nativeList.length) {
            DomainModule.GetDomainList({ action: this.domainAction, shouldCheckCache: true });
        }
    }

    async beforeSearch(triggerByUser = false) {
        const params = cloneDeep(this.searchParams);

        if (params.startTime === 0) {
            this.$message.error(`${this.$t("common.searchBar.errMsg4")}`);
            return;
        }
        // 无可选域名时，拦截请求
        if (this.domainList.length === 0) {
            this.$message.error(`${this.$t("statistics.common.chart.errMsg[0]")}`);
            return;
        }
        // 未做选择时域名总数超过100，触发查询需要提示
        if (this.childAccount && this.domain === "all" && this.domainOptions.length > this.domainCountLimit) {
            this.$message.error(`${this.$t("common.searchBar.errMsg5", { count: this.domainCountLimit })}`);
            return;
        }

        // 查询前先去获取scale
        const scaleDomainList = params.domainList?.length
            ? params.domainList
            : this.domainOptions.map(item => item.value);

        // 不传递域名参数domainList时，需要传递product
        if (!(params.product && params.product.length > 0)) {
            const isFcdnCtyunCtclouds = nUserModule.isFcdnCtyunCtclouds;
            if (!(params.domainList.length > 0)) {
                params.product = this.productOptions.map(item => {
                    return item.value;
                });
            } else if (isFcdnCtyunCtclouds) {
                // fcdn ctyun（国内+国际） 必传 product
                params.product = this.domainList
                    .filter(itm => params.domainList?.includes(itm.domain))
                    .map(itm => itm.productCode);
                params.product = [...new Set(params.product)];
            }
        }

        this.$emit("search", {
            searchParams1: params,
            triggerByUser,
            scaleDomainList,
        });
    }

    // 请求参数
    get searchParams() {
        const { domain, timeRange, domainOptions } = this;

        const params: SearchParams = {
            domainList: [],
            startTime: 0,
            endTime: 0,
            product: [],
        };

        const domainValueArr = domainOptions.map(item => item.value);
        if (domain === "all") {
            // 子账号默认传递域名选项，否则会导致越权
            if (this.childAccount) {
                params.domainList = domainValueArr;
            } else {
                params.domainList = [];
            }
        } else {
            // 若用户选择了域名，则传递选择的域名
            params.domainList = [domain];
        }

        if (timeRange) {
            const [startTime, endTime] = timeRange;
            params.startTime = Math.floor(+startTime / 1000);
            params.endTime = Math.floor(+endTime / 1000);
        }

        return params;
    }

    resetFilter() {
        this.domain = "all";
        this.$refs.timeRangeRef && ((this.$refs.timeRangeRef as any).currentPeriodLocal = "0");

        this.beforeSearch(true);
    }
}
</script>


<style lang="scss" scoped>
.statistics-search-bar {
    ::v-deep {
        .search-content {
            gap: 8px 48px;
        }
    }
}

.ml-8 {
    margin-left: 8px;
}
</style>
