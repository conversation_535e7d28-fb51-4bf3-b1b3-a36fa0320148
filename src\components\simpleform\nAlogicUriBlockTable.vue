<template>
    <div>
        <div class="radio-row">
            <el-radio-group v-model="objectModel.type">
                <el-radio :label="1">{{ $t("simpleForm.blockTable.allow") }}</el-radio>
                <el-radio :label="0">{{ $t("simpleForm.blockTable.block") }}</el-radio>
            </el-radio-group>
        </div>
        <div>
            <el-input
                v-model="objectModel.uri"
                class="textarea-content"
                type="textarea"
                :rows="5"
                :placeholder="placeholder"
            />
        </div>
    </div>
</template>

<script>
// import mixin from "../../editor.mixin";
export default {
    // mixins: [mixin],
    name: "n-alogic-uri-block-table",
    props: {
        uriData: Object,
    },
    data() {
        return {
            objectModel: {
                type: 1,
                uri: "",
            },
            maxNum: 400,
        };
    },
    computed: {
        placeholder() {
            return this.$t("domain.detail.tip41-1");
        },
        uriList() {
            // 需要过滤空表内容
            return this.objectModel.uri
                .split("\n")
                .map(i => i.trim())
                .filter(i => i);
        },
    },
    methods: {
        validateProcedure() {
            const result = {
                valid: true,
                msg: "",
                dom: "uri_type",
            };
            const { uriList, maxNum, objectModel } = this;
            if (objectModel.type === null && objectModel.uri) {
                result.valid = false;
                result.msg = this.$t("domain.detail.tip42");
            } else if (!objectModel.uri.trim()) {
                result.valid = false;
                result.msg = this.$t("domain.detail.tip49-1");
            } else if (uriList.length > maxNum) {
                result.valid = false;
                result.msg = this.$t("simpleForm.UriBlock.maxNum", { maxNum });
            }

            return Promise.resolve(result);
        },
    },
    watch: {
        objectModel: {
            handler(val) {
                if (val.uri === "" && val.type === null) {
                    this.model = "";
                } else {
                    this.model = {
                        type: val.type,
                        uri: this.uriList.join(","),
                    };
                    this.$emit("onChange", this.model);
                }
            },
            deep: true,
        },
    },
    mounted() {
        if (this.uriData) {
            this.objectModel = {
                type: this.uriData.type || 1,
                uri: this.uriData.uri.split(",").join("\n"),
            };
        }
    },
};
</script>

<style lang="scss" scoped>
.textarea-content {
    width: 70%;
}
</style>
