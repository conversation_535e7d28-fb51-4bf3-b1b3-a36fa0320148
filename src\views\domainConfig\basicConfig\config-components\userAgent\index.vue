<template>
    <div class="ct-edit-wrapper">
        <el-form
            label-width="140px"
            label-position="left"
            :model="userAgentForm"
            :rules="rules"
            ref="userAgentForm"
            :disabled="!isEdit || !isService || isLockUa"
        >
            <div v-if="!isPoweredByQiankun">
                <!-- UA黑白名单 开关 -->
                <el-form-item :label="$t('domain.create.ua')" prop="ua_switch">
                    <el-switch
                        v-model="userAgentForm.ua_switch"
                        active-value="on"
                        inactive-value="off"
                        @change="ua_switch_change"
                    ></el-switch>
                </el-form-item>

                <!-- 类型 -->
                <div v-if="userAgentForm.ua_switch === 'on'" class="switch-wrapper">
                    <!-- 忽略大小写 -->
                    <el-form-item :label="$t('domain.create.referer5')" prop="ignore_case">
                        <el-switch
                            v-model="userAgentForm.user_agent.ignore_case"
                            active-value="on"
                            inactive-value="off"
                            @change="onUaChange"
                        ></el-switch>
                    </el-form-item>
                    <!-- 匹配方式 -->
                    <el-form-item :label="$t('domain.create.matchMethod.label')" prop="mode">
                        <el-radio-group v-model="userAgentForm.user_agent.mode" @change="onUaChange">
                            <el-radio :label="1">{{ $t("domain.create.matchMethod.option1") }}</el-radio>
                            <el-radio :label="0">{{ $t("domain.create.matchMethod.option2") }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item :label="$t('domain.type')" prop="user_agent.ua" :rules="rules.ua">
                        <div>
                            <div class="radio-row">
                                <el-radio-group v-model="userAgentForm.user_agent.type" @change="onUaChange">
                                    <el-radio :label="1">{{ $t("domain.detail.trustlist") }}</el-radio>
                                    <el-radio :label="0">{{ $t("domain.detail.blocklist") }}</el-radio>
                                </el-radio-group>
                            </div>
                            <div>
                                <el-input
                                    class="textarea-wrapper"
                                    v-model="userAgentForm.user_agent.ua"
                                    type="textarea"
                                    :rows="7"
                                    :placeholder="uaPlaceholder"
                                    @change="onUaChange"
                                />
                            </div>
                        </div>
                    </el-form-item>
                </div>
            </div>
        </el-form>
    </div>
</template>

<script>
import componentMixin from "@/views/domainConfig/componentMixin";
import { cloneDeep } from "lodash-es";
import { SecurityAbilityModule } from "@/store/modules/securityAbility";

const defaultUaValue = {
    type: 1,
    ua: "",
    ignore_case: "on",
    mode: 1,
};

export default {
    name: "userAgent",
    components: {},
    mixins: [componentMixin],
    props: {
        datas: Object,
        isLockUa: Boolean,
    },
    data() {
        return {
            userAgentForm: {
                ua_switch: "off",
                user_agent: cloneDeep(defaultUaValue),
            },
            maxNum: 400,

            rules: {
                ua: [{ required: false, validator: this.valid_ua, trigger: "blur" }],
            },
        };
    },
    computed: {
        uaPlaceholder() {
            return this.$t("domain.detail.tip41");
        },
        uaList() {
            return this.userAgentForm.user_agent?.ua
                ?.split("\n")
                .map(i => i?.trim())
                .filter(i => i);
        },
    },
    watch: {
        datas: {
            deep: true,
            handler(val) {
                this.init(val);
            },
            immediate: true,
        },
    },
    methods: {
        init(v) {
            if (!v) return;
            this.userAgentForm.ua_switch = v?.ua_switch;
            this.userAgentForm.user_agent.type = v?.user_agent?.type;
            this.userAgentForm.user_agent.ua = v?.user_agent?.ua;
            this.userAgentForm.user_agent.ignore_case = v?.user_agent?.ignore_case;
            this.userAgentForm.user_agent.mode = v?.user_agent?.mode;
        },
        // UA黑白名单开关关闭重新打开，需要将类型设置为白名单
        ua_switch_change(val) {
            const originalConf = SecurityAbilityModule.securityBasicConfigOriginForm;
            this.userAgentForm = {
                ua_switch: val,
                user_agent: cloneDeep(originalConf.user_agent),
            }

            this.$emit("onChange", this.userAgentForm, this.uaList);
        },
        /**
         * UA相关字段变化
         */
        onUaChange() {
            this.$emit("onChange", this.userAgentForm, this.uaList);
        },
        valid_ua(rule, value, callback) {
            const { uaList, maxNum, userAgentForm } = this;

            if (userAgentForm.user_agent?.type === null && userAgentForm.user_agent?.ua) {
                return callback(new Error(this.$t("domain.detail.tip42")));
            } else if (!userAgentForm.user_agent?.ua?.trim()) {
                return callback(new Error(this.$t("domain.detail.tip49")));
            } else if (uaList && uaList.length > maxNum) {
                return callback(new Error(this.$t("domain.detail.tip50", { maxNum: maxNum })));
            } else {
                return callback();
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.ct-edit-wrapper {
    ::v-deep {
        .el-form--label-left .el-form-item__label {
            text-align: right;
        }
    }
}
.textarea-wrapper {
    width: calc(100% - 120px);
}
</style>
