<template>
    <div class="chart-wrap">
        <div class="title-wrap">
            <div class="operate-btn">
                <el-radio-group v-model="chartType" size="small" @change="changeCode">
                    <el-radio-button :label="$t('statistics.usageQuery.request.radioBtn1')"></el-radio-button>
                    <el-radio-button label="QPS"></el-radio-button>
                    <el-radio-button :label="$t('statistics.usageQuery.request.radioBtn2')"></el-radio-button>
                </el-radio-group>
            </div>
            <div class="total">
                <div class="tip" v-if="showHit.request || showHit.miss">
                    {{
                        showHit.request
                            ? `${$t("statistics.usageQuery.request.totalTip1")}`
                            : `${$t("statistics.usageQuery.request.totalTip2")}`
                    }}：
                    <span class="num">{{
                        fetchData[dataKeys.total]
                            | convertTenThousand2Int(
                                `${$t("statistics.usageQuery.request.totalTipUnit", { value: "" })}`
                            )
                    }}</span>
                </div>
                <template v-if="showHit.request">
                    <div class="tip">
                        {{
                            showHit.request
                                ? `${$t("statistics.usageQuery.request.totalTip3")}`
                                : `${$t("statistics.usageQuery.request.totalTip4")}`
                        }}：
                        <span class="num">{{
                            fetchData[dataKeys.max]
                                | convertTenThousand2Int(
                                    `${$t("statistics.usageQuery.request.totalTipUnit", { value: "" })}`
                                )
                        }}</span>
                        <span class="date">{{ fetchData.maxQueryTimestamp | timeFormat }}</span>
                    </div>
                    <div class="tip">
                        {{
                            showHit.request
                                ? `${$t("statistics.usageQuery.request.totalTip5")}`
                                : `${$t("statistics.usageQuery.request.totalTip6")}`
                        }}：
                        <span class="num">{{
                            fetchData[dataKeys.min]
                                | convertTenThousand2Int(
                                    `${$t("statistics.usageQuery.request.totalTipUnit", { value: "" })}`
                                )
                        }}</span>
                        <span class="date">{{ fetchData.minQueryTimestamp | timeFormat }}</span>
                    </div>
                </template>
            </div>
        </div>

        <v-chart
            class="chart"
            v-loading="loading"
            :element-loading-text="$t('statistics.common.chart.loading')"
            :empty-text="$t('common.table.empty')"
            autoresize
            theme="cdn"
            :options="options"
        />

        <ct-tip>
            {{ $t("statistics.usageQuery.request.ctTip") }}
            <el-tooltip
                effect="dark"
                :content="$t('statistics.common.searchDownloadContent')"
                placement="right"
            >
                <i class="el-icon-download usage-download-icon" @click="downloadTable"></i>
            </el-tooltip>
        </ct-tip>
        <el-table
            :data="fetchData.daily"
            stripe
            v-loading="loading"
            :element-loading-text="$t('statistics.common.table.loading')"
            show-summary
            :summary-method="getSummaries"
        >
            <el-table-column
                :label="$t('statistics.usageQuery.request.tableColumn1')"
                type="index"
                width="100"
            />
            <el-table-column :label="$t('statistics.usageQuery.request.tableColumn2')">
                <template slot-scope="{ row }">
                    {{ row.date }}
                </template>
            </el-table-column>
            <el-table-column
                :label="
                    isQueryOrQPS
                        ? `${$t('statistics.usageQuery.request.tableColumn3')}`
                        : `${$t('statistics.usageQuery.request.tableColumn4')}`
                "
            >
                <template slot-scope="{ row }">
                    {{ isQueryOrQPS ? row.query : row.miss }}
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<script lang="ts">
import { Component } from "vue-property-decorator";
import { mixins } from "vue-class-component";
import { StatisticsUsageUrl } from "@/config/url/statistics";
import { timeFormat } from "@/filters/index";
import { Request5Min, SearchParams } from "@/types/statistics/usage";
import { RequestFetchData } from "@/types/statistics/usage";
import { THEME_AREA_STYLE } from "@/config/echart/cdn-theme";
import ChartMixin from "../chartMixin";
import { cloneDeep } from "lodash-es";

type tooltipParam = { name: string; marker: string; value: string | number };

const defaultFetchData: RequestFetchData = {
    "5min": [],
    daily: [],
    totalQuery: 0,
    totalMiss: 0,
    maxQuery: 0,
    minQuery: 0,
    maxQueryTimestamp: "",
    minQueryTimestamp: "",
};

@Component({
    name: "Request",
})
export default class Request extends mixins(ChartMixin) {
    chartType = `${this.$t("statistics.usageQuery.request.chartType")}`; //
    // 接口数据
    fetchData: RequestFetchData = cloneDeep(defaultFetchData);
    // 当前的时间间隔
    currentInterval = 1;

    protected downloadDataList: RequestFetchData["5min"] = []; // 用于下载的数据

    // 当前选中的类型
    get showHit() {
        return {
            request: this.chartType === `${this.$t("statistics.usageQuery.request.chartType")}`,
            miss: this.chartType === `${this.$t("statistics.usageQuery.request.radioBtn2")}`,
            qps: this.chartType === "QPS",
        };
    }

    get isQueryOrQPS() {
        return this.showHit.request || this.showHit.qps;
    }

    // 获取 seriesData 所使用的 key
    get seriesDataKey() {
        return this.isQueryOrQPS ? "query" : "miss";
    }
    get dataKeys(): {
        total: "totalQuery" | "totalMiss";
        max: "maxQuery";
        min: "minQuery";
    } {
        return {
            total: this.isQueryOrQPS ? "totalQuery" : "totalMiss",
            max: "maxQuery",
            min: "minQuery",
        };
    }

    changeCode() {
        this.beforeGetData(this.searchParams);
    }
    private async localFetchGenerator<T>(url: string, params: SearchParams) {
        const rst = await this.fetchGenerator<T>(url, params);
        return rst;
    }
    // 1、数据请求
    protected async getData(params: SearchParams) {
        const url = this.isQueryOrQPS ? StatisticsUsageUrl.qpsList : StatisticsUsageUrl.qpsMissList;
        this.fetchData = await this.localFetchGenerator(url, {
            ...params,
        });

        if (!this.fetchData) this.fetchData = cloneDeep(defaultFetchData);

        // 处理用于下载的数据
        this.downloadDataList = this.fetchData["5min"];

        this.currentInterval = this.parseTimeInterval(this.searchParams.interval as string);
    }

    /**
     * 转换时间间隔为秒钟数
     */
    parseTimeInterval(interval: string) {
        if (!interval) return 1;
        const num = interval.slice(0, -1);
        const unit = interval.slice(-1) as "m" | "h";
        const unitMap = { m: 60, h: 3600 };
        return +num * unitMap[unit];
    }

    // 2、数据处理
    get options() {
        const { seriesDataKey } = this;
        // 获取计算的缩进单位和进制
        const [scale, unit] = this.queryUnit;

        // 根据 switch 获取差异化数据
        const title = this.showHit.request
            ? `${this.$t("statistics.usageQuery.request.chartOptions.title1")}`
            : this.showHit.qps
            ? `QPS`
            : `${this.$t("statistics.usageQuery.request.chartOptions.title2")}`;

        const xAxisData: string[] = [];
        const seriesData: number[] = [];
        const fetchDataList = this.fetchData["5min"] || [];

        fetchDataList
            .sort((a, b) => a.timestamp - b.timestamp)
            .forEach(item => {
                xAxisData.push(timeFormat(item.timestamp * 1000).replace(" ", "\n"));
                const value =
                    (item[seriesDataKey] || 0) / (this.showHit.qps ? this.currentInterval * scale : scale);
                seriesData.push(value);
            });

        const options = {
            ...this.comOptions,
            tooltip: {
                trigger: "axis",
                formatter: (a: tooltipParam[]) => {
                    const { name, marker, value } = a[0];
                    // 请求数不会出现小数，为了避免计算精度问题，要处理下计算后的小数位
                    // eg：2.3993 1.138 1.5474 1.4868
                    return `${name}<br>${marker}${title}: ${
                        this.showHit.qps
                            ? (+value * scale).toFixed(2)
                            : this.$t("statistics.usageQuery.request.totalTipUnit", {
                                  value: Math.round(+value * scale),
                              })
                    }`;
                },
            },
            xAxis: {
                type: "category", // 类目轴，从 data 中获取数据
                boundaryGap: false, // 坐标轴两边留白策略
                axisLabel: {
                    formatter: this.xAxisFormatterGenerator(),
                },
                data: xAxisData,
            },
            yAxis: {
                type: "value", // 数值轴
                name: this.showHit.qps
                    ? ""
                    : `${this.$t("statistics.usageQuery.request.chartOptions.yAxisName")}`,
                axisLabel: {
                    formatter(val: string): string | undefined {
                        return `${val}${unit}`;
                    },
                },
            },
            series: [
                {
                    type: "line",
                    data: seriesData,
                    areaStyle: THEME_AREA_STYLE["#358AE2"],
                },
            ],
        };

        return options;
    }

    // 根据数据获取缩进规则
    get queryUnit(): [number, string] {
        const data = this.getMaxFromList(
            this.showHit.qps
                ? cloneDeep(this.fetchData["5min"]).map((itm: Request5Min) => {
                      itm[this.seriesDataKey as "query"] = itm[this.seriesDataKey] / this.currentInterval;
                      return itm;
                  })
                : this.fetchData["5min"],
            this.seriesDataKey
        );
        if (+data < 10000) {
            return [1, ""];
        } else {
            return [10000, " W"];
        }
    }

    getSummaries() {
        return [
            `${this.$t("statistics.usageQuery.request.getSummaries")}`,
            "",
            this.isQueryOrQPS ? this.fetchData.totalQuery : this.fetchData.totalMiss,
        ];
    }

    // 重写 excel 数据拼接方法
    protected tableToExcel() {
        let str = `${this.$t("statistics.usageQuery.request.tableToExcel.excelColumn1")},${
            this.showHit.request
                ? `${this.$t("statistics.usageQuery.request.tableToExcel.excelColumn2")}`
                : this.showHit.qps
                ? "QPS"
                : `${this.$t("statistics.usageQuery.request.tableToExcel.excelColumn3")}`
        }\n`;

        // 输出格式
        str += this.downloadDataList.reduce((str, item) => {
            str += `${timeFormat(item["timestamp"] * 1000)},`;
            str += this.showHit.qps
                ? (item[this.seriesDataKey] / this.currentInterval).toFixed(2) + "\n"
                : (item[this.seriesDataKey] / 1).toFixed(2) + "\n";
            return str;
        }, "");

        if (this.showHit.request || this.showHit.miss) {
            //增加总请求数、请求数峰值、请求数谷值
            str += `\n${this.$t("statistics.usageQuery.request.tableToExcel.excelColumn4")}\n`;
            const { fetchData } = this;
            const { total, max, min } = this.dataKeys;
            const excelStr = {
                total: this.showHit.qps
                    ? this.$t("statistics.usageQuery.request.tableToExcel.excelColumn5")
                    : this.$t("statistics.usageQuery.request.totalTip2"),
                peak: this.showHit.qps
                    ? this.$t("statistics.usageQuery.request.tableToExcel.excelColumn6")
                    : this.$t("statistics.usageQuery.request.totalTip4"),
                valley: this.showHit.qps
                    ? this.$t("statistics.usageQuery.request.tableToExcel.excelColumn7")
                    : this.$t("statistics.usageQuery.request.totalTip6"),
            };
            str += `${excelStr.total},${fetchData[total]} \n ${excelStr.peak},${fetchData[max]} \n ${excelStr.valley},${fetchData[min]} \n`;
        }

        this.downloadExcel({
            name: this.showHit.request
                ? `${this.$t("statistics.usageQuery.request.tableToExcel.excelName")}`
                : this.showHit.qps
                ? `${this.$t("statistics.usageQuery.BandwidthFlow.tableToExcel.excelName", {
                      chartType: "QPS",
                  })}`
                : `${this.$t("statistics.usageQuery.request.tableToExcel.excelName2")}`,
            str,
        });
    }

    protected downloadTable() {
        if (this.checkListIsEmpty(this.fetchData?.daily)) return;

        let str = `${this.$t("statistics.usageQuery.BandwidthFlow.tableColumn1")},${
            this.isQueryOrQPS
                ? `${this.$t("statistics.usageQuery.request.tableColumn3")}`
                : `${this.$t("statistics.usageQuery.request.tableColumn4")}`
        }\n`;

        this.fetchData.daily.forEach(item => {
            str += item.date + ",";
            str += (this.isQueryOrQPS ? item.query : item.miss) + ",\n";
        });

        this.downloadExcel({
            name: `${this.$t("statistics.common.tab[2]")}-${this.$t("statistics.usageQuery.request.ctTip")}`,
            str,
        });
    }
}
</script>
<style lang="scss" scoped>
.chart-wrap .total {
    display: flex;
    gap: 60px;
    flex-wrap: wrap;
    row-gap: 4px;

    .tip {
        width: unset;
    }
}
</style>
