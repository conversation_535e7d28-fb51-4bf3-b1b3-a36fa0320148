<template>
    <div class="ct-edit-wrapper">
        <el-form
            label-width="140px"
            label-position="left"
            :model="refererChainForm"
            :rules="rules"
            ref="refererChainForm"
            :disabled="!isEdit || !isService || isLockReferer"
            v-loading="loading"
        >
            <div v-if="!isPoweredByQiankun">
              <!-- Referer防盗链 开关 -->
              <el-form-item :label="$t('domain.create.referer')" prop="referer">
                <el-switch
                    v-model="refererChainForm.referer"
                    active-value="on"
                    inactive-value="off"
                    @change="referer_switch_change"
                ></el-switch>
              </el-form-item>
              <div v-if="refererChainForm.referer === 'on'" class="switch-wrapper">
                <!-- 是否允许空referer访问 -->
                <el-form-item :label="$t('domain.create.referer2')" prop="allow_empty">
                    <el-switch v-model="refererChainForm.allow_empty" active-value="on" inactive-value="off" @change="allow_empty_change"></el-switch>
                </el-form-item>

                <!-- 是否允许空协议 -->
                <el-form-item :label="$t('domain.create.referer3')" prop="referer_empty_protocol" v-if="isNewEcgw">
                    <el-switch v-model="refererChainForm.referer_empty_protocol" active-value="on" inactive-value="off" @change="referer_empty_protocol_change"></el-switch>
                </el-form-item>

                <!-- 匹配所有端口 -->
                <el-form-item :label="$t('domain.create.referer4')" prop="match_all_ports" v-if="isNewEcgw">
                    <el-switch v-model="refererChainForm.match_all_ports" active-value="on" inactive-value="off" @change="formChange"></el-switch>
                </el-form-item>

                <!-- 忽略大小写 -->
                <el-form-item :label="$t('domain.create.referer5')" prop="ignore_case" v-if="isNewEcgw">
                    <el-switch v-model="refererChainForm.ignore_case" active-value="on" inactive-value="off" @change="formChange"></el-switch>
                </el-form-item>

                <!-- 是否追加 -->
                <el-form-item prop="is_append" v-if="isNewEcgw">
                    <span slot="label">
                        {{ $t('domain.create.referer6') }}
                        <span>
                            <el-tooltip placement="top" :content="$t('domain.create.tip53')">
                                <ct-svg-icon
                                    icon-class="question-circle"
                                    class-name="ct-sort-drag-icon"
                                ></ct-svg-icon>
                            </el-tooltip>
                        </span>
                    </span>
                    <el-switch v-model="refererChainForm.is_append" :active-value="1" :inactive-value="0" @change="onIsAppendChange"></el-switch>
                </el-form-item>

                <!-- 类型 -->
                <el-form-item
                    :label="$t('domain.type')"
                    prop="domainList"
                    key="domainList"
                    :rules="rules.domainList"
                >
                    <div class="radio-row">
                        <el-radio-group v-model="refererChainForm.refererType" @change="refererTypeChange">
                            <el-radio label="allow">{{ $t("domain.detail.trustlist") }}</el-radio>
                            <el-radio label="block">{{ $t("domain.detail.blocklist") }}</el-radio>
                        </el-radio-group>
                    </div>
                    <div>
                        <el-input
                            class="textarea-wrapper"
                            v-model="refererChainForm.domainList"
                            type="textarea"
                            :rows="6"
                            :placeholder="refererPlaceholder"
                            @change="onRefererChange"
                        />
                    </div>
                </el-form-item>
              </div>
            </div>

        </el-form>
    </div>
</template>

<script>
import componentMixin from "@/views/domainConfig/componentMixin";
import ctSvgIcon from "@/components/ctSvgIcon";
import { BasicUrl } from "@/config/url/basic";
import { SecurityAbilityModule } from "@/store/modules/securityAbility";

export default {
    name: "refererChain",
    components: {
        ctSvgIcon
    },
    mixins: [componentMixin],
    props: {
        datas: Object,
        isNewEcgw: Boolean,
        isLockReferer: Boolean,
    },
    data() {
        return {
            refererChainForm: {
                referer: "off",
                allow_empty: "on",
                referer_empty_protocol: "off",
                match_all_ports: "off",
                ignore_case: "off",
                is_append: 0,
                refererType: "allow",
                domainList: "",
            },
            maxNum: 400,
            loading: false,
            rules: {
              domainList: [{ required: false, validator: this.valid_domain_list, trigger: "blur" }],
            },
        };
    },
    mounted() {
        this.getConfig();
    },
    computed: {
        refererPlaceholder() {
            const { maxNum } = this;
            if (this.isNewEcgw) {
                return this.$t("domain.detail.tip37", { maxNum: maxNum });
            } else {
                return this.$t("domain.detail.tip38", { maxNum: maxNum });
            }
        },
        domainList() {
            // 需要过滤空表内容
            return this.refererChainForm.domainList
                ?.split("\n")
                ?.map(i => i?.trim())
                ?.filter(i => i);
        },
    },
    watch: {
        datas: {
            deep: true,
            handler(val) {
                this.init(val);
            },
            immediate: true,
        },
    },
    methods: {
      init(v) {
        if (!v) return;
        this.refererChainForm.referer = v?.referer;
        this.refererChainForm.allow_empty = v?.allow_empty;
        this.refererChainForm.referer_empty_protocol = v?.referer_empty_protocol;
        this.refererChainForm.refererType = v?.refererType;
        this.refererChainForm.domainList = v?.domainList;
        this.refererChainForm.match_all_ports = v?.match_all_ports;
        this.refererChainForm.ignore_case = v?.ignore_case;
        this.refererChainForm.is_append = v?.is_append;
      },
      referer_switch_change(val) {
        const originalConf = SecurityAbilityModule.securityBasicConfigOriginForm;
        this.refererChainForm = {
            referer: val,
            allow_empty: originalConf.allow_empty,
            referer_empty_protocol: originalConf.referer_empty_protocol,
            match_all_ports: originalConf.match_all_ports,
            ignore_case: originalConf.ignore_case,
            is_append: originalConf.is_append,
            refererType: originalConf.refererType,
            domainList: originalConf.domainList,
        }

        this.$emit("onChange", this.refererChainForm, this.domainList);
      },
      // 是否允许空referer访问 事件
      allow_empty_change() {
        this.$emit("onChange", this.refererChainForm, this.domainList);
      },
      // 是否允许空协议 事件
      referer_empty_protocol_change() {
        this.$emit("onChange", this.refererChainForm, this.domainList);
      },
      // 类型 事件
      refererTypeChange() {
        this.$emit("onChange", this.refererChainForm, this.domainList);
      },
      // 输入框触发事件
      onRefererChange(val) {
        this.$emit("onChange", this.refererChainForm, this.domainList);
      },
      onIsAppendChange(val) {
        val && (this.refererChainForm.domainList = "");
        this.formChange()
      },
      formChange() {
        this.$emit("onChange", this.refererChainForm, this.domainList);
      },
      valid_domain_list(rule, value, callback) {
            if (!value) {
                return callback();
            }
            if (this.isLockReferer) {
                return callback();
            }
            // let { pattern } = commonPattern.referer;
            // pattern = `${pattern}|^localhost$`;
            // const referReg = new RegExp(pattern);
            const { maxNum, domainList } = this;
            if (!this.refererChainForm?.refererType && this.refererChainForm?.domainList) {
                return callback(new Error(this.$t("domain.detail.tip42")));
            } else if (domainList && domainList.length > maxNum) {
                return callback(new Error(this.$t("domain.detail.tip43", { maxNum: maxNum })));
            } else {
                // for (let i = 0; i < domainList.length; i++) {
                //     const item = domainList[i].trim();
                //     // 输入的不是合格 domain
                //     if (!referReg.test(item)) {
                //         return callback(new Error(this.$t("domain.batch.tip3")));
                //     }
                // }

                // 检查重复
                const hasRepeat = new Set(domainList).size !== domainList.length;
                if (hasRepeat) {
                    return callback(new Error(this.$t("domain.detail.tip44")));
                }
                return callback();
            }
        },
        async getConfig() {
            try {
                this.loading = true;
                const { refererLimit } = await this.$ctFetch(BasicUrl.getConfig, { cache: true });
                refererLimit && (this.maxNum = refererLimit);
            } catch(e) {
                this.$errorHandler(e);
            } finally {
                this.loading = false;
            }
        }
    },
};
</script>

<style lang="scss" scoped>
.ct-edit-wrapper {
    ::v-deep {
        .el-form--label-left .el-form-item__label {
            text-align: right;
        }
    }
}
.textarea-wrapper {
    width: calc(100% - 120px);
}
</style>
