<template>
    <el-dialog
        :title="$t('domain.list.opennote')"
        :append-to-body="true"
        :close-on-click-modal="false"
        :visible.sync="dialogVisible"
        :before-close="cancel"
        class="ipv-6-act-dialog"
        width="500px"
        :show-close="true"
    >
        <el-alert title="" type="info" :closable="false" show-icon class="ct-alert">
            <template slot="title">
                <!-- <div>
                    若需要实现IPv6外链改造效果，请先在<el-button type="text" @click="handleClick"
                        >域名列表</el-button
                    >中开启IPv6解析能力。
                </div> -->
                <div>
                    {{ $t("domain.detail.placeholder36") }}
                </div>
            </template>
        </el-alert>
        <div slot="footer" class="btns">
            <el-button @click="cancel">{{ $t("domain.cancel") }}</el-button>
            <el-button type="primary" @click="submit">{{ $t("domain.list.remainsopen") }}</el-button>
        </div>
    </el-dialog>
</template>

<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";
import pattern from "@/config/pattern";
import { AppModule } from "@/store/modules/app";

@Component({})
export default class UpdateDialog extends Vue {
    private pattern = pattern;
    @Prop({ default: false, type: Boolean }) private dialogVisible!: boolean;

    private async submit() {
        this.$emit("submit");
    }
    private cancel() {
        this.$emit("cancel", "ipv6ActVisible");
    }
    private handleClick() {
        const router = AppModule.baseAppRouter;
        if (!router) {
            return;
        }
        router.push({
            path: "/accessManagement/domainList",
        });
    }
}
</script>

<style lang="scss" scoped>
.ipv-6-act-dialog {
    .ct-alert {
        ::v-deep {
            .el-alert {
                align-items: center;
                padding: 12px;
            }
        }
    }
    .text1 {
        width: 416px;
        height: 56px;
        padding-top: 20px;
        background-color: $color-master-bg-light-2;
    }
    .text2 {
        margin-top: 20px;
        color: $color-warning;
    }
}
</style>
