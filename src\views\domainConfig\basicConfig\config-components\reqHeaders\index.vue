<template>
    <div class="ct-edit-wrapper">
        <el-form
            label-width="140px"
            label-position="left"
            :model="form"
            :rules="rules"
            ref="reqHeadersForm"
            :disabled="!isEdit || !isService || isLockReqHeader"
        >
            <div>
              <el-form-item
                :label="$t('domain.detail.label26')"
                prop="req_headers"
                ref="reqHeaders"
                class="ct-table-form-item table-form-item-style"
            >
                <span slot="label" class="req-header-style">
                    {{ $t("domain.detail.label26") }}
                    <span class="question-style">
                        <el-tooltip
                            placement="top"
                            :content="$t('domain.editPage.tip7')"
                        >
                            <ct-svg-icon
                                icon-class="question-circle"
                                class-name="question-circle"/></el-tooltip>
                    </span>
                </span>
                <div class="ct-table-wrapper">
                    <el-table class="origin-table auto-table" :data="form.req_headers">
                        <el-table-column
                            prop="key"
                            :label="$t('domain.editPage.label15')">
                            <template slot="header">
                                <div class="flex-row-style">
                                    <span>{{ $t("domain.editPage.label15") }}</span>
                                    <el-tooltip
                                        class="item"
                                        style="margin-left:4px"
                                        effect="dark"
                                        :content="$t('domain.editPage.tooltip1')"
                                        placement="top"
                                    >
                                        <i class="el-icon-question" />
                                    </el-tooltip>
                                </div>
                            </template>
                            <template slot-scope="scope">
                                <el-form-item
                                    label=""
                                    :prop="`req_headers.` + scope.$index + `.key`"
                                    :rules="rules.req_headers_key"
                                >
                                    <el-tooltip placement="top" :content="$t('domain.editPage.placeholder3')">
                                        <el-input
                                            v-model="scope.row.key"
                                            :placeholder="$t('domain.editPage.placeholder3')"
                                            class="input-style"
                                            @change="handleChange"
                                        ></el-input>
                                    </el-tooltip>
                                </el-form-item>
                            </template>
                        </el-table-column>
                        <el-table-column
                            prop="value"
                            :label="$t('domain.detail.label44')"
                        >
                            <template slot-scope="scope">
                                <el-form-item
                                    label=""
                                    :prop="`req_headers.` + scope.$index + `.value`"
                                    :rules="rules.req_headers_value"
                                >
                                    <el-tooltip placement="top" :content="$t('domain.editPage.placeholder5')">
                                        <el-input
                                            v-model="scope.row.value"
                                            class="input-style"
                                            @change="handleChange"
                                            :placeholder="$t('domain.editPage.placeholder5')"
                                        ></el-input>
                                    </el-tooltip>
                                </el-form-item>
                            </template>
                        </el-table-column>
                        <el-table-column :label="$t('domain.operate')" width="80">
                            <template slot-scope="scope">
                                <el-button
                                    type="text"
                                    @click="
                                        onOperator(
                                            scope.row,
                                            'delete',
                                            'req_headers',
                                            scope.$index
                                        )
                                    "
                                    >{{ $t("domain.delete") }}</el-button
                                >
                            </template>
                        </el-table-column>
                    </el-table>
                    <div class="flex-row-style button-box">
                        <el-button
                            class="btn"
                            type="text"
                            @click="onOperator(null, 'create', 'req_headers')"
                        >
                            + {{ addButtonText }}
                        </el-button>
                    </div>
                </div>
              </el-form-item>
            </div>

        </el-form>
    </div>
</template>

<script>
import ctSvgIcon from "@/components/ctSvgIcon";
import componentMixin from "@/views/domainConfig/componentMixin";
import i18n from "@/i18n/index";
import { cloneDeep } from "lodash-es";

export default {
    name: "reqHeaders",
    components: {
        ctSvgIcon,
    },
    mixins: [componentMixin],
    props: {
        datas: Object,
        isLockReqHeader: Boolean,
    },
    data() {
        return {
            addButtonText: i18n.t("domain.editPage.label10"),
            currentType: "create",
            form: {
                req_headers: [], // 回源HTTP请求头
            },
        };
    },
    computed: {
        rules() {
            return this.isLockReqHeader ? {} : {
                req_headers_key: [
                    { required: true, message: i18n.t("domain.detail.tip76"), trigger: "blur" },
                    {
                        max: 300,
                        message: i18n.t("domain.editPage.ruleTip2"),
                        trigger: ["blur", "change"],
                    },
                    {
                        pattern: "^[\\w-]+$",
                        message: i18n.t("domain.editPage.ruleTip3"),
                        trigger: ["blur", "change"],
                    },
                    {
                    validator: (rule, value, callback) => {
                        if (value && value.toLowerCase() === "host")
                            callback(new Error(i18n.t("domain.detail.tip77")));
                        else callback();
                    },
                    trigger: ["blur", "change"],
                    },
                ],
                req_headers_value: [
                {
                    max: 300,
                    message: i18n.t("domain.editPage.ruleTip2"),
                    trigger: ["blur", "change"],
                },
                {
                    pattern:
                        "^[^\\u4e00-\\u9fa5\\u3002\\uff1f\\uff01\\uff0c\\u3001\\uff1b\\uff1a\\u201c\\u201d\\u2018\\u2019\\uff08\\uff09\\u300a\\u300b\\u3008\\u3009\\u3010\\u3011\\u300e\\u300f\\u300c\\u300d\\ufe43\\ufe44\\u3014\\u3015\\u2026\\u2014\\uff5e\\ufe4f\\uffe5]+$",
                    message: i18n.t("domain.detail.tip29"),
                    trigger: ["blur", "change"],
                },
                // {
                //     pattern: "^[\\S-]+$",
                //     message: i18n.t("domain.detail.tip78"),
                //     trigger: ["blur", "change"],
                // },
                ],
            }
        }
    },
    watch: {
        "datas.req_headers": {
            deep: true,
            handler(val) {
                this.init(val);
            },
            immediate: true,
        },
    },
    methods: {
      init(v) {
          this.form.req_headers = cloneDeep(v);
      },
      handleChange() {
        this.$emit("onChange", this.form.req_headers);
      },
      async onOperator(row, currentType, tabName, i) {
        this.currentType = currentType;
        const getTime = new Date().getTime();

        if (currentType === "create") {
            const defaultFormMap = {
                req_headers: { key: "", value: "" },
            };
            row = defaultFormMap[tabName];
        }
        if (currentType === "delete") {
            const msgMap = {
                req_headers: this.$t("domain.editPage.tip20"),
            };

            // 二次确认弹窗
            await this.$confirm(msgMap[tabName], this.$t("domain.delete"), {
                type: "warning",
            });
            this.form[tabName].splice(i, 1);

            this.$emit("onChange", this.form.req_headers);
        } else {
            this.form[tabName].push(row);
        }
      },
    },
};
</script>

<style lang="scss" scoped>
@import "@/views/domainConfig/commonStyle.scss";
.ct-edit-wrapper {
    ::v-deep {
        .el-form--label-left .el-form-item__label {
            text-align: right;
        }
    }
}
</style>
