/**
 * @Description: 计费详情-资源包管理公共逻辑
 * @Author: wang yuegong
 */
import { Component, Mixins } from "vue-property-decorator";
import TableAndPagerActionMixin from "@/mixins/tableAndPagerAction";
import { nCdnBillingUrl, nBannerList } from "@/config/url";
import { BaseBannerItem } from "@/types/common";
import { FlowPacketItem } from "@/types/billing/index";
import { ProductCodeMap, PacketTypeMap, PacketUnitMap } from "@/config/map";
import { domainLangAutoComple } from "@/utils";
import { nUserModule } from "@/store/modules/nuser"; // 已合并

/* eslint-disable @typescript-eslint/camelcase */
@Component
export default class ResourcePackageMixin extends Mixins(TableAndPagerActionMixin) {
    loading = false;
    prefetch = true; // 进入页面即请求
    operationList: BaseBannerItem[] = [];
    searchUrl = nCdnBillingUrl.flowList;
    sortFlag = {
        fieldSort: "", // 查询的排序字段（生效时间 eff_date ，失效时间 expr_date）
        sortOrder: "", // 排序方式（升序 asc 、降序 desc）
    };
    PacketUnitMap = PacketUnitMap;

    // 获取操作按钮列表
    async getOperation(domain: string) {
        try {
            const { list = [] } = await this.$ctFetch<{ list: BaseBannerItem[] }>(nBannerList, {
                cache: true,
                data: {
                    // 只要是国际站控制台，后面都带上en
                    domain: domainLangAutoComple(
                        `${domain}${nUserModule.isCtclouds ? ".ctcloud" : ""}`,
                        nUserModule.lang
                    ),
                },
            });

            this.operationList = list;
        } catch (e) {
            // 操作按钮报错不做提示
        }
    }

    // 外链操作
    open(o: BaseBannerItem) {
        window.open(o.href, "_blank");
    }

    // 表格的一些格式化方法
    formatterName(row: FlowPacketItem) {
        return `${ProductCodeMap[row.product_code] || ""}${PacketTypeMap[row.resource_type] || ""}`;
    }
    formatterStatus(row: FlowPacketItem) {
        return `${this.$t(`billing.packageStatusOptions.itm${[row.status]}`) ||
            this.$t("billing.common.itm7")}`;
    }
    formatTooltip(val: number) {
        return val + "%";
    }

    packagePercent(row: FlowPacketItem) {
        const { packet_size, used_num } = row;
        return packet_size === 0 ? 100 : 100 - ((packet_size - +used_num) / packet_size) * 100;
    }
    sliderStatus(row: FlowPacketItem) {
        if (row.status === 1 || row.status === 2 || row.status === 3) {
            return "theme-color";
        }
        return "deep-color";
    }

    // 排序操作
    sortTable({ prop, order }: { prop: string; order: string }) {
        // 存在排序则处理参数
        if (order) {
            this.sortFlag = {
                sortOrder: order === "ascending" ? "asc" : "desc",
                fieldSort: prop === "start_time" ? "eff_date" : "exp_date",
            };
        } else {
            this.sortFlag = {
                fieldSort: "",
                sortOrder: "",
            };
        }

        this.refresh = true;
    }
}
