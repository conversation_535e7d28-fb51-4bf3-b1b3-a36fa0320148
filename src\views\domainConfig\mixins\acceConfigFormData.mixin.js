import { cloneDeep } from "lodash-es";

export default {
    computed: {
        /**
         * 根据环境动态返回表单数据
         * 
         * 此方法用于根据当前环境决定返回哪种表单数据
         * 如果当前环境是通过QIANKUN框架运行，则返回aocdnFormData，否则返回fcdnFormData
         * 这种设计允许代码在不同环境中灵活使用不同的数据源
         * 
         * @returns {Object} 当前环境下的表单数据
         */
        formData() {
            return window.__POWERED_BY_QIANKUN__ ? this.aocdnFormData : this.fcdnFormData;
        },
        commonFormData() {
            const returnData = {
                upload_speed: 0,
                filetype_ttl: [],
                error_code: [],
                cachekey_uri: [],
                dynamic_ability: "off",
                origin_policy: {},
                dynamic_config: {
                    route_type: 2, // 选路方式
                },
                mp4_conf_info: {},
                cus_flv_info: [],
                statics_ability: "on",
            };
            const {
                filetype_ttl = [],
                error_code = [],
                cachekey_uri = [],
                cachekey_args = [],
                mp4_conf_info = {},
                cus_flv_info = [],
            } = this.form;

            returnData.filetype_ttl = cloneDeep(filetype_ttl);
            // 缓存过期时间
            if (!this.isLockCacheTime) {
                if (returnData.filetype_ttl && returnData.filetype_ttl.length > 0) {
                    for (let i = 0; i < returnData.filetype_ttl.length; i++) {
                        if (returnData.filetype_ttl[i].timeType === "4") {
                            returnData.filetype_ttl[i].ttl = returnData.filetype_ttl[i].ttl * 60;
                        } else if (returnData.filetype_ttl[i].timeType === "3") {
                            returnData.filetype_ttl[i].ttl = returnData.filetype_ttl[i].ttl * 60 * 60;
                        } else if (returnData.filetype_ttl[i].timeType === "2") {
                            returnData.filetype_ttl[i].ttl = returnData.filetype_ttl[i].ttl * 60 * 60 * 24;
                        } else if (returnData.filetype_ttl[i].timeType === "5") {
                            returnData.filetype_ttl[i].ttl = returnData.filetype_ttl[i].ttl * 1;
                        }
                        if (returnData.filetype_ttl[i].cache_type === 1) {
                            returnData.filetype_ttl[i].ttl = 0;
                        }
                    }
                }
            } else {
                delete returnData?.filetype_ttl;
            }

            returnData.error_code = cloneDeep(error_code);
            // 状态码过期时间
            if (returnData.error_code && returnData.error_code.length > 0) {
                for (let i = 0; i < returnData.error_code.length; i++) {
                    if (returnData.error_code[i].timeType === "4") {
                        returnData.error_code[i].ttl = returnData.error_code[i].ttl * 60;
                    } else if (returnData.error_code[i].timeType === "3") {
                        returnData.error_code[i].ttl = returnData.error_code[i].ttl * 60 * 60;
                    } else if (returnData.error_code[i].timeType === "2") {
                        returnData.error_code[i].ttl = returnData.error_code[i].ttl * 60 * 60 * 24;
                    } else if (returnData.error_code[i].timeType === "5") {
                        returnData.error_code[i].ttl = returnData.error_code[i].ttl * 1;
                    }
                }
            }
            // 缓存key-缓存参数
            if (!this.isLockCacheKeyArgs) {
                returnData.cachekey_args = cloneDeep(cachekey_args);
                let cachekey_args_condition = {};
                if (returnData.cachekey_args && returnData.cachekey_args.length > 0) {
                    for (let i = 0; i < returnData.cachekey_args.length; i++) {
                        if (returnData.cachekey_args[i].ignore_params === 1) {
                            // 不忽略
                            this.$set(returnData.cachekey_args[i], "ignore", 0); // 去参数缓存开关 取值：0 否; 1 是
                            this.$set(returnData.cachekey_args[i], "is_with_args", 0); // 带特定参数缓存 取值：0 否; 1 是
                            delete returnData.cachekey_args[i].args;
                            delete returnData.cachekey_args[i].mode;
                            delete returnData.cachekey_args[i].ignore_params;
                            delete returnData.cachekey_args[i].content;
                            delete returnData.cachekey_args[i]?.ignore_args;
                            delete returnData.cachekey_args[i]?.typeMode;
                        } else if (returnData.cachekey_args[i].ignore_params === 2) {
                            // 全部忽略
                            this.$set(returnData.cachekey_args[i], "ignore", 1);
                            delete returnData.cachekey_args[i].args;
                            delete returnData.cachekey_args[i].mode;
                            delete returnData.cachekey_args[i].ignore_params;
                            delete returnData.cachekey_args[i].content;
                            delete returnData.cachekey_args[i]?.ignore_args;
                            delete returnData.cachekey_args[i]?.is_with_args;
                            delete returnData.cachekey_args[i]?.typeMode;
                        } else if (returnData.cachekey_args[i].ignore_params === 3) {
                            // 保留指定参数
                            this.$set(returnData.cachekey_args[i], "ignore", 0);
                            this.$set(returnData.cachekey_args[i], "is_with_args", 1);
                            this.$set(returnData.cachekey_args[i], "mode", 0);
                            this.$set(
                                returnData.cachekey_args[i],
                                "args",
                                "?" + returnData.cachekey_args[i].args
                            );
                            delete returnData.cachekey_args[i].ignore_params;
                            delete returnData.cachekey_args[i].content;
                            delete returnData.cachekey_args[i]?.ignore_args;
                        } else if (returnData.cachekey_args[i].ignore_params === 4) {
                            // 忽略指定参数
                            this.$set(returnData.cachekey_args[i], "ignore", 1);
                            this.$set(
                                returnData.cachekey_args[i],
                                "ignore_args",
                                returnData.cachekey_args[i].args
                            );
                            delete returnData.cachekey_args[i].mode;
                            delete returnData.cachekey_args[i].args;
                            delete returnData.cachekey_args[i].ignore_params;
                            delete returnData.cachekey_args[i].content;
                            delete returnData.cachekey_args[i]?.is_with_args;
                        } else {
                            this.$set(returnData.cachekey_args[i], "mode", 1);
                            delete returnData.cachekey_args[i]?.typeMode;
                        }
                    }
                }
                // 处理 cachekey_args_condition
                const temp_cachekey_args_condition = cloneDeep(this.cachekey_args_condition);
                if (this.cacheKeyDeleteIdList && this.cacheKeyDeleteIdList.length > 0) {
                    this.cacheKeyDeleteIdList.map(item => {
                        if (temp_cachekey_args_condition[item]) {
                            delete temp_cachekey_args_condition[item];
                        }
                    });
                }

                if (cachekey_args && cachekey_args.length > 0) {
                    cachekey_args.map(item => {
                        if (item?.content && this.cacheKeyIdList.includes(item?.id)) {
                            cachekey_args_condition[item?.id] = [
                                {
                                    mode: item?.mode,
                                    content: item?.content,
                                },
                            ];
                        }
                    });
                    cachekey_args_condition = Object.assign(
                        temp_cachekey_args_condition,
                        cachekey_args_condition
                    );
                } else {
                    cachekey_args_condition = {};
                }

                returnData.cachekey_args_condition = cachekey_args_condition;
            } else {
                delete returnData?.cachekey_args;
                delete returnData?.cachekey_args_condition;
            }
            // 缓存uri
            if (!this.isLockCacheKeyUri) {
                returnData.cachekey_uri = cachekey_uri.length > 0 ? cloneDeep(cachekey_uri) : [];
            } else {
                delete returnData?.cachekey_uri;
            }

            // mp4拖拉
            if (!this.isLockMp4) {
                if (mp4_conf_info.switch === 1) {
                    returnData.mp4_conf_info = mp4_conf_info;
                } else {
                    returnData.mp4_conf_info = { switch: 0 };
                }
                const mp4_conf_info_condition = {}
                if (mp4_conf_info && Object.keys(mp4_conf_info).length > 0 && mp4_conf_info.switch === 1) {
                    mp4_conf_info_condition["mp4_time_delay"] = [
                        {
                            mode: mp4_conf_info?.mode,
                            content: mp4_conf_info?.content,
                        }
                    ]
                }
                returnData.mp4_conf_info_condition = mp4_conf_info_condition;
            } else {
                delete returnData?.mp4_conf_info;
                delete returnData?.mp4_conf_info_condition;
            }


            // flv拖拉
            if (!this.isLockFlv) {
                if (this.flv_info_switch) {
                    returnData.cus_flv_info = cloneDeep(cus_flv_info);
                } else {
                    returnData.cus_flv_info = [];
                }
                let cus_flv_info_condition = {};
                if (cus_flv_info && cus_flv_info.length > 0 && this.flv_info_switch) {
                    cus_flv_info.map(item => {
                        cus_flv_info_condition[item?.id] = [
                            {
                                mode: item?.mode,
                                content: item?.content,
                            }
                        ]
                    })
                } else {
                    cus_flv_info_condition = {};
                }
                returnData.cus_flv_info_condition = cus_flv_info_condition;
            } else {
                delete returnData?.cus_flv_info;
                delete returnData?.cus_flv_info_condition;
            }
            // 加速区域
            returnData.area_scope = this.form.area_scope;

            // 上传加速
            // 乾坤入口 或者 域名类型等于全站加速-websocket时("104": "全站加速-上传加速")， 才需要传参给后端接口
            console.log('isShowUploadSpeed :>> ', this.isShowUploadSpeed);
            console.log('this.form?.upload_speed :>> ', this.form?.upload_speed);
            if (this.isShowUploadSpeed) {
                returnData.upload_speed = this.form?.upload_speed || 0;
            }

            return returnData;
        },
        fcdnFormData() {
            const returnData = cloneDeep(this.commonFormData);

            // 动态配置
            delete returnData?.dynamic_ability;
            // 选路方式
            delete returnData?.dynamic_config;
            // 静态开关
            delete returnData?.statics_ability;

            // 旧框架
            if (this.temp_domain_detail?.use_ecgw === 0) {
                // mp4拖拉
                delete returnData?.mp4_conf_info;
                delete returnData?.mp4_conf_info_condition;
                // flv拖拉
                delete returnData?.cus_flv_info;
                delete returnData?.cus_flv_info_condition;
            }

            // 全站产品 detail 才会给 domain_type，有给需要回传
            // domain_type 有变更才回传
            if (
                this.form.domain_type &&
                this.form.domain_type !== this.temp_domain_detail?.domain_type
            ) {
                returnData.domain_type = this.form.domain_type;
            }
            // 动态回源策略，CDN入口处理逻辑
            // 1、如果产品类型为全站加速，即product_code等于“006”，并且/domain/isVivo接口返回的vivo为false，动态回源策略在页面选中什么就传什么值给后端
            // 2、如果源站的主源（即层级为：主）大于等于2，并且产品类型为非全站加速，即product_code不等于“006”，动态回源策略传"poll"给后端
            // 3、不满足第1点和第2点条件的话，动态回源策略origin_policy.http_config.origin_type不需要传参给后端
            const basicConfigFormData = this.$refs.basicConfig?.$children[0]?.formData || {};
            const origin = basicConfigFormData?.origin || []
            let role_count = 0;
            if (origin && origin.length > 0) {
                for (let i = 0; i < origin.length; i++) {
                    if (origin[i].role === "master") {
                        role_count++;
                    }
                }
            }
            if (this.form.origin_policy && Object.keys(this.form.origin_policy).length > 0) {
                returnData.origin_policy = this.form.origin_policy;
            }
            if (this.temp_domain_detail.product_code === "006" && !this.isVivo) {
                returnData.origin_policy.http_config.origin_type = this.form?.origin_policy?.http_config?.origin_type;
            } else if (role_count >= 2 && this.temp_domain_detail.product_code !== "006") {
                returnData.origin_policy.http_config.origin_type = "poll";
            } else {
                delete returnData?.origin_policy;
            }

            // 静态类、全站类产品才需要配置
            if (this.showForStaticAndIcdn) {
                // udfScript
                returnData.script = this.form?.script;
                // URL重定向
                if (!this.isLockDefineRedirect && this.temp_domain_detail.use_ecgw === 1) {
                    returnData.define_redirect = cloneDeep(this.form.define_redirect);
                    let define_redirect_condition = {};
                    if (this.form.define_redirect && this.form.define_redirect.length > 0) {
                        this.form.define_redirect.map(item => {
                            if (
                                item?.mode === "" ||
                                item?.mode === null ||
                                item?.mode === undefined ||
                                item?.content === "" ||
                                item?.content === null ||
                                item?.content === undefined
                            ) {
                                return;
                            } else {
                                define_redirect_condition[item?.id] = [
                                    {
                                        mode: item?.mode,
                                        content: item?.content,
                                    },
                                ];
                            }
                        });
                    } else {
                        define_redirect_condition = {};
                    }
                    returnData.define_redirect_condition = define_redirect_condition;
                } else {
                    delete returnData?.define_redirect;
                    delete returnData?.define_redirect_condition;
                }
                // 错误页面重定向
                if (!this.isLockErrorPage && this.temp_domain_detail.use_ecgw === 1) {
                    returnData.error_page = cloneDeep(this.form.error_page);
                } else {
                    delete returnData?.error_page;
                }
            } else {
                // udfScript
                delete returnData?.script;
                // URL重定向
                delete returnData?.define_redirect;
                delete returnData?.define_redirect_condition;
                // 错误页面重定向
                delete returnData?.error_page;
            }

            return returnData;
        },
        aocdnFormData() {
            const returnData = cloneDeep(this.commonFormData);
            const { dynamic_ability } = this.form;

            // 动态配置
            returnData.dynamic_ability = dynamic_ability;
            // 动态回源策略
            if (this.form.origin_policy && Object.keys(this.form.origin_policy).length > 0) {
                returnData.origin_policy = this.form.origin_policy;
            }
            returnData.origin_policy.http_config.origin_type =
                this.form?.origin_policy?.http_config?.origin_type || "poll";
            // 选路方式
            returnData.dynamic_config.route_type = this.form?.dynamic_config?.route_type;
            if (this.form?.dynamic_config?.probe_url || this.form?.dynamic_config?.probe_url === "") {
                returnData.dynamic_config.probe_url = this.form?.dynamic_config?.probe_url;
            }
            // 静态开关
            returnData.statics_ability = this.form?.statics_ability;

            return returnData;
        },
    }
}
