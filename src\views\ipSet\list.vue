<template>
    <ct-section-wrap v-loading="loading" :headerText="ipv6Act ? $t('ipSet.title') : ''">
        <template slot="tip" v-if="ipv6Act">
            {{ headerTip }}
        </template>
        <ct-box class="table-scroll-wrap" v-if="ipv6Act">
            <el-tabs v-model="activeTab">
                <el-tab-pane v-if="!noTabAuth.list" :label="$t('ipSet.IP集管理')" name="list" />
                <el-tab-pane v-if="!noTabAuth.usage" :label="$t('ipSet.IP集使用情况')" name="usage" />
            </el-tabs>

            <el-alert
                v-if="$PoweredByQiankun"
                :closable="false"
                type="info"
                class="ct-alert"
                show-icon
                :title="$t('ipSet.ipSetListTip')"
            ></el-alert>

            <!-- IP集管理 -->
            <ip-set-list v-if="activeTab === 'list' && !noTabAuth.list" />

            <!-- IP集使用情况 -->
            <ip-set-usage v-if="activeTab === 'usage' && !noTabAuth.usage" />
        </ct-box>
        <ct-box class="table-scroll-wrap" v-else>
            <div class="purchase-card">
                <div class="title-box">{{ $t("ipSet.title") }}</div>
                <div class="content-box">
                    {{
                        $t(
                            "ipSet.提供全局IP大容量封禁防护，可高效满足客户端IP的大规模封禁与解封需求，提升攻防对抗效率。"
                        )
                    }}
                </div>
                <el-button class="btn-style" type="primary" @click="toHighVersion" :disabled="false">{{
                    $t("domain.editPage.btn1")
                }}</el-button>
            </div>
        </ct-box>
    </ct-section-wrap>
</template>
<script lang="ts">
import { Component, Vue, Watch } from "vue-property-decorator";
import IpSetList from "./components/ipSetList.vue";
import IpSetUsage from "./components/ipSetUsage.vue";
import { SecurityAbilityModule } from "@/store/modules/securityAbility";
import { AppModule } from "@/store/modules/app";
import { CtiamButtonEnum } from "@/store/config";
import { MenuModule } from "@/store/modules/menu";

@Component({
    components: {
        IpSetList,
        IpSetUsage,
    },
})
export default class IpSet extends Vue {
    private activeTab = "list";
    private loading = false;
    /**
     * 根据环境变量判断是否显示特定提示信息
     *
     * @returns {string} 如果当前环境由Qiankun微前端框架控制，则返回国际化提示信息；否则返回空字符串
     */
    get headerTip() {
        return window.__POWERED_BY_QIANKUN__ ? this.$t("ipSet.tip") : "";
    }
    get ipv6Act() {
        if (!window.__POWERED_BY_QIANKUN__) {
            return true;
        }
        return SecurityAbilityModule.ipSet.ipSetAct;
    }
    async mounted() {
        this.loading = true;
        this.$PoweredByQiankun && await SecurityAbilityModule.GetIsBilling();
    }
    // 升级到高级版
    toHighVersion() {
        const billingInfos = sessionStorage.getItem("accessOnePackageInfos")
            ? JSON.parse(sessionStorage.getItem("accessOnePackageInfos") || "[]")
            : [];

        const router = AppModule.baseAppRouter;

        if (!router) {
            return;
        }

        const info = billingInfos.find((item: any) => item.resourceType === "EDGE_SEC_ACCE");
        if (!info) {
            return;
        }

        router.push({
            path: "/packageManagement/purchase",
            query: {
                resourceId: info.resourceId,
            },
        });
    }
    get noTabAuth() {
        return {
            list: [
                CtiamButtonEnum.ipSetListAdd,
                CtiamButtonEnum.ipSetListEdit,
                CtiamButtonEnum.ipSetListDelete,
                CtiamButtonEnum.ipSetListView,
                CtiamButtonEnum.ipSetListBindDomain,
                CtiamButtonEnum.ipSetListRetry,
                CtiamButtonEnum.ipSetListDownload,
                CtiamButtonEnum.ipSetListDetail,
            ].every(item => MenuModule.ctiamButtonAuth.authList.find(auth => auth.code === item)),
            usage: [CtiamButtonEnum.ipSetUsageList, CtiamButtonEnum.ipSetUsageDownload].every(item =>
                MenuModule.ctiamButtonAuth.authList.find(auth => auth.code === item)
            ),
        };
    }

    @Watch("activeTab", { immediate: true })
    onActiveTabChange(newVal: "list" | "usage") {
        // 如果当前tab没有权限，则切换到另一个tab
        if (this.noTabAuth[newVal as "list" | "usage"]) {
            this.activeTab = newVal === "list" ? "usage" : "list";
        }
    }
}
</script>
<style lang="scss" scoped>
.ct-alert {
    margin-bottom: 16px;

    ::v-deep {
        .el-alert {
            align-items: center;
            padding: 12px;
        }
    }
}
.purchase-card {
    min-width: 851px;
    min-height: 250px;
    background: url("../domainConfig/images/1.png") 0 center no-repeat;
    background-size: cover;
    padding-top: 1px;

    .title-box {
        opacity: 0.85;
        font-size: 20px;
        color: $color-neutral-10;
        letter-spacing: 0;
        width: 124px;
        line-height: 20px;
        font-weight: 600;
        margin: 52px 0 0 68px;
    }

    .content-box {
        width: 520px;
        height: 48px;
        font-size: 12px;
        color: $color-neutral-9;
        letter-spacing: 0;
        text-align: justify;
        line-height: 24px;
        font-weight: 400;
        margin: 22px 0 0 68px;
    }

    .btn-style {
        height: 36px;
        margin: 28px 0 0 68px;
    }
}
</style>
