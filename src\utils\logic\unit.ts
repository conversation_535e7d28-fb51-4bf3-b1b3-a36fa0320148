export * from '@cdnplus/common/utils/logic/unit';

/**
 * 统计数据单位转换
 * <AUTHOR>
 */
import {
    BandwidthUnits,
    BandwidthUnitB2G,
    BandwidthUnitM2G,
    BandwidthUnitM2P,
    BandwidthUnitB2P,
    BandwidthUnitB2Y,
    FlowUnits,
    FlowUnitB2G,
    FlowUnitM2G,
    FlowUnitM2P,
    FlowUnitB2P,
    FlowUnitB2Y,
} from "@cdnplus/common/types/statistics";
import { TenThousandUnit } from "@cdnplus/common/types/common";
import { TenThousandUnits } from "@cdnplus/common/config/map";

/**
* 通用方法：获取单位缩进处理方法，参数为所需要的单位数组；其中 num 是字符串，需要保留两位小数
* @param units 单位数组
* @param config 更多配置：type 返回数据类型，digits 小数位数（当 type 为 number 时，表示最大小数位数）
*/
export const getIndentation = <T extends string>(
    units: T[],
    { type, digits }: { type: "number" | "string"; digits?: number } = { type: "string", digits: 2 }
) => (data: number | string, scale = 1000) => {
    if (!data || isNaN(Number(data))) {
        data = 0;
    }

    // 转换为数字类型
    data = +data;

    function retract(data: number, idx = 0): [number, number] {
        // 还能缩进就继续递归，不能缩进就返回
        return data >= scale && idx < units.length - 1 ? retract(data / scale, ++idx) : [data, idx];
    }

    const [num, idx] = retract(data);

    const _num = type === "string" ? num.toFixed(digits) : parseFloat(num.toFixed(digits));

    let unit = units[idx];
    // 当进制为1024时，需要加多一个 i
    if (scale === 1024) {
        unit = unit.replace("bps", "ibps").replace("B", "iB") as T;
    }

    return {
        num: _num,
        unit,
        _unit: units[idx], // 保留一个原单位，用于某些特殊的实用场景
        result: _num + unit,
    };
};

/**
* 常用的几种单位组合下的带宽数据转换工具，m2p 一般用于过滤器
* @param bandwidth 带宽数值
* @param scale 缩进比例（正常由后端获取，默认 1000）
*/
const bandwidthB2GIndentation = getIndentation<BandwidthUnitB2G>(BandwidthUnits.B2G);
export function convertBandwidthB2G(bandwidth: number | string = 0, scale = 1000) {
    return bandwidthB2GIndentation(bandwidth, scale);
}
const bandwidthM2GIndentation = getIndentation<BandwidthUnitM2G>(BandwidthUnits.M2G);
export function convertBandwidthM2G(bandwidth: number | string = 0, scale = 1000) {
    return bandwidthM2GIndentation(bandwidth, scale);
}
const bandwidthM2PIndentation = getIndentation<BandwidthUnitM2P>(BandwidthUnits.M2P);
export function convertBandwidthM2P(bandwidth: number | string = 0, scale = 1000) {
    return bandwidthM2PIndentation(bandwidth, scale);
}
const bandwidthB2PIndentation = getIndentation<BandwidthUnitB2P>(BandwidthUnits.B2P);
export function convertBandwidthB2P(bandwidth: number | string = 0, scale = 1000) {
    return bandwidthB2PIndentation(bandwidth, scale);
}
const bandwidthB2YIndentation = getIndentation<BandwidthUnitB2Y>(BandwidthUnits.B2Y);
export function convertBandwidthB2Y(bandwidth: number | string = 0, scale = 1000) {
    return bandwidthB2YIndentation(bandwidth, scale);
}

/**
* 常用的几种单位组合下的流量数据转换工具
* @param flow 流量数值
* @param scale 缩进比例（正常由后端获取，默认 1000）
*/
const flowB2GIndentation = getIndentation<FlowUnitB2G>(FlowUnits.B2G);
export function convertFlowB2G(flow: number | string = 0, scale = 1000) {
    return flowB2GIndentation(flow, scale);
}
const flowM2GIndentation = getIndentation<FlowUnitM2G>(FlowUnits.M2G);
export function convertFlowM2G(flow: number | string = 0, scale = 1000) {
    return flowM2GIndentation(flow, scale);
}
const flowM2PIndentation = getIndentation<FlowUnitM2P>(FlowUnits.M2P);
export function convertFlowM2P(flow: number | string = 0, scale = 1000) {
    return flowM2PIndentation(flow, scale);
}
const flowB2PIndentation = getIndentation<FlowUnitB2P>(FlowUnits.B2P);
export function convertFlowB2P(flow: number | string = 0, scale = 1000) {
    return flowB2PIndentation(flow, scale);
}
const flowB2YIndentation = getIndentation<FlowUnitB2Y>(FlowUnits.B2Y);
export function convertFlowB2Y(flow: number | string = 0, scale = 1000) {
    return flowB2YIndentation(flow, scale);
}

// 万进制转换
const tenThousandIndentation = getIndentation<TenThousandUnit>(TenThousandUnits, { type: "number" });
export function convertTenThousand(number: number | string = 0) {
    return tenThousandIndentation(number, 10000) as {
        num: number;
        unit: TenThousandUnit;
        result: string;
    };
}
