import { VuexModule, Module, Mutation, getModule, Action } from "vuex-module-decorators";
import store from "@/store";
import { get, isEqual, values } from "lodash-es";
import { ctFetch } from "../../utils";
import { BasicUrl } from "@/config/url/basic";
import { errorHandler } from "@/utils";
// import actions from "@/microApp/actions";
import { DomainUrl } from "@/config/url/ipa/domain";
import router from "@/router";
import { OverviewUrl } from "@/config/url";
import { DomainModule } from "@/store/modules/domain";
import { cloneDeep } from "lodash-es";

type EasDomainItem = {
    domain: string;
    domain_id: number;
    cname: string;
    product_code: string;
    status: number;
    insert_date: string;
    ipv6_enable: boolean;
    ipv6_status: number;
    area_scope: number;
    ddos_switch: string;
    defenseIp: string;
    defenseCname: string;
    access_mode: number;
    inst_name: string;
    virtual_domain: number;
    creating: boolean;
};

// 安全能力store数据
@Module({ dynamic: true, store, name: "securityAbilityConfig" })
class SecurityAbility extends VuexModule {
    public securityDomainInfo: any = null;
    public isBilling = false; // 是否收费套餐，true代表是收费套餐，false代表免费版
    public webSocketEnable = false; // 是否支持web_socket，true代表支持
    public dynamicAccelerateEnable = false; // 是否支持动态加速，true代表支持
    public ipv6OutLinkReformEnable = false; // 是否支持ipv6，true代表支持
    public specialPortEnable = false; // 特殊端口（服务端口）
    public highPerfNetAct = false; // 是否支持高性能网络，true代表支持
    public isEdit = false; // 是否正在编辑中
    public currentModule: any = null; // 当前域名配置模块
    public moduleRenderKey = +new Date();

    public securityOriginForm: any = null; // 配置模块当前模块的原始表单对象
    public securityCurrentForm: any = null; // 配置模块当前模块表单对象
    // 基础配置
    public securityBasicConfigOriginForm: any = null; // 配置模块当前模块的原始表单对象
    public securityBasicConfigCurrentForm: any = null; // 配置模块当前模块表单对象
    // 域名列表：domainPanel组件专用
    public securityDomainList: EasDomainItem[] = []; // 域名列表数据
    public securityDomainTotal = 0; // 当前域名总数
    public securityDomainLoading = false; // 域名列表loading
    public securityBasicDomainInfo: any = null; // 新控制台域名信息，阉割信息（不全面）,基本信息
    public securityPanelDataLoading = false; // 用于域名列表：已启用和配置中的loading
    public securityPanelData: any = null; // 域名列表-已启用/配置中-数量统计
    public isIpaFormChange = false; // ipa模块是否变动
    public securityModule: any = null; // 当前模块
    public domainOrderStatus = false; // 用于判断是否开通边缘接入产品
    public hasDdos = false;
    public serviceIdentifier = ""; // 记录用户标识（如金华）
    public isBillingSuccess = false;
    public domain_type = "";
    public isCdnFixFormChange = false; // 融合后CDN模块是否变动
    isDomainDetailLoading = false;
    public isIpaDomainDetailLoading = false; // ipa域名详情loading
    public ipSet = {
        ipSetAct: 0,
        ipSetLimit: 0,
        ipSetIpLimit: 0,
    };

    // 当前域名，用于传递参数
    get securityDomain() {
        return get(this.securityDomainInfo, "domain");
    }
    // 当前域名的状态
    get securityDomainStatus() {
        return get(this.securityDomainInfo, "domainStatus");
    }
    // ipa接入到aocdn，域名列表切换时，获取域名
    get ipaSecurityDomain() {
        return get(this.securityBasicDomainInfo, "domain");
    }
    // 获取域名接入方式
    get ipaAccessMode() {
        return get(this.securityBasicDomainInfo, "access_mode");
    }
    // 获取 inst_name
    get ipaInstName() {
        return get(this.securityBasicDomainInfo, "inst_name");
    }
    // 获取 virtual_domain
    get ipaVirtualDomain() {
        return get(this.securityBasicDomainInfo, "virtual_domain");
    }

    get isIpaSecurityFormSame() {
        if (this.isIpaDomainDetailLoading || !this.ipaSecurityDomain) {
            return true;
        }

        const ipaFormProcessor = (form: any) => {
            const _form = cloneDeep(form);
            if (!_form.user_ip.switch) {
                _form.user_ip = { switch: false };
            }

            return _form;
        };

        return isEqual(ipaFormProcessor(this.securityOriginForm), ipaFormProcessor(this.securityCurrentForm));
    }

    // 当前安全能力模块表单是否发生变化
    get isSecurityFormSame() {
        let res = true;
        // 当前配置获取中 或者 域名为空，均不进行配置对比
        if (this.isDomainDetailLoading || !this.securityDomain) {
            res = true;
        } else {
            res =
                isEqual(this.securityOriginForm, this.securityCurrentForm) &&
                isEqual(this.securityBasicConfigOriginForm, this.securityBasicConfigCurrentForm);
        }
        return res;
    }
    @Mutation
    SET_IS_DOMAIN_DETAIL_LOADING(data: boolean) {
        this.isDomainDetailLoading = data;
    }
    @Mutation
    SET_IS_IPA_DOMAIN_DETAIL_LOADING(data: boolean) {
        this.isIpaDomainDetailLoading = data;
    }
    @Mutation
    SET_IS_IPA_SECURITY_CHANGE(isChange: boolean) {
        this.isIpaFormChange = isChange;
    }
    @Mutation
    SET_IS_CDN_FIX_SECURITY_CHANGE(isChange: boolean) {
        this.isCdnFixFormChange = isChange;
    }
    /**
     * 同步当前模块
     * @param module
     * @constructor
     */
    @Mutation
    SET_SECURITY_MODULE(module: any) {
        this.securityModule = module;
    }
    @Mutation
    SET_SECURITY_PANEL_DATA(data: any) {
        this.securityPanelData = data;
    }

    @Mutation
    SET_SECURITY_PANEL_DATA_LOADING(loading: boolean) {
        this.securityPanelDataLoading = loading;
    }

    /**
     * 同步当前域名信息
     * @param data
     * @constructor
     */
    @Mutation
    SET_SECURITY_DOMAIN_INFO(data: any) {
        this.securityDomainInfo = data;
    }

    /**
     * 同步表单初始值
     * @param data
     * @constructor
     */
    @Mutation
    SET_SECURITY_ORIGIN_FORM(data: any) {
        this.securityOriginForm = data;
    }
    // 基础配置-原始form
    @Mutation
    SET_SECURITY_BASIC_ORIGIN_FORM(data: any) {
        this.securityBasicConfigOriginForm = data;
    }

    /**
     * 同步表单值
     * 利用引用数据类型，做动态比对
     * @param data
     * @constructor
     */
    @Mutation
    SET_SECURITY_CURRENT_FORM(data: any) {
        this.securityCurrentForm = data;
    }
    // 基础配置-修改后的 form
    @Mutation
    SET_SECURITY_BASIC_CURRENT_FORM(data: any) {
        this.securityBasicConfigCurrentForm = data;
    }

    @Mutation
    SET_CURRENT_MODULE(module: any) {
        this.currentModule = module;
    }
    @Mutation
    SET_MODULE_RENDER_KEY() {
        this.moduleRenderKey = +new Date();
    }

    @Mutation
    SET_IS_EDIT(isEdit: boolean) {
        this.isEdit = isEdit;
    }

    @Mutation
    SET_IS_BILLING({ name, isEnable }: { name: string; isEnable: boolean }) {
        name === "isBilling" && (this.isBilling = isEnable);
        name === "webSocketEnable" && (this.webSocketEnable = isEnable);
        name === "dynamicAccelerateEnable" && (this.dynamicAccelerateEnable = isEnable);
        name === "ipv6OutLinkReformEnable" && (this.ipv6OutLinkReformEnable = isEnable);
        name === "specialPortEnable" && (this.specialPortEnable = isEnable);
        name === "highPerfNetAct" && (this.highPerfNetAct = isEnable);
    }

    @Mutation
    SET_IS_BILLING_SUCCESS(data: boolean) {
        this.isBillingSuccess = data;
    }
    @Mutation
    SET_DOMAIN_TYPE(data: string) {
        this.domain_type = data;
    }
    @Mutation
    SET_IP_SET(data: any) {
        this.ipSet = data;
    }

    @Action
    public async GetIsBilling() {
        try {
            const data: any = await ctFetch(BasicUrl.isBilling);
            if (data) {
                this.SET_IS_BILLING(
                    JSON.parse(JSON.stringify({ name: "isBilling", isEnable: data.isBilling }))
                );
                this.SET_IS_BILLING(
                    JSON.parse(JSON.stringify({ name: "webSocketEnable", isEnable: data.webSocketEnable }))
                );
                this.SET_IS_BILLING(
                    JSON.parse(
                        JSON.stringify({
                            name: "dynamicAccelerateEnable",
                            isEnable: data.dynamicAccelerateEnable,
                        })
                    )
                );
                this.SET_IS_BILLING(
                    JSON.parse(
                        JSON.stringify({
                            name: "ipv6OutLinkReformEnable",
                            isEnable: data.ipv6OutLinkReformEnable,
                        })
                    )
                );
                this.SET_IS_BILLING(
                    JSON.parse(
                        JSON.stringify({ name: "specialPortEnable", isEnable: data.specialPortEnable })
                    )
                );
                this.SET_IS_BILLING(
                    JSON.parse(JSON.stringify({ name: "highPerfNetAct", isEnable: data.highPerfNetAct }))
                );
                this.SET_IP_SET({
                    ipSetAct: data.ipSetAct,
                    ipSetLimit: data.ipSetLimit,
                    ipSetIpLimit: data.ipSetIpLimit,
                });
            }
            this.SET_IS_BILLING_SUCCESS(true);
        } catch (error) {
            this.SET_IS_BILLING_SUCCESS(false);
            errorHandler(error as any);
        }
    }
    /**
     * 同步域名列表数据
     * @param data
     * @constructor
     */
    @Mutation
    SET_SECURITY_DOMAIN_LIST(data: { result: EasDomainItem[] }) {
        this.securityDomainList = data?.result || [];

        if (!this.securityBasicDomainInfo) {
            this.securityBasicDomainInfo = get(this.securityDomainList, "[0]");
            return;
        }

        // this.securityBasicDomainInfo = get(this.securityDomainList, "[0]");
    }
    /**
     * 同步域名列表加载状态
     * @param loading
     * @constructor
     */
    @Mutation
    SET_SECURITY_DOMAIN_LOADING(loading: boolean) {
        this.securityDomainLoading = loading;
    }
    /**
     * 同步域名总数
     * @param total
     * @constructor
     */
    @Mutation
    SET_SECURITY_DOMAIN_TOTAL(total: any) {
        this.securityDomainTotal = total;
    }
    /**
     * 同步部分域名信息
     * @constructor
     */
    @Mutation
    SET_SECURITY_BASIC_DOMAIN_INFO(data: any) {
        this.securityBasicDomainInfo = data;
        // actions.setGlobalState({
        //     currentDomainInfo: data,
        // });
    }
    /**
     * 同步域名列表加载状态
     * @param loading
     * @constructor
     */
    @Mutation
    SET_DOMAIN_ORDER_STATUS(data: boolean) {
        this.domainOrderStatus = data;
    }

    @Mutation
    SET_HAS_DDOS(data: boolean) {
        this.hasDdos = data;
    }

    @Mutation
    SET_SERVICE_IDENTIFIER(val: string) {
        this.serviceIdentifier = val;
    }

    /**
     * 获取所有域名数据
     */
    @Action
    async getSecurityDomainList(queryParams: any) {
        this.SET_SECURITY_DOMAIN_LOADING(true);

        try {
            // const data = await this.panelDomainListFn(queryParams);
            const data = await ctFetch<{
                result: EasDomainItem[];
                length: number;
            }>(DomainUrl.domainListPage, {
                method: "GET",
                transferType: "json",
            });
            this.SET_SECURITY_DOMAIN_LIST(data);
            const hasParams = values(queryParams).some((item: any) => item);
            if (!hasParams) {
                this.SET_SECURITY_DOMAIN_TOTAL(data.length);
            }
        } catch (e) {
            errorHandler(e as any);
        }

        this.SET_SECURITY_DOMAIN_LOADING(false);
    }
    @Action
    async getDomainListPanelData(queryParams: any) {
        this.SET_SECURITY_PANEL_DATA_LOADING(true);
        try {
            const data: any = await ctFetch(DomainUrl.queryListCount, {
                method: "GET",
                transferType: "json",
                data: queryParams,
            });
            this.SET_SECURITY_PANEL_DATA(data.result);
        } catch (e) {
            errorHandler(e as any);
        }

        this.SET_SECURITY_PANEL_DATA_LOADING(false);
    }

    // 套餐信息
    public billingInfos: Record<string, string | boolean> | null = null;

    /**
     * 设置套餐信息
     * @constructor
     */
    @Mutation
    SET_BILLING_INFOS(data: Record<string, string | boolean>) {
        this.billingInfos = data;
    }

    @Action
    async getDomainOrderCheck() {
        const resp: any = await ctFetch(OverviewUrl.domainBillingCheck, {
            method: "GET",
            transferType: "json",
        });

        this.SET_DOMAIN_ORDER_STATUS(resp?.result?.has_product);
        this.SET_HAS_DDOS(resp?.result?.has_ddos);
        this.SET_SERVICE_IDENTIFIER(resp?.result?.serviceIdentifier || "jinhua");
        this.SET_BILLING_INFOS(resp?.result);
        DomainModule.setDomainOrderStatus(resp?.result?.status);
    }

    public securityRenderKey = +new Date(); // 安全模块强制刷新标志

    /**
     * 同步安全能力模块强制渲染标识，以时间戳为主
     * @constructor
     */
    @Mutation
    SET_SECURITY_RENDER_KEY() {
        this.securityRenderKey = +new Date();
    }

    /**
     * 同步清空所有安全模块信息
     * @constructor
     */
    @Mutation
    CLEAR_ALL_SECURITY_DATA() {
        this.securityDomainInfo = null;
    }
}

export const SecurityAbilityModule = getModule(SecurityAbility);
