// import { withResolver } from ".";

/**
 * 根据给定的URL和键下载文件
 * 此函数使用HTML表单和iframe来触发文件下载，然后通过解析iframe的内容来确认下载是否成功
 *
 * @param {Object} params 包含URL和键的对象
 * @param {string} params.url 要下载的文件URL
 * @param {string} params.key 用于区分不同下载任务的键
 * @returns {Promise<boolean>} 返回一个Promise，表示下载是否成功（true表示成功，false表示失败）
 */
export function downloadFile({ url, key }: { url: string; key: string }) {
    // const iframe = document.createElement("iframe");
    // const { promise, resolve } = withResolver<boolean>();
    // const target = `$log-downloadIframe${key}`;

    // iframe.style.display = "none";
    // iframe.src = "about:blank";
    // iframe.src = url;
    // iframe.title = target;
    // iframe.name = target;
    // iframe.id = target;
    // document.body.appendChild(iframe);

    const form = document.createElement("form");
    form.method = "GET";
    const formUrl = new URL(url);
    form.action = formUrl.origin + formUrl.pathname;
    formUrl.searchParams.forEach((value, key) => {
        const input = document.createElement("input");
        input.type = "hidden";
        input.name = key;
        input.value = value;
        form.appendChild(input);
    });
    form.target = "_blank";
    document.body.appendChild(form);

    form.submit();
    document.body.removeChild(form);

    // setTimeout(() => {
    //     if (iframe.contentDocument) {
    //         // 发出HEAD请求，检查文件是否存在&服务端是否可用
    //         const response = fetch(url, {
    //             method: "HEAD", // 使用 HEAD 方法，只请求头部信息，减少数据传输
    //             cache: "no-cache", // 避免缓存干扰
    //         });

    //         response
    //             .then(() => {
    //                 // 如果iframe的内容不为空或者不为HTML，说明下载失败
    //                 const nodeName = iframe?.contentDocument?.children?.[0]?.nodeName;
    //                 resolve(!nodeName || nodeName === "HTML");
    //             })
    //             .catch(() => {
    //                 const nodeName = iframe?.contentDocument?.children?.[0]?.nodeName;
    //                 // 如果是Mozilla的浏览器，可能会因为CORS问题导致下载失败，此时直接判断内容是否为Error
    //                 if (navigator.userAgent.includes("Firefox/")) {
    //                     return resolve(nodeName !== "Error");
    //                 }

    //                 resolve(false);
    //             })
    //     } else {
    //         // iframe的内容为空，说明是跨域的url，且下载失败
    //         resolve(false);
    //     }
    // }, 1500);
}

export type DownloadResult = {
    url: string;
    success: boolean;
    loading: boolean;
};
