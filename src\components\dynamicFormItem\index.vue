<template>
    <div>
        <div v-for="(item, key) in data" :key="item.renderId || 'undefined' + key" class="dynamic-form-row">
            <span v-if="key !== 0" class="relation-style">{{ relationWord }}</span>
            <el-form-item
                v-for="(field, fieldIndex) in fields"
                :key="fieldIndex"
                :label="field.label"
                :label-width="labelWidth"
                :prop="`${fatherProp}.${key}.${field.prop}`"
                :rules="rules(field)"
                :style="field.style"
            >
                <template slot="label">
                    <div v-if="labelShow" class="label-box">
                        {{ field.label }}
                    </div>
                </template>
                <div class="flex-row-style">
                    <el-cascader
                        v-if="field.type === 'cascader'"
                        v-model="item[field.prop]"
                        :options="listAry(field)"
                        :props="{ value: 'dictValue', label: 'dictName', multiple: true }"
                        collapse-tags
                    />
                    <el-input
                        v-if="field.type === 'input'"
                        v-model="item[field.prop]"
                        clearable
                        :placeholder="field.placeholder"
                        @input="handleInput($event, field, item)"
                        @blur="handleBlur(field, item)"
                    />
                    <el-select
                        v-if="field.type === 'select'"
                        v-model="item[field.prop]"
                        clearable
                        :placeholder="field.placeholder"
                    >
                        <el-option
                            v-for="optionItem in listAry(field)"
                            :key="optionItem.value"
                            :label="optionItem.label"
                            :value="optionItem.value"
                        />
                    </el-select>
                    <span v-if="field.suffix" class="suffix-style">{{ field.suffix }}</span>
                </div>
            </el-form-item>
            <!--            v-if="key + 1 === data.length"-->
            <i
                v-if="isShowAdd(key)"
                class="icon-action el-icon-plus"
                :class="{ 'is-disabled': disabled }"
                @click="handleAutoFormAction('add', data)"
            />
            <i
                v-if="isShowRemove(key)"
                class="icon-action el-icon-minus"
                :class="{ 'is-disabled': disabled }"
                @click="handleAutoFormAction('delete', data, key)"
            />
        </div>
        <el-button v-if="!data.length" type="text" @click="handleAutoFormAction('add', data)">新增</el-button>
    </div>
</template>

<script>
import { get, sortBy } from "lodash-es";
import { mapGetters } from "vuex";

export default {
    props: {
        data: {
            type: Array,
            default: () => [],
        },
        templateData: {
            type: Object,
            required: true,
            default: null,
        },
        fields: {
            type: Array,
            default: () => [],
        },
        labelWidth: {
            type: String,
            default: "",
        },
        // 公共字段组成的对象
        commonObj: {
            type: Object,
            default: null,
        },
        // 不允许空数据
        notAllowEmpty: {
            type: Boolean,
            default: true,
        },
        // 父级prop,用于动态表单校验
        fatherProp: {
            type: String,
            default: "",
        },
        labelShow: {
            type: Boolean,
            default: true,
        },
        relationWord: {
            type: String,
            default: "或",
        },
        btnIsLast: {
            type: Boolean,
            default: false,
        },
        disabled: {
            type: Boolean,
            default: false,
        },
        isRemoveFirst: {
            type: Boolean,
            default: false,
        },
    },
    computed: {
        ...mapGetters({
            optionsMap: "optionsMap",
        }),
        listAry() {
            return item => {
                let ary = null;
                if (item.list) {
                    ary = item.list;
                } else if (item.listFilter && typeof item.listFilter === "function") {
                    ary = item.listFilter(item);
                } else if (item.listProp) {
                    ary = get(this.optionsMap, item.listProp, []);
                } else {
                    ary = get(this.optionsMap, item.prop, []);
                }

                if (
                    item.listFilterValue &&
                    item.listFilterValue instanceof Array &&
                    item.listFilterValue.length
                ) {
                    const res = ary.map(filterItem => {
                        return {
                            label: filterItem.label,
                            value: filterItem.value,
                            disabled: !item.listFilterValue.includes(filterItem.value),
                        };
                    });
                    return sortBy(res, "disabled");
                }
                return ary;
            };
        },
        /**
         * 验证规则
         */
        rules() {
            return item => {
                let ary = [];
                if (item.required) {
                    const message = `${item.type === "select" ? "请选择" : "请输入"}${
                        item.label ? item.label : ""
                    }`;
                    ary.push({ required: true, message: message });
                }

                if (item.inputType === "number") {
                    ary.push({ validator: this.checkNumber });
                }

                if (item.rules && item.rules instanceof Array && item.rules.length) {
                    ary = ary.concat(item.rules);
                }

                return ary;
            };
        },
    },
    created() {
        if (this.notAllowEmpty && !this.data.length) {
            this.handleAutoFormAction("add", this.data);
        }
    },
    methods: {
        /**
         * 处理动态表单变化
         */
        handleAutoFormAction(type, form, index) {
            if (this.disabled) {
                return;
            }

            if (type === "delete") {
                form.splice(index, 1);
                return;
            }

            let info = null;
            if (this.commonObj) {
                info = Object.assign({}, this.templateData, this.commonObj);
            } else {
                info = this.templateData;
            }

            // 有些默认值是需要绑定成函数形式，比如说需要在某个接口回调后获取的值，通过计算获取的值
            Object.keys(info).forEach(keyName => {
                if (typeof info[keyName] !== "function") {
                    return;
                }

                info[keyName] = info[keyName]();
            });
            form.push({ ...info, renderId: +new Date() });
        },
        /**
         * 处理输入
         */
        handleInput(val, item, form) {
            if (item.input && typeof item.input === "function") {
                item.input(form);
            }
        },
        /**
         * 处理失焦
         */
        handleBlur(item, form) {
            if (item.inputType === "number") {
                form[item.prop] = Number(form[item.prop]);
            }
        },
        /**
         * 检查数否是数字
         */
        checkNumber(rule, value, callback) {
            if (isNaN(Number(value))) {
                return callback(new Error("请输入数字"));
            }

            return callback();
        },
        /**
         * 是否展示加号
         */
        isShowAdd(index) {
            if (!this.btnIsLast) {
                return true;
            }

            if (index === this.data.length - 1) {
                return true;
            }

            return false;
        },
        /**
         * 是否展示删除
         */
        isShowRemove(key) {
            if (this.isRemoveFirst) {
                return true;
            }

            if (!this.btnIsLast && (key < this.data.length - 1 || this.data.length > 1)) {
                return true;
            }

            if (this.btnIsLast && this.data.length > 1 && key === this.data.length - 1) {
                return true;
            }

            return false;
        },
    },
};
</script>

<style scoped lang="scss">
.dynamic-form-row {
    display: flex;
    flex-direction: row;
    position: relative;

    ::v-deep .el-form-item {
        .el-form-item__label {
            &:before {
                content: "";
            }
        }
    }
}

.icon-style {
    line-height: 32px;
    color: #ff9831;
    margin: 0 10px;
}

.suffix-style {
    flex-shrink: 0;
    margin-left: 10px;
}

.icon-action {
    width: 20px;
    height: 20px;
    background: #ffffff;
    border-radius: 2px;
    border: 1px solid rgba(0, 0, 0, 0.15);
    margin: 6px 10px 0 10px;
    line-height: 20px;
    text-align: center;
    cursor: pointer;

    &.is-disabled {
        cursor: not-allowed;
        background: #e4e7ed;
    }
}

.relation-style {
    position: absolute;
    color: #909399;
    font-size: 14px;
}
</style>
