import { Component, Ref, Vue, Watch } from "vue-property-decorator";
import NestedMenu from "./components/nestedMenu.vue";
import type { CurrentMenu, MenuListItem } from "./components/statistics.d";
import { SearchParams as SearchParamsRank } from "@/types/statistics/rank";
import { SearchParams as SearchParamsUsage } from "@/types/statistics/usage";
import { SearchParams as SearchParamsUser } from "@/types/statistics/user";
import SearchBarUsage from "./components/usage/components/SearchBar.vue";
import SearchBarRank from "./components/rank/components/SearchBar.vue";
import SearchBarUser from "./components/user/components/SearchBar.vue";
import { StatisticsModule } from "@/store/modules/statistics";
import { ProductModule } from "@/store/modules/ncdn/nproduct";
import { nUserModule } from "@/store/modules/nuser";
import i18n from "@/i18n";
import { ScaleModule } from "@/store/modules/scale";
import { defineAsyncComponent } from "vue";
import LoadingComponent from "./components/loading.vue";
import { TabMenuModule } from "@/store/modules/tabMenu";
import { DomainParamEnum } from "@/store/config";
import { SecurityAbilityModule } from "@/store/modules/securityAbility";
import { isTimeInMonth } from "@/utils";
import { LabelModule } from "@/store/modules/label";
import { DomainModule } from "@/store/modules/domain";

type SearchParams = SearchParamsRank | SearchParamsUsage | SearchParamsUser;
export type MainMenu = 'usage' | 'rank' | 'user';

@Component({
    components: {
        NestedMenu,
        SearchBarUsage,
        SearchBarRank,
        SearchBarUser,
    }
})
export default class EntryMixin extends Vue {
    currentMenu: CurrentMenu<MainMenu> = {
        main: "usage",
        sub: this.$route.name === "statistics.dcdn" ? "BandwidthFlowWhole" : "BandwidthFlow",
    }
    denyChildGetDataBeforeInit = {
        usage: true,
        rank: true,
        user: true,
    }
    getChart: any = "";
    childPanelMounted = false;
    isLoadingBasicConfig = true; // 是否正在加载基础配置

    @Ref("childPane") readonly childPane: any;
    @Ref("search") readonly searchRef: any;

    getData(params: {
        searchParams1: SearchParams
        searchParams2?: SearchParams,
        triggerByUser: boolean,
        scaleDomainList?: string[]
    }) {
        this.$nextTick(() => {
            if (params.triggerByUser || !this.denyChildGetDataBeforeInit[this.currentMenu.main]) {
                // 无可选域名时，拦截请求
                if (this.searchRef?.domainList.length === 0) {
                    this.$message.error(`${this.$t("statistics.common.chart.errMsg[0]")}`);
                    return;
                }

                // 校验时间范围是否在一个月内
                if (
                    (
                        params.searchParams1 &&
                        !isTimeInMonth(params.searchParams1.startTime, params.searchParams1.endTime)
                    ) ||
                    (
                        params.searchParams2 &&
                        !isTimeInMonth(params.searchParams2.startTime, params.searchParams2.endTime)
                    )
                ) {
                    return this.$message.error(this.headerTip);
                }

                this.childPane?.childPaneStartLoading();
                ScaleModule.GetScale(params.scaleDomainList).then(() => {
                    this.childPane?.beforeGetData(params.searchParams1, params.searchParams2);
                });
            }
        });
    }

    onChildPanelMounted(val: boolean) {
        this.childPanelMounted = val;
    }

    // 子组件下载数据
    download() {
        this.childPane.beforeDownload();
    }

    onSearchBarInitialized() {
        this.denyChildGetDataBeforeInit[this.currentMenu.main] = false;
    }

    get searchBarLoading() {
        return this.isLoadingBasicConfig || DomainModule[StatisticsModule.currentAction].loading;
    }
    get headerText() {
        return this.defaultMenuList.find(item => item.prop === this.currentMenu.main)?.name;
    }
    get currentIsDedicatedLine() {
        return ['PrivateNetworkAccelerator', 'PrivateNetworkAcceleratorWhole'].includes(this.currentMenu.sub || '')
    }

    get headerTip() {
        return {
            // 虚拟专线目前底层只支持最近六个月的数据查询
            usage: this.currentIsDedicatedLine ? `${this.$t("statistics.usageQuery.headerTip")}` : `${this.$t("statistics.dcdn.headerTip")}`,
            rank: `${this.$t("statistics.rank.headerTip")}`,
            user: `${this.$t("statistics.user.headerTip")}`,
        }[this.currentMenu.main];
    };

    // 各 tab 页面中对于 searchBar 日期选择的配置
    get getPeriodOptions() {
        const options = ["0", "1", "7", "30", "-1"];
        // 热门分析时间快捷选项按tab区分
        if (this.getMainMenu === "rank") {
            if (["DomainRank", "TopIp"].includes(this.getSubMenu)) {
                return options;
            }
            return ["0", "1", "-1"];
        }

        return options;
    }

    get defaultMenuList(): MenuListItem[] {
        const menuList: MenuListItem[] = [
            {
                name: i18n.t("statistics.common.usage") as string,
                prop: "usage",
                children: [
                    {
                        name: i18n.t("statistics.common.tab[0]") as string,
                        prop: "BandwidthFlow",
                    }, {
                        name: i18n.t("statistics.common.tab[1]") as string,
                        prop: "Miss",
                    }, {
                        name: i18n.t("statistics.common.tab[2]") as string,
                        prop: "Request",
                    }, {
                        name: i18n.t("statistics.common.tab[3]") as string,
                        prop: "Hit",
                    }, {
                        name: i18n.t("statistics.common.tab[4]") as string,
                        prop: "StatusCode",
                    }, {
                        name: i18n.t("statistics.common.tab[5]") as string,
                        prop: "BackToOriginStatusCode",
                    }, {
                        name: i18n.t("statistics.common.tab[6]") as string,
                        prop: "PvUv",
                    }, {
                        name: i18n.t("statistics.common.tab[7]") as string,
                        prop: "Provider",
                    }, {
                        name: i18n.t("statistics.common.tab[8]") as string,
                        prop: "DownloadSpeed",
                    }, {
                        name: i18n.t("statistics.common.tab[9]") as string,
                        prop: "PrivateNetworkAccelerator",
                    }
                ]
            }, {
                name: i18n.t("statistics.common.rank") as string,
                prop: "rank",
                children: [
                    {
                        name: i18n.t("statistics.rank.common.tab[0]") as string,
                        prop: "HotUrl",
                    }, {
                        name: i18n.t("statistics.rank.common.tab[1]") as string,
                        prop: "HotUrlMiss",
                    }, {
                        name: i18n.t("statistics.rank.common.tab[2]") as string,
                        prop: "HotReferer",
                    }, {
                        name: i18n.t("statistics.rank.common.tab[3]") as string,
                        prop: "DomainRank",
                    }, {
                        name: i18n.t("statistics.rank.common.tab[4]") as string,
                        prop: "TopIp",
                    }
                ]
            }, {
                name: i18n.t("statistics.common.user") as string,
                prop: "user",
            }
        ]

        // 下载加速不显示
        if (!this.isSpeedTabShow && menuList[0].children) {
            menuList[0].children = menuList[0].children.filter(item => item.prop !== "DownloadSpeed");
        }

        // 虚拟专线（高性能网络）不显示
        if (!this.isDedicatedLineShow && menuList[0].children) {
            menuList[0].children = menuList[0].children.filter(item => item.prop !== "PrivateNetworkAccelerator");
        }

        // 国际站默认不展示 地区运营商 和 下载速度
        if (this.isCtclouds && menuList[0].children) {
            menuList[0].children = menuList[0].children.filter(item => item.prop !== "DownloadSpeed");
        }

        // 国际站默认不展示 top客户端ip
        if (this.isCtclouds && menuList[1].children) {
            menuList[1].children = menuList[1].children.filter(item => item.prop !== "TopIp");
        }

        // 国际站不展示用户分析
        if (this.isCtclouds) {
            menuList.pop();
        }

        return menuList;
    }

    get getMainMenu() {
        return this.currentMenu.main || 'usage';
    }

    get getSubMenu() {
        return this.currentMenu.sub || '';
    }

    get getAuthTabs() {
        let usageTab: { [key: string]: boolean },
            rankTab: { [key: string]: boolean };

        if (window.__POWERED_BY_QIANKUN__) {
            usageTab = TabMenuModule[DomainParamEnum.AocdnUsageTab];
            rankTab = TabMenuModule[DomainParamEnum.AocdnRankTab];
        } else {
            usageTab = TabMenuModule[DomainParamEnum.UsageTab];
            rankTab = TabMenuModule[DomainParamEnum.RankTab];

        }

        return {
            BandwidthFlow: usageTab.bandwidthFlow,
            BandwidthFlowWhole: usageTab.bandwidthFlowWhole,
            Miss: usageTab.miss,
            MissWhole: usageTab.missWhole,
            Request: usageTab.request,
            RequestWhole: usageTab.requestWhole,
            Hit: usageTab.hit,
            StatusCode: usageTab.statusCode,
            BackToOriginStatusCode: usageTab.backToOriginStatusCode,
            PvUv: usageTab.pvuv,
            Provider: usageTab.provider,
            DownloadSpeed: usageTab.downloadSpeed,
            PrivateNetworkAccelerator: usageTab.privateNetworkAccelerator,
            PrivateNetworkAcceleratorWhole: usageTab.privateNetworkAcceleratorWhole,
            HotUrl: rankTab.hotUrl,
            HotUrlMiss: rankTab.hotUrlMiss,
            HotReferer: rankTab.hotReferer,
            DomainRank: rankTab.domainRank,
            TopIp: rankTab.topIp,
        }
    }

    get geCtiamAuthTabs() {
        const ctiamTab: { [key: string]: boolean } = TabMenuModule[DomainParamEnum.Ctiam];
        return ctiamTab;
    }
    get getMenuList(): MenuListItem[] {
        return [];
    }

    @Watch("currentMenu", { deep: true, immediate: true })
    onCurrentMenuChange() {
        this.getChart = this.getChartComponent()
    }

    getChartComponent() {
        if (this.getMenuList.length <= 0) return;

        const loaderComponent = this.currentMenu.sub
            ? `./components/${this.getMainMenu}/components/${this.getSubMenu}.vue`
            : `./components/${this.getMainMenu}/index.vue`;
        return defineAsyncComponent({
            loader: this.currentMenu.sub
                ? () => import(`./components/${this.getMainMenu}/components/${this.getSubMenu}.vue`)
                : () => import(`./components/${this.getMainMenu}/index.vue`),
            delay: 1000,
            // timeout需要设置的大一点，否则结束后即使组件加载成功，也不会替换掉loading组件
            timeout: 1000 * 60 * 10,
            errorComponent: LoadingComponent,
            loadingComponent: LoadingComponent,
            onError: (err, retry) => {
                console.error(err);
                const loaderComponentOnRetry = this.currentMenu.sub
                    ? `./components/${this.getMainMenu}/components/${this.getSubMenu}.vue`
                    : `./components/${this.getMainMenu}/index.vue`;
                if (loaderComponent === loaderComponentOnRetry) {
                    setTimeout(() => {
                        retry();
                    }, 2000);
                }
            },
        });
    }
    /**
     * 过滤并授权CTIAM菜单选项
     * 此函数用于根据CTIAM域参数筛选当前菜单列表中的项，并返回需要展示的菜单项的授权状态
     *
     * @param currentMenu 当前菜单列表，类型为MenuListItem数组，每个菜单项包含prop属性，用于标识菜单项
     * @returns 返回一个布尔值数组，表示每个菜单项是否被授权展示
     */
    filterCtiamAuthTabs(currentMenu: MenuListItem[]) {
        // 获取CTIAM的标签授权状态，使用DomainParamEnum.Ctiam来定位到TabMenuModule中的对应部分
        const ctiamTab: { [key: string]: boolean } = TabMenuModule[DomainParamEnum.Ctiam];
        // 从 ctiamTab 中取出值为 true 的 tab
        const ctiamTabList = Object.keys(ctiamTab).filter(key => ctiamTab[key] === true);
        // 遍历当前菜单列表，检查每个菜单项是否在CTIAM标签列表中，并返回相应的授权状态
        const data = currentMenu.filter(item => {
            // 全站加速的标识需要加上Whole后缀
            const prop = this.$route.name === "statistics.dcdn" ? `${item.prop}Whole` : item.prop;
            // 如果菜单项的标识包含在CTIAM标签列表中，则返回该菜单项的授权状态
            if (ctiamTabList.includes(prop)) {
                return this.$route.name === "statistics.dcdn"
                    ? ctiamTab[`${item.prop}Whole`]
                    : ctiamTab[item.prop];
            }
        })
        return data;
    }

    filterAuthTab(authTabs: { [key: string]: boolean }, currentMenu: MenuListItem[]) {
        const authTabsList = Object.keys(authTabs);

        // 一级tab不过滤
        return currentMenu.map(item => {
            const { children } = item;
            if (!children) return { ...item };

            return {
                ...item,
                children: children.filter(tab => {
                    if (authTabsList.includes(tab.prop)) {
                        if (authTabs[tab.prop]) {
                            return true;
                        } else {
                            return false;
                        }
                    } else {
                        return true;
                    }
                })
            }
        }).filter(item => {
            if (item.prop === "user") return true;
            else {
                if (item.children?.length) return true;
                else return false;
            }
        });
    }

    setCanAuthorizedTab(list: MenuListItem[]) {
        const authTabs = {
            usage: (list.filter(itm => itm.prop === "usage")?.[0]?.children || []).map(itm => ({ main: "usage", sub: itm.prop })),
            rank: (list.filter(itm => itm.prop === "rank")?.[0]?.children || []).map(itm => ({ main: "rank", sub: itm.prop })),
        }

        if (authTabs.usage.length) {
            this.currentMenu.main = "usage";
            this.currentMenu.sub = authTabs.usage[0].sub;
        } else if (authTabs.rank.length) {
            this.currentMenu.main = "rank";
            this.currentMenu.sub = authTabs.rank[0].sub;
        } else {
            this.currentMenu = { main: "user", sub: "" };
        }
    }

    /**
     * 原有的路由beforeEnter钩子中的逻辑迁移到created钩子中
     * 用于解决切换页面时，由于beforeEnter阻塞导致页面还显示上一个页面的问题
     * 迁移后，通过isLoadingBasicConfig控制
     */
    async loadBasicConfigData() {
        this.isLoadingBasicConfig = true;
        try {
            !nUserModule.isFcdnCtyunCtclouds && LabelModule.GetLabelList({ cache: false, action: "" });
            StatisticsModule.GetAreaList();
            StatisticsModule.GetIspList();
            if (window.__POWERED_BY_QIANKUN__) {
                TabMenuModule.GetAuthList(DomainParamEnum.AocdnUsageTab);
                TabMenuModule.GetAuthList(DomainParamEnum.AocdnRankTab);
                SecurityAbilityModule.GetIsBilling(); // 仅a1用到
            } else {
                TabMenuModule.GetAuthList(DomainParamEnum.UsageTab);
                TabMenuModule.GetAuthList(DomainParamEnum.RankTab);
                // fcdn-ctyun（国内+国际） 接入 ctiam 体系
                if (nUserModule.isFcdnCtyunCtclouds) {
                    TabMenuModule.GetCtiamAuthList(DomainParamEnum.Ctiam); // 获取一级菜单tab权限（用量分析、热门分析、用户分析...）这层级
                }
            }
            // 这里 to.name==="statistics" 等同于 window.isPoweredByQiankun; fcdn 产品列表在App.vue中已经阻塞获取了，所以此处不再请求
            this.$route.name === "statistics" && StatisticsModule.nGetAllProduct();
            await Promise.all([
                StatisticsModule.GetBasicConfig(), StatisticsModule.GetChildAccount(),
            ]);
            StatisticsModule.GetTabConfig();
            ProductModule.nGetProductInfo();
        } catch (err) {
            console.error(err);
        } finally {
            this.isLoadingBasicConfig = false;
        }
    }

    created() {
        this.loadBasicConfigData();
    }

    get isSpeedTabShow() {
        return StatisticsModule.speedAccount;
    }

    get isDedicatedLineShow() {
        if (window.__POWERED_BY_QIANKUN__) {
            // a1由SecurityAbilityModule.highPerfNetAct进行判断
            return SecurityAbilityModule.highPerfNetAct;
        }

        return ProductModule.dedicatedLine;
    }

    get isCtclouds() {
        return nUserModule.isCtclouds;
    }

    get getSearchBar() {
        const searchBarMap: { [key: string]: any } = {
            usage: SearchBarUsage,
            rank: SearchBarRank,
            user: SearchBarUser
        }
        return searchBarMap[this.getMainMenu];
    }

    get useWrapper() {
        return this.getMainMenu !== 'user';
    }
}
