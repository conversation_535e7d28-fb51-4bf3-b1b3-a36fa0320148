<template>
    <div>
        <div v-if="!isCreate">
            <div class="header-button">
                <el-button
                    :disabled="originList.length >= 60 || isDetail"
                    type="primary"
                    size="mini"
                    @click="addOriginByDialog"
                >
                    {{ $t("domain.add2") }}
                </el-button>
                <span class="tips" v-html="tooltips"></span>
            </div>
            <el-table :empty-text="$t('common.table.empty')" :data="originList">
                <el-table-column :label="$t('domain.create.number')" type="index"></el-table-column>
                <el-table-column :label="$t('domain.create.originServer')" prop="origin"></el-table-column>
                <el-table-column :label="$t('domain.create.level')">
                    <template #default="{ row }">
                        {{ levelMap[row.roleLevel] }}
                    </template>
                </el-table-column>
                <el-table-column
                    v-if="isWhole || isAdvanced"
                    :label="$t('domain.create.weight')"
                    prop="weight"
                ></el-table-column>
                <el-table-column :label="$t('common.table.operation')">
                    <template #default="{ $index }">
                        <el-button type="text" @click="editOriginByDialog($index)">{{
                            $t("domain.modify")
                        }}</el-button>
                        <el-button type="text" @click="deleteOrigin($index)">{{
                            $t("domain.delete")
                        }}</el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <div v-else>
            <div class="list">
                <el-row v-for="(c, index) in originList" :key="index" :gutter="15">
                    <el-col :span="6">
                        <el-input v-model="c.origin" size="small" />
                    </el-col>
                    <el-col :span="6">
                        <span class="label">{{ $t("domain.create.level") }}</span>
                        <el-select :disabled="index === 0" v-model="c.roleLevel" size="small">
                            <el-option :label="$t('domain.create.primary')" value="master1" />
                            <el-option :label="$t('domain.create.secondary')" value="slave1" />
                            <el-option
                                v-if="isAdvanced"
                                :label="$t('simpleForm.origin.slave.itm1')"
                                value="slave2"
                            />
                            <el-option
                                v-if="isAdvanced"
                                :label="$t('simpleForm.origin.slave.itm2')"
                                value="slave3"
                            />
                            <el-option
                                v-if="isAdvanced"
                                :label="$t('simpleForm.origin.slave.itm3')"
                                value="slave4"
                            />
                            <el-option
                                v-if="isAdvanced"
                                :label="$t('simpleForm.origin.slave.itm4')"
                                value="slave5"
                            />
                        </el-select>
                    </el-col>
                    <el-col v-if="isWhole || isAdvanced" :span="6">
                        <span class="label">{{ $t("domain.create.weight") }}</span>
                        <el-input v-model="c.weight" size="small" />
                    </el-col>
                    <el-button
                        v-if="isXos"
                        style="float: left; line-height: 36px"
                        type="text"
                        @click="openOSSDialog(index)"
                    >
                        {{ $t("simpleForm.origin.addOSS") }}
                    </el-button>
                    <el-col :span="1" v-if="index > 0">
                        <el-button @click="delOrigin(index)" icon="el-icon-remove-outline" type="text" />
                    </el-col>
                </el-row>
            </div>
            <el-button type="text" @click="addOrigin" :disabled="originList.length >= 60">
                {{ $t("domain.create.addOrigin") }}
            </el-button>
            <div class="tooltips" v-html="tooltips"></div>
        </div>

        <!-- 编辑时修改弹窗 -->
        <el-dialog
            :title="
                originForm.editRowIndex === undefined
                    ? $t('domain.create.originServer-1')
                    : $t('domain.create.originServer-2')
            "
            :modal-append-to-body="false"
            :close-on-click-modal="false"
            :visible.sync="showDialog"
        >
            <el-form ref="originForm" :model="originForm" :rules="originRules">
                <el-form-item :label="$t('domain.create.originServer')" prop="origin">
                    <el-input v-model="originForm.origin" size="small" />
                </el-form-item>
                <el-form-item :label="$t('domain.create.level')" prop="roleLevel">
                    <el-select v-model="originForm.roleLevel" size="small">
                        <el-option :label="$t('domain.create.primary')" value="master1" />
                        <el-option :label="$t('domain.create.secondary')" value="slave1" />
                        <el-option
                            v-if="isAdvanced"
                            :label="$t('simpleForm.origin.slave.itm1')"
                            value="slave2"
                        />
                        <el-option
                            v-if="isAdvanced"
                            :label="$t('simpleForm.origin.slave.itm2')"
                            value="slave3"
                        />
                        <el-option
                            v-if="isAdvanced"
                            :label="$t('simpleForm.origin.slave.itm3')"
                            value="slave4"
                        />
                        <el-option
                            v-if="isAdvanced"
                            :label="$t('simpleForm.origin.slave.itm4')"
                            value="slave5"
                        />
                    </el-select>
                </el-form-item>
                <el-form-item v-if="isWhole || isAdvanced" :label="$t('domain.create.weight')" prop="weight">
                    <el-input
                        v-model="originForm.weight"
                        size="small"
                        :placeholder="$t('simpleForm.origin.numberPlaceholder')"
                    />
                </el-form-item>
                <el-form-item v-if="isXos">
                    <el-button type="text" @click="openOSSDialog">{{
                        $t("simpleForm.origin.addOSS")
                    }}</el-button>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="showDialog = false">{{ $t("common.dialog.cancel") }}</el-button>
                <el-button type="primary" @click="confirmDialog">{{ $t("common.dialog.submit") }}</el-button>
            </div>
        </el-dialog>
        <el-dialog
            :title="$t('simpleForm.origin.addOSS')"
            :modal-append-to-body="false"
            :close-on-click-modal="false"
            :visible.sync="showOSSDialog"
        >
            <el-form ref="ossForm" :model="ossForm" :rules="ossRules">
                <el-form-item :label="$t('simpleForm.origin.resourcePool')" prop="pool">
                    <el-select v-model="ossForm.pool">
                        <el-option v-for="pool in ossPoolList" :label="pool" :value="pool" :key="pool" />
                    </el-select>
                </el-form-item>
                <el-form-item :label="$t('domain.list.tableLabel2')" prop="domain">
                    <el-select v-model="ossForm.domain">
                        <el-option
                            v-for="domain in ossDomainList"
                            :label="domain"
                            :value="domain"
                            :key="domain"
                        />
                    </el-select>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="showOSSDialog = false">{{ $t("common.dialog.cancel") }}</el-button>
                <el-button type="primary" @click="confirmOSSDialog">{{
                    $t("common.dialog.submit")
                }}</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
// import mixin from "../../editor.mixin";
import mixin from "@/components/simpleform/minxin";
import { ipv4, ip, domain } from "@cdnplus/common/config/pattern";
// import config from "../../config";
let config;
import utils from "./utils";
import { reservedIp } from "@/config/npattern";

const { equalObject } = utils;
const weightPattern = "^([1-9][0-9]?|100)$";
const weightReg = new RegExp(weightPattern);
const urlReg = new RegExp(domain);
const reservedIpReg = new RegExp(reservedIp); // ipv4 中的保留 ip 地址

const defaultOrigin = {
    origin: "",
    roleLevel: "master1", // role + level 组合值
    weight: "10",
    // port: "80",
    protocol: "http",
};

// 权重算法因子
const roleScore = { master: 10000, slave: 20000 };

// 域名排序方法
function sortOrigin(a, b) {
    // 排序规则：先主后备、层级从小到大、同类型同层级的选中从大到小
    const _a = roleScore[a.role] + (+a.level || 1) * 1000 + (100 - +a.weight);
    const _b = roleScore[b.role] + (+b.level || 1) * 1000 + (100 - +b.weight);
    return _a - _b;
}

export default {
    name: "n-alogic-origin",
    mixins: [mixin],
    data() {
        return {
            originList: [],
            showDialog: false,
            tooltips: "",
            originForm: { ...defaultOrigin },
            isAdvanced: false, // 是否开启高级源
            domainName: "", // 基础配置中的加速域名
            isWhole: false, // 是否全站产品
            isXos: false, // 是否从XOS选择域名
            showOSSDialog: false,
            ossForm: { domain: "", pool: "" },
        };
    },
    computed: {
        ipReg() {
            // 根据后端配置判断是否需要关闭 ipv6 验证
            const useIpv6 = this.item?.type?.[this.keys.paramsKey]?.includes("ipv6=true");
            return new RegExp(useIpv6 ? ip : ipv4);
        },
        originRules() {
            return {
                origin: [
                    { required: true, message: this.$t("domain.detail.tip73"), trigger: "blur" },
                    {
                        validator: this.validateOrigin,
                        trigger: "blur",
                    },
                ],
                roleLevel: [{ required: true, message: this.$t("domain.detail.tip74"), trigger: "blur" }],
                weight: [
                    { required: true, message: this.$t("domain.detail.placeholder20"), trigger: "blur" },
                    {
                        pattern: "^([1-9][0-9]?|100)$",
                        message: this.$t("simpleForm.origin.numberValidate"),
                        trigger: "blur",
                    },
                ],
            };
        },
        ossRules() {
            return {
                pool: [{ required: true, message: this.$t("simpleForm.origin.selectOSS"), trigger: "blur" }],
                domain: [
                    { required: true, message: this.$t("simpleForm.origin.selectOSS"), trigger: "blur" },
                ],
            };
        },
        levelMap() {
            return {
                master1: this.$t("domain.create.primary"),
                slave1: this.$t("domain.create.secondary"),
                slave2: this.$t("simpleForm.origin.slave.itm1"),
                slave3: this.$t("simpleForm.origin.slave.itm2"),
                slave4: this.$t("simpleForm.origin.slave.itm3"),
                slave5: this.$t("simpleForm.origin.slave.itm4"),
            };
        },
        ossPoolList() {
            return [];
        },
        ossDomainList() {
            return [];
        },
    },
    methods: {
        init() {
            const dftValue = this.item.type[this.keys.dftValueKey];
            if (dftValue) {
                this.originList = this.decode(dftValue, "Array", true)
                    .sort(sortOrigin)
                    .map(item => {
                        return {
                            origin: item.origin,
                            weight: item.weight,
                            // 将 role + level 合并成 roleLevel 用于页面处理
                            roleLevel: item.role + (item.level || "1"),
                            // port: item.port || "80", // 没数据则默认80，避免后续逻辑出错
                            protocol: item.protocol,
                        };
                    });
            }
        },
        // 联动处理，根据追踪的表单项数据做处理
        handleRelative(relativeFormValue, relativeIndex, relativeFormSubmitValue) {
            // 追踪的 http_config 用于判断是否开启高级源
            if (relativeFormValue?.http_config) {
                this.isAdvanced = relativeFormValue.http_config?.origin_type === "poll";
                this.setModel(this.originList);
            }

            if (typeof relativeFormValue === "string") {
                const { pId, pDisplayValue } = relativeFormSubmitValue;
                // 追踪的域名
                if (pId === "domain") {
                    this.domainName = relativeFormValue;
                }
                // 追踪加速类型（全站加速特殊需求）
                if (pId === "product_type") {
                    this.isWhole = pDisplayValue === this.$t("common.productName.5");
                }
            }
        },
        addOrigin() {
            this.originList.push({ ...defaultOrigin });
        },
        delOrigin(index) {
            this.originList.splice(index, 1);
        },
        validateOrigin(rule, value, callback) {
            const { ipReg } = this;
            const { origin } = this.originForm;
            if (!ipReg.test(origin) && !urlReg.test(origin)) {
                callback(this.$t("domain.detail.tip89"));
            } else if (reservedIpReg.test(origin)) {
                callback(this.$t("domain.create.tip16"));
            }
            callback();
        },
        validateProcedure() {
            let result = {
                valid: true,
                msg: "",
            };
            let hasMaster = false;
            const originMap = {};
            const { ipReg } = this;

            // 源站地址不能和加速域名重复
            if (this.domainName && this.originList.some(o => o.origin === this.domainName)) {
                return Promise.resolve({
                    valid: false,
                    msg: this.$t("domain.create.tip13"),
                });
            }

            for (let index = 0; index < this.originList.length; index += 1) {
                const element = this.originList[index];
                //地址唯一
                if (originMap[element.origin]) {
                    result = {
                        valid: false,
                        msg: this.$t("domain.create.tip14"),
                    };
                    return Promise.resolve(result);
                } else {
                    originMap[element.origin] = true;
                }

                if (element.roleLevel === "master1") {
                    hasMaster = true;
                }

                if (!ipReg.test(element.origin) && !urlReg.test(element.origin)) {
                    result = {
                        valid: false,
                        msg: this.$t("domain.detail.tip89"),
                    };
                } else if (reservedIpReg.test(element.origin)) {
                    result = {
                        valid: false,
                        msg: this.$t("domain.detail.tip89"),
                    };
                }
                // 校验权重为1-100的整数，注：当开启高级源的时候才有必要校验权限字段
                if (!weightReg.test(element.weight)) {
                    result = {
                        valid: false,
                        msg: this.$t("domain.create.tip17"),
                    };
                }

                if (!result.valid) {
                    return Promise.resolve(result);
                }
            }
            if (!hasMaster) {
                result = {
                    valid: false,
                    msg: this.$t("domain.create.tip18"),
                };
            }
            return Promise.resolve(result);
        },
        async deleteOrigin(index) {
            await this.$confirm(
                this.$t("domain.create.tip34") +
                    this.originList[index].origin +
                    this.$t("domain.create.tip35"),
                this.$t("domain.delete"),
                {
                    confirmButtonText: this.$t("common.dialog.submit"),
                    cancelButtonText: this.$t("common.dialog.cancel"),
                    type: "warning",
                }
            );
            this.delOrigin(index);
        },
        addOriginByDialog() {
            this.showDialog = true;
            this.originForm = {
                editRowIndex: undefined,
                ...defaultOrigin,
            };
        },
        editOriginByDialog(index) {
            this.showDialog = true;
            this.originForm = {
                editRowIndex: index,
                ...this.originList[index],
            };
        },
        async confirmDialog() {
            await this.$refs.originForm.validate();
            const { editRowIndex, ...other } = this.originForm;
            if (editRowIndex !== undefined) {
                this.$set(this.originList, editRowIndex, { ...other });
            } else {
                this.originList.push({ ...other });
            }
            this.showDialog = false;
        },
        // 设置 model 值，对数据进行格式化
        setModel(val) {
            const { isAdvanced } = this;
            this.model = val.map(i => {
                // 注意：数据组织顺序要和源数据一致，避免检查修改时误判
                const rst = {
                    origin: i.origin,
                    weight: i.weight,
                    role: i.roleLevel.slice(0, -1), // 将 roleLevle 拆分成 role + level 提交
                    protocol: i.protocol, // 原封带回
                    // level: +i.roleLevel.slice(-1) || 1,
                    // port: +i.port, // 需求 int 类型
                };

                // 当 origin_type = poll 时 level 存在（表现为已开启高级源，后面需要调整实现）
                if (isAdvanced) {
                    rst.level = +i.roleLevel.slice(-1) || 1;
                }

                return rst;
            });
        },
        checkEqual(pValue) {
            // level=1或者为空时统一处理
            let defaultValue = this.item.type[config.keys.dftValueKey] || "";
            // 当 origin_type = poll 时 level 存在（表现为已开启高级源，后面需要调整实现）
            defaultValue = this.isAdvanced ? defaultValue : defaultValue.replace(/;level=1?/g, "");
            // 排序后再对比，避免数组的顺序原因导致内容不一致
            return equalObject(
                (this.decode(defaultValue, "Array", true) || []).sort(sortOrigin),
                (this.decode(pValue, "Array", true) || []).sort(sortOrigin)
            );
        },
        openOSSDialog(index) {
            Object.assign(this.ossForm, this.$options.data().ossForm);
            this.showOSSDialog = true;
            if (this.isCreate) this.originForm = this.originList[index];
        },
        async confirmOSSDialog() {
            await this.$refs.ossForm.validate();
            this.originForm.origin = this.ossForm.domain;
            this.showOSSDialog = false;
        },
    },
    watch: {
        originList: {
            handler(val) {
                this.setModel(val);
            },
            deep: true,
        },
        isAdvanced(val) {
            if (!val) {
                this.originList.forEach(item => {
                    item.weight = 10;
                    item.level = item.level?.startsWith("slave") ? "slave1" : "master1";
                });
            }
        },
    },
    created() {
        // this.isXos = paramsDecode.xos === "true"; // todo
        if (this.isCreate) this.originList.push({ ...defaultOrigin });
    },
};
</script>

<style lang="scss" scoped>
.header-button {
    .tips {
        margin-left: 10px;
    }
}
.list {
    & + .list {
        margin-top: 20px;
    }
    .label {
        margin-right: 10px;
    }
    // .label + div {
    //     width: calc(100% - 40px);
    // }
    .el-row {
        margin-bottom: 10px;
    }
}

// 弹层
::v-deep .el-dialog {
    width: 60%;
    min-width: 340px;
}
</style>
