// 可插拔配置

import { VuexModule, Module, Action, Mutation, getModule } from "vuex-module-decorators";
import store from "@/store";
import { getModulesStatus } from "@/config/url/base";
import { ctFetch } from "../../utils";

// 定义模块接口
interface ModuleStatus {
    module: string;
    enable: boolean;
}

@Module({
    dynamic: true,
    store,
    name: "configModules",
})
class ConfigModules extends VuexModule {
    // 前端维护的完整模块清单（默认都是开启状态）
    moduleList: ModuleStatus[] = [
        { module: "shared_host", enable: true },
        { module: "remote_sync_auth", enable: true },
    ];
    enabledModules: ModuleStatus[] = []; // 存储后端返回的需要关闭的模块
    modulesLoading = false;
    masterSwitch = true; // 总开关状态
    funcLocks: string[] = []; // 存储功能锁列表
    /**
     * 更新模块状态
     *
     * 该方法通过将后端的模块状态与前端维护的模块清单进行合并，生成一个新的已启用模块列表
     *
     * @param modules 后端返回的模块状态数组，包含每个模块的启用状态
     */
    @Mutation
    SET_MODULES_STATUS(modules: ModuleStatus[]): void {
        // 将后端返回的模块状态与前端维护的模块清单合并
        this.enabledModules = this.moduleList.map(item => {
            // 查找后端是否有对应的配置
            const serverConfig = modules.find(m => m.module === item.module);
            // 如果后端返回了该模块的配置，使用后端的enabled状态，否则保持默认开启
            return {
                module: item.module,
                enable: serverConfig ? serverConfig.enable : true
            };
        });
    }

    @Mutation
    SET_MODULES_LOADING(loading: boolean): void {
        this.modulesLoading = loading;
    }
    /**
     * Mutation类型的方法，用于设置masterSwitch的值
     *
     * @param enabled - 一个布尔值，指示是否启用masterSwitch
     * @returns 无返回值
     */
    @Mutation
    SET_MASTER_SWITCH(enabled: boolean): void {
        this.masterSwitch = enabled;
    }

    @Mutation
    SET_FUNC_LOCKS(funcNames: string[]): void {
        this.funcLocks = funcNames;
    }
    /**
     * 异步获取模块状态
     *
     * 此函数通过网络请求获取指定域名下的模块状态，包括主开关状态和各个模块的开关状态
     * 它首先设置模块加载状态为true，然后发送GET请求到指定的URL，请求成功后，
     * 更新总开关状态和模块状态，并将模块加载状态设置为false
     *
     * @param domain 域名，用于查询模块状态
     */
    @Action
    async fetchModulesStatus(domain: string): Promise<void> {
        // 设置模块加载状态为true
        this.SET_MODULES_LOADING(true);

        // 发送GET请求获取模块状态
        const res = await ctFetch<{
            masterSwitch: boolean;
            modules: ModuleStatus[];
        }>(getModulesStatus, {
            method: "GET",
            data: { domain }
        });

        // 设置总开关状态
        this.SET_MASTER_SWITCH(res.masterSwitch);
        // 合并模块状态
        this.SET_MODULES_STATUS(res.modules || []);
        // 设置模块加载状态为false
        this.SET_MODULES_LOADING(false);
    }
    /**
     * Gets the status of the master switch.
     *
     * This getter method is used to check if the master switch is enabled.
     * It returns a boolean value representing the status of the master switch.
     *
     * @returns {boolean} The status of the master switch, true if enabled, false otherwise.
     */
    get isMasterSwitchEnabled(): boolean {
        return this.masterSwitch;
    }

    /**
     * 获取一个函数，用于判断给定的模块是否启用
     *
     * 此属性设计为一个函数，而不是直接返回一个值，目的是为了在每次调用时都能根据当前状态动态计算结果
     * 这种设计模式允许我们在运行时根据某些条件（如主开关状态）来动态调整模块的启用状态
     *
     * @param module 模块名称，用于查询该模块是否启用
     * @returns 如果模块启用则返回 true，否则返回 false
     */
    get isModuleEnabled(): (module: string) => boolean {
        return (module: string) => {
            // 总开关关闭时，所有模块都返回 False
            if (!this.masterSwitch) {
                return false;
            }
            // 查找给定模块名称对应的启用状态
            const moduleStatus = this.enabledModules.find(m => m.module === module);
            // 如果找到了模块状态且其启用属性为 true，则返回 true；否则返回 false
            // 这里使用了空值合并运算符，以处理当模块不存在时的情况
            return moduleStatus?.enable ?? true;
        };
    }

    // 获取功能锁状态
    get isModuleLocked(): (moduleName: string) => boolean {
        // 返回一个函数，用于检查指定模块名称是否在功能锁列表中
        return (moduleName: string) => {
            // 如果模块名称存在于功能锁列表中，则返回true，表示该功能被锁定
            return this.funcLocks.includes(moduleName);
        };
    }

    /**
     * 获取当前所有功能锁的名称列表
     *
     * 此方法用于返回当前已知的所有功能锁的名称列表，这些名称可以用于进一步的操作或查询
     * 该方法不需要任何参数，并返回一个字符串数组，每个字符串代表一个功能锁的名称
     *
     * @returns {string[]} 功能锁名称列表
     */
    get allFuncLocks(): string[] {
        return this.funcLocks;
    }
}

export const ConfigModulesModule = getModule(ConfigModules);
