// 品牌色
$brand1: #3d73f5;
$brand2: #3d73f5;
// 文字
$black1: #292b32;
$black2: #595c65;
$black3: #babc93;
$black4: #b8b8b8;
// line颜色
$grey1: #ebebeb;
$grey2: #dbdbdb;
$grey3: #bfbfbf;
$grey4: #595959;
// 填充颜色
$grey5: #f6f8fb;
$grey6: #f7f7f7;
$grey7: #f2f2f2;
$grey8: #fafafa;
// 功能色
$green: #35b34a;
$green2: #edfcf1;
$green3: #edfcf1;
$red: #f5212d;
$red2: #ffa39e;
$red3: #fff1f0;
$yellow: #faad15;
$yellow2: #ffe58f;
$yellow3: #fffbe6;
$blue: #1890ff;
$blue2: #91d5ff;
$blue3: #e6f7ff;
$netural: #8a8c93;
$netural2: #f2f2f2;
$netural3: #f2f2f2;

@mixin fix-element {
    ::v-deep {
        // el-tab样式重写
        // .el-tabs__item {
        //     height: 34px;
        //     line-height: 34px;
        //     margin-bottom: 3px;
        //     &::before,
        //     &::after {
        //         content: "";
        //         display: inline-block;
        //         width: 15px;
        //     }
        // }
        // .el-tabs__nav-wrap::after {
        //     background-color: $grey2;
        //     height: 1px;
        // }
        // .el-tabs__active-bar {
        //     height: 3px;
        // }
        // .el-tabs--card {
        //     .el-tabs__item {
        //         margin-bottom: 0;
        //         &::before,
        //         &::after {
        //             content: "";
        //             display: none;
        //         }
        //     }
        //     & > .el-tabs__header {
        //         border-bottom-color: $grey2;
        //         .el-tabs__nav {
        //             border-radius: 0;
        //             border-color: $grey2;
        //         }
        //     }
        //     .el-tabs__item {
        //         line-height: 36px;
        //         height: 36px;
        //         min-width: 110px;
        //         text-align: center;
        //     }
        // }
        // // el-pagination样式重写
        // .el-pagination {
        //     color: $black1;
        //     font-weight: normal;
        //     .el-pagination__sizes .el-input {
        //         margin: 0;
        //     }
        //     .el-pager {
        //         li {
        //             border: 1px solid $grey2;
        //             padding: 0;
        //             min-width: 28px;
        //             line-height: 26px;
        //         }
        //         li + li {
        //             margin-left: 5px;
        //         }
        //         li.active {
        //             color: #fff;
        //             background-color: $brand1;
        //             border-color: $brand1;
        //         }
        //     }
        //     .btn-prev,
        //     .btn-next {
        //         min-width: 28px;
        //         padding: 0;
        //         border: 1px solid $grey2;
        //     }
        //     .btn-prev {
        //         margin-right: 5px;
        //     }
        //     .btn-next {
        //         margin-left: 5px;
        //     }
        //     button:disabled {
        //         background: $grey6;
        //     }
        //     .el-pagination__jump {
        //         margin-left: 10px;
        //         .el-pagination__editor {
        //             padding: 0;
        //             margin: 0 10px;
        //         }
        //     }
        //     &.el-pagination--small {
        //         .el-pagination__sizes {
        //             height: auto;
        //             line-height: normal;
        //         }
        //         button,
        //         .el-pager,
        //         .el-pagination__total,
        //         .el-pagination__jump {
        //             vertical-align: middle;
        //         }
        //         .el-pager li {
        //             line-height: 20px;
        //             min-width: 20px;
        //         }
        //         .el-pager li:last-child {
        //             border-color: $grey2;
        //         }
        //         .btn-prev,
        //         .btn-next {
        //             min-width: 20px;
        //         }
        //     }
        // }

        // // el-button样式重写
        // .el-button {
        //     &.el-button--mini,
        //     &.el-button--small,
        //     &.el-button--medium {
        //         border-radius: 2px;
        //         font-size: 14px;
        //     }
        //     &.el-button--mini {
        //         min-width: 60px;
        //         padding: 6px 10px;
        //     }
        //     &.el-button--small {
        //         min-width: 68px;
        //         padding: 8px 20px;
        //     }
        //     &.el-button--medium {
        //         min-width: 100px;
        //         padding: 10px 30px;
        //     }
        //     &.el-button--default {
        //         &:hover,
        //         &:focus {
        //             background-color: #fff;
        //             color: $brand1;
        //             border-color: $brand1;
        //         }
        //         &:active {
        //             background-color: #fff;
        //             color: #df752e;
        //             border-color: #df752e;
        //         }
        //         &.is-disabled,
        //         &.is-disabled:hover,
        //         &.is-disabled:focus {
        //             background-color: $grey6;
        //             border-color: $grey1;
        //             color: $black4;
        //         }
        //     }
        //     &.el-button--text {
        //         padding: 0;
        //         min-width: auto;
        //         color: $brand2;
        //         &:hover,
        //         &:active {
        //             color: $brand1;
        //         }
        //         &.is-disabled,
        //         &.is-disabled:hover,
        //         &.is-disabled:focus {
        //             color: $black4;
        //         }
        //     }
        // }

        // // el-input样式重写
        // .el-input {
        //     &.el-input--small {
        //         font-size: 14px;
        //     }
        //     .el-input__inner {
        //         border-radius: 2px;
        //         padding: 0 12px;
        //         &:hover {
        //             border-color: $grey4;
        //         }
        //         &:active,
        //         &:focus {
        //             border-color: $brand1;
        //         }
        //         &[readonly] {
        //             background-color: $grey6;
        //             border-color: $grey1;
        //         }
        //         &[disabled] {
        //             color: $black4;
        //             background-color: $grey6;
        //             border-color: $grey1;
        //         }
        //     }
        // }

        // // el-select样式重写
        // .el-select {
        //     .el-input__inner {
        //         &[readonly] {
        //             background-color: #fff;
        //             border-color: $grey2;
        //         }
        //     }
        //     .el-input .el-select__caret {
        //         height: auto;
        //     }

        //     .el-select__tags > span {
        //         width: 100%;

        //         & > span:first-child {
        //             display: inline-block;
        //             width: auto !important;
        //         }
        //     }
        // }

        // // el-table样式重写
        // .el-table {
        //     color: #2a2b32;
        //     & th.is-leaf,
        //     & td {
        //         border-bottom-color: #ebebeb;
        //     }
        //     .el-table-column--selection {
        //         .cell {
        //             text-overflow: initial;
        //         }
        //     }
        //     .el-table__header-wrapper {
        //         .cell {
        //             height: 20px;
        //             line-height: 20px;
        //             padding-left: 10px;
        //             padding-right: 10px;
        //         }
        //         thead {
        //             color: #2a2b32;
        //         }
        //         th {
        //             font-weight: bold;
        //             background-color: #f7f7f7;
        //             padding: 5px 0;
        //         }
        //     }
        //     .el-table__body-wrapper {
        //         td {
        //             padding: 16px 0;
        //         }
        //         .cell {
        //             line-height: 20px;
        //             padding-left: 10px;
        //             padding-right: 10px;
        //             i {
        //                 font-size: 16px;
        //             }
        //         }
        //     }
        // }

        // // el-tag样式重写
        // .el-tag {
        //     border-radius: 2px;
        //     &.el-tag--meduim {
        //         font-size: 14px;
        //     }
        //     &.el-tag--mini {
        //         border-radius: 10px;
        //         padding: 0 10px;
        //     }
        // }

        // // datepicker、timepicker、datetimepicker样式重写
        // .el-date-editor {
        //     .el-input__prefix {
        //         left: auto;
        //         right: 5px;
        //         color: #595959;
        //     }
        //     .el-input__suffix {
        //         position: absolute;
        //         right: 25px;
        //     }
        //     .el-input__inner {
        //         padding-right: 25px;
        //     }
        //     &.el-range-editor {
        //         .el-range__icon {
        //             position: absolute;
        //             right: 5px;
        //             color: #595959;
        //         }
        //         .el-range-input {
        //             text-align: left;
        //         }
        //     }
        //     .el-range__close-icon {
        //         position: absolute;
        //         right: 25px;
        //     }
        // }
        // .el-range-editor--small {
        //     .el-range-input {
        //         font-size: 14px;
        //     }
        //     &.el-date-editor--datetimerange {
        //         &.el-input,
        //         &.el-input__inner {
        //             width: 350px;
        //         }
        //         .el-range-input {
        //             width: 132px;
        //         }
        //     }
        //     &.el-date-editor--daterange {
        //         &.el-input,
        //         &.el-input__inner {
        //             width: 230px;
        //         }
        //         .el-range-input {
        //             width: 73px;
        //         }
        //     }
        //     &.el-date-editor--timerange {
        //         &.el-input,
        //         &.el-input__inner {
        //             width: 230px;
        //         }
        //         .el-range-input {
        //             width: 73px;
        //         }
        //     }
        //     .el-date-editor .el-range__close-icon {
        //         display: none;
        //     }
        // }

        // // cascader
        // .el-cascader {
        //     .el-input__inner {
        //         &[readonly] {
        //             background-color: #fff;
        //             border-color: $grey2;
        //             padding-right: 30px;
        //             min-width: 350px;
        //         }
        //     }
        // }

        // // radio
        // .el-radio {
        //     color: #292b32;
        //     .el-radio__inner {
        //         width: 16px;
        //         height: 16px;
        //         &::after {
        //             width: 8px;
        //             height: 8px;
        //         }
        //     }
        //     .el-radio__input.is-checked:not(.is-disabled) .el-radio__inner {
        //         background-color: #fff;
        //         &::after {
        //             background-color: $brand1;
        //         }
        //     }
        //     &.is-bordered {
        //         border-radius: 2px;
        //     }
        // }

        // // radiobutton
        // .el-radio-button:first-child .el-radio-button__inner {
        //     border-radius: 2px 0 0 2px;
        // }
        // .el-radio-button:last-child .el-radio-button__inner {
        //     border-radius: 0 2px 2px 0;
        // }
        // .el-radio-button.is-disabled .el-radio-button__inner {
        //     background-color: #f7f7f7;
        //     border-color: #ebebeb;
        //     color: #b8b8b8;
        // }
        // .el-radio-button--small .el-radio-button__inner {
        //     font-size: 14px;
        //     line-height: 20px;
        //     padding: 6px 20px;
        // }
        // // inputnumber
        // .el-input-number {
        //     .el-input-number__increase,
        //     .el-input-number__decrease {
        //         background-color: #fff;
        //     }
        //     .el-input-number__increase.is-disabled,
        //     .el-input-number__decrease.is-disabled {
        //         background-color: #f7f7f7;
        //         color: #b8b8b8;
        //     }
        //     &.el-input-number--small {
        //         width: 110px;
        //     }
        // }

        // // checkbox
        // .el-checkbox {
        //     .el-checkbox__input.is-checked + .el-checkbox__label {
        //         color: #606266;
        //     }
        // }
    }
}

// @mixin select-dropdown-patch {
//     & .el-select-dropdown__item,
//     &.is-multiple .el-select-dropdown__item {
//         height: 32px;
//         line-height: 32px;
//         padding: 0 12px;
//         color: $black1;
//         &.is-disabled {
//             color: #8a8c93;
//         }
//         &:hover {
//             background-color: $grey6;
//         }
//         &.selected {
//             background-color: #fff5ef;
//             font-weight: normal;
//         }
//     }
// }

// @mixin cascader-dropdown-patch {
//     .el-cascader-node {
//         color: #292b32;
//         padding-left: 12px;
//         .el-checkbox,
//         .el-radio {
//             margin-right: 10px;
//         }
//     }
//     .el-cascader-node.in-active-path,
//     .el-cascader-node.is-selectable.in-checked-path,
//     .el-cascader-node.is-active {
//         background-color: #fff5ef;
//         font-weight: normal;
//         .el-cascader-node__prefix {
//             color: $brand1;
//         }
//         .el-icon-check {
//             display: none;
//         }
//     }
//     .el-cascader-node:not(.is-disabled):hover,
//     .el-cascader-node:not(.is-disabled):focus {
//         background-color: #f7f7f7;
//     }
//     .el-cascader-node__label {
//         padding: 0 12px 0 0;
//     }
// }

// @mixin fix-global-element {
//     .el-select-dropdown {
//         @include select-dropdown-patch;
//     }
//     .el-popper.el-cascader__dropdown {
//         @include cascader-dropdown-patch;
//     }
// }
