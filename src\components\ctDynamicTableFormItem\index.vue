<template>
    <div class="ct-dynamic-table-form-item">
        <el-table ref="table" :data="data" v-bind="$attrs">
            <el-table-column
                v-for="(column, columnIndex) in fields"
                v-bind="column.config"
                :key="column.prop || columnIndex"
                :label="column.label"
                :prop="column.prop"
            >
                <template slot-scope="{ row, $index }">
                    <el-form-item
                        :prop="`${fatherProp}.${$index}.${column.prop}`"
                        label-width="0"
                        :rules="getRules(column)"
                        :style="column.style || { width: column.width || width }"
                        class="dynamic-item"
                    >
                        <template v-if="column.type === 'input'">
                            <el-input
                                v-model="row[column.prop]"
                                :placeholder="getPlaceholder(column, row)"
                                :disabled="wholeDisabled || isDisabled(column)"
                                @input="handleInput($event, column, row)"
                            />
                        </template>
                        <template v-else-if="column.type === 'select'">
                            <el-select
                                v-model="row[column.prop]"
                                :disabled="wholeDisabled || isDisabled(column)"
                                @change="handleChange($event, row, column)"
                            >
                                <el-option
                                    v-for="optionItem in getListAry(column)"
                                    :key="optionItem.value"
                                    :value="optionItem.value"
                                    :label="optionItem.label"
                                    :disabled="optionItem.disabled"
                                />
                            </el-select>
                        </template>
                    </el-form-item>
                </template>
            </el-table-column>
            <el-table-column label="操作">
                <template slot-scope="{ $index }">
                    <el-form-item>
                        <el-button type="text" @click="handleAutoFormAction('delete', $index)">
                            {{ deleteButtonText }}
                        </el-button>
                    </el-form-item>
                </template>
            </el-table-column>
        </el-table>
        <div class="button-box">
            <el-button type="text" icon="el-icon-plus" @click="handleAutoFormAction('add')">
                {{ addButtonText }}
            </el-button>
        </div>
    </div>
</template>

<script>
import { cloneDeep, get, has } from "lodash-es";
import { mapGetters } from "vuex";

export default {
    name: "index",
    props: {
        data: {
            type: Array,
            default: () => [],
        },
        fields: {
            type: Array,
            default: () => [],
        },
        // 模板数据
        templateData: {
            type: [Array, Object],
            default: () => {
                return {};
            },
        },
        // 父级prop,用于动态表单校验
        fatherProp: {
            type: String,
            required: false,
            default: "",
        },
        disabled: {
            type: [Boolean, Function],
            default: false,
        },
        width: {
            type: String,
            default: "",
        },
        addButtonText: {
            type: String,
            default: "新增",
        },
        deleteButtonText: {
            type: String,
            default: "删除",
        },
        // 不允许空数据
        notAllowEmpty: {
            type: Boolean,
            default: true,
        },
        maxLength: {
            type: Number,
            default: 0,
        },
    },
    computed: {
        ...mapGetters({
            optionsMap: "optionsMap",
        }),
        // 全模块禁用
        wholeDisabled() {
            if (typeof this.disabled === "function") {
                return this.disabled();
            }

            return this.disabled;
        },
    },
    methods: {
        /**
         * 处理动态表单变化
         */
        handleAutoFormAction(type, index) {
            if (this.wholeDisabled) {
                return;
            }

            if (type === "delete") {
                if (this.notAllowEmpty && this.data.length === 1) {
                    this.$message.error("不允许空数据");
                    return;
                }

                this.data.splice(index, 1);
                return;
            }

            if (this.maxLength && this.data.length >= this.maxLength) {
                this.$message.error(`最多添加${this.maxLength}条数据`);
                return;
            }

            const info = cloneDeep(this.templateData);

            // 有些默认值是需要绑定成函数形式，比如说需要在某个接口回调后获取的值，通过计算获取的值
            Object.keys(info).forEach(keyName => {
                if (typeof info[keyName] !== "function") {
                    return;
                }

                info[keyName] = info[keyName]();
            });

            // 观察element-ui内部源码，得知table-body中的代码采用render函数式编程，数据改变引起元素改变
            // 而非采用绑定v-for dom的形式，故跳过diff算法验证
            // 所以这里不需要提供唯一key值
            this.data.push(info);
            // this.data.push({ ...info, renderId: +new Date() });

            this.$nextTick(() => {
                this.$emit("add");
            });
        },
        /**
         * 获取校验规则
         */
        getRules(item) {
            if (!item.required && !item.rules) {
                return null;
            }

            let ary = [];
            if (item.required && item.prop) {
                const inputAry = ["input", "textarea"];
                const type = inputAry.includes(item.type) ? "请输入" : "请选择";
                ary.push({ required: true, message: type + item.label });
            }

            if (item.rules && item.rules instanceof Array) {
                ary = ary.concat(item.rules);
            }

            return ary;
        },
        /**
         * 获取提示语
         * @param item
         * @param form
         * @returns {string|*}
         */
        getPlaceholder(item, form) {
            if (!item.placeholder) {
                return "请输入";
            }

            if (typeof item.placeholder === "function") {
                return item.placeholder(form);
            }

            return item.placeholder;
        },
        /**
         * 获取列表展示
         */
        getListAry(item, index, rowForm) {
            let ary = null;
            if (item.list) {
                ary = item.list;
            } else if (item.listFilter && typeof item.listFilter === "function") {
                ary = item.listFilter({ index: index, item: item, form: rowForm });
            } else if (item.listProp) {
                ary = get(this.optionsMap, item.listProp, []);
            } else {
                ary = get(this.optionsMap, item.prop, []);
            }

            return ary;
        },
        /**
         * 是否禁用
         */
        isDisabled(item) {
            if (has(item, "disabled") && typeof item.disabled === "function") {
                return item.disabled(this.form);
            }

            return item.disabled;
        },
        /**
         * 处理输入
         */
        handleInput(val, item, form) {
            if (item.input && typeof item.input === "function") {
                item.input(form);
            }
        },
        /**
         * 处理变化
         */
        handleChange(val, row, item) {
            if (item.change && typeof item.change === "function") {
                item.change({ val, row });
            }
        },
    },
};
</script>

<style scoped lang="scss">
@import "./index.scss";
</style>
