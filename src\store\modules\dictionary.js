import Vue from "vue";
import { getDictionary } from "@/api";
import { get } from "lodash-es";
import { translateDataToTree } from "@/utils/utils";

const state = {
    optionsMap: {},
    propMap: {},
    multipleOptionsMap: {},
    multiplePropMap: {},
};

const getters = {
    optionsMap: state => {
        const map = {};
        for (const key in state.propMap) {
            const item = state.propMap[key];
            map[key] = get(state.optionsMap, item, []);
        }
        return map;
    },
    propsMap: state => {
        const map = getters.optionsMap(state);
        const newMap = {};
        for (const key in map) {
            const itemMap = {};
            const itemAry = map[key];
            itemAry.forEach(subItem => {
                itemMap[subItem.value] = subItem.label;
            });
            newMap[key] = itemMap;
        }
        return newMap;
    },
    multipleOptionsMap: state => {
        const map = {};
        for (const key in state.dictionary.multiplePropMap) {
            const item = state.dictionary.multiplePropMap[key];
            map[key] = get(state.dictionary.multipleOptionsMap, item, []);
        }
        return map;
    },
    multiplePropsMap: state => {
        const map = getters.multipleOptionsMap(state);
        const newMap = {};
        for (const key in map) {
            const itemMap = {};
            const itemAry = map[key];
            itemAry.forEach(subItem => {
                itemMap[subItem.value] = subItem.label;
            });
            newMap[key] = itemMap;
        }
        return newMap;
    },
};

const mutations = {
    ADD_OPTIONSMAP: (state, info) => {
        // 添加optionsMap为响应式，在引入页面可以通过计算属性得到响应式数据
        Vue.set(state.optionsMap, info.key, info.list);
    },
    SET_PROPMAP: (state, info) => {
        state.propMap = Object.assign({}, state.propMap, info);
    },
    SET_MULTIPLEPROPMAP: (state, info) => {
        state.multiplePropMap = info;
    },
};

const actions = {
    /**
     * 字典查询
     * @param context
     * @param params
     * @returns {Promise<void>}
     */
    queryPageDictionary(context, params) {
        const { commit } = context;
        const { data } = params;
        const propMap = {};
        const multiplePropMap = {};
        for (const key in data) {
            const value = data[key];
            const ary = key.split(",");
            const query = {
                root: ary[0],
                dictName: ary[1],
            };
            // 判断是否是数组
            if (value instanceof Array) {
                value.forEach(item => {
                    propMap[item] = key;
                });
            }

            // 判断为字符串
            if (typeof value === "string") {
                propMap[value] = key;
            }

            let isMulti = false;

            // 判断为对象
            if (Object.prototype.toString.call(value) === "[object Object]") {
                isMulti = get(value, "isMulti", false);
                propMap[value.prop] = key;
            }

            // 以接口请求参数作为map的唯一标识符，优化减少字典表接口请求;
            if (state.optionsMap[key]) {
                continue;
            }

            getDictionary(query).then(res => {
                let resAry = res.map(item => {
                    return {
                        label: item.dictName,
                        value: item.dictValue,
                        ...item,
                    };
                });

                const filterFunction = get(value, "filter");
                if (filterFunction && typeof filterFunction === "function") {
                    resAry = filterFunction(resAry);
                }

                if (!isMulti) {
                    const item = {
                        key: key,
                        list: resAry,
                    };
                    commit("ADD_OPTIONSMAP", item);
                    return;
                }

                const item = {
                    key: key,
                    list: translateDataToTree(resAry),
                    isMulti: true,
                    originList: resAry,
                };
                commit("ADD_OPTIONSMAP", item);
            });
        }
        commit("SET_PROPMAP", propMap);
        commit("SET_MULTIPLEPROPMAP", multiplePropMap);
    },
};

export default {
    state,
    getters,
    mutations,
    actions,
};
