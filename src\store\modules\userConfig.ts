// 客户购买版本配置信息
import { VuexModule, Module, Action, Mutation, getModule } from "vuex-module-decorators";

import store from "../index";
import { getVersionConfigs, getAllDomain } from "@/api";
import { errorHandler } from "../../utils";
import { get } from "lodash-es";

interface UserConfigInfo {
    leakTaskCount: string;
    leakSchedule: string;
    evenSecTaskCount: string;
    evenSecAllSchedule: string;
    evenSecIndexSchedule: string;
    contentSecWordTaskCount: string;
    contentSecWordIndexSchedule: string;
    availabilityTaskCount: string;
    availabilitySchedule: string;
    dnsTaskCount: string;
    dnsSchedule: string;
    purchaseType: string;
    productCode: string;
    status: string;
}

@Module({ dynamic: true, store, name: "userConfig" })
class UserConfig extends VuexModule {
    public isLoading: any = false;
    public userVersionInfo: any = {};
    public userDomains: any = [];

    get userVersion() {
        return this.userVersionInfo;
    }

    get userPurchaseType() {
        return get(this.userVersion, "purchaseType", "");
    }

    @Mutation
    private SET_USER_VERSION_INFO(info: UserConfigInfo) {
        this.userVersionInfo = info;
    }

    @Mutation
    private SET_IS_LOADING(isLoading: boolean) {
        this.isLoading = isLoading;
    }

    @Mutation
    private SET_USER_DOMAINS(data: any) {
        this.userDomains = data;
    }

    @Action
    public async getUserVersionInfo() {
        if (this.isLoading) {
            return;
        }

        if (Object.keys(this.userVersionInfo).length) {
            return;
        }

        this.SET_IS_LOADING(true);
        try {
            const res = await getVersionConfigs();
            this.SET_USER_VERSION_INFO(res);
        } catch (e) {
            errorHandler(e);
        }
        this.SET_IS_LOADING(false);
    }

    @Action({ rawError: true })
    public async getUserDomains() {
        if (this.userDomains && this.userDomains.length) {
            return;
        }

        return new Promise((resolve, reject) => {
            getAllDomain()
                .then(res => {
                    this.SET_USER_DOMAINS(res);
                    resolve(res);
                })
                .catch(err => {
                    reject(err);
                });
        });
    }
}

export const UserVersionModule = getModule(UserConfig);
