<template>
    <ct-dialog :title="tip" :visible.sync="visible" width="410px" @close="visible = false">
        {{ title }}
        <template #footer>
            <el-button @click="handleCancel">{{ cancel }}</el-button>
            <el-button plain @click="handleAbandon">{{ giveUpChange }}</el-button>
            <el-button type="primary" :loading="loading" @click="handleSubmit">{{ submitAndSave }}</el-button>
        </template>
    </ct-dialog>
</template>

<script>
import ctDialog from "@/components/ctDialog";
import { SecurityAbilityModule } from "@/store/modules/securityAbility";
import i18n from "@/i18n/index";

export default {
    name: "index",
    components: { ctDialog },
    data() {
        return {
            visible: false,
            loading: false,
            title: i18n.t("domain.editPage.tooltip10"),
            currentDomainInfo: null,
            submitFn: null,
            // 以下是上面html的翻译
            tip: i18n.t("domain.list.note"),
            cancel: i18n.t("domain.editPage.btn3"),
            giveUpChange: i18n.t("domain.editPage.btn5"),
            submitAndSave: i18n.t("domain.submit2"),
        };
    },
    methods: {
        /**
         * 处理提交
         */
        async handleSubmit() {
            if (!this.submitFn || typeof this.submitFn !== "function") {
                this.handleSubmitCallback();
                return;
            }

            const fnRes = this.submitFn(this);
            if (!fnRes) {
                this.$emit("cancel");
                return;
            }

            if (fnRes === true) {
                this.handleSubmitCallback();
                return;
            }

            if (typeof fnRes.then === "function") {
                this.loading = true;
                try {
                    const result = await fnRes;
                    // 适配async-await中使用try-catch 并且return了值的情况
                    if (result === false) {
                        this.$message.error(i18n.t("domain.editPage.tip25"));
                        this.$emit("cancel");
                        return;
                    }

                    this.handleSubmitCallback();
                    this.loading = false;
                } catch (e) {
                    this.$message.error(i18n.t("domain.editPage.tip25"));
                    this.loading = false;
                }
            }
        },
        /**
         * 提交回调
         */
        handleSubmitCallback() {
            this.visible = false;
            SecurityAbilityModule.SET_IS_EDIT(false);
            this.$emit("action", "confirm");
        },
        /**
         * 放弃更改
         */
        handleAbandon() {
            this.visible = false;
            SecurityAbilityModule.SET_IS_EDIT(false);
            this.$emit("action", "abandon");
            window.custom.emit("handleEditDomainConfig", false);
        },
        /**
         * 取消
         */
        handleCancel() {
            this.visible = false;
            this.$emit("close");
        },
    },
};
</script>

<style lang="scss" scoped>
::v-deep {
    .el-dialog__header {
        padding: 16px 12px;
    }
}
</style>
