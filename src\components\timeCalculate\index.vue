<template>
    <div class="time-calculate flex-row-style">
        <div v-for="(item, key) in data" :key="key" class="flex-row-style">
            <el-input
                v-model="form[item.label]"
                style="width: 50px;"
                @input="handleInput($event, item.label)"
                @blur="handleBlur"
            />
            <span class="text"> {{ item.label }}</span>
        </div>
    </div>
</template>

<script>
export default {
    name: "timeCalculate",
    model: {
        prop: "value",
        event: "change",
    },
    props: {
        value: {
            type: [Number, String],
            required: false,
            default: "",
        },
        data: {
            type: Array,
            required: false,
            default: () => {
                return [
                    {
                        label: "时",
                        trans: 3600,
                    },
                    {
                        label: "分",
                        trans: 60,
                    },
                    {
                        label: "秒",
                        trans: 1,
                    },
                ];
            },
        },
    },
    data() {
        return {
            form: {},
        };
    },
    watch: {
        data: {
            handler(ary) {
                for (const item of ary) {
                    this.$set(this.form, item.label);
                }
            },
            immediate: true,
        },
        value: {
            handler(val) {
                let number = Number(val);
                for (const item of this.data) {
                    const res = parseInt(number / item.trans);
                    number = number % item.trans;
                    this.form[item.label] = res;
                }
            },
            immediate: true,
        },
    },
    methods: {
        handleInput(e, prop) {
            this.form[prop] = this.form[prop].replace(/[^\d]/g, "");
            this.form[prop] = this.form[prop] ? parseInt(this.form[prop]) : 0;
        },
        handleBlur() {
            let num = 0;
            for (const item of this.data) {
                const number = Number(this.form[item.label]);
                num = num + number * item.trans;
            }
            this.$emit("change", num);
        },
    },
};
</script>

<style scoped lang="scss">
.time-calculate {
    width: 100%;
    flex: 1;
    .text {
        font-size: 14px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #4c596d;
        line-height: 22px;
        margin: 0 8px;
    }
}
</style>
