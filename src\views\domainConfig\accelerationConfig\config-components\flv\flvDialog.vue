<template>
    <el-dialog
        :title="dialogTitle"
        :append-to-body="true"
        :close-on-click-modal="false"
        :visible.sync="dialogVisible"
        :before-close="cancel"
        width="800px"
        class="flv-dialog"
    >
        <el-form :rules="addRules" :model="flvForm" ref="flvForm" class="cache-form" label-width="140px">
            <el-form-item :label="$t('domain.type')" prop="mode">
                <el-radio-group
                    v-model.number="flvForm.mode"
                    @change="typeChange"
                    class="cache-form--selector"
                >
                    <el-radio
                        v-for="opt in cacheModeOptions"
                        :key="opt.value"
                        :label="opt.value"
                        :disabled="opt.disabled"
                    >
                        {{ opt.label }}
                    </el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item key="content" :label="$t('domain.content')" prop="content" v-if="dialogVisible">
                <el-input
                    v-model="flvForm.content"
                    :placeholder="fileTypePlaceholder()"
                    :disabled="flvForm.mode === 2 || flvForm.mode === 3"
                    @focus="e => showNameSet(e, flvForm)"
                    clearable
                ></el-input>
            </el-form-item>
            <el-form-item :label="$t('domain.detail.label92')" prop="flv_seek_type">
                <el-radio-group v-model="flvForm.flv_seek_type" @change="flv_seek_type_change">
                    <el-radio label="time">{{ $t("domain.detail.placeholder70") }}</el-radio>
                    <el-radio label="byte">{{ $t("domain.detail.placeholder71") }}</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item :label="$t('domain.detail.label93')" prop="flv_seek_arg_start">
                <el-input v-model="flvForm.flv_seek_arg_start" clearable></el-input>
            </el-form-item>
            <el-form-item :label="$t('domain.detail.label94')" prop="flv_seek_arg_end">
                <el-input v-model="flvForm.flv_seek_arg_end" clearable></el-input>
            </el-form-item>
            <el-form-item :label="$t('domain.detail.label48')" prop="priority">
                <el-input v-model.number="flvForm.priority" maxlength="16" clearable></el-input>
            </el-form-item>
        </el-form>
        <div slot="footer">
            <el-button class="dialog-btn" @click="cancel">{{ $t("domain.cancel") }} </el-button>
            <el-button type="primary" class="dialog-btn" @click="submit">{{ $t("common.dialog.submit") }}</el-button>
        </div>

        <file-suffix-dialog
            :form="suffixDialogForm.form"
            :visible="suffixDialogForm.visible"
            @cancel="suffixDialogForm.visible = false"
            @submit="suffixDialogFormSubmit"
        />
    </el-dialog>
</template>

<script lang="ts">
import { Component, Prop, Vue, Watch } from "vue-property-decorator";
import { formValidate2Promise } from "@/utils";
import { Form } from "element-ui";
import { nUserModule } from "@/store/modules/nuser";
import { cacheModeOptions } from "@/components/simpleform/nAlogicCacheTable/mixin";
import { allPathPattern } from "@/utils/validator.utils";
import { cloneDeep } from "lodash-es";
import fileSuffixDialog from "@/components/fileSuffixDialog/index.vue";

type flvParam = {
    mode: number;
    content: string;
    flv_seek_type: string;
    flv_seek_arg_start: string;
    flv_seek_arg_end: string;
    priority: string;
};
const formValidate2Field = (form: Form) =>
    new Promise((resolve, reject) => {
        form.validate(valid => {
            if (valid) {
                resolve(true);
            }
        });
    });

@Component({
    components: {
        fileSuffixDialog,
    },
})
export default class FlvDialog extends Vue {
    @Prop({ default: "create", type: String }) private from!: string;
    // 弹框显示标志位
    @Prop({ default: false, type: Boolean }) private dialogVisible!: boolean;
    @Prop({
        default: {
            mode: 0,
            content: "",
            flv_seek_type: "time",
            flv_seek_arg_start: "begin",
            flv_seek_arg_end: "stop",
            priority: 10,
        },
    }) private flvForm!: flvParam;
    @Prop({ type: Array, required: true }) private flvList!: flvParam[];

    private cacheModeOptionsList: any = [];
    private suffixDialogForm: {
        visible: boolean;
        form: flvParam | null;
    } = {
        visible: false,
        form: null,
    }

    // 动态计算选项
    get cacheModeOptions() {
        return cacheModeOptions?.map(option => {
            let disabled = false;

            // mode 2、3 只支持选一个
            if (option.value === 2 || option.value === 3) {
                disabled = this.flvList.some(item => {
                    return item.mode === option.value;
                });
            }

            return {
                disabled,
                ...option,
            };
        });
    }

    get dialogTitle() {
        const seprate = nUserModule.lang === "en" ? " " : "";
        return `${this.from === 'create' ? this.$t('domain.add2') : this.$t('domain.modify')}${seprate}${this.$t('domain.detail.label91')}`
    }

    // 校验规则
    get addRules() {
        return {
            mode: [{ required: true, message: this.$t("domain.detail.tip42"), trigger: "change" }],
            content: [
                {
                    validator: (rule: any, value: string, callback: Function) => {
                        const paths = value?.split(",");
                        paths?.forEach(path => {
                            if (
                                this.flvForm.mode === 4 &&
                                (path.includes("?") || path.includes("？"))
                            )
                                callback(this.$t("domain.detail.placeholder48"));
                            if (this.flvForm.mode === 4 && path.length > 0 && path[0] !== "/")
                                callback(this.$t("domain.detail.placeholder49"));
                        });
                        if (this.flvForm.mode === 0 && value === "")
                            callback(this.$t("domain.htmlForbid.forbid4"));
                        if (this.flvForm.mode === 0 && !/^\w{1,9}(,\w{1,9})*$/.test(value))
                            callback(this.$t("domain.detail.placeholder50"));
                        if (this.flvForm.mode === 1 && value === "")
                            callback(this.$t("domain.detail.placeholder73"));
                        if (this.flvForm.mode === 1 && !/^((\/[\w-.]+)+,)*(\/[\w-.]+)+$/.test(value))
                            callback(this.$t("domain.detail.placeholder51"));
                        if (this.flvForm.mode === 2 && value === "")
                            callback(this.$t("domain.detail.placeholder52"));
                        if (this.flvForm.mode === 3 && value === "")
                            callback(this.$t("domain.detail.placeholder53"));
                        if (this.flvForm.mode === 4 && value.trim() === "")
                            callback(this.$t("domain.detail.placeholder74"));
                        if (this.flvForm.mode === 4 && !allPathPattern.test(value))
                            callback(this.$t("domain.detail.placeholder14"));
                        else callback();
                    },
                    trigger: ["blur", "change"],
                },
                { required: true, message: this.$t("domain.detail.placeholder54"), trigger: "blur" },
            ],
            flv_seek_type: [
                { required: true, message: this.$t("domain.detail.placeholder72"), trigger: "change" },
            ],
            priority: [
                { required: true, message: this.$t("domain.detail.placeholder22"), trigger: "blur" },
                { trigger: "blur", pattern: /^[0-9]*$/, message: this.$t("domain.detail.placeholder75") },
                {
                    validator: (rule: any, value: number, callback: Function) => {
                        if (value > 100) callback(this.$t("domain.detail.placeholder76"));
                        if (value < 1) callback(this.$t("domain.detail.placeholder77"));
                        else callback();
                    },
                    trigger: ["blur", "change"],
                },
            ],
        };
    }
    private fileTypePlaceholder() {
        return this.flvForm.mode === 0
            ? this.$t("simpleForm.alogicCacheMixin.FileSuffix.0")
            : this.flvForm.mode === 1
            ? this.$t("simpleForm.alogicCacheMixin.FileSuffix.1")
            : this.$t("simpleForm.alogicCacheMixin.FileSuffix.4");
    }
    private async typeChange(val: number) {
        if (val === 2 || val === 3) {
            this.flvForm.content = "/";
        } else {
            this.flvForm.content = "";
        }
        await formValidate2Field(this.$refs.flvForm as Form);
    }
    private showNameSet(e: {
        srcElement?: HTMLInputElement
    }, form: flvParam) {
        // 内容为后缀名才弹出弹框
        if (form.mode !== 0) return;
        this.suffixDialogForm = {
            visible: true,
            form: cloneDeep(form)
        };
        e?.srcElement?.blur();
    }
    private suffixDialogFormSubmit(content: string) {
        this.flvForm.content = content;
        this.suffixDialogForm.visible = false;
    }
    flv_seek_type_change(val: any) {
        if (val === "time") {
            this.flvForm.flv_seek_arg_start = "begin"
            this.flvForm.flv_seek_arg_end = "stop"
        } else if (val === "byte") {
            this.flvForm.flv_seek_arg_start = "start"
            this.flvForm.flv_seek_arg_end = "end"
        }
    }
    private async submit() {
        await formValidate2Promise(this.$refs.flvForm as Form);
        this.$emit("submit");
    }
    private cancel() {
        this.$emit("cancel", "flvDialogVisible");
    }
}
</script>

<style lang="scss" scoped>
.flv-dialog {
    ::v-deep {
        .el-dialog__body {
            padding: 24px !important;
        }
    }
}
</style>
