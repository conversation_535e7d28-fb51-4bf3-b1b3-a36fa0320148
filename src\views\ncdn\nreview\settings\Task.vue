<template>
    <section>
        <div class="search-bar">
            <domain-select
                v-model="selectedDomain"
                :domainOptions="domainOptions"
                :multiple="true"
                placeholder="所有域名"
            />

            <el-select v-model="action" placeholder="所有类型">
                <el-option
                    v-for="opt in actionsOptions"
                    :key="opt.value"
                    :label="opt.label"
                    :value="opt.value"
                />
            </el-select>

            <el-select v-model="status" placeholder="所有状态">
                <el-option
                    v-for="opt in statusOptions"
                    :key="opt.value"
                    :label="opt.label"
                    :value="opt.value"
                />
            </el-select>
            <div class="seperator"></div>

            <el-date-picker
                v-model="searchDate"
                type="daterange"
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                :clearable="false"
                :default-time="['00:00:00', '23:59:59']"
            />
            <div class="search-btns">
                <el-button type="primary" @click="refresh = true"> 查 询 </el-button>
                <el-button @click="reset">重 置</el-button>
            </div>
        </div>
        <el-table :data="dataList" v-loading="loading">
            <el-table-column label="工单编号" prop="orderNo" />
            <el-table-column label="域名" prop="domain" />
            <!-- <el-table-column label="类型" prop="action" /> -->
            <el-table-column label="类型">
                <template slot-scope="{ row }">
                    <cute-state :color="actionColorMap[row.action]">
                        {{ row.action == 4 ? "启用" : "取消" }}
                    </cute-state>
                </template>
            </el-table-column>
            <el-table-column label="状态">
                <template slot-scope="{ row }">
                    <!-- <span :class="['status-' + row.status]"> {{ statusToText(row.status) }} </span> -->
                    <cute-state :color="statusColorMap[row.status]">
                        {{ statusToText(row.status) }}
                    </cute-state>
                </template>
            </el-table-column>
            <el-table-column label="创建时间" prop="createTime" />
        </el-table>

        <ct-pager :refresh.sync="refresh" :loadData="search" />
    </section>
</template>
<script lang="ts">
import DomainSelect from "@cdnplus/common/views/statistics/common/DomainSelect.vue";
import { DomainModule } from "@/store/modules/domain";
import { DomainActionEnum } from "@/config/map";
import { Component, Mixins, Watch } from "vue-property-decorator";
import TableAndPagerActionMixin from "@/mixins/tableAndPagerAction";
import { nReviewUrl } from "@/config/url/ncdn/nreview";
import variables from "@cutedesign/ui/style/themes/default/index.scss";

const actionColorMap = {
    4: variables.colorSuccess,
    3: variables.colorDanger,
};
const statusColorMap = {
    2: variables.colorMaster,
    3: variables.colorSuccess,
    4: variables.colorDanger,
}
@Component({
    components: {
        DomainSelect,
    },
})
export default class Task extends Mixins(TableAndPagerActionMixin) {
    private actionColorMap = actionColorMap;
    private statusColorMap = statusColorMap;
    selectedDomain = [];
    action = "";
    actionsOptions = [
        {
            label: "所有类型",
            value: "",
        },
        {
            label: "启用",
            value: 4,
        },
        {
            label: "取消",
            value: 3,
        },
    ];
    status = "";
    statusOptions = [
        {
            label: "所有状态",
            value: "",
        },
        {
            label: "进行中",
            value: 2,
        },

        {
            label: "成功",
            value: 3,
        },

        {
            label: "失败",
            value: 4,
        },
    ];
    usePost = true;
    //prefetch = true;
    searchUrl = nReviewUrl.settingTaskList;
    searchDate = ["", ""];

    get domainList() {
        return DomainModule[DomainActionEnum.Review].list;
    }

    get domainOptions() {
        return DomainModule[DomainActionEnum.Review].options;
    }

    // 所有域名
    get allDomains() {
        const list = [];
        for (const item of this.domainOptions) {
            list.push(item.value);
        }
        return list;
    }
    // 重写 mixin 定义
    get searchParams() {
        const { action, status, selectedDomain, searchDate } = this;

        const params = {
            action,
            status,
            startTime: searchDate[0] ? Number(searchDate[0]) / 1000 : "",
            endTime: searchDate[1] ? Number(searchDate[1]) / 1000 : "",
            domainList: selectedDomain.length === 0 ? this.allDomains : selectedDomain,
        };
        return params;
    }

    public mounted() {
        // 切换tab时，请求数据并进行展示
        if (this.domainOptions.length > 0) {
            this.refresh = true;
        }
    }

    private reset() {
        this.action = "";
        this.status = "";
        this.searchDate = ["", ""];
        this.selectedDomain = [];
        this.refresh = true;
    }

    private statusToText(status: number) {
        let statusText = "";
        if (status === 2) {
            statusText = "进行中";
        } else if (status === 3) {
            statusText = "成功";
        } else if (status === 4) {
            statusText = "失败";
        }
        return statusText;
    }
    // 将时间戳格式化
    private timeFormat(time: number) {
        // 将time转化为毫秒数
        const date = new Date(time * 1000);
        const year = date.getFullYear();
        let month: string | number = date.getMonth() + 1;
        let day: string | number = date.getDate();
        month = month < 10 ? "0" + month : month;
        day = day < 10 ? "0" + day : day;
        let hour: string | number = date.getHours();
        hour = hour < 10 ? "0" + hour : hour;
        let minute: string | number = date.getMinutes();
        minute = minute < 10 ? "0" + minute : minute;
        let second: string | number = date.getSeconds();
        second = second < 10 ? "0" + second : second;
        return year + "-" + month + "-" + day + " " + hour + ":" + minute + ":" + second;
    }

    public dataListFormat(list: any) {
        for (const item of list) {
            item.createTime = this.timeFormat(Number(item.createTime));
        }
        return list;
    }
    // 拿到域名后再发起请求
    @Watch("domainOptions")
    private domianOptionsChange() {
        this.refresh = true;
    }
}
</script>

<style lang="scss" scoped>
.seperator {
    margin-top: 8px;
}
.search-bar .search-btns {
    margin-top: 8px;
}
// 蓝色：进行中状态
// $g-color-blue: #0091ff;
// // 绿色：成功状态，启用类型
// $g-color-green: #3e821d;
// // 红色：失败状态，取消类型
// $g-color-red: #db2828;

// .action-4 {
//     color: $g-color-green;
// }
// .action-3 {
//     color: $g-color-red;
// }

// .status-2 {
//     color: $g-color-blue;
// }
// .status-3 {
//     color: $g-color-green;
// }
// .status-4 {
//     color: $g-color-red;
// }
</style>
