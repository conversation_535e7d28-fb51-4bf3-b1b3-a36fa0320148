// IP集类型枚举
export enum IpSetTypeEnum {
    WhiteList = 1,
    BlackList = 0,
}

// IP集状态枚举
export enum IpSetStatusEnum {
    Pending = 0, // 待执行---文案显示为 部署中
    Deployed = 1, // 下发成功---文案显示为 部署中
    AllSuccess = 2, // 全部执行成功---文案显示为 部署成功
    AllSuccess3 = 3, // 全部执行成功---文案显示为 部署成功
    TaskCleared = 1000, // 关联任务已清理---文案显示为 部署任务已过期
    NewSet = 2000, // 新集合---文案显示为 未部署
    DeployFailed = -1, // 下发失败---文案显示为 部署失败
    PartialFailed = -2, // 部分失败---文案显示为 部署失败
    Exception = -3, // 异常---文案显示为 部署失败
}

// IP集状态映射
export const IpSetStatusMap = {
    [IpSetStatusEnum.Pending]: "ipSet.status.部署中",
    [IpSetStatusEnum.Deployed]: "ipSet.status.部署中",
    [IpSetStatusEnum.AllSuccess]: "ipSet.status.部署成功",
    [IpSetStatusEnum.AllSuccess3]: "ipSet.status.部署成功",
    [IpSetStatusEnum.TaskCleared]: "ipSet.status.部署任务已过期",
    [IpSetStatusEnum.NewSet]: "ipSet.status.未部署",
    [IpSetStatusEnum.DeployFailed]: "ipSet.status.部署失败",
    [IpSetStatusEnum.PartialFailed]: "ipSet.status.部署失败",
    [IpSetStatusEnum.Exception]: "ipSet.status.部署失败",
};

// IP地址验证正则表达式（支持IPv4、IPv6和CIDR格式）
export const IP_REGEX = /^((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)(\/(\d|[1-2]\d|3[0-2]))?$|^([\da-fA-F]{1,4}:){6}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)(\/([1-9]?\d|(1([0-1]\d|2[0-8]))))?$|^::([\da-fA-F]{1,4}:){0,4}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)(\/([1-9]?\d|(1([0-1]\d|2[0-8]))))?$|^([\da-fA-F]{1,4}:):([\da-fA-F]{1,4}:){0,3}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)(\/([1-9]?\d|(1([0-1]\d|2[0-8]))))?$|^([\da-fA-F]{1,4}:){2}:([\da-fA-F]{1,4}:){0,2}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)(\/([1-9]?\d|(1([0-1]\d|2[0-8]))))?$|^([\da-fA-F]{1,4}:){3}:([\da-fA-F]{1,4}:){0,1}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)(\/([1-9]?\d|(1([0-1]\d|2[0-8]))))?$|^([\da-fA-F]{1,4}:){4}:((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)(\/([1-9]?\d|(1([0-1]\d|2[0-8]))))?$|^([\da-fA-F]{1,4}:){7}[\da-fA-F]{1,4}(\/([1-9]?\d|(1([0-1]\d|2[0-8]))))?$|^:((:[\da-fA-F]{1,4}){1,6}|:)(\/([1-9]?\d|(1([0-1]\d|2[0-8]))))?$|^[\da-fA-F]{1,4}:((:[\da-fA-F]{1,4}){1,5}|:)(\/([1-9]?\d|(1([0-1]\d|2[0-8]))))?$|^([\da-fA-F]{1,4}:){2}((:[\da-fA-F]{1,4}){1,4}|:)(\/([1-9]?\d|(1([0-1]\d|2[0-8]))))?$|^([\da-fA-F]{1,4}:){3}((:[\da-fA-F]{1,4}){1,3}|:)(\/([1-9]?\d|(1([0-1]\d|2[0-8]))))?$|^([\da-fA-F]{1,4}:){4}((:[\da-fA-F]{1,4}){1,2}|:)(\/([1-9]?\d|(1([0-1]\d|2[0-8]))))?$|^([\da-fA-F]{1,4}:){5}:([\da-fA-F]{1,4})?(\/([1-9]?\d|(1([0-1]\d|2[0-8]))))?$|^([\da-fA-F]{1,4}:){6}:(\/([1-9]?\d|(1([0-1]\d|2[0-8]))))?$/;

// IP集项类型
export interface IpSetItem {
    unique_name: string; // 唯一值（类似id）
    alias_name: string;
    forbid_type: IpSetTypeEnum;
    deploy_status: IpSetStatusEnum;
    create_time: string;
    update_time: string;
}

// IP集使用情况项类型
export interface IpSetUsageItem {
    unique_name: string; // 唯一值（类似id）
    alias_name: string;
    domain: string;
    forbid_type: IpSetTypeEnum;
}
