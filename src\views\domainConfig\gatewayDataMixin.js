// 基础配置-子组件
import hsts from "@/views/domainConfig/basicConfig/config-components/hsts/index.vue";
import websocket from "@/views/domainConfig/basicConfig/config-components/websocket.vue";
import backOriginTime from "@/views/domainConfig/basicConfig/config-components/backOriginTime.vue";
import privateBucketOrigin from "@/views/domainConfig/basicConfig/config-components/privateBucketOrigin/index.vue";
import serverPort from "@/views/domainConfig/basicConfig/config-components/serverPort/index.vue";
import origin from "@/views/domainConfig/basicConfig/config-components/origin/index.vue";
import refererChain from "@/views/domainConfig/basicConfig/config-components/refererChain/index.vue";
import ipBlackWhiteList from "@/views/domainConfig/basicConfig/config-components/ipBlackWhiteList/index.vue";
import ipsetList from "@/views/domainConfig/basicConfig/config-components/ipsetList/index.vue";
import userAgent from "@/views/domainConfig/basicConfig/config-components/userAgent/index.vue";
import uriDeny from "@/views/domainConfig/basicConfig/config-components/uriDeny/index.vue";
import udfScript from "@/views/domainConfig/basicConfig/config-components/udfScript/index.vue";
import urlAuth from "@/views/domainConfig/basicConfig/config-components/urlAuth/index.vue";
import limitSpeed from "@/views/domainConfig/basicConfig/config-components/limitSpeed/index.vue";
import entryLimit from "@/views/domainConfig/accelerationConfig/config-components/entryLimit/index.vue";
import respHeaders from "@/views/domainConfig/basicConfig/config-components/respHeaders/index.vue";
import reqHeaders from "@/views/domainConfig/basicConfig/config-components/reqHeaders/index.vue";
import internalUriRewriteNew from "@/views/domainConfig/basicConfig/config-components/internalUriRewriteNew/index.vue";
import cusGzip from "@/views/domainConfig/basicConfig/config-components/cusGzip/index.vue";
import htmlForbid from "@/views/domainConfig/basicConfig/config-components/htmlForbid/index.vue";
import sni from "@/views/domainConfig/basicConfig/config-components/sni/index.vue";
import splitSet from "@/views/domainConfig/basicConfig/config-components/splitSet/index.vue";
import ja3 from "@/views/domainConfig/basicConfig/config-components/ja3.vue";
import QuicConfigComponent from "@/views/domainConfig/basicConfig/config-components/quic.vue";

// CDN加速配置-子组件
import mp4 from "@/views/domainConfig/accelerationConfig/config-components/mp4/index.vue";
import flv from "@/views/domainConfig/accelerationConfig/config-components/flv/index.vue";
import cacheKeyUri from "@/views/domainConfig/accelerationConfig/config-components/cacheKeyUri/index.vue";
import cacheKeyArgs from "@/views/domainConfig/accelerationConfig/config-components/cacheKeyArgs/index.vue";
import errorCode from "@/views/domainConfig/accelerationConfig/config-components/errorCode/index.vue";
import filetypeTtl from "@/views/domainConfig/accelerationConfig/config-components/filetypeTtl/index.vue";
import defineRedirect from "@/views/domainConfig/accelerationConfig/config-components/defineRedirect/index.vue";
import errorPage from "@/views/domainConfig/accelerationConfig/config-components/errorPage/index.vue";


import sharedHost from "@/views/domainConfig/basicConfig/config-components/sharedHost.vue";
import remoteSyncAuth from "@/views/domainConfig/basicConfig/config-components/remote-sync-auth/index.vue";

export default {
    components: {
        // 基础配置-子组件
        hsts,
        websocket,
        backOriginTime,
        privateBucketOrigin,
        serverPort,
        origin,
        refererChain,
        ipBlackWhiteList,
        ipsetList,
        userAgent,
        uriDeny,
        udfScript,
        urlAuth,
        limitSpeed,
        entryLimit,
        respHeaders,
        reqHeaders,
        internalUriRewriteNew,
        cusGzip,
        sni,
        splitSet,
        ja3,
        // CDN加速配置-子组件
        htmlForbid,
        mp4,
        flv,
        cacheKeyUri,
        cacheKeyArgs,
        errorCode,
        filetypeTtl,
        defineRedirect,
        errorPage,
        QuicConfigComponent,

        // 可插拔配置-注册可配置组件
        sharedHost,
        remoteSyncAuth,
    },
    data() {
        return {};
    },
    computed: {},
    methods: {},
};
