<template>
    <div class="info-col">
        <div class="info-icon" v-if="showIcon">
            <ct-svg-icon icon-class="info-circle-fill"></ct-svg-icon>
        </div>
        <div>
            UDFScipt业务仅支持在“可编程客户资源池”部署，如需使用该功能，请<a
                href="https://www.ctyun.cn/console/smartservice/ticket/workorder/submit"
                target="_blank"
                >提交工单</a
            >将域名资源池迁移到“可编程客户资源池”。
            <p>更换资源池注意事项：</p>
            <p>1、需确认是否有回源白名单限制，如有需要提前加白。</p>
            <p>2、更换资源池有可能会引起短时间内的回源突增，一段时间内回源量会自动回落到正常水平。</p>
        </div>
    </div>
</template>

<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";
import ctSvgIcon from "@/components/ctSvgIcon/index.vue";

@Component({
    name: "BusinessScriptTip",
    components: {
        ctSvgIcon,
    },
})
export default class BusinessScriptTip extends Vue {
    @Prop({ default: true }) showIcon!: boolean;
}
</script>

<style lang="scss" scoped>
.info-col {
    display: flex;
    gap: 12px;

    .info-icon {
        font-size: 18px;
        padding-left: 28px;
        text-align: right;
    }

    a {
		color: $color-master;
    }

    line-height: 1.5;
}
</style>
