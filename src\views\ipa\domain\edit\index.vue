<template>
    <ct-section-wrap
        headerText="IP应用加速配置"
        headerTip="展示接入的所有域名/实例，可进行相关配置管理"
        class="ipa-domain-section-wrapper"
    >
        <template slot="headerBtn">
            <el-button type="text" icon="el-icon-d-arrow-left" @click="handleTargetToDomainList">
                IP应用加速接入
            </el-button>
        </template>
        <ct-box class="ct-box-style">
            <domain-panel ref="domainPanel" :is-open.sync="isOpen" :domain="domain" />

            <form-wrapper :key="renderKey" />
        </ct-box>
    </ct-section-wrap>
</template>

<script>
import FormWrapper from "./FormWrapper.vue";
import domainPanel from "@/components/domainPanel";
import { SecurityAbilityModule } from "@/store/modules/securityAbility";

export default {
    components: {
        FormWrapper,
        domainPanel,
    },
    data() {
        return {
            isOpen: true,
            state: "",
            noteClass: "",
        };
    },
    computed: {
        domain() {
            return this.$route.query.domain;
        },
        renderKey() {
            return SecurityAbilityModule.securityRenderKey;
        },
    },
    mounted() {
        window.custom.emit("handleCDNMonitor", [
            {
                event: "handleCDNConfigChange",
                type: "add",
            },
        ]);
    },
    beforeDestroy() {
        window.custom.emit("handleCDNMonitor", [
            {
                event: "handleCDNConfigChange",
                type: "remove",
            },
        ]);
    },
    methods: {
        handleTargetToDomainList() {
            this.$router.push({
                name: "domain.list",
            });
        },
    },
};
</script>

<style lang="scss" scoped>
// .ipa-domain-section-wrapper {
//     margin-right: 20px !important;
// }

.ct-box-style {
    width: 100%;
    height: 100%;
    padding: 20px 0 0 20px !important;
    // padding-bottom: 0 !important;
    overflow: hidden;
    display: flex;
    flex-direction: row;
}
</style>
