<template>
    <div>
        <div class="radio-row">
            <el-radio-group v-model="objectModel.type">
                <el-radio label="allow">{{ $t("simpleForm.blockTable.allow") }}</el-radio>
                <el-radio label="block">{{ $t("simpleForm.blockTable.block") }}</el-radio>
            </el-radio-group>
        </div>
        <div>
            <el-input
                class="textarea-content"
                v-model="objectModel.domainList"
                type="textarea"
                :rows="5"
                :placeholder="placeholder"
            />
        </div>
    </div>
</template>

<script>
import { number } from 'echarts';
// import mixin from "../../editor.mixin";
// import commonPattern from "@cdnplus/common/config/pattern";

export default {
    // mixins: [mixin],
    name: "n-alogic-referer-block-table",
    props: {
        refererData: Object,
        refererLimit: Number
    },
    data() {
        return {
            objectModel: {
                type: "allow",
                domainList: "",
            }
        };
    },
    computed: {
        placeholder() {
            const { refererLimit } = this;
            // const { type } = this.objectModel;
            // 黑名单也支持 localhost
            return this.$t("domain.detail.tip38", { maxNum: refererLimit });
        },
        domainList() {
            // 需要过滤空表内容
            return this.objectModel.domainList
                .split("\n")
                .map(i => i.trim())
                .filter(i => i);
        },
    },
    methods: {
        validateProcedure() {
            // let { pattern } = commonPattern.referer;
            // 白名单需要支持 localhost
            // pattern = this.objectModel.type === "allow" ? `${pattern}|^localhost$` : pattern;
            // 黑名单也要支持 localhost
            // pattern = `${pattern}|^localhost$`;
            // const referReg = new RegExp(pattern);
            const result = {
                valid: true,
                msg: "",
                dom: "referer_type",
            };
            const { domainList, refererLimit, objectModel } = this;

            if (!objectModel.type && objectModel.domainList) {
                result.valid = false;
                result.msg = this.$t("domain.detail.tip42");
            } else if (domainList.length > refererLimit) {
                result.valid = false;
                result.msg = this.$t("domain.detail.tip43", { maxNum: refererLimit });
            } else {
                // for (let i = 0; i < domainList.length; i++) {
                //     const item = domainList[i].trim();
                //     // 输入的不是合格 domain
                //     if (!referReg.test(item)) {
                //         result.valid = false;
                //         result.msg = "输入的域名地址格式不正确，请重新输入";
                //         break;
                //     }
                // }

                // 检查重复
                const hasRepeat = new Set(domainList).size !== domainList.length;
                if (hasRepeat) {
                    result.valid = false;
                    result.msg = this.$t("domain.detail.tip44");
                }
            }

            return Promise.resolve(result);
        },
    },
    watch: {
        objectModel: {
            handler(val) {
                this.model = {
                    type: val.type,
                    domainList: this.domainList.join(","),
                };
                this.$emit("onChange", this.model);
            },
            deep: true,
            immediate: true,
        },
    },
    mounted() {
        if (this.refererData) {
            this.objectModel = {
                type: this.refererData.type || "allow",
                domainList: this.refererData.domainList?.split(",").join("\n") || "",
            };
        }
    },
};
</script>

<style lang="scss" scoped>
.textarea-content {
    width: 70%;
}
</style>
