<!--
 * @Description: 运营商分布
 * @Author: wang yuegong
-->
<template>
    <ct-box :tags="$t('statistics.user.tip8')" class="user-section">
        <template #tags-slot>
            <el-radio-group v-model="currentType" size="small">
                <el-radio-button label="topBandwidth">{{ $t('statistics.dcdn.bandwidthFlowWhole.radioBtn1')
                    }}</el-radio-button>
                <el-radio-button label="flow">{{ $t('statistics.dcdn.bandwidthFlowWhole.radioBtn2') }}</el-radio-button>
                <el-radio-button label="request">{{ $t('statistics.rank.common.tableColumn5') }}</el-radio-button>
            </el-radio-group>
            <el-tooltip effect="dark" :content="$t('statistics.common.searchDownloadContent')" placement="right">
                <i class="el-icon-download download-icon" @click="download"></i>
            </el-tooltip>
        </template>

        <el-table :data="showDataListBase" max-height="300" v-loading="loading" :element-loading-text="$t('statistics.common.table.loading')">
            <el-table-column :label="$t('statistics.user.tip7')" prop="isp">
                <template slot-scope="scope">
                    {{ ispMap[scope.row.isp] }}
                </template>
            </el-table-column>
            <el-table-column :label="tableUnit" prop="currentData" />
            <el-table-column v-if="currentType === 'flow'" :label="$t('statistics.rank.common.tableColumn4')"
                prop="flowPer" />
        </el-table>
    </ct-box>
</template>

<script lang="ts">
import { Component } from "vue-property-decorator";
import { mixins } from "vue-class-component";
import tableMixin from "../tableMixin";
import { StatisticsUserUrl } from "@/config/url/statistics";
import { SearchParams } from "@/types/statistics/user";
import { IspFetchDataItem } from "@/types/statistics/user";
import { StatisticsModule } from "@/store/modules/statistics";
import { nUserModule } from "@/store/modules/nuser";

@Component({
    name: "UserIsp",
})
export default class UserIsp extends mixins(tableMixin) {
    currentType: "topBandwidth" | "flow" | "request" = "topBandwidth";
    loading = false;
    fetchDataList: IspFetchDataItem[] = []; // 请求的列表数据

    get ispMap() {
        return StatisticsModule.ispOptions.reduce<Record<string, string>>((prev, cur) => {
            prev[cur.isp_code] = nUserModule.lang === "en" ? cur.isp_enname : cur.isp_cnname;
            return prev;
        }, {})
    }
    private async localFetchGenerator<T>(url: string, payload: { method: string; body: any, headers?: any }) {
            const rst = await this.$ctFetch<T>(url, payload)
            return rst;
    }
    // 1、数据请求
    async getData(params: SearchParams) {
        this.loading = true;

        const rst = await this.localFetchGenerator<{ list?: IspFetchDataItem[] }>(StatisticsUserUrl.ispDataList, {
            method: "POST",
            body: { data: params },
            headers: {
                "Content-Type": "application/json",
            },
        });

        this.fetchDataList = rst?.list || [];
    }

    download() {
        const { fetchDataList } = this;
        if (fetchDataList.length === 0) return this.$message.warning(`${this.$t("statistics.common.chart.errMsg[2]")}`);

        const { currentType } = this;
        let { tableUnit } = this;
        let { scale } = this.dataFormatConfig;

        if (["topBandwidth", "flow"].includes(this.currentType)) {
            tableUnit = tableUnit.replace(this.dataFormatConfig.unit, this.currentType === "topBandwidth" ? this.Mbps : this.MB);
            scale = Math.pow(this.scale, 2);
        }
        let str = `${this.$t("statistics.user.tip9")},${tableUnit},${currentType !== "flow" ? "\n" : `${this.$t('statistics.rank.common.tableColumn4')}\n`}`;
        fetchDataList.forEach(item => {
            const currentData =
                currentType !== "request" ? (+item[currentType] / scale).toFixed(2) : item[currentType];

            
            str += this.ispMap[item.isp] + ",";
            str += currentData + ",";
            str += currentType !== "flow" ? "" : item.flowPer;
            str += "\n";
        });

        this.$emit("downloadExcel", {
            name: `${this.$t("statistics.user.tip10")}`,
            str,
        });
    }
}
</script>
