// 用户类型枚举值
export const UserChargingTypeEnum = <const>{
    PrePay: 1, // 预付费
    PostPay: 2, // 后付费
};
// 包类型
export const PacketTypeMap = {
    1: "流量包",
    2: "静态https请求包",
    3: "动态请求包",
    4: "WAF防护请求包",
    7: "上传加速流量包",
    8: "websocket流量包",
};
// 包单位
export const PacketUnitMap = {
    1: "billing.productUnitMap.2",
    2: "billing.productUnitMap.1",
    3: "billing.productUnitMap.1",
    4: "billing.productUnitMap.1",
    7: "billing.productUnitMap.2",
    8: "billing.productUnitMap.2",
};
// 包状态
export const PacketStatusMap = {
    1: "开通未使用",
    2: "使用中",
    3: "流量用完",
    4: "到期",
    5: "关停",
    6: "退订",
    7: "冻结",
};