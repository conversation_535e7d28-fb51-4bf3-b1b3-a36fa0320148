/*
 * @Description: 错误类型声明
 * @Author: wang yuegong
 */

// 期望的错误信息格式
export interface ErrorMsg {
    hashRoute?: string; // 缓存报错出现的路由，用于过滤非当前页面的报错展示
    code: string;
    reason: string;
    serial?: string; // fetch 错误特有
    url?: string; // fetch 错误特有
    params?: string; // fetch 错误特有，前端解析参数
    // row?: CtFetchMsg;
    stack?: string; // Error 对象的堆栈信息，是否需要？
    btnConfig?: ErrorModalBtn; // 弹窗中确定按钮的配置，非必须
    reasonUseHtml?: boolean; // 在白名单中的 code ，允许直接渲染 reason 为 html
    data?: any;
    isCtiamCode?: boolean;
}

// ctFetch 的错误信息格式
export interface CtFetchMsg {
    code: string;
    reason: string;
    url: string;
    // raw?: ErrorMsg;
    data?: ErrorMsg;
    btnConfig?: ErrorModalBtn; // 需要业务方定义弹窗的按钮
}

// 自定义错误弹窗的操作按钮
export interface ErrorModalBtn {
    title?: string; // 按钮的说明文字，默认“确定”
    cb?: Function; // 回调事件，优先级高于 href
    href?: string; // 跳转链接，由于 modal 组件中无法正常关联 $router ，所以该属性只能使用简单的 location.href = href 处理
}
