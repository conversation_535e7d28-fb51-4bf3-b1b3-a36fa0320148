<template>
    <div class="ct-edit-wrapper">
        <el-form
            label-width="140px"
            label-position="left"
            :model="ipsetListForm"
            :rules="rules"
            ref="ipsetListForm"
            :disabled="!isEdit || !isService"
        >
            <div>
                <el-form-item :label="$t('domain.detail.label124')" prop="switch">
                    <el-switch
                        v-model="ipsetListForm.switch"
                        :active-value="1"
                        :inactive-value="0"
                        @change="ipsetSwitchChange"
                    ></el-switch>
                    <span class="tips">
                        <ct-svg-icon icon-class="info-circle" class-name="icon-column-label" />
                        {{ $t("domain.editPage.ipsetTip") }}
                    </span>
                </el-form-item>
                <div v-if="ipsetListForm.switch === 1" class="switch-wrapper">
                    <el-form-item :label="$t('domain.type')" prop="forbid_type">
                        <div>
                            <div class="radio-row">
                                <el-radio-group v-model="ipsetListForm.forbid_type" @change="ipsetTypeChange">
                                    <el-radio :label="1">{{ $t("domain.detail.trustlist") }}</el-radio>
                                    <el-radio :label="0">{{ $t("domain.detail.blocklist") }}</el-radio>
                                </el-radio-group>
                            </div>
                        </div>
                    </el-form-item>
                    <el-form-item :label="$t('domain.detail.label125')" prop="alias_name">
                        <el-select
                            v-model="ipsetListForm.alias_name"
                            :placeholder="$t('domain.editPage.placeholder8')"
                            @change="uniqueNameChange"
                            :loading="loading"
                            :loading-text="$t('common.loading')"
                        >
                            <template #empty>
                                <div class="empty-text">
                                    {{ loading ? $t('common.loading') : $t('home.info.noData') }}
                                </div>
                            </template>
                            <el-option
                                v-for="item in ipsetList"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                </div>
            </div>
        </el-form>
    </div>
</template>

<script>
import componentMixin from "@/views/domainConfig/componentMixin";
import ctSvgIcon from "@/components/ctSvgIcon";
import { IpSetUrl } from "@/config/url/ipSet";
import { SecurityAbilityModule } from "@/store/modules/securityAbility";

export default {
    name: "ipsetList",
    mixins: [componentMixin],
    props: {
        datas: Object, // 表单数据对象
    },
    components: {
        ctSvgIcon,
    },
    data() {
        return {
            ipsetListForm: {
                switch: null,
                forbid_type: null,
                alias_name: null,
            },
            loading: false,
            ipsetList: [], // 当前类型下的IP集列表
            allIpsetList: [], // 存储所有IP集列表
            rules: {
                alias_name: [
                    {
                        required: true,
                        message: this.$t("domain.editPage.ruleIpset"),
                        trigger: "change",
                        validator: (rule, value, callback) => {
                            if (this.ipsetListForm.switch === 1 && !value) {
                                callback(new Error(this.$t("domain.editPage.ruleIpset")));
                            } else {
                                callback();
                            }
                        },
                    },
                ],
            },
        };
    },
    watch: {
        datas: {
            deep: true,
            handler(val) {
                this.init(val);
            },
            immediate: true,
        },
        allIpsetList: {
            handler(val) {
                if (val.length > 0) {
                    this.filterIpsetList();
                }
            },
            immediate: true,
        },
    },
    created() {
        this.getIpsetList();
    },
    methods: {
        init(v) {
            if (!v || !v.ip_set_forbid) return;
            this.ipsetListForm.switch = v.ip_set_forbid.switch;
            this.ipsetListForm.forbid_type = v.ip_set_forbid.forbid_type;
            this.ipsetListForm.alias_name = v.ip_set_forbid.alias_name;
            // 初始化后，如果已经有数据，需要立即过滤显示
            if (this.allIpsetList.length > 0) {
                this.filterIpsetList();
            }
        },
        async getIpsetList() {
            try {
                this.loading = true;
                const res = await this.$ctFetch(IpSetUrl.list, { method: "GET" });
                if (res?.list) {
                    // 保存完整的IP集列表
                    this.allIpsetList = res.list;
                    // 根据当前类型过滤数据
                    this.filterIpsetList();
                }
            } catch (error) {
                this.$errorHandler(error);
            } finally {
                this.loading = false;
            }
        },
        filterIpsetList() {
            // 从完整列表中过滤出当前类型的数据
            const filteredList = this.allIpsetList.filter(
                item => item.forbid_type === this.ipsetListForm.forbid_type
            );
            this.ipsetList = filteredList.map(item => ({
                label: item.alias_name,
                value: item.alias_name,
            }));
        },
        ipsetSwitchChange(val) {
            const originalConf = SecurityAbilityModule.securityBasicConfigOriginForm;
            this.ipsetListForm = {
                switch: val,
                forbid_type: originalConf.ip_set_forbid.forbid_type,
                alias_name: originalConf.ip_set_forbid.alias_name,
            }
            this.$emit("onChange", this.ipsetListForm);
        },
        ipsetTypeChange() {
            this.ipsetListForm.alias_name = "";
            // 直接过滤数据而不是重新获取
            this.filterIpsetList();
            this.$emit("onChange", this.ipsetListForm);
        },
        uniqueNameChange() {
            this.$emit("onChange", this.ipsetListForm);
        },
    },
};
</script>

<style lang="scss" scoped>
.tips {
    .question-circle {
        font-size: $text-size-md;
        cursor: pointer;
        margin-right: $margin;
    }
    font-size: 12px;
    color: $color-neutral-7;
    font-weight: 400;
    margin-left: $margin;
}
.empty-text {
    padding: 8px 12px;
    text-align: center;
    color: $color-neutral-6;
    font-size: 14px;
}
</style>
