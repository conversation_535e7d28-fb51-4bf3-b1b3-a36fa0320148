<template>
    <div class="menu">
        <el-menu
            :default-active="activeIdentifier"
            :unique-opened="true"
            @open="openClose"
            @close="openClose"
        >
            <div v-for="(item, index) in menuList" :key="index">
                <!-- 先判断是否有二级菜单 -->
                <div v-if="item.items">
                    <el-submenu :index="item.identifier" class="sidebar-submenu-wrapper">
                        <template slot="title">
                            <i :class="item.icon"></i>
                            <span>{{ item.name }}</span>
                        </template>
                        <el-menu-item
                            v-for="(it, idx) in item.items"
                            :key="idx"
                            :index="it.identifier"
                            @click="go(it)"
                            class="sidebar-submenu-menuitem-wrapper"
                        >
                            <i :class="it.icon"></i>
                            <span>{{ it.name }}</span>
                        </el-menu-item>
                    </el-submenu>
                </div>
                <div v-else>
                    <el-menu-item :index="item.identifier" @click="go(item)">
                        <i :class="item.icon"></i>
                        <span slot="title">{{ item.name }}</span>
                    </el-menu-item>
                </div>
            </div>
        </el-menu>
    </div>
</template>
<script lang="ts">
import { Component, Mixins, Watch } from "vue-property-decorator";
import { Route } from "vue-router";
import { Dom } from "../../../../utils";
import MenuMixin from "./menuMixin";

@Component({
    name: "sidebar-menu",
})
export default class SidebarMenu extends Mixins(MenuMixin) {
    @Watch("$route", { immediate: true })
    private onRouteChange(to: Route) {
        // 无需请求菜单的情况:  在拦截器路由中, 环境不匹配,匹配到了当前菜单
        const noInvoke = to.name === "interceptor" || Dom.isMobileEnv();
        if (noInvoke) {
            return;
        }
        this.$ctBus.$emit("menulist:update");
    }

    private openClose(key: string) {
        const map = this.pathMap;
        for (const [path, index] of map.entries()) {
            // 当没有配置 hrefLocal 时，保存下来的 path 是 menu_id
            if (key === index && path.includes("/")) {
                this.$router.push(path);
                break;
            }
        }
    }
}
</script>
<style lang="scss" scoped>
.menu {
    margin: 0 auto;
    // 由于设置了width为80%，防止.el-menu-item hover时会溢出
    overflow: hidden;

    // 由于template中多层菜单时，加了div标签，污染了el-menu标签的渲染，导致collapse状态时，文字和右边箭头并没有消失
    // 这里强制 文字和右边箭头消失
    ::v-deep.el-menu--collapse {
        .el-submenu__title span {
            display: none;
        }

        .el-icon-arrow-right::before {
            display: none;
        }
    }

    .el-menu {
        background: transparent;
        border: none;
        overflow: auto;
        flex: 1;
    }

    .el-menu-item.is-active {
        background-color: $color-info-bg;
    }

    .el-menu-item.is-active:before {
        width: 3px;
        height: 100%;
        top: 0;
        left: 0;
    }
    .sidebar-submenu-menuitem-wrapper {
        transition: background-color 0.3s;

        &:hover {
            background-color: $color-info-bg;
        }
    }
    ::v-deep {
        .el-menu-item, .el-submenu__title {
            &:hover {
                background-color: $color-info-bg;
            }
        }
    }
}
</style>
<style lang="scss">
.sidebar-submenu-wrapper {
    .el-submenu__icon-arrow.el-icon-arrow-down {
        &::before {
            content: "\e6df";
        }
    }
}
</style>
