/*
 * @Description: 用于域名标签树（store/label），区别于筛选域名列表
 * @Author: wang yuegong
 */

// 接口响应数据的类型
export type InterfaceLabel = {
    labelId: string; // id ，也就是我们期望的 value
    name: string; // 期望的 label
    note: string;
    create_date: string;
};
export type InterfaceLabelItem = InterfaceLabel & {
    children?: (InterfaceLabel & {
        state: string; // 绑定的域名个数，其实用不到
        domains: string[]; // 绑定的域名列表，用于生成映射关系
        domainList?: string[];
    })[];
};

// labelSelect 业务组件期望的数据模型
export type Label = {
    label: string; // 显示的文案
    value: string; // 后端生成的唯一 id
};

export type LabelItem = Label & {
    children: Label[];
};

// 中间状态
export type OneLable = {
    keyId: string; // key id
    key: string; // 页面中的语义 key ，也就是源数据第一层的 name
    valueId: string; // value id
    value: string; // 页面中的语义 value ，也就是源数据第二层的 name
};
// 域名已绑定的标签信息（前端对数据进行处理获得，用于域名列表的管理）
export type DomainMapLabel = {
    [domain: string]: LabelItem[];
};

// 域名已绑定的标签信息（简化版，只有标签id）
export type DomainMapLabelJr = {
    [domain: string]: string[];
};

// 标签已绑定的域名信息（也就是 children.domains ，提出来用起来方便）
export type LabelMapDomain = {
    [labelId: string]: string[];
};
