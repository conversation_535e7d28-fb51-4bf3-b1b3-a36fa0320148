<template>
    <el-dialog
        :title="dialogTitle"
        :append-to-body="true"
        :close-on-click-modal="false"
        :visible.sync="dialogVisible"
        :before-close="cancel"
        width="900px"
        class="cachekey-args-dialog"
    >
        <el-form
            :rules="addRules"
            :model="cachekeyArgsForm"
            ref="cachekeyArgsForm"
            class="cache-form"
            label-width="150px"
        >
            <el-form-item :label="$t('domain.type')" prop="mode">
                <el-radio-group
                    v-model.number="cachekeyArgsForm.mode"
                    @change="typeChange"
                    class="cache-form--selector"
                >
                    <!-- <el-radio :label="0">{{ $t("domain.detail.cacheModeMap[0]") }}</el-radio>
                    <el-radio :label="1">{{ $t("domain.detail.cacheModeMap[1]") }}</el-radio>
                    <el-radio :label="2">{{ $t("domain.detail.cacheModeMap[2]") }}</el-radio>
                    <el-radio :label="3">{{ $t("domain.detail.cacheModeMap[3]") }}</el-radio>
                    <el-radio :label="4">{{ $t("domain.detail.cacheModeMap[4]") }}</el-radio> -->
                    <el-radio
                        v-for="opt in cacheModeOptions"
                        :key="opt.value"
                        :label="opt.value"
                        :disabled="opt.disabled"
                    >
                        {{ opt.label }}
                    </el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item key="content" :label="$t('domain.content')" prop="content" v-if="dialogVisible">
                <el-input
                    v-model="cachekeyArgsForm.content"
                    :placeholder="fileTypePlaceholder()"
                    :disabled="cachekeyArgsForm.mode === 2 || cachekeyArgsForm.mode === 3"
                    @focus="showNameSet(cachekeyArgsForm)"
                ></el-input>
            </el-form-item>
            <el-form-item v-if="dialogVisible" :label="$t('domain.detail.label46')" prop="ignore_params">
                <el-radio-group
                    class="wrap"
                    v-model="cachekeyArgsForm.ignore_params"
                    @change="ignore_params_change(cachekeyArgsForm)"
                >
                    <el-radio :label="1">{{ $t("domain.detail.label51") }}</el-radio>
                    <el-radio :label="2">{{ $t("domain.detail.label52") }}</el-radio>
                    <el-radio :label="3">{{ $t("domain.detail.label53") }}</el-radio>
                    <el-radio :label="4">{{ $t("domain.detail.label54") }}</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item
                :label="$t('domain.detail.label47')"
                prop="args"
                v-if="cachekeyArgsForm.ignore_params === 3 || cachekeyArgsForm.ignore_params === 4"
            >
                <el-input v-model.trim="cachekeyArgsForm.args" class="input-with-select"> </el-input>
                <div class="note" v-if="cachekeyArgsForm.ignore_params === 3">
                    <!-- 保留指定参数示例：a=$arg_a&b=$arg_b，其中$arg_a代表问号后参数a的值。不支持参数名带中划线，如参数名：a-b，如需配置，请提交工单。 -->
                    {{ $t("domain.detail.tip19") }}
                </div>
                <div class="note" v-if="cachekeyArgsForm.ignore_params === 4">
                    <!-- 忽略指定参数示例："a,b"，多个参数以英文逗号分隔。 -->
                    {{ $t("domain.detail.tip20") }}
                </div>
            </el-form-item>
            <el-form-item :label="$t('domain.detail.label48')" prop="priority">
                <el-input v-model.number="cachekeyArgsForm.priority" maxlength="16" class="input-with-select"> </el-input>
            </el-form-item>
        </el-form>
        <div slot="footer">
            <el-button class="dialog-btn" @click="cancel">{{
                $t("common.dialog.cancel")
            }}</el-button>
            <el-button type="primary" class="dialog-btn" @click="submit">{{
                $t("common.dialog.submit")
            }}</el-button>
        </div>
        <el-dialog
            :title="$t('domain.detail.cacheModeMap.0')"
            :append-to-body="true"
            :close-on-click-modal="false"
            :modal-append-to-body="false"
            :visible.sync="extensionsDialogVisible"
            custom-class="aocdn-cache-extensions-dialog"
        >
            <div class="aocdn-cache-extensions" v-for="(val, key) in extensionsOptions" :key="val.title">
                <div class="extension-title">
                    <el-checkbox
                        v-model="extensionAllSelected[key]"
                        @change="checkAllChange(key)"
                        :label="val.title"
                    />
                </div>
                <div class="extension-list">
                    <el-checkbox-group
                        v-model="extensionSelected[key]"
                        @change="checkSingleChange(key)"
                    >
                        <el-checkbox
                            v-for="extension in val.list"
                            :label="extension"
                            :key="extension"
                        />
                    </el-checkbox-group>
                </div>
            </div>
            <div class="aocdn-cache-extensions">
                <div class="extension-title">{{ $t("domain.detail.label40") }}</div>
                <div class="extension-list">
                    <el-input
                        type="textarea"
                        :rows="2"
                        :placeholder="$t('domain.detail.placeholder1')"
                        v-model="extensionOther"
                    />
                </div>
            </div>
            <div slot="footer" class="dialog-footer">
                <el-button @click="extensionsDialogVisible = false">{{ $t("common.dialog.cancel") }}</el-button>
                <el-button type="primary" @click="setName">{{ $t("common.dialog.submit") }}</el-button>
            </div>
        </el-dialog>
    </el-dialog>
</template>

<script lang="ts">
import { Component, Prop, Vue, Watch } from "vue-property-decorator";
import { formValidate2Promise } from "@/utils";
import { Form } from "element-ui";
import i18n from "@/i18n/index";
import { extensionsOptions } from "@/components/simpleform/nAlogicCacheTable/config";
import { nUserModule } from "@/store/modules/nuser";
import { getConditionContentPlaceholder } from "@/components/commonCondition/utils";

const extensionsMap: any = {};
Object.keys(extensionsOptions).forEach((key: any) => {
    (extensionsOptions as any)[key].list.forEach((k: any) => {
        extensionsMap[k] = key;
    });
});

// 类型：字典翻译
const CacheModeMap: any = {
    0: i18n.t("domain.detail.cacheModeMap[0]"),
    1: i18n.t("domain.detail.cacheModeMap[1]"),
    2: i18n.t("domain.detail.cacheModeMap[2]"),
    3: i18n.t("domain.detail.cacheModeMap[3]"),
    4: i18n.t("domain.detail.cacheModeMap[4]"),
    // 5: i18n.t("domain.detail.cacheModeMap[5]"),
};
const cacheModeOptions = Object.keys(CacheModeMap).map((mode: any) => ({
    label: CacheModeMap[mode],
    value: mode * 1, // 需要是 number 类型
}));

type cachekeyArgsParam = {
    mode: string;
    content: string;
    ignore_params: string;
    args: string;
    priority: string;
};
const formValidate2Field = (form: Form) =>
    new Promise((resolve, reject) => {
        form.validate(valid => {
            if (valid) {
                resolve(true);
            }
        });
    });

@Component({})
export default class UpdateDialog extends Vue {
    @Prop({ default: "create", type: String }) private from!: string;
    // 弹框显示标志位
    @Prop({ default: false, type: Boolean }) private dialogVisible!: boolean;
    @Prop({
        default: {
            mode: 0,
            content: "",
            ignore_params: null,
            args: "",
            priority: 10,
        },
    })
    private cachekeyArgsForm!: cachekeyArgsParam;
    @Prop({ required: true, type: Array }) cachekey_args!: any[];

    extensionAllSelected: any = {
        dynamic: false,
        image: false,
        style: false,
        av: false,
        download: false,
        page: false,
    }
    extensionSelected: any = {
        dynamic: [],
        image: [],
        style: [],
        av: [],
        download: [],
        page: [],
    }
    extensionOther = ""
    extensionsDialogVisible = false
    extensionsOptions: any = extensionsOptions

    @Watch("dialogVisible")
    onVisibleChanged(val: boolean) {
        if (!val) return;
        (this.$refs.cachekeyArgsForm as Form)?.resetFields();
    }

    get dialogTitle() {
        const separate = nUserModule.lang === "en" ? " " : "";
        return `${this.from === "create" ? i18n.t('domain.add2') : i18n.t('domain.modify')}${separate}${i18n.t('domain.detail.label12')}`;
    }

    get values() {
        // 处理换行、所有空格、过滤空数据
        return this.cachekeyArgsForm;
    }
    // 校验规则
    get addRules() {
        return {
            mode: [{ required: true, message: this.$t("domain.detail.tip42"), trigger: "change" }],
            content: [
                {
                    validator: (rule: any, value: string, callback: Function) => {
                        const paths = value?.split(",");
                        const allPathPattern = new RegExp("^([A-Za-z0-9]|[!#$&'()*+,-./:;=?@_~])*$");
                        paths?.forEach(path => {
                            if (
                                parseInt(this.cachekeyArgsForm.mode) === 4 &&
                                (path.includes("?") || path.includes("？"))
                            )
                                
                                callback(new Error(this.$t("domain.detail.placeholder48") as string));
                            if (
                                parseInt(this.cachekeyArgsForm.mode) === 4 &&
                                path.length > 0 &&
                                path[0] !== "/"
                            )
                                callback(this.$t("domain.detail.placeholder49"));
                        });
                        if (parseInt(this.cachekeyArgsForm.mode) === 0 && value === "")
                            // callback(new Error("请输入缓存后缀名"));
                            callback(new Error(this.$t("domain.htmlForbid.forbid4") as string));
                        if (parseInt(this.cachekeyArgsForm.mode) === 0 && !/^\w{1,9}(,\w{1,9})*$/.test(value))
                            callback(this.$t("domain.detail.placeholder50"));
                        if (parseInt(this.cachekeyArgsForm.mode) === 1 && value === "")
                            // callback(new Error("请输入缓存目录"));
                            callback(new Error(this.$t("domain.detail.placeholder73") as string));
                        if (
                            parseInt(this.cachekeyArgsForm.mode) === 1 &&
                            !/^((\/[\w-.]+)+,)*(\/[\w-.]+)+$/.test(value)
                        )
                            callback(this.$t("domain.detail.placeholder51"));
                        if (parseInt(this.cachekeyArgsForm.mode) === 2 && value === "")
                            callback(this.$t("domain.detail.placeholder52"));
                        if (parseInt(this.cachekeyArgsForm.mode) === 3 && value === "")
                            callback(this.$t("domain.detail.placeholder53"));
                        if (parseInt(this.cachekeyArgsForm.mode) === 4 && value.trim() === "")
                            // callback(new Error("请输入缓存全路径文件"));
                            callback(new Error(this.$t("domain.detail.placeholder74") as string));
                        if (parseInt(this.cachekeyArgsForm.mode) === 4 && !allPathPattern.test(value))
                            callback(this.$t("domain.detail.placeholder14"));
                        else callback();
                    },
                    trigger: ["blur", "change"],
                },
                { required: true, message: this.$t("domain.detail.placeholder54"), trigger: "blur" },
            ],
            ignore_params: [
                { required: true, message: this.$t("domain.detail.placeholder15"), trigger: "change" },
            ],
            args: [
                { required: true, message: this.$t("domain.detail.placeholder16"), trigger: "blur" },
                {
                    pattern: "^[^\\u4e00-\\u9fa5]+$",
                    // message: "取值不支持中文",
                    message: this.$t("domain.detail.placeholder17"),
                    trigger: ["blur", "change"],
                },
            ],
            priority: [
                { required: true, message: this.$t("domain.detail.placeholder22"), trigger: "blur" },
                { trigger: "blur", pattern: /^[0-9]*$/, message: this.$t("domain.detail.placeholder21"), },
                {
                    validator: (rule: any, value: number, callback: Function) => {
                        if (value > 100 || value < 1) {
                            callback(new Error(this.$t("domain.detail.placeholder21") as string));
                        } else callback();
                    },
                    trigger: ["blur", "change"],
                },
            ],
        };
    }
    // 动态计算选项
    get cacheModeOptions() {
        return cacheModeOptions?.map(option => {
            let disabled = false;

            // mode 2、3 只支持选一个
            if (option.value === 2 || option.value === 3) {
                disabled = this.cachekey_args.some(item => item.mode === option.value);
            }

            return {
                disabled,
                ...option,
            };
        });
    }
    private fileTypePlaceholder() {
        return getConditionContentPlaceholder(this.cacheModeOptions, parseInt(this.cachekeyArgsForm.mode));
    }
    // 忽略参数 值改变，需要清空 指定参数 输入框的值
    ignore_params_change(curArgs: any) {
        curArgs.args = "";
    }
    private async typeChange(val: number) {
        if (val === 2 || val === 3) {
            this.cachekeyArgsForm.content = "/";
        } else {
            this.cachekeyArgsForm.content = "";
        }
        await formValidate2Field(this.$refs.cachekeyArgsForm as Form);
    }
    private async submit() {
        await formValidate2Promise(this.$refs.cachekeyArgsForm as Form);
        this.$emit("submit");
    }
    private cancel() {
        this.$emit("cancel", "cachekeyArgsDialogVisible");
    }

    showNameSet(cache: any) {
        // 内容为后缀名才弹出弹框
        if (cache.mode !== 0) return;
        // 清掉缓存
        Object.keys(this.extensionAllSelected).forEach((key: any) => {
            this.extensionAllSelected[key] = false;
            this.extensionSelected[key] = [];
        });
        const other: any = [];
        // 过滤有复选框的后缀名
        cache.content
            .split(",")
            .filter((val: any) => val)
            .forEach((item: any) => {
                if (extensionsMap[item]) {
                    this.extensionSelected[extensionsMap[item]].push(item);
                } else {
                    other.push(item);
                }
            });
        // 其他后缀名
        this.extensionOther = other.join(",");
        // 全选按钮是否选中
        Object.keys(this.extensionSelected).forEach(key => {
            this.extensionAllSelected[key] =
                new Set(this.extensionSelected[key]).size === (extensionsOptions as any)[key].list.length;
        });
        this.extensionsDialogVisible = true;
    }
    // 全选按钮逻辑
        checkAllChange(key: any) {
            const val = this.extensionAllSelected[key];
            this.extensionSelected[key] = val ? (extensionsOptions as any)[key].list : [];
        }
        // 选项是否触发全选
        checkSingleChange(key: any) {
            const val = this.extensionSelected[key];
            this.extensionAllSelected[key] = val.length === (extensionsOptions as any)[key].list.length;
        }
        setName() {
            const extentionReg = new RegExp("^\\w{1,9}(?:,\\w{1,9})*$|^$");
            if (!extentionReg.test(this.extensionOther)) {
                (this.$message.error as any)(this.$t("domain.detail.tip30"));
                return;
            }
            this.extensionsDialogVisible = false;
            // 选中复选框的后缀
            const extensions = Object.keys(this.extensionSelected)
                .reduce((rst, key) => {
                    if (this.extensionSelected[key].length > 0) {
                        return rst + "," + this.extensionSelected[key].join(",");
                    }
                    return rst;
                }, "")
                .slice(1);

            // 没有更多，则直接用计算出的结果；如果有更多，则进行拼接
            this.cachekeyArgsForm.content = !this.extensionOther
                ? extensions
                : `${extensions}${extensions ? "," : ""}${this.extensionOther}`;
        }
}
</script>

<style lang="scss" scoped>
.cachekey-args-dialog {
    ::v-deep {
        .el-dialog__body {
            padding: 24px !important;
        }
    }
}
// 缓存 URL 弹窗的样式
::v-deep .el-dialog {
    width: 60%;
    min-width: 340px;
}

.note {
    color: $color-neutral-7;
}
</style>
