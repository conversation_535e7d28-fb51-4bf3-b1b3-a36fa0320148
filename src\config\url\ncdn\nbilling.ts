import { NEW_PREFIX } from "../_PREFIX";

export const nCdnBillingUrl = {
    //产品列表
    // flowProduct: NEW_PREFIX + "/product/package/list", // 改成用 BasicUrl.productList
    //更新产品列表 新接口
    changeProduct: NEW_PREFIX + "/billing/change",
    //更新产品列表
    // updateProduct: LOGIC_ROUTE_PREFIX + "/billing/Change",
    // 流量包列表
    flowList: NEW_PREFIX + "/billing/flowpacket/List",
    // 流量包变更历史
    history: NEW_PREFIX + "/billing/ChangeHistoryList",
    //查询账户渠道
    getChannel: NEW_PREFIX + "/billing/QueryUserInfo",
};


export const newBillingUrl = {
    // CDN按需套餐列表
    cdnOndemandList: NEW_PREFIX + "/new_billing/cdn/list",
    // 全站按需套餐列表
    icdnOndemandList: NEW_PREFIX + "/new_billing/icdn/list",
    // 概览页按需套餐列表
    overviewList: NEW_PREFIX + "/new_billing/overview/list",
    // CDN资源包列表
    cdnPackageList: NEW_PREFIX + "/new_billing/cdn/packet",
    // 全站资源包列表
    icdnPackageList: NEW_PREFIX + "/new_billing/icdn/packet",
    // CDN计费方式变更
    cdnBillingChange: NEW_PREFIX + "/new_billing/cdn/change",
    // 全站计费方式变更
    icdnBillingChange: NEW_PREFIX + "/new_billing/icdn/change",
}
