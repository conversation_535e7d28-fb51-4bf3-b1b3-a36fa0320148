<!--这个组件为了适应v-model绑定为函数时报错，改用：value的形式；原生的elementUI必须用v-model，否则change事件触发的参数永远不会变化-->
<template>
    <el-radio-group v-model="valueInside" @change="handleChange">
        <el-radio v-for="(radioItem, radioIndex) in aryList" :key="radioIndex" :label="radioItem.value">
            {{ radioItem.label }}
        </el-radio>
    </el-radio-group>
</template>

<script>
export default {
    props: {
        value: {
            type: [String, Boolean, Number],
            required: false,
            default: "",
        },
        aryList: {
            type: Array,
            required: false,
            default: () => [],
        },
    },
    data() {
        return {
            valueInside: [],
        };
    },
    watch: {
        value: {
            handler(val) {
                this.valueInside = val;
            },
            immediate: true,
        },
    },
    methods: {
        handleChange(val) {
            this.$emit("change", val);
        },
    },
};
</script>

<style scoped></style>
