<template>
    <batch-edit-checked v-model="checked">
        <el-form
            class="ct-edit-wrapper"
            label-width="140px"
            label-position="right"
            :model="form"
            ref="formRef"
        >
            <host-select-mod
                v-model="form.req_host"
                :label="$t('domain.create.originHost2')"
                prop="req_host"
                host-type="reqHost"
                :rules="rules.req_host"
                :accelerate-domains="domainList"
                :origin-domains="originList.map(itm => itm.origin).filter(itm => !!itm)"
                :disabled="!checked"
                ref="req_host"
                ><span slot="tip">
                    <span>
                        <el-tooltip placement="top">
                            <div slot="content">
                                <div>1. {{ $t("domain.create.tip8") }}</div>
                                <div>
                                    2.
                                    {{
                                        $t(
                                            "domain.如果域名存在指定源站回源host配置时，则不对此域名更新回源host。"
                                        )
                                    }}
                                </div>
                            </div>
                            <ct-svg-icon
                                icon-class="question-circle"
                                class-name="ct-sort-drag-icon"
                            ></ct-svg-icon></el-tooltip></span
                ></span>
            </host-select-mod>
        </el-form>
    </batch-edit-checked>
</template>

<script lang="ts">
import { Component, Mixins, Prop, Watch } from "vue-property-decorator";
import ctSvgIcon from "@/components/ctSvgIcon/index.vue";
import BatchItemMixin from "../mixins/batch.mixin";
import { cloneDeep } from "lodash-es";
import { checkReqHost } from "@/utils/pattern";

@Component({
    name: "BatchEditOriginHost",
    components: {
        ctSvgIcon,
    },
})
export default class BatchEditOriginHost extends Mixins(BatchItemMixin) {
    @Prop({ type: Array, default: () => [] }) domainList!: string[];

    checked = false;
    form = {
        req_host: "",
    };
    originList: any[] = [];
    rules = {
        req_host: [{ required: false, validator: this.checkReqHost, trigger: "blur" }],
    };
    get formData() {
        return cloneDeep(this.form);
    }

    checkReqHost(rule: any, value: any, callback: any) {
        if (value && !checkReqHost(value)) {
            callback(new Error(`${this.$t("domain.create.tip19")}`));
        } else if (this.originList.some((o: any) => o.origin_host) && this.form.req_host) {
            callback(new Error(`${this.$t("domain.create.tip20")}`));
        }
        callback();
    }

    @Watch("form.req_host")
    onReqHostChange() {
        this.checkedWatchCallback();
    }

    checkedWatchCallback(val?: boolean) {
        this.$ctBus.$emit("batchEditOriginHost", this.checked && !!this.form.req_host);
    }

    onOriginListChange(originList: any[]) {
        this.originList = originList;
        this.formRef.validate();
    }

    mounted() {
        this.$ctBus.$on("batchEditOriginList", this.onOriginListChange);
    }

    beforeDestroy() {
        this.$ctBus.$off("batchEditOriginList", this.onOriginListChange);
    }
}
</script>

<style lang="scss" scoped>
@import "../commonStyle.scss";
</style>
