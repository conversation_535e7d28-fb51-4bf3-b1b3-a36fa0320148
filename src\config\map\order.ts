/*
 * @Description: 工单相关的 map 映射配置，scdn 相关的数据即将废弃
 * @Author: wang y<PERSON>
 */

import i18n from "@/i18n";

// 工单归属类型的枚举值
export const OrderTypeEnum = <const>{
    CDN: 2, // cdn 加速配置工单
    SCDN: 3, // scdn 安全配置工单
};

// 工单操作枚举值
export const OrderActionEnum = <const>{
    Create: "4", // 新增
    Update: "5", // 修改
    Enable: "2", // 启用
    Disable: "1", // 停用
    Delete: "3", // 删除
    ProductTypeUpdate: "6", // 只针对CDN加速的加速类型修改。原来已存在的全站之间的加速类型的修改，依旧是Update: "5"
    AreaUpdate: "7", // 加速区域修改
};

// 工单状态的枚举值
export const OrderStatusEnum = <const>{
    Queue: "1", // 未处理
    Progress: "2", // 处理中
    Success: "3", // 完成
    Failure: "4", // 失败
};

// 工单归属类型映射
export const OrderTypeMap = <const>{
    [OrderTypeEnum.CDN]: i18n.t("common.config.orderType.1"),
    [OrderTypeEnum.SCDN]: i18n.t("common.config.orderType.2"),
};

// 工单操作类型映射
export const OrderActionMap = <const>{
    [OrderActionEnum.Disable]: i18n.t("order.actionMap.itm1"),
    [OrderActionEnum.Enable]: i18n.t("order.actionMap.itm2"),
    [OrderActionEnum.Delete]: i18n.t("order.actionMap.itm3"),
    [OrderActionEnum.Update]: i18n.t("order.actionMap.itm5"),
    [OrderActionEnum.Create]: i18n.t("order.actionMap.itm4"),
    [OrderActionEnum.ProductTypeUpdate]: i18n.t("order.actionMap.itm5"),
    [OrderActionEnum.AreaUpdate]: i18n.t("order.actionMap.itm5"),
};

// 工单类型 select options
export const OrderActionOptions = <const>[
    { label: i18n.t("order.actionMap.itm4"), value: OrderActionEnum.Create },
    { label: i18n.t("order.actionMap.itm5"), value: OrderActionEnum.Update },
    { label: i18n.t("order.actionMap.itm2"), value: OrderActionEnum.Enable },
    { label: i18n.t("order.actionMap.itm1"), value: OrderActionEnum.Disable },
    { label: i18n.t("order.actionMap.itm3"), value: OrderActionEnum.Delete },
    { label: i18n.t("order.actionMap.itm5"), value: OrderActionEnum.ProductTypeUpdate },
    { label: i18n.t("order.actionMap.itm5"), value: OrderActionEnum.AreaUpdate },
];

// 工单状态映射
export const OrderStatusMap = <const>{
    // "0": "失败", // 增加 0 失败状态，非标状态不再提供
    [OrderStatusEnum.Queue]: i18n.t("order.statusMap.itm1"), // 工单系统的工单状态初始值是从客户控制台传递数据中获取，而该字段一直传递的是2
    [OrderStatusEnum.Progress]: i18n.t("order.statusMap.itm2"),
    [OrderStatusEnum.Success]: i18n.t("order.statusMap.itm3"),
    [OrderStatusEnum.Failure]: i18n.t("order.statusMap.itm4"),
};

// 工单状态（select options，由于 状态0为非标，而枚举值不易移除该状态，单做处理 options）
export const OrderStatusOptions = <const>[
    { label: i18n.t("order.statusMap.itm2"), value: OrderStatusEnum.Progress },
    { label: i18n.t("order.statusMap.itm3"), value: OrderStatusEnum.Success },
    { label: i18n.t("order.statusMap.itm4"), value: OrderStatusEnum.Failure },
];
