<template>
    <el-dialog
        :title="dialogTitle"
        :append-to-body="true"
        :close-on-click-modal="false"
        :visible="dialogInfo.visible"
        :before-close="() => $emit('cancel')"
        width="800px"
        class="limit-speed-dialog"
    >
        <el-form
            ref="form"
            :model="form"
            :rules="dialogInfo.visible ? getRules : {}"
            label-width="140px"
            :validate-on-rule-change="false"
        >
            <common-condition v-model="form" :list="dialogInfo.list" :required="false" />
            <el-form-item prop="frequency_threshold" :label="$t('domain.entryLimit.访问阈值')">
                <el-input v-model.number="form.frequency_threshold">
                    <template slot="append">
                        {{ $t("domain.entryLimit.次/秒") }}
                    </template>
                </el-input>
            </el-form-item>
            <el-form-item prop="priority" :label="$t('domain.detail.label48')">
                <el-input v-model.number="form.priority" />
            </el-form-item>
        </el-form>

        <div slot="footer">
            <el-button class="dialog-btn" @click="cancel">{{ $t("domain.cancel") }} </el-button>
            <el-button type="primary" class="dialog-btn" @click="submit">{{
                $t("common.dialog.submit")
            }}</el-button>
        </div>
    </el-dialog>
</template>

<script lang="ts">
import { Component, Prop, Watch, Ref, Vue } from "vue-property-decorator";
import { getLang } from "@/utils";
import commonCondition from "@/components/commonCondition/index.vue";
import { EntryLimitItem, getEntryLimitDefaultItem } from "./util";
import { ElForm } from "@cutedesign/ui";

@Component({
    name: "EntryLimitDialog",
    components: { commonCondition },
})
export default class EntryLimitDialog extends Vue {
    @Prop({ type: Object })
    dialogInfo!: {
        visible: boolean;
        type: string;
        form: EntryLimitItem;
        list: EntryLimitItem[];
    };
    @Ref("form")
    formRef!: ElForm;
    form = getEntryLimitDefaultItem();
    rules = {};

    @Watch("dialogInfo.visible", { immediate: true })
    onInit() {
        if (!this.dialogInfo.visible) {
            setTimeout(() => {
                this.form = {
                    id: "",
                    mode: null,
                    content: "",
                    frequency_threshold: 0,
                    priority: 0,
                };
                this.formRef?.clearValidate();
            }, 100);
            return;
        }

        this.form = { ...this.dialogInfo.form };
        this.$nextTick(() => {
            this.formRef.clearValidate();
        });
    }
    cancel() {
        this.$emit("cancel");
    }
    async submit() {
        const isValid = await new Promise(resolve => this.formRef.validate(resolve));
        if (!isValid) return;

        this.$emit("submit", { ...this.form });
    }
    get dialogTitle() {
        const seprate = getLang() === "en" ? " " : "";
        return `${
            this.dialogInfo.type === "create" ? this.$t("domain.add2") : this.$t("domain.modify")
        }${seprate}${this.$t("domain.entryLimit.IP访问限频规则")}`;
    }
    get getRules() {
        return {
            frequency_threshold: [
                { required: true, message: this.$t("domain.entryLimit.请输入访问阈值"), trigger: "blur" },
                {
                    type: "number",
                    min: 1,
                    max: 100000,
                    message: this.$t("domain.entryLimit.访问阈值取值范围", [1, 100000]),
                },
            ],
            priority: [
                { required: true, message: this.$t("domain.detail.placeholder20"), trigger: "blur" },
                {
                    type: "number",
                    min: 1,
                    max: 100,
                    message: this.$t("simpleForm.alogicCacheMixin.ValidationErrorMessages.InvalidPriority"),
                },
            ],
        };
    }
}
</script>
