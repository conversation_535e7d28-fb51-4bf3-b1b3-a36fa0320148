<!--
 * @Description: 布局容器
 * @Author: wang yuegong
 -->

<template>
    <div class="ct-box">
        <div class="ct-box-head" v-if="showTitle">
            <slot name="header">
                <span class="title" :title="titleText || title">
                    <i v-if="titleIcon" :class="titleIcon"></i>{{ title }}
                </span>
            </slot>
        </div>
        <div class="ct-box-tags" v-if="!!tags">
            <span class="text">{{ tags }}</span>
            <slot name="tags-slot"></slot>
        </div>
        <slot></slot>
    </div>
</template>

<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";

@Component({
    name: "CtBox",
})
export default class CtBox extends Vue {
    @Prop({ type: String }) private title?: string;
    @Prop({ type: String }) private titleText?: string;
    @Prop({ type: String }) private titleIcon?: string;
    @Prop({ type: String }) private tags?: string;

    get showTitle() {
        return !!this.title || !!this.$slots.header;
    }
}
</script>
<style lang="scss" scoped>
.ct-box {
    // @include g-media-attr(
    //     (
    //         attr: "padding",
    //         sm: $common-space-5x,
    //         xs: $common-space-5x,
    //     )
    // );
    padding: 20px;
    width: 100%;
    // 注意：不要给 ct-box hight:100% 小屏下展示会有一定的问题，交给自适应
    min-height: 100%; // 为了各页面内容区都能铺满页面，多个 ct-box 一起使用时局部定义
    background-color: $g-color-white;
    transition: all ease 0.3s;
    border-radius: 3px;
    border-color: $color-neutral-3;
    box-shadow: $shadow-1;
}

.ct-box-head {
    @include g-clear-fix;
    @include g-height-lineheight($height: $g-fs-strong * 2);
    border-bottom: 1px solid $g-page-bg-color;

    .title {
        float: left;
        @include g-set-font($font-size: $g-fs-strong);

        i {
            margin-right: 8px;
        }
    }
    .more {
        float: right;
        cursor: pointer;
    }
}
.ct-box-tags {
    margin-bottom: 20px;
    line-height: 1.5;
    font-size: 0;
    .text {
        display: inline-block;
        padding: 0 20px;
        font-size: 14px;
        border-left: 4px solid $color-master;
        line-height: 1.5;
        vertical-align: middle;
    }
}
</style>
