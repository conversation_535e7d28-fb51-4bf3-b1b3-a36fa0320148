<template>
    <div class="ct-edit-wrapper">
        <el-form
            label-width="140px"
            label-position="left"
            :model="form"
            :rules="rules"
            ref="htmlForbidForm"
            :disabled="!isEdit || !isService || isLockHtmlForbid"
        >
            <div>
                <el-form-item :label="$t('domain.htmlForbid.forbid1')" prop="switch">
                    <el-switch
                        v-model="form.html_forbid_switch"
                        :active-value="true"
                        :inactive-value="false"
                        @change="html_forbid_switch_change"
                    ></el-switch>
                </el-form-item>
                <div v-if="form.html_forbid_switch" class="switch-wrapper">
                    <div v-for="(item, index) in form.html_forbid_op" :key="index">
                        <el-form-item
                            :label="$t('domain.type')"
                            :prop="`html_forbid_op.${index}.mode`"
                            :rules="rules.mode"
                        >
                            <el-radio-group
                                v-model.number="item.mode"
                                @change="typeChange($event, index)"
                                class="cache-form--selector"
                            >
                                <el-radio :label="0">{{ $t("domain.detail.cacheModeMap[0]") }}</el-radio>
                                <el-radio :label="1">{{ $t("domain.detail.cacheModeMap[1]") }}</el-radio>
                                <el-radio :label="2">{{ $t("domain.detail.cacheModeMap[2]") }}</el-radio>
                                <el-radio :label="3">{{ $t("domain.detail.cacheModeMap[3]") }}</el-radio>
                                <el-radio :label="4">{{ $t("domain.detail.cacheModeMap[4]") }}</el-radio>
                            </el-radio-group>
                        </el-form-item>

                        <el-form-item
                            key="content"
                            :class="{
                                'is-required':
                                    item.mode !== '' && item.mode !== null && item.mode !== undefined,
                            }"
                            :label="$t('domain.content')"
                            :prop="`html_forbid_op.${index}.content`"
                            :rules="rules.content"
                        >
                            <el-input
                                v-model="item.content"
                                @change="handleChange"
                                :placeholder="fileTypePlaceholder(item)"
                                :disabled="item.mode === 2 || item.mode === 3"
                                clearable
                                class="input-style"
                                @focus="e => showNameSet(e, item)"
                            ></el-input>
                        </el-form-item>
                        <el-row>
                            <el-col :span="10">
                                <el-form-item
                                    :label="$t('domain.htmlForbid.forbid2')"
                                    :prop="`html_forbid_op.${index}.forbid_copy`"
                                    :rules="rules.forbid_copy"
                                >
                                    <el-switch
                                        v-model="item.forbid_copy"
                                        active-value="on"
                                        inactive-value="off"
                                        @change="forbid_copy_change(index)"
                                    ></el-switch>
                                </el-form-item>
                            </el-col>

                            <el-col :span="12">
                                <el-form-item
                                    :label="$t('domain.htmlForbid.forbid3')"
                                    label-width="140px"
                                    :prop="`html_forbid_op.${index}.forbid_select`"
                                    :rules="rules.forbid_select"
                                >
                                    <el-switch
                                        v-model="item.forbid_select"
                                        active-value="on"
                                        inactive-value="off"
                                        @change="forbid_copy_change(index)"
                                    ></el-switch>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </div>
                </div>
            </div>
        </el-form>

        <file-suffix-dialog
            :form="suffixDialogForm.form"
            :visible="suffixDialogForm.visible"
            @cancel="suffixDialogForm.visible = false"
            @submit="suffixDialogFormSubmit"
        />
    </div>
</template>

<script>
// import ctSvgIcon from "@/components/ctSvgIcon";
import componentMixin from "@/views/domainConfig/componentMixin";
import { cloneDeep } from "lodash-es";
import { allPathPattern } from "@/utils/validator.utils";
import fileSuffixDialog from "@/components/fileSuffixDialog/index.vue";
import { getConditionContentPlaceholder } from '@/components/commonCondition/utils';
import { cacheModeOptions } from '@/components/simpleform/nAlogicCacheTable/mixin';
import { SecurityAbilityModule } from "@/store/modules/securityAbility";

export default {
    name: "privateBucketOrigin",
    components: {
        fileSuffixDialog,
        // ctSvgIcon,
    },
    mixins: [componentMixin],
    props: {
        datas: Object,
        isLockHtmlForbid: Boolean,
    },
    data() {
        return {
            form: {
                html_forbid_switch: true,
                html_forbid_op: [
                    {
                        mode: null,
                        content: "aaa",
                        forbid_copy: "on",
                        forbid_select: "on",
                    },
                ],
            },
            suffixDialogForm: {
                visible: false,
                form: null,
            },
            currentForbidItem: null,
            rules: {
                mode: [{ required: false, validator: this.validMode, trigger: "change" }],
                content: [
                    {
                        validator: (rule, value, callback) => {
                            if (this.isLockHtmlForbid) callback();
                            const validIndex = rule.field.split(".")[1];
                            const mode =
                                this.form.html_forbid_op && this.form.html_forbid_op[validIndex]?.mode;
                            const paths = value?.split(",");
                            paths?.forEach(path => {
                                if (parseInt(mode) === 4 && (path.includes("?") || path.includes("？")))
                                    callback(new Error(this.$t("domain.detail.placeholder48")));
                                if (parseInt(mode) === 4 && path.length > 0 && path[0] !== "/")
                                    callback(new Error(this.$t("domain.detail.placeholder49")));
                            });
                            if (parseInt(mode) === 0 && !value)
                                callback(new Error(this.$t("domain.htmlForbid.forbid4")));
                            if (parseInt(mode) === 0 && !/^\w{1,9}(,\w{1,9})*$/.test(value))
                                callback(new Error(this.$t("domain.detail.placeholder50")));
                            if (parseInt(mode) === 1 && !value)
                                callback(new Error(this.$t("domain.detail.placeholder73")));
                            if (parseInt(mode) === 1 && !/^((\/[\w-.]+)+,)*(\/[\w-.]+)+$/.test(value))
                                callback(new Error(this.$t("domain.detail.placeholder51")));
                            if (parseInt(mode) === 2 && !value)
                                callback(new Error(this.$t("domain.detail.placeholder52")));
                            if (parseInt(mode) === 3 && !value)
                                callback(new Error(this.$t("domain.detail.placeholder53")));
                            if (parseInt(mode) === 4 && value.trim() === "")
                                callback(new Error(this.$t("domain.detail.placeholder74")));
                            if (parseInt(mode) === 4 && !allPathPattern.test(value))
                                callback(this.$t("domain.detail.placeholder14"));
                            else callback();
                        },
                        trigger: ["blur", "change"],
                    },
                ],
                forbid_copy: {
                    required: false,
                    validator: this.valid_forbid_copy,
                    trigger: ["blur", "change"],
                },
                forbid_select: {
                    required: false,
                    validator: this.valid_forbid_select,
                    trigger: ["blur", "change"],
                },
            },
        };
    },
    computed: {},
    watch: {
        datas: {
            deep: true,
            handler(val) {
                this.init(val);
            },
            immediate: true,
        },
    },
    methods: {
        html_forbid_switch_change(val) {
            if (val && this.form.html_forbid_op && this.form.html_forbid_op.length === 0) {
                const getTime = new Date().getTime();
                const data = {
                    id: `html_forbid_op_${getTime}`,
                    mode: null,
                    content: "",
                    forbid_copy: "on",
                    forbid_select: "on",
                };
                this.form.html_forbid_op?.push(data);
            } else {
                this.form.html_forbid_op = [];
            }

            const originalConf = SecurityAbilityModule.securityBasicConfigOriginForm;
            if (val && originalConf.html_forbid_op?.length) {
                this.form.html_forbid_op = cloneDeep(originalConf.html_forbid_op);
            }

            this.$emit("onChange", this.form.html_forbid_op);
        },
        init(val) {
            if (val?.html_forbid_op && val.html_forbid_op?.length > 0) {
                this.form.html_forbid_switch = true;
                this.form.html_forbid_op = cloneDeep(val.html_forbid_op);
            } else {
                this.form.html_forbid_switch = false;
                this.form.html_forbid_op = [];
            }
        },
        async typeChange(val, index) {
            if (val === 2 || val === 3) {
                this.form.html_forbid_op[index].content = "/";
            } else {
                this.form.html_forbid_op[index].content = "";
            }
            this.$refs.htmlForbidForm.validateField(`html_forbid_op.${index}.content`);
            this.$emit("onChange", this.form.html_forbid_op);
        },
        handleChange() {
            this.$emit("onChange", this.form.html_forbid_op);
        },
        forbid_copy_change(index) {
            this.$refs.htmlForbidForm.validateField(`html_forbid_op.${index}.forbid_copy`);
            this.$refs.htmlForbidForm.validateField(`html_forbid_op.${index}.forbid_select`);
            this.$emit("onChange", this.form.html_forbid_op);
        },
        validMode(rule, value, callback) {
            const validIndex = rule.field.split(".")[1];
            if (this.isLockHtmlForbid) return callback();
            if (
                (value === "" || value === null || value === undefined) &&
                this.form.html_forbid_op[validIndex]?.content
            ) {
                return callback(new Error(this.$t("domain.htmlForbid.forbid5")));
            } else {
                return callback();
            }
        },
        valid_forbid_copy(rule, value, callback) {
            if (this.isLockHtmlForbid) return callback();
            const validIndex = rule.field.split(".")[1];
            const forbid_copy = this.form.html_forbid_op && this.form.html_forbid_op[validIndex]?.forbid_copy;
            const forbid_select =
                this.form.html_forbid_op && this.form.html_forbid_op[validIndex]?.forbid_select;
            if (forbid_copy === "off" && forbid_select === "off") {
                return callback(new Error(this.$t("domain.htmlForbid.forbid6")));
            } else {
                return callback();
            }
        },
        valid_forbid_select(rule, value, callback) {
            if (this.isLockHtmlForbid) return callback();
            const validIndex = rule.field.split(".")[1];
            const forbid_copy = this.form.html_forbid_op && this.form.html_forbid_op[validIndex]?.forbid_copy;
            const forbid_select =
                this.form.html_forbid_op && this.form.html_forbid_op[validIndex]?.forbid_select;
            if (forbid_copy === "off" && forbid_select === "off") {
                return callback(new Error(this.$t("domain.htmlForbid.forbid6")));
            } else {
                return callback();
            }
        },
        fileTypePlaceholder(data) {
            return getConditionContentPlaceholder(cacheModeOptions, parseInt(data?.mode));
        },
        showNameSet(e, form) {
            // 内容为后缀名才弹出弹框
            if (form.mode !== 0) return;
            this.currentForbidItem = form;
            this.suffixDialogForm = {
                visible: true,
                form: cloneDeep(form)
            };
            e?.srcElement?.blur();
        },
        suffixDialogFormSubmit(content) {
            this.currentForbidItem.content = content;
            this.suffixDialogForm.visible = false;
            this.handleChange();
        }
    },
};
</script>

<style lang="scss" scoped>
.ct-edit-wrapper {
    ::v-deep {
        .el-form--label-left .el-form-item__label {
            text-align: right;
        }
    }
}
.input-style {
    width: 380px;
}
</style>
