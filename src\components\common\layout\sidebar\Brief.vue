<template>
    <div class="brief">
        <div class="brief-wrapper">
            <p class="name">{{ title }}</p>
            <Outlink />
        </div>
    </div>
</template>
<script>
import Outlink from './Outlink.vue';

export default {
    components: {
        Outlink,
    },
    data() {
        return {
            // pkgName: process.env.PKG_NAME.toLocaleUpperCase(),
            // pkgTitle: process.env.PKG_TITLE,
        };
    },
    computed: {
        title() {
            return process.env?.PLATFORM === "bs" ? "CDN-业务运营分析平台" : this.$t("layout.sidebarTitle");
        }
    },
    mounted() {
        process.env.PLATFORM === "bs" && !window.__POWERED_BY_QIANKUN__ && (document.title = "CDN-业务运营分析平台");
    }
};
</script>
<style lang="scss" scoped>
.name {
    font-size: 14px;
    color: #333;
    font-weight: 700;
    line-height: 1;
    font-family: -apple-system, "Noto Sans", "Helvetica Neue", Helvetica, "Nimbus Sans L", <PERSON><PERSON>, "Liberation Sans", "PingFang SC", "Hiragino Sans GB", "Noto Sans CJK SC", "Source Han Sans SC", "Source Han Sans CN", "Microsoft YaHei", "Wenquanyi Micro Hei", "WenQuanYi Zen Hei", "ST Heiti", SimHei, "WenQuanYi Zen Hei Sharp", sans-serif;
}
.logo {
    @include g-width(50px, 80px);
    @include g-height(50px, 80px);
    border-radius: 50%;
}
.brief-wrapper {
    border-bottom: 1px solid #e2e5ed;
    padding: 24px 0 16px 8px;
    margin: 0 16px 12px 16px;
}
</style>
