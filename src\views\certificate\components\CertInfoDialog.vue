<template>
    <el-dialog
        :title="$t('certificate.edit.title')"
        :close-on-click-modal="false"
        :visible="certInfoVisible"
        :before-close="previous"
        append-to-body
        class="add-dialog"
        width="720px"
    >
        <div class="cert-wrapper">
            <div class="progress-wrapper">
                <el-steps :active="currentActiveStep" class="progress" finish-status="success" align-center>
                    <el-step :title="this.$t('certificate.edit.title')"></el-step>
                    <el-step :title="this.$t('certificate.edit.parseCert')"></el-step>
                </el-steps>
            </div>
            <div class="cert-text-wrapper">
                <el-form :model="updateDetail" ref="updateDetail" :label-width="isEn ? '200px' : '160px'">
                    <el-form-item :label="$t('certificate.detail.label[0]')">
                        <span>{{ updateDetail.name }}</span>
                    </el-form-item>
                    <el-form-item :label="$t('certificate.detail.label[1]')">
                        <span>{{ updateDetail.cn }}</span>
                    </el-form-item>
                    <el-form-item :label="$t('certificate.detail.label[3]')">
                        <span>{{ updateDetail.issuer }}</span>
                    </el-form-item>
                    <el-form-item :label="$t('certificate.detail.label[8]')">
                        <span>{{ updateDetail.expires }}</span>
                    </el-form-item>
                    <el-form-item :label="$t('certificate.detail.label[4]')">
                        <span>{{ updateDetail.validTime }}</span>
                    </el-form-item>
                    <el-form-item :label="$t('certificate.detail.label[6]')">
                        <span>{{ updateDetail.addDomainChange }}</span>
                    </el-form-item>
                    <el-form-item :label="$t('certificate.detail.label[7]')">
                        <span>{{ updateDetail.reduceDomainChange }}</span>
                    </el-form-item>
                </el-form>
            </div>
        </div>
        <div v-loading="loading" slot="footer">
            <el-button @click="previous">
                {{ $t("common.dialog.previous") }}
            </el-button>
            <el-button type="primary" @click="submit">
                {{ $t("common.dialog.submit") }}
            </el-button>
        </div>
    </el-dialog>
</template>

<script>
import { nUserModule } from "@/store/modules/nuser";
import { getLang } from "@/utils";
import { CertificateUrl } from "@/config/url/certificate";
import { cloneDeep } from "lodash-es";

export default {
    name: "CertInfoDialog",
    components: {},
    props: {
        certificate: {
            type: Object,
            default: () => ({}),
        },
        // visible: {
        //     type: Boolean,
        //     default: false,
        // },
        // updateDetail: {
        //     type: Object,
        //     default: () => ({}),
        // },
        // currentActiveStep: {
        //     type: Number,
        //     default: 0,
        // },
    },
    data() {
        return {
            certInfoVisible: false,
            currentActiveStep: 0,
            updateDetail: {
                name: "", // 证书备注名
                cn: "", // 证书通用名称
                issuer: "", // 证书品牌
                expires: "", // 过期时间
                validTime: "", // 证书有效期
                addDomainChange: "", // 新增域名变化
                reduceDomainChange: "", // 减少域名变化
            },
            form: {
                certName: "",
                certs: "",
                key: "",
            },
            isAddMargin: false,
            loading: false,
        };
    },
    computed: {
        isEn() {
            return getLang() === "en";
        },
        email() {
            return nUserModule.userInfo.email;
        },
        userName() {
            return nUserModule.userInfo.name;
        },
    },
    watch: {
        // visible: {
        //     handler(val) {
        //         this.certInfoVisible = val;
        //     },
        //     deep: true,
        //     immediate: true,
        // },
        certificate: function(val) {
            this.init(cloneDeep(val));
        },
    },
    mounted() {
        this.$ctBus.$on("fromEditCertToCertInfo", data => {
            this.updateDetail = cloneDeep(data);
            this.currentActiveStep = 1;
            this.certInfoVisible = true;
        });
    },

    beforeDestroy() {
        this.$ctBus.$off("fromEditCertToCertInfo");
    },

    methods: {
        // 点击：上一步
        previous() {
            this.certInfoVisible = false;
            this.$ctBus.$emit("previous");
        },
        // 点击：确定
        submit() {
            // this.deployCert();
            this.certInfoVisible = false;
            this.$ctBus.$emit("certInfoSubmit");
        },
        // async deployCert() {
        //     await this.$ctFetch(CertificateUrl.deploys, {
        //         method: "POST",
        //         body: {
        //             data: {
        //                 cert_id: this.certificate.id,
        //             },
        //         },
        //         headers: {
        //             "Content-Type": "application/json",
        //         },
        //     }).catch(e => {
        //         store.$errorHandler(e);
        //     });
        // },
        init(val) {
            if (!val) return;
            this.$nextTick(() => {
                this.form.certName = val?.name;
            });
        },
    },
};
</script>

<style lang="scss" scoped>
.add-dialog {
    .table-scroll-wrap {
        width: 100%;
        display: flex;
        flex-direction: row;
        justify-content: center;
        .cert-wrapper {
            width: 50%;
        }
    }
    .content-wrapper {
        padding: 20px 0;
    }
    .progress-wrapper {
        width: 100%;
        display: flex;
        justify-content: center;
        .progress {
            width: 500px;
        }
    }
    .cert-text-wrapper {
        margin-top: 44px;
        margin-left: 16%;
        ::v-deep {
            .el-form-item.el-form-item--medium,
            .el-form-item.el-form-item--medium .el-form-item__content,
            .el-form-item.el-form-item--medium .el-form-item__label {
                font-size: 12px !important;
                line-height: 16px !important;
            }
        }
    }
    .icon-wrapper {
        color: #ff8f34 !important;
    }
}
</style>
