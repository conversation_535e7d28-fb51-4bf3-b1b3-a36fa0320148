<template>
    <el-form-item :label="isSm2 ? $t('certificate.国密证书') : $t('certificate.国际标准证书')" v-bind="$attrs">
        <div style="display: flex; gap: 12px">
            <el-select
                style="width: 380px"
                :value="value"
                ref="cert_name_ref"
                clearable
                :placeholder="$t('domain.create.placeholder1')"
                :disabled="!enable || lock"
                filterable
                default-first-option
                :loading="loading"
                @change="handleCertNameChange"
            >
                <el-option
                    v-for="itm in currentCertList"
                    :key="itm.cert_name"
                    :label="itm.cert_name"
                    :value="itm.cert_name"
                ></el-option>
            </el-select>
            <i
                :style="{
                    alignSelf: 'center',
                    cursor: canRefresh ? 'pointer' : 'not-allowed',
                    fontSize: '14px',
                }"
                :class="['aocdn-ignore-link', loading ? 'el-icon-loading' : 'el-icon-refresh-right']"
                @click="canRefresh && getCertListWrap()"
            />
        </div>
        <slot name="refresh"></slot>
        <div class="tooltips">
            <span>
                <ct-svg-icon icon-class="info-circle" class-name="icon-column-label1" />
            </span>
            <span class="tip-style">
                {{ $t("domain.editPage.certTip") }}
            </span>
            <span
                @click="isEdit && isService && enable && !lock && showCertUpload()"
                :class="{
                    'btn-bg': isEdit && isService && enable && !lock,
                }"
                >{{ $t("domain.create.tip9-1") }}</span
            >
        </div>

        <upload-cert-dialog
            :addVisible="addVisible"
            :certificate="addCertificate"
            :isFromCertList="false"
            :dataList="[]"
            @cancel="addVisible = false"
            @submit="submitCertificate"
        />
    </el-form-item>
</template>

<script setup lang="ts">
import { ref, computed, defineProps, defineEmits, defineExpose, getCurrentInstance } from "vue";
import type { Ref } from "vue";
import ctSvgIcon from "@/components/ctSvgIcon/index.vue";
import { useComponentMixin } from "@/views/domainConfig/mixins/componentMixin";
import { getDefaultCertParam } from "@/views/certificate/util";
import type { certParam } from "@/views/certificate/util";
import UploadCertDialog from "@/views/certificate/components/UploadCertDialog.vue";
import { nCertificateUrl } from "@/config/url";

// 类型定义
interface CertItem {
    cert_name: string;
    algorithm_type: number;
}

// 组件设置
const instance = getCurrentInstance();
const { proxy } = instance || {};

// 响应式状态
const loading = ref(false);
const cert_name_ref: Ref<any> = ref(null);
const addVisible = ref(false);
const addCertificate: Ref<certParam> = ref(getDefaultCertParam());

// 组合式函数
const { isEdit, isService, securityDomain } = useComponentMixin();

// 事件定义
const emit = defineEmits(["input", "change"]);

const props = defineProps({
    value: {
        type: String,
        default: "",
    },
    enable: {
        type: Boolean,
        default: false,
    },
    lock: {
        type: Boolean,
        default: false,
    },
    cert_name_list: {
        type: Array as () => CertItem[],
        default: () => [],
    },
    getCertList: {
        type: Function,
        default: () => () => Promise.resolve(),
    },
    // 是否为国密证书
    isSm2: {
        type: Boolean,
        default: false,
    },
});

// 根据国密标志获取当前证书列表的计算属性
const currentCertList = computed(() =>
    props.cert_name_list.filter(item => item.algorithm_type === (props.isSm2 ? 1 : 0))
);

// 刷新按钮状态的计算属性
const canRefresh = computed(() => !loading.value && isEdit.value && isService.value && !props.lock);

/**
 * 获取证书列表
 */
const getCertListWrap = () => {
    if (!props.getCertList) return;
    loading.value = true;
    props.getCertList().finally(() => {
        loading.value = false;
    });
};

/**
 * 业务逻辑函数
 * @param val 证书名称
 */
const handleCertNameChange = async (val: string) => {
    emit("input", val);
    emit("change", val);
};

/**
 * 失去焦点
 */
const blur = () => {
    cert_name_ref.value?.blur();
};

/**
 * 显示证书上传弹窗
 */
const showCertUpload = () => {
    if (!securityDomain.value) {
        proxy?.$message.error(proxy?.$t("domain.create.tip31") as string);
    } else {
        addVisible.value = true;
        addCertificate.value = getDefaultCertParam();
        addCertificate.value.algorithm_type = props.isSm2 ? 1 : 0;
    }
};

/**
 * 提交证书
 */
const submitCertificate = async () => {
    try {
        // 域名编辑时，aocdn 与 fcdn 都使用 ncdn 的创建接口
        const data = {
            ...addCertificate.value,
            domain: securityDomain.value,
        };

        if (addCertificate.value.algorithm_type !== 1) {
            delete data.certs_sign;
            delete data.key_sign;
        }

        await proxy?.$ctFetch(nCertificateUrl.createDomainCert, {
            method: "POST",
            body: data,
        });
        proxy?.$message.success(proxy?.$t("domain.create.tip32") as string);
        addVisible.value = false;
        getCertListWrap();
    } catch (e) {
        proxy?.$errorHandler(e);
    }
};

// 暴露方法给父组件
defineExpose({
    blur,
});
</script>
<style lang="scss" scoped>
@import "@/views/domainConfig/commonStyle.scss";
</style>
