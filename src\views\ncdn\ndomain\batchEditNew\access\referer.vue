<template>
    <batch-edit-checked v-model="checked">
        <el-form
            class="ct-edit-wrapper"
            label-width="140px"
            label-position="right"
            :model="form"
            :rules="rules"
            ref="formRef"
            v-loading="loading"
            :disabled="!checked"
        >
            <!-- Referer防盗链 开关 -->
            <el-form-item :label="$t('domain.create.referer')" prop="referer">
                <el-switch
                    v-model="form.referer"
                    active-value="on"
                    inactive-value="off"
                    @change="referer_switch_change"
                ></el-switch>
            </el-form-item>
            <div v-if="form.referer === 'on'" class="switch-wrapper">
                <!-- 是否允许空referer访问 -->
                <el-form-item :label="$t('domain.create.referer2')" prop="allow_empty">
                    <el-switch v-model="form.allow_empty" active-value="on" inactive-value="off"></el-switch>
                </el-form-item>

                <!-- 是否允许空协议 -->
                <el-form-item
                    :label="$t('domain.create.referer3')"
                    prop="referer_empty_protocol"
                    v-if="isNewEcgw"
                >
                    <el-switch
                        v-model="form.referer_empty_protocol"
                        active-value="on"
                        inactive-value="off"
                    ></el-switch>
                </el-form-item>

                <!-- 匹配所有端口 -->
                <el-form-item :label="$t('domain.create.referer4')" prop="match_all_ports" v-if="isNewEcgw">
                    <el-switch
                        v-model="form.match_all_ports"
                        active-value="on"
                        inactive-value="off"
                    ></el-switch>
                </el-form-item>

                <!-- 忽略大小写 -->
                <el-form-item :label="$t('domain.create.referer5')" prop="ignore_case" v-if="isNewEcgw">
                    <el-switch v-model="form.ignore_case" active-value="on" inactive-value="off"></el-switch>
                </el-form-item>

                <!-- 是否追加 -->
                <el-form-item prop="is_append" v-if="isNewEcgw">
                    <span slot="label">
                        {{ $t("domain.create.referer6") }}
                        <span>
                            <el-tooltip placement="top" :content="$t('domain.create.tip53')">
                                <ct-svg-icon
                                    icon-class="question-circle"
                                    class-name="ct-sort-drag-icon"
                                ></ct-svg-icon>
                            </el-tooltip>
                        </span>
                    </span>
                    <el-switch v-model="form.is_append" :active-value="1" :inactive-value="0"></el-switch>
                </el-form-item>

                <!-- 类型 -->
                <el-form-item
                    :label="$t('domain.type')"
                    prop="domainList"
                    key="domainList"
                    :rules="rules.domainList"
                >
                    <div class="radio-row">
                        <el-radio-group v-model="form.refererType">
                            <el-radio label="allow">{{ $t("domain.detail.trustlist") }}</el-radio>
                            <el-radio label="block">{{ $t("domain.detail.blocklist") }}</el-radio>
                        </el-radio-group>
                    </div>
                    <div>
                        <el-input
                            class="textarea-wrapper"
                            v-model="form.domainList"
                            type="textarea"
                            :rows="6"
                            :placeholder="refererPlaceholder"
                        />
                    </div>
                </el-form-item>
            </div>
        </el-form>
    </batch-edit-checked>
</template>

<script lang="ts">
import { Component, Prop, Mixins } from "vue-property-decorator";
import ctSvgIcon from "@/components/ctSvgIcon/index.vue";
import { BasicUrl } from "@/config/url/basic";
import BatchItemMixin from "../mixins/batch.mixin";
import { cloneDeep } from "lodash-es";

interface Form {
    referer: string;
    allow_empty: string;
    refererType: string;
    domainList: string;
    referer_empty_protocol: string;
    match_all_ports: string;
    ignore_case: string;
    is_append: number;
}

const getDefaultForm = (): Form => {
    return {
        referer: "off",
        allow_empty: "on",
        refererType: "allow",
        domainList: "",
        referer_empty_protocol: "off",
        match_all_ports: "off",
        ignore_case: "off",
        is_append: 0,
    };
};

@Component({
    name: "BatchEditReferer",
    components: {
        ctSvgIcon,
    },
})
export default class BatchEditReferer extends Mixins(BatchItemMixin) {
    @Prop({ type: Boolean, default: true }) isNewEcgw!: boolean;

    form: Form = getDefaultForm();
    maxNum = 400;
    loading = false;
    rules = {
        domainList: [{ required: false, validator: this.valid_domain_list, trigger: "blur" }],
    };
    mounted() {
        this.getConfig();
    }
    get refererPlaceholder() {
        const { maxNum } = this;
        if (this.isNewEcgw) {
            return this.$t("domain.detail.tip37", { maxNum: maxNum });
        } else {
            return this.$t("domain.detail.tip38", { maxNum: maxNum });
        }
    }
    get domainList() {
        // 需要过滤空表内容
        return this.form.domainList
            ?.split("\n")
            ?.map(i => i?.trim())
            ?.filter(i => i);
    }
    // Referer防盗链开关关闭重新打开，需要将类型设置为白名单
    referer_switch_change(val: "on" | "off") {
        this.form = {
            ...getDefaultForm(),
            referer: val,
        };
    }
    valid_domain_list(rule: any, value: any, callback: any) {
        if (!value) {
            return callback();
        }
        const { maxNum, domainList } = this;
        if (!this.form?.refererType && this.form?.domainList) {
            return callback(new Error(this.$t("domain.detail.tip42") as string));
        } else if (domainList && domainList.length > maxNum) {
            return callback(new Error(this.$t("domain.detail.tip43", { maxNum: maxNum }) as string));
        } else {
            // 检查重复
            const hasRepeat = new Set(domainList).size !== domainList.length;
            if (hasRepeat) {
                return callback(new Error(this.$t("domain.detail.tip44") as string));
            }
            return callback();
        }
    }
    onIsAppendChange(val: boolean) {
        val && (this.form.domainList = "");
    }
    async getConfig() {
        try {
            this.loading = true;
            const { refererLimit } = await this.$ctFetch<{ refererLimit: number }>(BasicUrl.getConfig, {
                cache: true,
            });
            refererLimit && (this.maxNum = refererLimit);
        } catch (e) {
            this.$errorHandler(e);
        } finally {
            this.loading = false;
        }
    }
    get formData() {
        const form: Form = cloneDeep(this.form);

        if (form.referer !== "on") {
            return {
                white_referer: {},
                black_referer: {},
            };
        }

        return {
            [this.form.refererType === "allow" ? "white_referer" : "black_referer"]: {
                allow_empty: form.allow_empty,
                allow_list: this.domainList,
                referer_empty_protocol: form.referer_empty_protocol,
                match_all_ports: form.match_all_ports,
                ignore_case: form.ignore_case,
                is_append: form.is_append,
            },
            [this.form.refererType === "allow" ? "black_referer" : "white_referer"]: {},
        };
    }
}
</script>

<style lang="scss" scoped>
@import "../commonStyle.scss";

.ct-edit-wrapper {
    width: 100%;
}

.textarea-wrapper {
    width: calc(100% - 120px);
}
</style>
