/*
 * @Description: 域名业务相关
 * @Author: wang yuegong
 */

import { LOGIC_ROUTE_PREFIX, NEW_PREFIX } from "../_PREFIX";

// 自动表单operation获取
export const N_URL_FORM_OPERATION_BATCHSUBMIT = `${NEW_PREFIX}/domain/batchUpdate`; // 批量修改域名
export const N_URL_FORM_OPERATION_SUBMIT = `${NEW_PREFIX}/domain/create`;
// export const N_URL_FORM_OPERATION_GET = `${LOGIC_ROUTE_PREFIX}/form/operation/Get`;
// export const N_URL_FORM_OPERATION_EDIT = `${LOGIC_ROUTE_PREFIX}/form/util/GetDomainDetail`;
// export const N_URL_FORM_OPERATION_FROM_ORDER = `${LOGIC_ROUTE_PREFIX}/order/GetDetail`;
// export const N_URL_FORM_OPERATION_EDIT_SUBMIT = `${NEW_PREFIX}/domain/update`; // 修改域名

// 域名相关
export const nDomainUrl = {
    // 域名列表（产品实例列表）
    domainList: NEW_PREFIX + "/domain/list", // 原/product/instance/List
    // domainList: LOGIC_ROUTE_PREFIX + "/product/instance/List",
    // 变更域名状态（启停删）
    changeDomainStatus: NEW_PREFIX + "/domain/changeStatus",
    // 获取域名基础状态
    domainBasicConfig: `${NEW_PREFIX}/domain/DomainBasicConfig`,
    // 创建更新加速区域或者加速类型的工单
    updateAreaOrProduct: `${NEW_PREFIX}/domain/updateAreaOrProduct`,
    // 域名校验接口
    validateDomain: `${NEW_PREFIX}/domain/validate`,
    // 域名查看接口
    domainDetail: `${NEW_PREFIX}/domain/detail`,
    // 域名编辑
    // domainUpdate: `${NEW_PREFIX}/domain/update`,
    // 域名批量修改
    // domainBatchUpdate: `${NEW_PREFIX}/domain/batchUpdate`,
    // VIVO客户
    domainIsVivo: `${NEW_PREFIX}/domain/isVivo`,
    // 域名批量新增
    domainBatchCreate: `${NEW_PREFIX}/domain/batchCreate`,
    // cdn 域名列表 ipv6 开关
    ipv6EnableUrl: `${NEW_PREFIX}/domain/updateIpv6`,
    // cdn 域名列表 高性能网络 开关
    virtualEnableUrl: `${NEW_PREFIX}/domain/updateVirtual`,
    // 判断是否支持CDN加速HTTPS功能
    checkSupportHttps: `${NEW_PREFIX}/new_billing/cdn/https`,
};

// 证书相关
export const nCertUrl = {
    // 添加证书（定制接口，会先执行域名和证书的匹配判断）
    createCert: NEW_PREFIX + "/cert/domain/Create",
};
// 配置项
export const nBasicUrl = {
    // 获取配置项：product_type-加速类型；domain_type-域名类型
    getProp: NEW_PREFIX + "/basic/getProperty",
};

export const checkNewUser = NEW_PREFIX + "/checkNewUser";
