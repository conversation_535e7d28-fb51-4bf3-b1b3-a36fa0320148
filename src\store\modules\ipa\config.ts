import { VuexModule, Module, Action, Mutation, getModule } from "vuex-module-decorators";
import store from "@/store";
import { ctFetch } from "@/utils";
import { DomainUrl } from "@/config/url/ipa/domain";

interface BaseIpaConfig {
    ipaFloatWinEnable: boolean; // 文档悬浮框开关
    ipaRegionAccessControlEnable: boolean; // 区域访问控制
    ipaNewLogDownLoadEnable: boolean; // 新版日志下载开关
    ipaDomainSelectorLimit: number; // 域名下拉框数量限制
    ipaLogDownloadSpan: number; // 日志下载时间范围
}

@Module({ dynamic: true, store, name: "ipaConfig" })
class IpaConfig extends VuexModule implements BaseIpaConfig {
    public ipaFloatWinEnable = false; // 文档悬浮框开关
    public ipaRegionAccessControlEnable = false; // 区域访问控制
    public ipaNewLogDownLoadEnable = false; // 新版日志下载开关
    public ipaDomainSelectorLimit = 0; // 域名下拉框数量限制
    public ipaLogDownloadSpan = 365; // 日志下载时间范围

    @Mutation
    SET_KEY_VALUE<T extends keyof BaseIpaConfig>({ key, value }: { key: T; value: BaseIpaConfig[T] }) {
        (this as any)[key] = value;
    }

    @Action
    public async GetIpaBasicConfig() {
        const { result } = await ctFetch<{
            result: {
                float_win_enable: boolean;
                region_access_control: boolean;
                new_log_download_enable: boolean;
                log_download_span: number;
                domain_selector_limit: number;
            };
        }>(DomainUrl.ipaConfig, { cache: true });
        this.SET_KEY_VALUE({ key: "ipaFloatWinEnable", value: result?.float_win_enable ?? false });
        this.SET_KEY_VALUE({ key: "ipaRegionAccessControlEnable", value: result?.region_access_control ?? false });
        this.SET_KEY_VALUE({ key: "ipaNewLogDownLoadEnable", value: result?.new_log_download_enable ?? false });
        this.SET_KEY_VALUE({ key: "ipaDomainSelectorLimit", value: result?.domain_selector_limit ?? 0});
        this.SET_KEY_VALUE({ key: "ipaLogDownloadSpan", value: result?.log_download_span ?? 365 });
    }
}

export const IpaConfigModule = getModule(IpaConfig);
