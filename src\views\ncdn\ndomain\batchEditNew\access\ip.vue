<template>
    <batch-edit-checked v-model="checked">
        <el-form
            class="ct-edit-wrapper"
            label-width="140px"
            label-position="right"
            :model="form"
            :rules="rules"
            ref="formRef"
            :disabled="!checked"
        >
            <!-- IP黑白名单 开关 -->
            <el-form-item :label="$t('domain.create.ip')" prop="ip_switch">
                <el-switch
                    v-model="form.ip_switch"
                    active-value="on"
                    inactive-value="off"
                    @change="ip_switch_change"
                ></el-switch>
            </el-form-item>
            <div v-if="form.ip_switch === 'on'" class="switch-wrapper">
                <!-- 类型 -->
                <el-form-item :label="$t('domain.type')" prop="ip" :rules="rules.ip">
                    <div>
                        <div class="radio-row">
                            <el-radio-group v-model="form.ipType" @change="ipTypeChange">
                                <el-radio label="allow">{{ $t("domain.detail.trustlist") }}</el-radio>
                                <el-radio label="block">{{ $t("domain.detail.blocklist") }}</el-radio>
                            </el-radio-group>
                        </div>
                        <div>
                            <el-input
                                class="textarea-wrapper"
                                v-model="form.ip"
                                type="textarea"
                                :rows="3"
                                :placeholder="ipPlaceholder"
                                @change="onIpChange"
                            />
                        </div>
                    </div>
                </el-form-item>
            </div>
        </el-form>
    </batch-edit-checked>
</template>

<script lang="ts">
import { Component, Mixins } from "vue-property-decorator";
import BatchItemMixin from "../mixins/batch.mixin";
import { cloneDeep } from "lodash-es";

interface Form {
    ip_switch: "off" | "on";
    ipType: "allow" | "block";
    ip: string;
}

@Component({
    name: "BatchEditIpBlackWhiteList",
})
export default class BatchEditIpBlackWhiteList extends Mixins(BatchItemMixin) {
    form: Form = {
        ip_switch: "off",
        ipType: "allow",
        ip: "",
    };
    maxNum = 400;
    isLimitNum = false; // 是否限制黑白名单ip个数

    rules = {
        ip: [{ required: false, validator: this.valid_ip, trigger: "blur" }],
    };
    get ipPlaceholder() {
        const { maxNum, isLimitNum } = this;
        if (isLimitNum) {
            return this.$t("domain.detail.tip39", { maxNum: maxNum });
        } else {
            return this.$t("domain.detail.tip40");
        }
    }
    get ipList() {
        const { form } = this;
        const { ip = "" } = form || {};
        return ip.split("\n");
    }
    // IP黑白名单开关关闭重新打开，需要将类型设置为白名单
    ip_switch_change() {
        this.$set(this.form, "ipType", "allow");
        this.$set(this.form, "ip", "");

        this.$emit("onChange", this.form);
    }
    // 类型 事件
    ipTypeChange() {
        this.$emit("onChange", this.form);
    }
    // 输入框触发事件
    onIpChange() {
        this.$emit("onChange", this.form);
    }
    valid_ip(rule: any, value: any, callback: any) {
        // 支持 ip 段
        const ipReg =
            /^((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)(\/(\d|[1-2]\d|3[0-2]))?$|^([\da-fA-F]{1,4}:){6}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)(\/([1-9]?\d|(1([0-1]\d|2[0-8]))))?$|^::([\da-fA-F]{1,4}:){0,4}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)(\/([1-9]?\d|(1([0-1]\d|2[0-8]))))?$|^([\da-fA-F]{1,4}:):([\da-fA-F]{1,4}:){0,3}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)(\/([1-9]?\d|(1([0-1]\d|2[0-8]))))?$|^([\da-fA-F]{1,4}:){2}:([\da-fA-F]{1,4}:){0,2}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)(\/([1-9]?\d|(1([0-1]\d|2[0-8]))))?$|^([\da-fA-F]{1,4}:){3}:([\da-fA-F]{1,4}:){0,1}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)(\/([1-9]?\d|(1([0-1]\d|2[0-8]))))?$|^([\da-fA-F]{1,4}:){4}:((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)(\/([1-9]?\d|(1([0-1]\d|2[0-8]))))?$|^([\da-fA-F]{1,4}:){7}[\da-fA-F]{1,4}(\/([1-9]?\d|(1([0-1]\d|2[0-8]))))?$|^:((:[\da-fA-F]{1,4}){1,6}|:)(\/([1-9]?\d|(1([0-1]\d|2[0-8]))))?$|^[\da-fA-F]{1,4}:((:[\da-fA-F]{1,4}){1,5}|:)(\/([1-9]?\d|(1([0-1]\d|2[0-8]))))?$|^([\da-fA-F]{1,4}:){2}((:[\da-fA-F]{1,4}){1,4}|:)(\/([1-9]?\d|(1([0-1]\d|2[0-8]))))?$|^([\da-fA-F]{1,4}:){3}((:[\da-fA-F]{1,4}){1,3}|:)(\/([1-9]?\d|(1([0-1]\d|2[0-8]))))?$|^([\da-fA-F]{1,4}:){4}((:[\da-fA-F]{1,4}){1,2}|:)(\/([1-9]?\d|(1([0-1]\d|2[0-8]))))?$|^([\da-fA-F]{1,4}:){5}:([\da-fA-F]{1,4})?(\/([1-9]?\d|(1([0-1]\d|2[0-8]))))?$|^([\da-fA-F]{1,4}:){6}:(\/([1-9]?\d|(1([0-1]\d|2[0-8]))))?$/;

        const { ipList, maxNum, form, isLimitNum } = this;

        if (!form?.ipType && form?.ip) {
            return callback(new Error(this.$t("domain.detail.tip42") as string));
        } else if (!form?.ip?.trim()) {
            return callback(new Error(this.$t("domain.detail.tip45") as string));
        } else if (ipList && ipList.length > maxNum && isLimitNum) {
            return callback(new Error(this.$t("domain.detail.tip46", { maxNum: maxNum }) as string));
        } else {
            for (let i = 0; i < ipList?.length; i++) {
                const item = ipList[i]?.trim();
                // 输入的不是 ip
                if (!ipReg.test(item)) {
                    return callback(new Error(this.$t("domain.detail.tip47") as string));
                }
            }

            // 检查重复
            const hasRepeat = new Set(ipList).size !== ipList.length;
            if (hasRepeat) {
                return callback(new Error(this.$t("domain.detail.tip48") as string));
            }
            return callback();
        }
    }
    get formData() {
        const form: Form = cloneDeep(this.form);

        if (form.ip_switch === "off") {
            return {
                ip_white_list: "",
                ip_black_list: "",
            };
        }

        return {
            [form.ipType === "allow" ? "ip_white_list" : "ip_black_list"]: form.ip
                .split("\n")
                .map(i => i.trim())
                .join(","),
            [form.ipType === "allow" ? "ip_black_list" : "ip_white_list"]: "",
        };
    }
}
</script>

<style lang="scss" scoped>
@import "../commonStyle.scss";
.ct-edit-wrapper {
    width: 100%;
}
.textarea-wrapper {
    width: calc(100% - 120px);
}
</style>
