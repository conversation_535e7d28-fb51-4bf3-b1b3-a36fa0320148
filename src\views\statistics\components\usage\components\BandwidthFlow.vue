<template>
    <div class="chart-wrap">
        <div class="title-wrap">
            <div class="operate-btn">
                <el-radio-group v-model="chartType" size="mini">
                    <el-radio-button :label="$t('statistics.dcdn.bandwidthFlowWhole.radioBtn1')"></el-radio-button>
                    <el-radio-button :label="$t('statistics.dcdn.bandwidthFlowWhole.radioBtn2')"></el-radio-button>
                </el-radio-group>
            </div>
            <template v-if="showBandwidth">
                <div class="total">
                    <div class="tip">
                        {{ $t("statistics.common.vchartTip1", { type: "", num: "" }) }}{{ titleTipSuffix }}：
                        <span class="num">
                            {{ fetchData.topBandwidth | convertBandwidthB2P(scale) }}
                        </span>
                        <span class="date">{{ fetchData.topBandwidthTime | timeFormat }}</span>
                    </div>
                    <div class="tip">
                        {{ dailyPeakMonthlyTitle }}{{ titleTipSuffix }}：
                        <span class="num">
                            {{ (isDailyPeakAccount ? dailyPeakAmount.val1 : fetchData.top95Bandwidth) |
                                convertBandwidthB2P(scale) }}
                        </span>
                    </div>
                </div>
                <div class="total" v-if="useCompare">
                    <div class="tip">
                        {{ $t("statistics.common.vchartTip1", { type: "", num: "2" }) }}：
                        <span class="num">
                            {{ fetchData2.topBandwidth | convertBandwidthB2P(scale) }}
                        </span>
                        <span class="date">{{ fetchData2.topBandwidthTime | timeFormat }}</span>
                    </div>
                    <div class="tip">
                        {{ dailyPeakMonthlyTitle }}2：
                        <span class="num">
                            {{ (isDailyPeakAccount ? dailyPeakAmount.val2 : fetchData2.top95Bandwidth) |
                                convertBandwidthB2P(scale) }}
                        </span>
                    </div>
                </div>
            </template>
            <template v-else>
                <div class="total">
                    <div class="tip">
                        {{ $t("statistics.common.vchartTip3", { type: "", num: "" }) }}{{ titleTipSuffix }}：
                        <span class="num">{{ fetchData.totalFlow | convertFlowB2P(scale) }}</span>
                    </div>
                    <div class="tip" v-if="useCompare">
                        {{ $t("statistics.common.vchartTip3", { type: "", num: "2" }) }}：
                        <span class="num">{{ fetchData2.totalFlow | convertFlowB2P(scale) }}</span>
                    </div>
                </div>
            </template>
        </div>

        <v-chart class="chart" v-loading="loading" :element-loading-text="$t('statistics.common.chart.loading')"
            autoresize theme="cdn" :options="options" @legendselectchanged="legendselectchanged" />

        <ct-tip>
            {{ $t("statistics.usageQuery.BandwidthFlow.ctTip") }}
            <el-tooltip effect="dark" :content="$t('statistics.common.searchDownloadContent')" placement="right">
                <i class="el-icon-download usage-download-icon" @click="downloadTable"></i>
            </el-tooltip>
        </ct-tip>

        <el-table :data="fetchData.daily" v-loading="loading"
            :element-loading-text="$t('statistics.common.table.loading')" :empty-text="$t('common.table.empty')">
            <el-table-column :label="$t('statistics.usageQuery.BandwidthFlow.tableColumn1')" prop="date"
                :sortable="true"></el-table-column>
            <el-table-column :label="$t('statistics.usageQuery.BandwidthFlow.tableColumn2', {
                unit: wrapWithBrackets(indentFlowConfig.unit),
            })
                ">
                <template slot-scope="{ row }">
                    {{ (row.flow / indentFlowConfig.scale).toFixed(2) }}
                </template>
            </el-table-column>
            <el-table-column :label="$t('statistics.usageQuery.BandwidthFlow.tableColumn3', {
                unit: wrapWithBrackets(indentQueryBandwidthConfig.unit),
            })
                ">
                <template slot-scope="{ row }">
                    {{ (row.topBandwidth / indentQueryBandwidthConfig.scale).toFixed(2) }}
                </template>
            </el-table-column>
            <el-table-column :label="$t('statistics.usageQuery.BandwidthFlow.tableColumn4')">
                <template slot-scope="{ row }">
                    {{ row.topTime | timeFormat }}
                </template>
            </el-table-column>
            <el-table-column :label="$t('statistics.usageQuery.BandwidthFlow.tableColumn5', {
                unit: wrapWithBrackets(indentMissBandwidthConfig.unit),
            })
                ">
                <template slot-scope="{ row }">
                    {{ (row.topMissBandwidth / indentMissBandwidthConfig.scale).toFixed(2) }}
                </template>
            </el-table-column>
            <el-table-column :label="$t('statistics.usageQuery.BandwidthFlow.tableColumn6')">
                <template slot-scope="{ row }">
                    {{ row.topMissTime | timeFormat }}
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<script lang="ts">
import { Component } from "vue-property-decorator";
import { mixins } from "vue-class-component";
import { StatisticsUsageUrl } from "@/config/url/statistics";
import { downloadCsv } from "@/utils";
import { timeFormat } from "@/filters/index";
import { SearchParams } from "@/types/statistics/usage";
import { QueryFetchData, QueryDaily } from "@/types/statistics/usage";
import { THEME_AREA_STYLE } from "@/config/echart/cdn-theme";
import { themeColorArr } from "@cdnplus/common/config/echart/theme";
import ChartMixin from "../chartMixin";
import { StatisticsModule } from "@/store/modules/statistics";
import { divideScale } from "@/utils/unit";
import { get } from "lodash-es";
import { cloneDeep } from "lodash-es";

type tooltipParam = { name: string; marker: string; value: string | number };

const defaultFetchData = {
    "5min": [],
    daily: [],
    topBandwidth: 0,
    top95Bandwidth: 0,
    totalFlow: 0,
    avgQueryFlow: 0,
    dailyPeakMonthlyAverage: 0,
    topBandwidthTime: 0,
}

@Component({
    name: "BandwidthFlow",
    filters: { timeFormat }
})
export default class BandwidthFlow extends mixins(ChartMixin) {
    chartType = `${this.$t("statistics.usageQuery.BandwidthFlow.chartType")}`; //
    useCompare = false; // 是否使用了数据对比
    // 缓存请求参数
    private searchParams2?: SearchParams;
    // 接口数据
    fetchData: QueryFetchData = { ...defaultFetchData };
    fetchData2: QueryFetchData = {
        "5min": [],
        daily: [],
        topBandwidth: 0,
        top95Bandwidth: 0,
        totalFlow: 0,
        avgQueryFlow: 0,
        dailyPeakMonthlyAverage: 0,
        topBandwidthTime: 0,
    };
    protected downloadDataList: QueryFetchData["5min"] = []; // 用于下载的数据

    private timeMinus?: number; // 时间差
    private legend1Selected = true; // 是否选择了图例1
    private selectedLegends: any = null // 记录当前勾选legend
    private currentLegends: any[] = [] // 记录当前全量legend，方便对应数据

    dailyPeakAmount = {
        val1: 0,
        val2: 0,
    }

    // 请求生成，type： flow-流量带宽数据 request-请求数 status-回源数据
    private async localFetchGenerator<T>(params: SearchParams, type = "flow") {
        const url = type === "flow" ? StatisticsUsageUrl.queryList : StatisticsUsageUrl.queryBwSourceList;
        const rst = await this.fetchGenerator<T>(url, {
            ...params,
        });

        return rst;
    }

    // 是否为需要展示日峰值月平均的账号
    get isDailyPeakAccount() {
        return StatisticsModule.dailyPeakMonthlyAverageAccount;
    }

    get dailyPeakMonthlyTitle() {
        return this.isDailyPeakAccount ? `${this.$t("statistics.usageQuery.tip1")}` : `${this.$t("statistics.usageQuery.tip2")}`;
    }

    // 当前展示是否为带宽（流量）
    get showBandwidth() {
        return this.chartType === `${this.$t("statistics.usageQuery.BandwidthFlow.chartType")}`;
    }

    // 获取 seriesData 所使用的 key
    get seriesDataKey() {
        return this.showBandwidth ? "bandwidth" : "flow";
    }

    // 标题1的后缀（存在数据对比时需要）
    get titleTipSuffix() {
        return this.useCompare ? "1" : "";
    }

    // 根据最大值获取图表的单位规则 { 单位，缩进 }
    get chartUnitConfig() {
        // 单数据 or 多数据
        let max = 0,
            max1 = 0,
            max2 = 0
        if (this.useCompare) {
            // const max1 = this.getMaxFromList(this.fetchData["5min"], this.seriesDataKey);
            // const max2 = this.getMaxFromList(this.fetchData2["5min"], this.seriesDataKey);
            if (!this.selectedLegends || this.selectedLegends[this.currentLegends[0]]) {
                max1 = this.getMaxFromList(this.fetchData["5min"], this.seriesDataKey);
            }
            if (!this.selectedLegends || this.selectedLegends[this.currentLegends[1]]) {
                max2 = this.getMaxFromList(this.fetchData2["5min"], this.seriesDataKey);
            }
            max = Math.max(max1, max2);
        } else {
            max = this.getMaxFromList(this.fetchData["5min"], this.seriesDataKey);
        }

        return this.showBandwidth ? this.getBandwidthUnitConfig(max) : this.getFlowUnitConfig(max);
    }

    // 1、数据请求
    protected async getData(params1: SearchParams, params2: SearchParams) {
        // 有参数2，说明开启了对齐
        this.useCompare = !!params2;

        // 重置当前legends
        this.selectedLegends = null;

        // 缓存参数
        this.searchParams2 = params2;

        if (!this.useCompare) {

            let rst: { daily: QueryDaily[] };
            [this.fetchData, rst] = await Promise.all([
                this.localFetchGenerator<QueryFetchData>(params1),
                this.localFetchGenerator<{ daily: QueryDaily[] }>(params1, "status"),
            ]);

            if (!this.fetchData) this.fetchData = cloneDeep(defaultFetchData);

            const missDaily = (get(rst, "daily", []) || []) as QueryDaily[];

            if (this.fetchData?.daily?.length) {
                for (let i = missDaily?.length - 1; i >= 0; i--) {
                    this.fetchData.daily[i].topMissBandwidth = missDaily[i].topBandwidth;
                    this.fetchData.daily[i].topMissTime = missDaily[i].topTime;
                }
            }

            this.assignDailyPeakMonthlyAverage([this.fetchData], [params1]);
        } else {
            let missDaily1: QueryDaily[], missDaily2: QueryDaily[];
            // 记录时间差，防止清空时间后tooltip报错
            this.timeMinus = (params1.startTime - params2.startTime) * 1000;
            [
                this.fetchData,
                { daily: missDaily1 = [] },
                this.fetchData2,
                { daily: missDaily2 = [] },
            ] = await Promise.all([
                this.localFetchGenerator<QueryFetchData>(params1),
                this.localFetchGenerator<{ daily: QueryDaily[] }>(params1, "status"),
                this.localFetchGenerator<QueryFetchData>(params2),
                this.localFetchGenerator<{ daily: QueryDaily[] }>(params2, "status"),
            ]);

            if (!this.fetchData) this.fetchData = cloneDeep(defaultFetchData);
            if (!this.fetchData2) this.fetchData2 = cloneDeep(defaultFetchData);

            this.assignDailyPeakMonthlyAverage([this.fetchData], [params1]);
            this.assignDailyPeakMonthlyAverage([this.fetchData2], [params2], true);

            if (this.fetchData?.daily?.length && this.fetchData2?.daily?.length) {
                // 合并两个接口中的回源数据和常规数据
                for (let i = missDaily1?.length - 1; i >= 0; i--) {
                    this.fetchData.daily[i].topMissBandwidth = missDaily1[i].topBandwidth;
                    this.fetchData.daily[i].topMissTime = missDaily1[i].topTime;
                    this.fetchData2.daily[i].topMissBandwidth = missDaily2[i].topBandwidth;
                    this.fetchData2.daily[i].topMissTime = missDaily2[i].topTime;
                }
                // 合并两组信息并去重
                this.fetchData.daily = (this.fetchData.daily || [])
                    ?.concat(this.fetchData2.daily || [])
                    .sort((a, b) => Number(new Date(a.date)) - Number(new Date(b.date)));

                for (let i = 1; i < this.fetchData?.daily?.length; i++) {
                    if (this.fetchData.daily[i].date === this.fetchData.daily[i - 1].date) {
                        this.fetchData.daily.splice(i, 1);
                    }
                }
            }
        }

        // 处理用于下载的数据
        this.downloadDataList = !this.useCompare
            ? (get(this.fetchData, "5min", []) || [])
            : (get(this.fetchData2, "5min", []) || []).concat(get(this.fetchData, "5min", []) || []);
    }

    // 日带宽峰值相关逻辑
    async assignDailyPeakMonthlyAverage(fetchDataList: QueryFetchData[], paramsList: SearchParams[], isCompare = false) {
        if (fetchDataList.length === 1 && paramsList.length === 1) {
            const params = paramsList[0];
            const key = isCompare ? "val2" : "val1";
            const fetchData = fetchDataList[0];
            if (!fetchData) return;

            if (params.interval !== "5m") {
                // 如果当前客户是日带宽峰值账号，且当前时间间隔不是5分钟，需要重新请求一次，把间隔改为5分钟
                const { dailyPeakMonthlyAverage = 0 } = await this.localFetchGenerator<QueryFetchData>({
                    ...params,
                    interval: "5m",
                })
                this.dailyPeakAmount[key] = dailyPeakMonthlyAverage;
            } else {
                this.dailyPeakAmount[key] = fetchData.dailyPeakMonthlyAverage;
            }
        }
    }

    // 2、数据处理
    get options() {
        const { seriesDataKey } = this;
        // 根据 switch 获取差异化数据
        const title = this.showBandwidth
            ? `${this.$t("statistics.usageQuery.BandwidthFlow.chartOptions.title1")}`
            : `${this.$t("statistics.usageQuery.BandwidthFlow.chartOptions.title2")}`;
        const { unit, scale } = this.chartUnitConfig;
        const yAxisName = `${this.$t("statistics.usageQuery.BandwidthFlow.chartOptions.yAxisName", {
            unit: unit,
        })}`;

        const xAxisData: string[] = [];
        const seriesData: string[] = [];
        const fetchDataList = this.fetchData["5min"] || [];
        fetchDataList
            .sort((a, b) => a.bandwidthTime - b.bandwidthTime)
            .forEach(item => {
                xAxisData.push(timeFormat(item.bandwidthTime * 1000).replace(" ", "\n"));
                seriesData.push(((item[seriesDataKey] || 0) / scale).toFixed(2));
            });

        const options = {
            ...this.comOptions,
            tooltip: {
                trigger: "axis",
                formatter: (a: tooltipParam[]) =>
                    `${a[0].name}<br>${a[0].marker}${title}: ${a[0].value}${unit}`,
            },
            xAxis: {
                type: "category", // 类目轴，从 data 中获取数据
                boundaryGap: false, // 坐标轴两边留白策略
                axisLabel: {
                    formatter: this.xAxisFormatterGenerator(),
                },
                data: xAxisData,
            },
            yAxis: {
                type: "value", // 数值轴
                name: yAxisName,
            },
            series: [
                {
                    type: "line",
                    data: seriesData,
                    areaStyle: THEME_AREA_STYLE[themeColorArr[0]],
                },
            ],
        };

        // 没有数据对比，则直接返回配置
        if (!this.useCompare) return options;

        // 使用数据对比，则继续组装配置
        // const xAxisData2: string[] = [];
        const seriesData2: string[] = [];
        const fetchDataList2 = this.fetchData2["5min"] || [];
        fetchDataList2
            .sort((a, b) => a.bandwidthTime - b.bandwidthTime)
            .forEach(item => {
                // xAxisData2.push(timeFormat(item.timestamp * 1000).replace(" ", "\n"));
                seriesData2.push(((item[seriesDataKey] || 0) / scale).toFixed(2));
            });
        // 调整 series
        options.series = [
            {
                name: `${title}1`,
                type: "line",
                data: seriesData,
                areaStyle: THEME_AREA_STYLE[themeColorArr[0]],
            },
            {
                name: `${title}2`,
                type: "line",
                data: seriesData2,
                areaStyle: THEME_AREA_STYLE[themeColorArr[1]],
            },
        ];
        // 增加图例
        options.legend = {
            data: [`${title}1`, `${title}2`],
        };
        this.selectedLegends && (options.legend.selected = this.selectedLegends)
        // 更新当前全量legend
        this.currentLegends = options.legend?.data;
        // 调整 tooltip ，动态控制 tooltip ，如果图例只选择一条时就只显示一组信息
        options.tooltip.formatter = (a: tooltipParam[]) => {
            // a[0].name带有换行符号，在firefox下new Date时会异常
            const name2 = timeFormat(
                Number(new Date(Number(new Date(a[0].name.replace("\n", " "))) - this.timeMinus!))
            );
            const name = this.legend1Selected ? a[0].name : name2;
            let tip = `${name}<br>${a[0].marker}${title}: ${a[0].value}${unit}`;
            if (a[1]) {
                tip += `<br>${name2}<br>${a[1].marker}${title}: ${a[1].value}${unit}`;
            }
            return tip;
        };
        return options;
    }

    // 监听图例选择变化
    legendselectchanged({ selected }: { selected: { [legend: string]: boolean } }) {
        const title = this.showBandwidth
            ? `${this.$t("statistics.usageQuery.BandwidthFlow.chartOptions.title1")}`
            : `${this.$t("statistics.usageQuery.BandwidthFlow.chartOptions.title2")}`;
        this.legend1Selected = selected[`${title}1`];
        // 更新勾选legend
        this.selectedLegends = selected;
    }

    // 获取各种缩进计算的配置（根据最大值获取）
    get indentQueryBandwidthConfig() {
        const max = this.getMaxFromList(get(this.fetchData, "daily", []), "topBandwidth");
        return this.getBandwidthUnitConfig(max, true);
    }
    get indentMissBandwidthConfig() {
        const max = this.getMaxFromList(get(this.fetchData, "daily", []), "topMissBandwidth");
        return this.getBandwithUnitConfigWithFixedScale(max, true, 1000);
    }
    get indentFlowConfig() {
        const max = this.getMaxFromList(get(this.fetchData, "daily", []), "flow");
        return this.getFlowUnitConfig(max, true);
    }

    // 重写 excel 数据拼接方法
    protected tableToExcel() {
        let str = `${this.$t("statistics.usageQuery.BandwidthFlow.tableToExcel.excelColumn1")},${this.chartType === `${this.$t("statistics.usageQuery.BandwidthFlow.tableToExcel.excelColumn2")}`
            ? `${this.$t("statistics.usageQuery.BandwidthFlow.tableToExcel.excelColumn3", {
                mbps: this.Mbps,
            })}`
            : `${this.$t("statistics.usageQuery.BandwidthFlow.tableToExcel.excelColumn4", {
                mb: this.MB,
            })}`
            }\n`;

        // 输出格式
        str += this.downloadDataList.reduce((str, item) => {
            str += `${timeFormat(item["bandwidthTime"] * 1000)},`;
            str += (item[this.seriesDataKey] / Math.pow(this.scale, 2)).toFixed(2) + "\n";
            return str;
        }, "");

        // 增加峰值带宽和95峰值带宽
        str += `\n${this.$t("statistics.usageQuery.BandwidthFlow.tableToExcel.excelColumn5")}\n`;

        const { titleTipSuffix, fetchData: _fetchData, fetchData2: _fetchData2 } = this;
        // 下载的数据单位以M为基底，需要先进行转换
        const fetchData = this.processQueryFetchData(_fetchData);
        const fetchData2 = this.processQueryFetchData(_fetchData2);
        const { topBandwidth, top95Bandwidth, totalFlow, avgQueryFlow } = fetchData;

        if (this.showBandwidth) {
            str += `${this.$t("statistics.usageQuery.BandwidthFlow.tableToExcel.excelColumn6", {
                num: "",
                suffix: titleTipSuffix,
            })},${topBandwidth} ${this.Mbps} \n ${this.dailyPeakMonthlyTitle}${titleTipSuffix},${this.isDailyPeakAccount ? divideScale(this.dailyPeakAmount.val1) : top95Bandwidth} ${this.Mbps} \n`;

            if (this.useCompare) {
                str += `${this.$t("statistics.usageQuery.BandwidthFlow.tableToExcel.excelColumn6", {
                    num: "2",
                    suffix: "",
                })},${fetchData2.topBandwidth} ${this.Mbps} \n ${this.dailyPeakMonthlyTitle}2,${this.isDailyPeakAccount ? divideScale(this.dailyPeakAmount.val2) : fetchData2.top95Bandwidth} ${this.Mbps} \n`;
            }
        } else {
            str += `${this.$t("statistics.usageQuery.BandwidthFlow.tableToExcel.excelColumn8", {
                num: "",
                suffix: titleTipSuffix,
            })},${totalFlow} ${this.MB} \n ${this.$t(
                "statistics.usageQuery.BandwidthFlow.tableToExcel.excelColumn9",
                {
                    num: "",
                    suffix: titleTipSuffix,
                }
            )},${avgQueryFlow} ${this.MB} \n`;

            if (this.useCompare) {
                str += `${this.$t("statistics.usageQuery.BandwidthFlow.tableToExcel.excelColumn8", {
                    num: "2",
                    suffix: "",
                })},${fetchData2.totalFlow} ${this.MB} \n ${this.$t(
                    "statistics.usageQuery.BandwidthFlow.tableToExcel.excelColumn9",
                    {
                        num: "2",
                        suffix: "",
                    }
                )},${fetchData2.avgQueryFlow} ${this.MB} \n`;
            }
        }

        this.downloadExcel({
            name: `${this.$t("statistics.usageQuery.BandwidthFlow.tableToExcel.excelName", {
                chartType: this.chartType,
            })}`,
            str,
        });
    }
    // 重写下载方法
    protected downloadExcel({ name, str }: { name: string; str: string }) {
        // 此时要用缓存的参数2判断是否启用了数据对比，以保证参数和数据一致
        const useCompare = !!this.searchParams2;

        // 存在对比的时候，是从2个时间范围内选择时间点
        let { startTime, endTime } = this.searchParams;
        if (useCompare) {
            startTime = startTime < this.searchParams2!.startTime ? startTime : this.searchParams2!.startTime;

            endTime = endTime > this.searchParams2!.endTime ? endTime : this.searchParams2!.endTime;
        }

        const t1 = timeFormat(+startTime)
            .replace(/-|\s|:/g, "")
            .slice(0, 12);
        const t2 = timeFormat(+endTime)
            .replace(/-|\s|:/g, "")
            .slice(0, 12);

        downloadCsv(`${name}${t1}-${t2}`, str);
    }

    protected downloadTable() {
        if (this.checkListIsEmpty(this.fetchData?.daily)) return;

        let str = `${this.$t("statistics.usageQuery.BandwidthFlow.tableColumn1")},${this.$t('statistics.usageQuery.BandwidthFlow.tableColumn2', {
            unit: this.wrapWithBrackets(this.MB),
        })},${this.$t('statistics.usageQuery.BandwidthFlow.tableColumn3', {
            unit: this.wrapWithBrackets(this.Mbps),
        })},${this.$t('statistics.usageQuery.BandwidthFlow.tableColumn4')},${this.$t('statistics.usageQuery.BandwidthFlow.tableColumn5', {
            unit: this.wrapWithBrackets("Mbps"),
        })},${this.$t('statistics.usageQuery.BandwidthFlow.tableColumn6')}\n`;
        this.fetchData.daily.forEach((item => {
            str += item.date + ",";
            str += (+item.flow / Math.pow(this.scale, 2)).toFixed(2) + ",";
            str += (+item.topBandwidth / Math.pow(this.scale, 2)).toFixed(2) + ",";
            str += timeFormat(item.topTime) + ",";
            str += (+item.topMissBandwidth / Math.pow(1000, 2)).toFixed(2) + ",";
            str += timeFormat(item.topMissTime) + ",\n";
        }))

        this.downloadExcel({
            name: `${this.$t("statistics.common.tab[0]")}-${this.$t("statistics.usageQuery.BandwidthFlow.ctTip")}`,
            str
        })
    }
}
</script>

<style lang="scss" scoped></style>
