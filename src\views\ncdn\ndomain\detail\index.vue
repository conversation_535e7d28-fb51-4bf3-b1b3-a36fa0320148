<template>
    <ct-section-wrap :key="detailKey">
        <template #header>
            <ct-section-header>
                <template #title>
                    <h3>
                        {{ domain }}
                        <cute-state :color="statusColorMap[status]">
                            {{ state }}
                        </cute-state>
                    </h3>
                    <template>
                        <el-button type="text" icon="el-icon-d-arrow-left" @click="handleTargetToDomainList">
                            {{ $t("domain.domainList") }}
                        </el-button>
                    </template>
                </template>
            </ct-section-header>
        </template>
        <div class="form-wrapper">
            <acceleration-config />
        </div>
    </ct-section-wrap>
</template>

<script>
import AccelerationConfig from "@/views/domainConfig/accelerationConfig/index.vue";
import { SecurityAbilityModule } from "@/store/modules/securityAbility";
import { DomainStatusMap } from "@/config/map";
import variables from "@cutedesign/ui/style/themes/default/index.scss";
import { StatisticsModule } from "@/store/modules/statistics";
import { handleLeaveModule } from "@/utils/common.js";

export default {
    components: {
        AccelerationConfig,
    },
    data() {
        return {
            domain: this.$route.query.domain,
            status: this.$route.query.status,
            creating: this.$route.query.creating,
            state: "",
            detailKey: +new Date(),
            statusColorMap: {
                1: variables.colorMaster,
                3: variables.colorMaster,
                2: variables.colorSuccess,
                4: variables.colorSuccess,
                5: variables.colorDanger,
                7: variables.colorDanger,
                9: variables.colorDanger,
                10: variables.colorDanger,
                11: variables.colorDanger,
                12: variables.colorDanger,
                6: variables.colorInfo,
                8: variables.colorInfo,
            },
        };
    },
    mounted() {
        this.$ctBus.$on("accelerationConfig:domain:status", this.updateDomainStatus)
        this.init();
    },
    methods: {
        init() {
            StatisticsModule.GetBasicConfig(); // 进入查看页面需要获取基础配置
            const currentDomainInfo = {
                domain: this.domain,
                domainStatus: this.status === "4" ? "NORMAL" : "DEPLOY", // 已启用 NORMAL，配置中 DEPLOY
            };
            if (this.creating) {
                currentDomainInfo.workStatus = 2;
            }
            this.state = this.$t(`${DomainStatusMap[this.status]}`);
            SecurityAbilityModule.SET_SECURITY_DOMAIN_INFO(currentDomainInfo);
        },
        updateDomainStatus(status) {
            this.status = status;
            this.init();
        },
        async handleTargetToDomainList() {
            if (SecurityAbilityModule.isCdnFixFormChange) {
                await handleLeaveModule();
            }
            this.$router.push({
                name: "ndomain.list",
            });
        },
    },
    beforeDestroy() {
        this.$ctBus.$off("accelerationConfig:domain:status", this.updateDomainStatus)
        // 离开当前页面的时候，需要将域名默认值清空，以免有缓存导致调用了上一次的域名的详情接口
        SecurityAbilityModule.CLEAR_ALL_SECURITY_DATA();
    },
};
</script>

<style lang="scss" scoped>
.form-wrapper {
    width: 100%;
    height: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: row;
    background: #fff;
    ::v-deep {
        .domain-edit {
            .ct-section-wrap > .el-scrollbar {
                margin-top: 0;
            }
        }
    }
}
.ct-section-wrap {
    ::v-deep {
        .header-title {
            display: flex;
            justify-content: space-between;
        }
    }
}
</style>
