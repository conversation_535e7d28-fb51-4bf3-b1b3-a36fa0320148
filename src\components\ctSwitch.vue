<template>
    <div class="flex-row-style" style="height: 32px;">
        <el-switch
            ref="test"
            :value="status"
            :active-value="activeValue"
            :inactive-value="inactiveValue"
            active-color="rgb(19, 206, 102)"
            inactive-color="#c7d2df"
            v-bind="$attrs"
            v-on="$listeners"
            class="ct-switch"
            @change="handleChange"
        />
        <i v-if="loading" class="el-icon-loading" />
    </div>
</template>

<script type="text/ecmascript-6">
export default {
    model: {
        prop: "value",
        event: "handleChange",
    },
    props: {
        value: {
            type: [Number, String, Boolean],
            default: "",
        },
        activeValue: {
            type: [String, Number, Boolean],
            default: "ON",
        },
        inactiveValue: {
            type: [String, Number, Boolean],
            default: "CLOSE",
        },
        loading: {
            type: Boolean,
            default: false
        },
        beforeChange: {
            type: [Object, Function, Promise],
            default: null
        },
        // 需要传递数据的话，用这个字段传进来
        beforeChangeData: {
            type: [Object, Number, String, Array],
            default: "",
        }
    },
    data() {
        return {
            status: this.inactiveValue
        }
    },
    watch: {
        value: {
            handler(val) {
                this.status = val;
            },
            immediate: true,
        }
    },
    methods: {
        /**
         * 处理switch变化
         */
        async handleChange(val) {
            if (!this.beforeChange && typeof this.beforeChange !== "function") {
                this.passResult(val, true);
                return;
            }

            const res = this.beforeChange(this.beforeChangeData);
            if (!res) {
                this.passResult(val, false);
                return;
            }

            if (res === true) {
                this.$emit("handleChange", val);
                return;
            }

            if (typeof res.then === "function") {
                try {
                    const result = await res;
                    // 适配async-await中使用try-catch 并且return了值的情况
                    if (result === false) {
                        this.passResult(val, false);
                        return;
                    }

                    this.passResult(val, true);
                } catch (e) {
                    this.passResult(val, false);
                }
            }
        },
        /**
         * 通过结果
         */
        passResult(val, result) {
            if (result) {
                this.$emit("handleChange", val);
                return;
            }

            this.status = this.value;
        },
    },
};
</script>

<style scoped lang="scss">
.el-icon-loading {
    color: #1890ff;
    font-size: 16px;
    margin-left: 15px;
}
// 基于 .ct-switch 全局样式重写了
</style>
