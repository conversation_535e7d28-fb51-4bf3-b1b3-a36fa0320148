<template>
    <div class="chart-wrap">
        <div class="title-wrap">
            <div class="operate-btn">
                <el-radio-group v-model="chartType" size="small" @change="handleChange">
                    <el-radio-button label="PV"></el-radio-button>
                    <el-radio-button label="UV"></el-radio-button>
                </el-radio-group>
            </div>

            <div class="total">
                <div class="tip">
                    <span v-html="$t('statistics.dcdn.PvUv.totalTip1New', {
                        type: chartType,
                        tipText: greater24Hours
                            ? `${$t('statistics.dcdn.PvUv.totalTipUnit1')}`
                            : `${$t('statistics.dcdn.PvUv.totalTipUnit2')}`,
                        max: fetchData[dataKeys.max],
                    })
                        "></span>
                    <span class="num">{{ $t("statistics.dcdn.PvUv.totalTipUnit", { max: fetchData[dataKeys.max] })
                        }}</span>
                </div>
                <div class="tip">
                    <span v-html="$t('statistics.dcdn.PvUv.totalTip2', {
                        type: chartType,

                    })
                        "></span>
                    <span class="num">{{ $t("statistics.dcdn.PvUv.totalTipUnit", { max: fetchData[dataKeys.total] })
                        }}</span>
                </div>
            </div>
        </div>

        <v-chart class="chart" v-loading="loading" :element-loading-text="$t('statistics.common.chart.loading')"
            autoresize theme="cdn" :options="options" />

        <ct-tip>
            {{ $t("statistics.dcdn.PvUv.ctTip") }}
            <el-tooltip effect="dark" :content="$t('statistics.common.searchDownloadContent')" placement="right">
                <i class="el-icon-download usage-download-icon" @click="downloadTable"></i>
            </el-tooltip>
        </ct-tip>

        <el-table :data="fetchData.daily" stripe v-loading="loading" show-summary :summary-method="getSummaries"
            :element-loading-text="$t('statistics.common.table.loading')" :empty-text="$t('common.table.empty')">
            <el-table-column :label="$t('statistics.dcdn.PvUv.tableColumn1')" type="index" width="100" />
            <el-table-column :label="$t('statistics.dcdn.PvUv.tableColumn2')">
                <template slot-scope="{ row }">
                    {{ (row.timestamp * 1000) | dateFormat({ separator: "-" }) }}
                </template>
            </el-table-column>
            <el-table-column :label="$t('statistics.dcdn.PvUv.tableColumn3')">
                <template slot-scope="{ row }">
                    {{ row.pvCount }}
                </template>
            </el-table-column>
            <el-table-column :label="$t('statistics.dcdn.PvUv.tableColumn4')">
                <template slot-scope="{ row }">
                    {{ row.uvCount }}
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<script lang="ts">
import { Component } from "vue-property-decorator";
import { mixins } from "vue-class-component";
import { StatisticsUsageUrl } from "@/config/url/statistics";
import { dateFormat, timeFormat } from "@/filters/index";
import { SearchParams } from "@/types/statistics/usage";
import { PvUvFetchData } from "@/types/statistics/usage";
import { THEME_AREA_STYLE } from "@/config/echart/cdn-theme";
import { DomainModule } from "@/store/modules/domain";
import { StatisticsModule } from "@/store/modules/statistics";
import { DomainActionEnum } from "@/config/map";
import ChartMixin from "../chartMixin";
import { cloneDeep } from "lodash-es";

type tooltipParam = { name: string; marker: string; value: string | number };

const defaultFetchData: PvUvFetchData = {
    hours: [],
    daily: [],
    maxPv: 0,
    totalPv: 0,
    maxUv: 0,
    totalUv: 0,
};

@Component({
    name: "PvUv",
})
export default class PvUv extends mixins(ChartMixin) {
    chartType = "PV";

    // 接口数据
    fetchData: PvUvFetchData = cloneDeep(defaultFetchData);

    protected downloadDataList: PvUvFetchData["hours"] = []; // 用于下载的数据 小时维度
    protected downloadDataListDaily: PvUvFetchData["daily"] = []; // 用于下载的数据 天维度

    // 当前展示是否为 PV
    get showPv() {
        return this.chartType === "PV";
    }

    // 获取 seriesData 所使用的 key
    get dataKeys(): {
        count: "pvCount" | "uvCount";
        max: "maxPv" | "maxUv";
        total: "totalPv" | "totalUv";
    } {
        return {
            count: this.showPv ? "pvCount" : "uvCount",
            max: this.showPv ? "maxPv" : "maxUv",
            total: this.showPv ? "totalPv" : "totalUv",
        };
    }

    get domainTotal() {
        return DomainModule[StatisticsModule.currentAction].nativeList.length;
    }

    get tips() {
        //区别未选择域名和没有域名
        return this.searchParams.domainList.length === 0 && this.domainTotal > 0
            ? `${this.$t("statistics.dcdn.PvUv.searchTip1")}`
            : `${this.$t("statistics.dcdn.PvUv.searchTip2")}`;
    }

    // 判断时间跨度是否超过24h
    get greater24Hours() {
        return (this.searchParams.endTime - this.searchParams.startTime) / 3600 > 24;
    }
    private async localFetchGenerator<T>(url: string, params: SearchParams) {
        const rst = await this.fetchGenerator<T>(url, params)
        return rst;
    }
    // 1、数据请求
    protected async getData(params: SearchParams) {
        this.fetchData = await this.localFetchGenerator(StatisticsUsageUrl.pvUvList, params);

        if (!this.fetchData) this.fetchData = cloneDeep(defaultFetchData);

        // 处理用于下载的数据
        this.downloadDataList = this.fetchData.hours;
        this.downloadDataListDaily = this.fetchData.daily;
    }
    handleChange(label: string) {
        StatisticsModule.SET_PV_UV_CHARTTYPE(label);
    }

    // 2、数据处理
    get options() {
        const { dataKeys, greater24Hours } = this;
        const xAxisData: string[] = [];
        const seriesData: number[] = [];
        const fetchDataList = greater24Hours ? this.fetchData.daily : this.fetchData.hours;
        fetchDataList
            .sort((a, b) => a.timestamp - b.timestamp)
            .forEach(item => {
                xAxisData.push(timeFormat(item.timestamp * 1000).replace(" ", "\n"));
                seriesData.push(item[dataKeys.count] || 0);
            });

        // x 轴格式化取值范围
        const axisLabelRange = greater24Hours ? [0, 10] : [11, 16];

        const options = {
            ...this.comOptions,
            tooltip: {
                trigger: "axis",
                formatter: (a: tooltipParam[]) => {
                    let timePeriod = "";
                    if (greater24Hours) {
                        timePeriod = a[0].name.slice(0, 10);
                    } else {
                        const date = new Date(a[0].name.replace(/[\n]/g, " ").replace(/-/g, "/"));
                        date.setMinutes(59);
                        const hour = date.getHours() < 10 ? "0" + date.getHours() : date.getHours();
                        timePeriod = a[0].name.slice(10, 16) + "-" + hour + ":" + date.getMinutes();
                    }
                    return `${timePeriod}<br> ${this.$t("statistics.dcdn.PvUv.vchartOptions.totalTipUnit", {
                        marker: a[0].marker,
                        value: +a[0].value + "",
                    })}`;
                },
            },
            xAxis: {
                type: "category", // 类目轴，从 data 中获取数据
                boundaryGap: false, // 坐标轴两边留白策略
                axisLabel: {
                    formatter: this.xAxisFormatterGenerator(),
                },
                data: xAxisData,
            },
            yAxis: {
                type: "value", // 数值轴
                name: `${this.$t("statistics.dcdn.PvUv.vchartOptions.yAxisName")}`,
                minInterval: 1,
            },
            series: [
                {
                    type: "line",
                    data: seriesData,
                    areaStyle: THEME_AREA_STYLE["#358AE2"],
                },
            ],
        };

        return options;
    }

    beforeDestroy() {
        StatisticsModule.SET_PV_UV_CHARTTYPE("PV");
    }

    // 重写 excel 数据拼接方法
    protected tableToExcel() {
        let str = `${this.$t("statistics.dcdn.PvUv.tableToExcel.excelColumn1", {
            chartType: this.chartType,
        })}\n`;

        // 输出格式
        if (this.greater24Hours) {
            // 天粒度
            str += this.downloadDataListDaily.reduce((str, item) => {
                str += `${timeFormat(item["timestamp"] * 1000)},`;
                str += (item[this.dataKeys.count] / 1).toFixed(2) + "\n";
                return str;
            }, "");
        } else {
            // 小时粒度
            str += this.downloadDataList.reduce((str, item) => {
                str += `${timeFormat(item["timestamp"] * 1000)},`;
                str += (item[this.dataKeys.count] / 1).toFixed(2) + "\n";
                return str;
            }, "");
        }

        // 增加峰值、总量
        const { fetchData } = this;
        const { total, max } = this.dataKeys;

        str +=
            `${this.$t(
                this.greater24Hours
                    ? "statistics.dcdn.PvUv.tableToExcel.excelColumn4"
                    : "statistics.dcdn.PvUv.tableToExcel.excelColumn2",
                {
                    chartType: this.chartType,
                }
            )},${fetchData[max]}` + "\n";
        str +=
            `${this.$t("statistics.dcdn.PvUv.tableToExcel.excelColumn3", {
                chartType: this.chartType,
            })},${fetchData[total]}` + "\n";

        this.downloadExcel({
            name: `${this.$t("statistics.dcdn.PvUv.tableToExcel.excelName", {
                title: this.chartType,
            })}`,
            str,
        });
    }

    // 计算汇总数据
    getSummaries() {
        let pvTotal = 0,
            uvTotal = 0;
        this.fetchData.daily.forEach(item => {
            pvTotal += +item.pvCount;
            uvTotal += +item.uvCount;
        });
        return [`${this.$t("statistics.dcdn.PvUv.getSummariesItem")}`, "", pvTotal, uvTotal];
    }

    protected downloadTable() {
        if (this.checkListIsEmpty(this.fetchData?.daily)) return;

        let str = `${this.$t("statistics.usageQuery.BandwidthFlow.tableColumn1")},${this.$t('statistics.dcdn.PvUv.tableColumn3')},${this.$t('statistics.dcdn.PvUv.tableColumn4')}\n`;

        this.fetchData.daily.forEach((item => {
            str += dateFormat(item.timestamp, { separator: this.lang === "en" ? "-" : "", replaceStr: "" }) + ",";
            str += item.pvCount + ",";
            str += item.uvCount + ",\n";
        }))

        this.downloadExcel({
            name: `${this.$t("statistics.common.tab[6]")}-${this.$t("statistics.dcdn.PvUv.ctTip")}`,
            str
        })
    }
}
</script>

<style lang="scss" scoped></style>
