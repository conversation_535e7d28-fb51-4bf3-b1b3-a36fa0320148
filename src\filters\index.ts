/**
 * @Description: 过滤器在 main.js 中注册，本目录仅提供函数（可以直接当工具函数调用）
 *               按照通用过滤器和业务过滤器区分，其中具体页面专用的过滤器在页面中注册，不在此提供
 * @Author: wang yuegong
 */

// 1、导出通用的过滤器
export * from "@cdnplus/common/filters/index";

// 2、导出私有过滤器
export * from "./logic/unit";

// 覆盖common包中的timeFormat
export { timeFormat } from "./logic/unit";

// 覆盖common包中的convertBandwidthM2P 和 convertFlowM2P, 兼容scale为1024
export { convertBandwidthM2P, convertBandwidthB2P } from "./logic/unit";
export { convertFlowM2P, convertFlowB2P } from "./logic/unit";
