<template>
    <ct-section-wrap :headerText="headerText" :isHtmlTag="true">
        <template slot="tip">
            <i18n :path="headerTip">
                <a class="aocdn-ignore-link" @click="$docHelp(headerTipHref)">{{ $t("log.headerTip-1") }}</a>
            </i18n>
        </template>
        <ct-box :class="[{ 'fix-box': dataList.length > 11 }, 'table-scroll-wrap']">
            <div class="search-bar-wrapper">
                <div>
                    <el-button type="primary" @click="multiDownload" :disabled="!multipleSelection.length">
                        {{ $t("log.tip6") }}
                    </el-button>
                </div>
                <div class="search-bar">
                    <!-- aocdn筛选 -->
                    <div v-if="this.isPowerByQIANKUN">
                        <template v-if="!isIpa">
                            <label class="search-label">{{ $t("log.tip1") }}</label>
                            <el-select v-model="product" filterable>
                                <el-option
                                    v-for="opt in productServiceOptions"
                                    :key="opt.value"
                                    :label="opt.label"
                                    :value="opt.value"
                                />
                            </el-select>
                        </template>
                        <el-select
                            v-if="isIpa"
                            v-model="type"
                            @change="typeChange"
                            placeholder="请选择"
                            style="width:90px"
                        >
                            <el-option :label="$t('log.option1')" :value="1"></el-option>
                            <el-option :label="$t('log.option2')" :value="2"></el-option>
                        </el-select>
                        <!-- 域名 下拉框 -->
                        <label class="label-name" v-if="type !== 2">{{ $t("log.tip2") }}</label>
                        <el-select
                            v-model="domain"
                            filterable
                            v-if="type !== 2"
                            @change="product === 2 && handleChange($event)"
                        >
                            <el-option
                                v-for="opt in product === 2
                                    ? ipaDomainOptions
                                    : product === 1
                                    ? domainOptions
                                    : []"
                                :key="opt.domain"
                                :label="opt.label"
                                :value="opt.domain"
                            />
                        </el-select>
                        <!-- 实例 下拉框 -->
                        <label class="label-name" v-if="product === 2 && type === 2">
                            {{ $t("log.tip3") }}
                        </label>
                        <el-select v-model="domain" filterable v-if="type === 2" @change="handleChange">
                            <el-option
                                v-for="opt in filteredInstanceOptions"
                                :key="opt.domain"
                                :label="opt.inst_name"
                                :value="opt.domain"
                            />
                        </el-select>
                    </div>
                    <!-- fcdn筛选 -->
                    <div v-else>
                        <label class="search-label">{{ $t("log.tip8") }}</label>
                        <el-select :loading="loading" v-model="product" filterable @change="nProductChange">
                            <el-option
                                v-for="opt in productServiceOptions"
                                :key="opt.value"
                                :label="$t(opt.label)"
                                :value="opt.value"
                            />
                        </el-select>
                        <label class="label-name" v-if="type !== 2">{{ $t("log.tip2") }}</label>
                        <el-select :loading="loading || domainOptionsLoading" v-model="domain" filterable>
                            <el-option
                                v-for="opt in productDomainOptions"
                                :key="opt.name"
                                :label="opt.label"
                                :value="opt.name"
                            />
                        </el-select>
                    </div>
                    <div>
                        <label class="label-name">{{ $t("log.tip4") }}</label>
                        <el-date-picker
                            v-model="dateRange"
                            type="datetimerange"
                            :clearable="false"
                            value-format="timestamp"
                            :default-time="['00:00:00', '23:59:59']"
                            range-separator="-"
                            :start-placeholder="$t('common.datePicker.start')"
                            :end-placeholder="$t('common.datePicker.end')"
                            :picker-options="pickerOptions"
                        />
                    </div>
                    <div>
                        <el-button type="primary" plain @click="handleBeforeSearch">
                            {{ $t("log.tip5") }}
                        </el-button>
                    </div>
                </div>
            </div>
            <el-table
                :data="dataList"
                :stripe="false"
                @selection-change="handleSelectionChange"
                v-loading="loading"
            >
                <el-table-column type="selection"></el-table-column>
                <el-table-column type="index" :label="$t('log.list.label1')">
                    <template slot-scope="{ row }">
                        <span>{{ row.index }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                    :label="domainLabel"
                    prop="domain"
                    min-width="120"
                    align="left"
                    show-overflow-tooltip
                ></el-table-column>
                <el-table-column
                    v-if="product === 2"
                    :label="$t('log.list.label4')"
                    prop="inst_name"
                ></el-table-column>
                <el-table-column :label="$t('log.list.label5')" prop="start_time"></el-table-column>
                <el-table-column
                    :label="$t('log.list.label6')"
                    prop="end_time"
                    min-width="150"
                ></el-table-column>
                <el-table-column
                    :label="$t('log.list.label7')"
                    prop="file_size"
                    min-width="100"
                ></el-table-column>
                <el-table-column :label="$t('log.list.label8')" prop="operation" min-width="80">
                    <template slot-scope="{ row }">
                        <div class="icon-style">
                            <el-button
                                type="text"
                                @click="download(row)"
                            >
                                {{ $t("log.tip7") }}
                            </el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
            <div class="pager">
                <el-pagination
                    :small="isXs"
                    :layout="isXs ? 'prev, pager, next' : 'total, sizes, prev, pager, next,jumper'"
                    :total="total"
                    :current-page.sync="pageNum"
                    :page-size.sync="pageSize"
                    :page-sizes="[10, 30, 50, 100]"
                    :hide-on-single-page="false"
                    @size-change="handlePageSizeChange"
                    @current-change="handleCurrentPageChange"
                ></el-pagination>
            </div>
        </ct-box>
    </ct-section-wrap>
</template>

<script lang="ts">
import { Component, Mixins } from "vue-property-decorator";
import { ScreenModule } from "@/store/modules/screen";
import TableAndPagerActionMixin from "@/views/log/tableAndPagerAction";
import { LogUrl } from "@/config/url";
import { LogFileData } from "@/types/log/index";
import ctSvgIcon from "@/components/SvgIcon/index.vue";
import { getAm0 } from "@/utils";
import { shortcuts_obj } from "@/utils/pickerOption";
import { ipaLogUrl } from "@/config/url/ipa/log";
import { DomainUrl } from "@/config/url/ipa/domain";
import urlTransformer from "@/utils/logic/url";
import { ProductModule } from "@/store/modules/ncdn/nproduct";
import { DomainModule } from "@/store/modules/domain";
import { DomainItem } from "@/types/domain";
import { DomainActionEnum, ProductCodeEnum, DomainStatusMap, getDomainAction, GetCtiamButtonAction } from "@/config/map";
import { nUserModule } from "@/store/modules/nuser";
import { checkCtiamButtonAuth } from "@/store/modules/menu";
import { CtiamCode } from "@/utils/ctFetch/errorConfig";
import { StatisticsModule } from "@/store/modules/statistics";
import { downloadFile } from "@/utils/download";
import { IpaConfigModule } from "@/store/modules/ipa/config";

@Component({
    components: {
        ctSvgIcon
    }
})
export default class Log extends Mixins(TableAndPagerActionMixin) {
    // private pickerOptions = {
    //     // 设置禁用时间，30天内
    //     disabledDate(time: Date) {
    //         return (
    //             +time > +getAm0(new Date()) + 24 * 3600 * 1000 - 1 ||
    //             +time < Date.now() - 365 * 24 * 3600 * 1000
    //         );
    //     },
    //     shortcuts: [
    //         shortcuts_obj.recent_15_minutes,
    //         shortcuts_obj.recent_1_hour,
    //         shortcuts_obj.recent_6_hours,
    //         shortcuts_obj.recent_24_hours,
    //         shortcuts_obj.recent_1_week,
    //         shortcuts_obj.recent_1_month,
    //     ],
    // };
    get pickerOptions() {
        const options = {
            // 设置禁用时间，365天内
            disabledDate: (time: Date) => {
                return (
                    +time > +getAm0(new Date()) + 24 * 3600 * 1000 - 1 ||
                    +time < Date.now() - this.logDownloadSpan * 24 * 3600 * 1000
                );
            },
            shortcuts: [
                shortcuts_obj.recent_15_minutes,
                shortcuts_obj.recent_1_hour,
                shortcuts_obj.recent_6_hours,
                shortcuts_obj.recent_24_hours,
                shortcuts_obj.recent_1_week,
                shortcuts_obj.recent_1_month,
            ],
        };
        return options;
    }

    // domainOptions: any = [];
    ipaDomainOptions: any = [];
    private domain = "";
    private inst_name = "";
    protected searchUrl = LogUrl;
    private multipleSelection: LogFileData[] = [];

    private dateRange: [number?, number?] = []; // 日期选择框选定的日期范围，均为0点到0点，单位：ms
    private product: string | number = "";
    private type = 1;
    private ctiamDomainModuleTimer: number | null = null;

    nProductChange(val: any) {
        this.domain = "";
    }
    typeChange() {
        this.domain = "";
    }
    handleChange(val: any) {
        const arr = this.ipaDomainOptions.filter((item: any) => item.domain === val)
        if (arr && arr.length > 0) {
            this.inst_name = arr[0].inst_name
        } else {
            this.inst_name = ""
        }
    }
    get productDomainOptions() {
        return this.nDomainOptions.filter((item: any) => {
            if (this.product === "006") {
                // 全站加速包含二级产品104、105
                return ["006", "104", "105"].includes(item.productCode);
            } else {
                return item.productCode === this.product;
            }
        });
    }
    get filteredInstanceOptions() {
        return this.ipaDomainOptions.filter((item: any) => item.inst_name !== "-");
    }
    get isFcdnCtyunCtclouds() {
        return nUserModule.isFcdnCtyunCtclouds;
    }
    get isIpa() {
        return this.$route.query?.productType === "eas";
    }
    get isPowerByQIANKUN() {
        return window.__POWERED_BY_QIANKUN__;
    }
    get headerText() {
        return this.isPowerByQIANKUN
            ? this.isIpa
                ? this.$t("log.title2-1")
                : this.$t("log.title2")
            : this.$t("log.title");
    }
    get headerTipHref() {
        return urlTransformer({
            fcdnCtcloud: "https://www.esurfingcloud.com/document/zh-cn/10015932/10016079",
            fcdnCtyun: "https://www.ctyun.cn/document/10015932/10621656",
            a1Ctyun: this.isIpa ? "https://www.ctyun.cn/document/10065985/10589937" : "https://www.ctyun.cn/document/10065985/10198489",
            a1Ctcloud: "https://www.esurfingcloud.com/document/zh-cn/20689737/20689927",
        })
    }
    get productServiceOptions() {
        return this.isPowerByQIANKUN
            ? [
                  { label: this.$t("log.productOption1"), value: 1 },
                //   { label: this.$t("log.productOption2"), value: 2 },
              ]
            : this.ProductOptions.filter(item => !["104", "105"].includes(item.value)); // 过滤二级产品。已知有104、105
    }
    get ProductOptions() {
        //二级产品过滤掉
        const productCodeArr: string[] = Object.values(ProductCodeEnum);
        return ProductModule.productOptions.filter(item => {
            return (
                item.value !== ProductCodeEnum.Socket &&
                item.value !== ProductCodeEnum.Upload &&
                productCodeArr.includes(item.value)
            );
        });
    }
    get domainAction() {
        // fcdn-ctyun 体系（国内+国际）需要取ctiam权限管控下的域名数据
        return this.isFcdnCtyunCtclouds
            ? getDomainAction("Log")
            : DomainActionEnum.Data;
    }
    get domainOptionsLoading() {
        return DomainModule[this.domainAction].loading;
    }
    get nDomainOptions() {
        return DomainModule[this.domainAction].nativeList
    }
    get domainOptions() {
        return DomainModule[DomainActionEnum.Data].nativeList.map(
            (item: any): DomainItem => {
                item.label = `${item.domain} (${this.$t(
                    `${DomainStatusMap[+item.status as keyof typeof DomainStatusMap]}`
                )})`;
                return item;
            }
        );
    }
    get domainLabel() {
        return this.isPowerByQIANKUN ? this.$t("log.list.label2") : this.$t("log.list.label3");
    }
    get screenWidth() {
        return ScreenModule.width;
    }
    get isXs() {
        return this.screenWidth < 600;
    }
    get searchParams() {
        const now = Date.now();
        const startTime = this.dateRange[0] ? Math.floor(this.dateRange[0] / 1000) : this.dateRange[0];
        // 结束时间不超过 now
        const endTime = this.dateRange[1]
            ? this.dateRange[1] > now
                ? Math.floor(now / 1000)
                : Math.floor(this.dateRange[1] / 1000)
            : this.dateRange[1];
        return {
            domain: this.domain,
            instName: this.inst_name,
            startTime,
            endTime,
        };
    }
    get headerTip() {
        let tip = "log.headerTip-2";
        if (this.isPowerByQIANKUN) {
            tip = "log.headerTip-3";
        }
        if (this.isIpa) {
            tip = "log.headerTip-2";
        }
        return tip;
    }
    /**
     * 日志下载时间范围
     */
    get logDownloadSpan() {
        if (this.isIpa) {
            return IpaConfigModule.ipaLogDownloadSpan;
        }
        return StatisticsModule.logDownloadSpan;
    }
    mounted() {
        if (this.isPowerByQIANKUN) {
            this.product = this.isIpa ? 2 : 1;
            this.searchUrl = this.isIpa ? ipaLogUrl : LogUrl;

            this.getDomainList();
        }
        this.isFcdnCtyunCtclouds &&(this.ctiamDomainModuleTimer = window.setInterval(() => {
            DomainModule.GetDomainList({ action: this.domainAction, shouldCheckCache: true });
        }, (StatisticsModule.cacheTtl > 15 ? StatisticsModule.cacheTtl : 15) * 1000));
    }

    created() {
        const now = new Date();
        const d0am = getAm0(now); // 当日0点
        // const d15ago = new Date(+d0am - 14 * 24 * 3600 * 1000); // 15天前
        const d30ago = new Date(+d0am - 29 * 24 * 3600 * 1000); // 30天前
        // 默认以当前时间结束
        this.dateRange = [+d30ago, +now];
    }

    beforeSearch() {
        if (!this.product) {
            this.$message.error(this.$t(this.isPowerByQIANKUN ? "log.info1" : "log.info6") as string);
            return false;
        }
        if (!this.domain) {
            if (this.type !== 2) {
                this.$message.error(this.$t("log.info2") as string);
            } else {
                this.$message.error(this.$t("log.info5") as string);
            }
            return false;
        }
        if (!this.dateRange) {
            this.$message.error(this.$t("log.info3") as string);
            return false;
        }
        return true;
    }
    private transformUrl(url: string) {
        return url.replace(/^http:/, "https:");
    }
    private async download(row: LogFileData) {
        await checkCtiamButtonAuth(GetCtiamButtonAction("logDownload"));
        window.open(this.transformUrl(row.url));
    }
    private handleSelectionChange(val: LogFileData[]) {
        this.multipleSelection = val;
    }
    private async multiDownload() {
        const newLogDownloadEnable = this.isIpa ? IpaConfigModule.ipaNewLogDownLoadEnable : StatisticsModule.newLogDownloadEnable;
        if (!newLogDownloadEnable) {
            this.originalMultiDownload();
            return;
        }

        await checkCtiamButtonAuth(GetCtiamButtonAction("logBatchDownload"));
        if (this.multipleSelection.length === 0) {
            this.$message.error(this.$t("log.info4") as string);
            return;
        }

        const urls = this.multipleSelection.map(log => this.transformUrl(log.url));

        for (let index = 0; index < urls.length; index++) {
            const url = urls[index];
            setTimeout(async () => {
                downloadFile({ url, key: `${index}` });
            }, index * 1000);
        }
    }
    private async originalMultiDownload() {
        await checkCtiamButtonAuth(GetCtiamButtonAction("logBatchDownload"));
        if (this.multipleSelection.length === 0) this.$message.error(this.$t("log.info4") as string);
        else
            this.multipleSelection.forEach((log, index) => {
                const anchor = document.createElement("a");
                anchor.setAttribute("href", this.transformUrl(log.url));
                anchor.setAttribute("download", "");
                anchor.setAttribute("style", "position:absolute;top:-9999px;");
                document.body.appendChild(anchor);
                setTimeout(() => {
                    anchor.dispatchEvent(new MouseEvent("click"));

                    setTimeout(() => {
                        document.body.removeChild(anchor);
                    }, 1000);
                }, index * 1000);
            });
    }
    // async fetchDomainList() {
    //     this.loading = true;
    //     const rst = DomainModule[DomainActionEnum.Data].nativeList;
    //     this.domainOptions = rst.map(
    //         (item: any): DomainItem => {
    //             item.label = `${item.domain} (${this.$t(
    //                 `${DomainStatusMap[+item.status as keyof typeof DomainStatusMap]}`
    //             )})`;
    //             return item;
    //         }
    //     );
    // }
    // 获取域名列表
    async getDomainList() {
        try {
            this.loading = true;
            const rst = await this.$ctFetch<{ result: string[] }>(DomainUrl.domainListNew, {
                method: "GET",
                transferType: "json",
                data: {
                    account_id: this.$store.state.user.userInfo.userId,
                },
                cache: true,
            });
            this.ipaDomainOptions = rst.result.map(
                (item: any): DomainItem => {
                    item.label = item.domain;
                    // item.label = `${item.domain} (${this.$t(
                    //     `${DomainStatusMap[+item.status as keyof typeof DomainStatusMap]}`
                    // )})`;
                    return item;
                }
            );
        } catch (err) {
            const isCtiamError = CtiamCode.includes((err as any)?.data?.code);
            if (isCtiamError) {
                // 权限报错时清空数据
                this.ipaDomainOptions = [];
            }
            this.$errorHandler(err);
        } finally {
            this.loading = false;
        }
    }
    handlePageSizeChange(pageSize: number) {
        this.pageSize = pageSize;
        this.pageNum = 1;
        this.search({ page: this.pageNum, pageSize });
    }
    handleCurrentPageChange(page: number) {
        this.pageNum = page;
        this.search({ page, pageSize: this.pageSize });
    }
    handleBeforeSearch() {
        if (!this.beforeSearch()) return;
        this.pageNum = 1;
        this.search({ page: this.pageNum, pageSize: this.pageSize });
        this.inst_name = "";
    }
    beforeDestroy() {
        this.ctiamDomainModuleTimer && clearInterval(this.ctiamDomainModuleTimer);
    }
}
</script>

<style lang="scss" scoped>
.cloud-download {
    width: 24px;
    cursor: pointer;
    vertical-align: middle;
}
.label-name {
    color: #333 !important;
}

// .search-label {
//     margin-right: 16px;
// }

.pager {
    margin-top: 8px;
    ::v-deep.el-pagination {
        text-align: right !important;
    }
}
.search-bar-wrapper {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 12px;
    .search-bar {
        display: flex;
        gap: 12px;
        flex-wrap: wrap;
        > div {
            flex-shrink: 0;
            display: flex;
            gap: 12px;
            align-items: center;
            flex-wrap: nowrap;
            label {
                font-size: 12px;
            }
        }
    }
}
.icon-style {
    display: flex;
    align-items: center;
}
.pager {
    text-align: right;
    margin-top: 8px;
}
</style>
