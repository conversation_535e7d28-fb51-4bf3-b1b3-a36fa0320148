<template>
    <el-dialog
        :title="$t('domain.create.tip3-1')"
        :visible.sync="visible"
        :modal-append-to-body="false"
        :close-on-click-modal="false"
        :before-close="cancel"
        :append-to-body="true"
        width="600px"
    >
        <span class="form-info">
            <i class="el-alert__icon cute-icon-info-circle-fill"></i>
            {{ $t('domain.list.tip22') }}
        </span>
        <el-table :data="data">
            <el-table-column prop="productType" :label="$t('domain.editPage.label2')" minWidth="150">
                <template slot-scope="{ row }">
                    {{ getI18nLabel(row.productType) }}
                </template>
            </el-table-column>
            <el-table-column prop="scale" :label="$t('domain.editPage.label37')" minWidth="150" />
            <el-table-column prop="ratio" :label="$t('domain.editPage.label38')" minWidth="150">
                <template slot-scope="{ row }">
                    {{ parseInt(row.ratio) + "%" }}
                </template>
            </el-table-column>
        </el-table>
        <div slot="footer" class="dialog-footer">
            <slot name="footer">
                <el-button v-if="!loading" @click="cancel">
                    {{ $t("common.dialog.cancel") }}
                </el-button>
                <el-button :loading="loading" type="primary" @click="submit">
                    {{ $t("common.dialog.submit") }}
                </el-button>
            </slot>
        </div>
    </el-dialog>
</template>

<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";
import { getI18nLabel } from "@/store/modules/ncdn/nproduct";

@Component
export default class OperationFeedbackDialog extends Vue {
    @Prop({ default: true, type: Boolean }) private visible!: boolean;
    @Prop({ default: () => [] }) private data!: Array<any>;
    @Prop() private submitCallback!: Function;
    private loading = false;
    private getI18nLabel = getI18nLabel;
    /**
     * 关闭当前弹窗
     */
    private cancel() {
        this.$emit("close");
    }
    /**
     * 提交工单
     */
    private async submit() {
        try {
            this.loading = true;
            const status = this.submitCallback && await this.submitCallback()
            status && this.cancel();
        } catch(e) {
            console.log(e)
        } finally {
            this.loading = false;
        }
    }
}
</script>
<style lang="scss" scoped>
.form-info {
    display: flex;
    .el-alert__icon {
        color: #ff842e;
        margin-right: 8px;
    }
    font-size: 12px;
    color: #333333;
    line-height: 18px;
}
</style>
