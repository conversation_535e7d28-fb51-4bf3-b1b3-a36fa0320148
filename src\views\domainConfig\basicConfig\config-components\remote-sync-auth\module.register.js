import i18n from "@/i18n";
import { registerConfigModule } from "@/registry/configModuleRegistry";
import { conditionAssign } from "@/utils/utils";
import { cloneDeep } from "lodash-es";

// 注册配置项
export function initRemoteSyncAuthModule() {
    registerConfigModule("remote_sync_auth", {
        fields: {
            remote_sync_auth: [],
            remote_sync_auth_condition: [],
        },
        toApi(form, { isLocked }) {
            // 如果有功能锁，返回空对象，不做任何修改
            if (isLocked) {
                return {};
            }

            const remote_sync_auth = Array.isArray(form.remote_sync_auth) ? cloneDeep(form.remote_sync_auth) : [];
            const remote_sync_auth_condition = conditionAssign(remote_sync_auth);

            remote_sync_auth.forEach(item => {
                item.auth_host = item.auth_host.split(",");

                if (item.use_original_request_args === "off") {
                    item.use_main_request_args_type = "replace";
                    item.use_original_request_args = "off";
                    item.use_main_request_args = [];
                }
                if (item.use_original_request_args === "custom") {
                    item.use_original_request_args = "off";
                    item.use_main_request_args_type = "inherit";
                }

                if (item.use_original_request_headers === "off") {
                    item.use_main_request_headers_type = "replace";
                    item.use_original_request_headers = "off";
                    item.use_main_request_headers = [];
                }
                if (item.use_original_request_headers === "custom") {
                    item.use_original_request_headers = "off";
                    item.use_main_request_headers_type = "inherit";
                }

                if (item.auth_respond_status) {
                    item.auth_respond_status = item.auth_respond_status.split(",").map(Number);
                } else {
                    item.auth_respond_status = [];
                }

                if (!item.direct_judge_response_body_switch) {
                    delete item.direct_judge_response_body;
                }

                item.auth_args_case_sensitive = item.auth_args_case_sensitive || null;
                item.auth_args_not_encode = item.auth_args_not_encode || null;

                delete item.direct_judge_response_body_switch;
                delete item.content;
                delete item.mode;
            });

            // 正常情况下的处理逻辑
            return {
                remote_sync_auth,
                remote_sync_auth_condition,
            };
        },
        // 添加展示条件配置
        displayCondition(_, vm) {
            return vm.isNewEcgw;
        },
        anchor: {
            prop: "#div_remoteSyncAuth",
            label: i18n.t("domain.remoteAuth.远程同步鉴权"),
            position: {
                type: "after",
                target: "#URLAuthentication", // 插入到URLAuthentication之后
            },
            // 其他的特殊展示条件(如有)
            anchorCondition() {
                return window.__POWERED_BY_QIANKUN__;
            },
            isInExpanded: true,
            isInCollapsed: false,
        },
    });
}

export function getDefaultRemoteSyncAuthItem() {
    return {
        id: `remote_sync_auth_${new Date().getTime()}`,
        priority: 10,
        auth_host: "",
        subject: "",
        pattern: "",
        replace: "",
        use_original_request_args: "on",
        use_main_request_args: [],
        use_original_request_headers: "on",
        use_main_request_headers: [],
        auth_args_case_sensitive: "off",
        auth_args_not_encode: "on",
        auth_scheme: "",
        auth_port: null,
        auth_method: "", // GET/POST/HEAD/OPTIONS
        auth_body_data: "",
        direct_judge_response_body_switch: 0,
        direct_judge_response_body: "",
        auth_timeout: null,
        auth_timeout_pass: "off",
        auth_respond_action: "allow",
        auth_respond_status: "200",
        forbidden_code_state: "follow",
        forbidden_code: "",
        // condition
        mode: null,
        content: "",
    };
}

export function getRemoteSyncAuthArgs() {
    return {
        arg_name: "",
        arg_value: "",
    };
}

export function getArgNameList() {
    return [
        "remote_addr",
        "server_addr",
        "scheme",
        "server_protocol",
        "uri",
        "args",
        "request_method",
        "request_uri",
        "request_id",
    ];
}

export function getArgValueList() {
    return [
        "$remote_addr",
        "$server_addr",
        "$scheme",
        "$server_protocol",
        "$uri",
        "$args",
        "$request_method",
        "$request_uri",
        "$request_id",
    ];
}

export function getRequestArgNameList() {
    return [
        "host",
        "user-agent",
        "referer",
        "content-type",
        "x-forward-for",
        "remote-addr",
        "server-addr",
        "scheme",
        "server-protocol",
        "uri",
        "args",
        "request-method",
        "request-uri",
        "request-id",
    ];
}

export function getRequestArgValueList() {
    return [
        "$http_host",
        "$http_user_agent",
        "$http_referer",
        "$http_content_type",
        "$http_x_forward_for",
        "$remote_addr",
        "$server_addr",
        "$scheme",
        "$server_protocol",
        "$uri",
        "$args",
        "$request_method",
        "$request_uri",
        "$http_request_id",
    ];
}
