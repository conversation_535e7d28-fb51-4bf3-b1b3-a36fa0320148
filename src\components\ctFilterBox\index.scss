.ct-filter-box {
    width: 100%;
    border: 1px solid $color-neutral-3;
    border-radius: $border-radius;

    ::v-deep .drop-box {
        width: 80px;

        &.el-select {
            .el-input__inner {
                padding: 0 0 0 $padding-2x;
                border: unset;

                &:focus {

                }
            }

            .el-input__suffix {
                right: 6px;
            }
        }
    }

    ::v-deep .view-box {
        flex: 2;

        .el-input, .el-select {
            width: 100%;

            .el-input__inner {
                border: unset;
            }
        }
    }
}
