<template>
    <ct-section-wrap
        headerText="业务脚本"
        :isHtmlTag="true"
    >
        <template #tip>
            <span>
                业务脚本预部署成功后，您可以调用api接口获取测试节点ip，在预部署环境对业务脚本调试成功后，再转全局部署。获取测试节点ip的接口地址：
                <a class="aocdn-ignore-link" @click="$docHelp('https://www.ctyun.cn/document/10006847/10045865')">
                获取预部署节点VIP</a></span>
        </template>
        <template #headerBtn>
            <el-button @click="searchWrapper" :disabled="loading">刷新</el-button>
            <el-button type="primary" @click="detailTask({}, 'add')">添加业务脚本</el-button>
        </template>
        <ct-box :class="[{ 'fix-box': dataList.length > 11 }, 'table-scroll-wrap']">
            <ct-table
                :dataList="dataList"
                :columnConfig="columnConfig"
                :tableConfig="{ stripe: false }"
                :pagerData="{ pageNum, pageSize }"
                v-loading="loading"
            >
                <template slot="index-slot" slot-scope="{ scope }">
                    <span>
                        {{ scope.row.index }}
                    </span>
                </template>
                <template slot="work-scope-slot" slot-scope="{ scope }">
                    <el-link @click="gotoDomainDetail(scope.row.work_scope)" target="_blank">
                        {{ scope.row.work_scope }}
                    </el-link>
                </template>
                <template slot="deployScope-slot" slot-scope="{ scope }">
                    <span>
                        {{ deployScopeMap[scope.row.deploy_scope] }}
                    </span>
                </template>
                <template slot="status-slot" slot-scope="{ scope }">
                    <span :class="'status-' + scope.row.deploy_status">
                        {{ statusFormatter(scope.row.deploy_status) }}
                    </span>
                </template>
                <template slot="operation-slot" slot-scope="{ scope }">
                    <el-button type="text" @click="detailTask(scope.row, 'detail')">查看</el-button>
                    <el-button
                        type="text"
                        v-if="showButton('edit', scope.row.deploy_status, scope.row.deploy_scope)"
                        @click="detailTask(scope.row, 'edit')"
                        :disabled="!workScopeDomainList.includes(scope.row.work_scope)"
                        >编辑</el-button
                    >
                    <el-button
                        type="text"
                        v-if="showButton('delete', scope.row.deploy_status, scope.row.deploy_scope)"
                        @click="handleClick('delete', scope.row)"
                        >删除</el-button
                    >
                    <el-button
                        type="text"
                        v-if="showButton('retry', scope.row.deploy_status, scope.row.deploy_scope)"
                        @click="handleClick('retry', scope.row)"
                        >失败重试</el-button
                    >
                    <el-button
                        type="text"
                        v-if="
                            showButton('publishToProduction', scope.row.deploy_status, scope.row.deploy_scope)
                        "
                        @click="handleClick('publishToProduction', scope.row)"
                        :disabled="!workScopeDomainList.includes(scope.row.work_scope)"
                        >预部署转生产</el-button
                    >
                    <el-button
                        type="text"
                        v-if="showButton('rollback', scope.row.deploy_status, scope.row.deploy_scope)"
                        @click="handleClick('rollback', scope.row)"
                        :disabled="!workScopeDomainList.includes(scope.row.work_scope)"
                        >预部署回滚</el-button
                    >
                </template>
            </ct-table>
            <ct-pager class="pager" :refresh.sync="refresh" :loadData="searchWrapper"></ct-pager>
        </ct-box>
    </ct-section-wrap>
</template>

<script lang="ts">
import { Component, Mixins, Watch } from "vue-property-decorator";
import TableAndPagerActionMixin from "@/mixins/tableAndPagerAction";
import { nscriptUrl } from "@/config/url/ncdn/nscript";
import { deploymentStatusMap, deployScopeMap, GetCtiamButtonAction } from "@/config/map";
import { nScriptModule } from "@/store/modules/nscript";
import { CtiamCode } from "@/utils/ctFetch/errorConfig";
import { checkCtiamButtonAuth } from "@/store/modules/menu";

@Component
export default class BusinessScript extends Mixins(TableAndPagerActionMixin) {
    protected prefetch = true;
    protected usePost = true;
    protected getResData = true;
    protected searchUrl = nscriptUrl.ScriptList;
    private deploymentStatusMap = deploymentStatusMap;
    private deployScopeMap = deployScopeMap;
    get columnConfig() {
        return [
            {
                type: "slot",
                name: "index-slot",
                baseConfig: {
                    label: "序号",
                    width: "60",
                    align: "left",
                },
            },
            {
                prop: "script_name",
                label: "脚本名称",
                align: "left",
                "show-overflow-tooltip": true,
            },
            {
                type: "slot",
                name: "deployScope-slot",
                baseConfig: {
                    label: "部署范围",
                    align: "left",
                },
            },
            {
                type: "slot",
                name: "status-slot",
                baseConfig: {
                    label: "部署状态",
                    align: "left",
                },
            },
            {
                type: "slot",
                name: "work-scope-slot",
                "show-overflow-tooltip": true,
                baseConfig: {
                    label: "作用范围",
                    align: "left",
                },
            },

            {
                prop: "update_time",
                label: "修改时间",
                align: "left",
            },
            {
                type: "slot",
                name: "operation-slot",
                baseConfig: {
                    label: "操作",
                    minWidth: "80",
                    align: "left",
                },
            },
        ];
    }
    // 作用域名数据
    get workScopeDomainList() {
        return (nScriptModule.scriptDomainList?.length ? nScriptModule.scriptDomainList : []).map(item => item.value);
    }
    private async detailTask(row: any, type: string) {
        if (type === "add") {
            await checkCtiamButtonAuth(GetCtiamButtonAction("udfBussinessAdd"));
        }
        if (type === "detail") {
            await checkCtiamButtonAuth(GetCtiamButtonAction("udfBussinessView"));
        }
        if (type === "edit") {
            await checkCtiamButtonAuth(GetCtiamButtonAction("udfBussinessEdit"));
        }

        this.$router.push({
            name: `nscript.business.${type}`,
            query: {
                id: row.id,
                operateType: type,
            },
        });
    }
    private async searchWrapper(payload: any) {
        // 获取作用域名列表
        nScriptModule.nGetScriptDomainList();
        const total = await this.search(payload);

        return total;
    }
    private async handleClick(type: string, row: any) {
        if (type === "delete") {
            await checkCtiamButtonAuth(GetCtiamButtonAction("udfBussinessDelete"));
        }
        if (type === "retry") {
            await checkCtiamButtonAuth(GetCtiamButtonAction("udfBussinessRetry"));
        }
        if (type === "publishToProduction") {
            await checkCtiamButtonAuth(GetCtiamButtonAction("udfBussinessPublish"));
        }
        if (type === "rollback") {
            await checkCtiamButtonAuth(GetCtiamButtonAction("udfBussinessRollback"));
        }
        try {
            const operationType = this.getTypeOrUrl(type, "operationType");
            await this.$confirm(`此操作将对业务脚本进行${operationType}操作，是否继续？`, `提示`, {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            });
            this.loading = true;
            const reqUrl = this.getTypeOrUrl(type, "url") as string;
            await this.$ctFetch(reqUrl, {
                method: "POST",
                body: {
                    data: { id: row.id },
                },
                headers: {
                    "Content-Type": "application/json",
                },
            });
            this.$message.success("操作成功！");
            this.refresh = true;
        } catch (error) {
            const isCtiamError = CtiamCode.includes((error as any)?.data?.code);
            if (isCtiamError) {
                this.$errorHandler(error);
            } else {
                const errData = error as any;
                if (errData === "cancel") return;
                this.$message.error(errData.data.reason);
            }
        } finally {
            this.loading = false;
        }
    }
    // 获取操作文本或请求路径
    private getTypeOrUrl(type: string, setType: string) {
        switch (type) {
            case "delete":
                return setType === "operationType" ? "删除" : nscriptUrl.DelScript;
            case "retry":
                return setType === "operationType" ? "失败重试" : nscriptUrl.RedeployScript;
            case "publishToProduction":
                return setType === "operationType" ? "预生产转生产" : nscriptUrl.PublishToProduction;
            case "rollback":
                return setType === "operationType" ? "预部署回滚" : nscriptUrl.ScriptRollback;
            default:
                break;
        }
    }
    private statusFormatter(status: number) {
        return this.deploymentStatusMap[status as keyof typeof deploymentStatusMap];
    }
    // 判断按钮显示 type：按钮类型  status：部署状态枚举   scope：部署范围枚举
    private showButton(type: string, status: number, scope: number) {
        switch (type) {
            case "edit":
                return [-1, 3, 4, 7].includes(status);
            case "delete":
                return [-1, 3, 4].includes(status);
            case "retry":
                return [-1, 4, 7].includes(status);
            case "publishToProduction":
                return scope === 1 && [3].includes(status);
            case "rollback":
                return scope === 1 && [-1, 3, 4, 7].includes(status);
            default:
                break;
        }
    }
    protected async gotoDomainDetail(domain: string) {
        await checkCtiamButtonAuth(GetCtiamButtonAction("domainDetail"), domain);

        this.$router.push({
            name: "ndomain.detail",
            query: {
                domain,
                opType: "view",
                creating: "false",
                status: this.workScopeDomainList.includes(domain) ? "4" : "3",
                anchor: "UDFScript"
            },
        })
    }
    @Watch("$route", { immediate: false, deep: true })
    OnRouterChanage(newVal: any) {
        this.refresh = newVal.query.isRefresh === "true";
    }
}
</script>

<style lang="scss" scoped>
// 状态颜色
.status-3 {
    color: $g-color-green;
}
.status--1,
.status-4,
.status-7 {
    color: $g-color-red;
}
.status-0,
.status-1,
.status-2,
.status-8 {
    color: $g-color-gray;
}

.el-button {
    margin-left: 0px !important;
    margin-right: 12px;
}
</style>
