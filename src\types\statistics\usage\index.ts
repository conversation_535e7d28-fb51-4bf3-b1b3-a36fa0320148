/*
 * @Description: 统计分析-用量查询的几个接口类型注解
 * @Author: wang yuegong
 */

// 各接口相关的类型注解，文件名即接口名
export * from "./QueryDataList";
export * from "./QueryDataQpsList";
export * from "./GetHit";
export * from "./GetHttpCode";
export * from "./GetPvUv";
export * from "./ListProvinceFBRData";

export type SearchParams = {
    domainList: string[]; // 根据业务要求，域名是必要参数
    startTime: number; // 根据业务要求，时间是必要参数
    endTime: number;
    product?: string[];
    productType?: string[];
    isp?: string[];
    province?: string[];
    continent_code?: string[];
    continent_region_code?: string[];
    city?: string[],
    type?: string; // 带宽接口需要 flow-流量带宽数据 request-请求数 status-回源数据
    httpCode?: string; // code 接口需要
    sort?: string; // 地区运营商-省份数据接口需要
    sortOrder?: string;
    busiType?: number[]; //全站加速二级产品
    protocol?: string;
    ipProtocol?: string;
    interval?: string;
    abroad?: string; // 区域
};
