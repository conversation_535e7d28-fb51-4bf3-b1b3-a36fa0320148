/*
 * @Description: 错误处理的相关配置
 * @Author: wang y<PERSON>gong
 */
import { LoginUrl, BcpLoginUrl } from "../../config/url";
import { ErrorModalBtn } from "./errorTypes";

// 成功状态码
export const CODE_OK = "core.ok";

// CTIAM 相关状态码
export const CODE_CTIAM_1601 = "core.1601";
export const CODE_CTIAM_1602 = "core.1602";
export const CtiamCode = [CODE_CTIAM_1601, CODE_CTIAM_1602];

// 证书相关状态码
export const CODE_CERT_ERROR_3001 = "core.e3001";
export const CODE_CERT_ERROR_1500 = "core.e1500";

// 服务器端口相关状态码
export const CODE_SERVER_PORT_ERROR_2364 = "clnt.e2364";
export const CODE_SERVER_PORT_ERROR_2367 = "clnt.e2367";

// 配额相关状态码
export const CODE_QUOTA_EXCEEDED_2396 = "clnt.e2396";

// 被忽略的 code 类型，不会触发错误提示
export const IgnoreCode = [CODE_OK];
const isBcp = window.location.origin.includes("bcp");

// 允许把 reaseon 解析为 html 的 code
export const ReasonCanUseHtmlCode = ["clnt.e2008006"];

// 特定 code 的处理配置，业务中具体的处理方案在组件内主动 catch 并提供 btnConfig
export const DefaultModalBtnConfig: {
    [code: string]: ErrorModalBtn;
} = {
    "core.e1019": {
        // title 使用国际化的滞后处理，goLogin对应的是i18n.ts的值
        title: "goLogin",
        href: isBcp ? BcpLoginUrl : LoginUrl,
    },
};
