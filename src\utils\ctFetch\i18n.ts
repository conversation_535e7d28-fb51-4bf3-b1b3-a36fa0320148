import Vue from "vue";
import VueI18n from "vue-i18n";
import { getLang } from "../logic/i18n";

Vue.use(VueI18n);

export default new VueI18n({
    locale: getLang(),
    messages: {
        zh: {
            title: "系统提示",
            defaultReason: "未知错误",
            detail: "错误详情",
            confirm: "确定",
            cancle: "取消",
            jsError: "脚本执行错误",
            goLogin: "登录",

            /* eslint-disable @typescript-eslint/camelcase*/
            errMsg: {
                err: "网络异常err",
                err_timeout: "请求接口超时 {number} 秒",
                err_json: "json格式错误",
                err_svr: "网络异常 {number}",
                err_interceptor_req: "请求拦截器提前终止流程返回",
                err_interceptor: "响应拦截器提前终止流程返回",
                err_codeok: "后台返回的状态码code错误",
            },
        },
        en: {
            title: "System Prompt",
            defaultReason: "Unknown Error",
            detail: "Error Details",
            confirm: "Confirm",
            cancle: "Cancle",
            jsError: "Script Execution Error",
            goLogin: "Login",

            errMsg: {
                err: "Network Anomaly",
                err_timeout: "Interface timeout {number} S",
                err_json: "Json Format Error",
                err_svr: "Network Anomaly {number}",
                err_interceptor_req: "Request interceptor Early Termination",
                err_interceptor: "Response interceptor Early Termination",
                err_codeok: "Server Code Error",
            },
        },
    },
});
