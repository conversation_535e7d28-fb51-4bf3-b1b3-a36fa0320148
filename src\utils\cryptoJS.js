import { StatisticsModule } from "@/store/modules/statistics";
import CryptoJS from "crypto-js";

// 加密
export function Encrypt(str) {
    const key = CryptoJS.enc.Hex.parse(StatisticsModule.certHex.key); // 一个常量，前后端协定后一个字符串即可
    const iv = CryptoJS.enc.Hex.parse(StatisticsModule.certHex.iv);

    // const srcs = CryptoJS.enc.Hex.parse(str);
    const encrypted = CryptoJS.TripleDES.encrypt(str, key, {
        iv: iv,
        // mode: CryptoJS.mode.ECB,
        mode: CryptoJS.mode.CBC, // mode 与后台一致。有多个模式可选
        padding: CryptoJS.pad.Pkcs7, //
    });

    // 需要返回base64格式的加密结果，使用此句
    return CryptoJS.enc.Base64.stringify(encrypted.ciphertext);

    // 需要返回hex格式的加密结果，使用此句
    // return encrypted.ciphertext.toString().toUpperCase();
}

// 解密
export function Decrypt(str) {
    const key = CryptoJS.enc.Hex.parse(StatisticsModule.certHex.key); // 一个常量，前后端协定后一个字符串即可
    const base64 = CryptoJS.enc.Base64.parse(str);
    const src = CryptoJS.enc.Base64.stringify(base64);

    const decrypt = CryptoJS.TripleDES.decrypt(src, key, {
        // mode: CryptoJS.mode.ECB,
        mode: CryptoJS.mode.CBC, // mode 与后台一致。有多个模式可选
        padding: CryptoJS.pad.Pkcs7,
    });

    const decryptedStr = decrypt.toString(CryptoJS.enc.Hex);
    return decryptedStr.toString();
}
