import { PROXY_PREFIX } from "./_PREFIX";
export const ipManagementUrl = {
    // 创建白名单方案
    WhitelistCreate: PROXY_PREFIX + "/v1/backoriginWhitelist/create",
    // 回源白名单方案列表
    GetWhitelist: PROXY_PREFIX + "/v1/backoriginWhitelist/list",
    // 删除/批量删除回源白名单
    WhitelistDelete: PROXY_PREFIX + "/v1/backoriginWhitelist/batchDelete",
    // 查看回源IP
    IpDetail: PROXY_PREFIX + "/v1/backoriginWhitelist/backoriginIp",
    // 获取通知渠道
    GetNotify: PROXY_PREFIX + "/v1/backoriginWhitelist/notify",
    // 新增/修改通知渠道
    UpdateNotify: PROXY_PREFIX + "/v1/backoriginWhitelist/notify",
    // 查询回源IP方案发送记录
    sendLog: PROXY_PREFIX + "/v1/backoriginWhitelist/sendLog",
};
