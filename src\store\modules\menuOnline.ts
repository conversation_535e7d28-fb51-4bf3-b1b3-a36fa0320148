// 在线菜单缓存vuex
import { VuexModule, Module, Action, Mutation, getModule } from "vuex-module-decorators";
import { ctFetch } from "../../utils";
import store from "@cdnplus/common/store";
import { MenuUrl } from "@/config/url";

/**
 * 构建数据
 */
function buildData(ary: any, map: any) {
    for (const item of ary) {
        const uniquePath = item.hrefLocal && item.hrefLocal.replace(/^.*#/, "").replace(/\?.*/, "");
        const uCode = item.ucode;
        const conditionRes = item.state === "online" && item.enable === "true";
        map[uniquePath] = conditionRes;
        map[uCode] = conditionRes;
        // 父级菜单未开启上线，子集默认关闭，无法访问
        if (!conditionRes) {
            continue;
        }

        if (item.items && item.items instanceof Array && item.items.length) {
            buildData(item.items, map);
        }
    }
}

@Module({ dynamic: true, store, name: "menuOnline" })
class MenuOnline extends VuexModule {
    public menuMap: any = {};

    @Mutation
    SET_MENU_MAP(ary: any) {
        const map = {};
        buildData(ary, map);
        this.menuMap = map;
    }
    // GetMenuAllList 暂时没有被用到
    @Action
    public async GetMenuAllList(workspaceId: string) {
        const { query } = store.state.route;
        if (!workspaceId) {
            workspaceId = (query.workspaceId as string) || store.state.user.workspaceId;
        }
        const params = {
            workspaceId: workspaceId,
            domain: `${process.env.PKG_NAME}.main`,
            // domain: `ctaccessone.main`, // 暂时注释：iam没配菜单，不能通过链接访问
            // depth: 3, // 暂时注释：iam没配菜单，不能通过链接访问
        };
        const menuData = await ctFetch(MenuUrl, {
            data: params,
            cache: true,
        });
        this.SET_MENU_MAP((menuData as any).items || []);
    }
}

export const MenuOnlineModule = getModule(MenuOnline);
