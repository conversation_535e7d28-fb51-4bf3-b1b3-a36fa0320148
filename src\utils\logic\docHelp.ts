import { IpaConfigModule } from "@/store/modules/ipa/config";
import { nUserModule } from "@/store/modules/nuser";
import { StatisticsModule } from "@/store/modules/statistics";

/**
 * 打开文档悬浮框
 * @param url 文档链接
 * @param event 事件对象
 * @param tagName 触发事件标签
 */
export default function(url: string, event?: Event, tagName?: string) {
    const floatWinEnable = window.location.hash.includes("productType=eas")
        ? IpaConfigModule.ipaFloatWinEnable
        : StatisticsModule.floatWinEnable;
    // 处理v-html嵌入标签不能绑定点击事件的情况，将事件绑定在外层元素上，再通过tagName定位到指定元素触发
    if (event && tagName && (event.target as any)?.localName !== tagName) return;
    // 限制当前为ctyun且链接为ctyun文档的链接才采用悬浮框展示
    if (nUserModule.isCtyun && floatWinEnable && /www.ctyun.cn\/[document|help|help2]/.test(url)) {
        // ctyun支持悬浮框展示
        const ctLayout: any = (window as any).CtcloudLayoutV2 || (window as any).CtcloudLayout;
        ctLayout?.DocOverlay.open({
            docUrl: url,
            zIndex: 9999,
        });
    } else {
        // 非ctyun仍以链接跳转新窗口
        window.open(url);
    }
}
