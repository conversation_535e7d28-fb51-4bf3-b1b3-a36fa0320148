<template>
    <ct-dialog
        title="域名归属权校验"
        :visible="dialogShow"
        :append-to-body="true"
        :show-submit-btn="false"
        cancel-text="关闭"
        @close="handleCancel"
    >
        <div class="tip-box">
            域名【{{ domain }}】需要完成归属权验证，您可以通过DNS解析验证或文件验证，若操作失败请
            <a
                href="https://www.ctyun.cn/h5/wsc/worksheet/submit"
                style="color: #FA8334;cursor:pointer;font-weight: bold;"
                target="_blank"
                >前往客服工单系统</a
            >
            提客户工单。
        </div>
        <el-tabs v-model="currentTab">
            <el-tab-pane label="DNS解析验证" name="dns">
                <div class="flex-row-style row-box">
                    <span class="title-box">1、</span>
                    请在您的域名DNS服务商添加以下TXT记录
                    <a
                        style="color: #FA8334;cursor:pointer;font-weight: bold;"
                        class="aocdn-ignore-link"
                        @click="$docHelp(serviceUrl)"
                    >
                        【验证操作指南】
                    </a>
                </div>
                <div class="row-box padding-style">
                    <el-table :data="tableData" border style="width: 80%;">
                        <el-table-column label="记录类型" prop="zoneType" />
                        <el-table-column label="主机记录" prop="zoneHost" />
                        <el-table-column label="记录值" prop="zoneRecord" />
                    </el-table>
                </div>
                <div class="row-box">
                    <span class="title-box">2、</span>
                    等待TXT解析生成。
                </div>
            </el-tab-pane>
            <el-tab-pane label="文件验证" name="file">
                <div class="flex-row-style row-box">
                    <span class="title-box">1、</span>
                    请在您的{{ get(data, "domainZone") }}域名源站根目录创建一个文件。
                    <a
                        style="color: #FA8334;cursor:pointer;font-weight: bold;"
                        class="aocdn-ignore-link" 
                        @click="$docHelp(serviceUrl)"
                    >
                        【验证操作指南】
                    </a>
                </div>
                <div class="row-box padding-style">
                    文件名：{{ get(data, "zoneHost", "暂无数据") }}，文件内容：{{
                        get(data, "zoneRecord", "暂无数据")
                    }}
                </div>
                <div class="row-box">
                    <span class="title-box">2、</span>
                    通过{{ renderUrl() }}访问到该文件。
                </div>
            </el-tab-pane>
            <div class="row-box">
                <span class="title-box">3、</span>
                单击下方按钮进行验证
            </div>
            <div v-loading="loading">
                <div class="row-box padding-style">
                    <el-button type="primary" @click="handleCheck">验 证</el-button>
                </div>
                <div class="row-box padding-style">
                    验证结果：
                    <span v-if="!checkData" class="wait-check">待验证</span>
                    <span v-if="get(checkData, 'verifyResult') === true" class="success"
                        >验证成功，您可以继续添加域名。</span
                    >
                    <span v-if="get(checkData, 'verifyResult') === false" class="fail">
                        验证失败（失败原因：{{ get(checkData, "verifyResultDesc", "暂无原因") }}）
                    </span>
                </div>
            </div>
        </el-tabs>
    </ct-dialog>
</template>

<script>
import ctDialog from "@/components/ctDialog";
import { get } from "lodash-es";
import { verifyDomainBelong } from "@/api";

export default {
    name: "checkDialog",
    components: {
        ctDialog,
    },
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        domain: {
            type: String,
            default: "",
        },
        type: {
            type: String,
            default: "brm",
        },
        // loading: {
        //     type: Boolean,
        //     default: false,
        // },
    },
    data() {
        return {
            currentTab: "dns",
            dialogShow: false,

            data: null,
            checkDataAll: {
                dns: null,
                file: null,
            },
            loading: false,
        };
    },
    computed: {
        serviceUrl() {
            switch (this.type) {
                case "scdn":
                    return "https://www.ctyun.cn/document/10007729/10032537";
                case "waf":
                    return "https://www.ctyun.cn/document/10016653/10032542";
                case "ddos":
                    return "https://www.ctyun.cn/document/10018711/10032543";
                default:
                    return "https://www.ctyun.cn/document/10023958/10032762";
            }
        },
        tableData() {
            const ary = [
                {
                    zoneType: get(this.data, "zoneType", "暂无数据"),
                    zoneHost: get(this.data, "zoneHost", "暂无数据"),
                    zoneRecord: get(this.data, "zoneRecord", "暂无数据"),
                },
            ];
            return ary;
        },
        checkData() {
            return get(this.checkDataAll, this.currentTab, null);
        },
    },
    watch: {
        visible: {
            handler(val) {
                this.dialogShow = val;
                if (!val) {
                    this.currentTab = "dns";
                }
            },
            immediate: true,
        },
    },
    methods: {
        /**
         * 初始化数据
         */
        initData(data, isCheckInside) {
            this.data = data;
            if (isCheckInside) {
                this.$set(this.checkDataAll, this.currentTab, data);
            } else {
                this.checkDataAll = {
                    dns: null,
                    file: null,
                };
            }
        },
        /**
         * 处理验证
         */
        async handleCheck() {
            const params = {
                domain: this.domain,
                verifyType: this.currentTab === "dns" ? 1 : 2,
            };
            this.loading = true;
            try {
                const res = await verifyDomainBelong(params);
                this.$set(this.checkDataAll, this.currentTab, res);
                let isSuccess = false;
                if (get(this.checkData, "verifyResult") === true) {
                    isSuccess = true;
                } else {
                    isSuccess = false;
                }
                this.$emit("handle-verify", isSuccess);
            } catch (e) {
                this.$errorHandler(e);
            }

            this.loading = false;
        },
        /**
         * 渲染URL
         */
        renderUrl() {
            const res = ["domainZone", "zoneHost", "zoneType"].every(item => {
                return get(this.data, item);
            });
            if (!res) {
                return "【暂无数据】";
            }

            const url = `http(s)://${get(this.data, "domainZone")}/${get(this.data, "zoneHost")}.${(
                get(this.data, "zoneType", "") || ""
            ).toLowerCase()}`;
            return url;
        },
        handleCancel() {
            this.$emit("update:visible", false);
        },
        get,
    },
};
</script>

<style scoped lang="scss">
.tip-box {
    font-size: 12px;
    background: #fdf6ec;
    padding: 10px;
    border-radius: 4px;
}
.row-box {
    margin-bottom: 10px;
    &.padding-style {
        padding-left: 25px;
    }
    .title-box {
        width: 25px;
    }
    .wait-check {
        color: #fa8334;
    }
    .success {
        color: #67c23a;
    }
    .fail {
        color: #f56c6c;
    }
}
</style>
