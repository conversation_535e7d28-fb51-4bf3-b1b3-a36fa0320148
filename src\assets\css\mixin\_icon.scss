@import './../_var.scss';
@import './_layout.scss';

//三条横线的菜单
@mixin g-css-menu-m($width: 18px, $height: 14px, $item-height: 2px, $color: $g-color-yellow, $parent-position: relative){
	&{
		color: $color;
		@include g-set-position($position: $parent-position);
	}
	&::before{
		@include g-set-position($position: absolute, $top: 50%, $left: 50%);
		content: '';
		width: $width;
		height: $height;
		border-top: 2px solid currentColor;
		border-bottom: 2px solid currentColor; 
		transform: translate(-50%, -50%);
	}
	&::after{
		@include g-set-position($position: absolute, $top: 50%, $left: 50%);
		content: '';
		width: $width;
		height: $item-height;
		background-color: currentColor;
		transform: translate(-50%, -50%);

	}
}

@mixin g-css-arrow-up($width: 14.414px, $height: 14.414px, $item-height: 2px, $color: $g-color-white, $parent-position: relative){
	&{
		$color: $color;
		@include g-set-position($position: $parent-position);
	}
	&::before{
		@include g-set-position($position: absolute, $top: 50%, $left: 50%);
		content: '';
		width: $width;
		height: $height;
		border-top: $item-height solid currentColor;
		border-left: $item-height solid currentColor; 
		//垂直方向拉回来25%，再旋转45度，刚好旋转后近似居中了
		transform: translate(-50%, -25%) rotate(45deg);
	}
}