<!--多个级联选择器-->
<template>
    <div class="cascade-select">
        <dictionary-select
            v-for="(item, key) in config"
            :key="key"
            v-model="form[item.prop]"
            v-bind="configInside(item)"
            :outside-ary="getRelateAry(item)"
            @handle-change="handleChange($event, item)"
        />
    </div>
</template>

<script>
import dictionarySelect from "@/components/dictionarySelect";
import { get, has } from "lodash-es";
import { mapGetters } from "vuex";

export default {
    name: "index",
    props: {
        form: {
            type: Object,
            required: true,
            default: () => {
                return {};
            },
        },
        config: {
            type: Array,
            required: true,
            default: () => {
                return [];
            },
        },
        isRelateDisabled: {
            type: Boolean,
            default: true,
        },
    },
    components: {
        dictionarySelect,
    },
    data() {
        return {
            // 关联数组映射表
            relateAryMap: null,
            dictionaryMap: null,
            relatePropMap: null,
        };
    },
    computed: {
        /**
         * 内部配置，去除prop
         */
        configInside() {
            return item => {
                const info = { ...item };
                delete info.prop;
                if (!this.isRelateDisabled) {
                    return info;
                }

                const prop = item.prop;
                const fatherProp = get(this.relatePropMap, prop);
                if (has(info, "disabled")) {
                    return info;
                }

                if (fatherProp && !get(this.form, fatherProp)) {
                    info.disabled = true;
                } else {
                    info.disabled = false;
                }

                return info;
            };
        },
        ...mapGetters({
            optionsMap: "optionsMap",
        }),
    },
    created() {
        this.init();
    },
    methods: {
        /**
         * 初始化数据
         */
        init() {
            if (!(this.config instanceof Array)) {
                return;
            }

            // 初始化关联数组映射表和字典映射表
            const map = {};
            const dictionaryMap = {};
            const childMap = {};
            const relateMap = {};
            this.config.forEach(item => {
                if (item.relateProp) {
                    map[item.relateProp] = [];
                    relateMap[item.relateProp] = item.prop;
                }
                if (item.isMap) {
                    dictionaryMap[item.prop] = "";
                }
                if (item.relateProp && item.relateType === "map") {
                    childMap[item.relateProp] = true;
                }

                if (childMap[item.prop]) {
                    item.isMap = true;
                }
            });

            this.relateAryMap = Object.assign({}, this.relateAryMap, map);
            this.dictionaryMap = Object.assign({}, this.dictionaryMap, dictionaryMap);
            this.relatePropMap = Object.assign({}, this.relatePropMap, relateMap);
        },
        /**
         * 处理组件变化
         */
        handleChange({ val, data }, item) {
            if (item.relateType !== "map") {
                const ary =
                    get(
                        data.find(item => item.value === val),
                        "children",
                        []
                    ) || [];
                this.$set(this.relateAryMap, item.relateProp, ary);
                return;
            }

            const mapRoot = item.relateMapRoot || "console_";
            const config = {
                prop: item.relateMapValue || val,
            };
            const map = {
                [`${mapRoot},${val}`]: Object.assign({}, config, item.relateMapConfig),
            };
            this.dictionaryMap[item.relateProp] = item.relateMapValue || val;
            const storeInfo = {
                type: "queryPageDictionary",
                data: map,
            };
            this.$store.dispatch(storeInfo);
        },
        /**
         * 获取关联数组
         * @param item 表述信息
         */
        getRelateAry(item) {
            if (item.outsideAry && item.outsideAry instanceof Array) {
                return item.outsideAry;
            }

            // const info = this.config.find()
            if (item.isMap) {
                const mapAry = get(this.optionsMap, get(this.dictionaryMap, item.prop), []) || [];
                return mapAry;
            }

            const ary = get(this.relateAryMap, item.prop, []) || [];
            return ary;
        },
    },
};
</script>

<style scoped lang="scss">
.cascade-select {
    display: flex;
    flex-direction: row;
}
</style>
