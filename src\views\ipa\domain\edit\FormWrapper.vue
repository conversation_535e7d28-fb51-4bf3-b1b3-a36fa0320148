<template>
    <section class="domain-edit-wrap">
        <el-scrollbar>
            <section class="domain-edit" v-loading="initLoading">
                <template v-if="!isService && !isCreating">
                    <el-alert
                        class="header-tip-style"
                        type="info"
                        show-icon
                        title="域名当前配置中/已停用，不支持配置能力。域名状态为已启用方可编辑"
                        :closable="false"
                    />
                </template>
                <template v-if="isCreating">
                    <el-alert
                        class="header-tip-style"
                        type="info"
                        show-icon
                        title="当前域名/实例正在新增中，页面展示初始化内容，请耐心等待5~10分钟"
                        :closable="false"
                    />
                </template>
                <div class="domain-edit-content">
                    <el-form
                        class="simple-create-form"
                        name="form"
                        ref="form"
                        :model="form"
                        label-width="130px"
                        label-position="left"
                        :disabled="!isService || !isEdit"
                    >
                        <div>
                            <!-- <p class="label-name">基础信息</p> -->
                            <cute-titled-block title="基础信息">
                                <template #content>
                                    <basic-info :baseInfo="baseInfo" class="base-info-style" />
                                </template>
                            </cute-titled-block>
                            <cute-titled-block title="回源配置" class="ct-table-style">
                                <template v-slot:content>
                                    <el-form-item
                                        label="回源策略"
                                        prop="origin_type"
                                        :rules="rules.origin_type"
                                    >
                                        <el-radio-group v-model="form.origin_type">
                                            <el-radio-button :label="1">择优回源</el-radio-button>
                                            <el-radio-button :label="2">按权重回源</el-radio-button>
                                            <el-radio-button :label="3">保持登录</el-radio-button>
                                        </el-radio-group>
                                        <div class="form-item-tip">
                                            <span>
                                                <ct-svg-icon
                                                    icon-class="info-circle"
                                                    class-name="icon-column-label icon-style"
                                                />
                                            </span>
                                            选择“择优回源”时，优先回最快的源站，忽略权重；选择“按权重回源”时，按照配置的权重回源；选择“保持登录”时则基于客户端IP哈希回源。
                                        </div>
                                    </el-form-item>
                                    <backSourceConf
                                        ref="backSourceConf"
                                        :data="form.forward_origins"
                                        :origin-type="form.origin_type"
                                        :access-mode="access_mode"
                                        page-type="edit"
                                    />
                                </template>
                            </cute-titled-block>
                            <!-- <p class="label-name">访问控制</p> -->
                            <cute-titled-block v-if="!isJinhua" title="访问控制">
                                <template #content>
                                    <el-form-item label="IP黑白名单">
                                        <el-switch
                                            v-model="form.black_white_list_switch"
                                            @change="black_white_list_switch_change"
                                        ></el-switch>
                                    </el-form-item>
                                    <div v-show="form.black_white_list_switch">
                                        <el-form-item label="类型">
                                            <el-radio-group v-model="form.blackWhiteListType">
                                                <el-radio :label="2">白名单</el-radio>
                                                <el-radio :label="1">黑名单</el-radio>
                                            </el-radio-group>
                                        </el-form-item>
                                        <el-form-item prop="ip_list" :rules="rules.ip_list">
                                            <el-input
                                                type="textarea"
                                                v-model="form.blackWhiteListContent"
                                                rows="5"
                                                class="blackWhiteList"
                                            ></el-input>
                                            <div class="form-item-tip">
                                                <span>
                                                    <ct-svg-icon
                                                        icon-class="info-circle"
                                                        class-name="icon-column-label icon-style"
                                                    />
                                                </span>
                                                通过黑/白名单来对访问者身份进行识别和过滤，支持IPv6地址填写。
                                            </div>
                                        </el-form-item>
                                    </div>
                                    <!-- 支持区域访问控制 -->
                                    <access-region-control
                                        ref="accessRegionControlRef"
                                        :defaultValue="form.region_access_control"
                                        @change="val => (form.region_access_control = val)"
                                        :disabled="!isService || !isEdit"
                                    />
                                </template>
                            </cute-titled-block>
                            <!-- <p class="label-name">传递用户IP回源</p> -->
                            <cute-titled-block title="传递用户IP回源">
                                <template #content>
                                    <el-form-item label="传递用户IP回源">
                                        <el-switch v-model="form.user_ip.switch"></el-switch>
                                    </el-form-item>
                                    <div class="user" v-show="form.user_ip.switch">
                                        <div class="user_item">
                                            <el-form-item label-width="150px" label="传递用户IP回源方式">
                                                <el-select
                                                    v-model="form.user_ip.proxy_protocol"
                                                    placeholder="请选择"
                                                >
                                                    <el-option label="tcp_option" :value="2"></el-option>
                                                    <el-option label="proxy_protocol" :value="1"></el-option>
                                                </el-select>
                                                <span
                                                    v-show="form.user_ip.proxy_protocol == 2"
                                                    class="user_item"
                                                >
                                                    <el-link
                                                        class="link-style"
                                                        type="primary"
                                                        @click="$docHelp(toaDownload)"
                                                        >toa模块下载</el-link
                                                    >
                                                </span>
                                                <div
                                                    v-show="form.user_ip.proxy_protocol == 2"
                                                    class="form-item-tip"
                                                >
                                                    <span>
                                                        <ct-svg-icon
                                                            icon-class="info-circle"
                                                            class-name="icon-column-label"
                                                        /> </span
                                                    >提示:使用tcp_option传递用户IP回源时,您需要下载适配源站系统版本的toa模块,并安装到源站后才可正常使用该功能。
                                                </div>
                                            </el-form-item>
                                        </div>
                                        <div class="user_item" v-show="form.user_ip.proxy_protocol === 1">
                                            <el-form-item
                                                label-width="150px"
                                                prop="proxy_protocol_version"
                                                :rules="rules.proxy_protocol_version"
                                                label="proxy_protocol版本"
                                            >
                                                <el-select
                                                    v-model="form.user_ip.proxy_protocol_version"
                                                    placeholder="请选择"
                                                >
                                                    <el-option label="v1" :value="1"></el-option>
                                                    <el-option label="v2" :value="2"></el-option>
                                                </el-select>
                                                <div class="form-item-tip">
                                                    <!-- <span> <i class="el-icon-warning-outline icon-tip"></i> </span> -->
                                                    <span>
                                                        <ct-svg-icon
                                                            icon-class="info-circle"
                                                            class-name="icon-column-label icon-style"
                                                        />
                                                    </span>
                                                    请根据源站支持的proxy_protocol协议版本选择,v1版本仅支持tcp协议,
                                                    v2版本同时支持tcp和udp。
                                                    <div>
                                                        <span style="margin-left:17px;"></span
                                                        >选择proxy_protocol协议时，必须确保源站也同时开启proxy_protocol协议；关闭时，源站也必须同时关闭。否则，业务会受到影响。
                                                    </div>
                                                </div>
                                            </el-form-item>
                                        </div>
                                    </div>
                                </template>
                            </cute-titled-block>
                        </div>
                    </el-form>
                </div>
            </section>
            <cute-fixed-footer class="submit">
                <div class="footer-content">
                    <el-tooltip
                        v-if="!isService"
                        class="item"
                        effect="dark"
                        content="域名配置中/已停用，不支持进行编辑 "
                        placement="top"
                    >
                        <el-button
                            type="primary"
                            @click="editConfig"
                            size="medium"
                            v-if="!isEdit && !isService"
                            :disabled="!isService"
                        >
                            编辑配置
                        </el-button>
                    </el-tooltip>
                    <el-button type="primary" @click="editConfig" size="medium" v-if="!isEdit && isService">
                        编辑配置
                    </el-button>
                    <el-button plain size="medium" @click="handleCancel" v-if="isEdit">
                        取 消
                    </el-button>
                    <el-button
                        type="primary"
                        :loading="submitLoading"
                        size="medium"
                        @click="handleSubmit(null, null)"
                        v-if="isEdit"
                    >
                        保 存
                    </el-button>
                </div>
            </cute-fixed-footer>
        </el-scrollbar>
    </section>
</template>

<script>
import { ip } from "@cdnplus/common/config/pattern";
import basicInfo from "@/views/domainConfig/basicInfo";
import { DomainUrl } from "@/config/url/ipa/domain";
import { WorkOrderList } from "@/config/url/ipa/work_order_list";
import { SecurityAbilityModule } from "@/store/modules/securityAbility";
import urlTransformer from "@/utils/logic/url";
import AccessRegionControl from "../components/access-region-control/index.vue";
import {
    defaultAccessRegionControlForm,
    defaultAccessRegionControlListItem,
    parseAccessRegionControlList,
} from "../components/access-region-control/util";

import { get, cloneDeep, mergeWith, set } from "lodash-es";
import commonMixin from "@/views/ipa/domain/commonMixin";
import ctSvgIcon from "@/components/ctSvgIcon";

import backSourceConf from "@/views/ipa/domain/components/backSourceConf/index.vue";
import { forwardOriginItem } from "../components/backSourceConf/templateData";
import { transFormPropToClassName } from "@/utils/utils";

export default {
    components: {
        basicInfo,
        ctSvgIcon,
        AccessRegionControl,
        // domainPanel,
        backSourceConf,
    },
    mixins: [commonMixin],
    props: {
        defaultData: Object,
        from: String,
    },
    data() {
        return {
            isOpen: true,
            isAddDomain: false, // 是否为添加域名模式
            // isView: false,
            initLoading: false,
            submitLoading: false,
            temp_black_white_list_type: 1,
            temp_black_white_list_content: "",
            form: {
                // originTableData: [],
                // udp_ports: [
                //     {
                //         origin: "",
                //         req: "",
                //     },
                // ],
                // tcp_ports: [
                //     {
                //         origin: "",
                //         req: "",
                //     },
                // ],
                origin_type: 1, // 回源策略
                blackWhiteListType: 1, //改
                blackWhiteListContent: "",
                user_ip: {
                    switch: false,
                    proxy_protocol: 1,
                    proxy_protocol_version: 1,
                },
                black_white_list_switch: false,
                region_access_control: defaultAccessRegionControlForm(),
                forward_origins: [cloneDeep(forwardOriginItem)],
            },
            addDialog: false, // 源站-弹窗
            dialogStatus: "",
            originItem: {
                address: "",
                role: 1,
                level: 1,
                weight: 10,
            },
            getDomainDetailAbortController: null,
            // ipv6_enable: false,
            // ipv6_enable_cache: false,
            // accelerateDomain: "",
            // accelerateRange: "",
            result: {
                id: "",
                account_id: "",
                area_scope: "",
                biz_tag_list: [],
                action: 5,
                config: {
                    origin: {
                        origin_type: 1,
                        probe_port: "",
                    },
                    access_control: {
                        control_switch: "",
                        control_type: "",
                        ip_list: "",
                        match_type: "",
                    },
                    dynamic_cfg: {
                        kcp: "",
                        package_loss: "",
                        route_type: "",
                    },
                    user_ip: {
                        proxy_protocol: null,
                        tcp_option: null,
                        proxy_protocol_version: null,
                    },
                },
                domain: "www.wfq1115h.ele.me",
                domain_id: "ed7c3fcfe6cf71d2f12dac23014412c6",
                domain_status: 2,
                domain_type: 1,
                global_resource_pool_id: 1509846,
                operator: "work_sheet",
                parent_domain_draft: 11,
                partner: "001",
                predeploy_resource_pool_id: 1509741,
                product: 9,
                version: "",
            },
            baseInfo: [
                { label: "加速域名", value: "-" },
                { label: "实例名称", value: "-" },
                { label: this.isJinhua ? "高防CNAME" : "CNAME", value: "-" },
                { label: "产品类型", value: "-" },
                { label: "加速区域", value: "-" },
                { label: "创建时间", value: "-" },
            ],
            areaScopeLabel(area_scope) {
                let label = "";
                switch (area_scope) {
                    case 1:
                        label = "中国内地";
                        break;
                    case 2:
                        label = "全球（不含中国内地）";
                        break;
                    case 3:
                        label = "全球";
                        break;
                }
                return label;
            },
            productCodeLabel(productCode) {
                let label = "";
                switch (productCode) {
                    case "001":
                        label = "静态加速(中国内地)";
                        break;
                    case "003":
                        label = "下载加速(中国内地)";
                        break;
                    case "004":
                        label = "视频点播加速(中国内地)";
                        break;
                    case "005":
                        label = "视频直播(中国内地)";
                        break;
                    case "006":
                        label = "全站加速(中国内地)";
                        break;
                    case "007":
                        label = "安全加速";
                        break;
                    case "008":
                        label = "CDN加速(中国内地)";
                        break;
                    case "009":
                        label = "应用加速(中国内地)";
                        break;
                    case "010":
                        label = "Web应用防火墙(边缘云版)";
                        break;
                    case "011":
                        label = "DDoS高防(边缘云版)";
                        break;
                    case "012":
                        label = "smart dns";
                        break;
                    case "013":
                        label = "网站安全监测";
                        break;
                    case "014":
                        label = "下载加速(闲时)(中国内地)";
                        break;
                    case "015":
                        label = "极速直播(中国内地)";
                        break;
                    case "016":
                        label = "分布式安全云平台";
                        break;
                    case "017":
                        label = "容器安全平台";
                        break;
                    case "018":
                        label = "爬虫管理";
                        break;
                    case "019":
                        label = "GTM";
                        break;
                    case "020":
                        label = "边缘安全与加速";
                        break;
                    case "021":
                        label = "零信任服务";
                        break;
                    case "022":
                        label = "重保产品";
                        break;
                    case "024":
                        label = "AOne 边缘接入服务";
                        // label = this.isJinhua ? "AOne 边缘接入服务" : "AccessOne 边缘接入服务";
                        break;
                }
                return label;
            },
            rules: {
                // 访问控制？黑白名单
                ip_list: [
                    {
                        validator: (rule, value, callback) => {
                            value = this.form.blackWhiteListContent;
                            if (value) {
                                const reg = /^[a-zA-Z0-9,-./:\s]*$/;
                                if (reg.test(value)) {
                                    callback();
                                } else {
                                    callback(new Error(`格式有误，请检查`));
                                }
                            } else {
                                callback();
                            }
                        },
                    },
                    {
                        validator: this.checkIpList,
                        trigger: "blur",
                    },
                ],
                proxy_protocol_version: [
                    {
                        validator: this.validatorVersion,
                        trigger: "change",
                    },
                ],
                // originAll: [{ required: true, validator: this.validateOriginAll, trigger: "blur, change" }],
                origin_type: [{ required: true, message: "请选择回源策略", trigger: "blur, change" }],
            },
        };
    },
    computed: {
        isJinhua() {
            // TODO 0619
            return SecurityAbilityModule.serviceIdentifier === "jinhua";
        },
        currentModule() {
            return "cdnIpaDomainEdit";
        },
        securityDomain() {
            return SecurityAbilityModule.ipaSecurityDomain;
        },
        securityBasicDomainInfo() {
            return SecurityAbilityModule.securityBasicDomainInfo;
        },
        isEdit() {
            return SecurityAbilityModule.isEdit;
        },
        // 域名是否启用中
        isService() {
            const status = get(SecurityAbilityModule.securityBasicDomainInfo, "status");
            return status === 4;
        },
        isCreating() {
            return get(this.result, "creating");
        },
        isIpaSecurityFormSame() {
            return SecurityAbilityModule.isIpaSecurityFormSame;
        },
        isDomainCorrect() {
            return this.securityDomain === this.result.domain;
        },
        access_mode() {
            return SecurityAbilityModule.ipaAccessMode;
        },
        inst_name() {
            return SecurityAbilityModule.ipaInstName;
        },
        virtual_domain() {
            return SecurityAbilityModule.ipaVirtualDomain;
        },
        toaDownload() {
            return urlTransformer({
                a1Ctyun: "https://www.ctyun.cn/document/10065985/10323895",
                a1Ctcloud: "https://www.esurfingcloud.com/document/zh-cn/20689737/20690458",
            });
        },
    },
    watch: {
        securityDomain: {
            async handler(val) {
                if (!val) {
                    return;
                }
                this.getDetail();
                SecurityAbilityModule.SET_IS_EDIT(false);
            },
            immediate: true,
        },
        securityBasicDomainInfo: {
            async handler(val) {
                if (!val) {
                    return;
                }
                this.handleBaseInfo(val);
            },
            immediate: true,
        },
        isIpaSecurityFormSame() {
            const data = {
                icChange: !this.isIpaSecurityFormSame,
                fn: this.handleSubmit,
            };

            // window.custom.emit("handleCDNConfigChange", data);

            SecurityAbilityModule.SET_IS_IPA_SECURITY_CHANGE(!this.isIpaSecurityFormSame);
            SecurityAbilityModule.SET_SECURITY_MODULE({ handleSubmit: data?.fn });
        },
        form: {
            deep: true,
            handler(val) {
                SecurityAbilityModule.SET_SECURITY_CURRENT_FORM(val);
            },
            immediate: true,
        },
    },
    mounted() {
        // this.isView = this.from === "view";
        this.init();
    },
    beforeDestroy() {
        this.getDomainDetailAbortController?.abort("cancel by user");
    },
    methods: {
        black_white_list_switch_change(val) {
            if (val) {
                this.$set(this.form, "blackWhiteListType", this.temp_black_white_list_type);
                this.$set(this.form, "blackWhiteListContent", this.temp_black_white_list_content);
            } else {
                this.$set(this.form, "blackWhiteListType", 1);
                this.$set(this.form, "blackWhiteListContent", "");
            }
        },
        async handleSubmit(resolve, reject) {
            try {
                await this.$refs.form.validate();
            } catch (err) {
                this.handleFormError(err);
                reject && reject(false);
                return;
            }

            const backSourceConfData = this.$refs.backSourceConf.getSubmitData();
            const hasFtpData = backSourceConfData.some(item => {
                const ftpPorts = get(item, "ftp_ports");
                return ftpPorts && Object.values(ftpPorts).every(value => value);
            });

            // 产品需求，检查是否配置ftp值
            if (hasFtpData) {
                await this.$alert(
                    "仅支持被动模式的ftp，不支持主动模式的ftp，如您确认是被动模式的ftp，请点击【确定】后继续",
                    "提示",
                    {
                        confirmButtonText: "确定",
                        type: "warning",
                    }
                );
            }

            const param = this.restructureData("123", backSourceConfData); // 123后端没有用到

            try {
                this.submitLoading = true;
                await this.reqUpdateDomain(param);
                this.$router.push({
                    name: "domain.list",
                    query: {
                        domain: this.result.domain,
                        status: 2,
                    },
                });
                this.$message.success("操作成功");
                const isEdit = false;
                SecurityAbilityModule.SET_IS_EDIT(isEdit);
                SecurityAbilityModule.SET_SECURITY_RENDER_KEY();
                resolve && resolve(true);
            } catch (e) {
                this.submitLoading = false;
                this.$errorHandler(e);
                reject && reject(false);
            } finally {
                this.submitLoading = false;
            }
        },
        /**
         * 处理表单报错情况
         */
        handleFormError(errorMap) {
            // 只处理回源配置的报错情况，由于新版回源配置的内容比较多，且能添加组
            // 这里优化补充，当回源配置报错时，自动定位到报错点，报错原因
            const errors = Object.keys(errorMap);
            for (const error of errors) {
                // 非回源配置相关，提前跳出
                if (error.indexOf("forward_origins") === -1) {
                    continue;
                }

                // 命中一个，则退出循环，后续循环不执行
                // 能够找到对应元素的原因是，前端在这里取巧，刚好利用各个元素的prop绑定成对应的className
                // 所以报错的prop可以找到对应的dom元素，进而滚动到对应的报错元素
                const className = transFormPropToClassName(error);
                const errorEle = document.querySelector(`.${className}`);
                errorEle && errorEle.scrollIntoView({ behavior: "smooth" });
                break;
            }
        },
        reqUpdateDomain(data) {
            return this.$ctFetch(DomainUrl.updataDomain, {
                method: "POST",
                transferType: "json",
                clearEmptyParams: false,
                body: {
                    data,
                },
            });
        },
        // 参数转化
        restructureData(id, backSourceConfData) {
            this.result.id = id;
            this.result.action = 5;
            // this.result.area_scope = this.accelerateRange;
            // if (this.ipv6_enable_cache !== this.ipv6_enable) {
            //     this.result.ipv6_enable = this.ipv6_enable;
            // } else {
            //     delete this.result.ipv6_enable;
            // }
            // 回源配置：回源策略
            this.result.config.origin.origin_type = parseInt(this.form.origin_type) || 1;
            // 回源配置：端口信息

            // 访问控制
            this.result.config.access_control.control_switch = this.form?.black_white_list_switch ? 1 : 2;
            this.result.config.access_control.control_type = this.form.blackWhiteListType;
            this.result.config.access_control.ip_list = this.form.blackWhiteListContent;
            this.result.config.access_control.region_access_control = this.$refs.accessRegionControlRef?.formData;
            // 传递用户ip回源
            if (this.form.user_ip.switch) {
                this.result.config.user_ip.proxy_protocol = this.form.user_ip.proxy_protocol === 1 ? 1 : null;
                this.result.config.user_ip.tcp_option = this.form.user_ip.proxy_protocol === 2 ? 1 : null;
                this.result.config.user_ip.proxy_protocol_version =
                    this.form.user_ip.proxy_protocol === 1 ? this.form.user_ip.proxy_protocol_version : null;
            } else {
                this.result.config.user_ip = null;
            }
            // access_mode
            this.result.access_mode = this.access_mode;
            // inst_name
            this.result.inst_name = this.inst_name;
            // virtual_domain
            this.result.virtual_domain = this.virtual_domain;

            const param = cloneDeep(this.result);
            if (param?.ipv6_enable || param?.ipv6_enable === false) {
                delete param?.ipv6_enable;
            }

            // 赋值回源配置
            set(param, "config.origin.forward_origins", backSourceConfData);
            return param;
        },
        async handleCancel() {
            if (this.isIpaSecurityFormSame) {
                SecurityAbilityModule.SET_IS_EDIT(false);
            } else {
                await this.$confirm("当前域名有未保存的配置，是否确定放弃改动？", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                }).then(() => {
                    if (!this.isIpaSecurityFormSame) {
                        this.getDetail();
                    }
                });
            }
        },
        /**
         * 检查多个IP
         */
        checkIpList(rule, value, callback) {
            value = this.form.blackWhiteListContent;
            if (this.form?.black_white_list_switch && !value) {
                return callback(new Error("IP黑白名单开启，请填写列表"));
            }
            if (!this.form?.black_white_list_switch && !value) {
                return callback();
            }

            const ary = value.split(",");
            // ip检验规则
            const pattern = new RegExp(ip);
            const numberPattern = /^([1-9][0-9]?|[1][0-9][0-9]|[2]([0-4][0-9]|[5][0-6]))$/;
            const res = ary.every(item => {
                // 单个ip的情况
                if (pattern.test(item)) {
                    return true;
                }

                // ip掩码情况
                const maskCode = item.split("/");
                if (maskCode && maskCode.length === 2) {
                    const start = maskCode[0];
                    const end = maskCode[1];
                    if (pattern.test(start) && numberPattern.test(end)) {
                        return true;
                    }
                }

                // 区间情况
                const range = item.split("-");
                if (range && range.length === 2) {
                    const start = range[0];
                    const end = range[1];
                    if (pattern.test(start) && pattern.test(end)) {
                        return true;
                    }
                }

                return false;
            });
            if (!res) {
                return callback(new Error("格式有误，请检查"));
            }

            return callback();
        },
        // toaDownload() {
        //     if (window.location.href.includes("esurfingcloud.com")) return;
        //     window.open("https://www.ctyun.cn/document/10065985/10323895", "_blank");
        // },
        /**
         * 校验 proxy_protocol版本
         */
        validatorVersion(rule, value, callback) {
            // 检查是否有udp端口值
            const forwards_origin = get(this.form, "forward_origins", []) || [];
            const hasUdpPortValue = forwards_origin.some(item => {
                const udp_ports = item.udp_ports || [];
                return udp_ports.some(valueItem => valueItem.req);
            });

            if (hasUdpPortValue) {
                if (
                    this.form.user_ip.proxy_protocol === 1 &&
                    this.form.user_ip.proxy_protocol_version === 1 &&
                    this.form.user_ip.switch
                ) {
                    return callback(new Error("v1版本仅支持tcp协议"));
                }
            }
            return callback();
        },
        // 获取域名详情
        async getDetail() {
            if (!this.securityDomain) return;
            this.$refs.form?.resetFields();

            this.getDomainDetailAbortController?.abort("cancel by user");
            this.getDomainDetailAbortController = new AbortController();

            try {
                this.initLoading = true;
                SecurityAbilityModule.SET_IS_IPA_DOMAIN_DETAIL_LOADING(true);
                const res = await this.$ctFetch(DomainUrl.domainDetail, {
                    method: "GET",
                    transferType: "json",
                    data: {
                        account_id: this.$store.state.user.userInfo.userId,
                        domain: this.securityDomain,
                    },
                    signal: this.getDomainDetailAbortController.signal,
                });
                // 回填数据
                if (res) {
                    this.init(res);
                    SecurityAbilityModule.SET_IS_EDIT(false);
                }
            } catch (error) {
                if (error.name === 'AbortError' || error.raw === "cancel by user") {
                    return;
                }

                SecurityAbilityModule.SET_SECURITY_ORIGIN_FORM(cloneDeep(this.form));
                SecurityAbilityModule.SET_SECURITY_CURRENT_FORM(this.form);

                this.$errorHandler(error);
            }

            this.initLoading = false;
            SecurityAbilityModule.SET_IS_IPA_DOMAIN_DETAIL_LOADING(false);
        },
        async init(val) {
            if (!val) return;
            if (this.securityDomain !== val?.result?.[0]?.domain) return;
            const result = cloneDeep(val?.result) || [];

            // 赋值给 result
            this.result = result[0];

            // 传递用户ip
            const user_ip = result[0]?.config?.user_ip || {};
            this.form.user_ip.switch =
                user_ip?.proxy_protocol === 2 && user_ip?.tcp_option === 2 ? false : true;
            if (this.form.user_ip.switch) {
                this.form.user_ip.proxy_protocol = user_ip?.proxy_protocol === 1 ? 1 : 2;
                this.form.user_ip.proxy_protocol_version =
                    user_ip?.proxy_protocol === 1 ? user_ip?.proxy_protocol_version : 1;
            }

            // 初始化回源配置信息
            this.initBackSourceConf(this.result);
            this.form.tactics = result[0]?.config?.origin?.origin_type;

            // 访问控制
            const black_white_list_switch =
                result[0]?.config.access_control?.control_switch === 1 ? true : false;
            this.$set(this.form, "black_white_list_switch", black_white_list_switch);

            this.form.blackWhiteListType = result[0]?.config?.access_control?.control_type || 1;
            this.temp_black_white_list_type = result[0]?.config?.access_control?.control_type || 1;

            this.form.blackWhiteListContent = this.result.config?.access_control?.ip_list;
            this.temp_black_white_list_content = this.result.config?.access_control?.ip_list;

            // 访问区域控制
            const region_access_control = result[0]?.config?.access_control?.region_access_control || {};
            const defaultRegionAccessControl = defaultAccessRegionControlForm();
            this.form.region_access_control = Object.assign(
                {},
                {
                    switch: region_access_control?.switch || defaultRegionAccessControl.switch,
                    type: region_access_control?.type || defaultRegionAccessControl.type,
                    list: region_access_control?.list?.length
                        ? region_access_control.list.map(itm => {
                              return {
                                  ...itm,
                                  country: parseAccessRegionControlList(itm?.country),
                                  area_province: parseAccessRegionControlList(itm?.area_province),
                                  vendor: parseAccessRegionControlList(itm?.vendor),
                              };
                          })
                        : [defaultAccessRegionControlListItem()],
                }
            );

            SecurityAbilityModule.SET_SECURITY_ORIGIN_FORM(cloneDeep(this.form));
            SecurityAbilityModule.SET_SECURITY_CURRENT_FORM(this.form);
            // 基础信息
            // this.accelerateDomain = result[0]?.domain;
            // this.ipv6_enable = result[0]?.ipv6_enable;
            // this.ipv6_enable_cache = result[0]?.ipv6_enable;
            // this.productName = result[0]?.product === 9 ? "应用加速" : "";
            // this.accelerateRange = result[0]?.area_scope;
            // 是否有正在进行的工单
            // const rstOrder = await this.getWorkOrderDetail();
            // this.workOrderGoing = rstOrder.result.length > 0 ? true : false;
            // this.workOrderGoingItem = rstOrder.result.length > 0 ? rstOrder.result[0] : null;
        },
        /**
         * 初始化回源配置信息
         */
        initBackSourceConf(data) {
            // 旧逻辑共赋值4个属性
            // 新逻辑使用lodash-mergeWith函数进行合并属性，剔除null值和undefined
            this.form.origin_type = get(data, "config.origin.origin_type", 1) || 1;

            // 可能是null或者 undefined，可以直接使用merge合并
            const forward_origins = get(data, "config.origin.forward_origins");
            let templateData = [cloneDeep(forwardOriginItem)];
            // 后端返回数组，且有长度
            // 对数组进行一对一映射，防止后端存在某些数据没有返回，导致前端元素塌陷
            if (forward_origins instanceof Array && get(forward_origins, "length")) {
                templateData = forward_origins.map(() => {
                    return cloneDeep(forwardOriginItem);
                });
            }

            // 这里是我的习惯，喜欢用一个新定义对象承接。merge是将会改变原对象的引用，其实新对象就是原对象
            const info = mergeWith(templateData, forward_origins, (oldValue, newValue) => {
                // 接口值返回null且前端默认有值，返回前端默认值
                if (newValue === null || (newValue === undefined && oldValue)) return oldValue;

                // 不进行返回值处理，使用lodash-merge的默认策略
                // 如果目标值存在，被解析为undefined的sources 来源对象属性将被跳过。数组和普通对象会递归合并，其他对象和值会被直接分配覆盖。
            });

            this.form.forward_origins = info;
        },
        // 处理基础信息
        handleBaseInfo(data) {
            let inst_name = "-";
            if (data.access_mode === 2) {
                inst_name = data?.inst_name;
            }
            const baseInfo = [
                { label: "加速域名", value: data?.domain || "-" },
                { label: "实例名称", value: inst_name || "-" },
                {
                    label: this.isJinhua ? "高防CNAME" : "CNAME",
                    value: (this.isJinhua ? data?.defenseCname : data?.cname) || "-",
                },
                {
                    label: "产品类型",
                    value: this.productCodeLabel(data?.product_code || "024") || "-",
                },
                {
                    label: "加速区域",
                    value: this.areaScopeLabel(data?.area_scope) || "-",
                },
                { label: "创建时间", value: data?.insert_date || "-" },
            ];
            this.baseInfo = cloneDeep(baseInfo);
        },
        // 是否有正在进行的工单？
        getWorkOrderDetail() {
            return this.$ctFetch(WorkOrderList.workOrderList, {
                method: "GET",
                transferType: "json",
                data: {
                    account_id: this.$store.state.user.userInfo.userId,
                    domain: this.$route.query.domain,
                    status: 2,
                },
            });
        },
        editConfig() {
            if (!this.isDomainCorrect) {
                this.$message.warning("域名信息获取失败，请重新刷新页面。");
                return;
            }
            const isEdit = true;
            SecurityAbilityModule.SET_IS_EDIT(isEdit);
        },
    },
};
</script>

<style lang="scss" scoped>
.domain-edit-wrap {
    overflow: hidden;
    height: 100%; // 定高，出现滚动条
    flex: 1;
    display: flex;
    flex-direction: column;
    padding-left: 20px;
    padding-bottom: 60px;
    // 新增样式
    ::v-deep .el-scrollbar {
        margin: 0 !important; // 上下留白
        margin-bottom: -60px !important; // 用于设置底部按钮的高度
        flex: 1; // 自适应高度
    }
    ::v-deep .el-scrollbar__wrap {
        .el-scrollbar__view {
            // padding: 0 8px; // 内容区固定一个间距出来
            padding: 0 !important; // 内容区固定一个间距出来
            height: 100%;
        }
        overflow-x: hidden;
    }
    ::v-deep {
        .el-form--label-left .el-form-item__label {
            text-align: right;
        }
    }
}
.domain-edit {
    overflow: auto; // 有滚动条
    display: flex;
    flex: 1;
    background: #fff;
    flex-direction: column;
    // margin: 20px;
    // padding: 20px !important;
    .header-tip-style {
        margin: 0 20px 16px 0 !important;
    }
}
.domain-edit-content {
    flex: 1;

    ::v-deep .el-scrollbar__wrap {
        overflow-x: hidden;
    }
    .simple-create-form {
        padding-bottom: 80px;
    }
}
.label-name {
    font-weight: 500;
    width: 600px;
    overflow: hidden;
    border-left: 4px solid $theme-color;
    line-height: 14px;
    padding-left: 12px;
    margin: 16px 0;
    height: 14px;
}
.base-info-style {
    margin: 20px 20px 0 0;
}
.ct-table-style {
    padding-right: 20px !important;
    ::v-deep {
        .el-table__header {
            width: 100% !important;
        }
    }
}
// .el-table::before {
//     width: calc(100% - 20px) !important;
// }
.origin-word {
    // font-family: PingFangSC-Regular;
    font-size: 12px;
    color: $color-neutral-7;
    line-height: 18px;
    font-weight: 400;
    margin-top: $margin-2x;
    .icon-tip {
        margin-right: $margin;
        font-size: 14px;
    }
}
.icon-tip {
    font-size: 14px;
}
.form-item-tip {
    // font-family: PingFangSC-Regular;
    font-size: 12px;
    color: $color-neutral-7;
    line-height: 18px;
    font-weight: 400;
    margin-top: $margin-2x;
}
// 端口信息-样式
.tcp {
    display: flex;
    margin-bottom: 24px;
    .request {
        margin-right: 20px;
        display: flex;
        .port-label {
            width: 80px;
            // margin-right: 20px;
            position: relative;
            &.red-star::before {
                display: block;
                content: "*";
                color: $color-danger;
                position: absolute;
                left: -8px;
                top: 4px;
            }
        }
        .port-input-style {
            width: 200px;
        }
        .el-form-item {
            margin-bottom: 20px;
            ::v-deep {
                .el-form-item__error {
                    align-items: baseline;
                }
            }
        }
    }
    .icon-action {
        color: $color-master;
        font-size: $text-size-lg;
        line-height: 32px;
        margin-right: $margin-2x;
        cursor: pointer;
    }
}
.port-form-item {
    margin-bottom: 0 !important;
}
.user {
    display: block;
    ::v-deep .el-select {
        width: 200px !important;
    }
    ::v-deep .el-form-item__label {
        text-align: left;
    }
    .user_item {
        flex: 1;
    }
}
.link-style {
    display: inline-block;
    margin-left: $margin-4x !important;
}
.blackWhiteList {
    width: 40%;
}
.input-w {
    width: 60%;
}
// .port-tip-style {
//     font-family: PingFangSC, helvetica neue, arial, microsoft yahei regular, microsoft yahei;
// }
.icon-column-label {
    color: $text-color-light;
    margin-right: $margin;
    font-size: $text-size-md;
    line-height: 18px;
    height: 14px;
    width: 14px;
}
.icon-style {
    margin-right: 0 !important;
}
.submit {
    z-index: 200;
    .footer-content {
        display: flex;
        justify-content: flex-end;
        .el-button {
            margin-left: 16px !important;
        }
    }
}
</style>
