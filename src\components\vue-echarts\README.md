
# ECharts 5

## 按需加载

> https://echarts.apache.org/zh/tutorial.html#在打包环境中使用%20ECharts

```js
// echarts 核心模块（必须）
import * as echarts from "echarts/lib/echarts";
// Canvas 渲染器（必须）
import { CanvasRenderer } from "echarts/renderers";
// 引入组件，后缀都为 Component
import { ... } from "echarts/components";
// 引入图表，后缀都为 Chart
import { ... } from "echarts/charts";

// 注册必须的组件
echarts.use([ ... ]);
```

## 主题定制

```js
// 主题
import cdnEchartsTheme from "@/config/echart/cdn-theme";

// 注册 ecahrt 主题
echarts.registerTheme("cdn", cdnEchartsTheme);
```

# vue-echarts

> fork 自 https://github.com/ecomfe/vue-echarts 。由于原包不支持 ECharts 5 ，因此 fork 后根据项目实际需求进行修改。

其他的配置已经处理，下面只对使用进行说明：

> 源文档： https://github.com/ecomfe/vue-echarts/blob/master/README.zh_CN.md#props-均为响应式

### Props *（均为响应式）*

* `initOptions`

  用来初始化 ECharts 实例。

* `theme`

  当前 ECharts 实例使用的主题。

* `options`

  ECharts 实例的数据。修改这个 prop 会触发 ECharts 实例的 `setOption` 方法。

  如果直接修改 `options` 绑定的数据而对象引用保持不变，`setOption` 方法调用时将带有参数 `notMerge: false`。否则，如果为 `options` 绑定一个新的对象，`setOption` 方法调用时则将带有参数 `notMerge: true`。

  例如，如果有如下模板：

  ```html
  <v-chart :options="data"/>
  ```

  那么：

  ```js
  this.data = newObject // setOption(this.options, true)
  this.data.title.text = 'Trends' // setOption(this.options, false)
  ```

* `group`

  实例的分组，会自动绑定到 ECharts 组件的同名属性上。

* `autoresize` （默认值：`false`）

  这个 prop 用来指定 ECharts 实例在组件根元素尺寸变化时是否需要自动进行重绘。

* `manual-update` （默认值：`false`）

  在性能敏感（数据量很大）的场景下，我们最好对于 `options` prop 绕过 Vue 的响应式系统。当将 `manual-update` prop 指定为 `true` 且不传入 `options` prop 时，数据将不会被监听。然后，你需要用 `ref` 获取组件实例以后手动调用 `mergeOptions` 方法来更新图表。

### 计算属性

* `width` **[只读]**

  用来获取 ECharts 实例的当前宽度。

* `height` **[只读]**

  用来获取 ECharts 实例的当前高度。

* `computedOptions` **[只读]**

  用来读取 ECharts 更新内部 `options` 后的实际数据。

### 方法

* `mergeOptions`（底层调用了 ECharts 实例的 `setOption` 方法）

  *提供了一个更贴切的名称来描述 `setOption` 方法的实际行为。*

* `appendData`
* `resize`
* `dispatchAction`
* `showLoading`
* `hideLoading`
* `convertToPixel`
* `convertFromPixel`
* `containPixel`
* `getDataURL`
* `getConnectedDataURL`
* `clear`
* `dispose`

### 静态方法

* `connect`
* `disconnect`
* `registerMap`
* `registerTheme`
* `graphic.clipPointsByRect`
* `graphic.clipRectByRect`

### 事件

Vue-ECharts 支持如下事件：

* `legendselectchanged`
* `legendselected`
* `legendunselected`
* `legendscroll`
* `datazoom`
* `datarangeselected`
* `timelinechanged`
* `timelineplaychanged`
* `restore`
* `dataviewchanged`
* `magictypechanged`
* `geoselectchanged`
* `geoselected`
* `geounselected`
* `pieselectchanged`
* `pieselected`
* `pieunselected`
* `mapselectchanged`
* `mapselected`
* `mapunselected`
* `axisareaselected`
* `focusnodeadjacency`
* `unfocusnodeadjacency`
* `brush`
* `brushselected`
* `rendered`
* `finished`
* 鼠标事件
  * `click`
  * `dblclick`
  * `mouseover`
  * `mouseout`
  * `mousemove`
  * `mousedown`
  * `mouseup`
  * `globalout`
  * `contextmenu`
* ZRender 事件 *(v4.1.0 新增)*
  * `click`
  * `mousedown`
  * `mouseup`
  * `mousewheel`
  * `dblclick`
  * `contextmenu`

更多详细信息请参考 [ECharts 的 API 文档](https://echarts.apache.org/zh/api.html)。
