<template>
    <div class="ct-edit-wrapper">
        <el-form
            label-width="140px"
            label-position="left"
            :model="form"
            :rules="rules"
            ref="cacheKeyUriForm"
            :disabled="!isEdit || !isService || isLockCacheKeyUri"
        >
            <div v-if="isNewEcgw">
              <el-form-item
                :label="$t('domain.detail.label13')"
                prop="cachekey_uri"
                ref="cachekeyUri"
                class="ct-table-form-item code-style"
                ><span slot="label"
                    >{{ $t("domain.detail.label13") }}
                    <span>
                        <el-tooltip placement="top">
                            <div slot="content">
                                {{ $t("domain.detail.tip22") }}
                                <a
                                    :underline="false"
                                    class="word-wrap aocdn-ignore-link"
                                    style="color:#3d73f5"
                                    @click="$docHelp(cache_uri_url)"
                                    >{{ $t("domain.detail.tip23") }}</a
                                >
                            </div>
                            <ct-svg-icon
                                icon-class="question-circle"
                                class-name="ct-sort-drag-icon"
                            ></ct-svg-icon></el-tooltip></span
                ></span>
                <div class="ct-table-wrapper">
                    <el-table class="origin-table auto-table" :data="form.cachekey_uri">
                        <el-table-column prop="pattern" :label="$t('domain.detail.label49')">
                            <template slot="header">
                                <div class="flex-row-style">
                                    <span>{{ $t("domain.detail.label49") }}</span>
                                    <el-tooltip
                                        class="item"
                                        style="margin-left:4px"
                                        effect="dark"
                                        :content="$t('domain.detail.tip24')"
                                        placement="top"
                                    >
                                        <i class="el-icon-question" />
                                    </el-tooltip>
                                </div>
                            </template>
                            <template slot-scope="scope">
                                <el-form-item
                                    label=""
                                    :prop="`cachekey_uri.` + scope.$index + `.pattern`"
                                    :rules="rules.cachekey_uri_pattern"
                                >
                                    <el-input
                                        v-model.trim="scope.row.pattern"
                                        placeholder=""
                                        class="input-style"
                                        @change="handleChange"
                                    ></el-input>
                                </el-form-item>
                            </template>
                        </el-table-column>
                        <el-table-column prop="replace" :label="$t('domain.detail.label50')">
                            <template slot="header">
                                <div class="flex-row-style">
                                    <span>{{ $t("domain.detail.label50") }}</span>
                                    <el-tooltip
                                        class="item"
                                        style="margin-left:4px"
                                        effect="dark"
                                        :content="$t('domain.detail.tip25')"
                                        placement="top"
                                    >
                                        <i class="el-icon-question" />
                                    </el-tooltip>
                                </div>
                            </template>
                            <template slot-scope="scope">
                                <el-form-item
                                    label=""
                                    :prop="`cachekey_uri.` + scope.$index + `.replace`"
                                >
                                    <div class="flex-row-style combination-box">
                                        <el-input
                                            v-model.trim="scope.row.replace"
                                            class="input-box"
                                            placeholder=""
                                            @change="handleChange"
                                        ></el-input>
                                    </div>
                                </el-form-item>
                            </template>
                        </el-table-column>
                        <el-table-column :label="$t('domain.operate')" width="80">
                            <template slot-scope="scope">
                                <el-button
                                    type="text"
                                    :disabled="!isEdit || !isService || isLockCacheKeyUri"
                                    @click="
                                        onOperator(
                                            scope.row,
                                            'delete',
                                            'cachekey_uri',
                                            scope.$index
                                        )
                                    "
                                    >{{ $t("domain.delete") }}</el-button
                                >
                            </template>
                        </el-table-column>
                    </el-table>
                    <div class="flex-row-style button-box">
                        <el-button
                            class="btn"
                            type="text"
                            :disabled="!isEdit || !isService || isLockCacheKeyUri"
                            @click="onOperator(null, 'create', 'cachekey_uri')"
                        >
                            + {{ addButtonText }}
                        </el-button>
                    </div>
                </div>
              </el-form-item>
            </div>

        </el-form>
    </div>
</template>

<script>
import ctSvgIcon from "@/components/ctSvgIcon";
import componentMixin from "@/views/domainConfig/componentMixin";
import i18n from "@/i18n/index";
import { cloneDeep } from "lodash-es";
import { nUserModule } from "@/store/modules/nuser";
import urlTransformer from "@/utils/logic/url";

export default {
    name: "cacheKeyUri",
    components: {
        ctSvgIcon,
    },
    mixins: [componentMixin],
    props: {
        datas: Object,
        isLockCacheKeyUri: Boolean,
        isStaticsAbilityOn: Boolean,
        isNewEcgw: Boolean,
    },
    data() {
        return {
            addButtonText: i18n.t("domain.editPage.label10"),
            currentType: "create",
            form: {
                cachekey_uri: [], // 缓存URI
            },
            // cache_uri_url: "https://www.ctyun.cn/document/10065985/10197800",
            rules: {
              cachekey_uri_pattern: [
                  { required: true, validator: this.validCacheKeyUriPattern, trigger: "blur" },
              ],
            },
        };
    },
    computed: {
        isCtclouds() {
            return nUserModule.isCtclouds;
        },
        cache_uri_url() {
            return urlTransformer(
                {
                    fcdnCtcloud: "https://www.esurfingcloud.com/document/zh-cn/10015932/20688153",
                    fcdnCtyun: "https://www.ctyun.cn/document/10015932/10639709",
                    a1Ctyun: "https://www.ctyun.cn/document/10065985/10197800",
                    a1Ctcloud: "https://www.esurfingcloud.com/document/zh-cn/20689737/20689872"
                }
            )
        },
    },
    watch: {
        "datas.cachekey_uri": {
            deep: true,
            handler(val) {
                this.init(val);
            },
            immediate: true,
        },
    },
    methods: {
      init(v) {
          this.form.cachekey_uri = cloneDeep(v);
      },
      handleChange() {
        this.$emit("onChange", this.form.cachekey_uri);
      },
      async onOperator(row, currentType, tabName, i) {
        this.currentType = currentType;
        const getTime = new Date().getTime();
        if (currentType === "create") {
            const defaultFormMap = {
                cachekey_uri: { pattern: "", replace: "", id: `cachekey_uri_${getTime}` },
            };
            row = defaultFormMap[tabName];
        }
        if (currentType === "delete") {
            const msgMap = {
                cachekey_uri: this.$t("domain.editPage.tip20"),
            };

            // 二次确认弹窗
            await this.$confirm(msgMap[tabName], this.$t("domain.delete"), {
                type: "warning",
            });
            this.form[tabName].splice(i, 1);
            this.$emit("onChange", this.form.cachekey_uri);
        } else {
            this.form[tabName].push(row);
        }
      },
      async validCacheKeyUriPattern(rule, value, callback) {
        if (this.isLockCacheKeyUri) return callback();
        if ((value === "" || value === null || value === undefined) && this.isStaticsAbilityOn) {
            return callback(new Error(this.$t("domain.detail.placeholder6")));
        }
        return callback();
      },
    },
};
</script>

<style lang="scss" scoped>
@import "@/views/domainConfig/commonStyle.scss";
.ct-edit-wrapper {
    ::v-deep {
        .el-form--label-left .el-form-item__label {
            text-align: right;
        }
    }
}
</style>
