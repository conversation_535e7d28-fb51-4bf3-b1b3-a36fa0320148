<template>
    <section class="domain-edit-wrap">
        <el-scrollbar>
            <section class="domain-edit" v-loading="initLoading">
                <template v-if="!isService && !isCreating">
                    <el-alert
                        class="header-tip-style"
                        type="info"
                        show-icon
                        title="域名当前配置中/已停用，不支持配置能力。域名状态为已启用方可编辑"
                        :closable="false"
                    />
                </template>
                <template v-if="isCreating">
                    <el-alert
                        class="header-tip-style"
                        type="info"
                        show-icon
                        title="当前域名/实例正在新增中，页面展示初始化内容，请耐心等待5~10分钟"
                        :closable="false"
                    />
                </template>
                <div class="domain-edit-content">
                    <el-form
                        class="simple-create-form"
                        name="form"
                        ref="form"
                        :model="form"
                        label-width="130px"
                        label-position="left"
                        :disabled="!isService || !isEdit"
                    >
                        <div>
                            <!-- <p class="label-name">基础信息</p> -->
                            <cute-titled-block title="基础信息">
                                <template #content>
                                    <basic-info :baseInfo="baseInfo" class="base-info-style" />
                                </template>
                            </cute-titled-block>
                            <!-- <p class="label-name">回源配置</p> -->
                            <cute-titled-block title="回源配置">
                                <template #content>
                                    <el-form-item
                                        label="源站"
                                        prop="originTableData"
                                        :rules="rules.originAll"
                                    >
                                        <div class="add-row">
                                            <el-button
                                                type="primary"
                                                @click="addDomain"
                                                :disabled="
                                                    (form.originTableData &&
                                                        form.originTableData.length >= 60) ||
                                                        !isService ||
                                                        !isEdit
                                                "
                                            >+ 添加</el-button
                                            >
                                            <div class="origin-word">
                                                <span>
                                                    <ct-svg-icon
                                                        icon-class="info-circle"
                                                        class-name="icon-column-label"
                                                    /> </span
                                                >支持IP或域名, 最多可添加60个
                                            </div>
                                        </div>
                                        <div class="ct-table-style">
                                            <el-table :data="form.originTableData">
                                                <el-table-column type="index" label="序号" width="60">
                                                </el-table-column>
                                                <el-table-column prop="address" label="源站">
                                                </el-table-column>
                                                <el-table-column label="角色">
                                                    <template slot-scope="scope">
                                                        <span>{{
                                                                scope.row.role === 1 ? "主源" : "备源"
                                                            }}</span>
                                                    </template>
                                                </el-table-column>
                                                <el-table-column prop="level" label="层级"> </el-table-column>
                                                <el-table-column prop="weight" label="权重">
                                                </el-table-column>
                                                <el-table-column label="操作" width="180">
                                                    <template slot-scope="scope">
                                                        <el-button
                                                            @click="operateEdit(scope.row, scope.$index)"
                                                            type="text"
                                                            size="small"
                                                        >修改
                                                        </el-button>
                                                        <el-button
                                                            @click="operateDel(scope.row, scope.$index)"
                                                            type="text"
                                                            size="small"
                                                        >删除
                                                        </el-button>
                                                    </template>
                                                </el-table-column>
                                            </el-table>
                                        </div>
                                    </el-form-item>
                                    <el-form-item
                                        label="回源策略"
                                        prop="origin_type"
                                        :rules="rules.origin_type"
                                    >
                                        <el-radio-group
                                            v-model="form.origin_type"
                                            @change="handleRadioChange"
                                        >
                                            <el-radio-button :label="1">择优回源</el-radio-button>
                                            <el-radio-button :label="2">按权重回源</el-radio-button>
                                            <el-radio-button :label="3">保持登录</el-radio-button>
                                        </el-radio-group>
                                        <div class="form-item-tip">
                                            <span>
                                                <ct-svg-icon
                                                    icon-class="info-circle"
                                                    class-name="icon-column-label icon-style"
                                                />
                                            </span>
                                            选择“择优回源”时，优先回最快的源站，忽略权重；选择“按权重回源”时，按照配置的权重回源；选择“保持登录”时则基于客户端IP哈希回源。
                                        </div>
                                    </el-form-item>
                                    <el-form-item label="端口信息" required class="port-form-item">
                                        <div class="tcp" v-for="(item, index) in form.tcp_ports" :key="index">
                                            <div class="request">
                                                <span class="port-label">TCP请求端口</span>
                                                <el-form-item
                                                    label-width="0"
                                                    :prop="`tcp_ports.${index}.req`"
                                                    :rules="rules.req"
                                                >
                                                    <el-input
                                                        v-model="item.req"
                                                        class="port-input-style"
                                                    ></el-input>
                                                </el-form-item>
                                            </div>
                                            <div class="request">
                                                <span class="port-label">TCP回源端口</span>
                                                <el-form-item
                                                    label-width="0"
                                                    :prop="`tcp_ports.${index}.origin`"
                                                    :rules="rules.tcp_origin_port"
                                                >
                                                    <el-input
                                                        v-model="item.origin"
                                                        class="port-input-style"
                                                    ></el-input>
                                                </el-form-item>
                                            </div>
                                            <div>
                                                <el-col>
                                                    <el-form-item label>
                                                        <!-- <i
                                                        class="icon-action el-icon-circle-plus-outline"
                                                        @click="tcpAddLine()"
                                                    />
                                                    <i
                                                        class="icon-action el-icon-remove-outline"
                                                        @click="tcpDelete(index)"
                                                    /> -->
                                                        <el-button
                                                            type="primary"
                                                            plain
                                                            icon="el-icon-plus"
                                                            circle
                                                            @click="tcpAddLine()"
                                                            :disabled="!isService || !isEdit"
                                                            style="padding:1px"
                                                        ></el-button>
                                                        <el-button
                                                            v-if="tcpDeleteButtonHide"
                                                            type="primary"
                                                            plain
                                                            icon="el-icon-minus"
                                                            circle
                                                            @click="tcpDelete(index)"
                                                            :disabled="!isService || !isEdit"
                                                            style="padding:1px"
                                                        ></el-button>
                                                    </el-form-item>
                                                </el-col>
                                            </div>
                                        </div>
                                    </el-form-item>
                                    <el-form-item>
                                        <div
                                            class="tcp"
                                            v-for="(item, index) in form.udp_ports"
                                            :key="'item' + index"
                                        >
                                            <div class="request">
                                                <span class="port-label">UDP请求端口</span>
                                                <el-form-item
                                                    label-width="0"
                                                    :prop="`udp_ports.${index}.req`"
                                                    :rules="rules.udp_req_port"
                                                >
                                                    <el-input
                                                        v-model="item.req"
                                                        class="port-input-style"
                                                        @input="handleUdpChange(item.req, index)"
                                                    ></el-input>
                                                </el-form-item>
                                            </div>
                                            <div class="request">
                                                <span class="port-label">UDP回源端口</span>
                                                <el-form-item
                                                    label-width="0"
                                                    :prop="`udp_ports.${index}.origin`"
                                                    :rules="rules.udp_origin_port"
                                                >
                                                    <el-input
                                                        v-model="item.origin"
                                                        class="port-input-style"
                                                        :disabled="!item.req"
                                                    ></el-input>
                                                </el-form-item>
                                            </div>
                                            <div>
                                                <el-col>
                                                    <el-form-item label>
                                                        <!-- <i
                                                        class="icon-action el-icon-circle-plus-outline"
                                                        @click="udpAddLine()"
                                                    />
                                                    <i
                                                        class="icon-action el-icon-remove-outline"
                                                        @click="udpDelete(index)"
                                                    /> -->
                                                        <el-button
                                                            type="primary"
                                                            plain
                                                            icon="el-icon-plus"
                                                            circle
                                                            @click="udpAddLine()"
                                                            :disabled="!isService || !isEdit"
                                                            style="padding:1px"
                                                        ></el-button>
                                                        <el-button
                                                            v-if="udpDeleteButtonHide"
                                                            type="primary"
                                                            plain
                                                            icon="el-icon-minus"
                                                            circle
                                                            @click="udpDelete(index)"
                                                            :disabled="!isService || !isEdit"
                                                            style="padding:1px"
                                                        ></el-button>
                                                    </el-form-item>
                                                </el-col>
                                            </div>
                                        </div>
                                        <div class="form-item-tip">
                                            <div>
                                                <span>
                                                    <ct-svg-icon
                                                        icon-class="info-circle"
                                                        class-name="icon-column-label icon-style"
                                                    />
                                                </span>
                                                提示:请求端口支持配置多个端口，不连续的端口间使用逗号分隔，连续的端口间使用“-”分隔，如：100,1000-2000,2050。
                                                <div>
                                                    回源端口每行仅支持配置一个，如需配置多个请点击"+"再添加一行进行配置。
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-item-tip" v-if="tcpPortLimit || udpPortLimit">
                                            <div>
                                                <span>
                                                    <ct-svg-icon
                                                        icon-class="info-circle"
                                                        class-name="icon-column-label icon-style"
                                                    />
                                                </span>
                                                以下端口为平台占用端口，不允许配置如下请求端口:
                                                <div class="port-tip-style">
                                                    <div>TCP: {{ tcpPortLimit }}</div>
                                                    <div>UDP: {{ udpPortLimit }}</div>
                                                </div>
                                            </div>
                                        </div>
                                    </el-form-item>
                                </template>
                            </cute-titled-block>
                            <!-- <p class="label-name">访问控制</p> -->
                            <cute-titled-block v-if="!isJinhua" title="访问控制">
                                <template #content>
                                    <el-form-item label="IP黑白名单">
                                        <el-switch
                                            v-model="form.black_white_list_switch"
                                            @change="black_white_list_switch_change"
                                        ></el-switch>
                                    </el-form-item>
                                    <div v-show="form.black_white_list_switch">
                                        <el-form-item label="类型">
                                            <el-radio-group v-model="form.blackWhiteListType">
                                                <el-radio :label="2">白名单</el-radio>
                                                <el-radio :label="1">黑名单</el-radio>
                                            </el-radio-group>
                                        </el-form-item>
                                        <el-form-item prop="ip_list" :rules="rules.ip_list">
                                            <el-input
                                                type="textarea"
                                                v-model="form.blackWhiteListContent"
                                                rows="5"
                                                class="blackWhiteList"
                                            ></el-input>
                                            <div class="form-item-tip">
                                                <span>
                                                    <ct-svg-icon
                                                        icon-class="info-circle"
                                                        class-name="icon-column-label icon-style"
                                                    />
                                                </span>
                                                通过黑/白名单来对访问者身份进行识别和过滤，支持IPv6地址填写。
                                            </div>
                                        </el-form-item>
                                    </div>
                                    <!-- 支持区域访问控制 -->
                                    <access-region-control
                                        ref="accessRegionControlRef"
                                        :defaultValue="form.region_access_control"
                                        @change="val => form.region_access_control = val"
                                        :disabled="!isService || !isEdit"
                                    />
                                </template>
                            </cute-titled-block>
                            <!-- <p class="label-name">传递用户IP回源</p> -->
                            <cute-titled-block title="传递用户IP回源">
                                <template #content>
                                    <el-form-item label="传递用户IP回源">
                                        <el-switch v-model="form.user_ip.switch"></el-switch>
                                    </el-form-item>
                                    <div class="user" v-show="form.user_ip.switch">
                                        <div class="user_item">
                                            <el-form-item label-width="150px" label="传递用户IP回源方式">
                                                <el-select
                                                    v-model="form.user_ip.proxy_protocol"
                                                    placeholder="请选择"
                                                >
                                                    <el-option label="tcp_option" :value="2"></el-option>
                                                    <el-option label="proxy_protocol" :value="1"></el-option>
                                                </el-select>
                                                <span
                                                    v-show="form.user_ip.proxy_protocol == 2"
                                                    class="user_item"
                                                >
                                                    <el-link
                                                        class="link-style"
                                                        type="primary"
                                                        @click="$docHelp(toaDownload)"
                                                    >toa模块下载</el-link
                                                    >
                                                </span>
                                                <div
                                                    v-show="form.user_ip.proxy_protocol == 2"
                                                    class="form-item-tip"
                                                >
                                                    <span>
                                                        <ct-svg-icon
                                                            icon-class="info-circle"
                                                            class-name="icon-column-label"
                                                        /> </span
                                                    >提示:使用tcp_option传递用户IP回源时,您需要下载适配源站系统版本的toa模块,并安装到源站后才可正常使用该功能。
                                                </div>
                                            </el-form-item>
                                        </div>
                                        <div class="user_item" v-show="form.user_ip.proxy_protocol === 1">
                                            <el-form-item
                                                label-width="150px"
                                                prop="proxy_protocol_version"
                                                :rules="rules.proxy_protocol_version"
                                                label="proxy_protocol版本"
                                            >
                                                <el-select
                                                    v-model="form.user_ip.proxy_protocol_version"
                                                    placeholder="请选择"
                                                >
                                                    <el-option label="v1" :value="1"></el-option>
                                                    <el-option label="v2" :value="2"></el-option>
                                                </el-select>
                                                <div class="form-item-tip">
                                                    <!-- <span> <i class="el-icon-warning-outline icon-tip"></i> </span> -->
                                                    <span>
                                                        <ct-svg-icon
                                                            icon-class="info-circle"
                                                            class-name="icon-column-label icon-style"
                                                        />
                                                    </span>
                                                    请根据源站支持的proxy_protocol协议版本选择,v1版本仅支持tcp协议,
                                                    v2版本同时支持tcp和udp。
                                                    <div>
                                                        <span style="margin-left:17px;"></span>选择proxy_protocol协议时，必须确保源站也同时开启proxy_protocol协议；关闭时，源站也必须同时关闭。否则，业务会受到影响。
                                                    </div>
                                                </div>
                                            </el-form-item>
                                        </div>
                                    </div>
                                </template>
                            </cute-titled-block>
                        </div>
                    </el-form>
                </div>
            </section>
            <cute-fixed-footer class="submit">
                <div class="footer-content">
                    <el-tooltip
                        v-if="!isService"
                        class="item"
                        effect="dark"
                        content="域名配置中/已停用，不支持进行编辑 "
                        placement="top"
                    >
                        <el-button
                            type="primary"
                            @click="editConfig"
                            size="medium"
                            v-if="!isEdit && !isService"
                            :disabled="!isService"
                        >
                            编辑配置
                        </el-button>
                    </el-tooltip>
                    <el-button type="primary" @click="editConfig" size="medium" v-if="!isEdit && isService">
                        编辑配置
                    </el-button>
                    <el-button plain size="medium" @click="handleCancel" v-if="isEdit">
                        取 消
                    </el-button>
                    <el-button
                        type="primary"
                        :loading="submitLoading"
                        size="medium"
                        @click="handleSubmit(null, null)"
                        v-if="isEdit"
                    >
                        保 存
                    </el-button>
                </div>
            </cute-fixed-footer>
        </el-scrollbar>
        <el-dialog
            :title="dialogStatus"
            :visible.sync="addDialog"
            v-if="addDialog"
            :close-on-click-modal="false"
            width="42%"
        >
            <el-form :model="originItem" :rules="originItemRules" ref="originItemForm" label-width="100px">
                <!-- IP或域名校验 -->
                <el-form-item label="源站" prop="address" :rules="rules.address">
                    <el-input v-model="originItem.address" class="input-w"></el-input>
                </el-form-item>
                <el-form-item label="角色" prop="role" :rules="rules.role">
                    <el-select v-model="originItem.role" @change="roleChange" class="input-w">
                        <el-option label="主源" :value="1"></el-option>
                        <el-option label="备源" :value="2"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="层级" prop="level" :rules="rules.level">
                    <el-select v-model="originItem.level" placeholder="请选择" class="input-w">
                        <el-option
                            v-for="item in originItem.role === 1 ? levelList : levelList5"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="权重" prop="weight">
                    <el-input
                        v-model.number="originItem.weight"
                        :disabled="weightDisabled"
                        class="input-w"
                    ></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="addDialog = false">取 消</el-button>
                <el-button type="primary" @click="addConfirm">确 定</el-button>
            </div>
        </el-dialog>
    </section>
</template>

<script>
import { ip } from "@cdnplus/common/config/pattern";
import basicInfo from "@/views/domainConfig/basicInfo";
import { DomainUrl } from "@/config/url/ipa/domain";
import { WorkOrderList } from "@/config/url/ipa/work_order_list";
import { SecurityAbilityModule } from "@/store/modules/securityAbility";
import urlTransformer from "@/utils/logic/url";
import AccessRegionControl from '../components/access-region-control/index.vue';
import { defaultAccessRegionControlForm, defaultAccessRegionControlListItem, parseAccessRegionControlList } from '../components/access-region-control/util';

// IP或域名校验
const ipRegexp = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/;
const domainRegxp = /^(\*\.|\*)?([a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,6}$/;
import ipRegex from "@/views/ipa/domain/components/ip-regex.js";
import { get, cloneDeep } from "lodash-es";
import commonMixin from "@/views/ipa/domain/commonMixin";
import ctSvgIcon from "@/components/ctSvgIcon";

export default {
    components: {
        basicInfo,
        ctSvgIcon,
        AccessRegionControl,
        // domainPanel,
    },
    mixins: [commonMixin],
    props: {
        defaultData: Object,
        from: String,
    },
    data() {
        return {
            isOpen: true,
            isAddDomain: false, // 是否为添加域名模式
            // isView: false,
            initLoading: false,
            submitLoading: false,
            temp_black_white_list_type: 1,
            temp_black_white_list_content: "",
            form: {
                originTableData: [],
                udp_ports: [
                    {
                        origin: "",
                        req: "",
                    },
                ],
                tcp_ports: [
                    {
                        origin: "",
                        req: "",
                    },
                ],
                origin_type: "1", // 回源策略
                blackWhiteListType: 1, //改
                blackWhiteListContent: "",
                user_ip: {
                    switch: false,
                    proxy_protocol: 1,
                    proxy_protocol_version: 1,
                },
                black_white_list_switch: false,
                region_access_control: defaultAccessRegionControlForm(),
            },
            addDialog: false, // 源站-弹窗
            dialogStatus: "",
            originItem: {
                address: "",
                role: 1,
                level: 1,
                weight: 10,
            },
            // ipv6_enable: false,
            // ipv6_enable_cache: false,
            // accelerateDomain: "",
            // accelerateRange: "",
            result: {
                id: "",
                account_id: "",
                area_scope: "",
                biz_tag_list: [],
                action: 5,
                config: {
                    origin: {
                        detail: [],
                        origin_type: "1",
                        probe_port: "",
                        tcp_ports: [],
                        udp_ports: [],
                    },
                    access_control: {
                        control_switch: "",
                        control_type: "",
                        ip_list: "",
                        match_type: "",
                    },
                    dynamic_cfg: {
                        kcp: "",
                        package_loss: "",
                        route_type: "",
                    },
                    user_ip: {
                        proxy_protocol: null,
                        tcp_option: null,
                        proxy_protocol_version: null,
                    },
                },
                domain: "www.wfq1115h.ele.me",
                domain_id: "ed7c3fcfe6cf71d2f12dac23014412c6",
                domain_status: 2,
                domain_type: 1,
                global_resource_pool_id: 1509846,
                operator: "work_sheet",
                parent_domain_draft: 11,
                partner: "001",
                predeploy_resource_pool_id: 1509741,
                product: 9,
                version: "",
            },
            baseInfo: [
                { label: "加速域名", value: "-" },
                { label: "实例名称", value: "-" },
                { label: this.isJinhua ? "高防CNAME" : "CNAME", value: "-" },
                { label: "产品类型", value: "-" },
                { label: "加速区域", value: "-" },
                { label: "创建时间", value: "-" },
            ],
            areaScopeLabel(area_scope) {
                let label = "";
                switch (area_scope) {
                    case 1:
                        label = "中国内地";
                        break;
                    case 2:
                        label = "全球（不含中国内地）";
                        break;
                    case 3:
                        label = "全球";
                        break;
                }
                return label;
            },
            productCodeLabel(productCode) {
                let label = "";
                switch (productCode) {
                    case "001":
                        label = "静态加速(中国内地)";
                        break;
                    case "003":
                        label = "下载加速(中国内地)";
                        break;
                    case "004":
                        label = "视频点播加速(中国内地)";
                        break;
                    case "005":
                        label = "视频直播(中国内地)";
                        break;
                    case "006":
                        label = "全站加速(中国内地)";
                        break;
                    case "007":
                        label = "安全加速";
                        break;
                    case "008":
                        label = "CDN加速(中国内地)";
                        break;
                    case "009":
                        label = "应用加速(中国内地)";
                        break;
                    case "010":
                        label = "Web应用防火墙(边缘云版)";
                        break;
                    case "011":
                        label = "DDoS高防(边缘云版)";
                        break;
                    case "012":
                        label = "smart dns";
                        break;
                    case "013":
                        label = "网站安全监测";
                        break;
                    case "014":
                        label = "下载加速(闲时)(中国内地)";
                        break;
                    case "015":
                        label = "极速直播(中国内地)";
                        break;
                    case "016":
                        label = "分布式安全云平台";
                        break;
                    case "017":
                        label = "容器安全平台";
                        break;
                    case "018":
                        label = "爬虫管理";
                        break;
                    case "019":
                        label = "GTM";
                        break;
                    case "020":
                        label = "边缘安全与加速";
                        break;
                    case "021":
                        label = "零信任服务";
                        break;
                    case "022":
                        label = "重保产品";
                        break;
                    case "024":
                        label = "AOne 边缘接入服务";
                        // label = this.isJinhua ? "AOne 边缘接入服务" : "AccessOne 边缘接入服务";
                        break;
                }
                return label;
            },
            rules: {
                req: [
                    {
                        validator: (rule, value, callback) => {
                            if (value) {
                                const reg = /^[0-9,-]*$/;
                                if (reg.test(value)) {
                                    const arr = value.split(/[,-]/);
                                    let flag = true;
                                    let count = 0;
                                    arr.map(item => {
                                        if (Number(item) < 1 || Number(item) > 65535) {
                                            flag = false;
                                        }
                                        if (!item) {
                                            flag = false;
                                        }
                                        if (["80","8080","443","8443"].includes(item)) {
                                            count++
                                        }
                                    });
                                    if (this.access_mode === 2 && count > 0) {
                                        callback(new Error(`接入方式为无域名接入时，TCP请求端口不支持80、8080、443、8443端口`));
                                    }else if (flag) {
                                        callback();
                                    } else {
                                        callback(new Error(`请求端口输入有误`));
                                    }
                                } else {
                                    callback(new Error(`请求端口输入有误`));
                                }
                            } else {
                                callback();
                            }
                        },
                    },
                    { validator: this.validateTcpPort },
                ],
                tcp_origin_port: [
                    {
                        validator: (rule, value, callback) => {
                            if (value) {
                                if (Number(value) >= 1 && Number(value) < 65536) {
                                    callback();
                                } else {
                                    callback(new Error(`回源端口输入有误`));
                                }
                            } else {
                                callback();
                            }
                        },
                    },
                ],
                udp_req_port: [
                    {
                        validator: (rule, value, callback) => {
                            if (value) {
                                const reg = /^[0-9,-]*$/;
                                if (reg.test(value)) {
                                    const arr = value.split(/[,-]/);
                                    let flag = true;
                                    arr.map(item => {
                                        if (Number(item) < 1 || Number(item) > 65535) {
                                            flag = false;
                                        }
                                        if (!item) {
                                            flag = false;
                                        }
                                    });
                                    if (flag) {
                                        callback();
                                    } else {
                                        callback(new Error(`请求端口输入有误`));
                                    }
                                } else {
                                    callback(new Error(`请求端口输入有误`));
                                }
                            } else {
                                callback();
                            }
                        },
                    },
                    { validator: this.validateUdpPort },
                ],
                udp_origin_port: [
                    {
                        validator: (rule, value, callback) => {
                            if (value) {
                                if (Number(value) >= 1 && Number(value) < 65536) {
                                    callback();
                                } else {
                                    callback(new Error(`回源端口输入有误`));
                                }
                            } else {
                                callback();
                            }
                        },
                    },
                ],
                // 访问控制？黑白名单
                ip_list: [
                    {
                        validator: (rule, value, callback) => {
                            value = this.form.blackWhiteListContent;
                            if (value) {
                                const reg = /^[a-zA-Z0-9,-./:\s]*$/;
                                if (reg.test(value)) {
                                    callback();
                                } else {
                                    callback(new Error(`格式有误，请检查`));
                                }
                            } else {
                                callback();
                            }
                        },
                    },
                    {
                        validator: this.checkIpList,
                        trigger: "blur",
                    },
                ],
                proxy_protocol_version: [
                    {
                        validator: this.validatorVersion,
                        trigger: "change",
                    },
                ],
                originAll: [{ required: true, validator: this.validateOriginAll, trigger: "blur, change" }],
                origin_type: [{ required: true, message: "请选择回源策略", trigger: "blur, change" }],
            },
            originItemRules: {
                // IP或域名校验
                address: [
                    { required: true, message: "请输入源站", trigger: "blur" },
                    {
                        validator: (rule, value, callback) => {
                            if (value) {
                                const isOringin =
                                    ipRegexp.test(value) ||
                                    domainRegxp.test(value) ||
                                    ipRegex.v6({ exact: true }).test(value);
                                if (!isOringin) {
                                    callback(new Error(`请输入正确的ip或域名`));
                                } else {
                                    const dataSame = this.form.originTableData.filter(
                                        item => item.address === value
                                    );
                                    const maxlen = this.dialogStatus === "新增源站" ? 0 : 1;
                                    if (dataSame.length > maxlen) {
                                        callback(new Error(`源站录入重复`));
                                    } else {
                                        callback();
                                    }
                                }
                            } else {
                                callback(new Error(`请输入源站`));
                            }
                        },
                    },
                ],
                // 角色
                role: [{ required: true, message: "请选择角色", trigger: "change" }],
                // 层级
                level: [{ required: true, message: "请选择层级", trigger: "change" }],
                // 权重校验
                weight: [
                    { required: true, message: "请输入权重", trigger: "blur" },
                    {
                        validator: (rule, value, callback) => {
                            if (value || value === 0) {
                                if (value < 1 || value > 100) {
                                    callback(new Error(`权重输入在1-100`));
                                } else {
                                    const ex = /^\d+$/;
                                    if (ex.test(value)) {
                                        callback();
                                    } else {
                                        callback(new Error(`权重输入只能为整数`));
                                    }
                                }
                            } else {
                                callback(new Error(`请输入权重`));
                            }
                        },
                    },
                ],
            },
        };
    },
    computed: {
        isJinhua() {
            // TODO 0619
            return SecurityAbilityModule.serviceIdentifier === "jinhua";
        },
        currentModule() {
            return "cdnIpaDomainEdit";
        },
        /**
         * 权重禁用
         */
        weightDisabled() {
            const val = get(this.form, "origin_type");
            if (Number(val) === 1 || Number(val) === 3) {
                return true;
            }

            return false;
        },
        securityDomain() {
            return SecurityAbilityModule.ipaSecurityDomain;
        },
        securityBasicDomainInfo() {
            return SecurityAbilityModule.securityBasicDomainInfo;
        },
        isEdit() {
            return SecurityAbilityModule.isEdit;
        },
        // 域名是否启用中
        isService() {
            const status = get(SecurityAbilityModule.securityBasicDomainInfo, "status");
            return status === 4;
        },
        isCreating() {
            return get(this.result, "creating");
        },
        isIpaSecurityFormSame() {
            return SecurityAbilityModule.isIpaSecurityFormSame;
        },
        tcpPortLimit() {
            const data = this.portCheckRules?.port_tcp_checker?.join(",") || "";
            return data;
        },
        udpPortLimit() {
            const data = this.portCheckRules?.port_udp_checker?.join(",") || "";
            return data;
        },
        isDomainCorrect() {
            return this.securityDomain === this.result.domain;
        },
        access_mode() {
            return SecurityAbilityModule.ipaAccessMode;
        },
        inst_name() {
            return SecurityAbilityModule.ipaInstName;
        },
        virtual_domain() {
            return SecurityAbilityModule.ipaVirtualDomain;
        },
        toaDownload() {
            return urlTransformer(
                {
                    a1Ctyun: "https://www.ctyun.cn/document/10065985/10323895",
                    a1Ctcloud: "https://www.esurfingcloud.com/document/zh-cn/20689737/20690458"
                }
            )
        },
        /**
         * TCP端口，只有一条数据时，删除按钮隐藏
         */
        tcpDeleteButtonHide() {
            let isShow = false;
            if (this.form.tcp_ports && this.form.tcp_ports.length > 1) {
                isShow = true;
            }
            return isShow;
        },
        /**
         * UDP端口，只有一条数据时，删除按钮隐藏
         */
        udpDeleteButtonHide() {
            let isShow = false;
            if (this.form.udp_ports && this.form.udp_ports.length > 1) {
                isShow = true;
            }
            return isShow;
        },
    },
    watch: {
        securityDomain: {
            async handler(val) {
                if (!val) {
                    return;
                }
                this.getDetail();
                SecurityAbilityModule.SET_IS_EDIT(false);
            },
            immediate: true,
        },
        securityBasicDomainInfo: {
            async handler(val) {
                if (!val) {
                    return;
                }
                this.handleBaseInfo(val);
            },
            immediate: true,
        },
        isIpaSecurityFormSame() {
            const data = {
                icChange: !this.isIpaSecurityFormSame,
                fn: this.handleSubmit,
            };

            // window.custom.emit("handleCDNConfigChange", data);

            SecurityAbilityModule.SET_IS_IPA_SECURITY_CHANGE(!this.isIpaSecurityFormSame);
            SecurityAbilityModule.SET_SECURITY_MODULE({ handleSubmit: data?.fn });
        },
        form: {
            deep: true,
            handler(val) {
                SecurityAbilityModule.SET_SECURITY_CURRENT_FORM(val);
            },
            immediate: true,
        },
    },
    created() {
        this.getDomainPortInfo();
    },
    mounted() {
        // this.isView = this.from === "view";
        this.init();
    },
    methods: {
        black_white_list_switch_change(val) {
            if (val) {
                this.$set(this.form, "blackWhiteListType", this.temp_black_white_list_type);
                this.$set(this.form, "blackWhiteListContent", this.temp_black_white_list_content);
            } else {
                this.$set(this.form, "blackWhiteListType", 1);
                this.$set(this.form, "blackWhiteListContent", "");
            }
        },
        handleAddDomain() {
            this.isAddDomain = true;
            this.isOpen = false;
        },
        async handleSubmit(resolve, reject) {
            const isValid = await new Promise(resolve1 => this.$refs.form.validate(resolve1));
            if (!isValid) {
                reject && reject(false);
                return;
            }

            if (!(await this.additionalValidate())) return;

            const param = this.restructureData("123"); // 123后端没有用到
            try {
                this.submitLoading = true;
                await this.reqUpdateDomain(param);
                this.$router.push({
                    name: "domain.list",
                    query: {
                        domain: this.result.domain,
                        status: 2,
                    },
                });
                this.$message.success("操作成功");
                const isEdit = false;
                SecurityAbilityModule.SET_IS_EDIT(isEdit);
                SecurityAbilityModule.SET_SECURITY_RENDER_KEY();
                resolve && resolve(true);
            } catch (e) {
                this.submitLoading = false;
                this.$errorHandler(e);
                reject && reject(false);
            } finally {
                this.submitLoading = false;
            }
        },
        /**
         * 提交域名额外校验
         */
        async additionalValidate() {
            // TCP 和 UDP 至少配置一个
            const isTcpConfigured = this.form?.tcp_ports?.some(itm => !!itm.req);
            const isUdpConfigured = this.form?.udp_ports?.some(itm => !!itm.req);
            if (!isTcpConfigured && !isUdpConfigured) {
                await this.$confirm(`TCP请求端口、UDP请求端口，至少配置一个`, {
                    title: `${this.$t("common.messageBox.title")}`,
                    confirmButtonText: `${this.$t("common.dialog.submit")}`,
                    showCancelButton: false
                });
                return false;
            }

            return true;
        },
        reqUpdateDomain(data) {
            return this.$ctFetch(DomainUrl.updataDomain, {
                method: "POST",
                transferType: "json",
                clearEmptyParams: false,
                body: {
                    data,
                },
            });
        },
        // 参数转化
        restructureData(id) {
            this.result.id = id;
            this.result.action = 5;
            // this.result.area_scope = this.accelerateRange;
            // if (this.ipv6_enable_cache !== this.ipv6_enable) {
            //     this.result.ipv6_enable = this.ipv6_enable;
            // } else {
            //     delete this.result.ipv6_enable;
            // }
            // 回源配置：源站
            this.result.config.origin.detail = cloneDeep(this.form.originTableData);
            // 回源配置：回源策略
            this.result.config.origin.origin_type = parseInt(this.form.origin_type) || 1;
            // 回源配置：端口信息
            this.result.config.origin.tcp_ports = cloneDeep(this.form.tcp_ports).filter(itm => itm.origin || itm.req);
            // udp_ports 如果origin和req都为空，传空数组[]
            this.result.config.origin.udp_ports = cloneDeep(this.form.udp_ports).filter(itm => itm.origin || itm.req);

            // 访问控制
            this.result.config.access_control.control_switch = this.form?.black_white_list_switch ? 1 : 2;
            this.result.config.access_control.control_type = this.form.blackWhiteListType;
            this.result.config.access_control.ip_list = this.form.blackWhiteListContent;
            this.result.config.access_control.region_access_control = this.$refs.accessRegionControlRef?.formData;
            // 传递用户ip回源
            if (this.form.user_ip.switch) {
                this.result.config.user_ip.proxy_protocol = this.form.user_ip.proxy_protocol === 1 ? 1 : null;
                this.result.config.user_ip.tcp_option = this.form.user_ip.proxy_protocol === 2 ? 1 : null;
                this.result.config.user_ip.proxy_protocol_version =
                    this.form.user_ip.proxy_protocol === 1 ? this.form.user_ip.proxy_protocol_version : null;
            } else {
                this.result.config.user_ip = null;
            }
            // access_mode
            this.result.access_mode = this.access_mode;
            // inst_name
            this.result.inst_name = this.inst_name;
            // virtual_domain
            this.result.virtual_domain = this.virtual_domain;

            const param = cloneDeep(this.result);
            if (param?.ipv6_enable || param?.ipv6_enable === false) {
                delete param?.ipv6_enable;
            }
            return param;
        },
        // 添加源站
        addDomain() {
            if (this.form.originTableData.length >= 60) {
                this.$message("最多可添加60个IP或域名");
                return;
            }
            this.dialogStatus = "新增源站";
            (this.originItem = {
                address: "",
                role: 1,
                level: 1,
                weight: 10,
            }),
                (this.addDialog = true);
        },
        // 修改源站
        operateEdit(rowData, index) {
            this.dialogStatus = "修改源站";
            this.dialogIndex = index;
            (this.originItem = {
                address: rowData.address,
                role: rowData.role,
                level: rowData.level,
                weight: rowData.weight,
            }),
                (this.addDialog = true);
        },
        roleChange(role) {
            if (role === 1) {
                this.originItem.level = 1;
            }
        },
        // 删除源站
        operateDel(rowData, index) {
            this.$confirm(`确认要删除所选择的源站 ${rowData.address} 吗？`, "删除", {
                confirmButtonText: "确定",
                type: "warning",
            })
                .then(() => {
                    this.form?.originTableData?.splice(index, 1);
                })
                .catch(() => {
                    this.form?.originTableData?.splice(index, 0);
                });
        },
        // 源站-弹窗-确定按钮事件
        addConfirm() {
            this.$refs.originItemForm.validate(valid => {
                if (valid) {
                    if (this.dialogStatus === "新增源站") {
                        this.form.originTableData.push(this.originItem);
                    } else if (this.dialogStatus === "修改源站") {
                        this.form.originTableData.splice(this.dialogIndex, 1, this.originItem);
                    }
                    (this.originItem = {
                        address: "",
                        role: 1,
                        level: 1,
                        weight: "",
                    }),
                        (this.addDialog = false);
                } else {
                    return false;
                }
            });
        },
        // 回源协议切换
        handleRadioChange(val) {
            if (val === 1 || val === 3) {
                // 清空默认值
                this.form?.originTableData?.forEach(item => {
                    item.weight = 10;
                });
            }
        },
        tcpAddLine() {
            const data = {
                origin: "",
                req: "",
            };
            this.form.tcp_ports.push(data);
        },
        tcpDelete(index) {
            if (this.form?.tcp_ports?.length > 1) {
                this.form?.tcp_ports?.splice(index, 1);
            }
        },
        udpAddLine() {
            const data = {
                origin: "",
                req: "",
            };
            this.form.udp_ports.push(data);
        },
        udpDelete(index) {
            if (this.form?.udp_ports?.length > 1) {
                this.form?.udp_ports?.splice(index, 1);
            }
        },
        /**
         * UDP请求端口输入框值清空时，UDP回源端口值也要自动清空，并且只读
         */
        handleUdpChange(val, index) {
            if (!val) {
                this.form.udp_ports[index].origin = "";
            }
        },
        async handleCancel() {
            if (this.isIpaSecurityFormSame) {
                SecurityAbilityModule.SET_IS_EDIT(false);
            } else {
                await this.$confirm("当前域名有未保存的配置，是否确定放弃改动？", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                }).then(() => {
                    if (!this.isIpaSecurityFormSame) {
                        this.getDetail();
                    }
                });
            }
        },
        /**
         * 检查多个IP
         */
        checkIpList(rule, value, callback) {
            value = this.form.blackWhiteListContent;
            if (this.form?.black_white_list_switch && !value) {
                return callback(new Error("IP黑白名单开启，请填写列表"));
            }
            if (!this.form?.black_white_list_switch && !value) {
                return callback();
            }

            const ary = value.split(",");
            // ip检验规则
            const pattern = new RegExp(ip);
            const numberPattern = /^([1-9][0-9]?|[1][0-9][0-9]|[2]([0-4][0-9]|[5][0-6]))$/;
            const res = ary.every(item => {
                // 单个ip的情况
                if (pattern.test(item)) {
                    return true;
                }

                // ip掩码情况
                const maskCode = item.split("/");
                if (maskCode && maskCode.length === 2) {
                    const start = maskCode[0];
                    const end = maskCode[1];
                    if (pattern.test(start) && numberPattern.test(end)) {
                        return true;
                    }
                }

                // 区间情况
                const range = item.split("-");
                if (range && range.length === 2) {
                    const start = range[0];
                    const end = range[1];
                    if (pattern.test(start) && pattern.test(end)) {
                        return true;
                    }
                }

                return false;
            });
            if (!res) {
                return callback(new Error("格式有误，请检查"));
            }

            return callback();
        },
        // toaDownload() {
        //     if (window.location.href.includes("esurfingcloud.com")) return;
        //     window.open("https://www.ctyun.cn/document/10065985/10323895", "_blank");
        // },
        /**
         * 获取域名端口校验规则
         */
        async getDomainPortInfo() {
            const { result } = await this.$ctFetch(DomainUrl.domainPortCheck);
            this.portCheckRules = result;
        },
        validatorVersion(rule, value, callback) {
            if (
                this.form.udp_ports.find(item => {
                    return item.req !== "";
                })
            ) {
                if (
                    this.form.user_ip.proxy_protocol === 1 &&
                    this.form.user_ip.proxy_protocol_version === 1 &&
                    this.form.user_ip.switch
                ) {
                    return callback(new Error("v1版本仅支持tcp协议"));
                }
            }
            return callback();
        },
        // 获取域名详情
        async getDetail() {
            if (!this.securityDomain) return;
            this.$refs.form?.resetFields();

            try {
                this.initLoading = true;
                SecurityAbilityModule.SET_IS_IPA_DOMAIN_DETAIL_LOADING(true);
                const res = await this.$ctFetch(DomainUrl.domainDetail, {
                    method: "GET",
                    transferType: "json",
                    data: {
                        account_id: this.$store.state.user.userInfo.userId,
                        domain: this.securityDomain,
                    },
                });
                // 回填数据
                if (res) {
                    this.init(res);
                    SecurityAbilityModule.SET_IS_EDIT(false);
                }
            } catch (error) {
                SecurityAbilityModule.SET_SECURITY_ORIGIN_FORM(cloneDeep(this.form));
                SecurityAbilityModule.SET_SECURITY_CURRENT_FORM(this.form);
                this.$errorHandler(error);
            } finally {
                this.initLoading = false;
            }
            SecurityAbilityModule.SET_IS_IPA_DOMAIN_DETAIL_LOADING(false);
        },
        async init(val) {
            if (!val) return;
            const result = cloneDeep(val?.result) || [];

            // 赋值给 result
            this.result = result[0];

            // 传递用户ip
            const user_ip = result[0]?.config?.user_ip || {};
            this.form.user_ip.switch =
                user_ip?.proxy_protocol === 2 && user_ip?.tcp_option === 2 ? false : true;
            if (this.form.user_ip.switch) {
                this.form.user_ip.proxy_protocol = user_ip?.proxy_protocol === 1 ? 1 : 2;
                this.form.user_ip.proxy_protocol_version =
                    user_ip?.proxy_protocol === 1 ? user_ip?.proxy_protocol_version : 1;
            }
            // 回源配置？源站
            this.form.originTableData = result[0]?.config?.origin?.detail || [];
            // 回源配置-回源策略
            // 回源配置-端口信息
            let udp_ports = [];
            if (
                result[0]?.config?.origin.udp_ports === undefined ||
                result[0]?.config?.origin.udp_ports?.length === 0
            ) {
                const udpPortsArr = [
                    {
                        req: "",
                        origin: "",
                    },
                ];
                udp_ports = udpPortsArr;
            } else {
                udp_ports = result[0]?.config?.origin?.udp_ports;
            }
            this.form.tactics = result[0]?.config?.origin?.origin_type;
            this.form.origin_type = result[0]?.config?.origin?.origin_type || 1;
            this.form.tcp_ports = result[0]?.config?.origin?.tcp_ports || [];
            this.form.udp_ports = cloneDeep(udp_ports);
            // 访问控制
            const black_white_list_switch =
                result[0]?.config.access_control?.control_switch === 1 ? true : false;
            this.$set(this.form, "black_white_list_switch", black_white_list_switch);

            this.form.blackWhiteListType = result[0]?.config?.access_control?.control_type || 1;
            this.temp_black_white_list_type = result[0]?.config?.access_control?.control_type || 1;

            this.form.blackWhiteListContent = this.result.config?.access_control?.ip_list;
            this.temp_black_white_list_content = this.result.config?.access_control?.ip_list;

            // 访问区域控制
            const region_access_control = result[0]?.config?.access_control?.region_access_control || {};
            const defaultRegionAccessControl = defaultAccessRegionControlForm();
            this.form.region_access_control = Object.assign({}, {
                switch: region_access_control?.switch || defaultRegionAccessControl.switch,
                type: region_access_control?.type || defaultRegionAccessControl.type,
                list: region_access_control?.list?.length
                    ? region_access_control.list.map(itm => {
                        return {
                            ...itm,
                            country: parseAccessRegionControlList(itm?.country),
                            area_province: parseAccessRegionControlList(itm?.area_province),
                            vendor: parseAccessRegionControlList(itm?.vendor),
                        };
                    })
                    : [defaultAccessRegionControlListItem()],
            });

            SecurityAbilityModule.SET_SECURITY_ORIGIN_FORM(cloneDeep(this.form));
            SecurityAbilityModule.SET_SECURITY_CURRENT_FORM(this.form);
            // 基础信息
            // this.accelerateDomain = result[0]?.domain;
            // this.ipv6_enable = result[0]?.ipv6_enable;
            // this.ipv6_enable_cache = result[0]?.ipv6_enable;
            // this.productName = result[0]?.product === 9 ? "应用加速" : "";
            // this.accelerateRange = result[0]?.area_scope;
            // 是否有正在进行的工单
            // const rstOrder = await this.getWorkOrderDetail();
            // this.workOrderGoing = rstOrder.result.length > 0 ? true : false;
            // this.workOrderGoingItem = rstOrder.result.length > 0 ? rstOrder.result[0] : null;
        },
        // 处理基础信息
        handleBaseInfo(data) {
            let inst_name = "-";
            if (data.access_mode === 2) {
                inst_name = data?.inst_name
            }
            const baseInfo = [
                { label: "加速域名", value: data?.domain || "-" },
                { label: "实例名称", value: inst_name || "-" },
                { label: this.isJinhua ? "高防CNAME" : "CNAME", value: (this.isJinhua ? data?.defenseCname : data?.cname) || "-" },
                {
                    label: "产品类型",
                    value: this.productCodeLabel(data?.product_code || "024") || "-",
                },
                {
                    label: "加速区域",
                    value: this.areaScopeLabel(data?.area_scope) || "-",
                },
                { label: "创建时间", value: data?.insert_date || "-" },
            ];
            this.baseInfo = cloneDeep(baseInfo);
        },
        // 是否有正在进行的工单？
        getWorkOrderDetail() {
            return this.$ctFetch(WorkOrderList.workOrderList, {
                method: "GET",
                transferType: "json",
                data: {
                    account_id: this.$store.state.user.userInfo.userId,
                    domain: this.$route.query.domain,
                    status: 2,
                },
            });
        },
        validateOriginAll(rule, value, callback) {
            let count = 0;
            for (let i = 0; i < this.form.originTableData.length; i++) {
                if (this.form.originTableData[i].role === 1) {
                    count++;
                }
            }
            if (count < 1) {
                callback("必须存在一个主源");
            } else {
                callback();
            }
            callback();
        },
        editConfig() {
            if (!this.isDomainCorrect) {
                this.$message.warning("域名信息获取失败，请重新刷新页面。");
                return;
            }
            const isEdit = true;
            SecurityAbilityModule.SET_IS_EDIT(isEdit);
        },
    },
};
</script>

<style lang="scss" scoped>
.domain-edit-wrap {
    overflow: hidden;
    height: 100%; // 定高，出现滚动条
    flex: 1;
    display: flex;
    flex-direction: column;
    padding-left: 20px;
    padding-bottom: 60px;
    // 新增样式
    ::v-deep .el-scrollbar {
        margin: 0 !important; // 上下留白
        margin-bottom: -60px !important; // 用于设置底部按钮的高度
        flex: 1; // 自适应高度
    }
    ::v-deep .el-scrollbar__wrap {
        .el-scrollbar__view {
            // padding: 0 8px; // 内容区固定一个间距出来
            padding: 0 !important; // 内容区固定一个间距出来
            height: 100%;
        }
        overflow-x: hidden;
    }
    ::v-deep {
        .el-form--label-left .el-form-item__label {
            text-align: right;
        }
    }
}
.domain-edit {
    overflow: auto; // 有滚动条
    display: flex;
    flex: 1;
    background: #fff;
    flex-direction: column;
    // margin: 20px;
    // padding: 20px !important;
    .header-tip-style {
        margin: 0 20px 16px 0 !important;
    }
}
.domain-edit-content {
    flex: 1;

    ::v-deep .el-scrollbar__wrap {
        overflow-x: hidden;
    }
    .simple-create-form {
        padding-bottom: 80px;
    }
}
.label-name {
    font-weight: 500;
    width: 600px;
    overflow: hidden;
    border-left: 4px solid $theme-color;
    line-height: 14px;
    padding-left: 12px;
    margin: 16px 0;
    height: 14px;
}
.base-info-style {
    margin: 20px 20px 0 0;
}
.ct-table-style {
    padding-right: 20px !important;
    ::v-deep {
        .el-table__header {
            width: 100% !important;
        }
    }
}
// .el-table::before {
//     width: calc(100% - 20px) !important;
// }
.origin-word {
    // font-family: PingFangSC-Regular;
    font-size: 12px;
    color: $color-neutral-7;
    line-height: 18px;
    font-weight: 400;
    margin-top: $margin-2x;
    .icon-tip {
        margin-right: $margin;
        font-size: 14px;
    }
}
.icon-tip {
    font-size: 14px;
}
.form-item-tip {
    // font-family: PingFangSC-Regular;
    font-size: 12px;
    color: $color-neutral-7;
    line-height: 18px;
    font-weight: 400;
    margin-top: $margin-2x;
}
// 端口信息-样式
.tcp {
    display: flex;
    margin-bottom: 24px;
    .request {
        margin-right: 20px;
        display: flex;
        .port-label {
            width: 80px;
            // margin-right: 20px;
            position: relative;
            &.red-star::before {
                display: block;
                content: "*";
                color: $color-danger;
                position: absolute;
                left: -8px;
                top: 4px;
            }
        }
        .port-input-style {
            width: 200px;
        }
        .el-form-item {
            margin-bottom: 20px;
            ::v-deep {
                .el-form-item__error {
                    align-items: baseline;
                }
            }
        }
    }
    .icon-action {
        color: $color-master;
        font-size: $text-size-lg;
        line-height: 32px;
        margin-right: $margin-2x;
        cursor: pointer;
    }
}
.port-form-item {
    margin-bottom: 0 !important;
}
.user {
    display: block;
    ::v-deep .el-select {
        width: 200px !important;
    }
    ::v-deep .el-form-item__label {
        text-align: left;
    }
    .user_item {
        flex: 1;
    }
}
.link-style {
    display: inline-block;
    margin-left: $margin-4x !important;
}
.blackWhiteList {
    width: 40%;
}
.input-w {
    width: 60%;
}
// .port-tip-style {
//     font-family: PingFangSC, helvetica neue, arial, microsoft yahei regular, microsoft yahei;
// }
.icon-column-label {
    color: $text-color-light;
    margin-right: $margin;
    font-size: $text-size-md;
    line-height: 18px;
    height: 14px;
    width: 14px;
}
.icon-style {
    margin-right: 0 !important;
}
.submit {
    .footer-content {
        display: flex;
        justify-content: flex-end;
        .el-button {
            margin-left: 16px !important;
        }
    }
}
</style>
