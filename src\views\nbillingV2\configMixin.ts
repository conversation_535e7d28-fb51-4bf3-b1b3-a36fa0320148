import { Component, Vue } from "vue-property-decorator";
import { BasicUrl } from "@/config/url/basic";
import { nUserModule } from "@/store/modules/nuser";
import { flowG2PIndentation, convertTenThousand2Int } from "@/filters";
import { StatisticsModule } from "@/store/modules/statistics";

@Component({})
export default class ConfigMixin extends Vue {
    baseResourceType: string[] = []
    billingChangeResourceType: string[] = []
    cdnPacketOrderBtn = ""
    cdnProductOrderBtn = ""
    icdnPacketOrderBtn = ""
    icdnProductOrderBtn = ""
    dataMap: any = {};
    get vipBillingOpt() {
        return StatisticsModule.vipBillingOpt;
    }
    get lang() {
        return nUserModule.lang === "zh" ? "zh-cn" : "en-us"
    }
    get isEn() {
        return nUserModule.lang === "en";
    }
    // 判断是否为线上+个人、线上+政企、线下+个人 
    get orderEnable() {
        return (nUserModule.userInfo as any).saleChannel === "2" || (nUserModule.userInfo as any).userType === "user"; 
    }
    get isCtclouds() {
        return nUserModule.isCtclouds;
    }
    async getConfig() {
        try {
            const basicConfig: any = await this.$ctFetch(BasicUrl.getConfig, { cache: true });
            this.baseResourceType = basicConfig.baseResourceType;
            this.billingChangeResourceType = basicConfig.billingChangeResourceType;
            this.cdnPacketOrderBtn = basicConfig.cdnPacketOrderBtn;
            this.cdnProductOrderBtn = basicConfig.cdnProductOrderBtn;
            this.icdnPacketOrderBtn = basicConfig.icdnPacketOrderBtn;
            this.icdnProductOrderBtn = basicConfig.icdnProductOrderBtn;
        } catch (e) {
            this.$errorHandler(e);
        }
    }
    convertUnit(row: any, isLeft = false) {
        const val = isLeft ? row.packet_size - row.used_num : row.packet_size;
        const total = row.packet_size;
        if ([1, 7, 8].includes(row.resource_type)) {
            const totalRst = flowG2PIndentation(total);
            const currentRst = flowG2PIndentation(val);
            if (totalRst.num === currentRst.num) return `${Math.trunc(currentRst.num as number)} ${currentRst.unit}`;
            else return currentRst.result;
        }
        if ([2, 3, 4].includes(row.resource_type)) {
            const rst = convertTenThousand2Int(val);
            return this.$tc("billing.productUnitMap.1", rst as any);
        }
    }
    /**
     * 对按需产品进行排序
     * 1. 基础服务>增值服务(展示时已处理)，服务中>待生效，中国内地>全球（不含中国内地）(不处理)
     * 2. 生效时间一致：静态请求数（中国内地>全球（不含中国内地）)>quic或动态请求数（中国内地>全球（不含中国内地）)>内容审核>高性能网络>边缘函数
     * 3. 生效时间不一致：按生效时间升序排序（即最早生效的在第一条）
     */
    sortOnDemandDataList(data: any[], isMain = false) {
        // 降序: b > a -> b - a
        data.sort((a, b) => {
            const effDataScore = new Date(a.billingType.effDate).getTime() - new Date(b.billingType.effDate).getTime();
            const statusScore = a.billingType.status - b.billingType.status;

            if (isMain) {
                // 主产品排序
                // 中国内地 > 全球（不含中国内地）
                const productScoreMap: any = {
                    _ABROAD: -1,
                }
                const productScoreKeys = Object.keys(productScoreMap);
                // 1. 按照产品类型排序
                const productAKey = productScoreKeys.find(key => a.resourceType.includes(key));
                const productBKey = productScoreKeys.find(key => b.resourceType.includes(key));
                const aScore = productAKey ? productScoreMap[productAKey] : 0;
                const bScore = productBKey ? productScoreMap[productBKey] : 0;
                if (aScore !== bScore) return bScore - aScore;
                // 2. 按照status排序，待生效一定在生效后面
                if (statusScore !== 0) return statusScore;
                // 3. 按照生效时间排序
                return effDataScore;
            } else {
                // 增值服务排序
                const productScoreMap: any = {
                    FS_100MS: 1,
                    FS_50MS: 2,
                    FS_10MS: 3,
                    VPL_A: 4,
                    VPL: 4.5,
                    C_AUDIT: 5,
                    DYN_REQ: 6,
                    STAQUIC_REQ_A: 7,
                    STAQUIC_REQ: 8,
                    STAHTTPS_REQ_A: 9,
                    STAHTTPS_REQ: 10,
                }
                // 1. 按照产品类型排序
                const productScoreKeys = Object.keys(productScoreMap);
                const productAKey = productScoreKeys.find(key => a.resourceType.includes(key));
                const productBKey = productScoreKeys.find(key => b.resourceType.includes(key));
                const aScore = productAKey ? productScoreMap[productAKey] : -1;
                const bScore = productBKey ? productScoreMap[productBKey] : -1;
                if (aScore !== bScore) return bScore - aScore;
                // 2. 按照status排序，待生效一定在生效后面
                if (statusScore !== 0) return statusScore;
                // 3. 按照生效时间排序
                return effDataScore;
            }
        });
    }
    effDateLe60(row: any) {
        return new Date(row.end_time).getTime() - new Date().getTime() <= 60 * 24 * 60 * 60 * 1000;
    }
    overseaRegionMap(region: string) {
        return region && `${region}` !== "100" ? `_${this.$t(`billingDetail.overseaRegionName['${region}']`)}` : ""
    }
    getOnDemandAcceArea(row: any) {
        if (this.isCtclouds) {
            let billing_area = [...row.billing_area];
            if (billing_area.length > 1) billing_area = [2];
            return billing_area.map((area: any) => this.$t(`billingDetail.ondemandOderTable.billingArea[${area}]`)).join("、")
        }

        return row.billing_area.map((area: any) => this.$t(`billingDetail.ondemandOderTable.billingArea[${area}]`)).join("、")
    }
    // 判断需要显示计费方式的资源类型
    showbillingType(resourceType: string) {
        const list = [
            "CDN_STAHTTPS_REQ",
            "CDN_STAQUIC_REQ",
            "CDN_STAHTTPS_REQ_A",
            "CDN_STAQUIC_REQ_A",
            "CDN_ACCE_C_AUDIT",
            "DL_STAQUIC_REQ",
            "DL_STAHTTPS_REQ",
            "DL_STAQUIC_REQ_A",
            "DL_STAHTTPS_REQ_A",
            "DOWNLOAD_C_AUDIT",
            "VOD_STAHTTPS_REQ",
            "VOD_STAHTTPS_REQ_A",
            "VOD_STAQUIC_REQ",
            "VOD_STAQUIC_REQ_A",
            "VOD_ACCE_C_AUDIT",
            "ST_STAHTTPS_REQ",
            "STATIC_STAQUIC_REQ",
            "ST_STAHTTPS_REQ_A",
            "STATIC_STAQUIC_REQ_A",
            "STATIC_ACCE_C_AUDIT",
        ];
        return !list.includes(resourceType);
    }
}
