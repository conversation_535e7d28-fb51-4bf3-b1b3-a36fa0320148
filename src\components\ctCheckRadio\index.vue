<template>
    <div style="display: inline-block;">
        <el-checkbox v-model="valueInside" v-bind="$attrs" @change="handleChange">
            {{ label }}
        </el-checkbox>
    </div>
</template>

<script>
export default {
    name: "index",
    model: {
        prop: "value",
        event: "handleChange",
    },
    props: {
        value: {
            type: [Boolean, Number],
            default: false,
        },
        label: {
            type: String,
            default: "",
        },
        beforeChange: {
            type: [Object, Function, Promise],
            default: null,
        },
        data: {
            type: [Object, Number, String, Array],
            default: "",
        },
    },
    data() {
        return {
            valueInside: false,
        };
    },
    watch: {
        value: {
            handler(val) {
                this.valueInside = val;
            },
            immediate: true,
        },
    },
    methods: {
        /**
         * 处理改变
         * @param val
         * @returns {Promise<void>}
         */
        async handleChange(val) {
            if (!this.beforeChange && typeof this.beforeChange !== "function") {
                this.passResult(val, true);
                return;
            }

            const res = this.beforeChange(this.data);
            if (!res) {
                this.passResult(val, false);
                return;
            }

            if (res === true) {
                this.$emit("handleChange", val);
                return;
            }

            if (typeof res.then === "function") {
                try {
                    const result = await res;
                    // 适配async-await中使用try-catch 并且return了值的情况
                    if (result === false) {
                        this.passResult(val, false);
                        return;
                    }

                    this.passResult(val, true);
                } catch (e) {
                    this.passResult(val, false);
                }
            }
        },
        /**
         * 通过结果
         */
        passResult(val, result) {
            if (result) {
                this.$emit("handleChange", val);
                return;
            }

            this.valueInside = this.value;
        },
    },
};
</script>

<style scoped></style>
