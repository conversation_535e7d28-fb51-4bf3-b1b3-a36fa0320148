<template>
    <div style="height: 100%">
        <el-tabs
            v-if="showCurrentTab"
            v-model="subActiveTab"
            :tab-position="'top'"
            class="domain-edit-menu">
            <el-tab-pane
                v-for="(item, key) in subTabChildren"
                :key="item.prop + key"
                :label="item.label"
                :name="item.prop"
                :ref="item.prop"
                :disabled="item.disabled"
            >
                <span slot="label">
                    <i class="el-icon-warning" v-show="domain_batch_icon_show[item.prop]" />
                    {{ item.name }}
                </span>
            </el-tab-pane>
        </el-tabs>
        <!-- 缓存设置 -->
        <el-form
            name="filetypeTtl"
            ref="filetypeTtlForm"
            :model="filetypeTtlForm"
            class="wrap-content"
            :rules="rules"
            label-width="124px"
            v-show="subActiveTab === 'filetypeTtl'"
            :disabled="false"
            label-position="left"
        >
            <p class="label-name">{{ $t("domain.create.cache") }}</p>
            <div class="form-box">
                <div class="header-button">
                    <el-button type="primary" @click="showCacheAdd"> {{ $t("domain.add2") }} </el-button>
                    <span class="tips">{{ $t("domain.detail.tip9") }}</span>
                </div>
                <div class="tips-div">
                    <p>{{ $t("domain.detail.tip10") }}</p>
                    <p>{{ $t("domain.detail.tip11") }}</p>
                    <p>{{ $t("domain.detail.tip12") }}</p>
                </div>
                <el-form-item label="" prop="filetype_ttl" :rules="rules.filetypeTtlAll" label-width="1px">
                    <el-table :empty-text="$t('common.table.empty')" :data="filetypeTtlForm.filetype_ttl">
                        <el-table-column
                            :label="$t('domain.type')"
                            prop="mode"
                            :formatter="modeFormatter"
                            minWidth="60"
                        />
                        <el-table-column :label="$t('domain.content')" prop="file_type" minWidth="110" />
                        <el-table-column
                            :label="$t('domain.detail.label32')"
                            prop="cache_type"
                            :formatter="cacheTypeFormatter"
                            min-wiudth="60"
                        ></el-table-column>
                        <el-table-column
                            :label="$t('domain.detail.label33')"
                            prop="ttl"
                            :formatter="ttlFormatter"
                            min-width="80"
                        />
                        <el-table-column
                            prop="cache_with_args"
                            :label="$t('domain.detail.label34')"
                            min-width="60"
                        >
                            <template slot-scope="scope">{{
                                cache_with_args_list[scope.row.cache_with_args]
                            }}</template>
                        </el-table-column>
                        <el-table-column :label="$t('domain.create.weight')" prop="priority" min-width="40" />
                        <el-table-column :label="$t('domain.operate')">
                            <template #default="{ row, $index }">
                                <!-- 非预设类型（如后台配置的正则类型）的将禁止操作，避免用户误操作 -->
                                <el-button
                                    type="text"
                                    :disabled="!CacheModeMap[row.mode]"
                                    @click="filetypeTtlEdit($index)"
                                >
                                    {{ $t("domain.modify") }}
                                </el-button>
                                <el-button
                                    type="text"
                                    :disabled="!CacheModeMap[row.mode]"
                                    @click="deleteCache($index)"
                                >
                                    {{ $t("domain.delete") }}
                                </el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-form-item>
                <div class="tooltips" v-html="tooltips"></div>
                <el-dialog
                    :title="currentIndex === -1 ? $t('domain.add') : $t('domain.edit')"
                    :append-to-body="true"
                    :close-on-click-modal="false"
                    :modal-append-to-body="false"
                    :visible.sync="dialogVisible"
                >
                    <el-form
                        class="form"
                        ref="cacheForm"
                        :model="currentCache"
                        :rules="rules"
                        label-width="124px"
                    >
                        <el-form-item :label="$t('domain.type')" prop="mode">
                            <el-radio-group
                                class="wrap"
                                v-model="currentCache.mode"
                                @change="changeCacheMode"
                            >
                                <el-radio
                                    v-for="opt in cacheModeOptions"
                                    :key="opt.value"
                                    :label="opt.value"
                                    :disabled="opt.disabled"
                                >
                                    {{ opt.label }}
                                </el-radio>
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item :label="$t('domain.content')" prop="file_type" :rules="nameRule">
                            <el-input
                                v-model="currentCache.file_type"
                                :disabled="currentCache.mode === 2 || currentCache.mode === 3"
                                :placeholder="currentCache.mode | inputPlaceholder"
                                @focus="showNameSet(currentCache)"
                            />
                            <div
                                slot="error"
                                slot-scope="{ error }"
                                class="el-form-item__error error-wrapper-mt-6"
                            >
                                {{ error }}
                            </div>
                        </el-form-item>

                        <el-form-item :label="$t('domain.detail.label32')" prop="cache_type">
                            <div v-if="currentCache.cache_type === 4">
                                <el-radio v-model="currentCache.cache_type" :label="4">
                                    {{ $t("domain.detail.label35") }}
                                </el-radio>
                                <div class="option-note">{{ $t("domain.detail.tip13") }}</div>
                            </div>
                            <el-radio-group
                                v-else
                                class="wrap"
                                v-model="currentCache.cache_type"
                                @change="cache_type_change"
                            >
                                <el-radio :label="1">{{ $t("domain.detail.label36") }}</el-radio>
                                <el-radio :label="2">{{ $t("domain.detail.label37") }}</el-radio>
                                <el-radio :label="3">{{ $t("domain.detail.label38") }}</el-radio>
                            </el-radio-group>
                        </el-form-item>

                        <el-form-item
                            :label="$t('domain.detail.label33')"
                            prop="ttl"
                            v-if="currentCache.cache_type !== 1"
                        >
                            <el-input v-model="currentCache.ttl" class="input-with-select">
                                <el-select v-model="currentCache.timeType" :placeholder="$t('simpleForm.alogicCacheMixin.CacheTtlMap.5')" slot="append">
                                    <el-option
                                        v-for="opt in cacheTtlMapOptions"
                                        :key="opt.value"
                                        :label="opt.label"
                                        :value="opt.value"
                                    />
                                </el-select>
                            </el-input>
                        </el-form-item>

                        <el-form-item :label="$t('domain.detail.label34')">
                            <el-switch
                                v-model="currentCache.cache_with_args"
                                :active-value="0"
                                :inactive-value="1"
                            ></el-switch>
                        </el-form-item>
                        <el-form-item
                            :label="$t('domain.create.weight')"
                            prop="priority"
                            :rules="rules.dialog_priority"
                        >
                            <el-input v-model="currentCache.priority" placeholder="1~100" />
                            <div
                                slot="error"
                                slot-scope="{ error }"
                                class="el-form-item__error error-wrapper-mt-6"
                            >
                                {{ error }}
                            </div>
                        </el-form-item>
                    </el-form>
                    <div slot="footer" class="dialog-footer">
                        <el-button @click="dialogVisible = false">{{ $t("common.dialog.cancel") }}</el-button>
                        <el-button type="primary" @click="submitCache">{{
                            $t("common.dialog.submit")
                        }}</el-button>
                    </div>
                </el-dialog>

                <el-dialog
                    :title="$t('domain.detail.cacheModeMap.0')"
                    :close-on-click-modal="false"
                    :modal-append-to-body="false"
                    :visible.sync="extensionsDialogVisible"
                >
                    <div class="extensions" v-for="(val, key) in extensionsOptions" :key="val.title">
                        <div class="extension-title">
                            <el-checkbox
                                v-model="extensionAllSelected[key]"
                                @change="checkAllChange(key)"
                                :label="val.title"
                            />
                        </div>
                        <div class="extension-list">
                            <el-checkbox-group
                                v-model="extensionSelected[key]"
                                @change="checkSingleChange(key)"
                            >
                                <el-checkbox
                                    v-for="extension in val.list"
                                    :label="extension"
                                    :key="extension"
                                />
                            </el-checkbox-group>
                        </div>
                    </div>
                    <div class="extensions">
                        <div class="extension-title">{{ $t('domain.detail.label40') }}</div>
                        <div class="extension-list">
                            <el-input
                                type="textarea"
                                :rows="2"
                                :placeholder="$t('domain.detail.placeholder1')"
                                v-model="extensionOther"
                            />
                        </div>
                    </div>
                    <div slot="footer" class="dialog-footer">
                        <el-button @click="extensionsDialogVisible = false">
                            {{ $t("common.dialog.cancel") }}
                        </el-button>
                        <el-button type="primary" @click="setName">
                            {{ $t("common.dialog.submit") }}
                        </el-button>
                    </div>
                </el-dialog>
            </div>
        </el-form>
        <!-- 状态码设置 -->
        <el-form
            name="errorCode"
            ref="errorCodeForm"
            :model="errorCodeForm"
            class="wrap-content"
            :rules="rules"
            label-width="124px"
            v-show="subActiveTab === 'errorCode'"
        >
            <p class="label-name">{{ $t("domain.detail.label10") }}</p>
            <div class="form-box">
                <div class="header-button">
                    <el-button
                        :disabled="
                            errorCodeForm.error_code &&
                                errorCodeForm.error_code.length === codeCandidate.length
                        "
                        type="primary"
                        @click="addCode"
                    >
                        {{ $t("domain.add2") }}
                    </el-button>
                    <span class="note">
                        {{ $t("domain.detail.tip14") }}
                        <el-tooltip class="item" effect="dark" placement="top-start">
                            <div slot="content">
                                {{ $t("domain.detail.tip105") }}<br />{{ $t("domain.detail.tip106") }}<br />{{ $t("domain.detail.tip107") }}
                            </div>
                            <span class="note-code">&nbsp;3xx,4xx,5xx</span>。
                        </el-tooltip>
                    </span>
                </div>
                <el-table :empty-text="$t('common.table.empty')" :data="errorCodeForm.error_code">
                    <el-table-column prop="code" :label="$t('domain.detail.label41')">
                        <template slot-scope="{ row }">
                            {{ row.code }}
                        </template>
                    </el-table-column>
                    <el-table-column
                        :label="$t('domain.detail.label33')"
                        prop="ttl"
                        :formatter="ttlFormatter"
                        min-width="80"
                    />
                    <el-table-column :label="$t('domain.operate')" width="150">
                        <template slot-scope="{ $index }">
                            <el-button type="text" @click="editCode($index)">
                                {{ $t("domain.edit") }}
                            </el-button>
                            <el-button type="text" @click="delCode($index)">
                                {{ $t("domain.delete") }}
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>

                <el-dialog
                    :title="$t('domain.detail.label42')"
                    :append-to-body="true"
                    :close-on-click-modal="false"
                    :visible.sync="errorCodeDialogVisible"
                    class="add-dialog"
                >
                    <el-form
                        :model="codeContent"
                        ref="statusCode"
                        class="state-code-add-form"
                        :rules="rules"
                        label-width="150px"
                    >
                        <el-form-item :label="$t('domain.detail.label33')" prop="ttl" :rules="rules.codeTtl">
                            <el-input v-model="codeContent.ttl" class="input-with-select">
                                <el-select v-model="codeContent.timeType" :placeholder="$t('simpleForm.alogicCacheMixin.CacheTtlMap.5')" slot="append">
                                    <el-option
                                        v-for="opt in cacheTtlMapOptions"
                                        :key="opt.value"
                                        :label="opt.label"
                                        :value="opt.value"
                                    />
                                </el-select>
                            </el-input>
                            <div
                                slot="error"
                                slot-scope="{ error }"
                                class="el-form-item__error error-wrapper-mt-6"
                            >
                                {{ error }}
                            </div>
                        </el-form-item>
                        <el-form-item
                            :label="$t('domain.detail.label41')"
                            class="code"
                            prop="code"
                            v-if="errorCodeDialogVisible"
                        >
                            <el-input
                                v-model="codeContent.code"
                                :placeholder="$t('domain.detail.placeholder3')" />
                            <div
                                slot="error"
                                slot-scope="{ error }"
                                class="el-form-item__error error-wrapper-mt-6"
                            >
                                {{ error }}
                            </div>
                        </el-form-item>
                    </el-form>
                    <div slot="footer">
                        <el-button @click="errorCodeDialogVisible = false" class="dialog-btn"
                            >{{ $t("common.dialog.cancel") }}
                        </el-button>
                        <el-button type="primary" class="dialog-btn" @click="submitStatusCode">
                            {{ $t("common.dialog.submit") }}
                        </el-button>
                    </div>
                </el-dialog>
            </div>
        </el-form>
        <!-- HTTP响应头 -->
        <el-form
            name="respHeaders"
            ref="respHeadersForm"
            :model="respHeadersForm"
            class="wrap-content"
            :rules="rules"
            label-width="124px"
            v-show="subActiveTab === 'respHeaders'"
        >
            <p class="label-name">{{ $t("domain.detail.label45") }}</p>
            <div :title="$t('domain.detail.label45')" class="form-box">
                <el-button @click="showDialog()" type="primary"> {{ $t("domain.add2") }} </el-button>

                <span class="note">
                    {{ $t("domain.detail.tip15") }}
                </span>

                <el-table :empty-text="$t('common.table.empty')" :data="respHeadersForm.resp_headers">
                    <el-table-column :label="$t('domain.detail.label43')" prop="key" />
                    <el-table-column :label="$t('domain.detail.label44')" prop="value" />
                    <el-table-column :label="$t('domain.operate')">
                        <template slot-scope="{ row }">
                            <el-button type="text" @click="showDialog(row)">
                                {{ $t("domain.modify") }}
                            </el-button>
                            <el-button type="text" @click="deleteHeader(row)">
                                {{ $t("domain.delete") }}
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <el-dialog
                    :modal-append-to-body="false"
                    :title="$t('domain.detail.label45')"
                    :visible.sync="resHeaderDialogVisible"
                    :close-on-click-modal="false"
                    width="860px"
                >
                    <el-form :rules="rules" :model="headerForm" ref="headerForm" label-width="150px">
                        <el-form-item :label="$t('domain.detail.label43')" prop="key">
                            <el-input
                                :placeholder="$t('domain.detail.placeholder4')"
                                v-model="headerForm.key"
                                maxlength="300"
                            />
                            <div
                                slot="error"
                                slot-scope="{ error }"
                                class="el-form-item__error error-wrapper"
                            >
                                {{ error }}
                            </div>
                            <div class="note">{{ $t("domain.detail.tip16") }}</div>
                        </el-form-item>
                        <el-form-item :label="$t('domain.detail.label44')" prop="value">
                            <el-input
                                :placeholder="$t('domain.detail.placeholder5')"
                                v-model="headerForm.value"
                                maxlength="300"
                            />
                            <div
                                slot="error"
                                slot-scope="{ error }"
                                class="el-form-item__error error-wrapper"
                            >
                                {{ error }}
                            </div>
                            <div class="note">
                                <div>{{ $t("domain.detail.tip17") }}</div>
                                <div>{{ $t("domain.detail.tip18") }}</div>
                            </div>
                        </el-form-item>
                    </el-form>
                    <template #footer>
                        <el-button @click="resHeaderDialogVisible = false">
                            {{ $t("common.dialog.cancel") }}
                        </el-button>
                        <el-button type="primary" @click="submitHeader">
                            {{ $t("common.dialog.submit") }}
                        </el-button>
                    </template>
                </el-dialog>
            </div>
        </el-form>
    </div>
</template>

<script>
import minxin from "@/components/simpleform/minxin";
import cacheMixin from "@/components/simpleform/nAlogicCacheTable/mixin";
import { extensionsOptions } from "@/components/simpleform/nAlogicCacheTable/config";
import { formValidate2Promise, lowerFirst } from "@/utils";
import gatewayMapMix from "../gatewayMapMinxin";
import { DomainModule } from "@/store/modules/domain";
import { cloneDeep } from "lodash-es";
import i18n from "@/i18n";
import { cacheStatusCodeValidator, checkArrayRepeat } from '@/utils/validator.utils'

const extensionsMap = {};
Object.keys(extensionsOptions).forEach(key => {
    extensionsOptions[key].list.forEach(k => {
        extensionsMap[k] = key;
    });
});
const defaultCache = {
    code: "",
    ttl: "80", // 上传的时间
    // time: "80", // 显示的时间，配合 type
    timeType: "5", // 5 秒 4 分 3 时 2 天
};

// 缓存类型
const CacheModeMap = {
    0: i18n.t("domain.detail.cacheModeMap[0]"),
    1: i18n.t("domain.detail.cacheModeMap[1]"),
    2: i18n.t("domain.detail.cacheModeMap[2]"),
    3: i18n.t("domain.detail.cacheModeMap[3]"),
    4: i18n.t("domain.detail.cacheModeMap[4]"),
    // 5: "正则",
};
const cacheModeOptions = Object.keys(CacheModeMap).map(mode => ({
    label: CacheModeMap[mode],
    value: mode * 1, // 需要是 number 类型
}));

export default {
    name: "cache",
    components: {},
    mixins: [minxin, cacheMixin, gatewayMapMix],
    props: ["isTabValid", "showCurrentTab"],
    data() {
        return {
            domain_batch_icon_show: DomainModule.domain_batch_icon_show,
            gatewayTabs: [],
            subActiveTab: "filetypeTtl",
            // 缓存过期时间
            filetypeTtlForm: {
                filetype_ttl: [],
            },
            tooltips: "",
            currentIndex: -1, // -1 是新增，其他的是编辑
            currentCache: {
                mode: 0,
                file_type: "",
                ttl: "80", // 上传的时间
                // time: "80", // 显示的时间，配合 timeType
                timeType: "5", // 5 秒
                cache_with_args: 0,
                priority: 10,
            },
            cache_with_args_list: {
                0: this.$t('domain.detail.label96'),
                1: this.$t('domain.detail.label97'),
            },
            extensionAllSelected: {
                dynamic: false,
                image: false,
                style: false,
                av: false,
                download: false,
                page: false,
            },
            extensionsOptions,
            extensionSelected: {
                dynamic: [],
                image: [],
                style: [],
                av: [],
                download: [],
                page: [],
            },
            extensionOther: "",
            extensionsDialogVisible: false,
            // 状态码过期时间
            errorCodeForm: {
                error_code: [],
            },
            codeContent: {},
            activeNameIndex: -1,
            errorCodeDialogVisible: false,
            codeCandidate: [
                "400",
                "401",
                "403",
                "404",
                "405",
                "407",
                "414",
                "500",
                "501",
                "502",
                "503",
                "504",
                "509",
                "514",
            ],
            // HTTP响应头
            respHeadersForm: {
                resp_headers: [],
            },
            editRowKey: "", // 记录被修改的item的key值
            editRowLevel: "", // 记录被修改的item的level值
            curIndex: null,
            dialogVisible: false,
            resHeaderDialogVisible: false,
            headerForm: {
                key: "",
                value: "",
            },
            // 结束
            type: "1",
            text: "",
            rules: {
                dialog_priority: {
                    required: true,
                    validator: this.dialog_priority_valid,
                    trigger: "blur",
                },
                code: [
                    { required: true, message: this.$t("domain.detail.placeholder8"), trigger: "blur" },
                    {
                        validator: (rule, value, callback) => {
                            let currentCode = [];
                            let repeatCode = "";
                            const code = value.replace(/,$/gi, "");
                            //判断是编辑还是新增
                            const bufferCode = JSON.parse(JSON.stringify(this.errorCodeForm.error_code));
                            this.activeNameIndex >= 0
                                ? bufferCode.splice(this.activeNameIndex, 1, { code: value })
                                : bufferCode.push({ code: value });
                            //获取当前的code码
                            bufferCode.forEach(item => {
                                currentCode = currentCode.concat(item.code.split(","));
                            });
                            repeatCode = checkArrayRepeat(currentCode);
                            //检查状态码重复性
                            if (repeatCode.length > 0) {
                                callback(new Error(`${this.$t("domain.detail.tip26")}${repeatCode}`));
                            }
                            //检查状态码规范性
                            if (!cacheStatusCodeValidator(code.split(","))) {
                                callback(new Error(this.$t("domain.detail.tip27")));
                            }

                            callback();
                        },
                    },
                ],
                // 过期时间
                ttl: [
                    { required: true, message: this.$t("domain.detail.ttlTip[0]") },
                    { pattern: "^\\d*$", message: this.$t("domain.detail.ttlTip[1]") },
                    {
                        validator: (rule, value, callback) => {
                            if (!/^\d+$/.test(value)) {
                                callback(new Error(this.$t("domain.detail.tip95")));
                            } else if (
                                this.currentCache.timeType === "2" &&
                                Number(this.currentCache.ttl) > 1095
                            ) {
                                callback(new Error(this.$t("domain.detail.ttlTip[3]")));
                            } else if (
                                this.currentCache.timeType === "3" &&
                                Number(this.currentCache.ttl) > 26280
                            ) {
                                callback(new Error(this.$t("domain.detail.ttlTip[4]")));
                            } else if (
                                this.currentCache.timeType === "4" &&
                                Number(this.currentCache.ttl) > 1576800
                            ) {
                                callback(new Error(this.$t("domain.detail.ttlTip[5]")));
                            } else if (
                                this.currentCache.timeType === "5" &&
                                Number(this.currentCache.ttl) > 94608000
                            ) {
                                callback(new Error(this.$t("domain.detail.ttlTip[6]")));
                            } else {
                                callback();
                            }
                        },
                    },
                ],
                // 过期时间
                codeTtl: [
                    { required: true, message: this.$t("domain.detail.ttlTip[0]") },
                    { pattern: "^\\d*$", message: this.$t("domain.detail.ttlTip[1]") },
                    {
                        validator: (rule, value, callback) => {
                            if (!/^\d+$/.test(value)) {
                                callback(new Error(this.$t("domain.detail.tip95")));
                            } else if (
                                this.codeContent.timeType === "2" &&
                                Number(this.codeContent.ttl) > 1095
                            ) {
                                callback(new Error(this.$t("domain.detail.ttlTip[3]")));
                            } else if (
                                this.codeContent.timeType === "3" &&
                                Number(this.codeContent.ttl) > 26280
                            ) {
                                callback(new Error(this.$t("domain.detail.ttlTip[4]")));
                            } else if (
                                this.codeContent.timeType === "4" &&
                                Number(this.codeContent.ttl) > 1576800
                            ) {
                                callback(new Error(this.$t("domain.detail.ttlTip[5]")));
                            } else if (
                                this.codeContent.timeType === "5" &&
                                Number(this.codeContent.ttl) > 94608000
                            ) {
                                callback(new Error(this.$t("domain.detail.ttlTip[6]")));
                            } else {
                                callback();
                            }
                        },
                    },
                ],
                filetypeTtlAll: [
                    { required: false, validator: this.validFiletypeTtlAll, trigger: "blur, change" },
                ],
                key: [
                    {
                        required: true,
                        message: this.$t("domain.detail.placeholder4"),
                        trigger: "blur",
                    },
                    {
                        pattern: "^[\\w-]+$",
                        message: this.$t("domain.detail.tip16"),
                        trigger: ["blur", "change"],
                    },
                    {
                        validator: (rule, value, callback) => {
                            if (
                                this.curIndex !== null &&
                                this.respHeadersForm.resp_headers[this.curIndex].key === value
                            )
                                callback();
                            else if (this.respHeadersForm.resp_headers.some(header => header.key === value))
                                callback(new Error(this.$t("domain.detail.tip28")));
                            else callback();
                        },
                        trigger: ["blur", "change"],
                    },
                ],
                value: [
                    {
                        pattern:
                            "^[^\\u4e00-\\u9fa5\\u3002\\uff1f\\uff01\\uff0c\\u3001\\uff1b\\uff1a\\u201c\\u201d\\u2018\\u2019\\uff08\\uff09\\u300a\\u300b\\u3008\\u3009\\u3010\\u3011\\u300e\\u300f\\u300c\\u300d\\ufe43\\ufe44\\u3014\\u3015\\u2026\\u2014\\uff5e\\ufe4f\\uffe5]+$",
                        message: this.$t("domain.detail.tip29"),
                        trigger: ["blur", "change"],
                    },
                ],
                name: [
                    { required: true, message: this.$t("domain.htmlForbid.forbid4"), trigger: "blur" },
                    {
                        // 为空是为了满足创建时，自定义后缀名允许为空；而编辑时必填是由上一条 required:true 限制
                        pattern: "^\\w{1,9}(?:,\\w{1,9})*$|^$",
                        message: this.$t("domain.detail.placeholder10"),
                    },
                ],
                path: [
                    { required: true, message: this.$t("domain.detail.placeholder73"), trigger: "blur" },
                    {
                        // pattern: "^(?:/[\\w-]+)+(?:,(?:/[\\w-]+)+)*$",
                        pattern: /^((\/[\w-.]+)+,)*(\/[\w-.]+)+$/,
                        message: this.$t("domain.detail.placeholder51"),
                    },
                ],
                allPath: [
                    { required: true, message: this.$t("domain.detail.placeholder74") },
                    // {
                    //     pattern:
                    //         // "^(?:(?:/[\\w-*]+)+\\.?[A-Za-z0-9*]+)+(?:,(?:/[\\w-*]+)+\\.?[A-Za-z0-9*]+)*$",
                    //         "^([A-Za-z0-9]|[!#$&'()*+,-./:;=?@_~])*$",
                    //     message: this.$t("domain.detail.placeholder14"),
                    // },
                    {
                        validator: (rule, value, callback) => {
                            const paths = value?.split(",");
                            const allPathPattern = new RegExp("^([A-Za-z0-9]|[!#$&'()*+,-./:;=?@_~])*$");
                            paths?.forEach(path => {
                                if (
                                    parseInt(this.currentCache.mode) === 4 &&
                                    (path.includes("?") || path.includes("？"))
                                )
                                    callback(this.$t("domain.detail.placeholder48"));
                                else if (parseInt(this.currentCache.mode) === 4 &&
                                    path.length > 0 &&
                                    path[0] !== "/")
                                    callback(this.$t("domain.detail.placeholder49"));
                                else if (parseInt(this.currentCache.mode) === 4 && !allPathPattern.test(value))
                                    callback(this.$t("domain.detail.placeholder14"));
                                else {
                                    callback();
                                }
                            });
                            callback();
                        },
                        trigger: ["blur", "change"],
                    },
                ],
            },
        };
    },
    computed: {
        subTabChildren() {
            return this.gatewayTabs.find(item => item.prop === "cache").children;
        },
        nameRule() {
            const defaultRule = [{ required: true, message: this.$t("domain.detail.placeholder18") }];
            const { mode } = this.currentCache;
            return (
                {
                    0: this.rules.name,
                    1: this.rules.path,
                    4: this.rules.allPath,
                }[mode] || defaultRule
            );
        },
        formData() {
            const returnData = {};
            const { filetype_ttl = [] } = this.filetypeTtlForm;
            const { error_code = [] } = this.errorCodeForm;
            const { resp_headers = [] } = this.respHeadersForm;
            returnData.filetype_ttl = cloneDeep(filetype_ttl);
            if (returnData.filetype_ttl && returnData.filetype_ttl.length > 0) {
                for (let i = 0; i < returnData.filetype_ttl.length; i++) {
                    if (returnData.filetype_ttl[i].priority) {
                        returnData.filetype_ttl[i].priority = returnData.filetype_ttl[i].priority * 1;
                    }

                    if (returnData.filetype_ttl[i].timeType === "4") {
                        returnData.filetype_ttl[i].ttl = returnData.filetype_ttl[i].ttl * 60;
                    } else if (returnData.filetype_ttl[i].timeType === "3") {
                        returnData.filetype_ttl[i].ttl = returnData.filetype_ttl[i].ttl * 60 * 60;
                    } else if (returnData.filetype_ttl[i].timeType === "2") {
                        returnData.filetype_ttl[i].ttl = returnData.filetype_ttl[i].ttl * 60 * 60 * 24;
                    } else if (returnData.filetype_ttl[i].timeType === "5") {
                        returnData.filetype_ttl[i].ttl = returnData.filetype_ttl[i].ttl * 1;
                    }
                    delete returnData.filetype_ttl[i].time;
                    // 缓存规则值为：不缓存时(参数 === 1)，过期时间字段传0给后端
                    if (returnData.filetype_ttl[i].cache_type === 1) {
                        returnData.filetype_ttl[i].ttl = 0;
                    }
                }
            }
            returnData.error_code = cloneDeep(error_code);
            if (returnData.error_code && returnData.error_code.length > 0) {
                for (let i = 0; i < returnData.error_code.length; i++) {
                    if (returnData.error_code[i].timeType === "4") {
                        returnData.error_code[i].ttl = returnData.error_code[i].ttl * 60;
                    } else if (returnData.error_code[i].timeType === "3") {
                        returnData.error_code[i].ttl = returnData.error_code[i].ttl * 60 * 60;
                    } else if (returnData.error_code[i].timeType === "2") {
                        returnData.error_code[i].ttl = returnData.error_code[i].ttl * 60 * 60 * 24;
                    } else if (returnData.error_code[i].timeType === "5") {
                        returnData.error_code[i].ttl = returnData.error_code[i].ttl * 1;
                    }
                }
            }
            returnData.resp_headers = cloneDeep(resp_headers);
            // 需要去掉前后空格，保留中间的空格
            if (returnData.resp_headers && returnData.resp_headers.length > 0) {
                for (let i = 0; i < returnData.resp_headers.length; i++) {
                    returnData.resp_headers[i].value = returnData.resp_headers[i].value.trim();
                }
            }
            return returnData;
        },
        // 动态计算选项
        cacheModeOptions() {
            return cacheModeOptions.map(option => {
                let disabled = false;

                // mode 2、3 只支持选一个
                if (option.value === 2 || option.value === 3) {
                    disabled = this.filetypeTtlForm.filetype_ttl.some(item => item.mode === option.value);
                }

                return {
                    disabled,
                    ...option,
                };
            });
        },
    },
    watch: {
        isTabValid: {
            deep: true,
            handler() {
                this.gatewayTabs.forEach(item => {
                    if (item.label === this.$t("domain.detail.tab6")) {
                        item.children.forEach(e => {
                            if (this.domain_batch_icon_show[e.prop]) {
                                this.$nextTick(() => {
                                    this.subActiveTab = e.prop;
                                });
                            }
                        });
                    }
                });
            },
            immediate: true,
        },
    },
    methods: {
        validFiletypeTtlAll(rule, value, callback) {
            let element = {};
            for (let i = 0; i < this.filetypeTtlForm.filetype_ttl.length; i++) {
                const timePattern = new RegExp(this.rules.ttl[1].pattern);

                element = this.filetypeTtlForm.filetype_ttl[i];
                const { ttl } = element;
                if (!timePattern.test(Number(ttl))) {
                    callback(new Error(this.$t("domain.detail.placeholder19")));
                } else {
                    callback();
                }
            }
            callback();
        },
        dialog_priority_valid(rule, value, callback) {
            if (!value && value !== 0) {
                callback(new Error(this.$t("domain.detail.placeholder20")));
            }
            if (!/^[0-9]*$/.test(value)) {
                callback(new Error(this.$t("domain.detail.placeholder21")));
            }
            if (value > 100 || value < 1) {
                callback(new Error(this.$t("domain.detail.placeholder21")));
            }
            callback();
        },
        // 缓存过期时间------页面所有的方法
        // 类型文案格式化
        modeFormatter(row, column, cellValue) {
            return this.CacheModeMap[cellValue] || this.$t("domain.unknown");
        },
        // 过期时间格式化
        ttlFormatter({ ttl, timeType }) {
            return `${ttl} ${this.CacheTtlMap[timeType]?.name || ""}`;
        },
        // 去问号缓存文案格式化
        // argsFormatter(row, column, cellValue) {
        //     return cellValue ? "开启" : "关闭";
        // },
        // 缓存类型文案格式化
        cacheTypeFormatter({ cache_type }) {
            if (cache_type === 1) {
                return this.$t("domain.detail.label36");
            } else if (cache_type === 2) {
                return this.$t("domain.detail.label37");
            } else if (cache_type === 4) {
                return this.$t("domain.detail.label35");
            } else {
                return this.$t("domain.detail.label38");
            }
        },
        // 显示新增界面
        showCacheAdd() {
            this.currentIndex = -1;
            this.setCurrentCache(this.defaultCacheData);
            this.dialogVisible = true;
        },
        // 显示修改界面
        filetypeTtlEdit(index) {
            this.currentIndex = index;
            this.setCurrentCache(this.filetypeTtlForm.filetype_ttl[index]);
            this.dialogVisible = true;
        },
        // 设置 currentCache
        setCurrentCache(item) {
            this.currentCache = { ...item, priority: Number(item.priority), ttl: String(item.ttl) };
        },
        showNameSet(cache) {
            // 内容为后缀名才弹出弹框
            if (cache.mode !== 0) return;
            // 清掉缓存
            Object.keys(this.extensionAllSelected).forEach(key => {
                this.extensionAllSelected[key] = false;
                this.extensionSelected[key] = [];
            });
            const other = [];
            // 过滤有复选框的后缀名
            cache.file_type
                .split(",")
                .filter(val => val)
                .forEach(item => {
                    if (extensionsMap[item]) {
                        this.extensionSelected[extensionsMap[item]].push(item);
                    } else {
                        other.push(item);
                    }
                });
            // 其他后缀名
            this.extensionOther = other.join(",");
            // 全选按钮是否选中
            Object.keys(this.extensionSelected).forEach(key => {
                this.extensionAllSelected[key] =
                    new Set(this.extensionSelected[key]).size === extensionsOptions[key].list.length;
            });
            this.extensionsDialogVisible = true;
        },
        // 全选按钮逻辑
        checkAllChange(key) {
            const val = this.extensionAllSelected[key];
            this.extensionSelected[key] = val ? extensionsOptions[key].list : [];
        },
        // 选项是否触发全选
        checkSingleChange(key) {
            const val = this.extensionSelected[key];
            this.extensionAllSelected[key] = val.length === extensionsOptions[key].list.length;
        },
        setName() {
            const extentionReg = new RegExp(this.rules.file_type[1].pattern);
            if (!extentionReg.test(this.extensionOther)) {
                this.$message.error(this.$t("domain.detail.tip30"));
                return;
            }
            this.extensionsDialogVisible = false;
            // 选中复选框的后缀
            const extensions = Object.keys(this.extensionSelected)
                .reduce((rst, key) => {
                    if (this.extensionSelected[key].length > 0) {
                        return rst + "," + this.extensionSelected[key].join(",");
                    }
                    return rst;
                }, "")
                .slice(1);

            // 没有更多，则直接用计算出的结果；如果有更多，则进行拼接
            this.currentCache.file_type = !this.extensionOther
                ? extensions
                : `${extensions}${extensions ? "," : ""}${this.extensionOther}`;
        },
        // 修改当前 cache 的类型
        changeCacheMode() {
            const { mode } = this.currentCache;
            // 首页和全部文件给一个默认值，且设定为不可修改，但是又要记忆之前输入的值
            if (mode === 2 || mode === 3) {
                this.tempCacheName =
                    this.tempCacheName === undefined ? this.currentCache.file_type : this.tempCacheName;
                this.currentCache.file_type = "/";
            } else {
                this.currentCache.file_type =
                    this.tempCacheName === undefined ? this.currentCache.file_type : this.tempCacheName;
                this.tempCacheName = undefined;
            }
            this.$refs.cacheForm.validateField("file_type");
        },
        // 缓存规则为不缓存时，过期时间需要置为：0
        cache_type_change() {
            const { cache_type } = this.currentCache;
            this.$set(this.currentCache, "timeType", "5");
            if (cache_type === 1) {
                this.$set(this.currentCache, "ttl", "0");
            } else {
                this.$set(this.currentCache, "ttl", "80");
            }
        },

        // 新增/修改时的保存
        async submitCache() {
            await formValidate2Promise(this.$refs.cacheForm);

            // 进行重复性检查
            const { mode } = this.currentCache;
            const typeName = this.CacheModeMap[mode] || "";
            const typeList = this.filetypeTtlForm.filetype_ttl
                .filter((i, index) => index !== this.currentIndex) // 先把指定的过滤掉
                .filter(item => item.mode === mode); // 再把不符合的类型过滤掉

            typeList.push(this.currentCache); // 再把当前的追进去进行全比对

            const repeatedName = this.checkRepeatedName(typeList);
            if (repeatedName) {
            await Promise.reject(this.$t("simpleForm.alogicCacheMixin.ValidationErrorMessages.DuplicateValidation", { repeatedName, type: lowerFirst(typeName) }));
            }

            // 将数据存入到list
            if (this.currentIndex === -1) {
                this.filetypeTtlForm.filetype_ttl.push({ ...this.currentCache });
            } else {
                Object.assign(this.filetypeTtlForm.filetype_ttl[this.currentIndex], this.currentCache);
            }

            this.dialogVisible = false;
        },
        // 显示删除
        async deleteCache(index) {
            await this.$confirm(this.$t("domain.detail.tip32"), this.$t("domain.delete"), {
                confirmButtonText: this.$t("common.dialog.submit"),
                cancelButtonText: this.$t("common.dialog.cancel"),
                type: "warning",
            });

            this.filetypeTtlForm.filetype_ttl.splice(index, 1);
        },
        // 状态码过期时间------页面所有的方法
        addCode() {
            this.activeNameIndex = -1;
            this.errorCodeDialogVisible = true;
            this.setCodeContent(defaultCache);
        },
        editCode(index) {
            this.activeNameIndex = index;
            this.setCodeContent(this.errorCodeForm.error_code[index]);
            this.errorCodeDialogVisible = true;
        },
        setCodeContent(item) {
            this.codeContent = { ...item, ttl: String(item.ttl) };
        },
        submitStatusCode() {
            this.$refs.statusCode.validate(valid => {
                if (valid) {
                    this.errorCodeDialogVisible = false;
                    this.codeContent.code = this.codeContent.code.replace(/,$/gi, "");
                    const target = JSON.parse(JSON.stringify(this.codeContent));
                    if (this.activeNameIndex >= 0) {
                        this.errorCodeForm.error_code.splice(this.activeNameIndex, 1, target);
                    } else {
                        this.errorCodeForm.error_code.push(target);
                    }
                }
            });
        },
        async delCode(index) {
            await this.$confirm(this.$t("domain.detail.tip33"), this.$t("domain.delete"), {
                confirmButtonText: this.$t("common.dialog.submit"),
                cancelButtonText: this.$t("common.dialog.cancel"),
                type: "warning",
            });
            this.errorCodeForm.error_code.splice(index, 1);
        },

        // HTTP响应头------页面用到的所有方法
        submitHeader() {
            this.$refs.headerForm.validate(valid => {
                if (valid) {
                    if (this.curIndex !== null) {
                        this.respHeadersForm.resp_headers.splice(this.curIndex, 1, {
                            ...this.headerForm,
                        });
                        if (this.headerForm.key === this.editRowKey) {
                            // key值未改变，视为修改，将level原样返回给后端
                            Object.assign(this.respHeadersForm.resp_headers[this.curIndex], {
                                level: this.editRowLevel,
                            });
                        } else {
                            // key值改变，视为新增，默认下发level:"1,2,3"
                            // Object.assign(this.respHeadersForm.resp_headers[this.curIndex], {
                            //     level: "1,2,3",
                            // });
                            delete this.respHeadersForm.resp_headers[this.curIndex].level;
                        }
                    } else {
                        this.respHeadersForm.resp_headers.push(this.headerForm);
                    }
                    this.resHeaderDialogVisible = false;
                }
            });
        },
        async deleteHeader(header) {
            await this.$confirm(this.$t("domain.detail.tip34"), this.$t("domain.delete"), {
                confirmButtonText: this.$t("common.dialog.submit"),
                cancelButtonText: this.$t("common.dialog.cancel"),
                type: "warning",
            });
            this.respHeadersForm.resp_headers = this.respHeadersForm.resp_headers.filter(
                item => item.key !== header.key
            );
        },
        showDialog(header) {
            this.resHeaderDialogVisible = true;
            if (header) {
                this.editRowLevel = header.level;
                this.editRowKey = header.key;
                this.curIndex = this.respHeadersForm.resp_headers.findIndex(item => item.key === header.key);
                this.headerForm = { ...header };
            } else {
                this.curIndex = null;
                // 新增的时候，向后端默认下发level:"1,2,3"，level不在页面进行展示
                this.headerForm = { key: "", value: "" };
            }
        },
    },
    created() {
        this.gatewayTabs = [...this.defaultTabs];
    },
    filters: {},
};
</script>

<style lang="scss" scoped>
@import "@/components/index.scss";
.el-icon-warning {
    color: #f56c6c;
}
.wrap-content {
    height: 100%;
    overflow-y: auto;
}
::v-deep {
    .el-tabs__header {
        margin: 0;
    }
}
.label-name {
    font-size: 14px;
    font-weight: bold;
    margin: 20px 0 8px 0;
    width: 600px;
    overflow: hidden;
}
::v-deep {
    .el-form .el-form-item {
        margin-bottom: 20px;
    }
}
.origin-add {
    margin: 0 0 6px 0;
}
.tooltips {
    font-size: 12px;
    line-height: 12px;
    margin-top: 10px;
    color: $neutral-7;
}
.info-text {
    margin-left: 10px;
    font-size: 14px;
}
.form-box {
    // margin: 0 0 0 20px;
}
.header-button {
    margin-bottom: 16px;
    .tips {
        margin-left: 8px;
        color: $neutral-7;
    }
}

.tips-div {
    font-size: 12px;
    line-height: 12px;
    margin-top: 10px;
    margin-bottom: 10px;
    p {
        margin-top: 6px;
        color: $neutral-7;
    }
}

// 弹层
::v-deep .el-dialog {
    width: 60%;
    min-width: 340px;
}

// 提示语
.option-note {
    font-size: 12px;
    line-height: 18px;
    margin-top: 8px;
    color: $neutral-7;
}
.note {
    color: $neutral-7;
    margin-left: 4px;
    font-size: 12px;
    div:nth-child(n + 2) {
        margin-top: -8px;
    }
    .note-code {
        color: $color-master;
        cursor: pointer;
    }
}
// 弹层
::v-deep .el-dialog {
    width: 60%;
    min-width: 340px;
}

.extension-title {
    // float: left;
    display: inline-block;
    width: 155px;
    vertical-align: center;
    ::v-deep .el-checkbox {
        display: grid;
        grid-template-columns: 8px auto;
        gap: 8px;
        align-items: end;
        margin-top: 10px;
        .el-checkbox__label {
            white-space: pre-wrap;
        }
        .el-checkbox__input {
            align-self: start;
            padding-top: 2px;
        }
    }
}

.extension-list {
    overflow: hidden;
    display: inline-block;
    width: calc(100% - 155px);
    vertical-align: top;
    white-space: initial;

    ::v-deep .el-checkbox {
        width: 70px;
    }
}

.extensions {
    margin-bottom: 5px;
    padding: 5px 0;
    border-bottom: 1px solid #ccc;
    line-height: 36px;
}
// 表单

.input-with-select {
    ::v-deep {
        .el-input-group__append {
            .el-select {
                // 时分秒
                .el-input {
                    width: 105px;
                }
                // .el-input__inner {
                //     background-color: transparent;
                //     border-color: transparent;
                // }
            }
        }
    }
}
.wrap {
    white-space: normal;
}
.error-wrapper {
    margin-top: -10px;
}
.error-wrapper-mt-6 {
    margin-top: -6px;
}
</style>
