<template>
	<el-tooltip v-if="lock && addon" effect="dark" placement="top" popper-class="aocdn-ignore-lock-tip-wrapper">
		<div slot="content" class="lock-tip">
			<span>{{ $t("domain.editPage.tip17") }}</span>
			<a :href="orderLink" target="_blank">{{ this.$t(
				"domain.editPage.tip22"
			) }}</a>
			<span>{{ $t("domain.editPage.tip18") }}</span>
		</div>
		<slot></slot>
	</el-tooltip>

	<!-- 不存在功能锁并且域名以.ctyuncs.cn为后缀 -->
	<el-tooltip
		v-else-if="isDomainEndsWithCtyun && !lock && ((!fromRespHeaders && !respHeadersShowXosDomainTip) || (fromRespHeaders && respHeadersShowXosDomainTip))"
		effect="dark"
		placement="top"
		popper-class="aocdn-ignore-lock-tip-wrapper"
	>
		<div slot="content" class="lock-tip">
			<span>{{ tipContent }}</span>
		</div>
		<slot></slot>
	</el-tooltip>

    <!-- 跟随请求端口回源 开关开启，回源端口的HTTP和HTTPS输入框鼠标移入需要提示：跟随请求端口回源优先生效。 -->
    <el-tooltip
			v-else-if="originPortDisabled"
			effect="dark"
			placement="top"
			popper-class="aocdn-ignore-lock-tip-wrapper"
    >
			<div slot="content" class="lock-tip">
				<span>{{ tipContent }}</span>
			</div>
			<slot></slot>
    </el-tooltip>
	
	<div v-else-if="addon">
		<slot></slot>
	</div>
</template>

<script>
import { commonLinks } from "@/utils/logic/url";

export default {
	name: "lockTip",
	props: {
		lock: {
			type: Boolean,
			default: false,
		},
		// 额外用于控制组件显隐
		addon: {
			type: Boolean,
			default: true
		},
		isDomainEndsWithCtyun: {
			type: Boolean,
			default: false,
		},
		fromRespHeaders: {
			type: Boolean,
			default: false,
		},
		respHeadersShowXosDomainTip: {
			type: Boolean,
			default: false,
		},
		originPortDisabled: {
			type: Boolean,
			default: false,
		},
	},
	computed: {
		orderLink() {
			return commonLinks.orderLink;
		},
		tipContent() {
			if (this.fromRespHeaders && this.respHeadersShowXosDomainTip) {
					return this.$t("domain.editPage.tip28");
			}
			if (this.originPortDisabled) {
				return this.$t("domain.editPage.originPortTip.tip1");
			}
			return this.$t("domain.editPage.tip27");
		},
	},
};
</script>

<style lang="scss">
.aocdn-ignore-lock-tip-wrapper {
	a {
		color: $color-master;
	}
}
</style>
