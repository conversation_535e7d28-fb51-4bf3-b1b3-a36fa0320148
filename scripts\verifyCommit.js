const chalk = require("chalk");
const msgPath = process.env.HUSKY_GIT_PARAMS || process.env.GIT_PARAMS;
const msg = require("fs")
    .readFileSync(msgPath, "utf-8")
    .trim();
// 兼容分支合并自动生成的 commit 信息，其他按照规范约束
const commitRE = /^Merge branch|^(?:\s*(?:perf|chore|ci|feat|fix|refactor|docs|style|test|types|release)(?:\(\w+\))?\s*)(?:\|(?:\s*(?:perf|chore|ci|feat|fix|refactor|docs|style|test|types|release)(?:\(\w+\))?\s*))*(?::|：).{1,100}$/;
// 校验是否包含 CDN-xxxxx 格式（xxxxx为5个数字）
const cdnTicketRE = /CDN-\d{5}/;

if (!commitRE.test(msg)) {
    console.error(`
        ${chalk.bgRed.white("错误：")} ${chalk.red("无效的 commit-message 格式")}\n
        ${chalk.red(
            "需要按照文档中要求书写。注意：perf、chore、ci 无需后缀，其他类型均需指定子项目后缀。示例："
        )}\n
            ${chalk.green("ci: 调整 ci 流程")}
            ${chalk.green("feat: 完成 xx 功能")}
            ${chalk.green("fix(domain): 修复域名的 yy 问题 (jira SCDN-9527) ")}\n
            ${chalk.green("ci | docs: 调整工作流，更新相关文档")}\n
        ${chalk.red("更多查看 ./docs/basics/git-message规范.md ")}\n
    `);
    process.exit(1);
}

// 检查是否包含 CDN-xxxxx 格式，但排除分支合并信息
if (!msg.startsWith("Merge branch") && !cdnTicketRE.test(msg)) {
    console.error(`
        ${chalk.bgRed.white("错误：")} ${chalk.red("commit-message 必须包含 CDN 工单号")}\n
        ${chalk.red("提交信息必须包含 CDN-xxxxx 格式，其中 xxxxx 为5个数字。示例：")}\n
            ${chalk.green("feat: 完成 xx 功能 (CDN-12345)")}
            ${chalk.green("fix(domain): 修复域名的 yy 问题 (CDN-67890)")}\n
            ${chalk.green("ci | docs: 调整工作流，更新相关文档 (CDN-11111)")}\n
    `);
    process.exit(1);
}
