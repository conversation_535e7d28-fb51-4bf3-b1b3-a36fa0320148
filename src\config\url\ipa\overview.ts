/*
 * @Description: ipa
 */

import { IPA_PREFIX } from "../_PREFIX";

export const OverviewUrl = {
    domainCount: IPA_PREFIX + "/v1/domain/count",
    peakBandWith: IPA_PREFIX + "/v1/statistics/bandwidth/stats",
    flow: IPA_PREFIX + "/v1/statistics/flow/stats",
    flowList: IPA_PREFIX + "/v1/statistics/flow/list",
    bandWithList: IPA_PREFIX + "/v1/statistics/bandwidth/list",
    portTotalCount: IPA_PREFIX + "/v1/statistics/port_num",
    billingMethod: IPA_PREFIX + "/v1/statistics/charge_method",
    bulletinMessage: IPA_PREFIX + "/v1/info_center/bulletin",
    domainMessage: IPA_PREFIX + "/v1/info_center/bulletin/domain",
    connectionsList: IPA_PREFIX + "/v1/statistics/connections/list",
    domainOrderCheck: IPA_PREFIX + "/v1/domain/order/check",
    domainBillingCheck: IPA_PREFIX + "/v1/domain/billing",
    statisticsStats: IPA_PREFIX + "/v1/statistics/stats",
    chargeMethod: IPA_PREFIX + "/v1/statistics/charge_method",
};
