<template>
    <ct-box :tags="$t('statistics.user.tip1')" class="user-area">
        <template #tags-slot>
            <el-radio-group v-model="areaType" size="small" class="area-selector">
                <el-radio-button label="mainland">{{ $t('statistics.provider.mainland') }}</el-radio-button>
                <el-radio-button label="global">{{ $t('statistics.provider.overseas3') }}</el-radio-button>
            </el-radio-group>
            <el-radio-group v-model="currentType" size="small">
                <el-radio-button label="bandwidth">{{ $t('statistics.dcdn.bandwidthFlowWhole.radioBtn1')
                    }}</el-radio-button>
                <el-radio-button label="flow">{{ $t('statistics.dcdn.bandwidthFlowWhole.radioBtn2') }}</el-radio-button>
                <el-radio-button label="requestCnt">{{ $t('statistics.rank.common.tableColumn5') }}</el-radio-button>
            </el-radio-group>
            <el-tooltip effect="dark" :content="$t('statistics.common.searchDownloadContent')" placement="right">
                <i class="el-icon-download download-icon" @click="download"></i>
            </el-tooltip>
        </template>

        <div class="user-section">
            <!-- 流量表格 -->
            <el-table :data="showDataList" v-loading="loading"
                :element-loading-text="$t('statistics.common.table.loading')" v-if="currentType === 'flow'" key="table1" >
                <el-table-column :label="$t('statistics.user.tip2')" prop="provinceName" />
                <el-table-column :label="tableUnit" prop="currentData" />
                <el-table-column :label="$t('statistics.rank.common.tableColumn4')"
                    :prop="areaType === 'mainland' ? 'mainlandFlowProportion' : 'flowProportion'" />
            </el-table>
            <!-- 带宽、请求数表格 -->
            <el-table :data="showDataList" v-loading="loading"
                :element-loading-text="$t('statistics.common.table.loading')" v-else key="table2">
                <el-table-column :label="$t('statistics.user.tip2')" prop="provinceName" />
                <el-table-column :label="tableUnit" prop="currentData" />
            </el-table>
            <div class="pager-wrapper">
                <div class="pager-number">{{ $t('statistics.user.tip3', { num: pagerNumber }) }}/{{
                    $t('statistics.user.tip4', {
                        num: pagerTotal
                    }) }}</div>
                <div class="pager-btn">
                    <el-button size="mini" plain :disabled="pagerNumber < 2 || pagerTotal === 0"
                        icon="el-icon-caret-left" @click="pagerDec">
                    </el-button>
                    <el-button size="mini" plain :disabled="pagerNumber === pagerTotal || pagerTotal === 0"
                        icon="el-icon-caret-right" @click="pagerAdd">
                    </el-button>
                </div>
            </div>
        </div>

        <v-chart v-show="areaType === 'mainland'" class="user-section" v-loading="loading" :element-loading-text="$t('common.chart.loading')"
            autoresize theme="cdn" :options="options" />

    </ct-box>
</template>

<script lang="ts">
import { Component, Watch } from "vue-property-decorator";
import { mixins } from "vue-class-component";
import tableMixin from "../tableMixin";
import "@cdnplus/common/config/echart/china";
import { SearchParams } from "@/types/statistics/user";
import { StatisticsUserUrl } from "@/config/url/statistics";
import { AreaFetchDataItem } from "@/types/statistics/user";
import { getIndexByField } from "@/utils";
import provinceValue from "../province";
import provinceEn from "@/i18n/en/province.json";
import provinceZh from "@/i18n/zh/province.json";
import { nUserModule } from "@/store/modules/nuser";

type UserAreaType = 'bandwidth' | 'flow' | 'requestCnt';

@Component({
    name: "UserArea",
})
export default class UserArea extends mixins(tableMixin) {
    currentType: UserAreaType = "bandwidth";
    areaType = "mainland";
    loading = false;
    fetchDataList: AreaFetchDataItem[] = []; // 请求的列表数据
    overseasDataList: AreaFetchDataItem[] = []; // 海外数据列表

    pagerNumber = 0; // 右侧表格前端分页
    pagerSize = 7;

    @Watch('areaType')
    onAreaTypeChange() {
        // 切换区域类型时重置分页到第一页
        this.pagerNumber = this.sourceData.length > 0 ? 1 : 0;
    }

    get sourceData() {
        return this.areaType === 'mainland' ? this.fetchDataList : this.overseasDataList;
    }

    get pagerTotal() {
        return Math.ceil(this.sourceData.length / this.pagerSize);
    }

    private async localFetchGenerator<T>(url: string, payload: { method: string; body: any }) {
            const rst = await this.$ctFetch<T>(url, payload)
            return rst;
    }
    // 1、数据请求
    async getData(params: SearchParams) {
        this.loading = true;
        const rst = await this.localFetchGenerator<{ list: AreaFetchDataItem[] }>(StatisticsUserUrl.areaUserDataList, {
            method: "POST",
            body: { data: params },
        });

        if (rst?.list) {
            // 分离国内和海外数据
            this.fetchDataList = rst.list.filter(item => item.overseas === 0);
            this.overseasDataList = rst.list.filter(item => item.overseas === 1 || item.overseas === 2);
        } else {
            this.fetchDataList = [];
            this.overseasDataList = [];
        }

        // 有就切到 1 ，没有就切到 0
        this.pagerNumber = Number(!!this.fetchDataList.length);
    }

    // 获取地图的计算最大值配置
    get visualMapConfig() {
        // 计算最大值的数据
        const { currentType, fetchDataList } = this;
        const { scale } = this.dataFormatConfig;
        let visualMapMax = 1000;
        const visualMapMin = 0;

        if (fetchDataList.length > 0) {
            if (currentType !== "requestCnt") {
                visualMapMax = Number((+fetchDataList[0][currentType] / scale).toFixed(2));
            } else {
                visualMapMax = Number(fetchDataList[0][currentType]);
            }
        }
        // 当没数据时，颜色一致
        if (visualMapMax === 0) visualMapMax = 1000;

        return {
            max: visualMapMax,
            min: visualMapMin,
        };
    }

    // 2、数据处理
    get options() {
        const { currentType, fetchDataList, visualMapConfig } = this;
        // 获取单位
        const { label, unit, scale } = this.dataFormatConfig;

        const seriesData: { name: string; value: number }[] = provinceValue;

        //进行表格数据倒叙排序
        fetchDataList
            .sort((a, b) => +b[currentType] - +a[currentType])
            .forEach(item => {
                //获取接口中的每一项对应地图初始话数据的索引值
                const index = getIndexByField(seriesData, "name", item.provinceName);
                //根据索引值将数据填充到地图上面
                if (index !== -1) {
                    seriesData[index].value =
                        currentType !== "requestCnt"
                            ? parseFloat((+item[currentType] / scale).toFixed(2))
                            : +item[currentType];
                }
            });



        const options: any = {
            tooltip: {
                formatter: (a: { data: { name: any; value: any } }) => {
                    if (this.currentType === "requestCnt") {
                        return `${label}<br />${a.data.name}: ${a.data.value}${unit ? `(${unit})` : ''}`;
                    }
                    return `${label}<br />${a.data.name}: ${a.data.value}(${unit})`;
                },
            },
            series: [
                {
                    name: this.$t("statistics.dcdn.bandwidthFlowWhole.radioBtn1"),
                    type: "map",
                    geoIndex: 0,
                    data: seriesData,
                },
            ],
            // 地图最大值
            visualMap: {
                ...visualMapConfig,
                left: "right",
                top: "bottom",
                itemHeight: 120,
                text: [this.$t("statistics.user.tip5[0]"), this.$t("statistics.user.tip5[1]")],
                inRange: {
                    color: ["#bad6ff", "#699bff"],
                },
                show: true,
            },
            geo: {
                map: "china",
                roam: false,
                zoom: 1.23,
                label: {
                    normal: {
                        show: true,
                        fontSize: "10",
                        color: "#333",
                    }
                },
                itemStyle: {
                    normal: {
                        borderColor: "rgba(0, 0, 0, 0.2)",
                    },
                    emphasis: {
                        areaColor: "#3d73f5",
                        shadowOffsetX: 0,
                        shadowOffsetY: 0,
                        shadowBlur: 20,
                        borderWidth: 0,
                        shadowColor: "rgba(0, 0, 0, 0.1)",
                    },
                },
            },
        };

        const isEn = nUserModule.lang === "en";
        if (isEn) {
            options.geo.nameMap = Object.keys(provinceEn).reduce<{ [key: string]: string }>((obj, key) => {
                const mapKey = isEn ? (provinceZh as any)[key] : (provinceEn as any)[key];
                const val = isEn ? (provinceEn as any)[key] : (provinceZh as any)[key];
                obj[mapKey] = val;
                return obj;
            }, {})
        }

        return options;
    }

    //切换类型
    changeAreaTab() {
        if (this.fetchDataList.length === 0) return;
        // 切换类型时分页置1
        this.pagerNumber = 1;
    }

    // 计算需要展示的数据
    get showDataList() {
        const { pagerNumber, pagerSize } = this;
        return this.showDataListBase.slice((pagerNumber - 1) * pagerSize, pagerNumber * pagerSize);
    }

    get showDataListBase() {
        const { currentType } = this;
        const { scale } = this.dataFormatConfig;
        
        return this.sourceData
            .map(item => {
                const currentData =
                    currentType === "requestCnt"
                        ? item[currentType]
                        : (+item[currentType] / scale).toFixed(2);

                return {
                    ...item,
                    currentData,
                };
            })
            .sort((a, b) => Number(b[currentType]) - Number(a[currentType])); // 倒叙排列展示
    }

    //表格前端分页
    pagerAdd() {
        this.pagerNumber += 1;
    }
    pagerDec() {
        this.pagerNumber -= 1;
    }

    download() {
        if (this.sourceData.length === 0) return this.$message(`${this.$t("statistics.common.chart.errMsg[2]")}`);

        const { currentType, areaType } = this;
        let { tableUnit } = this;
        let { scale } = this.dataFormatConfig;

        if (["bandwidth", "flow"].includes(this.currentType)) {
            tableUnit = tableUnit.replace(this.dataFormatConfig.unit, this.currentType === "bandwidth" ? this.Mbps : this.MB);
            scale = Math.pow(this.scale, 2);
        }

        let str = `${this.$t("statistics.user.tip2")},${tableUnit},${currentType !== "flow" ? "\n" : `${this.$t("statistics.rank.common.tableColumn4")}\n`}`;
        this.sourceData.forEach(item => {
            const currentData =
                currentType !== "requestCnt" ? (+item[currentType] / scale).toFixed(2) : item[currentType];

            str += item["provinceName"] + ",";
            str += currentData + ",";
            str += currentType !== "flow" ? "" : (areaType === 'mainland' ? item.mainlandFlowProportion : item.flowProportion);
            str += "\n";
        });

        this.$emit("downloadExcel", {
            name: `${this.$t("statistics.user.tip6")}`,
            str,
        });
    }
}
</script>

<style lang="scss" scoped>
.area-selector {
    margin-right: 20px;
}

.user-section {
    height: 350px;
    .el-table {
        margin: 10px 0 10px
    }
}

.pager-wrapper {
    display: flex;
    flex-direction: row;

    .pager-number {
        color: #606266;
        padding: 10px;
        font-size: 13px;
    }

    .pager-btn {
        margin-top: 5px;

        .el-button+.el-button {
            margin: 0;
        }
    }
}
</style>
