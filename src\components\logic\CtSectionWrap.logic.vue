<!--
 * @Description: 新版内容容器部分，顶部固定，内容区可滚动；
                简单的，只需要展示标题，可以用 headerText{prop} 参数
                复杂的，可以 <template #header></template> 定制
 * @Author: wang yuegong
-->

<template>
    <section class="ct-section-wrap">
        <ct-breadcrumb v-if="showBreadcrumb" />
        <slot name="header">
            <!-- 只处理简单 header ，其他的用插槽实现 -->
            <ct-section-header
                v-if="!!headerText"
                :title="headerText"
                :tip="headerTip"
                :isHtmlTag="isHtmlTag"
            >
                <template #tip>
                    <slot name="tip"></slot>
                </template>
                <template #button>
                    <slot name="headerBtn"></slot>
                </template>
            </ct-section-header>
        </slot>
        <alarm-tip />
        <el-scrollbar :wrap-class="scrollClass">
            <slot></slot>
        </el-scrollbar>
    </section>
</template>
<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";
import AlarmTip from "./AlarmTip.vue";

@Component({
    name: "CtSectionWrap",
    components: { AlarmTip },
})
export default class CtSectionWrap extends Vue {
    @Prop({ type: String }) private headerText?: string;
    @Prop({ type: String }) private headerTip?: string;
    @Prop({ type: Boolean, default: true }) private showBreadcrumb?: boolean;
    @Prop({ type: Boolean, default: false }) private isHtmlTag?: boolean;
    @Prop({ type: String, default: "" }) private scrollClass?: string;
}
</script>

<style lang="scss" scoped>
.ct-section-wrap {
    height: 100%; // 定高，出现滚动条
    display: flex;
    flex-direction: column;

    ::v-deep .el-scrollbar {
        margin: $common-space-5x 0; // 上下留白
        flex: 1; // 自适应高度
    }
    ::v-deep .el-scrollbar__wrap {
        .el-scrollbar__view {
            padding: 0; // 内容区固定一个间距出来
            height: 100%;
            padding: 0 $common-space-5x;
        }
        overflow-x: hidden;
    }
}
</style>
