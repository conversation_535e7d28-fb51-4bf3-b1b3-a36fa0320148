export const cacheStatusCodeValidator = (arr: string[]): boolean => {
  // 支持除304外的3xx、4xx、5xx
  if (arr.some(i => i.length !== 3)) return false;
  return arr.map(Number).every(i => !isNaN(i) && Number.isInteger(i) && i !== 304 && i >= 300 && i < 600);
}

export const errorPageCodeValidator = (arr: string[]): boolean => {
    // 支持：300～599 且非 499
    if (arr.some(i => i.length !== 3)) return false;
    return arr.map(Number).every(i => !isNaN(i) && Number.isInteger(i) && i !== 499 && i >= 300 && i < 600);
  }


// 判断重复性
export const checkArrayRepeat = (array: string[]) => {
    array.sort();
    const reEle = [];
    for (let i = 0; i < array.length; i++) {
        if (i + 1 === array.length) {
            continue;
        }
        //判断相邻的元素是否相同
        if (array[i] === array[i + 1]) {
            reEle.push(array[i]);
        }
    }
    // 去重
    return Array.from(new Set(reEle)).join(",");
}

// 校验范围为 1-65535 的端口
export const portRegex = /^([1-9]\d{0,3}|[1-5]\d{4}|6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5])$/

export const allPathPattern = new RegExp("^([A-Za-z0-9]|[!#$&'()*+,-./:;=?@_~])*$");

// 精准域名匹配正则，不支持泛域名
export const exactDomainRegexp = /^([a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,6}$/;
