/*
 * @Description: 菜单通用的逻辑
 * @Author: wang yuegong
 */
import { Component, Vue, Prop } from "vue-property-decorator";
import { MenuListItem } from "@/types/store";

@Component
export default class MenuMixin extends Vue {
    @Prop({ required: true, type: Map }) pathMap!: Map<string, string>;
    @Prop({ required: true, type: Array }) menuList!: any[];
    $docHelp: any
    // 跳转
    go(menu: MenuListItem) {
        // 如果渲染类型为 blank 则打开新的页面
        if (menu.renderer === "blank") {
            // window.open(menu.href || menu.hrefLocal, "_blank");
            this.$docHelp(menu.href)
        } else if (menu.renderer === "button") {
            // 如果是 button 类型则通过 ucode 调用预设的方法
            if (menu.ucode in this) (this as any)[menu.ucode]();
        } else {
            // 有配置链接再跳转
            menu.hrefLocal && this.$router.push(menu.hrefLocal);
        }
    }

    // 预置的获取大屏地址的处理
    async getCsa() {
        const rst = await this.$ctFetch<{ url: string }>("/v1/scdn/scdn/GetCsaUrl");

        window.open(rst.url, "_blank");
    }

    private get activeIdentifier() {
        // 当前路径 /domain/list
        const curPath = this.$store.state.route.path;
        const map = this.pathMap;
        let identifier = "0";
        /**
         * 两种匹配情况：
         * 1、 curPath 完全是指定 path 的路径
         * 2、 curPath 和 path 的部分前缀匹配（简化为只匹配第一层和第二层）
         */
        /* eslint-disable prefer-const */
        for (let [path, index] of map.entries()) {
            path = path.replace(/\?.*/, "");
            // 匹配到全等路径
            if (curPath === path) {
                return (identifier = index);
            }
        }

        const curPathPre1 = curPath
            .split("/")
            .slice(0, 2)
            .join("/");
        const curPathPre2 = curPath
            .split("/")
            .slice(0, 3)
            .join("/");

        for (let [path, index] of map.entries()) {
            // 匹配到前两级全等路径（优先匹配两级）
            if (path.startsWith(curPathPre2)) {
                return (identifier = index);
            }
        }

        for (let [path, index] of map.entries()) {
            // 匹配到前一级全等路径
            if (path.startsWith(curPathPre1)) {
                return (identifier = index);
            }
        }

        return identifier;
    }
}
