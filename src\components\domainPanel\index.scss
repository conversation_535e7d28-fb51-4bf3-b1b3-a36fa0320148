.ct-domain-panel {
    width: 230px !important;
    height: 100%;
    padding-right: $padding-5x;
    border-right: 1px solid $color-neutral-3;
    transition: all 0.3s ease-in-out;

    &.fold {
        width: 0 !important;
    }

    .domain-panel {
        width: 100%;
        height: 100%;
        // font-family: element-icons;
        font-size: $text-size;
        color: $text-color;
        line-height: 18px;
        font-weight: 400;
        display: flex;
        flex-direction: column;

        & > div {
            width: 100%;
        }

        .title-style {
            // font-family: element-icons;
            font-size: $text-size-lg;
            color: $text-color;
            line-height: 24px;
            font-weight: 500;
        }

        .second-box {
            height: 32px;
            margin: $margin-2x 0 $margin 0;

            .plus-circle {
                margin-right: $margin;
            }
        }

        .domain-list-box {
            flex: 1;
            margin-top: $margin;
            overflow: hidden;

            .domain-item {
                width: 100%;
                height: 32px;
                overflow: hidden;
                margin-bottom: $margin;
                padding: 0 $padding-2x;
                cursor: pointer;

                .domain {
                    flex: 1;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    margin-right: $margin-5x;
                }

                &:last-child {
                    margin-bottom: 0;
                }

                &:hover {
                    background: $color-neutral-1;
                }

                &.domain-item-active {
                    .domain {
                        color: $color-master;
                    }
                }
            }
        }
    }
}

.indent-style {
    font-size: 16px;
    color: $text-color;
    cursor: pointer;

    &:hover {
        color: $color-master;
    }
}

.folder-style {
    margin-right: $margin-2x;
}

.empty-column {
    display: flex;
    flex-direction: column;
}

::v-deep .protect-text {
    margin-right: $margin;

    &.icon-style {
        width: 14px;
        height: 14px;
        background-position: 0 0;
        background-size: contain;
        background-repeat: no-repeat;

        &.success {
            // background-image: url("../images/protect-icon.svg");
        }

        &.warning {
            // background-image: url("../images/unprotect-icon.svg");
        }
    }
}
