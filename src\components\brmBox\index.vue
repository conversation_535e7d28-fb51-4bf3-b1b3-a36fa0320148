<template>
    <div class="dos-box" :class="{ 'has-pre-label': preLabel }">
        <div v-if="preLabel" class="left" :class="{ 'is-select': type === 'select' }">
            <slot name="prepend">
                <template v-if="type !== 'select'">{{ preLabel }}</template>
                <el-select v-else v-model="valueInside" @change="handleChange" :style="{ width: labelWidth }">
                    <el-option
                        v-for="(item, key) in list"
                        :key="key"
                        :value="item.value"
                        :label="item.label"
                    />
                </el-select>
            </slot>
        </div>
        <slot></slot>
    </div>
</template>

<script>
export default {
    name: "index",
    model: {
        prop: "value",
        event: "change",
    },
    props: {
        preLabel: {
            type: String,
            required: false,
            default: "",
        },
        value: {
            type: [String, Number, Array],
            required: true,
            default: "",
        },
        list: {
            type: Array,
            required: false,
            default: () => [],
        },
        type: {
            type: String,
            required: false,
            default: "",
        },
        labelWidth: {
            type: String,
            required: false,
            default: "80px",
        },
    },
    data() {
        return {
            valueInside: "",
        };
    },
    watch: {
        value: {
            handler(val) {
                this.valueInside = val;
            },
            immediate: true,
        },
    },
    methods: {
        handleChange(val) {
            this.$emit("change", val);
        },
    },
};
</script>

<style scoped lang="scss">
.dos-box {
    display: flex;
    flex-direction: row;
    align-items: center;

    .left {
        color: rgba(0, 0, 0, 0.65);
        line-height: 32px;
        height: 32px;
        padding: 0 24px;
        border-style: solid;
        border-width: 1px 0 1px 1px;
        border-color: $border-color transparent $border-color $border-color;
        border-radius: 2px 0 0 2px;
        white-space: nowrap;
        box-sizing: border-box;
        display: flex;
        align-items: center;

        ::v-deep.el-select {
            display: flex;
            align-items: center;
            height: 30px;
            background: pink;

            .el-input__inner {
                line-height: 30px;
                height: 30px;
                background: $bg-color-light;
                border: unset;
            }
        }

        &.is-select {
            padding: 0;
        }
    }

    &.has-pre-label {
        ::v-deep .el-input__inner {
            border-radius: 0 2px 2px 0;
        }
    }
}
</style>
