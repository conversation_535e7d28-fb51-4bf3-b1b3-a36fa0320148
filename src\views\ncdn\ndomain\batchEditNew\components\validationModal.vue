<template>
    <el-dialog
        :title="title"
        :visible.sync="visible"
        :append-to-body="true"
        custom-class="ecs-validation-error-modal"
        width="620px"
    >
        <div v-for="(v, index) in validationError" :key="index" class="validation-row">
            <span class="label-span">{{ v.name }}:</span>
            <i class="el-icon-success" v-if="v.pass === 'true'"></i>
            <i class="el-icon-error" v-else></i>
            <span v-html="v.note"></span>
        </div>
        <span slot="footer" class="dialog-footer">
            <el-button type="primary" @click="visible = false">{{ $t("common.dialog.submit") }}</el-button>
        </span>
    </el-dialog>
</template>

<script lang="ts">
import { Vue, Component, Prop, Watch } from "vue-property-decorator";

@Component({
    name: "BatchEditValidationModal",
})
export default class BatchEditValidationModal extends Vue {
    @Prop({ required: true, type: Boolean }) value!: boolean;
    @Prop({ required: true, type: Array }) validationError!: { name: string; pass: string; note: string }[];
    @Prop({ required: true, type: String }) title!: string;

    visible = this.value;

    @Watch("value")
    onValueChange(val: boolean) {
        this.visible = val;
    }

    @Watch("visible")
    onVisibleChange(val: boolean) {
        this.$emit("input", val);
    }
}
</script>

<style lang="scss" scoped>
.ecs-validation-error-modal {
    .validation-row {
        line-height: 26px;
        margin-bottom: 8px;

        .label-span {
            display: inline-block;
            width: 220px;
            margin-right: 8px;
            vertical-align: middle;
        }

        i {
            margin: 0 8px 0 20px;
            &.el-icon-success {
                color: $color-success;
            }
            &.el-icon-error {
                color: $color-danger;
            }
            &.el-icon-warning {
                color: $color-warning;
                font-size: 18px;
            }
        }

        a:hover,
        a:active,
        a:link,
        a:visited,
        strong {
            color: $color-master;
        }
    }
}
</style>
