<template>
    <ct-section-wrap :headerText="getHeaderText">
        <ct-box :class="['fix-box', 'table-scroll-wrap']">
            <div class="script-form">
                <el-form
                    :model="form"
                    status-icon
                    :rules="rules"
                    ref="scriptForm"
                    label-width="100px"
                    class="demo-ruleForm"
                    v-loading="loading"
                    :hide-required-asterisk="type === 'detail'"
                >
                    <el-row>
                        <el-col v-if="type === 'add'" style="padding-bottom: 24px;"><BusinessScriptTip /></el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="14">
                            <el-form-item label="脚本名称" prop="script_name">
                                <el-input
                                    v-if="type === 'add'"
                                    v-model="form.script_name"
                                    placeholder="2-64位，只支持小写字母、数字、下划线，开头结尾只允许小写字母和数字"
                                ></el-input>
                                <span v-else>{{ form.script_name }}</span>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="14">
                            <el-form-item label="作用域名" prop="work_scope">
                                <el-select
                                    v-if="type === 'add'"
                                    v-model="form.work_scope"
                                    clearable
                                    filterable
                                    placeholder="请选择作用域名"
                                >
                                    <el-option
                                        v-for="item in domainList"
                                        :key="item.label"
                                        :value="item.value"
                                        :label="item.label"
                                    ></el-option>
                                </el-select>
                                <span v-else>{{ form.work_scope }}</span>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="14">
                            <el-form-item label="脚本语言">
                                <span>{{ "lua" }}</span>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-form-item label="脚本内容" prop="src_code">
                            <el-col :span="20">
                                <el-popover
                                    placement="right"
                                    trigger="manual"
                                    width="800"
                                    ref="popoverRef"
                                    v-model="popoverVisible"
                                >
                                    <h3 class="src-code-content-title">选择脚本模板</h3>
                                    <div class="src-code-box">
                                        <el-radio-group v-model="srcCodeRadio">
                                            <el-radio
                                                v-for="(item, index) in srcCodeTitle"
                                                :label="index"
                                                :key="item"
                                                border
                                            >
                                                <div class="src-code-content">
                                                    <p class="src-code-content-title">{{ item }}</p>
                                                    <p class="src-code-content-text1">
                                                        {{ srcCodeText1[index] }}
                                                    </p>
                                                    <p class="src-code-content-text2">
                                                        <span class="src-code-content-text-title"
                                                            >示例：</span
                                                        >
                                                        {{ srcCodeText2[index] }}
                                                    </p>
                                                </div>
                                            </el-radio>
                                        </el-radio-group>
                                    </div>
                                    <div style="text-align: right; margin: 0">
                                        <el-button size="mini" type="text" @click="popoverVisible = false"
                                            >取消</el-button
                                        >
                                        <el-button type="primary" size="mini" @click="handleAdd"
                                            >确定</el-button
                                        >
                                    </div>
                                    <el-button :disabled="isDisabled" @click="selectScript" slot="reference"
                                        >选择脚本模板</el-button
                                    >
                                </el-popover>
                            </el-col>
                            <el-col :span="20">
                                <div class="monaco-box">
                                    <ct-monaco
                                        v-model.trim="form.src_code"
                                        :minimap="false"
                                        auto-size
                                        language="lua"
                                    />
                                </div>
                            </el-col>
                        </el-form-item>
                    </el-row>
                </el-form>
                <div class="dictionary-flootr">
                    <el-button @click="goBack()">{{ isDisabled ? "返回" : "取消" }}</el-button>
                    <el-button type="primary" v-if="!isDisabled" :disabled="isContentModified" @click="submit">确认提交</el-button>
                </div>
            </div>
        </ct-box>
    </ct-section-wrap>
</template>

<script lang="ts">
import { Component, Mixins, Vue, Ref } from "vue-property-decorator";
import { nscriptUrl } from "@/config/url/ncdn/nscript";
import { easyClone, getImgSize } from "@/utils";
import { ElForm } from "element-ui/types/form";
import { deploymentStatusMap } from "@/config/map";
import { dictionaryName } from "@/config/npattern";
import { debounce } from "@cdnplus/common/utils";
import { nScriptModule } from "@/store/modules/nscript";
import { isEqual } from "lodash-es";
import BusinessScriptTip from "./Tip.vue";
import { CtiamCode } from "@/utils/ctFetch/errorConfig";

const dictionaryNameReg = new RegExp(dictionaryName);
type updateReq = {
    id?: string;
    scriptName: string;
    srcCode: string;
    workScope: string;
};

const srcCodeTitle = ["定制化鉴权", "请求头改写", "响应头改写", "重定向", "限速", "回源URL改写"];
const srcCodeText1 = [
    "一般是进行防盗链校验，只有校验通过后的请求才放行，校验不通过返回403。",
    "一般是回源时对请求头进行改写或者新增请求头。",
    "无条件新增响应头或根据响应码或者响应头，新增其他响应头。",
    "针对某些情况，返回新的访问url给客户端，同时返回302。",
    "有时候需要根据不同的时间段对文件请求进行限速，比如早晚高峰时间限速500k。",
    "某些场景下，需要对回源的URL进行改写。",
];
const srcCodeText2 = [
    "对请求uri进行md5加密，然后用加密后的结果跟secret查询参数做比较，如果不相等，则校验不通过拒绝访问",
    "如果是m3u8文件请求，删除range头",
    "新增响应头：Access-Control-Allow-Origin: *, Access-Control-Expose-Headers:Content-Length.如果源站响应200，新增响应头: Cache-Status: HIT. 否则新增Cache-Status: MISS",
    "如果客户端发起http请求，为了数据安全，重定向https请求给客户端",
    "8点到18点限速700k；18点到22点限速500k；22点到次日8点限速1000k",
    "当访问m3u8文件时，需要将文件名数字前缀去掉，比如123_abc.m3u8，回源时需要改写为abc.m3u8",
];
const srcCodeContent = [
    `local secret_key = "*****"
if ctyun.md5(ctyun.var("uri")..secret_key) ~= ctyun.var("arg_secret") then
    ctyun.resp.exit(403)
end`,
    String.raw`if ctyun.re.match(ctyun.var("uri"), "\\.m3u8$", "jo") ~= nil then
    ctyun.req.set_header("range", nil)
end`,
    `--[[无条件设置]]--
ctyun.resp.set_header("Access-Control-Allow-Origin", "*")
ctyun.resp.set_header("Access-Control-Expose-Headers", "Content-Length")

local function set_cache_status()
    if ctyun.resp.get_code() == 200 or ctyun.resp.get_code() == 206 then
        ctyun.resp.set_header("Cache-Status", "HIT")
    else
        ctyun.resp.set_header("Cache-Status", "MISS")
    end
end

--[[
由于本脚本是在请求时处理的，此时还没有回源不清楚源站响应的状态码。
需要使用enroll_header_back注册回调函数，在收到响应头处理
]]--
ctyun.req.enroll_header_back(set_cache_status)`,
    `if ctyun.var("scheme") == "http" then
    ctyun.resp.set_header("location", "https://"..ctyun.var("host")..ctyun.var("request_uri"))
    ctyun.resp.exit(302)
end`,
    `local current_time = ctyun.var("time_local")
if ctyun.re.match(current_time, "(0[89]|1[0-7]):[0-5]{2}:[0-5]{2}", "jo") then
    ctyun.resp.set_limit_rate("700K")
elseif ctyun.re.match(current_time, "(1[89]|2[0-1]):[0-5]{2}:[0-5]{2}", "jo") then
    ctyun.resp.set_limit_rate("500K")
else
    ctyun.resp.set_limit_rate("1000K")
end`,
    String.raw`local uri = ctyun.var("uri")
local m = ctyun.re.match(uri, "(.+)/[0-9]+_(.+)\\.m3u8$")
if m ~= nil then
    uri = m[1].."/"..m[2]..".m3u8"
    ctyun.req.set_uri(uri)
end`,
];
@Component({
    components: {
        BusinessScriptTip
    }
})
export default class BusinessScriptAdd extends Mixins(Vue) {
    private deploymentStatusMap = deploymentStatusMap;
    // 脚本模板提示标题，内容
    private srcCodeTitle = srcCodeTitle;
    private srcCodeText1 = srcCodeText1;
    private srcCodeText2 = srcCodeText2;
    // 脚本模板内容
    private srcCodeContent = srcCodeContent;
    // 选择的脚本模板
    private srcCodeRadio = "";
    // 显示模板选择气泡框
    private popoverVisible = false;
    // 防抖
    private debounce = debounce;
    private loading = false;
    // 作用域名选择框loading
    private selectLoading = false;
    private form = {} as any;
    private oldForm = {} as any;

    private rules = {
        // eslint-disable-next-line @typescript-eslint/camelcase
        script_name: [
            { required: true, message: "请输入脚本名称", trigger: "blur" },
            { validator: this.validateDictionary, trigger: "blur" },
        ],
        // eslint-disable-next-line @typescript-eslint/camelcase
        src_code: [{ required: true, validator: this.validateSrcCode, trigger: "blur" }],
        // eslint-disable-next-line @typescript-eslint/camelcase
        work_scope: [{ required: true, message: "请选择作用域名", trigger: "change" }],
    };
    // 作用域名数据
    get domainList() {
        return nScriptModule.scriptDomainAddList.length ? nScriptModule.scriptDomainAddList : [];
    }
    get getHeaderText() {
        return this.type === "edit" ? "编辑业务脚本" : this.type === "add" ? "添加业务脚本" : "查看业务脚本";
    }
    get isDisabled() {
        return this.$route.query.operateType === "detail";
    }
    get isContentModified() {
        return this.type === "edit" && isEqual(this.form?.src_code, this.oldForm?.src_code);
    }
    get type() {
        return this.$route.query.operateType;
    }
    get getId() {
        return this.$route.query.id;
    }
    validateSrcCode(rule: any, value: string, callback: Function) {
        if (!this.form.src_code) {
            return callback(new Error("请输入脚本内容"));
        }
        callback();
    }
    validateDictionary(rule: any, value: string, callback: Function) {
        if (!dictionaryNameReg.test(value)) {
            return callback(
                new Error(
                    "脚本名称格式错误，请输入2-64位,只支持小写字母、数字、下划线，开头结尾只允许小写字母和数字"
                )
            );
        }
        callback();
    }

    mounted() {
        this.initForm();
    }
    async initForm() {
        if (this.type !== "add") {
            try {
                this.loading = true;
                const res = await this.$ctFetch<{ result: {} }>(nscriptUrl.ScriptDetail, {
                    method: "POST",
                    body: {
                        data: { id: this.getId },
                    },
                    headers: {
                        "Content-Type": "application/json",
                    },
                });
                this.form = easyClone(res.result);
                this.oldForm = easyClone(this.form);
            } catch (error) {
                const isCtiamError = CtiamCode.includes((error as any)?.data?.code);
                if (isCtiamError) {
                    this.$errorHandler(error);
                } else {
                    this.$message.error((error as any).data.reason);
                }
            } finally {
                this.loading = false;
            }
        }
    }
    private goBack() {
        // this.$router.push({
        //     name: "nscript.business",
        //     query: {
        //         isRefresh: "fasle",
        //     },
        // });
        this.$router.go(-1);
    }
    private selectScript() {
        this.popoverVisible = true;
    }
    private handleAdd() {
        if (this.srcCodeRadio === "") {
            return this.$message.warning("请选择脚本模板！");
        }
        this.$confirm(`此操作将覆盖已填入的脚本内容，是否继续？`, `提示`, {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
        })
            .then(() => {
                // eslint-disable-next-line @typescript-eslint/camelcase
                this.form.src_code = this.srcCodeContent[Number(this.srcCodeRadio)];
                // 选中之后编辑器视图没有更新临时解决方案
                this.$forceUpdate();
            })
            .finally(() => {
                this.popoverVisible = false;
            });
    }
    @Ref("scriptForm") readonly scriptFormRef!: ElForm;
    private async submit() {
        try {
            await this.$ctUtil.formValidate2Promise(this.scriptFormRef);
            await this.scriptFormRef.validate();
            const operationType = this.getHeaderText.split("业务脚本")[0];
            if (getImgSize(btoa(encodeURIComponent(this.form.src_code))) > 5120) {
                return this.$message.warning("脚本内容不能超过5MB！");
            }
            if (JSON.stringify(this.form) === JSON.stringify(this.oldForm)) {
                this.$router.push({
                    name: "nscript.business",
                    query: {
                        isRefresh: "false",
                    },
                });
                return;
            }
            await this.$confirm(`此操作将对业务脚本进行${operationType}操作，是否继续？`, `提示`, {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            });
            this.loading = true;
            const params = {
                id: this.getId,
                scriptName: String(this.form.script_name),
                srcCode: btoa(encodeURIComponent(this.form.src_code)),
                workScope: this.form.work_scope,
            } as updateReq;
            if (this.type === "add") delete params.id;

            if (this.type === "edit") {
                // 调用scriptDomain接口，判断当前操作的域名是否在接口返回的域名列表中，如果不在不允许提交，并提示
                const scriptDomainList = await this.$ctFetch<{ result: string[] }>(nscriptUrl.ScriptDomainList, {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                    },
                    body: { removeScriptDomain: false },
                });

                if (!scriptDomainList.result.includes(params.workScope)) {
                    const h = this.$createElement;
                    this.$msgbox({
                        title: "提示",
                        type: "error",
                        message: h(BusinessScriptTip, { props: { showIcon: false } }),
                        showCancelButton: false,
                        confirmButtonText: "确定"
                    // eslint-disable-next-line @typescript-eslint/no-empty-function
                    }).catch(() => {});

                    return;
                }
            }

            const reqUrl = this.type === "add" ? nscriptUrl.AddScript : nscriptUrl.UpdateScript;
            await this.$ctFetch(reqUrl, {
                method: "POST",
                body: { data: params },
                headers: {
                    "Content-Type": "application/json",
                },
            });
            this.$message.success("脚本下发成功!");
            this.$router.push({
                name: "nscript.business",
                query: {
                    isRefresh: "true",
                },
            });
        } catch (error) {
            const isCtiamError = CtiamCode.includes((error as any)?.data?.code);
            if (isCtiamError) {
                this.$errorHandler(error);
            } else {
                const errData = error as any;
                if (errData.data && errData.data.reason) {
                    return this.$message.error(errData.data.reason);
                }
            }
        } finally {
            this.loading = false;
        }
    }
}
</script>

<style lang="scss" scoped>
@media screen and (min-width: 992px) {
    .el-form {
        ::v-deep .el-form-item__content {
            width: 100%;
            > .el-input,
            .el-select {
                width: 90%;
            }
        }
    }
}
.ct-section-wrap {
    ::v-deep .el-scrollbar__wrap .el-scrollbar__view {
        height: auto !important;
    }
}
.src-code-box {
    display: flex;
    .src-code-content {
        width: 280px;
        white-space: break-spaces;
        padding-right: 8px;
        .src-code-content-title {
            color: #000;
            font-weight: 600;
            font-size: 14px;
        }

        .src-code-content-text1,
        .src-code-content-text2 {
            margin: 5px 0;
            color: #000;
            text-align: left;
            white-space: normal;
            .src-code-content-text-title {
                color: #000;
                font-weight: 600;
            }
        }
    }
}

.el-radio-group {
    display: flex;
    flex-wrap: wrap;
    ::v-deep .el-radio__input {
        // align-self: center;
        position: absolute;
        right: 10px;
    }
}
::v-deep .el-radio--small.is-bordered {
    height: auto;
    padding: 8px 15px 8px 10px;
    margin-right: 10px;
    margin-bottom: 10px;
    position: relative;
}

::v-deep .el-radio.is-bordered,
.el-radio.is-bordered {
    margin-left: 10px;
    margin-bottom: 10px;
    height: 120px;
}

.monaco-box {
    margin-top: 10px;
}

.dictionary-flootr {
    margin-top: 20px;
    display: flex;
    justify-content: center;
    align-content: center;
}
</style>
