/*
 * @Description: 用户分析相关
 * @Author: wang yuegong
 */

export type SearchParams = {
    product: string[];
    domainList: string[]; // 根据业务要求，域名列表是必要参数
    startTime: number; // 根据业务要求，时间是必要参数
    endTime: number;
};

// 访问用户区域分布 ListProvinceFBRData 接口数据
export interface AreaFetchDataItem {
    bandwidth: string; // 带宽
    flow: string; // 流量
    request: string; // 请求数
    flowProportion: string; // 流量占比(全球tab)
    requestCnt: string;
    province: string; // 省份编码
    provinceName: string; // 省份名称
    overseas?: number; // 0: 大陆数据, 1: 全球数据  2:全球数据(中国大陆)
    mainlandFlowProportion?: string; // 流量占比(中国大陆tab)
}

// 访问运营商分布 ListIspFBRData 接口数据
export interface IspFetchDataItem {
    topBandwidth: string; // 带宽
    flow: string; // 流量
    request: string; //  请求数
    flowPer: string; // 流量占比
    isp: string; // 运营商 id
    ispName: string; // 运营商名称
}
