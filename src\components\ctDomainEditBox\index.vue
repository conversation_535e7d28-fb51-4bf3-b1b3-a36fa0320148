<template>
    <div class="ct-edit-box">
        <div v-loading="loading" class="content-box common-full-box">
            <slot></slot>
        </div>
        <transition name="el-fade-in-linear">
            <div v-if="isEdit" class="submit-box">
                <!-- 提交表单状态不允许点击-->
                <el-button plain type="primary" :disabled="submitLoading" @click="handleCancel"
                    >返 回</el-button
                >
                <el-button type="primary" :loading="testLoading" @click="handleSubmit">提交保存</el-button>
            </div>
        </transition>
    </div>
</template>

<script>
import { SecurityAbilityModule } from "@/store/modules/securityAbility";
import LeaveInquiry from "@/components/ctLeaveInquiryNew";
import { get } from "lodash-es";

export default {
    name: "index",
    props: {
        // 内容加载loading
        loading: {
            type: Boolean,
            default: false,
        },
        // 提交loading
        submitLoading: {
            type: Boolean,
            default: false,
        },
        // 提交函数
        submitFn: {
            type: Function,
            default: null,
        },
    },
    data() {
        return {
            // submitLoading: false,
            testLoading: false,
        };
    },
    computed: {
        isEdit() {
            return SecurityAbilityModule.isEdit;
        },
    },
    methods: {
        async handleCancel() {
            await LeaveInquiry();

            SecurityAbilityModule.SET_MODULE_RENDER_KEY();
        },
        /**
         * 处理提交
         */
        async handleSubmit() {
            if (this.submitFn) {
                this.testLoading = true;
                // 利用async await的特性,因为submitFn本身就是一个async await的函数，所以在二次调用的时候，他并不是promise，所以不会被阻塞；而是一定会调用
                try {
                    await this.constructedPromise();
                    SecurityAbilityModule.SET_IS_EDIT(false);
                    window.custom.emit("handleEditDomainConfig", false);
                } catch (e) {
                    console.log(e, "-----我在这里");
                }
                // await this.submitFn();
            }

            this.testLoading = false;
            this.$emit("submit");
        },
        constructedPromise() {
            return new Promise((resolve, reject) => {
                // const submitFn = get(DomainConfigModule.currentModule, "handleSubmit");
                // submitFn(resolve, reject);
                this.submitFn(resolve, reject);
            });
        },
    },
};
</script>

<style scoped lang="scss">
.ct-edit-box {
    width: 100%;
    height: 100%;
    flex: 1;
    display: flex;
    flex-direction: column;
    position: relative;

    .btn-box {
        position: absolute;
        right: 20px;
        top: 5px;
    }

    .content-box {
        flex: 1;
        overflow: hidden;
        //overflow: auto;
    }

    .submit-box {
        width: 100%;
        height: 60px;
        background: #f5f5f5;
        text-align: center;
        line-height: 60px;
    }
}
</style>
