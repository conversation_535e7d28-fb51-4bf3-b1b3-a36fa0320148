.billing-wrap {
    >::v-deep.el-scrollbar {
        flex: unset;
        max-height: unset;

        &>.el-scrollbar__wrap {
            margin-bottom: unset !important;

            &>.el-scrollbar__view {
                overflow-y: unset;
            }
        }

        &>.el-scrollbar__bar {
            >.el-scrollbar__thumb {
                // cute包用important覆盖了滚动条背景色，所以这里需要使用important覆盖回去
                background-color: rgba(102, 102, 102, .3) !important;
            }
        }
    }
    >::v-deep.ct-section-header {
        overflow: unset;
    }
    &-card + &-card {
        margin-top: 20px;
    }
    &-card {
        margin: 0px;
        &--title {
            font-size: 14px;
            color: #333333;
            letter-spacing: 0;
            line-height: 24px;
            font-weight: 500;
        }
        &--tip {
            background: #f0f7ff;
            padding: 12px 16px;
            font-size: 12px;
            p {
                color: #193ba8;
                line-height: 24px;
            }
        }
        &--title + &--tip {
            margin-top: 8px;
        }
        &--btn {
            margin-top: 20px;
            &-tip {
                margin-top: 8px;
                font-size: 12px;
                color: #7C818C;
                line-height: 18px;
                font-weight: 400;
            }
        }
        ::v-deep .span-table tbody tr:hover>td { 
            background-color: transparent !important;
        }
        .el-table {
            ::v-deep .el-icon-arrow-down::before {
                font-family: $cute-icons-2;
                content: "\e601";
                font-size: 16px;
            }
            .el-progress {
                display: inline-block;
                margin-right: 12px;
                width: 200px;
            }
            .alert-icon {
                color: #FF8F34;
            }
            .status-icon {
                display: inline-block;
                width: 6px;
                height: 6px;
                margin-right: 8px;
                border-radius: 100%;
            }
            .status-0 {
                background: #999999;
            }
            .status-1 {
                background: #1AC45D;
            }
            .status-2 {
                background: #8C111D;
            }
            .status-3 {
                background: #FF4545;  
            }
            .status-4 {
                background: #FF8F34;
            }
            .status-5 {
                background: #999999;
            }
            .status-6 {
                background: #91BAFF;
            }

            .table-btn {
                display: block;
                line-height: 18px;
                margin: 0;
            }
        }
    }
}
