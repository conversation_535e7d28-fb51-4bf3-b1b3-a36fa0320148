const config = {
    ncdn: {
        moveDetail: [
            {
                sourceSelector: "#section_URLAuthentication",
                targetSelector: "#section_deny",
                hiddenTitle: true,
            },
            {
                sourceSelector: "#section_div8",
                targetSelector: "#section_div1",
            },
            {
                sourceSelector: "#section_div9",
                targetSelector: "#section_div8",
            },
            {
                sourceSelector: '#section_remoteSyncAuth',
                targetSelector: "#section_URLAuthentication",
            }
        ],
        moveDetailAnchor: [
            {
                sourceSelector: "#anchor_div9",
                targetSelector: "#anchor_div1",
            },
            {
                sourceSelector: "#anchor_div8",
                targetSelector: "#anchor_div1",
            },
        ],
        sortDetailAnchor: [
            {
                selector: "#anchor_URLAuthentication",
                hidden: true,
            },
        ],
    },
    aocdn: {},
};

const getConfig = () => {
    return window.__POWERED_BY_QIANKUN__ ? config.aocdn : config.ncdn;
};

export { getConfig };

/**
 * （id isQiankun/!isQiankun）

    ## accelerationConfig
    section_div0 基本信息
    section_div8 静态配置/缓存配置
    section_div9 动态配置
    section_div10 上传配置
    section_video 视频拖拉
    section_advancedConf 高级配置
    section_UDFScript UDFScript

    ## basicConfig
    section_div0 基本信息
    section_div1 回源配置
    section_backOriginRewrite 回源参数改写
    section_privateBucketOrigin 私有Bucket回源
    section_div2 请求协议
    section_div3 HTTPS配置
    section_div4 头部修改
    section_div5 文件处理
    section_URLAuthentication URL鉴权
    section_deny 访问控制
    section_div6 IPv6外链改造
    section_div7 WebSocket
    section_htmlForbid 页面优化
 */
