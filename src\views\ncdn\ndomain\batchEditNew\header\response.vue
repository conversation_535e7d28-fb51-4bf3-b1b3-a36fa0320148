<template>
    <batch-edit-checked v-model="checked">
        <el-form
            class="response-header-form"
            label-width="140px"
            label-position="right"
            :model="form"
            :rules="rules"
            ref="formRef"
            :disabled="!checked"
        >
            <el-form-item
                :label="$t('domain.detail.tab9')"
                prop="resp_headers"
                ref="respHeaders"
                class="ct-table-form-item table-form-item-style"
            >
                <span slot="label" class="res-header-style">
                    {{ $t("domain.detail.tab9") }}
                    <span class="question-style">
                        <el-tooltip placement="top" :content="$t('domain.editPage.tip6')">
                            <ct-svg-icon
                                icon-class="question-circle"
                                class-name="question-circle"
                            ></ct-svg-icon></el-tooltip></span
                ></span>
                <div class="ct-table-wrapper">
                    <el-table class="origin-table auto-table" :data="form.resp_headers">
                        <!-- 头部值 -->
                        <el-table-column prop="key" :label="$t('domain.editPage.label15')">
                            <template #default="scope">
                                <el-form-item
                                    label=""
                                    :prop="`resp_headers.` + scope.$index + `.key`"
                                    :rules="rules.resp_headers_key"
                                >
                                    <el-tooltip placement="top" :content="$t('domain.editPage.placeholder3')">
                                        <el-input
                                            v-model="scope.row.key"
                                            :placeholder="$t('domain.editPage.placeholder3')"
                                            class="input-style"
                                            @change="handleChange($event, scope.row)"
                                            :disabled="scope.row.disabled"
                                        ></el-input>
                                    </el-tooltip>
                                </el-form-item>
                            </template>
                        </el-table-column>
                        <!-- 跨域验证 -->
                        <el-table-column
                            v-if="isShowCorsCheckColumn"
                            prop="key"
                            :label="$t('domain.editPage.label33')"
                            width="110"
                        >
                            <template #default="scope">
                                <el-form-item
                                    v-show="isShowCorsCheck(scope.row)"
                                    label=""
                                    :prop="`resp_headers.` + scope.$index + `.cors_check`"
                                >
                                    <el-select
                                        v-model="scope.row.cors_check"
                                        @change="handleChange($event, scope.row)"
                                        :disabled="scope.row.disabled"
                                    >
                                        <el-option :label="$t('domain.detail.label96')" value="on" />
                                        <el-option :label="$t('domain.detail.label97')" value="off" />
                                    </el-select>
                                </el-form-item>
                            </template>
                        </el-table-column>
                        <!-- 取值 -->
                        <el-table-column prop="value" :label="$t('domain.detail.label44')">
                            <template #default="scope">
                                <el-form-item
                                    label=""
                                    :prop="`resp_headers.` + scope.$index + `.value`"
                                    :rules="rules.resp_headers_value"
                                >
                                    <el-tooltip
                                        placement="top"
                                        :content="getValueInputPlaceholder(scope.row)"
                                    >
                                        <el-input
                                            v-model="scope.row.value"
                                            @change="handleChange($event, scope.row)"
                                            :placeholder="getValueInputPlaceholder(scope.row)"
                                            class="input-style"
                                            :disabled="scope.row.disabled"
                                        ></el-input>
                                    </el-tooltip>
                                </el-form-item>
                            </template>
                        </el-table-column>
                        <!-- 操作 -->
                        <el-table-column :label="$t('domain.operate')" width="80">
                            <template #default="scope">
                                <el-button
                                    type="text"
                                    @click="onOperator(scope.row, 'delete', 'resp_headers', scope.$index)"
                                    >{{ $t("domain.delete") }}</el-button
                                >
                            </template>
                        </el-table-column>
                    </el-table>
                    <div class="flex-row-style button-box">
                        <el-button
                            class="btn"
                            type="text"
                            @click="onOperator(null, 'create', 'resp_headers')"
                        >
                            + {{ $t("domain.editPage.label10") }}
                        </el-button>
                    </div>
                </div>
            </el-form-item>
        </el-form>
    </batch-edit-checked>
</template>

<script lang="ts">
import { Component, Mixins } from "vue-property-decorator";
import ctSvgIcon from "@/components/ctSvgIcon/index.vue";
import i18n from "@/i18n/index";
import { cloneDeep } from "lodash-es";
import BatchItemMixin from "../mixins/batch.mixin";

interface ResponseHeaderItem {
    key: string;
    value: string;
    cors_check: string;
    disabled: boolean;
}

@Component({
    name: "BatchEditResponseHeader",
    components: {
        ctSvgIcon,
    },
})
export default class BatchEditResponseHeader extends Mixins(BatchItemMixin) {
    checked = false;
    form: {
        resp_headers: ResponseHeaderItem[];
    } = {
        resp_headers: [], // HTTP响应头
    };
    currentType = "create";
    rules = {
        resp_headers_key: [
            { required: true, message: i18n.t("domain.editPage.ruleTip1"), trigger: "blur" },
            {
                max: 300,
                message: i18n.t("domain.editPage.ruleTip2"),
                trigger: ["blur", "change"],
            },
            {
                pattern: "^[\\w-]+$",
                message: i18n.t("domain.editPage.ruleTip3"),
                trigger: ["blur", "change"],
            },
            {
                validator: (rule: any, value: any, callback: any) => {
                    const validIndex = rule.field.split(".")[1];
                    const respHeadersItem = this.form.resp_headers && this.form.resp_headers[validIndex];
                    const str1 = "content-type";
                    const str2 = "content-disposition";
                    if (
                        !respHeadersItem.disabled &&
                        (value.toUpperCase() === str1.toUpperCase() ||
                            value.toUpperCase() === str2.toUpperCase())
                    ) {
                        return callback(new Error(i18n.t("domain.editPage.tip28") as string));
                    } else callback();
                },
                trigger: ["blur", "change"],
            },
        ],
        resp_headers_value: [
            {
                max: 300,
                message: i18n.t("domain.editPage.ruleTip2"),
                trigger: ["blur", "change"],
            },
            {
                pattern:
                    "^[^\\u4e00-\\u9fa5\\u3002\\uff1f\\uff01\\uff0c\\u3001\\uff1b\\uff1a\\u201c\\u201d\\u2018\\u2019\\uff08\\uff09\\u300a\\u300b\\u3008\\u3009\\u3010\\u3011\\u300e\\u300f\\u300c\\u300d\\ufe43\\ufe44\\u3014\\u3015\\u2026\\u2014\\uff5e\\ufe4f\\uffe5]+$",
                message: i18n.t("domain.detail.tip29"),
                trigger: ["blur", "change"],
            },
        ],
    };
    get isShowCorsCheckColumn() {
        return this.form.resp_headers.some(item => item.key === "Access-Control-Allow-Origin");
    }
    handleChange(val: any, row: any) {
        if (row.key === "Access-Control-Allow-Origin") {
            !row.cors_check && (row.cors_check = "off");
        } else {
            row.cors_check = "";
        }
        this.$emit("onChange", this.form.resp_headers);
    }
    async onOperator(
        row: ResponseHeaderItem | null,
        currentType: "create" | "delete",
        tabName: "resp_headers",
        i = 0
    ) {
        this.currentType = currentType;

        if (currentType === "create") {
            const defaultFormMap = {
                resp_headers: { key: "", value: "", cors_check: "" },
            };
            row = defaultFormMap[tabName] as ResponseHeaderItem;
        }
        if (currentType === "delete") {
            const msgMap = {
                resp_headers: this.$t("domain.editPage.tip20") as string,
            };

            // 二次确认弹窗
            await this.$confirm(msgMap[tabName], this.$t("domain.delete") as string, {
                type: "warning",
            });
            this.form[tabName].splice(i, 1);
            this.$emit("onChange", this.form.resp_headers);
        } else {
            this.form[tabName].push(row as ResponseHeaderItem);
        }
    }
    isShowCorsCheck(row: ResponseHeaderItem) {
        return row.key === "Access-Control-Allow-Origin";
    }
    getValueInputPlaceholder(row: ResponseHeaderItem) {
        let placeholder = this.$t("domain.editPage.placeholder4");
        if (row.key === "Access-Control-Allow-Origin") {
            placeholder += ` ${this.$t("domain.detail.placeholder84")}`;
        }
        return placeholder;
    }
    get formData() {
        return cloneDeep(this.form);
    }
}
</script>

<style lang="scss" scoped>
@import "../commonStyle.scss";

.response-header-form {
    width: 100%;
}
</style>
