$theme-color: #3d73f5; // 主题色
$theme-color-light: rgba(250, 131, 52, 0.5); // 主题色浅
$bg-color-light: #f5f7fa;
$bg-color-silver: #fafafa;
$white: white;
$active-color: #2883d6;
$text-color-title: rgba(0, 0, 0, 0.85);
$text-color: #333333;
$text-color-light: #666666;

$disabled-color: #c7d2df;
$border-color: #DDDDDD;

$border-radius: 4px;

$d-color-primary: #1890ff;
$d-color-success: #67C23A;
$d-color-warning: #E6A23C;
$d-color-danger: #F56C6C;
$d-color-info: #909399;
$color-brand-6: #3d73f5;

// ----字体-------------
// 风格
// $d-font-family: "Microsoft YaHei";
// // 大小
// $d-font-mini: 12px;
// $d-font-small: 14px;
// $d-font-large: 20px;
// // 特殊字号
// $d-font-16: 16px;
// $d-font-22: 22px;
// $d-font-24: 24px;

// $d-font-sizes: (
//     "mini": $d-font-mini,
//     "small": $d-font-small,
//     "large": $d-font-large,
//     "24": $d-font-24,
//     "16": $d-font-16,
//     "22": $d-font-22
// );
// @each $key,$val in $d-font-sizes {
//     .d-font-#{$key} {
//         font-size: $val;
//     }
// };
