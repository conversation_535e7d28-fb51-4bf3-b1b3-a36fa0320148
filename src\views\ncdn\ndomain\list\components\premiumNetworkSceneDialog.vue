<template>
    <el-dialog
      :title="$t('domain.list.note')"
      :append-to-body="true"
      :close-on-click-modal="false"
      :visible.sync="dialogVisible"
      :before-close="cancel"
      class="tip-dialog"
      width="560px"
      :show-close="true"
    >
        <div class="text-style">
          <i class="el-icon-warning icon-style"></i>
          <span>{{ $t("domain.list.tableLabelPremiumNetworkTip5") }}</span>
        </div>
        <div class="scene-wrap" v-loading="updateLoading">
          <!-- 场景1限制只有海外加速才能配置，场景2限制只有全球加速才能配置，场景3限制只有海外加速和全球加速才能配置 -->
          <el-radio-group v-model="virtual_scene" @change="virtual_scene_change">
            <el-radio :label="1" :disabled="rowData.areaScope !== 2">{{
              $t("domain.list.scene[0]")
            }}</el-radio>
            <el-radio :label="2" :disabled="rowData.areaScope !== 3">{{
              $t("domain.list.scene[1]")
            }}</el-radio>
            <el-radio :label="3" :disabled="![2, 3].includes(rowData.areaScope)">{{
              $t("domain.list.scene[2]")
            }}</el-radio>
          </el-radio-group>
        </div>
        <el-alert title="" type="info" :closable="false" class="ct-alert">
          <template slot="title">
            <div>{{ $t("domain.list.tableLabelPremiumNetworkTip1") }}</div>
            <div>{{ $t("domain.list.tableLabelPremiumNetworkTip2") }}</div>
            <div>{{ $t("domain.list.tableLabelPremiumNetworkTip3") }}</div>
          </template>
        </el-alert>
        <div slot="footer" class="btns">
          <el-button @click="cancel" :disabled="updateLoading">{{ $t("domain.cancel") }}</el-button>
          <el-button
            type="primary"
            @click="submit"
            :loading="updateLoading"
            :disabled="updateLoading || virtual_scene === null || virtual_scene === ''"
          >{{ $t("common.dialog.submit") }}</el-button>
        </div>
    </el-dialog>
</template>

<script lang="ts">
import { Component, Prop, Vue, Watch } from "vue-property-decorator";

@Component({})
export default class UpdateDialog extends Vue {
    @Prop({ default: false, type: Boolean }) private dialogVisible!: boolean;
    @Prop({ default: {}, type: Object }) private rowData!: {};
    @Prop({ default: false, type: Boolean }) private updateLoading!: boolean;

    private virtual_scene = null

    private async submit() {
      this.$emit("submit");
    }
    private cancel() {
      this.$emit("cancel", "sceneDialogVisible");
    }
    private virtual_scene_change() {
      this.$emit("virtualSceneChange", this.virtual_scene);
    }

    @Watch("dialogVisible", { immediate: true })
    private dialogVisibleChange(val: boolean) {
        this.virtual_scene = null;
    }
}
</script>

<style lang="scss" scoped>
.tip-dialog {
  .ct-alert {
    margin-left: 26px;
    margin-right: 20px;
    ::v-deep {
      .el-alert {
        align-items: center;
        padding: 12px;
      }
    }
  }
  .text1 {
    width: 416px;
    height: 56px;
    padding-top: 20px;
    background-color: $color-master-bg-light-2;
  }
  .text2 {
    margin-top: 20px;
    color: $color-warning;
  }
  .text-style {
    margin-bottom: 8px;
    font-weight: bold;
  }
  .icon-style {
    color: $color-warning;
    margin-right: 12px;
    font-size: $text-size-lg;
  }
  .scene-wrap {
    margin: 16px 0 16px 26px;
  }
}
</style>
