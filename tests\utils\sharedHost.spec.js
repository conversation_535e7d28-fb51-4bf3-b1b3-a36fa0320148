import { describe, it, expect, beforeEach } from '@jest/globals';
import { registerConfigModule } from '@/registry/configModuleRegistry';

describe('SharedHost Component toApi Tests', () => {
    let sharedHostModule;
    let form;

    beforeEach(() => {
        // 重新注册模块以获取最新的 toApi 函数
        registerConfigModule('shared_host', {
            fields: {
                shared_cache_enable: 'off',
                shared_host: null,
            },
            onModuleChange(form, val) {
                return {
                    shared_cache_enable: val.shared_cache_enable,
                    shared_host: val.shared_host
                };
            },
            toApi(form, { isLocked }) {
                if (isLocked) {
                    return {};
                }
                return {
                    shared_host: form.shared_host
                };
            },
            displayCondition(form) {
                return form.statics_ability === 'on';
            }
        });

        // 获取注册的模块
        sharedHostModule = require('@/registry/configModuleRegistry').getModule('shared_host');

        // 初始化 form 对象
        form = {
            shared_cache_enable: 'on',
            shared_host: 'example.com'
        };
    });

    describe('toApi function', () => {
        it('should return empty object when isLocked is true', () => {
            const result = sharedHostModule.toApi(form, { isLocked: true });
            expect(result).toEqual({});
        });

        it('should return correct shared_host config when isLocked is false', () => {
            const result = sharedHostModule.toApi(form, { isLocked: false });
            expect(result).toEqual({
                shared_host: 'example.com'
            });
        });

        it('should handle null shared_host value', () => {
            form.shared_host = null;
            const result = sharedHostModule.toApi(form, { isLocked: false });
            expect(result).toEqual({
                shared_host: null
            });
        });

        it('should handle empty shared_host value', () => {
            form.shared_host = '';
            const result = sharedHostModule.toApi(form, { isLocked: false });
            expect(result).toEqual({
                shared_host: ''
            });
        });

        it('should handle undefined shared_host value', () => {
            form.shared_host = undefined;
            const result = sharedHostModule.toApi(form, { isLocked: false });
            expect(result).toEqual({
                shared_host: undefined
            });
        });
    });
});