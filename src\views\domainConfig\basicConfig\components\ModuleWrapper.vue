<template>
    <!-- 可插拔配置:配置项包装组件 -->
    <div>
        <div v-if="isEnabled && isMasterSwitchEnabled && addon">
            <slot></slot>
        </div>
    </div>
</template>

<script>
import { ConfigModulesModule } from '@/store/modules/configModules';

export default {
    name: 'ModuleWrapper',
    props: {
        moduleName: {
            type: String,
            required: true
        },
        addon: {
            type: Boolean,
            default: true
        }
    },
    computed: {
        isMasterSwitchEnabled() {
            return ConfigModulesModule.isMasterSwitchEnabled;
        },
        isEnabled() {
            return ConfigModulesModule.isModuleEnabled(this.moduleName);
        }
    }
};
</script>
