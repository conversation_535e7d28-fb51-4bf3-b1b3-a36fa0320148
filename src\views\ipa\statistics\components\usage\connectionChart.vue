<template>
    <el-card>
        <el-radio-group v-model="queryForm.connectionNumberType" class="connection-number-type">
            <el-radio-button :label="$t('statistics.eas.tab[2]')"></el-radio-button>
            <el-radio-button :label="$t('statistics.eas.tip15')"></el-radio-button>
        </el-radio-group>
        <template>
            <div class="total" v-if="queryForm.connectionNumberType === $t('statistics.eas.tab[2]')">
                <div class="tip">
                    {{ $t('statistics.eas.excel.tip1') }}
                    <span class="num">
                        {{ fetchData.totalConnections |
                            convertTenThousand2Int(`${$t("statistics.eas.connections.tip1", { value: "" })}`) }}
                    </span>
                </div>
                <div class="tip">
                    {{ $t('statistics.eas.excel.tip3') }}
                    <span class="num">
                        {{ fetchData.peakQueryConnections |
                            convertTenThousand2Int(`${$t("statistics.eas.connections.tip1", { value: "" })}`) }}
                    </span>
                    <span class="date">{{ fetchData.peakQueryConnectionsTimestamp || "" }}</span>
                </div>
                <div class="tip">
                    {{ $t('statistics.eas.excel.tip4') }}
                    <span class="num">
                        {{ fetchData.peak95QueryConnections |
                            convertTenThousand2Int(`${$t("statistics.eas.connections.tip1", { value: "" })}`) }}
                    </span>
                    <span class="date">{{ fetchData.peak95QueryConnectionsTimestamp || "" }}</span>
                </div>
            </div>
            <div class="total" v-else>
                <div class="tip">
                    {{ $t('statistics.eas.excel.tip5') }}
                    <span class="num">
                        {{ fetchData.peakQueryConnections |
                            convertTenThousand2Int(`${$t("statistics.eas.connections.tip2", { value: "" })}`) }}
                    </span>
                    <span class="date">{{ fetchData.peakQueryConnectionsTimestamp || "" }}</span>
                </div>
                <div class="tip">
                    {{ $t('statistics.eas.excel.tip6') }}
                    <span class="num">
                        {{ fetchData.peak95QueryConnections |
                            convertTenThousand2Int(`${$t("statistics.eas.connections.tip2", { value: "" })}`) }}
                    </span>
                    <span class="date">{{ fetchData.peak95QueryConnectionsTimestamp || "" }}</span>
                </div>
            </div>
        </template>

        <v-chart class="statistic-chart-connection" v-loading="loading"
            :element-loading-text="$t('statistics.common.chart.loading')" autoresize theme="cdn" :options="options" />

        <ct-tip>
            {{ $t("statistics.eas.tip13") }}
            <el-tooltip effect="dark" :content="$t('statistics.common.searchDownloadContent')" placement="right">
                <i class="el-icon-download ipa-download-icon usage-download-icon"
                    @click="() => $refs.connectionTable.downloadTable($t('statistics.eas.tab[2]'))"></i>
            </el-tooltip>
        </ct-tip>
        <usage-table ref="connectionTable" />
    </el-card>
</template>

<script>
/* eslint-disable @typescript-eslint/camelcase */
import { statistics as StatisticsUrl } from "@/config/url/ipa/statistics";

// ***单位换算：ms->"YYYY-MM-DD HH:mm:ss"
import { timeFormat } from "@/filters";
import mixChart from "../mixChart";
import chartShim from "../chartShim";
import usageTable from "./usageTable.vue";
import { THEME_AREA_STYLE } from "@/config/echart/cdn-theme";
import { themeColorArr } from "@cdnplus/common/config/echart/theme";

export default {
    mixins: [mixChart, chartShim],
    components: { usageTable },
    data() {
        return {
            series: [],
            activeStatusFlow: "flow",
            flowDataList: [],
            bandWithDataList: [],
            charData: [],
            xData: [],
            loading: false,
            downloadDataList: [],

            showBandwidth: true,
            fetchData: {
                "5min": [],
                daily: [],
                peakQueryConnections: 0,
                peak95QueryConnections: 0,
                totalConnections: 0,
                peakQueryConnectionsTimestamp: "",
                avgQueryflow: 0,
            },
            // fetchData2: {
            //     "5min": [],
            //     daily: [],
            //     peakQueryConnections: 0,
            //     peak95QueryConnections: 0,
            //     totalConnections: 0,
            //     avgQueryflow: 0,
            // },
            // ***备份，用于监听时传参1
            reqParamLocal: {},
            options: null
        };
    },
    props: {},
    computed: {
        getUnit() {
            return this.queryForm.connectionNumberType === this.$t('statistics.eas.tab[2]') ?
                `${this.$t("statistics.eas.connections.unit1")}` :
                `${this.$t("statistics.eas.connections.unit2")}`
        },
        getTooltipUnit() {
            return this.queryForm.connectionNumberType === this.$t('statistics.eas.tab[2]') ?
                "statistics.eas.connections.tip1" :
                "statistics.eas.connections.tip2";
        }
    },
    watch: {
        "queryForm.connectionNumberType": {
            handler() {
                this.updateAgency();
            },
        },
    },
    filters: {},
    methods: {
        async initData(reqParam) {
            // ***备份，用于监听时传参2
            this.reqParamLocal = JSON.parse(JSON.stringify(reqParam));

            this.loading = true;
            this.downloadDataList = [];
            const { result } = await this.getConnectionsList(reqParam);
            this.fetchData = {
                "5min": [],
                daily: [],
                peakQueryConnections: result.peek.connections,
                peak95QueryConnections: result.valley.connections,
                totalConnections: result.total,
                avgQueryflow: 0,
                peakQueryConnectionsTimestamp: timeFormat(result.peek.timestamp),
                peak95QueryConnectionsTimestamp: timeFormat(result.valley.timestamp),
            };
            this.xData = []; //重置
            this.charData = []; //重置
            result.list.map(item => {
                this.xData.push(item.timestamp * 1000); // ***s->ms
                this.charData.push(item.connections);
                this.downloadDataList.push({
                    value: item.connections,
                    date: item.timestamp * 1000,
                });
            });
            this.renderChart();
            this.loading = false;
        },
        getConnectionsList(data) {
            if (!data) {
                data = { account_id: this.$store.state.user.userInfo.userId };
            }
            return this.$ctFetch(StatisticsUrl.connectionsList, {
                method: "POST",
                transferType: "json",
                body: {
                    data: {
                        ...data,
                        type: this.queryForm.connectionNumberType === `${this.$t("statistics.eas.tab[2]")}` ? 0 : 1
                    },
                },
            });
        },

        renderChart() {
            let count = 0;
            const option = {
                tooltip: {
                    trigger: "axis",
                    formatter: ([params]) => {
                        const time = this.$dayjs(params.axisValue * 1).format("YYYY-MM-DD HH:mm:ss");
                        const value = params.value;
                        return `
                                <div>${time}</div>
                                <div>${this.queryForm.connectionNumberType}：${this.$t(this.getTooltipUnit, { value })}</div>
                                `;
                    },
                },
                xAxis: {
                    // type: 'time',
                    type: "category",
                    boundaryGap: false,
                    data: this.xData,
                    axisLabel: {
                        formatter: value => {
                            const time = this.$dayjs(value * 1).format("MM-DD HH:mm");
                            const arr = time.split(" ");
                            count += 1;
                            if (count % 2 === 1) return `${arr[0]}\n${arr[1]}`;
                        },
                    },
                },
                yAxis: {
                    name: this.getUnit,
                    type: "value",
                    // boundaryGap: [0, "100%"],
                },
                series: [
                    {
                        name: `${this.$t("statistics.provider.traffic")}`,
                        type: "line",
                        data: this.charData,
                        areaStyle: THEME_AREA_STYLE[themeColorArr[0]],
                    },
                ],
            };
            Object.assign(option, this.toolBox);

            this.options = option;
        },
        /**
         * 导出处理函数
         */
        tableToExcel() {
            let str = `${this.$t("statistics.usageQuery.BandwidthFlow.tableToExcel.excelColumn1")},${this.queryForm.connectionNumberType},\n`;

            const { fetchData } = this;
            const { totalConnections, peakQueryConnections, peak95QueryConnections } = fetchData;

            // 输出格式
            str += this.downloadDataList.reduce((str, item) => {
                str += `${timeFormat(item.date)},`;
                str += item.value + "\n";
                return str;
            }, "");

            if (this.queryForm.connectionNumberType === `${this.$t("statistics.eas.tab[2]")}`) {
                str += `${this.$t("statistics.eas.tip16")},${totalConnections} \n ${this.$t("statistics.eas.tip17")},${peakQueryConnections} \n ${this.$t("statistics.eas.tip18")},${peak95QueryConnections} \n`;
            } else {
                str += `${this.$t("statistics.eas.tip19")},${peakQueryConnections} \n ${this.$t("statistics.eas.tip20")},${peak95QueryConnections} \n`;
            }

            const timeRange = this.reqParamsFromParentBackup.timeRange;
            const startTime = timeRange[0]
                ? this.$dayjs(timeRange[0]).startOf("day").format("YYYYMMDDHHmmss")
                : "";
            const endTime = timeRange[1]
                ? this.$dayjs(timeRange[1]).endOf("day").format("YYYYMMDDHHmmss")
                : "";
            const string = startTime && endTime ? `${startTime}-${endTime}` : "";
            const name = `${this.queryForm.connectionNumberType === `${this.$t("statistics.eas.tab[2]")}` ? this.$t("statistics.eas.tip21") : this.$t("statistics.eas.tip29")}_${string}`;
            this.downloadExcel({
                name: name,
                str,
            });
        },
    },
};
</script>
<style lang="scss" scoped>
.sub-query-wrapper {
    margin-bottom: 12px;
}

.connection-number-type {
    margin-bottom: 12px;
}

// 统计数字
.total {
    width: 100%;
    text-align: left;
    font-size: 14px;
    color: #333333;
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 8px;

    .num {
        margin-left: 4px;
    }

    .date {
        color: #666666;
        font-size: 12px;
        margin-left: 4px;
    }
}

// 内容区
.statistic-chart-connection {
    clear: both;
    width: 100%;
    height: 380px;
}

.ml-12 {
    margin-left: 12px;
}
</style>
