<template>
    <div class="area-select el-select" v-clickoutside="handleClose">
        <div class="area-select__tags">
            <el-tag size="mini" type="info">
                {{
                    selectedAreaLocal[-1] || selectedCount === 0
                        ? $t("common.areaSelect.allArea")
                        : $tc("common.areaSelect.selectedCount", selectedCount)
                }}
            </el-tag>
        </div>
        <div
            class="area-select__input el-input"
            @click.stop.prevent="toggleDropdown"
            ref="reference"
            :class="{ 'is-focus': visible }"
        >
            <input
                ref="input"
                type="text"
                class="el-input__inner"
                :placeholder="selectedCount ? '' : placeholder"
                readonly
                @focus="handleFocus"
                @keydown.esc.stop.prevent="visible = false"
                @keydown.tab="visible = false"
            />
            <i :class="['el-icon-arrow-up', { 'is-reverse': visible }]"></i>
        </div>
        <transition name="el-zoom-in-top">
            <div
                v-if="areaList.length"
                v-show="visible"
                class="aocdn-ignore-area-select__dropdown el-popper"
                ref="popper"
            >
                <div class="aocdn-ignore-area-select__dropdown-wrapper">
                    <div class="area-selector-section">
                        <label class="area-selector-section--label">{{ areaList[0].label }}</label>
                        <div class="area-selector-section--content">
                            <div
                                v-for="(region, index) in areaList[0].children"
                                :key="index"
                                class="area-selector-section--content-row"
                            >
                                <div class="area-selector-section--content-row__left">
                                    <el-checkbox
                                        class="aocdn-ignore-region-checkbox"
                                        v-model="selectedAreaLocal[region.value]"
                                        :label="region.label"
                                        @change="handleCheckAllChange"
                                    />
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="area-selector-section">
                        <label class="area-selector-section--label">{{ areaList[1].label }}</label>
                        <div class="area-selector-section--content">
                            <div
                                v-for="(region, index) in areaList[1].children"
                                :key="index"
                                class="area-selector-section--content-row"
                            >
                                <div class="area-selector-section--content-row__left">
                                    <el-checkbox
                                        class="aocdn-ignore-region-checkbox"
                                        v-model="selectedAreaLocal[region.value]"
                                        :label="region.label"
                                        :disabled="disabledFlag.province"
                                        @change="handleRegionAllSelect($event, region.value, 'province')"
                                    />
                                </div>
                                <el-checkbox-group
                                    v-if="!!region.children"
                                    v-model="regionChecklist.province[region.value]"
                                    :disabled="disabledFlag.province"
                                    class="area-selector-section--content-row__right"
                                >
                                    <el-checkbox
                                        class="aocdn-ignore-region-checkbox-area"
                                        v-for="province in region.children"
                                        :key="province.value"
                                        :label="province.value"
                                        @change="handleAreaChange($event, region.value, 'province')"
                                    >
                                        {{ province.label }}
                                    </el-checkbox>
                                </el-checkbox-group>
                            </div>
                        </div>
                    </div>
                    <div class="area-selector-section" v-if="enableCity">
                        <label class="area-selector-section--label">{{ areaList[2].label }}</label>
                        <div class="area-selector-section--content">
                            <div
                                v-for="(region, index) in areaList[2].children"
                                :key="index"
                                class="area-selector-section--content-row"
                            >
                                <div class="area-selector-section--content-row__left">
                                    <el-checkbox
                                        class="aocdn-ignore-region-checkbox"
                                        v-model="selectedAreaLocal[region.value]"
                                        :label="region.label"
                                        @change="handleRegionAllSelect($event, region.value, 'city')"
                                        :disabled="disabledFlag.city"
                                    />
                                </div>
                                <el-checkbox-group
                                    v-if="!!region.children"
                                    v-model="regionChecklist.city[region.value]"
                                    class="area-selector-section--content-row__right"
                                >
                                    <el-checkbox
                                        class="aocdn-ignore-region-checkbox-area"
                                        v-for="city in region.children"
                                        :key="city.value"
                                        :label="city.value"
                                        @change="handleAreaChange($event, region.value, 'city')"
                                        :disabled="disabledFlag.city"
                                    >
                                        {{ city.label }}
                                    </el-checkbox>
                                </el-checkbox-group>
                            </div>
                        </div>
                    </div>
                    <div class="area-selector-section">
                        <label class="area-selector-section--label">{{ areaListContinent.label }}</label>
                        <div class="area-selector-section--content">
                            <div
                                v-for="(region, index) in areaListContinent.children"
                                :key="index"
                                class="area-selector-section--content-row"
                            >
                                <div class="area-selector-section--content-row__left">
                                    <el-checkbox
                                        class="aocdn-ignore-region-checkbox"
                                        v-model="selectedAreaLocal[region.value]"
                                        :disabled="disabledFlag.continent"
                                        :label="region.label"
                                        @change="handleRegionAllSelect($event, region.value, 'continent')"
                                    />
                                </div>
                                <el-checkbox-group
                                    v-model="regionChecklist.continent[region.value]"
                                    :disabled="disabledFlag.continent"
                                    class="area-selector-section--content-row__right"
                                >
                                    <el-checkbox
                                        class="aocdn-ignore-region-checkbox-area"
                                        v-for="province in region.children"
                                        :key="province.value"
                                        :label="province.value"
                                        @change="handleAreaChange($event, region.value, 'continent')"
                                    >
                                        {{ province.label }}
                                    </el-checkbox>
                                </el-checkbox-group>
                            </div>
                        </div>
                    </div>
                    <div class="area-selector-section">
                        <label class="area-selector-section--label">{{ areaListCountry.label }}</label>
                        <div class="area-selector-section--content">
                            <div
                                v-for="(region, index) in areaListCountry.children"
                                :key="index"
                                class="area-selector-section--content-row"
                            >
                                <div class="area-selector-section--content-row__left">
                                    <el-checkbox
                                        class="aocdn-ignore-region-checkbox"
                                        v-model="selectedAreaLocal[region.value]"
                                        :disabled="disabledFlag.country"
                                        :label="region.label"
                                        @change="handleRegionAllSelect($event, region.value, 'country')"
                                    />
                                </div>
                                <el-checkbox-group
                                    v-model="regionChecklist.country[region.value]"
                                    class="area-selector-section--content-row__right"
                                    :disabled="disabledFlag.country"
                                >
                                    <el-checkbox
                                        class="aocdn-ignore-region-checkbox-area"
                                        v-for="province in region.children"
                                        :key="province.value"
                                        :label="province.value"
                                        @change="handleAreaChange($event, region.value, 'country')"
                                    >
                                        {{ province.label }}
                                    </el-checkbox>
                                </el-checkbox-group>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </transition>
    </div>
</template>
<script lang="ts">
import { Component, Prop, Watch } from "vue-property-decorator";
import { mixins } from "vue-class-component";
// 引入element封装的popper工具
import Popper from "element-ui/lib/utils/vue-popper";
import Clickoutside from "element-ui/lib/utils/clickoutside";

type Province = {
    value: string;
    label: string;
    continent_code?: string;
    continent_region_code?: string;
};
type BooleanObject = {
    [key: string]: boolean;
};
type Region = { label: string; children: Province[]; value: string };

// 地区选择
@Component({
    directives: { Clickoutside },
    // model: {
    //     prop: "selectedArea",
    //     event: "select",
    // },
})
export default class AreaSelect extends mixins(Popper) {
    @Prop({ default: true }) private appendToBody!: boolean;
    @Prop({ default: () => [] }) private areaList!: Array<any>;
    @Prop({ default: false }) private multiple!: boolean;
    @Prop({ default: "" }) private placeholder!: string;
    // @Prop({ default: () => [] }) private selectedArea!: string[];
    @Prop({
        default: () => {
            return {
                province: [],
                continent: [],
                continentRegion: [],
                city: [],
            };
        },
    })
    private selectedArea!: any;
    @Prop({ default: false }) private oversea!: boolean;
    @Prop({ default: true }) private popperAppendToBody!: boolean;
    @Prop({ default: false }) private disabled!: boolean;
    @Prop({ default: false }) private enableCity!: boolean;
    @Prop({ default: false }) private isFromIpa!: boolean;
    private selectedAreaLocal: BooleanObject = {};
    private visible = false;

    private locked = false;
    private referenceElm?: Element;
    private popperElm?: Element;
    private isAllSelected = true;
    private regionChecklist: any = {
        province: {},
        continent: {},
        country: {},
        city: {},
    };
    private regionMap: any = {
        province: {},
        continent: {},
        country: {},
        city: {},
    };
    private continentMap: any = {
        continent: [],
        continentRegion: [],
    };
    private disabledFlag: BooleanObject = {
        province: false,
        continent: false,
        country: false,
        city: false,
    };

    get selectedCount() {
        return (
            (this.selectedArea.province?.length || 0) +
            (this.selectedArea.continent?.length || 0) +
            (this.selectedArea.continentRegion?.length || 0) +
            (this.selectedArea.city?.length || 0)
        );
    }

    private mounted() {
        this.referenceElm = (this.$refs.reference as Vue)?.$el;
        this.popperElm = (this.$refs.popper as Vue)?.$el;
        this.createPopper();
        this.$on("updatePopper", () => {
            if (this.visible) this.updatePopper();
        });
        this.$on("destroyPopper", this.destroyPopper);
    }

    get areaListContinent() {
        return this.areaList[this.isFromIpa ? 2 : 3];
    }
    get areaListCountry() {
        return this.areaList[this.isFromIpa ? 3 : 4];
    }

    private initData() {
        this.regionChecklist = {
            province: {},
            continent: {},
            country: {},
            city: {},
        };
        this.regionMap = {
            province: {},
            continent: {},
            country: {},
            city: {},
        };
        let index;
        for (const key in this.regionMap) {
            switch (key) {
                case "province":
                    index = 1;
                    break;
                case "city":
                    index = 2;
                    break;
                case "continent":
                    index = this.isFromIpa ? 2 : 3;
                    break;
                case "country":
                    index = this.isFromIpa ? 3 : 4;
                    break;
                default:
                    return;
            }
            this.areaList[index] &&
                this.areaList[index].children?.forEach((region: Region) => {
                    this.$set(this.regionChecklist[key], region.value, []);
                    region.children?.forEach((province: Province) => {
                        if (Array.isArray(this.regionMap[key][region.value])) {
                            this.regionMap[key][region.value].push(province.value);
                        } else {
                            this.regionMap[key][region.value] = [province.value];
                        }
                        if (province.continent_code) {
                            this.continentMap.continent.push(province.continent_code);
                        }
                        if (province.continent_region_code) {
                            this.continentMap.continentRegion.push(province.continent_region_code);
                        }
                        // 回填
                        switch (key) {
                            case "province":
                            case "country":
                                if (this.selectedArea.province?.includes(province.value)) {
                                    this.regionChecklist[key][region.value].push(province.value);
                                }
                                break;
                            case "city":
                                if (this.selectedArea.city?.includes(province.value)) {
                                    this.regionChecklist[key][region.value].push(province.value);
                                }
                                break;
                            case "continent":
                                if (
                                    this.selectedArea.continent
                                        .concat(this.selectedArea.continentRegion)
                                        ?.includes(province.value)
                                ) {
                                    this.regionChecklist[key][region.value].push(province.value);
                                }
                                break;
                        }
                    });
                });
        }
    }

    private handleAreaChange(val: boolean, region: string, section: string) {
        // 更新上级region
        this.selectedAreaLocal[region] =
            this.regionChecklist[section][region].length === this.regionMap[section][region].length;
        // 全选置false
        !val && (this.selectedAreaLocal["-1"] = false);

        if (
            Object.keys(this.regionChecklist[section]).some(
                key => !!this.regionChecklist[section][key].length
            )
        ) {
            Object.keys(this.disabledFlag).forEach(key => {
                if (key === section) {
                    this.disabledFlag[key] = false;
                } else {
                    this.disabledFlag[key] = true;
                    this.handleDaisabled(key);
                }
            });
        } else {
            Object.keys(this.disabledFlag).forEach(key => {
                this.disabledFlag[key] = false;
                this.handleDaisabled(key);
            });
        }
    }

    private handleRegionAllSelect(val: boolean, region: string, section: string) {
        if (region === "china") {
            this.handleCheckMainland(val);
        } else {
            // 更新下级checkbox
            this.regionChecklist[section][region] = val ? [...this.regionMap[section][region]] : [];
        }
        // 全选置false
        !val && (this.selectedAreaLocal["-1"] = false);

        // 更新disabled状态
        if (
            Object.keys(this.regionChecklist[section]).some(
                key => !!this.regionChecklist[section][key].length
            )
        ) {
            Object.keys(this.disabledFlag).forEach(key => {
                if (key === section) {
                    this.disabledFlag[key] = false;
                } else {
                    this.disabledFlag[key] = true;
                    this.handleDaisabled(key);
                }
            });
        } else {
            Object.keys(this.disabledFlag).forEach(key => {
                this.disabledFlag[key] = false;
                this.handleDaisabled(key);
            });
        }
    }

    private handleCheckMainland(val: boolean) {
        Object.keys(this.regionMap["province"]).forEach(region => {
            if (region !== "0000") {
                this.selectedAreaLocal[region] = val;
                this.regionChecklist["province"][region] = val ? this.regionMap["province"][region] : [];
            }
        });
    }

    private handleCheckAllChange(val: boolean) {
        // 中国内地没有对应的下级，不会向上勾选，需要单独勾选
        this.selectedAreaLocal["china"] = val;
        // 全选
        Object.keys(this.regionMap).forEach(section => {
            Object.keys(this.regionMap[section]).forEach(region => {
                this.selectedAreaLocal[region] = val;
                this.regionChecklist[section][region] = val ? this.regionMap[section][region] : [];
            });
        });
        // 更新disabled状态
        Object.keys(this.disabledFlag).forEach(key => {
            this.disabledFlag[key] = false;
        });
    }

    private handleDaisabled(section: string) {
        if (section === "province") {
            // 中国内地没有对应的下级，不会向上勾选，需要单独勾选
            this.selectedAreaLocal["china"] = false;
        }
        Object.keys(this.regionMap[section]).forEach(region => {
            this.selectedAreaLocal[region] = false;
            this.regionChecklist[section][region] = [];
        });
    }
    private emitData() {
        const selectedArea: any = {
            province: [],
            continent: [],
            continentRegion: [],
            city: [],
        };
        if (!this.selectedAreaLocal["-1"]) {
            Object.keys(this.regionMap).forEach(section => {
                switch (section) {
                    case "province":
                    case "country":
                        selectedArea.province.push(
                            ...Object.keys(this.regionChecklist[section]).reduce(
                                (preArr: Array<string>, curKey: string) => {
                                    return preArr.concat(this.regionChecklist[section][curKey]);
                                },
                                []
                            )
                        );
                        break;
                    case "city":
                        selectedArea.city.push(
                            ...Object.keys(this.regionChecklist[section]).reduce(
                                (preArr: Array<string>, curKey: string) => {
                                    return preArr.concat(this.regionChecklist[section][curKey]);
                                },
                                []
                            )
                        );
                        break;
                    case "continent":
                        this.regionChecklist[section][Object.keys(this.regionChecklist[section])[0]]?.forEach(
                            (item: string) => {
                                if (this.continentMap.continent.includes(item)) {
                                    selectedArea.continent.push(item);
                                } else if (this.continentMap.continentRegion.includes(item)) {
                                    selectedArea.continentRegion.push(item);
                                }
                            }
                        );
                        break;
                }
            });
        }

        this.$emit("select", selectedArea);
    }
    private handleClose() {
        this.visible = false;
        this.emitData();
    }
    private handleFocus() {
        // 点击下拉按钮会先触发focus事件再触发click事件，所以要加个锁防止下拉框消失，同时要清除锁防止关闭不了
        if (!this.visible) {
            this.locked = true;
            this.visible = true;
            setTimeout(() => {
                this.locked = false;
            }, 300);
        }
    }
    private toggleDropdown() {
        if (this.locked) this.locked = false;
        else this.visible = !this.visible;
    }

    @Watch("areaList", { immediate: true })
    onAreaListChange(val: Region[]) {
        this.initData();
    }
    @Watch("visible")
    private onVisibleChange(val: boolean) {
        if (val) {
            if (this.selectedCount === 0) {
                this.selectedAreaLocal["-1"] = true;
                this.handleCheckAllChange(true);
            }
            this.updatePopper();
        } else {
            (this.$refs.input as HTMLInputElement).blur(); // 关闭时清除边框
            this.$emit("blur");
        }
    }
    @Watch("selectedArea", { immediate: true })
    private onSelectedAreaChange(val: string[]) {
        this.initData();
    }
    @Watch("enableCity")
    private onEnableCityChange(val: boolean) {
        // 切换到不支持city的tab，如果切换前有勾选city，则全选
        if (!val && Object.values(this.regionChecklist.city).flat()?.length) {
            this.selectedAreaLocal["-1"] = true;
            this.handleCheckAllChange(true);
        }
    }
}
</script>
<style lang="scss" scoped>
.area-select {
    color: #c0c4cc;
    display: inline-block;
    position: relative;
    width: 215px;
    line-height: 32px;
}

.area-select__tags {
    position: absolute;
    z-index: 100;
    line-height: 32px;
    max-width: calc(100% - 60px);
    top: 50%;
    transform: translate(0, -50%);

    .el-tag {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        vertical-align: middle;
        width: 100%;
    }
}

.area-select__input {
    position: relative;
    width: 100%;

    input {
        outline: none;
        cursor: pointer;
        width: 100%;
        height: 32.1px;
        box-sizing: border-box;
        line-height: 32px;
        border: 1px solid #dcdfe6;

        &.is-focus {
            border: 1px solid #fa8334;
        }

        &::placeholder {
            color: #c0c4cc;
            font-size: 13px;
            line-height: 30px;
        }
    }
}

.el-icon-arrow-up {
    pointer-events: none;
    cursor: pointer;
    text-align: center;
    position: absolute;
    width: 25px;
    right: 5px;
    transform: rotateZ(180deg);
    transition: transform 0.3s;
    line-height: 32px;

    &.is-reverse {
        transform: rotateZ(0deg);
    }
}
</style>

<style lang="scss">
.aocdn-ignore-area-select__dropdown {
    position: absolute;
    z-index: 1001;
    border: solid 1px #e4e7ed;
    width: 1000px;
    border-radius: 2px;
    background-color: #ffffff;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    box-sizing: border-box;
}

.aocdn-ignore-area-select__dropdown-wrapper {
    max-height: 60vh;
    overflow: auto;
    padding: var(--padding-4x);
    .area-selector-section + .area-selector-section {
        border-top: 1px solid $border-color;
    }
    .area-selector-section {
        display: flex;
        padding: var(--padding-3x) 0;
        &--label {
            width: 60px;
            margin-right: var(--margin-5x);
            padding-top: 4px;
            font-size: 14px;
            font-weight: bolder;
        }
        &--content {
            flex: 1;
            display: flex;
            flex-direction: column;
            // .area-selector-section--content-row + .area-selector-section--content-row {
            //     margin-top: var(--margin-3x);
            // }
            &-row {
                display: flex;
                line-height: 28px;
                flex-direction: row;
                &__left {
                    width: 100px;
                    margin-right: var(--margin-6x);
                }
                &__right {
                    flex: 1;
                    line-height: 28px;
                    & > .el-checkbox {
                        width: 100px;
                    }
                }
            }
        }
        .el-checkbox__label {
            white-space: nowrap; // 避免被主应用覆盖
        }
    }
}
</style>
