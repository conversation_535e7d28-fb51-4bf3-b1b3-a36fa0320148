/*
 * @Description: element-theme 中无法通过配置 var 的方式重置的，统一在这里进行层叠处理
 * @Author: wang yuegong
 */
@import '../_var.scss';

// ========== 重置 el-menu 样式 start ==========
.menu {
    height: calc(100% - 55px);
    &>.el-menu {
        height: 100%;
    }
}

.el-menu .el-menu-item,
.el-menu .el-submenu__title {
    padding-left: 24px !important;
    min-width: auto;
    height: 40px;
    line-height: 40px;
    background-color: transparent;
    border-right: 2px solid transparent;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;

    &.is-active {
        // border-right: 3px solid $g-color-yellow;
        background: #fff;
        &:before {
            left: 16px;
            content: "";
            display: inline-block;
            background-color: #3d73f5;
            width: 2px;
            height: 12px;
            position: absolute;
            top: 14px;
            z-index: 1;
        }
    }
    span {
        vertical-align: inherit;
    }
    i {
        display: none !important; // ui改版不展示菜单图标icon
    }
    i.el-submenu__icon-arrow {
        display: inline-block !important; // ui改版需要展示展示arrow icon
    }
}

.el-menu .el-submenu .el-menu-item {
    padding-left: 40px !important;
    min-width: auto; // 原设定 200px ，会导致 menu 容器较窄时，item 尺寸超过父级，无法展示右侧 border
    height: 40px !important;
    line-height: 40px !important;
    min-width: auto;
    height: 40px;
    line-height: 40px;
    background-color: transparent;
    border-right: 2px solid transparent;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    &:before{
        left: 32px;
    }
}

.el-message--success {
    background: #F0FFF6 !important;
}

.el-message {
    height: unset !important;
}

// ========== 重置 el-menu 样式 end ==========


// // ========== 重置 el-table + el-pager 样式 start ==========
// .el-table {
//     font-size: $g-fs-normal; // 设置 element 主题为 small 后，表格字体会改为 12px ，需要调回

//     th {
//         padding: 8px 0; // 注意：该属性的调整会影响到 src\assets\css\global\common.scss 中 table-scroll-wrap 的样式
//         background-color: #ebeff5;
//         color: #595c65;
//     }
// }

// .el-table td {
//     padding: 7px 0;
// }

// .el-pagination {
//     button {
//         min-width: 30px;
//         height: 28px;
//         background-color: #F4F4F5 !important;
//     }

//     .btn-prev {
//         padding: 0;
//         margin-right: 6px;
//     }

//     .btn-next {
//         padding: 0;
//         margin-left: 6px;
//     }
// }

// .el-pager li {
//     background-color: #F4F4F5;
//     margin: 0 5px;
//     border-radius: 2px;
//     min-width: 30px;
//     height: 28px;

//     &.active {
//         color: white;
//         background-color: $g-color-yellow;
//     }
// }

// // ========== 重置 el-table + el-pager 样式 end ==========

// // ========== 重置 el-dialog 样式 start ==========
// .el-dialog__header {
//     border-bottom: 1px solid #DFE0DF;
// }

// .el-dialog__footer {
//     background-color: #F5F5F5;
//     padding: 10px;
//     text-align: center;

//     div {
//         background-color: #F5F5F5;
//     }
// }

// // ========== 重置 el-dialog 样式 end ==========

// // ========== 处理小屏下的 datepicker/daterange start ==========
// @media screen and (max-width: 635px) {
//     .el-date-range-picker {
//         width: 100%;
//     }
// }

// @media screen and (max-width: 520px) {
//     .el-date-range-picker {
//         width: 90%;

//         .el-picker-panel__body {
//             min-width: 0;
//             padding: 10px
//         }

//         .el-date-range-picker__content {
//             padding: 0;
//             width: 100%;
//             box-sizing: border-box;
//             border: none;
//         }
//     }

//     .el-message-box {
//         width: 90%;
//     }

//     .el-dialog {
//         width: 90%;
//     }
// }


// // ========== 处理小屏下的 datepicker/daterange end ==========

// // ========== 处理多选输入框折行问题 start ==========
// .el-select__tags>span {
//     width: 100%;

//     &>span:first-child {
//         display: inline-block;
//         width: calc(100% - 60px);

//         span {
//             display: inline-block;
//             width: calc(100% - 15px);
//             overflow: hidden;
//             text-overflow: ellipsis;
//             white-space: nowrap;
//             vertical-align: middle;
//         }
//     }
// }

// .el-select__tags .el-select__input {
//     display: none\0;
// }

// // ========== 处理多选输入框折行问题 end ==========


// // ========== 处理多个下拉框合并边框问题 start ==========
// // 自定义一个外部容器，以便特殊处理
// .ct-select-group .el-select {
//     margin-right: -1px;
//     vertical-align: middle;

//     .el-input__inner {
//         border-radius: 0px;
//     }

//     &:first-of-type {
//         .el-input__inner {
//             border-radius: 2px 0 0 2px;
//         }
//     }

//     &:last-of-type {
//         .el-input__inner {
//             border-radius: 0 2px 2px 0;
//         }
//     }


//     .el-input {
//         position: relative;

//         &.is-focus::before {
//             content: '';
//             position: absolute;
//             top: 0;
//             right: 0;
//             height: 100%;
//             width: 0;
//             border-right: 1px solid $g-color-yellow;
//             z-index: 1000;
//         }
//     }
// }

// // ========== 处理多个下拉框合并边框问题 end ==========

// // ========== 处理 tab 丑陋的 focus 状态 start ==========
// .el-tabs__item:focus.is-active.is-focus:not(:active) {
//     -webkit-box-shadow: none;
//     box-shadow: none;
// }
// // ========== 处理 tab 丑陋的 focus 状态 end ==========
