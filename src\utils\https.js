import { ctFetch, appendQuery } from "./index";
import { get, set } from "lodash-es";
import { errorHandler } from "./ctFetch/errorHandler";
import { getCookie } from "@/utils/cookie";
import { delCookie } from "@/utils/cookie";
import { getDistributorTargetURL } from "@/utils/utils";
import { LoginUrl } from "@/config/url";
import i18n from "@/i18n";
import ctFetch18n from "@/utils/ctFetch/i18n";

// let isLoading = false;
// const isBs = process.env.PLATFORM === "bs";
// const parentUrl = document.referrer;
// const parentUrl = Cookie.get("referer");

// request拦截器
ctFetch.interceptors.request.use(requestMeta => {
    if (requestMeta.config.method === "POST") {
        const params = {
            workspaceId: get(requestMeta, "config.data.workspaceId"),
        };
        if (requestMeta.config.isFile) {
            const config = requestMeta.config;
            params.fileType = config.fileType;
            requestMeta.url = appendQuery(requestMeta.url, params);
            const { ...data } = requestMeta.config.body;
            const file = new File([data.data.file], config.name, {
                type: config.type,
            });
            data.data.file = file;
            set(requestMeta, "params", data.data);
            return requestMeta;
        }

        // 对于 ipa 的 POST 请求，由于数据需要嵌套在 body.data下，因此需要跳过下面的语句
        // 由于js的函数也是引用类型，无法再另外维护一个ctFetch，这里对ctFetch的属性赋值会污染全局其他地方引入的ctFetch
        // 所以对于 ipa 的请求，需要特殊处理
        requestMeta.url = appendQuery(requestMeta.url, params);
        if (requestMeta.url.includes("/aocdn/eas")) {
            return;
        }
        requestMeta.config.transferType = "ajson";
        set(requestMeta.config.headers, "Content-Type", "application/json");
        const { ...data } = requestMeta.params;
        set(requestMeta, "params", data.data);
    }
    return requestMeta;
});

// response拦截器
ctFetch.interceptors.response.use(async meta => {
    // 重定向
    const code = meta.data.code;
    // 100402 这个 code 好像没用上
    // if (code === "100403") {
    //     const userInfo = store.state.user;
    //     const userId = get(userInfo, "userInfo.userId", "");
    //     const href = window.location.href;
    //     const res = href.replace(/workspaceId=[A-Za-z\d]*/g, `workspaceId=${userId}`);
    //     window.location.href = res;
    //     // 打开强制刷新
    //     window.location.reload();
    //     return {
    //         value: [meta.data],
    //         stop: true,
    //     };
    // }
    // // 100402 这个 code 好像没用上
    // if (code === "100402") {
    //     if (isLoading) {
    //         return;
    //     }

    //     isLoading = true;
    //     try {
    //         const res = await ctFetch("/iam/gw/self/workspace/List", {
    //             data: {
    //                 offset: 0,
    //                 limit: 999,
    //             },
    //             cache: true,
    //         });
    //         const list = get(res, "list", []);
    //         const userWorkSpaceList = list.filter(
    //             item => ["member", "manager", "owner"].indexOf(item.userState) >= 0
    //         );
    //         if (userWorkSpaceList.length === 1) {
    //             return {
    //                 value: [meta.data],
    //                 stop: true,
    //             };
    //         }

    //         router.replace({
    //             path: "/interceptor",
    //         });
    //     } catch (e) {
    //         console.log(e);
    //     }
    //     isLoading = false;
    //     return {
    //         value: [meta.data],
    //         stop: true,
    //     };
    // }

    const dataUrl = meta.url;
    const dataStatus = meta.data.raw?.status;
    // 100401 用户认证失败或无权访问 & 边缘接入鉴权失效特殊处理
    if (code === "100401" || (dataUrl.includes("/aocdn/eas") && dataStatus === 401)) {
        if (dataUrl.includes("/aocdn/eas")) {
            meta.data.reason = i18n.t("common.sessionError"); // 更换提示信息
        }
        const err = {
            data: meta.data,
            btnConfig: {
                title: ctFetch18n.t("goLogin"),
                cb: () => {
                    const isDistributorDC = !!getCookie("cdn_distributor_auth");
                    if (isDistributorDC) {
                        delCookie("cdn_distributor_auth");

                        const url = getDistributorTargetURL("/distributorManagement/customer");
                        window.location.replace(url);
                        window.location.reload();
                        return;
                    }
                    window.location.href = LoginUrl;
                },
            },
        };
        errorHandler(err);
        return {
            value: [meta.data],
            stop: true,
        };
    }
    // 这些code实际好像没有用到
    // if (code === "100707" || code === "1000011" || code === "100704") {
    //     console.log("免密登录验证父页面地址:", parentUrl);
    //     console.log("isBs:", isBs);
    //     const err = {
    //         data: meta.data,
    //         btnConfig: {
    //             title: "goLogin",
    //             cb: () => {
    //                 window.location.href = isBs
    //                     ? parentUrl
    //                         ? parentUrl
    //                         : "https://www.ctyun.cn/h5/auth/"
    //                     : LoginUrl;
    //             },
    //         },
    //     };
    //     errorHandler(err);
    //     return {
    //         value: [meta.data],
    //         stop: true,
    //     };
    // }
});

export default ctFetch;
