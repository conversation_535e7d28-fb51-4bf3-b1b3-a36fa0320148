<template>
    <div class="origin-wrapper">
        <el-form
            label-width="140px"
            label-position="left"
            :model="form"
            :rules="rules"
            ref="subOriginForm"
            :disabled="!isEdit || !isService || isLock || isDomainEndsWithCtyun"
        >
            <div>
                <el-form-item
                    :label="$t('domain.create.originServer')"
                    prop="originAll"
                    :rules="rules.originAll"
                    ref="origin"
                    class="ct-table-form-item table-form-item-style"
                    ><span slot="label"
                        >{{ $t("domain.create.originServer") }}
                        <span>
                            <el-tooltip placement="top" :content="noMoreThanSixtyDomainTip" :offset="-30">
                                <ct-svg-icon
                                    icon-class="question-circle"
                                    class-name="ct-sort-drag-icon"
                                ></ct-svg-icon></el-tooltip></span
                    ></span>

                    <div class="ct-table-wrapper">
                        <el-table class="origin-table auto-table" :data="form.origin" :key="endPointStr">
                            <!-- 源站类型：用于区分：普通源站和xos源站 -->
                            <el-table-column prop="is_xos" :label="$t('domain.create.originType')">
                                <template slot="header">
                                    <div class="flex-row-style">
                                        <span>{{ $t("domain.create.originType") }}</span>
                                        <el-tooltip
                                            class="item"
                                            style="margin-left:4px"
                                            effect="dark"
                                            placement="top"
                                        >
                                            <div slot="content" v-if="!showZosOrigin">
                                                <span>
                                                    {{ $t("domain.editPage.tip19-prev") }}
                                                </span>
                                                <span v-if="!isCtclouds">
                                                    <a
                                                        class="aocdn-ignore-link"
                                                        @click="$docHelp(xosOriginUrl)"
                                                        >{{ this.$t("domain.create.tip43") }}</a
                                                    >
                                                </span>
                                                <div>{{ $t("domain.create.tip44-1") }}</div>
                                                <div>
                                                    {{
                                                        $t("domain.create.tip44", {
                                                            endPointStr: endPointStr
                                                                ? endPointStr
                                                                : `ctyunxs.cn${$t(
                                                                      "domain.or"
                                                                  )}xstore.ctyun.cn`,
                                                        })
                                                    }}
                                                </div>
                                                <div>
                                                    {{ $t("domain.create.tip45") }}
                                                </div>
                                                <div>
                                                    <span>
                                                        {{ $t("domain.create.tip50") }}
                                                    </span>
                                                    <span v-if="!isCtclouds">{{
                                                        $t("domain.create.tip51")
                                                    }}</span>
                                                    <a
                                                        v-if="!isCtclouds"
                                                        class="aocdn-ignore-link"
                                                        @click="$docHelp(cdnOriginFlowLink)"
                                                    >
                                                        {{ $t("domain.create.tip52") }}
                                                    </a>
                                                </div>
                                            </div>
                                            <div v-else slot="content">
                                                <i18n path="domain.editPage.tip19">
                                                    <a
                                                        v-if="!isCtclouds"
                                                        class="aocdn-ignore-link"
                                                        @click="
                                                            $docHelp('https://www.ctyun.cn/document/10306929')
                                                        "
                                                        >{{ $t("domain.editPage.tip19-1") }}</a
                                                    >
                                                    <span v-else>{{ $t("domain.editPage.tip19-1") }}</span>
                                                    <a
                                                        v-if="!isCtclouds"
                                                        class="aocdn-ignore-link"
                                                        @click="
                                                            $docHelp('https://www.ctyun.cn/document/10026735')
                                                        "
                                                        >{{ $t("domain.editPage.tip19-2") }}</a
                                                    >
                                                    <span v-else>{{ $t("domain.editPage.tip19-2") }}</span>
                                                </i18n>
                                                <div>{{ $t("domain.create.tip44-1") }}</div>
                                                <div>
                                                    {{
                                                        $t("domain.editPage.tip19-3", {
                                                            endPointStr: endPointStr
                                                                ? endPointStr
                                                                : `ctyunxs.cn ${$t(
                                                                      "domain.or"
                                                                  )} xstore.ctyun.cn`,
                                                            endPointStr2:
                                                                (zosOriginSuffix || []).join(
                                                                    ` ${$t("domain.or")} `
                                                                ) || ".zos.ctyun.cn",
                                                        })
                                                    }}
                                                </div>
                                                <div>
                                                    {{ $t("domain.editPage.tip19-4") }}
                                                </div>
                                            </div>
                                            <i class="el-icon-question" />
                                        </el-tooltip>
                                    </div>
                                </template>
                                <template slot-scope="scope">
                                    <el-form-item
                                        label-width="0"
                                        :prop="`origin.` + scope.$index + `.is_xos`"
                                    >
                                        <el-select
                                            v-model="scope.row.is_xos"
                                            class="input-style"
                                            @change="() => handleChange('origin_type')"
                                            :disabled="isLock || isDomainEndsWithCtyun"
                                        >
                                            <el-option :value="0" :label="$t('domain.create.xos0')" />
                                            <el-option
                                                v-if="showXosOrigin"
                                                :disabled="
                                                    xosZosSelectedIndex !== -1 &&
                                                        xosZosSelectedIndex !== scope.$index
                                                "
                                                :value="1"
                                                :label="$t('domain.create.xos1')"
                                            />
                                            <el-option
                                                v-if="showZosOrigin"
                                                :disabled="
                                                    xosZosSelectedIndex !== -1 &&
                                                        xosZosSelectedIndex !== scope.$index
                                                "
                                                :value="2"
                                                :label="$t('domain.create.zos.tip1')"
                                            />
                                        </el-select>
                                    </el-form-item>
                                </template>
                            </el-table-column>
                            <el-table-column prop="origin" :label="$t('domain.create.originServer')">
                                <template slot-scope="scope">
                                    <!-- 普通源站 -->
                                    <!-- v-show="scope.row.is_xos === 0" -->

                                    <el-form-item
                                        v-if="scope.row.is_xos === 0"
                                        label-width="0"
                                        :prop="`origin.` + scope.$index"
                                        key="origin"
                                        :rules="rules.origin"
                                    >
                                        <el-input
                                            v-model="scope.row.origin"
                                            @change="handleChange"
                                            placeholder=""
                                            class="input-style"
                                            :disabled="isLock || isDomainEndsWithCtyun"
                                        ></el-input>
                                    </el-form-item>

                                    <!-- xos源站 -->
                                    <el-form-item
                                        v-if="scope.row.is_xos === 1"
                                        label-width="0"
                                        :prop="`origin.` + scope.$index"
                                        key="xos_origin"
                                        :rules="rules.origin"
                                    >
                                        <el-tooltip
                                            placement="top"
                                            :content="$t('domain.create.placeholder5')"
                                        >
                                            <el-autocomplete
                                                popper-class="el-autocomplete-aocdn-width-adjust"
                                                v-model.trim="scope.row.origin"
                                                :fetch-suggestions="querySearch"
                                                :placeholder="$t('domain.create.placeholder5')"
                                                @select="handleSelect($event, scope.$index)"
                                                @change="() => handleChange('origin')"
                                                :disabled="isLock || isDomainEndsWithCtyun"
                                                class="text-content"
                                                style="width:100%"
                                            >
                                                <template slot-scope="{ item }">
                                                    <el-tooltip
                                                        effect="dark"
                                                        :content="item"
                                                        placement="right-end"
                                                    >
                                                        <div class="name">
                                                            {{ item }}
                                                        </div>
                                                    </el-tooltip>
                                                </template>
                                            </el-autocomplete>
                                        </el-tooltip>
                                    </el-form-item>

                                    <!-- zos源站 -->
                                    <el-form-item
                                        v-if="scope.row.is_xos === 2"
                                        label-width="0"
                                        :prop="`origin.` + scope.$index"
                                        key="zos_origin"
                                        :rules="rules.origin"
                                    >
                                        <el-tooltip placement="top" :content="$t('domain.create.zos.tip2')">
                                            <el-input
                                                v-model="scope.row.origin"
                                                @change="handleChange"
                                                :placeholder="$t('domain.create.zos.tip2')"
                                                class="input-style"
                                                :disabled="isLock || isDomainEndsWithCtyun"
                                            ></el-input>
                                        </el-tooltip>
                                    </el-form-item>
                                </template>
                            </el-table-column>
                            <el-table-column prop="role" :label="$t('domain.create.level')" width="125">
                                <template slot-scope="scope">
                                    <el-form-item
                                        label-width="0"
                                        :prop="
                                            isLock || isDomainEndsWithCtyun
                                                ? null
                                                : `origin.` + scope.$index + `.role`
                                        "
                                    >
                                        <el-select
                                            v-model="scope.row.role"
                                            @change="handleChange"
                                            class="input-style"
                                            :disabled="isLock || isDomainEndsWithCtyun"
                                        >
                                            <el-option value="master" :label="$t('domain.create.primary')" />
                                            <el-option value="slave" :label="$t('domain.create.secondary')" />
                                        </el-select>
                                    </el-form-item>
                                </template>
                            </el-table-column>
                            <el-table-column prop="origin" :label="$t('domain.create.weight')" width="80">
                                <template slot-scope="scope">
                                    <el-form-item
                                        label-width="0"
                                        :prop="
                                            isLock || isDomainEndsWithCtyun
                                                ? null
                                                : `origin.` + scope.$index + `.weight`
                                        "
                                        :rules="isLock || isDomainEndsWithCtyun ? null : rules.weight"
                                        class="weight-wrapper"
                                    >
                                        <el-input
                                            v-model="scope.row.weight"
                                            @change="handleChange"
                                            :placeholder="$t('domain.detail.placeholder20')"
                                            class="input-style"
                                            :disabled="isLock || isDomainEndsWithCtyun"
                                        ></el-input>
                                    </el-form-item>
                                </template>
                            </el-table-column>
                            <!-- 指定源站回源HOST -->
                            <el-table-column
                                prop="origin_host"
                                :label="$t('domain.create.originHost')"
                                v-if="temp_domain_detail.use_ecgw === 1"
                            >
                                <template slot-scope="scope">
                                    <host-select-mod
                                        v-model.trim="scope.row.origin_host"
                                        :accelerate-domains="[securityCurrentForm.domain]"
                                        :origin-domains="
                                            form.origin.map(itm => itm.origin).filter(itm => !!itm)
                                        "
                                        host-type="originHost"
                                        :other-host-value="form.origin_host"
                                        :prop="
                                            isLock || isDomainEndsWithCtyun
                                                ? null
                                                : `origin.` + scope.$index + `.origin_host`
                                        "
                                        :disabled="isLock || isDomainEndsWithCtyun"
                                        :rules="[
                                            {
                                                validator:
                                                    isLock || isDomainEndsWithCtyun
                                                        ? null
                                                        : valid_origin_host,
                                                trigger: 'blur',
                                            },
                                        ]"
                                        label-width="0"
                                        @change="handleChange"
                                    />
                                </template>
                            </el-table-column>
                            <el-table-column :label="$t('domain.operate')" width="80">
                                <template slot-scope="scope">
                                    <el-button
                                        type="text"
                                        :disabled="!isEdit || !isService || isLock || isDomainEndsWithCtyun"
                                        @click="onOperator(scope.row, 'delete', 'origin', scope.$index)"
                                        >{{ $t("domain.delete") }}</el-button
                                    >
                                </template>
                            </el-table-column>
                        </el-table>
                        <div class="flex-row-style button-box">
                            <el-button
                                class="btn"
                                type="text"
                                @click="onOperator(null, 'create', 'origin')"
                                :disabled="
                                    form.origin.length >= 60 ||
                                        !isEdit ||
                                        !isService ||
                                        isLock ||
                                        isDomainEndsWithCtyun
                                "
                            >
                                + {{ $t("domain.editPage.label20") }}
                            </el-button>
                        </div>
                    </div>
                </el-form-item>
            </div>
        </el-form>
    </div>
</template>

<script>
import validFieldMixin from "@/views/domainConfig/validFieldMixin";
import componentMixin from "@/views/domainConfig/componentMixin";
import ctSvgIcon from "@/components/ctSvgIcon";
import { siteUrl } from "@/config/url";
import { cloneDeep } from "lodash-es";
import { ipv4, ip, domain } from "@cdnplus/common/config/pattern";
import { nUserModule } from "@/store/modules/nuser";
import { backoriginModeMap, gatewayTypeMap, getGatewayType } from "@/utils/product";
import { StatisticsModule } from "@/store/modules/statistics";
import { CdnConfigModule } from "@/store/modules/cdnConfig";
import { withResolver } from "@/utils";
import { reservedIp } from "@/config/npattern";
import { SecurityAbilityModule } from "@/store/modules/securityAbility";

const urlReg = new RegExp(domain);
const ipReg = new RegExp(ip);
const reservedIpReg = new RegExp(reservedIp);

const weightPattern = "^([1-9][0-9]?|100)$";
const weightReg = new RegExp(weightPattern);

export default {
    name: "origin",
    components: {
        ctSvgIcon,
    },
    mixins: [validFieldMixin, componentMixin],
    props: {
        datas: Array,
        isLockOrigin: Boolean,
        isLockXosOrigin: Boolean,
        isLockZosOrigin: Boolean,
        reqHost: String,
        domain: String,
        temp_domain_detail: Object,
        isNewEcgw: Boolean,
        isDomainEndsWithCtyun: Boolean,
    },
    data() {
        return {
            form: {
                origin: [],
            },
            wholeStationProductCode: ["006", "104", "105"], // 全站加速的 product_code

            xosOriginUrl: "https://www.ctyun.cn/document/10306929",
            xosOriginList: [],
            endPointList: [],
            endPointStr: "",
            xos_origin_bucket: "",
            ctBackupSource: [], // AOne云备源
            isOtherCompanyCtBackupSource: false, // 是否是其他公司的云备源
            checkCtBackupSourceAbortController: null,
            rules: {
                originAll: [{ required: true, validator: this.validateOriginAll, trigger: "blur, change" }],
                role: [{ required: true, message: this.$t("domain.detail.tip74"), trigger: "change" }],
                weight: [
                    { required: true, message: this.$t("domain.detail.placeholder20"), trigger: "blur" },
                    {
                        validator: (rule, value, callback) => {
                            if (value === "" || value === null || value === undefined) {
                                callback(this.$t("domain.detail.placeholder20"));
                            } else if (!weightReg.test(value)) {
                                callback(this.$t("domain.create.tip17"));
                            } else callback();
                        },
                        trigger: ["blur", "change"],
                    },
                ],
                origin: [
                    { required: true, message: this.$t("domain.detail.tip73"), trigger: "blur" },
                    {
                        validator: (rule, value, callback) => {
                            if (this.isLock || this.isDomainEndsWithCtyun) return callback();
                            const endPointList = cloneDeep(this.endPointList);
                            const originList = cloneDeep(this.form.origin);
                            const { origin, is_xos } = value;
                            let suffixNum = 0;
                            let count = 0;
                            endPointList.forEach(item => {
                                if (item) {
                                    originList?.forEach(ori => {
                                        if (ori?.origin?.endsWith(item) && ori?.is_xos === 1) {
                                            suffixNum++;
                                        }
                                    });
                                    // 当前弹窗如果选中的是 媒体存储(融合版)源站，输入的域名后缀，至少包含一个后端接口返回的后缀中的一个，否则需要校验报错
                                    if (origin?.endsWith(item) && is_xos === 1) {
                                        count++;
                                    }
                                }
                            });

                            // zos
                            if (is_xos === 2 && !this.zosOriginSuffix.some(zos => origin?.endsWith(zos))) {
                                callback(
                                    new Error(
                                        `${this.$t("domain.create.zos.tip3", {
                                            endPointStr:
                                                this.zosOriginSuffix?.join(` ${this.$t("domain.or")} `) ||
                                                ".zos.ctyun.cn",
                                        })}`
                                    )
                                );
                            }

                            if (origin === "" || origin === null || origin === undefined) {
                                callback(this.$t("domain.detail.placeholder80"));
                            } else if (!ipReg.test(origin) && !urlReg.test(origin) && this.isEdit) {
                                callback(this.$t("domain.detail.tip89"));
                            } else if (reservedIpReg.test(origin) && this.isEdit) {
                                callback(this.$t("domain.create.tip16"));
                            } else if (suffixNum === 0 && is_xos === 1) {
                                callback(this.$t("domain.detail.tip102"));
                            } else if (suffixNum > 1 && is_xos === 1) {
                                callback(this.$t("domain.detail.tip103"));
                            } else if (count === 0 && is_xos === 1) {
                                callback(this.$t("domain.detail.tip102"));
                            } else callback();
                        },
                        trigger: ["blur", "change"],
                    },
                    {
                        validator: (rule, value, callback) => {
                            if (this.isOtherCompanyCtBackupSource && value.is_xos === 1) {
                                callback(new Error(this.$t("domain.detail.tip111")));
                            } else {
                                callback();
                            }
                        },
                        trigger: ["blur", "change"],
                    },
                ],
            },
        };
    },
    computed: {
        isLock() {
            return this.isLockOrigin || this.isLockXosOrigin || this.isLockZosOrigin;
        },
        isCtclouds() {
            return nUserModule.isCtclouds;
        },
        cdnOriginFlowLink() {
            return "https://www.ctyun.cn/document/10306929/10323667";
        },
        securityCurrentForm() {
            return SecurityAbilityModule.securityCurrentForm;
        },
        zosOriginSuffix() {
            return StatisticsModule.zosOriginSuffix;
        },
        showXosOrigin() {
            return (
                (!window.__POWERED_BY_QIANKUN__ &&
                    this.temp_domain_detail.use_ecgw === 1 &&
                    this.temp_domain_detail.backorigin_mode === 0 &&
                    this.temp_domain_detail.product_code !== "006" &&
                    this.temp_domain_detail.product_code !== "104" &&
                    this.temp_domain_detail.product_code !== "105") ||
                this.temp_domain_detail.gateway_type === 6 ||
                this.temp_domain_detail.product_code === "006" ||
                this.temp_domain_detail.product_code === "104" ||
                this.temp_domain_detail.product_code === "105" ||
                window.__POWERED_BY_QIANKUN__
            );
        },
        showZosOrigin() {
            if (!this.zosOriginSuffix) return false;
            // 仅静态底座且新框架且ATS回源模式的域名
            // if (window.__POWERED_BY_QIANKUN__) return true
            const { backorigin_mode, gateway_type, product_code } = this.temp_domain_detail;
            const staticCondition =
                this.isNewEcgw &&
                backoriginModeMap.ATS === backorigin_mode &&
                gatewayTypeMap.STATIC === getGatewayType(product_code, gateway_type);
            const wholeCondition =
                this.wholeStationProductCode.includes(product_code) ||
                gatewayTypeMap.WHOLE === getGatewayType(product_code, gateway_type);

            return staticCondition || wholeCondition;
        },
        noMoreThanSixtyDomainTip() {
            const isSupportXos =
                (this.temp_domain_detail.product_code !== "006" &&
                    this.temp_domain_detail.backorigin_mode === 0 &&
                    this.temp_domain_detail.use_ecgw === 1 &&
                    !this.isPoweredByQiankun) ||
                this.temp_domain_detail.gateway_type === 6 ||
                this.wholeStationProductCode.includes(this.temp_domain_detail.product_code) ||
                this.isPoweredByQiankun;
            const isSupportZos = this.showZosOrigin;

            if (isSupportXos && isSupportZos) {
                return this.$t("domain.create.tip5-1");
            } else if (isSupportXos && !isSupportZos) {
                return this.$t("domain.create.tip5");
            } else if (!isSupportXos && isSupportZos) {
                return this.$t("domain.create.tip5-2");
            } else {
                return this.$t("domain.create.tip4");
            }
        },
        xosZosSelectedIndex() {
            const idx = (this.form?.origin || []).findIndex(item => item.is_xos === 1 || item.is_xos === 2);
            return idx;
        },
        getXosOriginSelectedList() {
            // xos源站
            const list = (this.xosOriginList || []).map(item => item.bucketName + "." + item.endPoint);
            // aone云备源
            if (this.isPoweredByQiankun) {
                list.push(...this.ctBackupSource);
            }
            return list;
        },
    },
    watch: {
        xos_origin_bucket: {
            deep: true,
            handler(val) {
                this.$emit("onBucketChange", val);
            },
            immediate: true,
        },
        datas: {
            deep: true,
            handler(val) {
                this.init(val);
            },
            immediate: true,
        },
    },
    methods: {
        init(v) {
            if (v && v.length > 0) {
                this.form.origin = cloneDeep(v);
                this.getCloudBackupSource();
            } else {
                this.form.origin = [];
            }
        },
        handleChange(type) {
            this.handleBucket();
            ["origin", "origin_type"].includes(type) && this.handleUpdateIsCtBackupSource();
            this.$emit("onChange", this.form.origin);

            // elForm嵌入elForm的弊端出现，elForm将会提供一个provide将本身实例透传，无论处于哪种层级的子组件都可通过inject接收
            // 需要联动校验的话，这里只能将事件抛出
            this.$emit("validateReqHost");
        },
        /**
         * 判断当前用户输入的源站域名是否是其他公司的云备源
         */
        handleUpdateIsCtBackupSource() {
            let hasCtBackupSource = false;
            this.form.origin.map(async (ori, index) => {
                this.isOtherCompanyCtBackupSource = false;
                if (
                    ori?.origin &&
                    ori.is_xos === 1 &&
                    this.isPoweredByQiankun &&
                    !this.getXosOriginSelectedList.includes(ori?.origin) &&
                    urlReg.test(ori.origin) // 输入的是域名才需要校验云备源
                ) {
                    if (this.checkCtBackupSourceAbortController) {
                        this.checkCtBackupSourceAbortController.abort();
                    }
                    this.checkCtBackupSourceAbortController = new AbortController();
                    // 云备源校验依赖接口返回，设置等待表单验证，避免提交错误数据
                    CdnConfigModule.SET_IS_WAITING_FORM_VALIDATE({
                        key: "ctBackupSource",
                        value: true,
                    });
                    const data = await this.$ctFetch(`${siteUrl.checkCtBucketName}`, {
                        signal: this.checkCtBackupSourceAbortController.signal,
                        method: "GET",
                        transferType: "json",
                        data: { originDomain: ori.origin },
                    });

                    this.isOtherCompanyCtBackupSource = data === true ? false : true;
                    this.$refs.subOriginForm.validateField(`origin.` + index);

                    CdnConfigModule.SET_IS_WAITING_FORM_VALIDATE({
                        key: "ctBackupSource",
                        value: false,
                    });
                }
                // 如果是xos源站，且是云备源，设置云备源选中状态
                if (
                    ori?.origin &&
                    ori.is_xos === 1 &&
                    this.isPoweredByQiankun &&
                    this.ctBackupSource?.includes(ori?.origin)
                ) {
                    hasCtBackupSource = true;
                }
            });

            CdnConfigModule.SET_IS_CT_BACKUP_SOURCE_SELECTED(hasCtBackupSource);
        },
        async onOperator(row, currentType, tabName, i) {
            this.currentType = currentType;
            const getTime = new Date().getTime();

            // 添加
            if (currentType === "create") {
                const defaultFormMap = {
                    // is_xos: 0
                    origin: { origin: "", role: "master", weight: "10", origin_host: "", is_xos: 0 },
                };
                row = defaultFormMap[tabName];
            }
            // 删除
            if (currentType === "delete") {
                const msgMap = {
                    origin: this.$t("domain.editPage.tip20"),
                };

                // 二次确认弹窗
                await this.$confirm(msgMap[tabName], this.$t("domain.delete"), {
                    type: "warning",
                });
                this.form[tabName].splice(i, 1);
                this.$emit("onChange", this.form.origin);
            } else {
                this.form[tabName].push(row);
            }
        },
        // 校验：指定源站回源HOST
        async valid_origin_host(rule, value, callback) {
            if (value === "" || value === null || value === undefined) return callback();

            if (this.isNewEcgw && value && this.reqHost && this.isEdit) {
                return callback(new Error(this.$t("domain.create.tip20")));
            }
            return callback();
        },
        // 校验源站回源HOST
        handleCheckOriginHost() {
            // 整理数组需要校验的字段
            const arr = (this.form.origin || []).map((item, index) => {
                return `origin.${index}.origin_host`;
            });

            this.$refs.subOriginForm.validateField(arr);
        },

        handleBucket() {
            const endPointList = JSON.parse(JSON.stringify(this.endPointList));
            const originList = JSON.parse(JSON.stringify(this.form.origin));
            endPointList.forEach(item => {
                if (item) {
                    originList?.forEach(ori => {
                        if (ori?.origin?.endsWith(item) && ori.is_xos === 1) {
                            this.xos_origin_bucket = ori.origin.substring(0, ori.origin.indexOf("."));
                        }
                    });
                }
            });
        },
        async getXosList() {
            let res = [];
            res = await this.$ctFetch(siteUrl.xosList);
            const getData = res;
            if (getData) {
                const data = [];
                for (let i = 0; i < getData.length; i++) {
                    if (getData[i].bucketName) {
                        data.push({
                            bucketName: getData[i].bucketName,
                            endPoint: getData[i].endPoint,
                        });
                    }
                }
                this.xosOriginList = data;
            }
        },
        // // 获取xos源站后缀，用来校验
        async getXosSuffix() {
            let res = [];
            res = await this.$ctFetch(siteUrl.xosSuffix);
            const getData = res;
            if (getData) {
                const endPointDatas = [];
                let endPointStr = "";

                for (let i = 0; i < getData.length; i++) {
                    endPointDatas.push(getData[i].suffix);
                    // 动态配置后缀
                    if (getData[i].suffix) {
                        endPointStr +=
                            getData[i].suffix?.substring(1, getData[i].suffix?.length) +
                            `${i === getData.length - 1 ? "" : this.$t("domain.or")}`;
                    }
                }
                this.endPointStr = endPointStr;
                this.endPointList = endPointDatas;
            }
        },
        handleSelect(item, index) {
            this.form.origin[index].origin = item;
            this.handleUpdateIsCtBackupSource();
            this.$emit("onChange", this.form.origin);
        },
        querySearch(queryString, cb) {
            const list = this.getXosOriginSelectedList;
            const results = queryString ? list.filter(this.createFilter(queryString)) : list;
            cb(results);
        },
        createFilter(queryString) {
            return restaurant => {
                return (
                    !!restaurant.bucketName &&
                    restaurant.bucketName.toLowerCase().indexOf(queryString.toLowerCase()) === 0
                );
            };
        },
        validateOriginAll(rule, value, callback) {
            if (this.isLock || this.isDomainEndsWithCtyun || !this.isEdit) return callback();
            let count = 0;
            const temp_origin = this.form.origin.map(item => item.origin);
            const origin_set = new Set(temp_origin);

            for (let i = 0; i < this.form.origin.length; i++) {
                if (this.form.origin[i].role === "master") {
                    count++;
                }
            }

            // zos
            const specialOriList = this.form.origin.filter(ori => ori.is_xos > 0);
            const xosList = specialOriList.filter(ori => ori.is_xos === 1);
            const zosList = specialOriList.filter(ori => ori.is_xos === 2);

            if (xosList.length && zosList.length) {
                callback(new Error(`${this.$t("domain.create.zos.tip4")}`));
            }

            // xos || zos 源站只能配置一个
            if (this.showZosOrigin) {
                if (xosList.length > 1 || zosList.length > 1) {
                    callback(new Error(`${this.$t("domain.create.zos.tip5")}`));
                }
            }

            // 源站地址不能和加速域名重复
            if (this.domain && this.form.origin?.some(o => o.origin === this.domain)) {
                callback(this.$t("domain.create.tip13"));
            } else if (origin_set.size !== temp_origin.length) {
                callback(this.$t("domain.create.tip14"));
            } else if (count < 1) {
                callback(this.$t("domain.create.tip18"));
            } else {
                callback();
            }
            callback();
        },
        // 获取AOne云备源
        async getCloudBackupSource() {
            if (!this.isPoweredByQiankun) return Promise.resolve();

            const { promise, resolve } = withResolver();
            const data = await this.$ctFetch(siteUrl.getBackupSource, { cache: true });
            this.ctBackupSource = Array.isArray(data) ? data : [];

            setTimeout(() => {
                this.handleUpdateIsCtBackupSource();
                resolve();
            }, 0);

            return promise;
        },
    },
    mounted() {
        this.$ctBus.$on("refershPage", () => {
            this.$refs.subOriginForm?.clearValidate();
        });
        this.getXosList();
        this.getXosSuffix();
        this.isPoweredByQiankun && this.getCloudBackupSource();
    },
    beforeDestroy() {
        this.$ctBus.$off("refershPage");
    },
};
</script>

<style lang="scss" scoped>
@import "@/views/domainConfig/commonStyle.scss";

.origin-wrapper {
    ::v-deep {
        .el-form--label-left .el-form-item__label {
            text-align: right;
        }
    }
}
.question-circle {
    font-size: $text-size-md;
    cursor: pointer;
}
.icon-column-label {
    color: $text-color-light;
    margin-left: $margin-2x;
    font-size: $text-size-md;
    line-height: 18px;
    height: 14px;
    width: 14px;
}
.tips {
    font-size: 12px;
    color: $color-neutral-7;
    font-weight: 400;
    margin-left: $margin;
}
.weight-wrapper {
    ::v-deep {
        .el-form-item__error {
            align-items: normal !important;
        }
    }
}
</style>
<style lang="scss">
.el-autocomplete-aocdn-width-adjust {
    width: 360px !important;
}
</style>
