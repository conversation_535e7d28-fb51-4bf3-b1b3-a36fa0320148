### 级联选择框图片

###### 使用情景
1、父级为字典，且含有children字段，子级选择框展示children字段。

2、父级为选择框，二级需要父级选择后，用选择的值去请求字典。

3、都为普通选择框，无关联关系

##### 引用
<cascade-select
:form="formTest"
:config="configTest" />

import cascadeSelect from "@/components/cascadeSelect”;

##### 参数


| prop |默认值  |描述  |
| --- | ---- | --- |
| form |  {}对象 | 多个选择框组所绑定的数据集合 |
| config |  []数组 | 配置参数  |
| isRelateDisabled | true | 子集在父级未选择时是否禁用 |

##### config详细配置


| 属性 |值  |描述  |
| --- | --- | --- |
| prop | string | 必填，表示与form所绑定的变量 |
| relateProp | string  | 选填，表示关联的变量，理解成子级prop  |
| disabled | boolean,function | 是否禁用该组件 |
| relateType | map |选填，map表示将触发子级请求字典（根据父级的选择值）  |
| relateMapRoot | string | 选填，在relateType为map的情况下生效，默认为console_，soc可传入为soc_brm  |
| outsideAry | array | 选填，select的data,此项优先级最高，若传入outsideAry则渲染数据就是outsideAry，不再请求也不与父级联动 |

其他配置参考element-ui select,cascader控件参数
###### 参考配置

情景1:
configTest: [
{
prop: "a",
dicStr: "soc_brm,statusCode",
dicConfig: {
isMulti: true
},
relateProp: "b",
},
{
prop: "b",
},
],

情景2：
configTest: [
{
prop: "a",
relateProp: "b",
relateType: "map",
relateMapRoot: "soc_brm",
outsideAry: [
{ label: "1", value: "serviceStatus" },
{ label: "2", value: "domainStatus" },
],
},
{
prop: "b",
},
],

情景3:
configTest: [
{
prop: "a",
},
{
prop: "b",
},
],
