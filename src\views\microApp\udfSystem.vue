<template>
    <div>
        <skeletonPage v-if="showSkeleton" />
        <div class="ctMicroAppViewBox"></div>
    </div>
</template>

<script>
import { loadMicroApp } from "qiankun";
import skeletonPage from "@/views/skeletonPage/developerPlatform/index.vue";
import { QianKunApps } from "@/qiankun";
import { MicroAppModule } from "@/store/modules/microApp";

export default {
    name: "udfApp",
    // mixins: [mixin],
    components: { skeletonPage },
    data() {
        return {
            showSkeleton: false,
            edgeMicroApp: null,
        };
    },
    watch: {
        $route: {
            handler(to) {
                if (!MicroAppModule.edgeMicroRoutesUseSkeleton.includes(to.path)) {
                    this.showSkeleton = false;
                    this.edgeMicroApp?.unmount();
                    this.loadApp(QianKunApps[0]);
                } else {
                    this.edgeMicroApp?.unmount();
                    setTimeout(() => {
                        MicroAppModule.SET_MICRO_APP_LOADING(false);
                        this.showSkeleton = true;
                    });
                }
            },
            immediate: true,
        },
    },
    methods: {
        loadApp(app) {
            MicroAppModule.SET_MICRO_APP_LOADING(true);
            setTimeout(async () => {
                this.edgeMicroApp = loadMicroApp(app, {
                    singular: true,
                });

                await this.edgeMicroApp.mountPromise
                    .catch(() => {
                        console.log("error loading micro app");
                    })
                    .finally(() => {
                        MicroAppModule.SET_MICRO_APP_LOADING(false);
                    });

                MicroAppModule.SET_MICRO_APP_LOADING(false);
            });
        },
    },
    beforeDestroy() {
        this.edgeMicroApp?.unmount();
    },
};
</script>

<style scoped lang="scss">
::v-deep #udfBox {
    width: 100%;
    height: 100%;

    #__qiankun_microapp_wrapper_for_udf__ {
        width: 100%;
        height: 100%;
    }
}
</style>
