<template>
    <el-dialog
        :title="$t('domain.list.note')"
        :append-to-body="true"
        :close-on-click-modal="false"
        :visible.sync="dialogVisible"
        :before-close="submit"
        class="server-port-tip-dialog"
        width="550px"
        :show-close="true"
    >
        <el-alert title="" type="info" :closable="false" show-icon class="ct-alert">
            <template slot="title">
                <div v-if="errorCode === 'clnt.e2364'">
                    <span>{{ $t("domain.editPage.tip21") }}</span>
                    <span v-html="serverPortTip"></span>
                    <span>{{ $t("domain.editPage.tip18") }}</span>
                </div>
                <div v-else-if="errorCode === 'clnt.e2367'">
                    <i18n path="domain.editPage.specialPortTip2.tip1">
                        <a :href="serviceTicketUrl" target="_blank" class="aocdn-ignore-link">{{ $t("domain.editPage.specialPortTip2.tip2") }}</a>
                    </i18n>
                </div>
            </template>
        </el-alert>
        <div slot="footer" class="btns">
            <el-button type="primary" @click="submit">{{ $t("common.dialog.submit") }}</el-button>
        </div>
    </el-dialog>
</template>

<script lang="ts">
import { commonLinks } from "@/utils/logic/url";
import { Component, Prop, Vue, Watch } from "vue-property-decorator";
import { CODE_SERVER_PORT_ERROR_2364 } from "@/utils/ctFetch/errorConfig";

@Component({})
export default class UpdateDialog extends Vue {
    @Prop({ default: false, type: Boolean }) private dialogVisible!: boolean;
    @Prop({ default: CODE_SERVER_PORT_ERROR_2364, type: String }) private errorCode!: string;

    get serverPortTip() {
        return `<a target="_blank" href="${commonLinks.orderLink}">${this.$t(
            "domain.editPage.tip22"
        )}</a>`;
    }

    private async submit() {
        this.$emit("submit");
    }
    get serviceTicketUrl() {
        return commonLinks.orderLink;
    }
}
</script>

<style lang="scss" scoped>
.server-port-tip-dialog {
    .ct-alert {
        ::v-deep {
            .el-alert {
                align-items: center;
                padding: 12px;
            }
            a {
                color: $color-master;
            }
        }
    }
}
</style>
