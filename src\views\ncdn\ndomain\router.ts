import { RouteConfig } from "vue-router";
import { ProductModule } from "@/store/modules/ncdn/nproduct";
import { LabelModule } from "@/store/modules/label";
import { nUserModule } from "@/store/modules/nuser";
import { StatisticsModule } from "@/store/modules/statistics";
import { getCtiamAction } from "@/store/config";
import { DomainModule } from "@/store/modules/domain";

const indexRouter: RouteConfig = {
    path: "/ndomain",
    name: "ndomain",
    component: () => import("./index.vue"),
    meta: {
        breadcrumb: {
            title: "$t('domain.title')",
            route: ["ndomain"],
        },
    },
    redirect: {
        name: "ndomain.list",
    },
    async beforeEnter(to, from, next) {
        // 不阻塞页面加载
        await ProductModule.nGetProductInfo();
        to.name === "ndomain.list" && await LabelModule.GetLabelList({
            cache: false,
            action: getCtiamAction("DomainListLabel"),
        });

        if (!window.__POWERED_BY_QIANKUN__) {
            // fcdn 新用户提示
            nUserModule.checkNewUser();
            StatisticsModule.GetBasicConfig();
            // 判断是否支持CDN加速HTTPS功能
            nUserModule.checkSupportHttps();
        }
        next();
    },
    children: [
        {
            path: "list",
            name: "ndomain.list",
            component: () => import("./list/index.vue"),
            meta: {
                breadcrumb: {
                    title: "$t('domain.domainList')",
                    route: ["ndomain", "ndomain.list"],
                },
                keepAlive: true,
                perm: "ndomain.list",
            },
        },
        {
            path: "detail",
            name: "ndomain.detail",
            component: () => import("./detail/index.vue"),
            meta: {
                breadcrumb: {
                    title: "$t('domain.domainView')",
                    route: ["ndomain", "ndomain.list", "ndomain.detail"],
                },
                keepAlive: false,
            },
        },
        // 编辑和查看是同一个界面，只有面包屑不同
        {
            path: "edit",
            name: "ndomain.edit",
            component: () => import("./detail/index.vue"),
            meta: {
                breadcrumb: {
                    title: "$t('domain.domainEdit')",
                    route: ["ndomain", "ndomain.list", "ndomain.edit"],
                },
                keepAlive: false,
            },
        },
        {
            path: "create",
            name: "ndomain.create",
            component: () => import("@/views/ncdn/ndomain/create/index.vue"),
            meta: {
                breadcrumb: {
                    title: "$t('domain.create.title')",
                    route: ["ndomain", "ndomain.list", "ndomain.create"],
                },
                perm: "ndomain.list",
            },
            beforeEnter(to, from, next) {
                if (from.name === "ndomain.detail" && Object.keys(DomainModule.createDomainStage?.data ?? {}).length) {
                    DomainModule.SET_CREATE_DOMAIN_STATE_DATA({ key: "isFromDetail", data: true});
                }
                next();
            },
        },
        {
            path: "batchEdit",
            name: "ndomain.batchEdit",
            component: () => import("@/views/ncdn/ndomain/batchEditNew/index.vue"),
            meta: {
                breadcrumb: {
                    title: "$t('domain.domainBatch')",
                    route: ["ndomain", "ndomain.list", "ndomain.batchEdit"],
                },
                perm: "ndomain.list",
            },
        },
    ]
};

export default indexRouter;
