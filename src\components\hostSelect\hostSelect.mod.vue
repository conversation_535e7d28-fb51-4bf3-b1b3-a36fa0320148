<template>
    <el-form-item :prop="prop" :rules="rules" :label-width="labelWidth">
        <span slot="label">
            {{ label }}
            <span v-if="hostType === 'reqHost' && !$slots.create">
                <slot v-if="$slots.tip" name="tip"></slot>
                <el-tooltip v-else placement="top" :content="$t('domain.create.tip8')">
                    <ct-svg-icon icon-class="question-circle" class-name="ct-sort-drag-icon"></ct-svg-icon>
                </el-tooltip>
            </span>
        </span>
        <div class="host-select">
            <!-- 类型选择下拉框 -->
            <el-select
                v-model="selectedType"
                placeholder="请选择指定源站回源HOST"
                class="type-select"
                :disabled="disabled"
                @change="onTypeChange"
                @blur="onBlur"
            >
                <el-option label="加速域名" value="accelerate" />
                <el-option label="源站域名" value="origin" />
                <el-option label="自定义域名" value="custom" />
            </el-select>

            <!-- 动态输入框/下拉框 -->
            <div class="input-container">
                <!-- 下拉框和输入框 -->
                <el-tooltip placement="top" :content="computedPlaceholder" :key="selectedType">
                    <!-- 加速域名下拉框 -->
                    <el-select
                        v-if="selectedType === 'accelerate'"
                        v-model="selectedValue"
                        :placeholder="computedPlaceholder"
                        class="value-select"
                        :disabled="disabled"
                        filterable
                        @change="onInputChange"
                        @blur="onBlur"
                    >
                        <el-option
                            v-for="(item, idx) in accelerateDomains"
                            :key="item + idx"
                            :label="item"
                            :value="item"
                        />
                    </el-select>

                    <!-- 源站域名下拉框 -->
                    <el-select
                        v-if="selectedType === 'origin'"
                        v-model="selectedValue"
                        :placeholder="computedPlaceholder"
                        class="value-select"
                        :disabled="disabled"
                        filterable
                        @change="onInputChange"
                        @blur="onBlur"
                    >
                        <el-option
                            v-for="(item, idx) in originDomains"
                            :key="item + idx"
                            :label="item"
                            :value="item"
                        />
                    </el-select>

                    <!-- 自定义域名输入框 -->
                    <el-input
                        v-if="selectedType === 'custom'"
                        v-model="selectedValue"
                        :placeholder="computedPlaceholder"
                        class="value-input"
                        :disabled="disabled"
                        @change="onInputChange"
                        @blur="onBlur"
                    />
                </el-tooltip>
            </div>
        </div>
        <slot name="create"></slot>
    </el-form-item>
</template>

<script>
import { isEqual } from "lodash-es";
import ctSvgIcon from "@/components/ctSvgIcon/index.vue";

export default {
    name: "hostSelectMod",
    components: { ctSvgIcon },
    props: {
        // 加速域名数组
        accelerateDomains: {
            type: Array,
            default: () => [],
        },
        // 源站域名数组
        originDomains: {
            type: Array,
            default: () => [],
        },
        // 默认选中的类型
        defaultType: {
            type: String,
            default: "custom",
            validator: value => ["accelerate", "origin", "custom"].includes(value),
        },
        // 默认值
        value: {
            type: String,
            default: "",
        },
        // 是否禁用
        disabled: {
            type: Boolean,
            default: false,
        },
        // 新增props
        hostType: {
            type: String,
            default: "reqHost", // 'reqHost' 或 'originHost'
            validator: value => ["reqHost", "originHost"].includes(value),
        },
        otherHostValue: {
            type: String,
            default: "",
        },
        label: {
            type: String,
            default: "",
        },
        prop: {
            type: String,
            default: "",
        },
        labelWidth: {
            type: String,
            default: "140px",
        },
        rules: {
            type: Array,
            default: () => [],
        },
    },
    data() {
        return {
            selectedType: this.defaultType,
            selectedValue: this.value,
            selectedIndex: -1,
            innerChange: false,
        };
    },
    computed: {
        computedPlaceholder() {
            if (this.hostType === "originHost") {
                if (this.selectedType === "custom") {
                    return this.$t("domain.detail.placeholder27");
                } else {
                    return this.$t("domain.请选择指定源站回源HOST");
                }
            } else {
                if (this.selectedType === "custom") {
                    return this.$t("domain.请输入默认回源HOST");
                } else {
                    return this.$t("domain.请选择默认回源HOST");
                }
            }
        },
    },
    watch: {
        value: {
            handler(newVal) {
                this.selectedValue = newVal;
                if (this.innerChange) {
                    this.innerChange = false;
                    return;
                } else {
                    this.initializeType();
                }
            },
            immediate: true,
        },
        defaultType(newVal) {
            this.selectedType = newVal;
        },
        accelerateDomains(newVal, oldVal) {
            if (isEqual(newVal, oldVal)) return;
            if (this.selectedType === "accelerate") {
                if (
                    this.selectedIndex <= newVal.length &&
                    this.selectedIndex >= 0 &&
                    newVal?.[this.selectedIndex]
                )
                    this.selectedValue = newVal[this.selectedIndex];
                else this.selectedType = "custom";
            }
        },
        originDomains(newVal, oldVal) {
            if (isEqual(newVal, oldVal)) return;
            if (this.selectedType === "origin") {
                if (
                    this.selectedIndex <= newVal.length &&
                    this.selectedIndex >= 0 &&
                    newVal?.[this.selectedIndex]
                )
                    this.selectedValue = newVal[this.selectedIndex];
                else this.selectedType = "custom";
            }
        },
    },
    methods: {
        // 处理类型变化
        onTypeChange(type) {
            this.selectedType = type;
            // 切换类型时清空值
            this.selectedValue = "";

            // 如果切换到加速域名且只有一个选项，自动选中
            if (type === "accelerate" && this.accelerateDomains.length === 1) {
                this.selectedValue = this.accelerateDomains[0];
            }

            this.emitChange();
        },
        // 处理值变化
        onInputChange(value) {
            this.selectedValue = value;
            this.selectedIndex = -1;
            if (["accelerate", "origin"].includes(this.selectedType)) {
                this.selectedIndex =
                    this.selectedType === "accelerate"
                        ? this.accelerateDomains.indexOf(value)
                        : this.originDomains.indexOf(value);
            }
            this.emitChange();
        },
        // 发送值变化事件
        emitChange() {
            const value = this.selectedValue;
            this.innerChange = true;
            this.$emit("input", value);
            this.$emit("change", {
                type: this.selectedType,
                value: this.selectedValue,
            });
        },
        // 根据value初始化类型
        initializeType() {
            if (!this.selectedValue) return;

            // 检查是否在加速域名列表中
            if (this.accelerateDomains.includes(this.selectedValue)) {
                this.selectedType = "accelerate";
                this.selectedIndex = this.accelerateDomains.indexOf(this.selectedValue);
                return;
            }

            // 检查是否在源站域名列表中
            if (this.originDomains.includes(this.selectedValue)) {
                this.selectedType = "origin";
                this.selectedIndex = this.originDomains.indexOf(this.selectedValue);
                return;
            }

            // 否则为自定义域名
            this.selectedType = "custom";
        },
        // 处理加速域名只有一个的情况
        handleSingleAccelerateDomain() {
            if (
                this.accelerateDomains.length === 1 &&
                this.selectedType === "accelerate" &&
                !this.selectedValue
            ) {
                this.selectedValue = this.accelerateDomains[0];
                this.emitChange();
            }
        },
        onBlur(...args) {
            this.$emit("blur", ...args);
        },
    },
};
</script>

<style lang="scss" scoped>
.host-select {
    display: flex;
    align-items: center;
    gap: 12px;

    .type-select {
        width: 120px;
        flex-shrink: 0;
    }

    .input-container {
        flex: 1;
        min-width: 150px;
        max-width: 320px;

        .value-select,
        .value-input {
            width: 100%;
        }
    }
}

// 响应式设计
@media (max-width: 768px) {
    .host-select {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;

        .type-select {
            width: 100%;
        }
    }
}

.host-select ::v-deep .el-input--suffix > .el-input__inner {
    height: 32px !important;
}
</style>
