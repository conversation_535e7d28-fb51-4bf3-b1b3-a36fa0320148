<template>
    <el-dialog
        :title="$t(isView ? 'ipSet.dialog.IP集查看' : 'ipSet.dialog.IP集编辑') + ' - ' + ipSet.alias_name"
        :visible="dialogVisible"
        :close-on-click-modal="false"
        :append-to-body="false"
        :modal-append-to-body="false"
        width="980px"
        @close="handleClose"
    >
        <div class="ip-set-edit" :class="{ 'full-width': isView }">
            <div class="table-container" :class="{ 'full-width': isView }">
                <!-- IP列表 -->
                <div class="left-table" :class="{ 'full-width': isView }">
                    <!-- 搜索栏 -->
                    <div class="search-bar">
                        <el-button
                            type="primary"
                            @click="showAddIpDialog"
                            class="el-icon-plus"
                            v-if="!isView"
                        >
                            {{ $t("ipSet.新增IP") }}
                        </el-button>
                        <!-- isView时 占位 -->
                        <div v-if="isView"></div>
                        <div class="operation-buttons">
                            <el-input
                                v-model="searchIp"
                                :placeholder="$t('ipSet.form.请输入IP')"
                                style="width: 200px"
                                clearable
                            >
                            </el-input>
                            <el-button slot="append" @click="handleSearch">查询</el-button>
                        </div>
                    </div>
                    <el-table
                        :data="ipList"
                        v-loading="loading"
                        @selection-change="handleSelectionChange"
                        ref="ipTable"
                    >
                        <el-table-column type="selection" width="55" v-if="!isView" />
                        <el-table-column :label="$t('common.table.index')" type="index" width="60" />
                        <el-table-column label="IP" prop="ip" />
                        <el-table-column :label="$t('ipSet.table.开始时间')" prop="start_time">
                            <template #default="{ row }">
                                {{ row.start_time }}
                            </template>
                        </el-table-column>
                        <el-table-column :label="$t('ipSet.table.结束时间')" prop="end_time">
                            <template #default="{ row }">
                                {{ row.end_time }}
                            </template>
                        </el-table-column>
                    </el-table>

                    <!-- 分页 -->
                    <div class="pagination-container">
                        <span class="pagination-tip">
                            <el-tooltip effect="dark" placement="top">
                                <div slot="content">{{ $t("ipSet.pagination.tip") }}</div>
                                <ct-svg-icon
                                    icon-class="question-circle"
                                    class-name="ct-sort-drag-icon"
                                ></ct-svg-icon>
                            </el-tooltip>
                            &nbsp;{{ $t("ipSet.pagination.total", { total }) }}
                        </span>
                        <el-pagination
                            :current-page="page"
                            :page-sizes="[10, 20, 50, 100]"
                            :page-size="pageSize"
                            :total="displayTotal"
                            :layout="'sizes, prev, pager, next, jumper'"
                            :pager-count="5"
                            @current-change="handleCurrentChange"
                            @size-change="handleSizeChange"
                        />
                    </div>
                </div>

                <!-- 中间箭头 -->
                <div class="middle-arrow" v-if="!isView">
                    <el-button
                        type="primary"
                        icon="el-icon-arrow-right"
                        circle
                        @click="handleAddToSelected"
                        :disabled="!selectedIps.length"
                    ></el-button>
                </div>

                <!-- 已选IP列表 -->
                <div class="right-table" v-if="!isView">
                    <div class="search-bar">
                        <span class="title">{{ $t("ipSet.已选IP列表") }}</span>
                        <el-button
                            type="danger"
                            @click="handleClearSelected"
                            :disabled="!selectedIpList.length"
                            >{{ $t("ipSet.清空") }}</el-button
                        >
                    </div>
                    <el-table :data="selectedIpList">
                        <el-table-column :label="$t('common.table.index')" type="index" width="60" />
                        <el-table-column label="IP" prop="ip" />
                        <el-table-column :label="$t('common.table.operation')" width="100">
                            <template #default="{ row }">
                                <el-button type="text" @click="handleRemoveFromSelected(row)">{{
                                    $t("ipSet.移除")
                                }}</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
            </div>

            <div class="dialog-footer" v-if="!isView">
                <el-button @click="handleSubmit">
                    {{ $t("common.dialog.close") }}
                </el-button>
                <el-button
                    type="primary"
                    :loading="loading"
                    :disabled="!selectedIpList.length"
                    @click="handleBatchDelete"
                >
                    {{ $t("ipSet.批量删除") }}
                </el-button>
            </div>
        </div>

        <!-- 新增IP对话框 -->
        <ip-set-dialog
            :visible="addDialogVisible"
            :is-add-ip="true"
            :added-ip-count="total"
            @update:visible="closeAddDialog"
            @success="handleAddIpSuccess"
        />
    </el-dialog>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from "vue-property-decorator";
import IpSetDialog from "./IpSetDialog.vue";
import { IpSetUrl } from "@/config/url/ipSet";
import ctSvgIcon from "@/components/ctSvgIcon/index.vue";

enum ConfigTypeEnum {
    Manual = "manual",
    File = "file",
}

interface IpItem {
    ip: string;
    start_time: number;
    end_time: number;
}

interface IpForm {
    configType: ConfigTypeEnum;
    ips: string;
    ipFile?: File;
    timeRange: [string, string];
}

@Component({
    components: {
        IpSetDialog,
        ctSvgIcon,
    },
})
export default class IpSetEditDialog extends Vue {
    @Prop({ type: Boolean, default: false }) visible!: boolean;
    @Prop({ type: Boolean, default: false }) isView!: boolean;
    @Prop({ type: Object, required: true }) ipSet!: any;

    // 计算属性：用于同步 visible prop
    get dialogVisible(): boolean {
        return this.visible;
    }

    private ConfigTypeEnum = ConfigTypeEnum;
    private loading = false;
    private searchIp = "";
    private selectedIps: IpItem[] = [];
    private ipList: IpItem[] = [];
    private addDialogVisible = false;
    private page = 1;
    private pageSize = 10;
    private total = 0;
    private selectedIpList: IpItem[] = [];

    // 计算最大显示的总条数，确保最多只有20页
    get displayTotal(): number {
        return Math.min(this.total, this.pageSize * 20);
    }

    $refs!: {
        ipTable: any;
    };

    created() {
        this.loadIpList();
    }
    /**
     * 异步加载IP列表
     * 此方法根据当前页码和搜索IP，从服务器获取IP列表和总数
     * 它首先检查是否存在一个有效的IP集合标识，如果不存在，则不执行任何操作
     * 然后，它设置加载状态为true，并根据当前页码和页面大小发送请求
     * 请求成功后，更新IP列表和总数，并将加载状态设置为false
     */
    private async loadIpList() {
        if (!this.ipSet?.unique_name) return;

        this.loading = true;
        // 确保页码不超过20
        const currentPage = this.page > 20 ? 20 : this.page;
        const rst = await this.$ctFetch<{ list: IpItem[]; total: number }>(IpSetUrl.listIp, {
            method: "GET",
            data: {
                unique_name: this.ipSet.unique_name,
                ip: this.searchIp,
                pageIndex: currentPage,
                pageSize: this.pageSize,
            },
        });
        this.ipList = rst.list;
        this.total = rst.total;
        this.loading = false;
    }
    /**
     * 处理搜索请求的方法
     *
     * 该方法被调用时，会将页码重置为第1页，并调用loadIpList方法来加载IP列表
     * 这通常用于用户执行搜索操作时，重新获取数据以响应用户的搜索请求
     */

    private handleSearch() {
        this.page = 1;
        this.loadIpList();
    }
    /**
     * 处理每页条数变化的事件
     *
     * 当用户在分页组件中选择不同的每页条数时，这个方法会被调用
     * 它负责更新每页条数、重置当前页码，并重新加载IP列表数据
     *
     * @param size 每页显示的条数，由分页组件触发的事件传递
     */
    private handleSizeChange(size: number) {
        this.pageSize = size;
        this.page = 1; // 切换每页条数时重置为第一页
        this.loadIpList();
    }
    /**
     * 处理当前页码变化的事件
     *
     * 当用户在分页组件中选择不同的页码时，这个方法会被调用
     * 它负责更新当前页码，并重新加载IP列表数据
     *
     * @param currentPage 当前选中的页码，由分页组件触发的事件传递
     */
    private handleCurrentChange(currentPage: number) {
        this.page = currentPage;
        this.loadIpList();
    }
    /**
     * 监听visible prop的变化
     *
     * 当visible prop发生变化时，这个方法会被调用
     * 它负责更新页码为1，并重新加载IP列表数据
     *
     */
    @Watch("visible")
    private onVisibleChange(newVal: boolean) {
        if (newVal) {
            this.page = 1;
            this.loadIpList();
        }
    }
    /**
     * 处理表格选择变化的事件
     *
     * 当用户在表格中选择不同的行时，这个方法会被调用
     * 它负责更新选中的IP列表，并清除当前选定的IP地址和表格中的选择
     */
    private handleSelectionChange(selection: IpItem[]) {
        this.selectedIps = selection;
    }
    /**
     * 将选定的IP地址添加到已选IP列表中
     * 此方法首先检查尝试添加的IP地址是否已存在于已选IP列表中，如果存在，则显示警告消息
     * 之后，它将去重并更新已选IP列表，同时清除当前选定的IP地址和表格中的选择
     */
    private handleAddToSelected() {
        // 检查重复的IP
        const duplicateIps = this.selectedIps.filter(ip =>
            this.selectedIpList.some(existingIp => existingIp.ip === ip.ip)
        );

        if (duplicateIps.length > 0) {
            this.$message.warning(
                this.$t("ipSet.validation.IP已存在", {
                    ips: duplicateIps.map(ip => ip.ip).join(", "),
                }).toString()
            );
        }

        this.selectedIpList = [...new Set([...this.selectedIpList, ...this.selectedIps])]; // 去重后添加到右侧
        this.selectedIps = [];
        this.$refs.ipTable.clearSelection();
    }
    /**
     * 处理清除已选IP列表的方法
     *
     * 当用户点击清除按钮时，这个方法会被调用
     * 它负责清空已选IP列表，并触发success事件和update:visible事件以关闭对话框
     */
    private handleClearSelected() {
        this.selectedIpList = [];
    }
    /**
     * 处理从已选IP列表中移除IP的方法
     *
     * 当用户点击移除按钮时，这个方法会被调用
     * 它负责从已选IP列表中移除指定的IP地址，并更新已选IP列表
     */
    private handleRemoveFromSelected(row: IpItem) {
        this.selectedIpList = this.selectedIpList.filter(item => item.ip !== row.ip);
    }

    /**
     * 删除IP列表
     * @param ips 要删除的IP列表
     * @param confirmMessage 确认提示信息
     * @param successMessage 成功提示信息
     * @param errorMessage 错误提示信息
     */
    private async deleteIps(
        ips: string[],
        confirmMessage: string,
        successMessage: string,
        errorMessage: string
    ) {
        if (!ips.length) return;

        try {
            await this.$confirm(confirmMessage, this.$t("common.messageBox.title").toString(), {
                type: "warning",
            });

            await this.$ctFetch(IpSetUrl.deleteIp, {
                method: "POST",
                data: {
                    unique_name: this.ipSet.unique_name,
                    list: ips,
                },
                headers: {
                    "Content-Type": "application/json",
                },
            });

            // 删除成功后重新加载列表
            this.loadIpList();
            this.$message.success(successMessage);
            return true; // 表示删除成功
        } catch (e) {
            // 用户点击取消按钮时，不显示错误信息
            if (e !== "cancel") {
                this.$errorHandler(e);
            }
            return false; // 表示删除被取消或失败
        }
    }
    /**
     * 处理批量删除操作
     * 此函数负责调用deleteIps方法，传入选中的IP列表、确认删除的提示信息、删除成功的信息和删除失败的信息
     * 它只在删除成功后清空选中的IP列表，并触发success事件和update:visible事件以关闭对话框
     */
    private async handleBatchDelete() {
        const isDeleted = await this.deleteIps(
            this.selectedIpList.map(item => item.ip),
            this.$t("ipSet.confirm.确定要删除选中的IP吗？").toString(),
            this.$t("ipSet.success.批量删除成功").toString(),
            this.$t("ipSet.error.批量删除失败").toString()
        );
        // 只有在删除成功时才清空选中项
        if (isDeleted) {
            this.selectedIpList = [];
            this.$emit("success");
            this.$emit("update:visible", false);
        }
    }
    /**
     * 显示新增IP对话框
     * 此方法负责将addDialogVisible设置为true，以显示新增IP对话框
     */
    private showAddIpDialog() {
        this.addDialogVisible = true;
    }
    /**
     * 关闭新增IP对话框
     * 此方法负责将addDialogVisible设置为false，以关闭新增IP对话框
     */
    private closeAddDialog() {
        this.addDialogVisible = false;
    }
    /**
     * 异步提交表单的方法
     *
     * 此方法在表单提交时被调用，它执行以下操作：
     * 1. 触发一个名为'success'的自定义事件，通知父组件表单提交成功
     * 2. 调用handleClose方法来关闭表单对话框或组件
     * 3. 触发一个名为'update:visible'的自定义事件，并传递false值，用于在父组件中更新表单的可见状态
     */
    private async handleSubmit() {
        this.$emit("success");
        this.handleClose();
        this.$emit("update:visible", false);
    }
    /**
     * 异步处理IP添加成功的逻辑
     *
     * 此函数被设计为异步函数，以便能够使用await语法来等待异步操作完成
     * 它接受一个参数params，该参数包含要添加到IP集合的IP信息
     * 函数的目的是将新的IP信息发送到服务器进行添加，然后触发成功消息和事件通知
     *
     * @param params 一个包含IP信息的对象，用于添加到IP集合中
     */
    private async handleAddIpSuccess(params: any) {
        await this.$ctFetch(IpSetUrl.addIp, {
            method: "POST",
            data: {
                unique_name: this.ipSet.unique_name,
                ...params,
            },
        });
        this.$message.success(this.$t("ipSet.success.IP添加成功").toString());
        this.$emit("success");
        this.$emit("update:visible", false);
    }
    /**
     * 处理关闭对话框的方法
     *
     * 当用户点击关闭按钮时，这个方法会被调用
     * 它负责触发一个名为'update:visible'的自定义事件，并传递false值，用于在父组件中更新表单的可见状态
     */
    private handleClose() {
        this.$emit("update:visible", false);
    }
}
</script>

<style lang="scss" scoped>
.ip-set-edit {
    display: flex;
    flex-direction: column;
    height: 550px;
}

:deep(.el-dialog__title) {
    display: inline-block;
    max-width: 800px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    vertical-align: middle;
}

.search-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .operation-buttons {
        display: flex;
        gap: 10px;
    }
}

.table-container {
    display: flex;
    gap: 10px;
    flex: 1;
    min-height: 0;

    .left-table {
        width: 65%;
        display: flex;
        flex-direction: column;

        &.full-width {
            width: 100%;
        }

        .el-table {
            height: 425px !important;

            :deep(.el-table__body-wrapper) {
                overflow-y: auto;
            }
        }

        .pagination-container {
            display: flex;
            justify-content: flex-end;
            align-items: center;

            .pagination-tip {
                margin-right: 15px;
                font-size: 12px;
                display: flex;
                align-items: center;
            }
        }
    }

    .middle-arrow {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 10px;
    }

    .right-table {
        width: 30%;
        display: flex;
        flex-direction: column;

        .el-table {
            height: 425px !important;

            :deep(.el-table__body-wrapper) {
                overflow-y: auto;
            }
        }
    }
}

.dialog-footer {
    text-align: right;
}

.ip-upload {
    :deep(.el-upload) {
        width: 100%;
    }

    :deep(.el-upload-dragger) {
        width: 100%;
        height: 120px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .upload-content {
        text-align: center;

        p {
            margin: 0;
            font-size: 14px;
            color: #606266;

            &.download-tip {
                margin-top: 8px;
                font-size: 12px;
                color: #909399;
            }
        }

        em {
            color: #409eff;
            font-style: normal;
            cursor: pointer;
        }
    }
}

.template-link {
    color: #409eff;
    margin-left: 10px;
    text-decoration: none;

    &:hover {
        text-decoration: underline;
    }
}

.time-tip {
    margin-top: 8px;
    font-size: 12px;
    color: #909399;
}
</style>
