<template>
    <el-select
        v-model="valueInside"
        multiple
        :placeholder="placeholder"
        collapse-tags
        clearable
        popper-class="pop_select_down"
        @visible-change="handleVisibleChange"
        @change="handleSelectCheck"
        v-on="$listeners"
    >
        <div class="flex-row-style pubMarginBottom">
            <div v-if="noFilter" style="width: 100%;margin-bottom: 8px;">
                <el-checkbox
                    v-if="isCheckedAll && hasData"
                    v-model="isSelectAll"
                    style="padding: 0 5px 0 20px"
                    @change="handleSelectAll"
                >
                    全选
                </el-checkbox>
            </div>
            <template v-else>
                <el-checkbox
                    v-if="isCheckedAll && hasData"
                    v-model="isSelectAll"
                    style="padding: 0 5px 0 20px"
                    @change="handleSelectAll"
                />
            </template>
            <el-input
                v-if="!noFilter"
                v-model.trim="filterMsg"
                style="width: 70%"
                suffix-icon="el-icon-search"
                placeholder="请输入"
                @input="filterFun"
            />
        </div>
        <el-checkbox-group :value="valueInside">
            <el-option
                v-for="(item, index) of dataShowAry"
                :label="item.label"
                :value="item.value"
                :key="index"
                :disabled="item.disabled"
            >
                <span v-if="item.label === '暂无数据'" style="margin-left: 35%">暂无数据</span>
                <el-checkbox
                    v-else
                    :label="item.value"
                    @click.native.stop.prevent="handleCheckBoxChange(item.value, item.disabled)"
                    :disabled="item.disabled"
                    >{{ item.label }}</el-checkbox
                >
            </el-option>
        </el-checkbox-group>
    </el-select>
</template>

<script>
import { cloneDeep, last } from "lodash-es";

export default {
    props: {
        value: {
            type: Array,
            default: () => [],
        },
        data: {
            type: Array,
            default: () => [],
        },
        alternative: {
            type: [String, Object],
            default: null,
        },
        isCheckedAll: {
            type: Boolean,
            default: true,
        },
        placeholder: {
            type: String,
            default: "请选择",
        },
        filterId: {
            type: Boolean,
            default: false,
        },
        noFilter: {
            type: Boolean,
            default: false,
        },
    },
    model: {
        prop: "value",
        event: "change",
    },
    data() {
        return {
            valueInside: [],
            isSelectAll: false,
            filterMsg: "",
            dataSource: [],
        };
    },
    computed: {
        /**
         * 展示数据
         */
        dataShowAry() {
            if (this.alternative === null) {
                return this.dataSource;
            }

            if (!this.value.length) {
                return this.dataSource;
            }

            let ary = null;
            if (this.value.includes(this.alternative)) {
                ary = this.dataSource.map(item => {
                    return {
                        ...item,
                        disabled: item.disabled || item.value !== this.alternative,
                    };
                });
            } else {
                ary = this.dataSource.map(item => {
                    return {
                        ...item,
                        disabled: item.disabled || item.value === this.alternative,
                    };
                });
            }

            return ary;
        },
        hasData() {
            if (JSON.stringify(this.dataSource) === JSON.stringify([{ label: "暂无数据", disabled: true }])) {
                return false;
            }

            return true;
        },
    },
    watch: {
        value: {
            handler(val) {
                this.valueInside = val;
                this.$nextTick(() => {
                    if (val.length === this.data.length && this.data.length) {
                        this.isSelectAll = true;
                    } else {
                        this.isSelectAll = false;
                    }
                });
            },
            immediate: true,
        },
        data: {
            handler(val) {
                this.dataSource = cloneDeep(val);
            },
            immediate: true,
        },
    },
    methods: {
        /**
         * 过滤函数
         */
        filterFun(val) {
            if (!val) {
                this.dataSource = cloneDeep(this.data);
                return;
            }

            const ary = this.data.filter(item => {
                if (
                    item.label.indexOf(val) > -1 ||
                    item.label.toUpperCase().indexOf(val.toUpperCase()) > -1
                ) {
                    return item;
                }

                if (!this.filterId) {
                    return;
                }

                if (
                    item.value.indexOf(val) > -1 ||
                    item.value.toUpperCase().indexOf(val.toUpperCase()) > -1
                ) {
                    return item;
                }
            });
            if (ary.length) {
                this.dataSource = ary;
            } else {
                this.dataSource = [{ label: "暂无数据", disabled: true }];
            }
        },
        /**
         * 处理全选
         */
        handleSelectAll(val) {
            if (!val) {
                this.$emit("change", []);
                return;
            }

            const ary = this.data.map(item => {
                return item.value;
            });
            this.$emit("change", ary);
        },
        /**
         * 处理下拉框隐藏/显示
         */
        handleVisibleChange(val) {
            if (!val) {
                return;
            }

            this.filterMsg = "";
            this.filterFun("");
        },
        /**
         * 处理选中
         */
        handleSelectCheck(val) {
            const res = this.beforeEmit(val);
            this.$emit("change", res);
        },
        /**
         * 处理checkbox变化
         * @param val
         */
        handleCheckBoxChange(val, disabled) {
            if (disabled) {
                return;
            }

            const ary = cloneDeep(this.valueInside);
            const index = ary.indexOf(val);
            if (index === -1) {
                ary.push(val);
                const res = this.beforeEmit(ary);
                this.$emit("change", res);
                return;
            }

            ary.splice(index, 1);
            const res = this.beforeEmit(ary);
            this.$emit("change", res);
        },
        /**
         * 回调前处理
         */
        beforeEmit(ary) {
            if (this.alternative === null) {
                return ary;
            }

            const lastItem = last(ary);
            if (lastItem === this.alternative) {
                return [this.alternative];
            }

            return ary.filter(item => {
                return item !== this.alternative;
            });
        },
    },
};
</script>

<style scoped lang="scss">
.pubMarginBottom {
    justify-content: center;
}
</style>
