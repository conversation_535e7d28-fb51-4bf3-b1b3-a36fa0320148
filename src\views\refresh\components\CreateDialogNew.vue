<template>
    <div>
        <el-dialog
            :title="dialogTitle"
            :close-on-click-modal="false"
            :visible.sync="addVisible"
            :before-close="cancel"
            class="add-dialog"
            width="800px"
        >
            <el-form
                :model="addTask"
                ref="addForm"
                class="refresh-form"
                :label-width="isEn ? '120px' : '80px'"
                label-position="left"
                :rules="formRules"
            >
                <el-form-item :label="$t('refresh.create.operationLabel')" prop="refresh_type" required>
                    <el-select
                        v-model="addTask.refresh_type"
                        class="refresh-form--selector"
                        :disabled="!!taskId"
                    >
                        <el-option
                            v-for="item in SchduelTaskTypeList"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        ></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <div class="form-info">
                        <i class="el-alert__icon el-icon-warning-outline"></i>
                        <div>
                            <div
                                class="tips-warn"
                                v-for="tip in $t(tips[addTask.refresh_type], { size: valuesSize }).split(
                                    '\n'
                                )"
                                :key="tip"
                            >
                                {{ tip }}
                            </div>
                        </div>
                    </div>
                </el-form-item>
                <el-form-item label="任务类型" prop="task_type">
                    <el-radio-group v-model="addTask.task_type" @change="changeTaskType" :disabled="!!taskId">
                        <el-radio :label="SchduelTaskTypeEnum.appoint">预约</el-radio>
                        <el-radio :label="SchduelTaskTypeEnum.loop">循环</el-radio>
                    </el-radio-group>
                </el-form-item>
                <template v-if="addTask.task_type === SchduelTaskTypeEnum.appoint">
                    <el-form-item
                        label="日期"
                        label-width="130px"
                        prop="plan_datetime"
                        style="margin-top: 24px;"
                    >
                        <el-date-picker
                            v-model="addTask.plan_datetime"
                            type="datetime"
                            placeholder="选择日期时间"
                            value-format="yyyy-MM-dd HH:mm:ss"
                            default-time="23:59:59"
                            :picker-options="pickerOptionsOfBook"
                            :clearable="false"
                            popper-class="aocdn-ignore-schedule-time-appoint-date-picker"
                        >
                        </el-date-picker>
                    </el-form-item>
                </template>
                <template v-else-if="addTask.task_type === SchduelTaskTypeEnum.loop">
                    <el-form-item
                        label="刷新时间"
                        label-width="154px"
                        prop="loop_date"
                        style="margin-top: 24px;"
                        :key="SchduelTaskTypeEnum.loop"
                    >
                        <div class="loop-date-time-wrapper">
                            <el-date-picker
                                v-model="addTask.loop_date"
                                type="daterange"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                                value-format="yyyy-MM-dd"
                                class="daterange-picker-wrapper"
                                :picker-options="pickerOptionsOfLoop"
                                :clearable="false"
                            >
                            </el-date-picker>
                            <el-time-picker
                                v-model="addTask.plan_time"
                                :clearable="false"
                                class="aocdn-ignore-schedule-time-picker-wrapper"
                                value-format="HH:mm:ss"
                                placeholder="选择时间"
                            >
                            </el-time-picker>
                        </div>
                    </el-form-item>
                    <el-form-item
                        label="刷新频率"
                        label-width="154px"
                        prop="loop_frequency"
                        style="margin-top: 24px;"
                    >
                        <el-radio-group v-model="addTask.loop_frequency">
                            <el-radio :label="SchduelFrequencyEnum.cron_everyday">每天</el-radio>
                            <el-radio :label="SchduelFrequencyEnum.cron_week"
                                >每周
                                <el-select
                                    v-model="addTask.cron_week"
                                    multiple
                                    collapse-tags
                                    clearable
                                    style="width: 140px;"
                                    :disabled="addTask.loop_frequency !== SchduelFrequencyEnum.cron_week"
                                >
                                    <el-option label="一" :value="1"></el-option>
                                    <el-option label="二" :value="2"></el-option>
                                    <el-option label="三" :value="3"></el-option>
                                    <el-option label="四" :value="4"></el-option>
                                    <el-option label="五" :value="5"></el-option>
                                    <el-option label="六" :value="6"></el-option>
                                    <el-option label="日" :value="0"></el-option>
                                </el-select>
                            </el-radio>
                            <el-radio :label="SchduelFrequencyEnum.cron_monday"
                                >每月
                                <el-select
                                    v-model="addTask.cron_monday"
                                    multiple
                                    collapse-tags
                                    clearable
                                    style="width: 140px;"
                                    :disabled="addTask.loop_frequency !== SchduelFrequencyEnum.cron_monday"
                                >
                                    <el-option v-for="i in 31" :key="i" :label="i" :value="i">{{
                                        i
                                    }}</el-option>
                                </el-select>
                            </el-radio>
                        </el-radio-group>
                        <el-tooltip>
                            <div slot="content">
                                <span>如果当月不存在所选择的日则不执行。</span>
                            </div>
                            <ct-svg-icon
                                style="margin-left: 8px; font-size: 14px;"
                                icon-class="question-circle"
                            ></ct-svg-icon>
                        </el-tooltip>
                    </el-form-item>
                </template>
                <el-form-item label="刷新内容" required style="margin-top: 32px;">
                    <div class="area-container">
                        <el-input
                            type="textarea"
                            :spellcheck="false"
                            :placeholder="placeholders[addTask.refresh_type]"
                            v-model="addTask.content"
                            :autosize="{ minRows: 10, maxRows: 10 }"
                        ></el-input>
                        <span class="count-span">{{ values.length + "/" + valuesSize }}</span>
                    </div>
                </el-form-item>
            </el-form>
            <div v-loading="loading" slot="footer">
                <el-button class="dialog-btn" @click="cancel">{{ $t("refresh.create.cancel") }} </el-button>
                <el-button type="primary" class="dialog-btn" @click="submit">{{
                    $t("refresh.create.confirm")
                }}</el-button>
            </div>
        </el-dialog>
        <operation-feedback-dialog
            v-if="showFeedback"
            :domain-list="disabledList"
            :has-continue="hasContinue"
            @cancel="showFeedback = false"
            @continue="operateContinue"
        />
    </div>
</template>

<script lang="ts">
import { Component, Prop, Vue, Watch } from "vue-property-decorator";
import pattern from "@/config/pattern";
import { refreshUrl } from "@/config/url";
import { DomainModule } from "@/store/modules/domain";
import { DomainActionEnum, getCtiamAction } from "@/config/map";
import { nUserModule } from "@/store/modules/nuser";
import OperationFeedbackDialog from "./OperationFeedbackDialog.vue";
import ctSvgIcon from "@/components/ctSvgIcon/index.vue";
import {
    getDefaultSchedualTask,
    SchduelTaskParam,
    SchduelRefreshTypeEnum,
    SchduelTaskTypeEnum,
    SchduelFrequencyEnum,
    RefreshCheckResultItem,
} from "../util";
import { DatePickerOptions } from "element-ui/types/date-picker";
import { getAm0 } from "@/utils";
import { ElForm } from "@cutedesign/ui";

@Component({
    name: "refreshCreate",
    components: {
        OperationFeedbackDialog,
        ctSvgIcon,
    },
})
export default class UpdateDialog extends Vue {
    private pattern = pattern;
    private loading = false;
    private showFeedback = false;
    private disabledList: RefreshCheckResultItem[] = [];
    private enabledList: RefreshCheckResultItem[] = [];
    private hasContinue = false;
    @Prop({ default: false, type: Boolean }) private addVisible!: boolean; // 弹框显示标志位
    @Prop({ default: false, type: Boolean }) private dialogLoading!: boolean; // 加载状态标志位
    @Prop({ default: getDefaultSchedualTask() }) private addTask!: SchduelTaskParam;
    @Prop({ default: "", type: Number }) private taskId!: number; // 是否是编辑状态

    @Watch("dialogLoading")
    onDialogLoadingChange(val: boolean) {
        this.loading = val;
    }

    get dialogTitle() {
        return this.taskId ? `编辑任务` : `新增任务`;
    }

    get isEn() {
        return nUserModule.lang === "en";
    }
    get tips() {
        return {
            [SchduelRefreshTypeEnum.url]: "refresh.create.tip[0]",
            [SchduelRefreshTypeEnum.dir]: "refresh.create.tip[1]",
            [SchduelRefreshTypeEnum.re]: "refresh.create.tip[3]",
        };
    }
    get SchduelTaskTypeEnum() {
        return SchduelTaskTypeEnum;
    }
    get SchduelFrequencyEnum() {
        return SchduelFrequencyEnum;
    }
    get SchduelTaskTypeList() {
        return [
            { label: this.$t("refresh.common.tab[0]"), value: SchduelRefreshTypeEnum.url },
            { label: this.$t("refresh.common.tab[1]"), value: SchduelRefreshTypeEnum.dir },
            { label: this.$t("refresh.common.tab[2]"), value: SchduelRefreshTypeEnum.re },
        ];
    }
    get formRules() {
        return {
            refresh_type: [
                {
                    required: true,
                    message: "请选择任务类型",
                    trigger: "change",
                },
            ],
            task_type: [
                {
                    required: true,
                    message: "请选择任务类型",
                    trigger: "change",
                },
            ],
            plan_datetime: [
                {
                    required: true,
                    message: "请选择日期",
                    trigger: "blur",
                },
                {
                    validator: (rule: any, value: any, callback: any) => {
                        if (value && new Date(value).getTime() < new Date().getTime()) {
                            callback(new Error("预约不能早于当前时间"));
                        } else {
                            callback();
                        }
                    },
                    trigger: "blur",
                },
            ],
            loop_date: [
                {
                    required: true,
                    message: "请选择日期",
                    trigger: "blur",
                },
                {
                    validator: (rule: any, value: Date[], callback: any) => {
                        if (
                            new Date(`${value[0]} ${this.addTask.plan_time}`).getTime() >
                                new Date().getTime() ||
                            this.taskId
                        ) {
                            callback();
                        } else {
                            callback(new Error("起始日期+时间必须大于当前时间"));
                        }
                    },
                },
            ],
            loop_frequency: [
                {
                    required: true,
                    message: "请选择刷新频率",
                    trigger: "change",
                },
                {
                    validator: (rule: any, value: SchduelFrequencyEnum, callback: any) => {
                        if (value !== "cron_everyday" && this.addTask[value].length === 0) {
                            callback(new Error("请选择刷新频率"));
                        } else {
                            callback();
                        }
                    },
                    trigger: "change",
                },
            ],
        };
    }
    get placeholders() {
        const url = nUserModule.isCtclouds ? "www.esurfingcloud.com" : "www.ctyun.com.cn";
        return {
            [SchduelRefreshTypeEnum.url]: this.$t("refresh.create.placeholder[0]", { url }),
            [SchduelRefreshTypeEnum.dir]: this.$t("refresh.create.placeholder[1]", { url }),
            [SchduelRefreshTypeEnum.re]: this.$t("refresh.create.placeholder[3]", { url }),
        };
    }
    get pickerOptionsOfBook(): DatePickerOptions {
        return {
            disabledDate: (time: Date) => {
                const today0 = getAm0(new Date());
                return time.getTime() < today0.getTime();
            },
        };
    }
    get pickerOptionsOfLoop(): DatePickerOptions {
        return {
            disabledDate: (time: Date) => {
                const today0 = getAm0(new Date());
                return time.getTime() < today0.getTime();
            },
        };
    }
    get values() {
        // 处理换行、所有空格、过滤空数据
        return this.addTask.content
            ? this.addTask.content
                  .split("\n")
                  .map((val: string) => val.replace(/\s*/g, ""))
                  .filter((val: any) => val)
            : [];
    }
    get domainList() {
        return DomainModule[DomainActionEnum.Refresh].list;
    }
    get wildDomainList() {
        // 以*开头的泛域名，移除第一位
        return this.domainList.filter(d => d.startsWith("*")).map(d => d.slice(1));
    }
    get rules() {
        const { url, catalogue, regUrl } = this.pattern;
        return {
            [SchduelRefreshTypeEnum.dir]: catalogue,
            [SchduelRefreshTypeEnum.url]: url,
            [SchduelRefreshTypeEnum.re]: regUrl,
        }[this.addTask.refresh_type];
    }
    get remainLength() {
        return this.values.length < this.valuesSize ? this.valuesSize - this.values.length : 0;
    }

    get valuesSize() {
        if (this.addTask.refresh_type === SchduelRefreshTypeEnum.re) {
            // 正则刷新，底层限制10个
            return 10;
        }
        return this.addTask.refresh_type === SchduelRefreshTypeEnum.url ? 1000 : 50;
    }

    get isFcdnCtyunCtclouds() {
        return nUserModule.isFcdnCtyunCtclouds;
    }

    patternCheck() {
        // 保证每一个URL的格式正确
        return this.values.every((val: string) => {
            const p = new RegExp(this.rules.pattern);
            return p.test(val);
        });
    }
    private getSubmitTask() {
        const task: {
            [key: string]: any;
        } = {
            refresh_type: this.addTask.refresh_type,
            task_type: this.addTask.task_type,
            urls: this.enabledList.map(itm => itm.url),
        };

        if (task.task_type === SchduelTaskTypeEnum.appoint) {
            task.plan_time = {
                plan_datetime: this.addTask.plan_datetime,
            };
        } else {
            task.plan_time = {
                eff_date: this.addTask.loop_date[0],
                exp_date: this.addTask.loop_date[1],
                plan_time: this.addTask.plan_time,
                cron_everyday: 0,
                cron_week: "",
                cron_monday: "",
            };
            if (this.addTask.loop_frequency === "cron_everyday") {
                task.plan_time.cron_everyday = 1;
            } else {
                task.plan_time[this.addTask.loop_frequency] = [...this.addTask[this.addTask.loop_frequency]]
                    .sort((a, b) => a - b)
                    .join(",");
            }
        }

        if (this.taskId) {
            task.id = this.taskId;
        }

        return task;
    }
    private async submit() {
        const valid = await new Promise(resolve => (this.$refs.addForm as ElForm).validate(resolve));
        if (!valid) return;

        if (this.values.length === 0) return this.$message.error(this.$t("refresh.create.msg1") as string);

        if (this.values.length > this.valuesSize)
            return this.$message.error(this.$t("refresh.create.msg2", { size: this.valuesSize }) as string);

        if (!this.patternCheck()) {
            const msg = `${this.$t(this.rules.message)}` || "";
            this.$message.error(msg);
            return;
        }
        this.loading = true;
        let action = "";
        if (window.__POWERED_BY_QIANKUN__) {
            action = "accessone_domain";
        } else {
            if (this.isFcdnCtyunCtclouds) {
                action = getCtiamAction("RefreshPreloadLabel");
            } else {
                action = DomainActionEnum.Refresh;
            }
        }
        const rst = await this.$ctFetch<RefreshCheckResultItem[]>(refreshUrl.checkDomainPrivilege, {
            method: "POST",
            body: {
                data: {
                    urls: this.values,
                    action: action,
                },
            },
        });
        this.loading = false;
        this.disabledList = [];
        this.enabledList = [];
        this.hasContinue = false;
        // 如果鉴权不通过，则报错提示
        rst.forEach(item => {
            if (item.enable === "true") {
                this.enabledList.push(item);
            } else if (item.enable === "false") {
                this.disabledList.push(item);
            }
        });
        if (this.enabledList.length) {
            this.hasContinue = true;
        }
        if (this.disabledList.length) {
            this.showFeedback = true;
        } else {
            this.$emit("submit", this.getSubmitTask());
        }
        return;
    }
    private changeTaskType() {
        (this.$refs.addForm as ElForm).clearValidate();
    }
    private operateContinue() {
        this.$emit("submit", this.getSubmitTask());
    }
    private cancel() {
        (this.$refs.addForm as ElForm).clearValidate();
        this.$emit("cancel");
    }
}
</script>

<style lang="scss" scoped>
.el-form-item {
    margin-bottom: 0;
}
.form-info {
    display: flex;
    .el-alert__icon {
        color: #ff842e;
        margin-right: 8px;
    }
    font-size: 12px;
    color: #333333;
    line-height: 18px;
}
.add-dialog {
    .form-info {
        margin: 10px 0;
        display: flex;
        .el-alert__icon {
            color: #7c818c;
            margin-right: 8px;
        }
        font-size: 12px;
        color: #7c818c;
        line-height: 18px;
    }
    .tips {
        display: inline-block;
        font-size: 12px;
        color: #7c818c;
    }
    .tips + .tips {
        margin-left: 20px;
    }

    .dialog-icon {
        margin-left: 140px;
        color: #333;
        font-size: 18px;
    }

    .alert-wrapper {
        margin-bottom: 16px;
    }

    .dialog-btn {
        width: 100px;
    }

    ::v-deep .el-dialog {
        @include g-width(90%, 60%, 45%);
    }

    .refresh-form {
        .refresh-form--selector {
            width: 100%;
            max-width: none;
        }
        .seperat-label {
            margin-bottom: 0;
        }
        .area-container {
            position: relative;
            margin-bottom: 20px;
            .count-span {
                position: absolute;
                bottom: 0;
                right: 20px;
                color: #999;
            }
        }
        ::v-deep {
            .el-textarea {
                width: 100%;
                & > textarea {
                    padding-right: 60px;
                }
            }
            .el-form-item__label {
                display: inline-block;
                @include g-width(100%, 160px, 160px);
                @include g-media-attr(
                    (
                        attr: text-align,
                        md: right,
                        sm: right,
                        xs: left,
                    )
                );
            }

            .el-form-item__content {
                @include g-width(100%, auto, auto);
                @include g-media-attr(
                    (
                        attr: margin-left,
                        md: 160px,
                        sm: 160px,
                        xs: 0,
                    )
                );
            }
        }
    }
}
.loop-date-time-wrapper {
    display: flex;
    gap: 12px;
    align-items: center;
}
.daterange-picker-wrapper::v-deep {
    .el-range-input {
        background-color: unset !important;
    }
}
</style>
<style lang="scss">
.aocdn-ignore-schedule-time-picker-wrapper {
    width: 123px !important;
    .el-input__inner {
        padding-right: 12px !important;
    }
}
.aocdn-ignore-schedule-time-appoint-date-picker {
    > .el-picker-panel__footer .el-picker-panel__link-btn.el-button--text {
        display: none;
    }
}
</style>
