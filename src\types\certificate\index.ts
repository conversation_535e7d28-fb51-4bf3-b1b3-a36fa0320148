export interface CertItem {
    expiredCount: number;
    paging: PageItem;
    secrets: SecretsItem[];
}
export interface PageItem {
    page: number;
    per_page: number;
    total_page: number;
    total_record: number;
}

export interface DetailItem { domain: string, product_code: string }

export interface SecretsItem {
    cn: string;
    created: number | string;
    expires: number;
    issue: number;
    issuer: string;
    name: string;
    remainTime: number;
    sans: DetailItem [];
    certs?: string;
    key?: string;
    validTime?: string;
    id?: string;
    is_chain_complete: 0 | 1;
    algorithm_type?: 0 | 1;
}

export interface VersionItem {
    update_time: string;
    certificate_id: number | string;
    certificate_version: string;
    sans: string;
    issue_at: string;
    expires_at: string;
    operate_type: number;
}
