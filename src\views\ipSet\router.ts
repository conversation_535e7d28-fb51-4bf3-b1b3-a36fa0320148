import { RouteConfig } from "vue-router";

const ipSetRouter: RouteConfig[] = [
    {
        path: "/ipset",
        name: "ipset",
        component: () => import("./index.vue"),
        meta: {
            breadcrumb: {
                title: "$t('ipSet.title')",
                route: ["ipset"],
            },
            perm: "ipset",
        },
        children: [
            {
                path: "list",
                name: "ipset.list",
                component: () => import("./list.vue"),
                meta: {
                    breadcrumb: {
                        title: "$t('ipSet.title')",
                        route: ["ipset"],
                    },
                    perm: "ipset",
                },
            },
            {
                path: "batchBind",
                name: "ipset.batchBind",
                component: () => import("@/views/certificate/batchBind/index.vue"),
                meta: {
                    breadcrumb: {
                        title: '$t("certificate.title2")',
                        route: ["ipset", "ipset.batchBind"],
                    },
                    perm: "ipset",
                },
            },
        ],
    },
];

export default ipSetRouter;
