<template>
    <ct-section-wrap :headerText="getHeaderText">
        <ct-box :class="['fix-box', 'table-scroll-wrap']">
            <div class="box-header-title">
                <h3>说明：</h3>
                1、字典名称：2-64位，只支持小写字母、数字、下划线，开头结尾只允许小写字母和数字<br />
                2、字典最大100M，单位支持KB和MB，1~1023KB、1~100MB<br />
                3、最多配置5个全局字典，且名称不能相同<br />
            </div>
            <div class="dictionary-table">
                <el-form
                    :model="form"
                    status-icon
                    :rules="rules"
                    ref="dictionaryForm"
                    label-width="100px"
                    class="demo-ruleForm"
                >
                    <el-table :data="form.dicts" :show-header="false" border :loading="loading">
                        <el-table-column prop="dict_name">
                            <template slot-scope="scope">
                                <el-form-item
                                    label="字典名称"
                                    :prop="'dicts.' + scope.$index + '.dict_name'"
                                    :rules="rules.dict_name"
                                >
                                    <el-input
                                        type="text"
                                        :disabled="isDisabled"
                                        v-model.trim="scope.row.dict_name"
                                        @blur="handleBlur()"
                                    ></el-input>
                                </el-form-item>
                            </template>
                        </el-table-column>
                        <el-table-column prop="dict_size">
                            <template slot-scope="scope">
                                <el-form-item
                                    label="字典大小"
                                    :prop="'dicts.' + scope.$index + '.dict_size'"
                                    :rules="rules.dict_size"
                                >
                                    <el-input
                                        type="text"
                                        :disabled="isDisabled"
                                        maxlength="4"
                                        v-model.trim.number="scope.row.dict_size"
                                    >
                                        <template slot="append">
                                            <el-select
                                                v-model="scope.row.dict_type"
                                                :disabled="isDisabled"
                                                placeholder="请选择"
                                            >
                                                <el-option
                                                    v-for="item in dictionarySizeOption"
                                                    :key="item.value"
                                                    :label="item.label"
                                                    :value="item.value"
                                                >
                                                </el-option>
                                            </el-select>
                                        </template>
                                    </el-input>
                                </el-form-item>
                            </template>
                        </el-table-column>
                        <el-table-column width="200">
                            <template slot-scope="scope">
                                <el-form-item label-width="0">
                                    <div class="operation-box">
                                        <el-button
                                            type="text"
                                            icon="el-icon-circle-plus-outline"
                                            class="operation-add"
                                            :disabled="isDisabled"
                                            @click="handleAdd()"
                                        ></el-button>
                                        <el-button
                                            type="text"
                                            icon="el-icon-delete"
                                            class="operation-del"
                                            :disabled="isDisabled"
                                            @click="handleDel(scope.$index)"
                                        ></el-button>
                                    </div>
                                </el-form-item>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-form>
            </div>
            <div class="dictionary-flootr">
                <el-button @click="goBack()">{{ isDisabled ? "返回" : "取消" }}</el-button>
                <el-button type="primary" v-if="!isDisabled" :disabled="!isContentModified" @click="submit">确认提交</el-button>
            </div>
        </ct-box>
    </ct-section-wrap>
</template>

<script lang="ts">
import { Component, Mixins, Vue, Ref } from "vue-property-decorator";
import { ndictionary } from "@/config/url/ncdn/nscript";
import { nUserModule } from "@/store/modules/nuser";
import { easyClone } from "@/utils";
import { DictionaryItem } from "@/types/script/index";
import { deploymentStatusMap } from "@/config/map";
import { dictionaryName } from "@/config/npattern";
// 校验的表单类型
import { ElForm } from "element-ui/types/form";
import { isEqual } from "lodash-es";
import { CtiamCode } from "@/utils/ctFetch/errorConfig";

const dictionaryNameReg = new RegExp(dictionaryName);

@Component
export default class Dictionary extends Mixins(Vue) {
    private deploymentStatusMap = deploymentStatusMap;
    private loading = true;
    private form = {
        dicts: [],
    } as any;
    private oldDictsData = [];
    private dictionarySizeOption = [
        { label: "KB", value: "KB" },
        { label: "MB", value: "MB" },
    ];
    private rules = {
        // eslint-disable-next-line @typescript-eslint/camelcase
        dict_name: [
            { required: true, message: "请输入字典名称", trigger: "blur" },
            { validator: this.validateDictionary, trigger: "blur" },
        ],
        // eslint-disable-next-line @typescript-eslint/camelcase
        dict_size: [
            { required: true, message: "请输入字典大小", trigger: "blur" },
            { validator: this.validateSize, trigger: "blur" },
        ],
    };
    get getHeaderText() {
        const type = this.$route.query.operateType;
        return type === "edit" ? "编辑全局字典" : type === "add" ? "添加全局字典" : "查看全局字典";
    }
    get isDisabled() {
        return this.$route.query.operateType === "detail";
    }
    get type() {
        return this.$route.query.operateType;
    }
    get isContentModified() {
        return !isEqual(this.form?.dicts, this.oldDictsData);
    }
    validateDictionary(rule: any, value: string, callback: Function) {
        const dictionaryNameList = this.form.dicts
            .filter((item: any) => !!item.dict_name)
            .map((item: any) => item.dict_name);
        if (!dictionaryNameReg.test(value)) {
            return callback(new Error("字典名称格式错误！"));
        }
        const filterList = dictionaryNameList.filter((item: any) => item === value);
        if (filterList.length > 1) {
            return callback(new Error("字典名称不能重复！"));
        }
        callback();
    }
    validateSize(rule: any, value: number, callback: Function) {
        const index = rule.field.split(".")[1];
        const dictionarySizeType = this.form.dicts[index].dict_type;
        if (typeof value !== "number") {
            return callback(new Error("请输入数字"));
        }
        if (dictionarySizeType === "KB") {
            return value > 0 && value < 1024
                ? callback()
                : callback(new Error("字典大小类型为KB，输入范围1~1023"));
        }
        if (dictionarySizeType === "MB") {
            return value > 0 && value < 101
                ? callback()
                : callback(new Error("字典大小类型为MB，输入范围1~100"));
        }
        callback();
    }

    mounted() {
        this.initForm();
    }
    // 校验字典名称是否重复
    handleBlur() {
        const validList = this.form.dicts.map((item: DictionaryItem, index: number) => {
            return `dicts.${index}.dict_name`;
        });
        this.dictionaryFormRef.validateField(validList);
    }
    async initForm() {
        if (this.type !== "add") {
            const { workspaceId, userId } = this.$route.query;
            const data = await this.$ctFetch(ndictionary.getInfos, {
                method: "POST",
                body: {
                    data: {
                        workspaceId: String(workspaceId),
                        $user: String(userId),
                    },
                },
                headers: {
                    "Content-Type": "application/json",
                },
            });
            this.form = easyClone((data as any)?.result || {});
            this.form?.dicts &&
                this.form.dicts.forEach((item: DictionaryItem, index: number) => {
                    // eslint-disable-next-line @typescript-eslint/camelcase
                    this.form.dicts[index].dict_size = Number(item.dict_size);
                });
            this.oldDictsData = easyClone(this.form?.dicts || {});
        } else {
            // eslint-disable-next-line @typescript-eslint/camelcase
            this.form.dicts = [{ dict_name: "", dict_size: 1, dict_type: "KB" }];
        }
    }
    private handleAdd() {
        if (this.form.dicts?.length > 4) {
            return this.$message.warning("最多配置5个全局字典！");
        }
        // eslint-disable-next-line @typescript-eslint/camelcase
        this.form.dicts.push({ dict_name: "", dict_size: 1, dict_type: "KB" });
    }
    private goBack() {
        this.$router.go(-1);
    }
    @Ref("dictionaryForm") readonly dictionaryFormRef!: ElForm;
    private async submit() {
        try {
            await this.$ctUtil.formValidate2Promise(this.dictionaryFormRef);
            await this.dictionaryFormRef.validate();
            const operationType = this.getHeaderText.split("全局字典")[0];

            if (JSON.stringify(this.form.dicts) === JSON.stringify(this.oldDictsData)) {
                this.$router.push({ name: "nscript.dictionary" });
                return;
            }
            await this.$confirm(`此操作将对全局字典进行${operationType}操作，是否继续？`, `提示`, {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            });
            this.loading = true;
            const dictsList = easyClone(this.form?.dicts).map((item: DictionaryItem) => ({
                // eslint-disable-next-line @typescript-eslint/camelcase
                dict_name: item.dict_name,
                // eslint-disable-next-line @typescript-eslint/camelcase
                dict_size: item.dict_size + item.dict_type,
            }));

            const params = {
                $user: String(nUserModule.userInfo.userId),
                workspaceId: String(this.$route.query.workspaceId),
                id: this.form.id,
                dicts: dictsList,
            };
            if (this.type === "add") delete params.id;
            const reqUrl = this.type === "add" ? ndictionary.add : ndictionary.update;
            await this.$ctFetch(reqUrl, {
                method: "POST",
                body: { data: params },
                headers: {
                    "Content-Type": "application/json",
                },
            });
            this.$message.success("全局字典下发成功");
            this.$router.push({ name: "nscript.dictionary" });
        } catch (error) {
            const isCtiamError = CtiamCode.includes((error as any)?.data?.code);
            if (isCtiamError) {
                this.$errorHandler(error);
            } else {
                const errData = error as any;
                if (errData.data && errData.data.reason) {
                    return this.$message.error(errData.data.reason);
                }
            }
        } finally {
            this.loading = false;
        }
    }
    private handleDel(index: number) {
        if (this.form.dicts.length === 1) {
            return this.$message.warning("字典默认一条数据");
        }
        this.form.dicts.splice(index, 1);
    }
}
</script>

<style lang="scss" scoped>
.dictionary-table {
    margin-top: 40px;
    ::v-deep .el-form .el-form-item__content .el-input {
        width: 80%;
        .el-input__inner {
            padding-right: 24px;
        }
    }
    ::v-deep .el-select .el-input {
        width: 80px !important;
    }
}
::v-deep .el-select .el-input__validateIcon {
    display: none !important;
}
.operation-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .operation-add,
    .operation-del {
        width: 50%;
        height: 50%;
        font-size: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}
.dictionary-flootr {
    margin: 40px;
    display: flex;
    justify-content: center;
    align-content: center;
}
.box-header-title {
    color: #7C818C;
    h3 {
        color: #7C818C;
    }
}
</style>
