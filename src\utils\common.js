// import { SecurityAbilityModule } from "@/store/modules/securityAbility";
import { beforeLeaveComponent } from "@/components/ctLeaveInquiryNew";

/**
 * 处理离开模块前的询问
 */
export async function handleLeaveModule() {
    return new Promise((resolve, reject) => {
        const fn = beforeLeaveComponent();
        if (typeof fn.then !== "function") {
            resolve(true);
            return;
        }

        fn.then(() => {
            resolve(true);
        }).catch(() => {
            reject(false);
        });
    });
}
