import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import configModuleMixin from '@/mixins/configModuleMixin';

jest.mock('@/store/modules/configModules', () => ({
  ConfigModulesModule: {
    masterSwitch: true,
    isModuleEnabled: jest.fn(() => true),
    isModuleLocked: jest.fn(() => false),
    fetchModulesStatus: jest.fn(),
  },
}));

const mockRegistry = {
  testModule: {
    fields: { a: 1, b: 2 },
    onModuleChange: jest.fn((form, val) => ({ a: val })),
    toApi: jest.fn(form => ({ a: form.a })),
    displayCondition: jest.fn(() => true),
    init: jest.fn(val => ({ a: val?.a ?? 1, b: val?.b ?? 2 })),
  },
};

jest.mock('@/registry/configModuleRegistry', () => ({
  getRegistryConfig: jest.fn(module => (module ? mockRegistry[module] : mockRegistry)),
}));

describe('configModuleMixin', () => {
  let vm;
  let fetchModulesStatus;
  beforeEach(() => {
    // 重新获取 mock 函数的引用
    fetchModulesStatus = require('@/store/modules/configModules').ConfigModulesModule.fetchModulesStatus;
    vm = {
      ...configModuleMixin.methods,
      form: {},
      $refs: { testModule: { $refs: { testModule_form: {} } } },
      isMasterSwitchEnabled: true,
      isModuleEnabled: () => true,
      checkDisplayCondition: () => true,
    };
  });

  it('mergeConfigModuleFields 合并 fields', () => {
    vm.form = { c: 3 };
    vm.mergeConfigModuleFields();
    expect(vm.form).toEqual({ c: 3, a: 1, b: 2 });
  });

  it('initConfigModules 初始化表单', () => {
    vm.form = {};
    vm.initConfigModules({ a: 10 });
    expect(vm.form).toEqual({ a: 10, b: 2 });
  });

  it('onModuleChange 联动更新', () => {
    vm.form = { a: 1 };
    vm.onModuleChange('testModule', 5);
    expect(vm.form.a).toBe(5);
  });

  it('getApiData 返回 toApi 数据', () => {
    vm.form = { a: 7, b: 8 };
    const data = vm.getApiData();
    expect(data).toEqual({ a: 7 });
  });

  it('renderConfigModuleValidate 返回表单 refs', () => {
    const result = vm.renderConfigModuleValidate();
    expect(result).toEqual([{}]);
  });

  it('checkDisplayCondition 默认 true', () => {
    const res = configModuleMixin.methods.checkDisplayCondition.call({ form: {}, isMasterSwitchEnabled: true }, 'testModule');
    expect(res).toBe(true);
  });

  it('loadConfigModules 调用 fetchModulesStatus', async () => {
    await vm.loadConfigModules('domain.com');
    expect(fetchModulesStatus).toHaveBeenCalledWith('domain.com');
  });

  it('模块锁定状态下的行为', () => {
    const ConfigModulesModule = require('@/store/modules/configModules').ConfigModulesModule;
    ConfigModulesModule.isModuleLocked.mockReturnValueOnce(true);
    
    const res = configModuleMixin.methods.checkDisplayCondition.call(
      { form: {}, isMasterSwitchEnabled: true },
      'testModule'
    );
    expect(res).toBe(false);
  });

  it('主开关禁用时的行为', () => {
    const res = configModuleMixin.methods.checkDisplayCondition.call(
      { form: {}, isMasterSwitchEnabled: false },
      'testModule'
    );
    expect(res).toBe(false);
  });

  it('显示条件为false时的行为', () => {
    mockRegistry.testModule.displayCondition.mockReturnValueOnce(false);
    const res = configModuleMixin.methods.checkDisplayCondition.call(
      { form: {}, isMasterSwitchEnabled: true },
      'testModule'
    );
    expect(res).toBe(false);
  });

  it('表单验证失败的情况', () => {
    vm.$refs.testModule.$refs.testModule_form = {
      validate: jest.fn().mockResolvedValue(false)
    };
    
    return vm.renderConfigModuleValidate().then(result => {
      expect(result).toBe(false);
    });
  });

  it('模块状态变化的处理', () => {
    const ConfigModulesModule = require('@/store/modules/configModules').ConfigModulesModule;
    ConfigModulesModule.isModuleEnabled.mockReturnValueOnce(false);
    
    vm.form = { a: 1, b: 2 };
    vm.onModuleChange('testModule', 5);
    expect(vm.form.a).toBe(1); // 模块禁用时不应更新值
  });

  it('初始化时处理undefined值', () => {
    vm.form = {};
    vm.initConfigModules(undefined);
    expect(vm.form).toEqual({ a: 1, b: 2 }); // 使用默认值
  });

  it('处理无效的模块名称', () => {
    vm.form = { a: 1 };
    vm.onModuleChange('invalidModule', 5);
    expect(vm.form.a).toBe(1); // 无效模块不应改变表单值
  });

  it('处理toApi返回空对象的情况', () => {
    mockRegistry.testModule.toApi.mockReturnValueOnce({});
    vm.form = { a: 7, b: 8 };
    const data = vm.getApiData();
    expect(data).toEqual({});
  });

  it('处理onModuleChange返回undefined的情况', () => {
    mockRegistry.testModule.onModuleChange.mockReturnValueOnce(undefined);
    vm.form = { a: 1 };
    vm.onModuleChange('testModule', 5);
    expect(vm.form.a).toBe(1); // 返回undefined时不应更新表单
  });

  it('处理多个模块的字段合并', () => {
    mockRegistry.anotherModule = {
      fields: { c: 3, d: 4 },
      init: jest.fn(val => ({ c: val?.c ?? 3, d: val?.d ?? 4 }))
    };
    
    vm.form = { e: 5 };
    vm.mergeConfigModuleFields();
    expect(vm.form).toEqual({ e: 5, a: 1, b: 2, c: 3, d: 4 });
  });

  it('处理模块初始化时的部分字段覆盖', () => {
    vm.form = {};
    vm.initConfigModules({ a: 10, c: 30 });
    expect(vm.form).toEqual({ a: 10, b: 2 }); // 只覆盖存在的字段
  });

  it('处理表单验证抛出异常的情况', () => {
    vm.$refs.testModule.$refs.testModule_form = {
      validate: jest.fn().mockRejectedValue(new Error('验证失败'))
    };
    
    return vm.renderConfigModuleValidate().catch(error => {
      expect(error.message).toBe('验证失败');
    });
  });

  it('处理模块状态切换时的表单更新', async () => {
    const ConfigModulesModule = require('@/store/modules/configModules').ConfigModulesModule;
    
    // 模拟模块状态从禁用变为启用
    ConfigModulesModule.isModuleEnabled
      .mockReturnValueOnce(false)
      .mockReturnValueOnce(true);
    
    vm.form = { a: 1 };
    await vm.loadConfigModules('domain.com');
    vm.onModuleChange('testModule', 5);
    expect(vm.form.a).toBe(5); // 模块启用后应该可以更新
  });

  it('处理displayCondition动态变化的情况', () => {
    mockRegistry.testModule.displayCondition
      .mockReturnValueOnce(true)
      .mockReturnValueOnce(false);
    
    const res1 = configModuleMixin.methods.checkDisplayCondition.call(
      { form: {}, isMasterSwitchEnabled: true },
      'testModule'
    );
    const res2 = configModuleMixin.methods.checkDisplayCondition.call(
      { form: {}, isMasterSwitchEnabled: true },
      'testModule'
    );
    
    expect(res1).toBe(true);
    expect(res2).toBe(false);
  });
}); 