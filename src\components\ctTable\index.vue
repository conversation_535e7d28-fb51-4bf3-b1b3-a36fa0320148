<template>
    <div class="ct-table" :class="className">
        <el-table
            ref="commonTable"
            :data="data"
            v-bind="$attrs"
            v-on="$listeners"
            size="medium"
            stripe
            class="base-table"
            highlight-current-row
            header-row-class-name="table-head"
        >
            <template v-for="(column, columnIndex) in columns">
                <!-- index渲染-->
                <el-table-column
                    v-if="column.type === 'index' && column.render"
                    type="index"
                    :key="column.prop || 'index' + columnIndex"
                    :align="column.align || 'left'"
                    v-bind="column"
                >
                    <template slot-scope="scope">
                        <slot name="index" :data="scope"></slot>
                    </template>
                </el-table-column>
                <el-table-column
                    v-else-if="column.type === 'index' && !column.render"
                    type="index"
                    :key="column.prop || 'index' + columnIndex"
                    :align="column.align || 'left'"
                    v-bind="column"
                />
                <!-- 展开-->
                <el-table-column
                    v-else-if="column.type === 'expand'"
                    type="expand"
                    :key="column.prop || 'index' + columnIndex"
                    :align="column.align || 'left'"
                >
                    <template v-if="column.render" slot-scope="props">
                        <slot name="expand" :data="props"></slot>
                    </template>
                </el-table-column>
                <!-- 排序-->
                <el-table-column
                    v-else-if="column.type === 'sort'"
                    v-bind="column"
                    :key="column.prop || 'index' + columnIndex"
                    :align="column.align || 'left'"
                >
                    <template v-if="column.labelTip" slot="header">
                        <div class="flex-row-style" style="justify-content: center">
                            <span>{{ column.label }}</span>
                            <el-tooltip
                                class="item"
                                effect="dark"
                                :content="column.labelTip"
                                placement="top-start"
                            >
                                <i class="el-icon-warning-outline" />
                            </el-tooltip>
                        </div>
                    </template>
                    <template slot-scope="scope">
                        <div class="flex-row-style" style="justify-content: center">
                            <span style="margin-right: 20px">{{ scope.$index + 1 }}</span>
                            <div class="icon-drag">
                                <i
                                    v-if="isShowUp(scope.$index)"
                                    class="el-icon-arrow-up"
                                    @click="handleChangeSort(scope.row, -1)"
                                />
                                <i
                                    v-if="isShowDown(scope.$index)"
                                    class="el-icon-arrow-down"
                                    @click="handleChangeSort(scope.row, 1)"
                                />
                            </div>
                        </div>
                    </template>
                </el-table-column>
                <!-- selection渲染-->
                <el-table-column
                    v-else-if="column.type === 'selection'"
                    type="selection"
                    v-bind="column"
                    :key="column.prop || 'selection'"
                    :align="column.align || 'left'"
                >
                </el-table-column>
                <!-- 特殊渲染-->
                <el-table-column
                    v-else-if="column.render"
                    v-bind="column"
                    :key="column.prop || columnIndex"
                    :align="column.align || 'left'"
                >
                    <template v-if="column.labelTip" slot="header">
                        <div class="flex-row-style">
                            <span>{{ column.label }}</span>
                            <el-tooltip
                                class="item"
                                effect="dark"
                                :content="column.labelTip"
                                placement="top-start"
                            >
                                <i
                                    class="icon-column-label"
                                    :class="column.labelIcon ? column.labelIcon : 'el-icon-warning-outline'"
                                />
                            </el-tooltip>
                        </div>
                    </template>
                    <template slot-scope="{ row, $index }">
<!--                        :data="{ row, $index }"-->
                        <slot v-if="!column.renderFunction" :name="column.prop" :row="row" :index="$index"></slot>
                        <!-- 支持函数式组件渲染-->
                        <custom-render v-else :row="row" :render="column.renderFunction" :info="column" />
                    </template>
                </el-table-column>
                <!-- 操作按钮渲染-->
                <el-table-column
                    v-else-if="column.operationList"
                    v-bind="column"
                    :key="column.prop || 'operation' + columnIndex"
                    :align="column.align || 'left'"
                >
                    <template v-if="column.labelTip" slot="header">
                        <div class="flex-row-style" style="justify-content: center">
                            <span>{{ column.label }}</span>
                            <el-tooltip
                                class="item"
                                effect="dark"
                                :content="column.labelTip"
                                placement="top-start"
                            >
                                <i
                                    class="icon-column-label"
                                    :class="
                                            column.labelIcon ? column.labelIcon : 'el-icon-warning-outline'
                                        "
                                />
                            </el-tooltip>
                        </div>
                    </template>
                    <div slot-scope="{ row, $index }" class="flex-row-style center-row">
                        <div
                            v-for="(operation, operationIndex) in operationList(
                                    column.operationList,
                                    row
                                )"
                            :key="operationIndex"
                            class="button-box"
                        >
                            <!-- 普通按钮-->
                            <el-tooltip
                                v-if="operation.type === 'icon'"
                                effect="dark"
                                :content="renderPropByFunction(operation, row, 'label')"
                                placement="top"
                            >
                                <el-button
                                    :type="operation.iconType || 'primary'"
                                    circle
                                    class="icon-button"
                                    :icon="renderPropByFunction(operation, row, 'icon')"
                                    :style="{ background: operation.color }"
                                    :size="operation.size || 'mini'"
                                    :disabled="operationDisabled(operation, row)"
                                    :loading="get(row, operation.loadingProp, false)"
                                    @click="handleOperationClick($event, operation, row, $index)"
                                />
                            </el-tooltip>
                            <el-button
                                v-else
                                class="edit"
                                :class="{
                                        'no-line-style': operationIndex === 0,
                                        'action-style': !get(row, operation.loadingProp, false),
                                        'loading-style': get(row, operation.loadingProp, false),
                                    }"
                                :type="operation.type || 'text'"
                                :loading="get(row, operation.loadingProp, false)"
                                :disabled="operationDisabled(operation, row)"
                                @click="handleOperationClick($event, operation, row, $index)"
                            >
                                {{ renderPropByFunction(operation, row, "label") }}
                            </el-button>
                        </div>
                    </div>
                </el-table-column>
                <!-- 普通渲染-->
                <el-table-column
                    v-else
                    v-bind="column"
                    :key="column.prop || columnIndex"
                    :align="column.align || 'left'"
                >
                    <template v-if="column.labelTip" slot="header">
                        <div class="flex-row-style" style="justify-content: center">
                            <span>{{ column.label }}</span>
                            <el-tooltip
                                class="item"
                                effect="dark"
                                :content="column.labelTip"
                                placement="top-start"
                            >
                                <i
                                    class="icon-column-label"
                                    :class="
                                            column.labelIcon ? column.labelIcon : 'el-icon-warning-outline'
                                        "
                                />
                            </el-tooltip>
                        </div>
                    </template>
                    <template slot-scope="{ row }">
                        <template v-if="column.formatter">
                            <div v-if="column.isHtml" v-html="column.formatter(row, column)" />
                            <template v-else>{{ column.formatter(row, column) }}</template>
                        </template>
                        <template v-else>
                                <span
                                    :class="{ 'is-pointer': column.click }"
                                    @click="handleNormalClick(row, column)"
                                >
                                    {{ renderNormalValue(row, column) }}
                                </span>
                        </template>
                    </template>
                </el-table-column>
            </template>
            <!-- 空值重写-->
            <template slot="empty">
                <slot name="empty">
                    <ct-empty :description="emptyText">
                        <template slot="description">
                            <slot name="emptyDescription"></slot>
                        </template>
                    </ct-empty>
                </slot>
            </template>
        </el-table>
<!--        -->
        <el-pagination
            v-if="showPagination && total > 10"
            :current-page="pageParam.pageIndex"
            :page-size="pageParam.pageSize"
            :total="total"
            class="base-pagination"
            :layout="paginationLayout"
            :page-sizes="[2, 10, 15, 20, 50]"
            :pager-count="pagerCount"
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
        />
    </div>
</template>

<script>
import ctEmpty from "@/components/ctEmpty";
import { mapGetters } from "vuex";
import { get, has, set } from "lodash-es";

// 函数式组件
const customRender = {
    functional: true,
    props: {
        row: {
            type: Object,
            default: () => {
                return {};
            },
        },
        info: {
            type: Object,
            default: () => {
                return {};
            },
        },
        render: {
            type: Function,
            require: true,
        },
    },
    render: (c, ctx) => {
        return ctx.props.render({
            c: c,
            row: ctx.props.row,
            info: ctx.props.info,
        });
    },
};

export default {
    components: {
        customRender,
        ctEmpty,
    },
    props: {
        data: {
            type: Array,
            required: true,
            default: () => [],
        },
        // 表格列信息
        columns: {
            type: Array,
            required: false,
            default: () => [],
        },
        info: {
            type: Object,
            required: false,
            // eslint-disable-next-line @typescript-eslint/no-empty-function
            default: null,
        },
        // 是否展示分页信息
        showPagination: {
            type: Boolean,
            required: false,
            default: true,
        },
        total: {
            type: Number,
            required: false,
            default: 0,
        },
        // 分页数据
        pagination: {
            type: Object,
            required: false,
            default: () => {
                return {
                    pageIndex: 1,
                    pageSize: 10,
                };
            },
        },
        emptyText: {
            type: String,
            default: "暂无数据",
        },
        className: {
            type: String,
            default: "",
        },
        paginationLayout: {
            type: String,
            default: "total, sizes, prev, pager, next, jumper",
        },
        pagerCount: {
            type: Number,
            default: 7
        }
    },
    inject: {
        elForm: {
            default: ''
        },
        elFormItem: {
            default: ''
        }
    },
    data() {
        return {
            // pageParam: this.pagination,
        };
    },
    computed: {
        ...mapGetters({
            propsMap: "propsMap",
        }),
        /**
         * 是否展示操作按钮
         */
        isShowOperation() {
            return (item, row) => {
                if (!has(item, "isShow")) {
                    return true;
                }

                if (typeof item.isShow === "function") {
                    return item.isShow({ item, row });
                }

                return item.isShow;
            };
        },
        // 是否有存在分页
        hasPaging() {
            const pages = this.total / this.pageParam.pageSize;
            return pages > 1;
        },
        /**
         * 分页参数
         */
        pageParam: {
            get() {
                return {
                    pageIndex: get(this.pagination, "pageIndex", 1),
                    pageSize: get(this.pagination, "pageSize", 10),
                };
            },
            set(newVal) {
                set(this.pagination, "pageIndex", get(newVal, "pageIndex"));
                set(this.pagination, "pageSize", get(newVal, "pageSize"));
            },
        },
        /**
         * 操作列表
         */
        operationList() {
            return (ary, row) => {
                const res = [];
                ary.forEach(item => {
                    if (this.isShowOperation(item, row)) {
                        res.push(item);
                    }
                });
                return res;
            };
        },
    },
    methods: {
        isShowUp(index) {
            if (this.pageParam && this.total) {
                const { pageSize, pageIndex } = this.pageParam;
                return index + pageSize * (pageIndex - 1) !== 0;
            }
            return true;
        },
        isShowDown(index) {
            if (this.pageParam && this.total) {
                const { pageSize, pageIndex } = this.pageParam;
                return index + pageSize * (pageIndex - 1) !== this.total - 1;
            }
            return true;
        },
        handleCurrentChange(curPage) {
            const obj = { pageIndex: curPage };
            this.pageParam = Object.assign({}, this.pageParam, obj);
            this.emitPageParamChange();
        },
        handleSizeChange(pageSize) {
            const obj = { pageIndex: 1, pageSize: pageSize };
            this.pageParam = Object.assign({}, this.pageParam, obj);
            this.emitPageParamChange();
        },
        emitPageParamChange() {
            this.$emit("pagination-change");
        },
        /**
         * 处理操作点击
         */
        handleOperationClick(e, operation, row, $index) {
            e.stopPropagation();
            e.preventDefault();
            if (operation.fn && typeof operation.fn === "function") {
                operation.fn({
                    row: row,
                    index: $index,
                    item: this.info,
                });
            }
        },
        /**
         * 点击处理排序
         */
        handleChangeSort(row, type) {
            this.$emit("click-sort", {
                row: row,
                type: type,
            });
        },
        /**
         * 获取公共表格实例
         */
        getInstance() {
            return this.$refs.commonTable;
        },
        /**
         * 按钮是否禁用
         */
        operationDisabled(item, row) {
            if (item.disabled === false) {
                return false;
            }

            if ((this.elForm || {}).disabled) {
                return (this.elForm || {}).disabled;
            }

            if (!has(item, "disabled")) {
                return false;
            }

            if (typeof item.disabled === "function") {
                return item.disabled(row);
            }

            return item.disabled;
        },
        /**
         * 渲染按钮标题
         */
        renderPropByFunction(operation, row, prop) {
            return typeof operation[prop] === "function" ? operation[prop](row) : operation[prop];
        },
        /**
         * 渲染普通值
         */
        renderNormalValue(row, column) {
            const value = row[column.prop];
            if (column.isMap) {
                const mapProp = column.mapProp || column.prop;
                // 数组情况
                if (value instanceof Array) {
                    const res = value.reduce((pre, item) => {
                        const val = get(get(this.propsMap, mapProp), item);
                        val && pre.push(val);
                        return pre;
                    }, []);

                    return res.join("；");
                }

                return get(get(this.propsMap, mapProp), value, "-");
            }

            if (value || value === 0) {
                return value;
            }

            return "-";
        },
        /**
         * 处理普通点击
         */
        handleNormalClick(data, item) {
            if (item.click && typeof item.click === "function") {
                item.click({ data, item });
            }
        },
        doLayout() {
            this.$refs.commonTable.doLayout();
        },
        clearSelection() {
            this.$refs.commonTable.clearSelection();
        },
        get,
    },
};
</script>

<style scoped lang="scss">
@import "./index.scss";
</style>
