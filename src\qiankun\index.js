import { registerM<PERSON><PERSON><PERSON><PERSON>, runAfterFirstMounted, addGlobalUncaughtErrorHandler } from "qiankun";
import { MicroAppModule } from "@/store/modules/microApp";
import { isDevelop } from "@/config/config";
import router from "@/router/index.ts";
export const entry = `${window.location.protocol}//${window.location.hostname}/h5`;

const loader = loading => {
    MicroAppModule.SET_MICRO_APP_LOADING(loading);
};

const getActiveRule = hash => {
    return location => {
        if (hash instanceof Array) {
            return hash.some(item => location.hash.indexOf(item) > -1);
        }

        return location.hash.indexOf(hash) > -1;
    };
};
const times = new Date().getTime();
export const QianKunApps = [
    {
        name: "udf", // 应用的名字
        // entry: "https://www-test.ctcdn.cn/h5/udf/",
        entry: isDevelop ? "https://local.ctcdn.cn:8080/" : `${entry}/cdnudf/?_t=${times}`, // 默认会加载这个html 解析里面的js 动态的执行 （子应用必须支持跨域）fetch
        container: ".ctMicroAppViewBox", // 容器名
        activeRule: getActiveRule("#/udfMicroApp"),
        props: {
            //传递参数数据,
            routerInstance: router,
        },
        loader,
    },
];

registerMicroApps(QianKunApps, {
    beforeLoad: [
        app => {
            console.log("[LifeCycle] before load %c%s", "color: green;", app.name);
        },
    ],
    beforeMount: [
        app => {
            console.log("[LifeCycle] before mount %c%s", "color: green;", app.name);
        },
    ],
    afterUnmount: [
        app => {
            console.log("[LifeCycle] after unmount %c%s", "color: green;", app.name);
        },
    ],
});

// 报错拦截
addGlobalUncaughtErrorHandler(e => {
    console.log("addGlobalUncaughtErrorHandler", e);
});

runAfterFirstMounted(() => {
    console.log("初始加载完成...");
});
