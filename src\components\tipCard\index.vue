<template>
    <div class="tip-card" :class="type">
        <div class="flex-row-style">
            <i :class="[iconSet[type], type]" />
            <span class="title">{{ title }}</span>
        </div>
        <div class="text">{{ description }}</div>
    </div>
</template>

<script>
export default {
    props: {
        type: {
            type: String,
            default: "warning",
        },
        title: {
            type: String,
            default: "",
        },
        description: {
            type: String,
            default: "",
        },
    },
    data() {
        return {
            iconSet: {
                warning: "el-icon-warning-outline",
            },
        };
    },
};
</script>

<style scoped lang="scss">
.tip-card {
    width: 100%;
    padding: 16px 12px;
    border: 1px solid #ffe58f;
    border-radius: $border-radius;
    &.warning {
        background: #fffbe6;
    }
    .warning {
        color: #faad14;
        font-size: 20px;
        margin-right: 10px;
    }
    .title {
        font-size: 16px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: rgba(0, 0, 0, 0.85);
    }
    .text {
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.65);
        line-height: 22px;
    }
}
</style>
