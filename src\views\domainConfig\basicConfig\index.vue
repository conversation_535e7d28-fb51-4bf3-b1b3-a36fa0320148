<template>
    <form-wrapper
        :key="renderKey"
        :defaultData="defaultData"
        :fromAccelerationConfig="fromAccelerationConfig"
        :isFlatten="isFlatten"
        :showForStaticAndIcdn="showForStaticAndIcdn"
        @update-child="updateChild"
    />
</template>

<script>
import FormWrapper from "./FormWrapper.vue";
import { SecurityAbilityModule } from "@/store/modules/securityAbility";

export default {
    components: {
        FormWrapper,
    },
    props: {
        defaultData: Object,
        fromAccelerationConfig: Boolean,
        isFlatten: Boolean,
        showForStaticAndIcdn: Boolean,
    },
    data() {
        return {};
    },
    computed: {
        renderKey() {
            return SecurityAbilityModule.securityRenderKey;
        },
    },
    methods: {
        updateChild() {
            this.$emit("update-child", new Date().getTime());
        },
    },
    mounted() {
        window.custom.emit("handleCDNMonitor", [
            {
                event: "handleCDNConfigChange",
                type: "add",
            },
        ]);
    },
    beforeDestroy() {
        window.custom.emit("handleCDNMonitor", [
            {
                event: "handleCDNConfigChange",
                type: "remove",
            },
        ]);
    },
};
</script>

<style lang="scss" scoped></style>
