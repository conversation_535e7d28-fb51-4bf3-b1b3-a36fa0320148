<template>
    <div class="ct-edit-wrapper">
        <el-form
            label-width="140px"
            label-position="left"
            :model="form"
            :rules="rules"
            ref="filetypeTtlForm"
            :disabled="!isEdit || !isService || isLockCacheTime"
        >
            <div v-if="isStaticsAbilityOn">
              <el-form-item
                prop="filetype_ttl"
                ref="filetypeTtl"
                class="cache-table-style"
                ><span slot="label">
                    <!-- cdn 入口 缓存配置文案改为缓存时间配置 -->
                    {{
                        isPoweredByQiankun
                            ? $t("domain.detail.tab6")
                            : $t("domain.detail.tab6-1")
                    }}
                    <span>
                        <el-tooltip placement="top">
                            <div slot="content">
                                <span>{{ $t("domain.detail.tip9") }}<br /></span>
                                <span>{{ $t("domain.detail.tip10") }}</span>
                                <span>{{ $t("domain.detail.tip11") }}</span>
                                <span>{{ $t("domain.detail.tip12") }}</span>
                                <!-- 支持配置自定义资源的缓存过期时间规则，支持指定路径或者文件名后缀方式。<br />全局默认优先遵循源站缓存。默认开启去问号参数缓存，若需要带问号后参数缓存，请选择关闭该功能。权重支持自定义生效顺序，优先级数字大则优先生效。 -->
                            </div>
                            <ct-svg-icon
                                icon-class="question-circle"
                                class-name="ct-sort-drag-icon"
                            ></ct-svg-icon></el-tooltip></span
                ></span>
                <div class="ct-table-wrapper">
                    <el-table
                        class="origin-table"
                        :data="form.filetype_ttl"
                        ref="filetypeTtl"
                    >
                        <el-table-column prop="mode" :label="$t('domain.type')">
                            <template slot-scope="scope">{{
                                type_list[scope.row.mode]
                            }}</template>
                        </el-table-column>
                        <el-table-column prop="file_type" :label="$t('domain.content')"> </el-table-column>
                        <el-table-column
                            prop="ttl"
                            :label="$t('domain.detail.label33')"
                            :formatter="ttlFormatter"
                        >
                        </el-table-column>
                        <el-table-column prop="cache_type" :label="$t('domain.detail.label32')">
                            <template slot-scope="scope">{{
                                cache_type_list[scope.row.cache_type]
                            }}</template>
                        </el-table-column>
                        <el-table-column prop="cache_with_args" :label="$t('domain.detail.label34')">
                            <template slot-scope="scope">{{
                                cache_with_args_list[scope.row.cache_with_args]
                            }}</template>
                        </el-table-column>
                        <el-table-column prop="priority" :label="$t('domain.create.weight')"> </el-table-column>
                        <el-table-column :label="$t('domain.operate')" width="120">
                            <template slot-scope="scope">
                                <el-button
                                    type="text"
                                    @click="
                                        handleCacheOper(
                                            scope.row,
                                            'edit',
                                            'filetype_ttl',
                                            scope.$index
                                        )
                                    "
                                    >{{ $t("domain.modify") }}</el-button
                                >
                                <el-button
                                    type="text"
                                    @click="
                                        handleCacheOper(
                                            scope.row,
                                            'delete',
                                            'filetype_ttl',
                                            scope.$index
                                        )
                                    "
                                    >{{ $t("domain.delete") }}</el-button
                                >
                            </template>
                        </el-table-column>
                    </el-table>
                    <div class="flex-row-style button-box">
                        <el-button
                            class="btn"
                            type="text"
                            @click="handleCacheOper(null, 'create', 'filetype_ttl')"
                        >
                            + {{ addButtonText }}
                        </el-button>
                    </div>
                </div>
              </el-form-item>
            </div>

        </el-form>
        <filetype-ttl-dialog
          :dialogVisible="filetypeTtlDialogVisible"
          :filetypeTtlForm="filetypeTtlForm"
          :from="currentType"
          :filetype_ttl="form.filetype_ttl"
          @cancel="cancel"
          @submit="submitFiletypeTtl"
        />
    </div>
</template>

<script>
import ctSvgIcon from "@/components/ctSvgIcon";
import componentMixin from "@/views/domainConfig/componentMixin";
import validFieldMixin from "@/views/domainConfig/validFieldMixin";
import i18n from "@/i18n/index";
import { cloneDeep } from "lodash-es";
import filetypeTtlDialog from "@/views/domainConfig/accelerationConfig/components/filetypeTtlDialog.vue";
import { lowerFirst } from '@/utils';

const CacheTtlMap = {
    2: { name: i18n.t("simpleForm.alogicCacheMixin.CacheTtlMap.2"), factor: 60 * 60 * 24 },
    3: { name: i18n.t("simpleForm.alogicCacheMixin.CacheTtlMap.3"), factor: 60 * 60 },
    4: { name: i18n.t("simpleForm.alogicCacheMixin.CacheTtlMap.4"), factor: 60 },
    5: { name: i18n.t("simpleForm.alogicCacheMixin.CacheTtlMap.5"), factor: 1 },
};
// 缓存配置-类型：字典翻译
const CacheModeMap = {
    0: i18n.t("domain.detail.cacheModeMap[0]"),
    1: i18n.t("domain.detail.cacheModeMap[1]"),
    2: i18n.t("domain.detail.cacheModeMap[2]"),
    3: i18n.t("domain.detail.cacheModeMap[3]"),
    4: i18n.t("domain.detail.cacheModeMap[4]"),
    5: i18n.t("domain.detail.cacheModeMap[5]"),
};

export default {
    name: "filetypeTtl",
    components: {
        ctSvgIcon,
        filetypeTtlDialog,
    },
    mixins: [
        componentMixin,
        validFieldMixin
    ],
    props: {
        datas: Object,
        isStaticsAbilityOn: Boolean,
        isLockCacheTime: Boolean,
    },
    data() {
        return {
            addButtonText: i18n.t("domain.editPage.label10"),
            form: {
                filetype_ttl: [], // 缓存参数
            },
            filetypeTtlDialogVisible: false,
            currenIndex: "",
            filetypeTtlForm: {
                mode: 0,
                file_type: "",
                ttl: 80,
                cache_type: 3,
                cache_with_args: 0,
                priority: 10,
                timeType: "5",
                split: 0,
            },
            // 缓存参数
            currentType: "create",
            rules: {},
        };
    },
    computed: {},
    watch: {
        "datas.filetype_ttl": {
            deep: true,
            handler(val) {
                this.init(val);
            },
            immediate: true,
        },
    },
    methods: {
      init(v) {
          this.form.filetype_ttl = cloneDeep(v);
      },
      async handleCacheOper(row, currentType, tabName, i) {
        this.currentType = currentType;

        this.currenIndex = i;

        if (currentType === "create") {
            const defaultFormMap = {
                filetype_ttl: {
                    mode: 0,
                    file_type: "",
                    ttl: 80,
                    cache_type: 3,
                    cache_with_args: 0,
                    priority: 10,
                    timeType: "5",
                    split: 0,
                },
            };
            row = defaultFormMap[tabName];
        }
        if (currentType === "delete") {
            const msgMap = {
                filetype_ttl: this.$t("domain.editPage.tip20"),
            };

            // 二次确认弹窗
            await this.$confirm(msgMap[tabName], this.$t("domain.delete"), {
                type: "warning",
            });
            this.form[tabName].splice(i, 1);
            this.$emit("onChange", this.form.filetype_ttl);
        } else {
            this.filetypeTtlDialogVisible = true;
            const rowData = cloneDeep(row);
            this.$set(this, "filetypeTtlForm", rowData);
        }
      },
      // 从 cache 数组的 file_type 字段中检查出重复内容
      checkRepeatedName(cacheList) {
        if (cacheList.length === 0) return "";

        const nameLsit = cacheList
            .map(item => item.file_type)
            .join(",")
            .split(",");
        const nameMap = {};
        for (let idx = 0; idx < nameLsit.length; idx += 1) {
            if (nameMap[nameLsit[idx]]) {
                return nameLsit[idx];
            } else {
                nameMap[nameLsit[idx]] = true;
            }
        }

        return "";
      },
      // 缓存配置
      ttlFormatter({ ttl, timeType }) {
        return `${ttl} ${CacheTtlMap[timeType]?.name || ""}`;
      },
      // 缓存key-缓存参数，弹窗点击确定事件
      async submitFiletypeTtl() {
        // 进行重复性检查
        const { mode } = this.filetypeTtlForm;
        const typeName = CacheModeMap[mode] || "";
        const typeList = this.form.filetype_ttl
            .filter((i, index) => index !== this.currenIndex) // 先把指定的过滤掉
            .filter(item => item.mode === mode); // 再把不符合的类型过滤掉

        typeList.push(this.filetypeTtlForm); // 再把当前的追进去进行全比对

        const repeatedName = this.checkRepeatedName(typeList);
        if (repeatedName) {
            await Promise.reject(this.$t("simpleForm.alogicCacheMixin.ValidationErrorMessages.DuplicateValidation", { repeatedName, type: lowerFirst(typeName) }));
        }

        if (this.currentType === "create") {
            this.form.filetype_ttl.push({
                ...this.filetypeTtlForm,
            });
        } else {
            this.$set(this.form.filetype_ttl, this.currenIndex, this.filetypeTtlForm);
        }
        this.$emit("onChange", this.form.filetype_ttl);
        this.filetypeTtlDialogVisible = false;
      },
      cancel(dialogKey) {
        this[dialogKey] = false;
      },
    },
};
</script>

<style lang="scss" scoped>
@import "@/views/domainConfig/commonStyle.scss";
.ct-edit-wrapper {
    ::v-deep {
        .el-form--label-left .el-form-item__label {
            text-align: right;
        }
    }
}
</style>
