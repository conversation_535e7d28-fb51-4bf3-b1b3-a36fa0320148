# 本地开发环境配置

## 生成 ssl证书

- 推荐使用 mkcert [下载地址](https://github.com/FiloSottile/mkcert/releases)
- 本地安装 CA ，生成证书

```bash
$ mkcert -install

The local CA is already installed in the system trust store! 👍
Note: Firefox support is not available on your platform. ℹ️
The local CA is already installed in Java's trust store! 👍

# 生成 vip 环境证书
$ mkcert local.ctcdn.cn

Created a new certificate valid for the following names 📜
- "local.ctcdn.cn"

The certificate is at "./local.ctcdn.cn.pem" and the key at "./local.ctcdn.cn-key.pem" ✅

It will expire on 26 April 2023 🗓

# 生成国际站证书
$ mkcert local.esurfingcloud.com

# 生成 ctyun 环境证书
$ mkcert local.ctyun.cn
```

注：证书生成后将公钥和私钥都拷贝至项目根目录。该证书只供本地使用，不需要提交到 git 。

## 配置host

```bash
# vip 控制台
127.0.0.1 local.ctcdn.cn
# ctyun 控制台
127.0.0.1 local.ctyun.cn
# 国际站
127.0.0.1 local.esurfingcloud.com
```
