/*
 * @Description: 统计分析除用量查询外的 url
 * @Author: wang y<PERSON><PERSON>
 */

import { PROXY_PREFIX } from "./_PREFIX";

// 统计分析-用量查询
export const StatisticsUsageUrl = {
    // 用量查询-带宽流量
    queryList: PROXY_PREFIX + "/v1/statistics/QueryFlowBandwidth",
    queryOverviewList: PROXY_PREFIX + "/v1/statistics/overview/QueryFlowBandwidth",
    queryBwSourceList: PROXY_PREFIX + "/v1/statistics/bandwidth/QuerySourceStatistics",
    // 用量查询-回源统计
    queryMissList: PROXY_PREFIX + "/v1/statistics/QuerySourceStatistics",
    // 用量查询-请求数
    qpsList: PROXY_PREFIX + "/v1/statistics/QueryRequest",
    // 用量查询-请求数（回源）
    qpsMissList: PROXY_PREFIX + "/v1/statistics/QuerySourceRequest",
    // 用量查询-请求数（全站）
    qpsDcdnList: PROXY_PREFIX + "/v1/statistics/QueryQpsRequest",
    // 用量查询-命中率
    hitList: PROXY_PREFIX + "/v1/statistics/GetHit",
    // 用量查询-状态码-total
    codeTotal: PROXY_PREFIX + "/v1/statistics/GetHttpTotal",
    // 用量查询-状态码-httpCode
    codeList: PROXY_PREFIX + "/v1/statistics/GetHttpCode",
    // 用量查询-PV/UV
    pvUvList: PROXY_PREFIX + "/v1/statistics/GetPvUv",
    // 用量查询-地区运营商
    areaDataList: PROXY_PREFIX + "/v1/statistics/ListProvinceFBRData",
    // 用量查询-下载速度
    downloadSpeedList: PROXY_PREFIX + "/v1/statistics/QuerySpeed",
    // 虚拟专线
    dedicatedLineList: PROXY_PREFIX + "/v1/statistics/dedicatedLine",
    // 用量查询-回源状态码-total
    backToOriginCodeTotal: PROXY_PREFIX + "/v1/statistics/GetSourceHttpTotal",
    // 用量查询-回源状态码-httpCode
    backToOriginCodeList: PROXY_PREFIX + "/v1/statistics/GetSourceHttpCode",
};
// 统计分析-热门分析
export const StatisticsRankUrl = {
    // 热门URL/热门URL(回源)
    topUriList: PROXY_PREFIX + "/v1/statistics/ListTopUri",
    // 热门 referer
    topRefererList: PROXY_PREFIX + "/v1/statistics/ListTopReferer",
    // 域名排行
    topDomainList: PROXY_PREFIX + "/v1/statistics/ListTopDomain",
    // top 客户端 ip
    topIpList: PROXY_PREFIX + "/v1/statistics/ListTopIp",
};

// 统计分析-用户分析
export const StatisticsUserUrl = {
    // 访问用户区域分布数据
    areaDataList: PROXY_PREFIX + "/v1/statistics/ListProvinceFBRData",
    areaUserDataList: PROXY_PREFIX + "/v1/statistics/user/ListProvinceFBRData",
    // 独立 ip 数据
    uvDataList: PROXY_PREFIX + "/v1/statistics/GetPvUv",
    uvUserDataList: PROXY_PREFIX + "/v1/statistics/user/GetPvUv",
    // 访问运营商分布
    ispDataList: PROXY_PREFIX + "/v1/statistics/ListIspFBRData",
};

// 判断该账号是否为子账号
export const checkChildAccount = PROXY_PREFIX + "/v1/basic/checkChildAccount";
export const ctyunChildAccount = PROXY_PREFIX + "/ctyun/new/Current";
// 获取配置参数
export const getTabConfig = PROXY_PREFIX + "/v1/basic/config";
