import { DEFAULT_LANG, LANG_COOKIE_KEY, ZH_LANG } from "@/i18n";

const langMap: {
    [lang: string]: string;
} = {
    "en-us": "en",
    "zh-cn": "zh",
};

// 转换 lang
export function switchLang(lang: string) {
    return langMap[lang] || lang;
}

// 获取 lang
export function getLang() {
    // TODO: 先特殊判断下，后续控制台域名拆分后就不需要这一段逻辑了
    // if (window.__POWERED_BY_QIANKUN__) {
    //     return ZH_LANG;
    // }
    const lang = localStorage.getItem(LANG_COOKIE_KEY) || DEFAULT_LANG;
    return switchLang(lang);
}

// 设置 lang
export function setLang(lang: string) {
    localStorage.setItem(LANG_COOKIE_KEY, lang);
}

// 处理 osp 中 domain 参数后缀，如果是 zh 默认不追加（以保持和现有内容相同），其他语言则追加 .lang
export function domainLangAutoComple(domain: string, lang = ZH_LANG) {
    return lang === ZH_LANG ? domain : `${domain}.${lang}`;
}

export const lowerFirst = (str: string): string => {
    return str.charAt(0).toLowerCase() + str.slice(1);
}
