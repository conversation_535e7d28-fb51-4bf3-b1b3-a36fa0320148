export * from "./date";
export * from "./i18n";
export * from "./product";
export * from "./redirect";
export * from "./form";
export * from "./fetch";
export * from "./unit";

// 节流
export const throttleFn = (time = 3) => {
    return function(target: any, propertyKey: string, descriptor: PropertyDescriptor) {
        const oldFun = descriptor.value;
        let isLock = false;
        descriptor.value = function(...args: any[]) {
            if (isLock) {
                return;
            }
            isLock = true;
            setTimeout(() => {
                isLock = false;
            }, time * 1000);
            oldFun.apply(this, args);
        };
        return descriptor;
    };
};
