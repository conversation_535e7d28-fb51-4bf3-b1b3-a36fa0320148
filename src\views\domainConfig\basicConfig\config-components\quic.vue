<template>
    <el-form-item label="QUIC" prop="quic.switch" :rules="rules">
        <el-switch :value="switchValue" @input="updateValue" :active-value="1" :inactive-value="0">
        </el-switch>
        <TipWithSvgIcon padding-left="8px">{{
            $t("domain.需配置证书才能开启QUIC功能，默认支持版本：IETF-QUIC(H3-v1)")
        }}</TipWithSvgIcon>
    </el-form-item>
</template>

<script lang="ts">
import { Vue, Component, Model, Prop } from "vue-property-decorator";
import componentMixin from "@/views/domainConfig/mixins/componentMixin";
import TipWithSvgIcon from "../components/tipWithSvgIcon.vue";

@Component({
    mixins: [componentMixin],
    components: {
        TipWithSvgIcon,
    },
})
export default class QuicConfigComponent extends Vue {
    @Model("input", { type: Number, default: 0 }) switchValue!: number;
    @Prop({ default: false, type: Boolean }) isSm2!: boolean;
    updateValue(value: number) {
        this.$emit("input", value);
        this.$emit("change", value);
    }

    rules = {
        required: false,
        validator: this.valid_cert_name_gm_quic_rule,
        trigger: "change",
    };

    valid_cert_name_gm_quic_rule(rule: any, value: any, callback: any) {
        if (this.isSm2 && this.switchValue) {
            return callback(new Error(this.$t("certificate.国密证书和quic不能同时开启") as string));
        }
        callback();
    }
}
</script>

