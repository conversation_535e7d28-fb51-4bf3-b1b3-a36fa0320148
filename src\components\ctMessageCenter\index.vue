<template>
    <ct-drawer
        size="50%"
        :with-header="false"
        :width-footer="false"
        :visible.sync="insideShow"
        :wrapper-closable="true"
        @close="handleClose"
    >
        <div v-show="!showDetail">
            <div class="flex-row-style message-header">
                <el-tabs v-model="activeName" style="width: 100%;">
                    <el-tab-pane v-for="(item, key) in menuList" :key="key" :name="item.value">
                        <template slot="label">
                            <el-badge v-if="getUnReadMessage(item)" :value="getUnReadMessage(item)" :max="99">
                                {{ item.label }}
                            </el-badge>
                            <template v-else>
                                {{ item.label }}
                            </template>
                        </template>
                    </el-tab-pane>
                </el-tabs>
                <div class="flex-row-style message-header-right">
                    <el-radio-group v-model="currentStatus">
                        <el-radio-button v-for="(item, key) in statusList" :key="key" :label="item.id">
                            <el-badge :is-dot="isShowDot(item)">
                                {{ item.label }}
                            </el-badge>
                        </el-radio-button>
                    </el-radio-group>
                    <el-button type="primary" style="margin-left: 20px;" @click="handleReadAllMessage"
                        >全部已读</el-button
                    >
                </div>
            </div>
            <div class="body-main">
                <div v-for="(item, key) in currentMessageList" :key="key" class="message-row">
                    <div class="line">
                        <i class="circle" />
                        <i class="lin-style" />
                    </div>
                    <div class="message-content">
                        <div class="content-label">
                            {{ item.label }}
                        </div>
                        <div
                            v-for="(subItem, subIndex) in item.children"
                            :key="subIndex"
                            class="flex-row-style message-line"
                            :class="{ read: subItem.read, active: currentRow === subItem }"
                            @click="handleViewDetail(subItem)"
                        >
                            <div class="content-title">
                                {{ subItem.msgTitle }}
                            </div>
                            <div class="content-time">{{ subItem.time }}</div>
                        </div>
                    </div>
                </div>
                <ct-empty v-if="!currentMessageList.length" />
            </div>
        </div>
        <div v-loading="loading" v-show="showDetail" class="detail-box">
            <div class="flex-row-style back-header" @click="showDetail = false">
                <label class="flex-row-style" style="cursor: pointer;"
                    ><i class="el-icon-d-arrow-left pdr5"></i>返回消息</label
                >
            </div>
            <div class="detail-title">{{ currentDetail.msgTitle }}</div>
            <div class="sub-title">
                消息类型：{{ currentDetail.msgType }} |
                <template v-if="currentDetail.sender">来源：{{ currentDetail.sender }} |</template>
                {{ currentDetail.createDate }} {{ currentDetail.time }}
            </div>
            <div class="fs16 pdt36 detail-user-tip d-cursor">
                <span class="mgr5"></span>尊敬的用户，大家好！
            </div>
            <div class="detail-content" v-html="currentDetail.content" />
            <div class="fs16 pdt36 detail-user-tip d-cursor">
                天翼云 · 安全团队与您同行，祝您使用愉快
            </div>
        </div>
    </ct-drawer>
</template>

<script>
import ctDrawer from "@/components/ctDrawer";
import ctEmpty from "@/components/ctEmpty";
import { messageListAll, detailMessage, readAllMessage, readMessage } from "@/api";
import { mapGetters } from "vuex";
import { get, has, sortBy } from "lodash-es";

export default {
    components: {
        ctDrawer,
        ctEmpty,
    },
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        unReadNumber: {
            type: Number,
            default: 0,
        },
    },
    data() {
        return {
            activeName: "0",
            currentStatus: false,
            statusList: [
                { label: "全部", id: "all" },
                { label: "未读", id: false },
                { label: "已读", id: true },
            ],

            loading: true,
            dataSource: null,
            allData: [],
            showDetail: false,

            currentDetail: {},
            currentRow: null,

            insideShow: false,
        };
    },
    computed: {
        ...mapGetters({
            optionsMap: "optionsMap",
        }),
        /**
         * 菜单项
         */
        menuList() {
            return get(this.optionsMap, "systemMsgType", []) || [];
        },
        /**
         * 当前列表数据
         */
        currentMessageList() {
            const data = get(this.dataSource, this.activeName, []) || [];
            // 根据状态过滤
            const ary =
                this.currentStatus === "all" ? data : data.filter(item => item.read === this.currentStatus);
            // 按照时间纬度进行分类
            const map = {};
            let index = 0;
            const res = [];
            ary.forEach(item => {
                const date = item.createDate;
                if (!has(map, date)) {
                    map[date] = index;
                    index++;
                    res.push({
                        label: this.transDay(date),
                        children: [],
                    });
                }

                res[map[date]].children.unshift(item);
            });
            const sortAry = sortBy(res, item => {
                let time = item.label;
                if (time === "今天") {
                    time = this.$dayjs().format("YYYY/MM/DD");
                } else if (time === "昨天") {
                    time = this.$dayjs()
                        .subtract(1, "day")
                        .format("YYYY/MM/DD");
                }
                return -this.$dayjs(time).unix();
            });
            return sortAry;
        },
    },
    watch: {
        visible: {
            handler(val) {
                this.insideShow = val;
            },
            immediate: true,
        },
    },
    created() {
        const map = {
            "console_,msgType": "systemMsgType",
        };
        const data = {
            type: "queryPageDictionary",
            data: map,
        };
        this.$store.dispatch(data);
        this.getMessageData();
    },
    methods: {
        /**
         * 获取消息数据
         */
        async getMessageData() {
            const res = (await messageListAll()) || [];
            this.allData = res;

            const unReadNumber = res.reduce((total, item) => {
                if (!item.read) {
                    total = total + 1;
                }
                return total;
            }, 0);
            this.$emit("update:unReadNumber", unReadNumber);

            // 按照消息类型进行分类展示
            const map = {
                0: [], // 全部
            };
            res.forEach(item => {
                if (!map[item.msgType]) {
                    map[item.msgType] = [];
                }
                map[item.msgType].push(item);
                map["0"].push(item);
            });
            this.dataSource = map;
        },
        /**
         * 处理查看详情
         */
        async handleViewDetail(item) {
            this.currentRow = item;
            this.showDetail = true;
            const params = {
                msgId: item.msgId,
                type: item.msgType,
            };

            this.loading = true;
            try {
                const res = await detailMessage(params);
                this.currentDetail = res;
            } catch (e) {
                this.$errorHandler(e);
            }

            if (item.read) {
                this.loading = false;
                return;
            }

            try {
                const params = {
                    msgId: item.msgId,
                    type: item.msgType,
                };
                await readMessage(params);
                this.getMessageData();
            } catch (e) {
                this.$errorHandler(e);
            }
            this.loading = false;
        },
        /**
         * 处理已读所有消息
         */
        async handleReadAllMessage() {
            const flag = this.allData.every(item => item.read);
            if (flag) {
                await this.$alert("消息全部已读", "提示", {
                    confirmButtonText: "确定",
                    type: "warning",
                });
                return;
            }

            await this.$confirm("请确认是否已读所有消息？", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            });

            const params = {
                readInfo: [],
            };

            this.allData.reduce((pre, item) => {
                if (item.read) {
                    return pre;
                }

                pre.readInfo.push({
                    msgId: item.msgId,
                    type: item.msgType,
                });
                return pre;
            }, params);

            await readAllMessage(params);
            this.$message.success("操作成功");
            this.getMessageData();
        },
        /**
         * 转化时间文本
         */
        transDay(time) {
            const today = this.$dayjs()
                .startOf("day")
                .format("YYYY/MM/DD");
            const yesterday = this.$dayjs()
                .subtract(1, "day")
                .startOf("day")
                .format("YYYY/MM/DD");
            if (time === today) {
                return "今天";
            } else if (time === yesterday) {
                return "昨天";
            }
            return time;
        },
        /**
         * 是否展示原点
         */
        isShowDot(item) {
            if (item.id === true || item.id === "all") {
                return false;
            }

            const data = get(this.dataSource, this.activeName, []) || [];
            return data.some(item => !item.read);
        },
        /**
         * 获取未读信息
         */
        getUnReadMessage(item) {
            const data = get(this.dataSource, item.value, []) || [];
            return data.filter(item => !item.read).length;
        },
        handleClose() {
            this.activeName = "0";
            this.currentStatus = false;
            this.$emit("update:visible", false);
        },
    },
};
</script>

<style scoped lang="scss">
@import "./index.scss";
</style>
