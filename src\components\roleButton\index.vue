<template>
    <el-button v-bind="$attrs" @click.stop="handleClick" :class="{ 'role-button-disabled': styleDisabled }">
        <slot></slot>
    </el-button>
</template>

<script>
import { mapGetters } from "vuex";
import { get } from "lodash-es";

export default {
    name: "RoleButton",
    props: {
        beforeClick: {
            type: [Function, null],
            required: false,
            default: null,
        },
        message: {
            type: String,
            required: false,
            default: "",
        },
    },
    computed: {
        ...mapGetters({
            propsMap: "propsMap",
        }),
        /**
         * 样式上禁用
         */
        styleDisabled() {
            return false;
        },
    },
    methods: {
        /**
         * 处理点击
         */
        async handleClick() {
            if (this.beforeClick && typeof this.beforeClick === "function") {
                const res = this.beforeClick();
                if (!res) {
                    return;
                }

                if (typeof res.then === "function") {
                    await res;
                }
            }

            this.$emit("click");
        },
    },
};
</script>

<style scoped lang="scss">
.el-button--text {
    font-size: 14px;
    font-family: Microsoft YaHei;
    font-weight: 400;
    color: $theme-color;
    line-height: 22px;
    cursor: pointer;
    .el-icon-edit {
        margin-right: 2px;
    }
    &.role-button-disabled {
        color: $disabled-color;
        &:hover {
            color: $disabled-color;
        }
    }
}
</style>
