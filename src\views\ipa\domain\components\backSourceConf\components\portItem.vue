<template>
    <div class="port-item">
        <el-form-item
            v-for="(fieldItem, fieldIndex) in fields"
            :key="fieldIndex"
            :label="fieldItem.label"
            :prop="`${fatherProp}.${fieldItem.prop}`"
            :class="transFormPropToClassName(`${fatherProp}.${fieldItem.prop}`)"
            :rules="getCheckRules(fieldItem)"
            label-width="100px"
            style="width: 300px"
        >
            <el-input
                v-model="form[fieldItem.prop]"
                :placeholder="fieldItem.placeholder || '请输入内容'"
                :disabled="isDisabled(fieldItem)"
                @change="handleInputChange($event, fieldItem)"
            />
        </el-form-item>
    </div>
</template>

<script>
import emitter from "@/utils/emitter";
import { has, get } from "lodash-es";
import { transFormPropToClassName } from "@/utils/utils";

export default  {
    mixins: [emitter],
    props: {
        // 字段维护
        fields: {
            type: Array,
            default: () => []
        },
        // 用于校验element表单
        fatherProp: {
            type: String,
            default: ""
        },
        // 表单绑定
        form: {
            type: Object,
            default: () => {
                return {}
            }
        },
        // 端口校验映射表
        rulesMap: {
            type: Object,
            default: () => {
                return {}
            }
        }
    },
    methods: {
        // 是否禁用
        isDisabled(item) {
            if(!has(item, "disabled")) return false;

            return typeof item.disabled === "function" ? item.disabled(this.form) : item.disabled;
        },
        /**
         * 处理输入框变动
         * @param val
         * @param item
         */
        handleInputChange(val, item) {
            if (item.change && typeof item.change === "function") {
                // 将当前数据返回抛出
                item.change(val, this.form);
            }

            this.$emit("change", val, item);
            // 由于向上一层有一个端口信息校验（五项需要配置一项），这里需要产生联动效果。在变更完通知el-form-item组件进行更新状态
            this.dispatch("ElFormItem", "el.form.change", val);
        },
        /**
         * 获取校验规则
         * @param item
         */
        getCheckRules(item) {
            return get(this.rulesMap, item.prop, []) || [];
        },
        transFormPropToClassName,
    }
}
</script>

<style lang="scss" scoped>
.port-item {
    display: flex;
    flex-direction: row;
    .el-form-item {
        margin: 0 20px 20px 0;
    }
}
</style>
