<!-- 折叠卡片 -->
<template>
    <div class="ct-fold-card" :style="boxStyle">
        <div class="flex-row-style inside">
            <div class="flex-row-style">
                <svg-icon v-if="icon" :icon-class="icon" :class-name="iconInsideStyle" />
                <div>
                    <div>
                        <span class="title">{{ title }}</span>
                        <span v-if="unit" class="unit">{{ unit }}</span>
                    </div>
                    <span class="sub-title">{{ subTitle }}</span>
                </div>
            </div>
            <slot></slot>
        </div>
    </div>
</template>

<script>
import svgIcon from "@/components/SvgIcon";

export default {
    name: "index",
    components: {
        svgIcon,
    },
    props: {
        color: {
            type: String,
            default: "#DEE9FE",
        },
        icon: {
            type: String,
            default: "",
        },
        iconStyle: {
            type: String,
            default: "",
        },
        title: {
            type: [String, Number],
            default: "",
        },
        subTitle: {
            type: [String, Number],
            default: "",
        },
        unit: {
            type: [String, Number],
            default: "",
        },
    },
    computed: {
        boxStyle() {
            return {
                background: this.color,
            };
        },
        iconInsideStyle() {
            let str = "normal-icon";
            if (this.iconStyle) {
                str = str + ` ${this.iconStyle}`;
            }

            return str;
        },
    },
};
</script>

<style scoped lang="scss">
@import "./index.scss";
</style>
