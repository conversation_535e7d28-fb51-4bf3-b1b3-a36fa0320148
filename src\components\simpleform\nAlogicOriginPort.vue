<template>
    <div>
        <div class="wrap">
            <el-row>
                <lock-tip v-if="backorigin_protocol !== 'https'" :originPortDisabled="originPortDisabled" :addon="backorigin_protocol !== 'https'">
                    <el-col :span="backorigin_protocol === 'http' ? 6 : 4">
                        <div class="http-div">
                            <span class="http-span">HTTP</span>
                            <el-input size="medium" v-model="originPort.http_origin_port" :disabled="originPortDisabled"></el-input>
                        </div>
                    </el-col>
                </lock-tip>
                <lock-tip v-if="backorigin_protocol !== 'http'" :originPortDisabled="originPortDisabled" :addon="backorigin_protocol !== 'http'">
                    <el-col :span="6">
                        <div class="https-div" :class="{ 'https-div2': backorigin_protocol === 'follow_request' }">
                            <span class="https-span">HTTPS</span>
                            <el-input size="medium" v-model="originPort.https_origin_port" :disabled="originPortDisabled"></el-input>
                        </div>
                    </el-col>
                </lock-tip>
                <el-col :span="4">
                    <div class="https-div">
                        <span class="https-span">
                            {{ $t("domain.editPage.label32") }}
                            <el-tooltip
                                placement="top"
                                :content="$t('domain.editPage.originPortTip.tip2')">
                                <ct-svg-icon
                                    icon-class="question-circle"
                                    class-name="ct-sort-drag-icon"
                                ></ct-svg-icon>
                            </el-tooltip>
                        </span>
                        <el-switch
                            v-model="inner_follow_request_backport"
                            :active-value="1"
                            :inactive-value="0"
                            @change="onFollowRequestChange"
                        ></el-switch>
                    </div>
                </el-col>
            </el-row>
        </div>
    </div>
</template>

<script type="text/javascript">
import { portRegex } from '@/utils/validator.utils';
import ctSvgIcon from "@/components/ctSvgIcon";
import lockTip from "@/views/domainConfig/components/lockTIp.vue";
// import mixin from "../../editor.mixin";
// import optionMixin from "../option.mixin";

export default {
    name: "n-alogic-origin-port",
    // mixins: [mixin, optionMixin],
    components: { ctSvgIcon, lockTip },
    props: {
        http_origin_port: Number,
        https_origin_port: Number,
        backorigin_protocol: String,
        follow_request_backport: Number,
    },
    data() {
        return {
            originPort: {
                http_origin_port: "80",
                https_origin_port: "443",
            },
            inner_follow_request_backport: 0,
        };
    },
    computed: {
        originPortDisabled() {
            return this.inner_follow_request_backport === 1;
        },
    },
    watch: {
        backorigin_protocol() {
            this.originPort = { ...this.$options.data().originPort };
        },
        originPort: {
            handler(input) {
                this.$emit("portChange", input);

                this.model = Object.assign(
                    { http_origin_port: input.http_origin_port },
                    { https_origin_port: input.https_origin_port }
                );
            },
            deep: true,
        },
        http_origin_port: {
            handler(v) {
                if (v) {
                    this.originPort.http_origin_port = this.http_origin_port || 80;
                }
            },
        },
        https_origin_port: {
            handler(v) {
                if (v) {
                    this.originPort.https_origin_port = this.https_origin_port || 443;
                }
            },
        },
        follow_request_backport: {
            handler(v) {
                if ([0, 1].includes(v)) this.inner_follow_request_backport = v;
                else this.inner_follow_request_backport = 0;
            }
        },
    },
    methods: {
        onFollowRequestChange() {
            this.$emit("followRequestBackportChange", this.inner_follow_request_backport);
        },
        async validateProcedure() {
            const result = {
                valid: true,
                msg: "",
                dom: "origin_port",
            };
            const pattern = portRegex;
            if (
                !pattern.test(this.originPort.http_origin_port) ||
                Number(this.originPort.http_origin_port) === 443
            ) {
                result.valid = false;
                result.msg = this.$t("domain.detail.tip91");
            }
            if (!pattern.test(this.originPort.https_origin_port)) {
                result.valid = false;
                result.msg = this.$t("simpleForm.originPort.httpTip");
            }
            return Promise.resolve(result);
        },
    },
    mounted() {
        this.originPort.http_origin_port = this.http_origin_port || 80;
        this.originPort.https_origin_port = this.https_origin_port || 443;
        this.inner_follow_request_backport = this.follow_request_backport;
    },
};
</script>

<style lang="scss" scoped>
.wrap {
    .http-div {
        min-width: 280px;
    }
    .https-div {
        // margin-left: 20px;
        min-width: 280px;
    }
    .https-div2 {
        margin-left: 20px;
    }
    .http-span,
    .https-span {
        margin-right: 8px;
        font-size: 12px;
    }
    .el-input {
        min-width: 70px;
        max-width: 80px;
    }
}
</style>
