<template>
    <div class="nested-menu-container">
        <div class="left-menu">
            <div
                v-for="menu in menuList"
                :key="menu.prop"
                :class="[
                    'el-tabs__item menu-item',
                    currentMenu.main === menu.prop ? 'is-active is-active-custom' : '',
                    isBottomToTop ? 'is-bottom-to-top' : '',
                ]"
                @click="handleChangeMenu(menu)"
            >
                {{ menu.name }}
            </div>
        </div>
        <div class="sub-menu-wrapper">
            <el-tabs tab-position="top" v-model="currentMenu.sub" class="sub-menu-container">
                <el-tab-pane
                    v-for="subMenu in getChildMenu"
                    :key="subMenu.prop"
                    :label="subMenu.name"
                    :name="subMenu.prop"
                >
                </el-tab-pane>
            </el-tabs>
            <div
                v-loading="searchbarLoading"
                :element-loading-text="$t('common.loading')"
                style="min-height: 150px"
            >
                <slot></slot>
            </div>
        </div>
    </div>
</template>

<script lang="tsx">
import { Component, Model, Prop, Vue } from "vue-property-decorator";
import type { CurrentMenu, MenuListItem } from "./statistics.d";

@Component({
    name: "NestedMenu",
})
export default class NestedMenu extends Vue {
    @Prop({ default: [] }) public menuList!: MenuListItem[];
    @Prop({ default: false }) public searchbarLoading!: boolean;
    public isBottomToTop = false;

    @Model('change', {
        default: () => ({ currentMenu: "", currentSubMenu: "" }),
    }) currentMenu!: CurrentMenu<string>;

    mounted() {
        if (this.menuList.length <= 0) return;
        // 初始化当前选中的菜单
        this.currentMenu.main = this.menuList[0].prop;
        this.currentMenu.sub = this.menuList[0].children?.[0].prop;
    }

    public handleChangeMenu(menu: MenuListItem) {
        this.checkIsBottomToTop(menu);
        this.currentMenu.main = menu.prop;
        if (Array.isArray(this.getChildMenu) && !this.getChildMenu.find(subMenu => subMenu.prop === this.currentMenu.sub)) {
            this.currentMenu.sub = this.getChildMenu[0]?.prop;
        }
    }

    public checkIsBottomToTop(menu: MenuListItem) {
        const currentMenuIdx = this.menuList.findIndex(item => item.prop === this.currentMenu.main);
        const menuIdx = this.menuList.findIndex(item => item.prop === menu.prop);
        this.isBottomToTop = currentMenuIdx > menuIdx;
    }

    get getChildMenu() {
        return this.menuList.find(menu => menu.prop === this.currentMenu.main)?.children || [];
    }
};
</script>

<style lang="scss" scoped>
.nested-menu-container {
    display: flex;
    gap: 12px;

    .sub-menu-wrapper {
        flex: 1;
        margin-top: 0;
        margin-left: 12px;

        overflow: auto;
    }

    .left-menu {
        min-width: 200px;
        flex-grow: 0;
        display: flex;
        flex-direction: column;

        .menu-item {
            height: unset;
            padding: 8px 0;
            text-align: center;
            box-sizing: border-box;
            transition: background-color 0.2s ease-in-out;

            &:hover {
                background-color: $color-bg-3;
            }
        }

        .is-active-custom {
            position: relative;
            background-color: $color-bg-3;

            &::after {
                content: "";
                position: absolute;
                float: right;
                width: 2px;
                height: 100%;
                right: 0;
                top: 0;
                background-color: $color-master;
                animation: height-fade-in 0.3s ease;
            }
        }

        .is-bottom-to-top {
            &::after {
                top: unset;
                bottom: 0;
            }
        }
    }
}

@keyframes height-fade-in {
    0% {
        height: 0;
    }

    100% {
        height: 100%;
    }
}
</style>
