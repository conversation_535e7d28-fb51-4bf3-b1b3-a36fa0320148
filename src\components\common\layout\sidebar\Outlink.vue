<template>
  <div v-if="oldConsoleSwitch" class="outlink-wrapper" slot="outlink">
    <div class="outlink" @click="goTo">
      {{ $t("common.oldVersion") }}
      <ctSvgIcon icon-class="outlink" style="width: 20px; height: 20px;"></ctSvgIcon>
    </div>
  </div>
</template>

<script lang="ts">
import { nBasicConfig } from "@/config/url";
import { Component, Vue } from "vue-property-decorator";
import ctSvgIcon from "../../../SvgIcon/index.vue";

@Component({
  name: "Outlink",
  components: { ctSvgIcon },
})
export default class Outlink extends Vue {
  oldConsoleSwitch = false;
  oldConsoleUrl = "";
  initialized = false;

  get alertRoute() {
    return [
      '/ndomain/create',
      '/ndomain/edit',
      '/ndomain/batchEdit',
      '/certificate/batchBind',
      '/nscript/dictionary/add',
      '/nscript/dictionary/edit',
      '/nscript/task/add',
      '/nscript/task/edit',
      '/nscript/business/add',
      '/nscript/business/edit'
    ]
  }

  beforeCreate() {
    this.$ctBus.$on("outlink:update", () => {
      if (this.initialized) return;
      this.$nextTick(() => {
        this.$ctFetch<{
          oldConsoleSwitch: boolean,
          oldConsoleUrl: string
        }>(nBasicConfig, { cache: true }).then(rst => {
          const { oldConsoleSwitch, oldConsoleUrl } = rst;
          this.oldConsoleSwitch = oldConsoleSwitch;
          this.oldConsoleUrl = oldConsoleUrl;
          this.initialized = true;
          this.removeEvent();
        });
      })
    })
  }

  beforeDestroy() {
    this.removeEvent();
  }
  replaceLink() {
    window.location.replace(`${this.oldConsoleUrl}?workspaceId=${this.$route?.query?.workspaceId || ""}`);
  }
  goTo() {
    const { path } = this.$route;
    if (this.alertRoute.includes(path)) {
      this.$confirm(`${this.$t("common.oldVersionTip")}`, {
        title: `${this.$t("common.messageBox.title")}`,
        confirmButtonText: `${this.$t("common.dialog.submit")}`,
        cancelButtonText: `${this.$t("common.dialog.cancel")}`,
      }).then(() => {
        this.replaceLink();
      }).catch(err => err)
    } else {
      this.replaceLink();
    }
  }
  removeEvent() {
    this.$ctBus.$off("outlink:update");
  }
};
</script>

<style lang="scss" scoped>
.outlink-wrapper {
  cursor: pointer;
  margin-top: 12px;

  .outlink {
    display: flex;
    flex-wrap: nowrap;
    justify-content: space-between;
    align-items: center;
    color: $color-master;
    font-weight: bold;
    font-size: 12px;
    background-color: $color-master-bg-light-2;
    padding: 8px;
  }
}
</style>
