/*
 * @Description: 重定向页面进入时的地址，主要为了解决 osp 服务地址中无法正确跳转的问题
 * @Author: wang yuegong
 */

export const redirectEntryUrl = () => {
    /**
     * 异常状态下：
     * href : https://local.ctcdn.cn/h5/cdn/?workspaceId=10010290#/
     * search : ?workspaceId=10010290
     * hash : #/
     *
     * 正常状态下：
     * href : https://local.ctcdn.cn/h5/cdn/#/index?workspaceId=10010290
     * search : ""
     * hash : #/index?workspaceId=10010290
     */
    const { search, hash, href } = window.location;

    // 1、无 search ，不作任何处理
    if (!search) {
        return;
    }

    // 无任何参数的 href
    const pureHref = href.replace(search, "").replace(hash, "");

    // 2、有 search
    let newHash = "";
    if (!hash) {
        // 2.1 无 hash ，给一个默认 hash
        newHash = `#/index${search}`;
    } else {
        // 2.2 有 hash
        if (hash.includes("?")) {
            // 2.2.1 hash 中有 query 参数，则直接把原 search 参数去掉 ? 追加到 hash 最后
            newHash = `${hash}&${search.substr(1)}`;
        } else {
            // 2.2.2 hash 中无 query 参数
            newHash = `${hash}${search}`;
        }
    }

    window.location.href = `${pureHref}${newHash}`;
};
