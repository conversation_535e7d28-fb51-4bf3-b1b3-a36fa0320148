<template>
    <ct-section-wrap :headerText="headerTitle">
        <ct-box class="table-scroll-wrap">
            <div class="search-bar-wrapper">
                <div>
                    <div class="search-bar">
                        <el-tooltip placement="top" :content="$t('certificate.placeholder[0]')" :disabled="!!deploymentStatus">
                            <el-select class="filter-select" v-model="deploymentStatus" clearable :placeholder="$t('certificate.placeholder[0]')">
                                <el-option
                                    v-for="item in statusOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                />
                            </el-select>
                        </el-tooltip>
                        <el-tooltip placement="top" :content="$t('certificate.placeholder[1]')" :disabled="!!keyword">
                            <el-input size="medium" class="filter-input" v-model="keyword" :placeholder="$t('certificate.placeholder[1]')">
                                <el-select
                                    slot="prepend"
                                    v-model="keywordType"
                                    :class="[isEn && 'increse-width']"
                                >
                                    <el-option
                                        v-for="item in searchOptions"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    />
                                </el-select>
                            </el-input>
                        </el-tooltip>
                        <el-button type="primary" primary @click="fetchList">
                            {{ $t("common.search.start") }}
                        </el-button>
                        <el-button @click="reset">{{ $t("common.search.reset") }} </el-button>
                    </div>
                </div>
            </div>
            <el-table :empty-text="$t('common.table.empty')" :data="dataList" v-loading="loading">
                <el-table-column :label="$t('common.table.index')" width="60">
                    <template #default="scope">
                        {{ (page - 1) * perPage + +scope.$index + 1 }}
                    </template>
                </el-table-column>
                <el-table-column
                    min-width="120"
                    :label="$t('certificate.table.label[0]')"
                    prop="name"
                    align="left"
                    show-overflow-tooltip
                />
                <el-table-column
                    min-width="120"
                    :label="$t('certificate.证书类型')"
                    prop="algorithm_type"
                    align="left"
                    show-overflow-tooltip
                >
                    <template #default="{ row }">
                        {{ row.algorithm_type === 1 ? $t('certificate.国密证书') : $t('certificate.国际标准证书') }}
                    </template>
                </el-table-column>
                <el-table-column
                    :label="$t('certificate.table.label[1]')"
                    prop="cn"
                    align="left"
                    min-width="120"
                    show-overflow-tooltip
                />
                <el-table-column
                    min-width="120"
                    :label="$t('certificate.table.label[2]')"
                    prop="issuer"
                    align="left"
                    show-overflow-tooltip
                />
                <el-table-column :label="$t('certificate.table.label[3]')" width="180">
                    <template #default="{ row }">
                        {{ (row.issue * 1000) | timeFormat }}
                    </template>
                </el-table-column>
                <el-table-column :label="$t('certificate.table.label[4]')" width="180">
                    <template #default="{ row }">
                        {{ row.expires | timeFormat }}
                    </template>
                </el-table-column>
                <el-table-column :label="$t('certificate.table.label[5]')" width="180">
                    <template #default="{ row }">
                        {{ (row.created * 1000) | timeFormat }}
                    </template>
                </el-table-column>
                <el-table-column :label="$t('certificate.table.label[6]')" width="180">
                    <template #default="{ row }">
                        {{ (row.updated * 1000) | timeFormat }}
                    </template>
                </el-table-column>
                <el-table-column :label="$t('certificate.table.label[7]')" width="180">
                    <template #default="{ row }">
                        <cute-state :color="statusColorMap[row.state]">
                            {{ $t(`${statusMap[row.state]}`)  }}
                        </cute-state>
                    </template>
                </el-table-column>
                <el-table-column :label="$t('certificate.table.label[8]')" width="180">
                    <template #default="{ row }">
                        <el-button type="text" @click="viewVersions(row)">{{ $t('certificate.title5') }}</el-button>
                    </template>
                </el-table-column>
            </el-table>

            <div class="pager">
                <el-pagination
                    :small="isXs"
                    :layout="isXs ? 'prev, pager, next' : 'total, sizes, prev, pager, next,jumper'"
                    :total="totalRecord"
                    :current-page.sync="page"
                    :page-size="perPage"
                    :page-sizes="[10, 30, 50, 100]"
                    :hide-on-single-page="false"
                    @size-change="sizeChange"
                ></el-pagination>
            </div>
        </ct-box>
    </ct-section-wrap>
</template>

<script lang="ts">
import { Component, Vue, Watch } from "vue-property-decorator";
import { timeFormat } from "@/filters/index";
import { ScreenModule } from "@/store/modules/screen";
import { nUserModule } from "@/store/modules/nuser";
import { CertificateUrl } from "@/config/url/certificate";
import { SecretsItem } from "@/types/certificate";
import { getLang } from "@/utils";
import variables from "@cutedesign/ui/style/themes/default/index.scss";
import { CtiamCode } from "@/utils/ctFetch/errorConfig";
import { checkCtiamButtonAuth } from "@/store/modules/menu";
import { GetCtiamButtonAction } from "@/store/config";


@Component({
    name: "certificateHistory",
    filters: {
        timeFormat(data: string): string {
            return timeFormat(data) || data;
        },
    },
})
export default class Certificate extends Vue {
    private searchOptions = [
        {
            value: "NAME",
            label: this.$t("certificate.keyword.opt[0]"),
        },
        {
            value: "CN",
            label: this.$t("certificate.keyword.opt[1]"),
        },
        {
            value: "ISSUER",
            label: this.$t("certificate.keyword.opt[2]"),
        },
    ];
    private statusOptions = [
        {
            value: 1,
            label: this.$t("certificate.status.opt[0]"),
        },
    ];
    private statusMap = {
        0: this.$t("certificate.status.opt[0]"),
        1: this.$t("certificate.status.opt[1]"),
        2: this.$t("certificate.status.opt[2]"),
    };
    private statusColorMap = {
        0: variables.colorSuccess,
        1: variables.colorMaster,
        2: variables.colorInfo,
    };
    keyword = "";
    keywordType = "NAME";
    deploymentStatus = "";
    loading = false;
    dataList: SecretsItem[] = []; // 最终展示的分页数据
    page = 1;
    perPage = 10;
    totalRecord = 0;

    mounted() {
        this.fetchList();
    }

    @Watch("page")
    onPageChange() {
        this.fetchList();
    }
    get isEn() {
        return getLang() === "en";
    }
    get isXs() {
        return this.screenWidth < 600;
    }
    get screenWidth() {
        return ScreenModule.width;
    }
    get email() {
        return nUserModule.userInfo.email;
    }
    get userName() {
        return nUserModule.userInfo.name;
    }
    get lang() {
        return nUserModule.lang;
    }
    get headerTitle() {
        return this.$t("certificate.title4")
    }

    private async viewVersions(row: SecretsItem) {
        await checkCtiamButtonAuth(GetCtiamButtonAction("certHistory"));
        this.$router.push({
            name: "certificate.versions",
            query: {
                id: row.id,
                name: row.name
            }
        });
    }

    async fetchList({
        keyword = this.keyword,
        keywordType = this.keyword && this.keywordType,
        state = this.deploymentStatus || 0,
        pageIndex = this.page,
        pageSize = this.perPage,
    } = {}) {
        this.loading = true;
        try {
            const rst = await this.$ctFetch<{ secrets: SecretsItem[]; paging: { total_record: number } }>(
                CertificateUrl.certDeployList,
                {
                    encodeParams: true,
                    data: {
                        keyword: keyword.replace(/\?/g, "？"), // 兼容输入问号导致查询报错
                        keywordType,
                        state,
                        pageIndex,
                        pageSize,
                    },
                }
            );
            this.dataList = rst.secrets;
            this.totalRecord = rst.paging.total_record;
        } catch (err) {
            const isCtiamError = CtiamCode.includes((err as any)?.data?.code);
            if (isCtiamError) {
                // 权限报错时清空数据
                this.dataList = [];
                this.totalRecord = 0;
            }
            this.$errorHandler(err);
        } finally {
            this.loading = false;
        }
    }
    reset() {
        this.keyword = "";
        this.deploymentStatus = "";

        // 重置查询条件后执行查询
        this.fetchList();
    }
    sizeChange(val: number) {
        // 恢复到第一页
        this.page = 1;
        this.perPage = val;
        this.fetchList();
    }
}
</script>

<style lang="scss" scoped>
.pager {
    text-align: right;
    margin-top: 8px;
}

.el-form-item {
    margin-bottom: 20px;
}

.alert {
    color: $g-color-yellow;
}

.search-bar-wrapper {
    @media (max-width: 1330px) {
        display: grid;
        grid-template: 1fr / 1fr;
        gap: 12px;
    }
}

.search-bar {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    & > *:last-child {
        margin-right: 0;
    }
    .filter-select {
        width: 150px;
    }
    .filter-input {
        width: 400px;
        ::v-deep .el-input-group__prepend {
            width: 130px;
        }
    }
}

.search-label {
    margin-right: 16px;
    font-size: 12px;
    color: #333 !important;
}
.ml-12 {
    margin-left: 12px;
}

.increse-width {
    min-width: 140px !important;
}
.danger {
    color: $color-danger;
}
.info {
    color: $color-info;
}
.success {
    color: $color-success;
}
</style>
