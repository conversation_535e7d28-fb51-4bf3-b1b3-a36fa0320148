<!--
 * @Description: 独立 ip
 * @Author: wang yuegong
-->
<template>
    <ct-box :tags="$t('statistics.user.tip11')" class="user-section">
        <template #tags-slot>
            <el-tooltip effect="dark" :content="$t('statistics.common.searchDownloadContent')" placement="right">
                <i class="el-icon-download download-icon" style="margin-left: 0;" @click="download"></i>
            </el-tooltip>
        </template>

        <div class="total">
            <div class="ip-data">
                {{ $t('statistics.user.tip12') }}{{ `(${greater24Hours ? $t('statistics.user.tip13') :
                    $t('statistics.user.tip14')})` }}：
                <span>{{ (fetchData.maxUv || 0).toLocaleString() }}{{ $t('statistics.user.tip17', { prefix: " " }) }}</span>
            </div>
            <div class="ip-data" v-if="!greater24Hours">
                {{ $t('statistics.user.tip15') }}
                <span>{{ (fetchData.totalUv || 0).toLocaleString() }}{{ $t('statistics.user.tip17', { prefix: " " }) }}</span>
            </div>
        </div>

        <v-chart class="chart" v-loading="loading" :element-loading-text="$t('common.chart.loading')" autoresize
            theme="cdn" :options="options" />
    </ct-box>
</template>

<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import { StatisticsUserUrl } from "@/config/url/statistics";
import { SearchParams } from "@/types/statistics/user";
import { PvUvFetchData } from "@/types/statistics/usage";
import { timeFormat } from "@/filters/index";
import { THEME_AREA_STYLE } from "@/config/echart/cdn-theme";
import { nUserModule } from "@/store/modules/nuser";

@Component({
    name: "UserIp",
})
export default class UserIp extends Vue {
    loading = false;
    // 请求的列表数据
    fetchData: PvUvFetchData = {
        hours: [],
        daily: [],
        maxPv: 0,
        totalPv: 0,
        maxUv: 0,
        totalUv: 0,
    };
    greater24Hours = false; // 请求参数是否超过24h

    // 获取缩进
    get uvUnit() {
        // 通过首项来判断是否缩进万位
        const uvList = this.greater24Hours ? this.fetchData.daily : this.fetchData.hours;

        if (uvList[0] && uvList[0].uvCount >= 10000) {
            return {
                unit: "W",
                scale: 10000,
            };
        } else {
            return {
                unit: "",
                scale: 1,
            };
        }
    }
    private async localFetchGenerator<T>(url: string, payload: { method: string; body: any, headers?: any }) {
            const rst = await this.$ctFetch<T>(url, payload)
            return rst;
    }
    // 1、数据请求
    async getData(params: SearchParams) {
        this.loading = true;
        this.greater24Hours = (params.endTime - params.startTime) / 3600 > 24;
        const rst = await this.localFetchGenerator(StatisticsUserUrl.uvUserDataList, {
            method: "POST",
            body: {
                data: {
                    ...params,
                    // 全部域名，新增一个ignore字段
                    ignore: params.domainList.length > 1 ? "domain" : "",
                },
            },
            headers: {
                "Content-Type": "application/json",
            },
        });

        Object.assign(
            this.fetchData,
            rst || {
                hours: [],
                daily: [],
                maxPv: 0,
                totalPv: 0,
                maxUv: 0,
                totalUv: 0,
            }
        );
    }

    // 2、数据处理
    get options() {
        const { greater24Hours } = this;
        const { daily, hours } = this.fetchData;
        const { unit, scale } = this.uvUnit;

        const xAxisData: string[] = [];
        const seriesData: number[] = [];

        // 根据区间选择数据源
        (greater24Hours ? daily : hours)
            .sort((a, b) => a.timestamp - b.timestamp)
            .forEach(item => {
                xAxisData.push(timeFormat(item.timestamp * 1000));
                seriesData.push(item.uvCount / scale || 0);
            });

        const times = nUserModule.lang === "en" ? "" : "次";
        const options = {
            tooltip: {
                trigger: "axis",
                formatter: (a: { [x: string]: any }[]) => {
                    const date = new Date(a[0].name.replace(/-/g, "/"));
                    let timePeriod = "";
                    const count = parseInt(a[0].value * scale + "").toLocaleString();
                    if (this.greater24Hours) {
                        // date.setHours(23);
                        // date.setMinutes(59);
                        timePeriod = a[0].name.slice(0, 10);
                    } else {
                        date.setMinutes(59);
                        let hour: number | string = date.getHours();
                        hour = hour < 10 ? `0${hour}` : hour;
                        timePeriod = `${a[0].name.slice(10, 16)}-${hour}:${date.getMinutes()}`;
                    }
                    return `${timePeriod}<br>${a[0].marker}${count}${times}`;
                },
            },
            xAxis: {
                type: "category", // 类目轴，从 data 中获取数据
                axisLabel: {
                    formatter(val: string): string | undefined {
                        // 小于24小时，横坐标显示小时数目
                        return greater24Hours ? val.slice(5, 10) : val.slice(10, 16);
                    },
                },
                data: xAxisData,
            },
            yAxis: {
                name: `${this.$t("statistics.dcdn.requestWhole.vchartOptions.yAxisName")}`,
                type: "value",
                minInterval: 1,
                axisLabel: {
                    formatter: `{value} ${unit}`,
                },
            },
            series: [
                {
                    type: "line",
                    color: "#0c59db",
                    data: seriesData,
                    areaStyle: THEME_AREA_STYLE["#358AE2"],
                },
            ],
            grid: {
                // 容器各个方向的留白
                left: scale === 1 ? "10%" : "18%",
                right: "8%",
                top: "12%",
                bottom: "13%",
            },
        };

        return options;
    }

    download() {
        const { greater24Hours } = this;
        const { daily, hours, maxUv, totalUv } = this.fetchData;
        const uvList = greater24Hours ? daily : hours;

        if (uvList.length === 0) return this.$message.warning(`${this.$t("statistics.common.chart.errMsg[2]")}`);

        let str = `${this.$t("statistics.user.tip16")},IP(${this.$t("statistics.user.tip17", { prefix: "" })})\n`;

        uvList.forEach(item => {
            str += `${timeFormat(item["timestamp"] * 1000)},`;
            str += item.uvCount + "\n";
        });

        str += `${this.$t("statistics.user.tip18")}(${greater24Hours ? this.$t("statistics.user.tip13") : this.$t("statistics.user.tip14")
            }),${maxUv} ` + "\n";

        if (!greater24Hours) str += `${this.$t("statistics.user.tip19")}, ${totalUv} ` + "\n";

        this.$emit("downloadExcel", {
            name: `${this.$t("statistics.user.tip20")}`,
            str,
        });
    }
}
</script>

<style lang="scss" scoped>
.chart {
    width: 100%;
    height: 280px;
}

.total .ip-data {
    display: inline-block;

    >span {
        color: #666666;
    }

    &+.ip-data {
        margin-left: 16px;
    }
}
</style>
