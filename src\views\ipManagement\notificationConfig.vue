<template>
    <div v-loading="loading" class="config-wrapper">
        <el-form :rules="rules" :model="configForm" ref="configForm" label-width="120px">
            <el-form-item :label="$t('ipManagement.notification.formTitle[0]')" prop="email">
                <el-input
                    type="textarea"
                    v-model="configForm.email"
                    :disabled="!isEdit"
                    :placeholder="$t('ipManagement.notification.formInfo[0]')"
                />
            </el-form-item>
            <el-form-item prop="voice">
                <template slot="label">
                    {{ $t('ipManagement.notification.formTitle[1]') }}
                    <span class="question-style">
                        <el-tooltip :content="$t('ipManagement.notification.formInfo[1]')">
                            <ct-svg-icon icon-class="question-circle" class-name="ct-sort-drag-icon" />
                        </el-tooltip>
                    </span>
                </template>
                <el-input
                    type="textarea"
                    v-model="configForm.voice"
                    :disabled="!isEdit"
                    :placeholder="$t('ipManagement.notification.formInfo[2]')"
                />
            </el-form-item>
            <el-form-item :label="$t('ipManagement.notification.formTitle[2]')" prop="message">
                <el-input
                    type="textarea"
                    v-model="configForm.message"
                    :disabled="!isEdit"
                    :placeholder="$t('ipManagement.notification.formInfo[3]')"
                />
            </el-form-item>
        </el-form>
        <div class="config-wrapper__footer">
            <span v-if="isEdit">
                <el-tooltip effect="dark" :disabled="isWhitelistUser">
                    <template slot="content">
                        <span v-html="$t('ipManagement.tip', { orderLink })" />
                    </template>
                    <el-button type="primary" :disabled="!isWhitelistUser" @click="submit">
                        {{ $t('ipManagement.notification.btns[3]') }}
                    </el-button>
                </el-tooltip>
                <el-button @click="cancel">
                    {{ $t('ipManagement.notification.btns[4]') }}
                </el-button>
            </span>
            <span v-else>
                <el-tooltip effect="dark" :disabled="isWhitelistUser">
                    <template slot="content">
                        <span v-html="$t('ipManagement.tip', { orderLink })" />
                    </template>
                    <el-button type="primary" :disabled="!isWhitelistUser" @click="edit">
                        {{ $t('ipManagement.notification.btns[2]') }}
                    </el-button>
                </el-tooltip>
                <el-button v-if="isDeclassification" @click="handleDeclassification(false)">
                    {{ $t('ipManagement.notification.btns[0]') }}
                </el-button>
                <el-button v-else @click="handleDeclassification(true)">
                    {{ $t('ipManagement.notification.btns[1]') }}
                </el-button>
            </span>
        </div>
    </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from "vue-property-decorator";
import CtSvgIcon from "@/components/ctSvgIcon/index.vue";
import { commonLinks } from "@/utils/logic/url";
import { ipManagementUrl } from "@/config/url/ipManagement";

@Component({
    name: "NotificationConfig",
    components: {
        CtSvgIcon,
    },
})
export default class NotificationConfig extends Vue {
    @Prop({ default: true, type: Boolean }) private isWhitelistUser!: boolean;
    private loading = false;
    private isEdit = false;
    private isDeclassification = true;
    private configForm = {
        email: "",
        voice: "",
        message: "",
    };
    private defaultForm = {
        email: "",
        voice: "",
        message: "",
    };
    private rules = {
        email: [
            { required: true, message: this.$t("ipManagement.notification.formInfo[0]"), trigger: "blur" },
            { validator: this.checkEmail, trigger: "blur" },
        ],
        voice: { validator: this.checkPhone, trigger: "blur" },
        message: { validator: this.checkPhone, trigger: "blur" },
    };

    get orderLink() {
        return commonLinks.orderLink;
    }

    @Watch("isDeclassification", { immediate: true })
    private isDeclassificationChange(val: boolean) {
        this.getConfigDetail();
    }

    /**
     * 获取配置项
     */
    private async getConfigDetail() {
        this.loading = true;
        const { email, voice_phone, msg_phone } = (await this.$ctFetch(ipManagementUrl.GetNotify, {
            data: {
                product: "020",
                masking: this.isDeclassification,
            },
        })) as any;
        this.configForm = {
            email,
            voice: voice_phone,
            message: msg_phone,
        };
        this.defaultForm = Object.assign(this.defaultForm, this.configForm);
    }

    /**
     * 提交表单
     */
    private submit() {
        (this.$refs?.configForm as any).validate((valid: boolean) => {
            if (valid) {
                this.saveConfig();
            }
        });
    }

    /**
     * 保存配置
     */
    private async saveConfig() {
        this.loading = true;
        await this.$ctFetch(ipManagementUrl.UpdateNotify, {
            method: "POST",
            data: {
                product: "020",
                email: this.configForm.email,
                voice_phone: this.configForm.voice,
                msg_phone: this.configForm.message,
            },
            headers: {
                "Content-Type": "application/json",
            },
        });
        this.isEdit = false;
        this.getConfigDetail();
    }

    /**
     * 编辑配置项
     */
    private edit() {
        this.isEdit = true;
        this.isDeclassification = false;
    }

    /**
     * 取消编辑
     */
    private cancel() {
        this.configForm = Object.assign(this.configForm, this.defaultForm);
        (this.$refs?.configForm as any).clearValidate();
        this.isEdit = false;
    }

    /**
     * 显示全部/加密显示
     * @param val 是否脱敏
     */
    private async handleDeclassification(val: boolean) {
        if (val) {
            this.isDeclassification = val;
            return;
        }

        await this.$confirm(`${this.$t("ipManagement.showTextTip")}`, {
            title: `${this.$t("common.messageBox.title")}`,
            confirmButtonText: `${this.$t("common.dialog.submit")}`,
            cancelButtonText: `${this.$t("common.dialog.cancel")}`,
        });

        this.isDeclassification = val;
    }

    /**
     * 校验邮箱
     * @param rule 规则
     * @param value 校验值
     * @param callback 校验回调
     */
    private checkEmail(rule: string, value: string, callback: Function) {
        if (!value) {
            return callback();
        }
        if (
            value
                .split(";")
                // eslint-disable-next-line no-useless-escape
                .every(email => new RegExp(/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/, "i").test(email))
        ) {
            return callback();
        }
        return callback(new Error(this.$t("ipManagement.notification.validInfo[0]") as string));
    }

    /**
     * 校验手机号
     * @param rule 规则
     * @param value 校验值
     * @param callback 校验回调
     */
    private checkPhone(rule: string, value: string, callback: Function) {
        if (!value) {
            return callback();
        } else if (!value.split(";").every(phone => /^\+?(?:[0-9] ?){6,14}[0-9]$/.test(phone))) {
            return callback(new Error(this.$t("ipManagement.notification.validInfo[1]") as string));
        } else if (value.length > 255) {
            return callback(new Error(this.$t("ipManagement.notification.validInfo[2]") as string))
        } else {
            return callback();
        }
    }
}
</script>

<style lang="scss" scoped>
.config-wrapper {
    .el-textarea {
        width: 600px;
    }
    &__footer {
        width: 720px;
        display: flex;
        justify-content: center;
        .el-button + .el-button {
            margin-left: var(--margin-8x);
        }
    }
}
</style>
