<template>
    <section class="domain-edit-wrap" v-loading="loading">
        <section class="domain-edit">
            <el-tabs v-model="activeTab" :tab-position="!isXs ? 'left' : 'top'" class="domain-edit-menu">
                <el-tab-pane
                    v-for="(item, key) in gatewayTabs"
                    :key="key"
                    :label="item.label"
                    :name="item.prop"
                    :ref="item.prop"
                    :disabled="item.disabled"
                >
                    <span slot="label">
                        <i class="el-icon-warning show-icon" v-show="domain_batch_icon_show[item.prop]" />
                        {{ item.name }}
                    </span>
                </el-tab-pane>
            </el-tabs>

            <el-scrollbar class="domain-edit-content">
                <el-col :span="24" ref="orderform">
                    <component
                        v-for="(item, idx) in gatewayTabs"
                        :key="`item_${idx}`"
                        :is="item.prop"
                        :showCurrentTab="activeTab === item.prop"
                        v-show="activeTab === item.prop"
                        :ref="item.prop"
                        :children="item.children"
                        :isTabValid="isTabValid"
                        :productCodeModel="productCodeModel"
                    />
                </el-col>
            </el-scrollbar>
        </section>
        <!-- 展示校验结果 -->
        <validation-modal
            v-model="showValidationModal"
            :validation-error="validateArrFromBe"
            :title="validationTitle"
        />
    </section>
</template>

<script>
import { ctFetch } from "@/utils";
import { N_URL_FORM_OPERATION_BATCHSUBMIT } from "@/config/url";
import ValidationModal from "./ValidationModal";
import { ScreenModule } from "@/store/modules/screen";
import gatewayMapMix from "./gatewayMapMinxin";
import gatewayDataMix from "./gatewayDataMixin";
import { DomainModule } from "@/store/modules/domain";

export default {
    mixins: [gatewayMapMix, gatewayDataMix],
    components: { ValidationModal },
    props: ["rootScreen", "ifsubmit", "isTabValid"],
    data() {
        return {
            domain_batch_icon_show: DomainModule.domain_batch_icon_show,
            items: {},
            gatewayTabs: [],
            activeTab: "origin",
            namespace: "",
            activeDomainTab: "",
            activeGroupTab: "",
            submitLoading: false,
            submitDesc: {
                text: this.$t("domain.list.batchEditBtn"),
                type: "default",
            },
            domain_list: [],
            signature: "",
            error: {},
            showDomainGroup: {},
            loading: false,
            showValidationModal: false,
            validateArrFromBe: [],
            validationTitle: "",
            // notReady: true,
            wholeOriginPolicyChange: false,
            productCodeModel: "",
        };
    },
    computed: {
        isXs() {
            return ScreenModule.isXs;
        },
        params() {
            const param = {};
            for (let i = 0; i < this.$refs.orderform.$children.length; i++) {
                Object.assign(param, this.$refs.orderform.$children[i].formData);
                param.domain_list = this.domain_list;
            }
            return {
                data: param,
            };
        },
        submitUrlWithQuery() {
            return `${N_URL_FORM_OPERATION_BATCHSUBMIT}`;
        },
    },
    mounted() {
        this.$ctBus.$on("wholeOriginPolicyValueChange", data => {
            this.wholeOriginPolicyChange = data;
        });
    },
    destroyed() {
        this.$ctBus.$off("wholeOriginPolicyValueChange");
    },
    watch: {
        ifsubmit(val) {
            if (val) {
                // 提交
                this.submit();
            }
        },
    },
    methods: {
        async submit() {
            DomainModule.nGetDomainAllBatchIcon();
            const validateForm = [];
            if (this.$refs.orderform.$children) {
                for (let i = 0; i < this.$refs.orderform.$children.length; i++) {
                    if (this.$refs.orderform.$children[i].$refs.basic) {
                        validateForm.push(this.$refs.orderform.$children[i].$refs.basicForm);
                    }
                    if (this.$refs.orderform.$children[i].$refs.sourceStation) {
                        validateForm.push(this.$refs.orderform.$children[i].$refs.sourceStationForm);
                    }
                    if (this.$refs.orderform.$children[i].$refs.httpReqHeader) {
                        validateForm.push(this.$refs.orderform.$children[i].$refs.httpReqHeaderForm);
                    }
                    if (this.$refs.orderform.$children[i].$refs.filetypeTtl) {
                        validateForm.push(this.$refs.orderform.$children[i].$refs.filetypeTtlForm);
                    }
                    if (this.$refs.orderform.$children[i].$refs.errorCode) {
                        validateForm.push(this.$refs.orderform.$children[i].$refs.errorCodeForm);
                    }
                    if (this.$refs.orderform.$children[i].$refs.respHeaders) {
                        validateForm.push(this.$refs.orderform.$children[i].$refs.respHeadersForm);
                    }
                    if (this.$refs.orderform.$children[i].$refs.refererChain) {
                        validateForm.push(this.$refs.orderform.$children[i].$refs.refererChainForm);
                    }
                    if (this.$refs.orderform.$children[i].$refs.ipBlackWhiteList) {
                        validateForm.push(this.$refs.orderform.$children[i].$refs.ipBlackWhiteListForm);
                    }
                }
            }

            try {
                await Promise.all(validateForm.map(itm => this.formValidate2Promise(itm)));
            } catch (error) {
                this.$message({
                    message: error,
                    type: "error",
                });
                this.submitLoading = false;
                return false;
            }
            const { params } = this;
            ctFetch(this.submitUrlWithQuery, {
                method: "POST",
                body: params,
                clearQsWithPost: false,
                clearEmptyParams: false,
                headers: {
                    "Content-Type": "application/json",
                },
            })
                .then(async data => {
                    this.submitLoading = false;
                    this.$emit("batchLoading", false);
                    // 判断是否有部分域名失败
                    // 如果有部分域名失败
                    if (data.failedDomainList.length === 0 || data.failedDomainList === "") {
                        this.$message.success(this.$t("common.message.success"));

                        setTimeout(() => {
                            this.$router.push({
                                name: "ndomain.list",
                            });
                        }, 1000);
                    } else if (data.failedDomainList && data.failedDomainList.length > 0) {
                        await this.$alert(data.failedDomainList.join("<br/>"), this.$t("domain.batch.tip2"), {
                            confirmButtonText: this.$t("common.dialog.submit"),
                            customClass: "message-alert",
                            dangerouslyUseHTMLString: true,
                        });
                        setTimeout(() => {
                            this.$router.push({
                                name: "ndomain.list",
                            });
                        }, 1000);
                    } else {
                        this.validateArrFromBe = data.validate;
                        this.validationTitle = `${this.submitDesc.text}${this.$t("domain.create.tip30")}`;
                        this.showValidationModal = true;
                    }
                })
                .catch(err => {
                    this.submitLoading = false;
                    this.$emit("batchLoading", false);
                    const validate = err.data && err.data.data && err.data.data.validate;
                    let v = true;
                    if (validate) {
                        v = validate.reduce((acc, cur) => {
                            return acc && cur.pass === "true";
                        }, true);
                        if (!v) {
                            this.validateArrFromBe = validate;
                            this.validationTitle = `${this.submitDesc.text}${this.$t("domain.create.tip30")}`;
                            this.showValidationModal = true;
                        }
                    }
                    if (v) {
                        // 默认展示接口报错信息
                        // const errMsg = (err.data && err.data.reason) || err.reason;
                        // this.$message.error(errMsg || "操作失败");
                        this.$errorHandler(err);
                    }
                });
        },
    },
    created() {
        this.gatewayTabs = [...this.defaultTabs];
        let domainArr = window.sessionStorage.getItem("batchEdit");
        domainArr = JSON.parse(domainArr);
        this.productCodeModel = domainArr[0]?.productCode || "001";
        for (let i = 0; i < domainArr.length; i++) {
            this.domain_list.push(domainArr[i].domain);
        }
    },
};
</script>

<style lang="scss" scoped>
// 错误提示感叹号要用红色，凸显提示作用
.el-icon-warning {
    color: $g-color-red;
}
::v-deep {
    .show-icon {
        color: #f56c6c;
        font-size: 14px;
        vertical-align: middle;
        font-family: "element-icons" !important;
    }
}

.domain-edit-wrap {
    height: 100%;
    overflow: hidden;
    flex: 1;
    display: flex;
    flex-direction: column;
    margin: 0 10px;
    // 新增样式
    ::v-deep .el-scrollbar {
        margin: 0 !important; // 上下留白
        flex: 1; // 自适应高度
    }
    ::v-deep .el-scrollbar__wrap {
        .el-scrollbar__view {
            padding: 0 10px; // 内容区固定一个间距出来
            height: 100%;
        }
        overflow-x: hidden;
    }
}
.domain-edit {
    overflow: hidden;
    flex: 1;
    margin: 10px 0;
    padding: 10px;
    display: flex;
    background: #fff;

    @include g-media-attr(
        (
            attr: "flex-direction",
            sm: row,
            xs: column,
        )
    );
}

// 大屏左右布局，小屏上下布局
.domain-edit-menu {
    // @include g-height(auto, 100%);
    background: #fff;
    height: 100%;
    overflow-y: auto;
}

.domain-edit-content {
    flex: 1;

    ::v-deep .el-scrollbar__wrap {
        overflow-x: hidden;
    }
}

.submit {
    text-align: center;
    margin-bottom: 10px;
}
</style>
