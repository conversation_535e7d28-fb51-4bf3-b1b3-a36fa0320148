<template>
    <el-dialog
        :title="dialogTitle"
        :append-to-body="true"
        :close-on-click-modal="false"
        :visible="dialogInfo.visible"
        :before-close="() => $emit('cancel')"
        width="800px"
        class="limit-speed-dialog"
    >
        <el-form
            ref="form"
            :model="form"
            :rules="dialogInfo.visible ? getRules : {}"
            label-width="140px"
            :validate-on-rule-change="false"
        >
            <common-condition v-model="form" :list="dialogInfo.list" :required="false" />
            <el-form-item prop="rate" :label="$t('domain.detail.limitSpeed.tip2')">
                <el-input v-model.number="form.rate">
                    <template slot="append">
                        <el-select v-model="form.unit">
                            <el-option
                                v-for="item in unitOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            />
                        </el-select>
                    </template>
                </el-input>
            </el-form-item>
            <el-form-item prop="weight" :label="$t('domain.detail.label48')">
                <el-input v-model.number="form.weight" />
            </el-form-item>
        </el-form>

        <div slot="footer">
            <el-button class="dialog-btn" @click="cancel">{{ $t("domain.cancel") }} </el-button>
            <el-button type="primary" class="dialog-btn" @click="submit">{{
                $t("common.dialog.submit")
            }}</el-button>
        </div>
    </el-dialog>
</template>

<script>
import { getLang } from "@/utils";
import commonCondition from "@/components/commonCondition/index.vue";

export default {
    name: "LimitSpeedDialog",
    props: {
        dialogInfo: Object,
        unitOptions: Array,
    },
    components: { commonCondition },
    data() {
        return {
            form: {
                id: "",
                mode: null,
                content: "",
                unit: "b/s",
                rate: null,
                weight: 10,
            },
        };
    },
    watch: {
        "dialogInfo.visible"(val) {
            if (!val) {
                setTimeout(() => {
                    this.form = {
                        id: "",
                        mode: null,
                        content: "",
                        unit: "b/s",
                        rate: null,
                        weight: 10,
                    };
                    this.$refs.form?.clearValidate();
                }, 100);
                return;
            }

            this.form = { ...this.dialogInfo.form };
            this.$nextTick(() => {
                this.$refs.form?.clearValidate();
            });
        },
    },
    methods: {
        cancel() {
            this.$emit("cancel");
        },
        async submit() {
            const isValid = await new Promise(resolve => this.$refs.form.validate(resolve));
            if (!isValid) return;

            this.$emit("submit", { ...this.form });
        },
        rateValidator(rule, value, callback) {
            const MAX_SPEED = 100 * 1024 * 1024;
            const MIN_SPEED = 1;
            const { rate, unit } = this.form;
            let speed = rate;
            if (unit === this.unitOptions[1].value) {
                speed *= 1024;
            } else if (unit === this.unitOptions[2].value) {
                speed *= 1024 * 1024;
            }

            if (speed > MAX_SPEED) {
                callback(new Error(`${this.$t("domain.detail.limitSpeed.tip4")}`));
            } else if (speed < MIN_SPEED) {
                callback(new Error(`${this.$t("domain.detail.limitSpeed.tip5")}`));
            }

            callback();
        },
    },
    computed: {
        dialogTitle() {
            const seprate = getLang() === "en" ? " " : "";
            return `${
                this.dialogInfo.type === "create" ? this.$t("domain.add2") : this.$t("domain.modify")
            }${seprate}${this.$t("domain.detail.limitSpeed.tip1")}`;
        },
        getRules() {
            return {
                rate: [
                    { required: true, message: this.$t("domain.detail.limitSpeed.tip3"), trigger: "blur" },
                    { type: "number", message: this.$t("domain.detail.tip95") },
                    { validator: this.rateValidator },
                ],
                weight: [
                    { required: true, message: this.$t("domain.detail.placeholder20"), trigger: "blur" },
                    {
                        type: "number",
                        min: 1,
                        max: 100,
                        message: this.$t(
                            "simpleForm.alogicCacheMixin.ValidationErrorMessages.InvalidPriority"
                        ),
                    },
                ],
            };
        },
    },
};
</script>
