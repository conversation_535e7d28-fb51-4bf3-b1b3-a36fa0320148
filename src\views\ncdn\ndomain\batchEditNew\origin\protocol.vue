<template>
    <batch-edit-checked v-model="checked">
        <el-form
            class="origin-protocol"
            label-width="140px"
            label-position="right"
            :model="form"
            :rules="rules"
            ref="formRef"
            :disabled="!checked"
        >
            <el-form-item prop="backorigin_protocol" :rules="rules.backorigin_protocol">
                <span slot="label">
                    {{ $t("domain.create.originPolicy") }}000
                    <span>
                        <el-tooltip placement="top" :content="$t('domain.create.tip6')" :offset="-120">
                            <ct-svg-icon
                                icon-class="question-circle"
                                class-name="ct-sort-drag-icon"
                            ></ct-svg-icon>
                        </el-tooltip>
                    </span>
                </span>
                <el-radio-group v-model="form.backorigin_protocol" @change="onBackoriginProtocolChange">
                    <el-radio label="http">{{ $t("domain.editPage.radio1[0]") }}</el-radio>
                    <el-radio label="https">{{ $t("domain.editPage.radio1[1]") }}</el-radio>
                    <el-radio label="follow_request">{{ $t("domain.editPage.radio1[2]") }}</el-radio>
                </el-radio-group>
            </el-form-item>
            <div class="server_port_wrapper">
                <div class="server_style">
                    <el-form-item :label="$t('domain.editPage.label4')" class="is-required"> </el-form-item>
                    <lock-tip
                        :originPortDisabled="originPortDisabled"
                        :addon="form.backorigin_protocol !== 'https'"
                    >
                        <el-form-item
                            v-if="form.backorigin_protocol !== 'https'"
                            label="HTTP"
                            prop="basic_conf.http_origin_port"
                            :rules="rules.http_origin_port"
                            class="server_http_port_wrapper"
                            label-width="50px"
                        >
                            <el-input
                                v-model.number="form.basic_conf.http_origin_port"
                                maxlength="16"
                                class="port-input"
                                :disabled="originPortDisabled"
                            ></el-input>
                        </el-form-item>
                    </lock-tip>
                    <lock-tip
                        :originPortDisabled="originPortDisabled"
                        :addon="form.backorigin_protocol !== 'http'"
                    >
                        <el-form-item
                            v-if="form.backorigin_protocol !== 'http'"
                            label="HTTPS"
                            prop="basic_conf.https_origin_port"
                            :rules="rules.https_origin_port"
                            class="server_https_port_wrapper"
                            label-width="80px"
                        >
                            <el-input
                                v-model.number="form.basic_conf.https_origin_port"
                                maxlength="16"
                                class="port-input"
                                :disabled="originPortDisabled"
                            ></el-input>
                        </el-form-item>
                    </lock-tip>
                </div>
                <!-- 跟随请求端口回源 -->
                <el-form-item prop="follow_request_backport" label-width="150px">
                    <span slot="label">
                        {{ $t("domain.editPage.label32") }}
                        <el-tooltip placement="top" :content="$t('domain.editPage.originPortTip.tip2')">
                            <ct-svg-icon
                                icon-class="question-circle"
                                class-name="ct-sort-drag-icon"
                            ></ct-svg-icon>
                        </el-tooltip>
                    </span>
                    <el-switch
                        v-model="form.follow_request_backport"
                        :active-value="1"
                        :inactive-value="0"
                    ></el-switch>
                </el-form-item>
            </div>
        </el-form>
    </batch-edit-checked>
</template>

<script lang="ts">
import { Component, Mixins, Watch } from "vue-property-decorator";
import i18n from "@/i18n/index";
import { portRegex } from "@/utils/validator.utils";
import ctSvgIcon from "@/components/ctSvgIcon/index.vue";
import LockTip from "@/views/domainConfig/components/lockTIp.vue";
import BatchItemMixin from "../mixins/batch.mixin";
import { cloneDeep } from "lodash-es";

@Component({
    name: "BatchEditOriginProtocol",
    components: {
        ctSvgIcon,
        LockTip,
    },
})
export default class BatchEditOriginProtocol extends Mixins(BatchItemMixin) {
    checked = false;
    form = {
        backorigin_protocol: "http",
        basic_conf: {
            https_origin_port: 443,
            http_origin_port: 80,
        },
        follow_request_backport: 0,
    };
    rules = {
        backorigin_protocol: [{ required: true, message: i18n.t("domain.detail.tip75"), trigger: "change" }],
        http_origin_port: [{ required: true, validator: this.valid_http_origin_port, trigger: "blur" }],
        https_origin_port: [{ required: true, validator: this.https_origin_port_valid, trigger: "blur" }],
    };

    async valid_http_origin_port(rule: any, value: any, callback: any) {
        const pattern = portRegex;
        if (value === "" || value === null || value === undefined) {
            return callback(new Error(i18n.t("domain.detail.tip90") as string));
        }
        if (!pattern.test(value) || Number(value) === 443) {
            return callback(new Error(i18n.t("domain.detail.tip91") as string));
        }
        return callback();
    }

    async https_origin_port_valid(rule: any, value: any, callback: any) {
        const pattern = portRegex;
        if (value === "" || value === null || value === undefined) {
            // return callback(new Error("请填写HTTPS端口"));
            return callback(new Error(i18n.t("domain.detail.tip92") as string));
        }
        if (!pattern.test(value)) {
            // return callback(new Error("请输入正确的端口号，1-65535"));
            return callback(new Error(i18n.t("domain.detail.tip93") as string));
        }
        return callback();
    }
    get formData() {
        return cloneDeep(this.form);
    }

    get originPortDisabled() {
        return !!this.form.follow_request_backport;
    }

    onBackoriginProtocolChange() {
        this.form.basic_conf = {
            http_origin_port: 80,
            https_origin_port: 443,
        };
        this.$ctBus.$emit("batchEdit:backorigin_protocol", {
            backorigin_protocol: this.form.backorigin_protocol,
            checked: this.checked,
        });
    }

    @Watch("checked")
    onCheckedChange() {
        this.$ctBus.$emit("batchEdit:backorigin_protocol", {
            backorigin_protocol: this.form.backorigin_protocol,
            checked: this.checked,
        });
    }
}
</script>

<style lang="scss" scoped>
.server_port_wrapper {
    display: flex;
    justify-content: start;
    .server_style {
        display: flex;
    }
}
</style>
