worker_processes 8;
worker_rlimit_nofile 65535;
pid sbin/nginx.pid;

events {
  use epoll;
  worker_connections  65535;
}

http {
  include mime.types;
  include log.conf;

  default_type aplication/octet-stream;
  sendfile on;
  server_tokens off;
  vhost_traffic_status_zone;
  gzip on;
  gzip_min_length 1k;
  gzip_buffers 4 16k;
  gzip_http_version 1.1;
  gzip_comp_level 3;
  gzip_types text/plain application/x-javascript text/css application/xml;
  gzip_vary on;

  server {
    listen 80;
    client_max_body_size 100M;
    proxy_set_header Host $http_host;
    proxy_set_header X-real-ip $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Real-URI $uri;

    fastcgi_intercept_errors on;
    proxy_intercept_errors on;
    proxy_http_version 1.1;
    proxy_set_header Connection "";
    absolute_redirect off;

    location / {
        # 不缓存html 和 htm结尾的文件
        if ($request_filename ~* .*\.(?:htm|html)$) {
            add_header Cache_Control "private, no-store, no-cache, must-revalidate, proxy-revalidate";
        }
        etag on;
        alias /www/;
    }

    location /metrics {
        proxy_pass http://localhost:9913/metrics;
        access_log off;
    }

    location /status {
      vhost_traffic_status_display;
      vhost_traffic_status_display_format html;
      access_log off;
    }

	  location /h5/aocdn/mock/ {
        proxy_set_header host "yapi.ctcdn.cn";
        proxy_pass https://yapi.ctcdn.cn/mock/13/;
    }
  }
}
