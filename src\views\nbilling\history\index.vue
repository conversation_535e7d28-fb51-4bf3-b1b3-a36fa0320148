<template>
    <el-table :empty-text="$t('common.table.empty')" :data="historyData" v-loading="loading">
        <el-table-column type="index" :label="$t('billing.common.itm6')" width="80" />
        <el-table-column :label="$t('billing.history.methodRecord')" min-width="200">
            <template slot-scope="{ row }">
                {{ row.resourceTypeCname || "" }} : {{ row.billingTypeCname }}
            </template>
        </el-table-column>
        <el-table-column prop="createTime" min-width="160" :label="$t('billing.history.submit')" />
        <el-table-column prop="effDate" min-width="160" :label="$t('billing.productTable.column3')" />
    </el-table>
</template>

<script lang="ts">
import { nCdnBillingUrl } from "@/config/url";
import { Component, Vue } from "vue-property-decorator";
import { HistoryData } from "@/types/billing";

@Component({
    name: "History",
})
export default class History extends Vue {
    loading = false;
    private historyData: HistoryData[] = [];
    private async getHistory() {
        this.loading = true;
        const res = await this.$ctFetch<{ list?: HistoryData[] }>(nCdnBillingUrl.history, { cache: false });
        this.historyData = res.list || [];
    }

    mounted() {
        this.getHistory();
    }
}
</script>
