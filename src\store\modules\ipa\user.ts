// 废弃
import { VuexModule, Module, Action, Mutation, getModule } from "vuex-module-decorators";

import store from "../../index";
import { ctFetch } from "@cdnplus/common/utils";
import { CurrentUrl, LoginUrl } from "@/config/url";

const origin = window.location.origin;

interface UserInfo {
    name: string;
    userId: string;
    email: string;
    saleChannel?: string;
    channel?: string;
}

interface CurrentPromise {
    isLoggedIn: boolean;
    property: UserInfo;
}

export interface UserState {
    isVip: boolean;
    isCtclouds: boolean;
    isLoggedIn: boolean;
    userInfo: UserInfo;
    workspaceId: string;
    // lang: string;
}

@Module({ dynamic: true, store, name: "user" })
class User extends VuexModule implements UserState {
    // public isVip = !process.env.PLATFORM?.includes("bs") && !domain.includes("ctyun.cn"); // 以域名为标准，控制全局展示
    // public isVip = origin.endsWith("ctcdn.cn"); // 以域名为标准，控制全局展示
    public isVip = origin.endsWith("ctcdn.cn"); // 以域名为标准，控制全局展示
    public isCtclouds = origin.endsWith("ctclouds.com"); // 以域名为标准，控制全局展示
    public isLoggedIn = false;
    public userInfo: UserInfo = {
        // 预设一下空值
        name: "",
        userId: "",
        email: "",
        channel: "",
        saleChannel: "",
    };
    public workspaceId = "";
    // public lang = getLang();

    // @Mutation
    // public SET_LANG(lang: string) {
    //     lang = switchLang(lang);
    //     this.lang = lang;
    //     setLang(lang);
    //     i18n.locale = lang;
    //     ctFetchI18n.locale = lang;
    // }

    @Mutation
    public SET_USERINFO(userInfo: UserInfo) {
        this.userInfo = userInfo;
    }

    @Mutation
    public SET_LOGGEDIN(loggedIn: boolean) {
        this.isLoggedIn = loggedIn;

        if (loggedIn === false) {
            // 未登录
            window.location.href = LoginUrl;
        }
    }

    @Mutation
    public SET_WORKSPACEID(workspaceId: string) {
        if (workspaceId === this.workspaceId) return;

        this.workspaceId = workspaceId;

        ctFetch.config({
            mergeDataStrategy: "merge",
            data: { workspaceId },
            wrapperDataWithPost: true, // 将 post 请求中的 workspaceId 移到 body.data 下
        });
    }

    @Action
    public async GetUserInfo(payload: { cache: boolean } = { cache: true }) {
        if (payload.cache && this.isLoggedIn) {
            return this.userInfo;
        }

        // 根据不同环境使用 layout 提供的登录数据，用直接请求兜底 1
        let fetchPromise: Promise<CurrentPromise>;
        if (window.AlogicLayout && window.AlogicLayout.authCurrentPromise) {
            fetchPromise = window.AlogicLayout.authCurrentPromise as Promise<CurrentPromise>;
        } else if (window.CtcloudLayout && window.CtcloudLayout.authCurrentPromise) {
            fetchPromise = window.CtcloudLayout.authCurrentPromise as Promise<CurrentPromise>;
        } else {
            // bs使用通用current即可
            fetchPromise = ctFetch(CurrentUrl);
        }

        const {
            isLoggedIn,
            property: userInfo,
        }: {
            isLoggedIn: boolean;
            property: UserInfo;
        } = await fetchPromise;

        this.SET_USERINFO(userInfo);
        this.SET_LOGGEDIN(isLoggedIn);

        // 普通用户，使用用户id作为工作区id
        if (isLoggedIn && !this.isVip) {
            this.SET_WORKSPACEID(userInfo.userId);
        }

        return userInfo;
    }
}

export const UserModule = getModule(User);
