import { createI18n } from "@cutedesign/ui";
import zh from "./zh";
import en from "./en";
import VueI18n from "vue-i18n";
import { getCookie } from "@/utils/cookie.js";

const isCtcloud = window.location.origin.includes("cloud");

// 中文
export const ZH_LANG = "zh";

// cookie 键
// 所有控制台统一使用 `ctc_lang`，因为理论上一个控制台使用了某个语言，其他控制台也都应该使用这个语言。
export const LANG_COOKIE_KEY = "ctc_lang";

// 设置默认语言：
// - 国际站默认从cookie取。
// - 其他站点默认为中文。
// 特别注意：国际站的layout不支持重写菜单点击事件。因此，在语言切换后，无法立即更新本地缓存的 `ctc_lang`。
// 这会导致某些 js 文件在初始化时使用的是上一次切换设置的语言，而不是当前正确的默认语言。

// 经测试，`SET_LANG` 方法仅在国际站中生效。页面上显示的最终语言由 `DEFAULT_LANG` 和 `SET_LANG` 方法共同决定，
// 但 js 文件加载时所用的语言设置无法被此方法更改。
let DEFAULT_LANG_INNER = isCtcloud
    ? ({
          "en-us": "en",
          "zh-cn": "zh",
      } as { [key: string]: string })[getCookie(LANG_COOKIE_KEY) || "en-us"]
    : "zh";

// 获取url上的查询参数
function findGetParameter(paramName: string): string | null {
    // 由于 fcdn 路由模式改为 hash，因此这里window.location.search不可用，需要使用window.location.hash
    const queryParams = new URLSearchParams(window.location.hash.split("?")?.[1] || "");
    return queryParams.get(paramName);
}

// 国际站特殊处理
if (isCtcloud) {
    // 国际站语言设置逻辑
    // 如果url上的lang是en-us或zh-cn，直接使用
    // 如果url上的lang是en-us或zh-cn以为的其他字符，默认使用en
    // 如果url上没有lang,但是localStorage中有ctc_lang，依然不会使用localStorage中的, 默认使用en
    // 如果url上没有lang，localStorage中也没有ctc_lang，默认使用en
    const lang = findGetParameter("lang");
    if (lang && ["en-us", "zh-cn"].includes(lang)) {
        DEFAULT_LANG_INNER =
            ({
                "en-us": "en",
                "zh-cn": "zh",
            } as { [key: string]: string })[lang] || DEFAULT_LANG_INNER;

        // 这里还需考虑 createI18n 方法优先读取 localStorage 中的值,所以需要同步修改
        localStorage.setItem(LANG_COOKIE_KEY, DEFAULT_LANG_INNER);
    } else {
        // 同步localStorage的值
        localStorage.setItem(LANG_COOKIE_KEY, DEFAULT_LANG_INNER);
    }
}

export const DEFAULT_LANG = DEFAULT_LANG_INNER;

const options: { storageKey?: string; defaultLang?: string } & VueI18n.I18nOptions = {
    storageKey: "ctc_lang", // 本地持久化时的 localeStorage key，默认 lang用于获取标准 locale 参数
    defaultLang: DEFAULT_LANG, // 默认展示的语言，默认 zh用于获取标准 fallbackLocale 参数及 locale 兜底默认值
    silentFallbackWarn: true, // 静默警告
    // ... 其他 i18n 初始化参数（透传）
};

const i18n = createI18n(options);
process.env?.PLATFORM !== "bs" && !window.__POWERED_BY_QIANKUN__ && (document.title = `CDN ${i18n.locale === "en-us" ? "Console" : "控制台"}`);

// 扩展 locale
i18n.mergeLocaleMessage("zh", zh);
i18n.mergeLocaleMessage("en", en);

export default i18n;
