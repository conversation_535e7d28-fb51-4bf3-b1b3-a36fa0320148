<template>
  <ct-section-wrap :headerText="headerText" class="report-create-container">
    <el-card class="report-create-wrap">
        <el-form
            v-loading="loading"
            ref="form"
            :model="form"
            :label-width="isEn ? '140px' : '120px'"
            :rules="rules"
        >
        <div class="detail-title">{{ $t("report.create.title[0]") }}</div>

        <el-form-item :label="$t('report.create.form[0]')" prop="name">
            <el-input v-model="form.name" :placeholder="$t('report.create.placeholder[0]')" />
        </el-form-item>
        <el-form-item :label="$t('report.create.form[1]')" prop="frequency">
            <el-select v-model="form.frequency">
                <el-option
                    v-for="(item, index) in frequencyOptions"
                    :key="index"
                    :label="item.label"
                    :value="item.value"
                />
            </el-select>
        </el-form-item>
        <el-form-item v-if="form.frequency === 1" :label="$t('report.create.form[2]')" prop="time">
            <el-date-picker
                size="medium"
                v-model="form.time"
                type="datetimerange"
                :default-time="defaultTimeRange"
                :start-placeholder="$t('report.create.placeholder[1]')"
                :end-placeholder="$t('report.create.placeholder[2]')"
                :picker-options="rangePickerOptions"
                value-format="timestamp"
                @focus="handlePickerFocus"
                @blur="handlePickerBlur"
            />
        </el-form-item>
        <el-form-item :label="$t('report.create.form[3]')" prop="sendType">
            <template slot="label">
                <span>{{ $t('report.create.form[3]') }}</span>
            </template>
            <el-select
                v-model="form.sendType"
                :disabled="isUpdate"
                @change="sendTypeChange"
            >
                <el-option
                    v-for="(item, index) in sendTypeOptions"
                    :key="index"
                    :label="item.label"
                    :value="item.value"
                />
            </el-select>
        </el-form-item>
        <el-form-item :label="$t('report.create.form[4]')" prop="sendTime">
            <el-date-picker
                size="medium"
                v-model="form.sendTime"
                type="datetime"
                :default-time="defaultTime"
                :placeholder="$t('report.create.validInfo[2]')"
                :picker-options="pickerOptions"
                value-format="timestamp"
                popper-class="aocdn-ignore-report-create-date-picker"
            />
        </el-form-item>
        <el-form-item :label="$t('report.create.form[5]')" prop="email">
            <el-input
                v-model="form.email"
                type="textarea"
                :rows="2"
                :placeholder="$t('report.create.validInfo[4]')"
            />
        </el-form-item>
        <div class="detail-title">{{ $t("report.create.title[1]") }}</div>
        <el-form-item label-width="0" prop="dataList">
            <el-table :data="form.dataList">
                <el-table-column :label="$t('report.create.table[0]')" width="80">
                    <template slot-scope="scope">
                        {{ scope.$index + 1 }}
                    </template>
                </el-table-column>
                <el-table-column
                    v-if="dataListType === 1"
                    :label="$t('report.create.table[1]') + '/' + $t('report.create.table[2]')"
                    prop="productCode"
                    show-overflow-tooltip
                >
                    <template slot-scope="{ row }">
                        <span v-if="row.domain.length">{{ row.domain.slice(0, 100).join(", ") + (row.domain.length > 100 ? "..." : "") }}</span>
                        <span v-else>{{ $t(getProductName(row.productCode)) }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                    v-if="dataListType === 2"
                    :label="$t('report.create.table[1]')"
                    prop="productCode"
                    show-overflow-tooltip
                >
                    <template slot-scope="{ row }">
                        <span>{{ $t(getProductName(row.productCode)) }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                    v-if="dataListType === 2"
                    :label="$t('report.create.table[2]')"
                    prop="domain"
                    show-overflow-tooltip
                >
                    <template slot-scope="{ row }">
                        <span v-if="row.domain.length">{{ row.domain.slice(0, 100).join(", ") + (row.domain.length > 100 ? "..." : "") }}</span>
                        <span v-else>{{ $t("report.create.dialog.region[0]") }}</span>
                    </template>
                </el-table-column>
                <el-table-column v-if="dataListType === 2" :label="$t('report.create.table[3]')" width="200">
                    <template slot-scope="{ row }">
                        {{ regionMap[row.region] }}
                    </template>
                </el-table-column>
                <el-table-column :label="$t('report.create.table[4]')">
                    <template slot-scope="{ row }">
                        <span
                            v-for="(item, index) in row.chart"
                            :key="index"
                            class="table-span"
                        >
                            {{ $t(`report.create.dialog.chart[${+item - 1}]`) }}
                            <el-button
                                type="text"
                                icon="el-icon-delete"
                                :disabled="row.chart.length <= 1"
                                @click="spliceSpan(row.chart, index)"
                            />
                        </span>
                        <span
                            v-for="(item, index) in row.data"
                            :key="index"
                            class="table-span"
                        >
                            {{ $t(`report.create.dialog.data[${+item - 1}]`) }}
                            <el-button
                                type="text"
                                icon="el-icon-delete"
                                :disabled="row.data.length <= 1"
                                @click="spliceSpan(row.data, index)"
                            />
                        </span>
                    </template>
                </el-table-column>
                <el-table-column :label="$t('report.create.table[5]')" width="90">
                    <template slot-scope="{ $index }">
                        <el-button type="text" @click="handleDelete($index)">{{ $t('report.create.btns.delete') }}</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <div class="table-append">
                <el-button type="text" :disabled="form.dataList.length >= 10" @click="showDialog" >{{ `+ ${$t('report.create.btns.add')}` }}</el-button>
            </div>
        </el-form-item>

        </el-form>
        <cute-fixed-footer class="submit">
            <div class="footer-content">
                <el-button :loading="submitLoading" size="medium" @click="cancel">
                    {{ $t("report.create.btns.cancel") }}
                </el-button>
                <el-button :loading="submitLoading" type="primary" size="medium" @click="handleSubmit">
                    {{ $t("report.create.btns.save") }}
                </el-button>
            </div>
        </cute-fixed-footer>
    </el-card>
    <div class="fixed-append" />
    <AddChartOrDataDialog
        ref="addDialog"
        :type="form.sendType"
        :remain-length="10 - form.dataList.length"
        @add="addData"
    />
  </ct-section-wrap>
</template>

<script lang="ts">
import { Component, Vue, Watch, Provide, Inject } from "vue-property-decorator";
import { Form } from "element-ui";
import { getAm0 as get0 } from "@/utils";
import AddChartOrDataDialog from "./components/AddChartOrDataDialog.vue";
import { DatePickerOptions } from "element-ui/types/date-picker";
import ctSvgIcon from "@/components/ctSvgIcon/index.vue";
import { reportUrl } from "@/config/url";
import { StatisticsModule } from "@/store/modules/statistics";
import { ProductModule } from "@/store/modules/ncdn/nproduct";
import { timeFormat } from "@/filters/index";
import { getLang } from "@/utils";
import dayjs from "dayjs";

interface MyDatePickerOptions extends DatePickerOptions {
    reset(): void;
}

@Component({
    name: "reportCreate",
    components: { AddChartOrDataDialog, ctSvgIcon }
})
export default class ReportCreate extends Vue {
    private headerText = this.isUpdate ? this.$t("report.updateSubscription") : this.$t("report.addSubscription")
    private submitLoading = false
    private loading = false
    private frequencyOptions = [
        { label: this.$t("report.search.frequencyOptions[0]"), value: 1 },
        { label: this.$t("report.search.frequencyOptions[1]"), value: 2 },
        { label: this.$t("report.search.frequencyOptions[2]"), value: 3 },
        { label: this.$t("report.search.frequencyOptions[3]"), value: 4 },
        { label: this.$t("report.search.frequencyOptions[4]"), value: 5 },
        { label: this.$t("report.search.frequencyOptions[5]"), value: 6 },
        { label: this.$t("report.search.frequencyOptions[6]"), value: 7 },
    ]
    private sendTypeOptions = [
        { label: this.$t("report.create.sendTypeOptions[0]"), value: 1 },
        { label: this.$t("report.create.sendTypeOptions[1]"), value: 2 },
    ]
    private form: any = {
        name: "",
        frequency: 1,
        time: [],
        sendType: 1,
        sendTime: "",
        email: "",
        dataList: []
    }
    private dataListType = 1
    private regionMap = {
        0: this.$t("report.create.dialog.region[0]"),
        1: this.$t("report.create.dialog.region[1]"),
        2: this.$t("report.create.dialog.region[2]")
    }
    private rules = {
        name: [{ required: true, message: this.$t("report.create.validInfo[0]"), trigger: "blur" }],
        frequency: [{ required: true, message: this.$t("report.create.validInfo[1]"), trigger: "blur" }],
        time: [{ required: true, message: this.$t("report.create.validInfo[2]"), trigger: "blur" }],
        sendType: [{ required: true, message: this.$t("report.create.validInfo[3]"), trigger: "blur" }],
        sendTime: [
            { required: true, message: this.$t("report.create.validInfo[2]"), trigger: "blur" }
        ],
        email: [
            { required: true, message: this.$t("report.create.validInfo[4]"), trigger: "blur" },
            { validator: this.checkEmail, trigger: "blur" },
        ],
        dataList: [
            { required: true, message: this.$t("report.create.validInfo[6]"), trigger: "blur" },
        ]
    }
    private currentTime = new Date();
    private defaultTimeRange = ['00:00:00', this.currentTime.toLocaleTimeString()]
    private minDate: null | number = null;
    private rangePickerOptions: MyDatePickerOptions = {
        // 设置禁用时间
        disabledDate: (time: Date) => {
            // 获取今天0点数据
            const today0 = get0(new Date());
            // 禁用大于今天时间23：59：59
            if (+time > +new Date(+today0 + 24 * 60 * 60 * 1000 - 1)) return true;

            //当最小时间为null的时候则关闭禁用，点清空时会变为0，所以0也要判断
            if (this.minDate === null || this.minDate === 0) return false;

            // 由于现在选择时间后，默认会调整到当日23:59:59，跨度30天即为31天，需要减掉 1s
            const oneMonth = 31 * 24 * 60 * 60 * 1000 - 1000;
            // 超出当前选择时间前后31天的不可选择，time为选择框中出现的日期

            return !!this.minDate && (+time > this.minDate + oneMonth || +time < this.minDate - oneMonth);
        },
        onPick: ({ minDate }) => {
            this.minDate = +minDate;
        },
        reset: () => {
            if (!this.form.time || this.form.time.length === 0) this.minDate = null;
        },
    };
    private defaultTime = this.currentTime.toLocaleTimeString()
    private pickerOptions: DatePickerOptions = {
        // 设置禁用时间
        disabledDate: (time: Date) => {
            const today0 = get0(this.currentTime);
            return +time < +today0
        }
    };
    private originalPickerChangeMethod: any = null; // 保存原始的 picker change 方法

    get isUpdate() {
        return this.$route.name === "reportManagement.update"
    }

    get isEn() {
        return getLang() === "en";
    }

    @Provide()
    private reportDic: any = {
        chartOptions: [
            { label: this.$t("report.create.dialog.chart[7]"), code: 8 },
            { label: this.$t("report.create.dialog.chart[6]"), code: 7 },
            { label: this.$t("report.create.dialog.chart[5]"), code: 6 },
            { label: this.$t("report.create.dialog.chart[4]"), code: 5 },
            { label: this.$t("report.create.dialog.chart[3]"), code: 4 },
            { label: this.$t("report.create.dialog.chart[2]"), code: 3 },
            { label: this.$t("report.create.dialog.chart[1]"), code: 2 },
            { label: this.$t("report.create.dialog.chart[0]"), code: 1 }
        ],
        dataOptions: [
            { label: this.$t("report.create.dialog.data[1]"), code: 2 },
            { label: this.$t("report.create.dialog.data[0]"), code: 1 }
        ]
    }

    get productOptions() {
        let options: any[] = []
        options = ProductModule.allProductOptions;
        if (window.__POWERED_BY_QIANKUN__) {
            options = StatisticsModule.allProductOptions.map(item => {
                return {
                    label: item.product_cname,
                    value: item.product_code
                }
            })
        }
        return (options as any).filter((option: any) => {
            if(window.__POWERED_BY_QIANKUN__) {
                // 仅支持安全与加速（020）
                return ["020"].includes(option.value)
            } else {
                // 仅支持静态加速（001）、下载加速（003）、视频点播加速（004）、CDN加速（008）、全站加速（006）
                return ["001", "003", "004", "006", "008"].includes(option.value)
            }
        })
    }

    private mounted() {
        this.isUpdate && this.getInfo()
    }

    /**
     * 获取配置信息
     */
    private async getInfo() {
        try {
            this.loading = true;
            const data: any = await this.$ctFetch(reportUrl.getReportDetail, {
                method: "GET",
                transferType: "json",
                data: {
                    reportTemplateId: this.$route.query.id
                },
            });
            this.form.name = data.reportName;
            this.form.frequency = data.sendFrequency;
            this.form.time = [new Date(data.oneTimeStartTime), new Date(data.oneTimeEndTime)];
            this.form.sendType = data.sendType;
            this.dataListType = this.form.sendType;
            this.form.sendTime = new Date(data.thisDate);
            this.form.email = data.email;
            this.form.data = [];
            let key = ""
            this.form.sendType === 1 && (key = "chartData")
            this.form.sendType === 2 && (key = "chartList")
            // pdf
            if (key === "chartData") {
                Array.isArray(data[key].productDomain) && data[key].productDomain.forEach((item: any) => {
                    this.form.dataList.push({
                        productCode: item.code,
                        domain: [],
                        chart: item.list.map((listItem: any) => listItem.id)
                    });
                });
                Array.isArray(data[key].domain) && data[key].domain.forEach((item: any) => {
                    this.form.dataList.push({
                        productCode: "",
                        domain: [item.domainName],
                        chart: item.list.map((listItem: any) => listItem.id),
                    });
                });
            }
            // excel
            if (key === "chartList" && Array.isArray(data[key])) {
                data[key].forEach((item: any) => {
                    this.form.dataList.push({
                        productCode: item.code,
                        domain: item.domainList,
                        region: item.abroad,
                        data: item.list.map((listItem: any) => listItem.id)
                    });
                });
            }
        } catch(e) {
            this.$errorHandler(e);
        } finally {
            this.loading = false;
        }
    }

    /**
     * 获取产品名称
     */
    private getProductName(code: string) {
        return this.productOptions.find((option: any) => {
            return option.value === code;
        })?.label || code;
    }

    /**
     * 发送形式变更回调
     */
    private sendTypeChange(val: number) {
        if (this.form.dataList.length) {
            this.$confirm(this.$t('report.create.tips[0]') as string, {
                title: `${this.$t("common.messageBox.title")}`,
                confirmButtonText: `${this.$t("common.dialog.submit")}`,
                cancelButtonText: `${this.$t("common.dialog.cancel")}`,
                type: "warning"
            }).then(() => {
                this.dataListType = val
                this.form.dataList = []
            }).catch(err => {
                this.form.sendType = this.dataListType
            })
        } else {
            this.dataListType = val
            this.form.dataList = []
        }
    }

    /**
     * 消减表格已选项
     */
    private spliceSpan(data: any, index: number) {
        data.splice(index, 1)
    }

    /**
     * 删除配置
     */
    private handleDelete(index: number) {
        this.form.dataList.splice(index, 1)
    }

    /**
     * 添加配置
     */
    private addData(data: any[]) {
        this.form.dataList.push(...data);
        (this.$refs?.form as any).clearValidate();
    }

    /**
     * 打开弹窗
     */
    private showDialog() {
        const dialog = this.$refs.addDialog as any
        dialog && (dialog.dialogVisible = true)
    }

    /**
     * 提交数据
     */
    private async submit() {
        try {
            this.submitLoading = true
            const params: any = {
                reportName: this.form.name,
                sendFrequency: this.form.frequency,
                sendType: this.form.sendType,
                thisDate: timeFormat(new Date(this.form.sendTime).getTime()),
                email: this.form.email
            }
            if (this.isUpdate) {
                params.reportTemplateId = this.$route.query.id;
            }
            if (params.sendFrequency === 1) {
                params.oneTimeStartTime = timeFormat(new Date(this.form.time[0]).getTime());
                params.oneTimeEndTime = timeFormat(new Date(this.form.time[1]).getTime());
            }
            // pdf
            if (this.form.sendType === 1) {
                params.chartData = {
                    domain: [],
                    productDomain: []
                }
                this.form.dataList.forEach((item: any) => {
                    if (item.domain.length) {
                        params.chartData.domain.push({
                            domainName: item.domain[0],
                            list: item.chart.map((chartItem: number) => {
                                return { id: chartItem };
                            })
                        })
                    } else {
                        params.chartData.productDomain.push({
                            code: item.productCode,
                            list: item.chart.map((chartItem: number) => {
                                return { id: chartItem };
                            })
                        })
                    }
                })
            }
            // excel
            if (this.form.sendType === 2) {
                params.chartList = []
                this.form.dataList.forEach((item: any) => {
                    params.chartList.push({
                        code: item.productCode,
                        domainList: item.domain,
                        abroad: item.region,
                        list: item.data.map((dataItem: number) => {
                            return { id: dataItem };
                        })
                    })
                })
            }
            await this.$ctFetch(reportUrl.updateReport, {
                method: "POST",
                body: { data: params },
            });
            this.$router.push({ name: "reportManagement" });
        } catch(e) {
            this.$errorHandler(e);
        } finally {
            this.submitLoading = false;
        }
    }

    /**
     * 提交配置
     */
    private handleSubmit() {
        (this.$refs.form as Form)?.validate(async (valid: boolean) => {
            if (valid) {
                const now = new Date();
                if (this.form.sendTime <= now) {
                    const delay10min = new Date(now.getTime() + 10 * 60 * 1000);
                    this.$confirm(this.$t("report.create.confirmInfo[0]", { time: timeFormat(delay10min.getTime()) }) as string, {
                        title: `${this.$t("common.messageBox.title")}`,
                        confirmButtonText: `${this.$t("common.dialog.submit")}`,
                        cancelButtonText: `${this.$t("common.dialog.cancel")}`,
                    })
                    .then(() => {
                        this.form.sendTime = delay10min;
                        this.submit();
                    })
                    .catch(err => err)
                } else {
                    this.submit();
                }
            }
        });
    }

    /**
     * 返回
     */
    private cancel() {
        this.$router.push({
            name: "reportManagement"
        })
    }

    /**
     * 校验邮箱
     * @param rule 规则
     * @param value 校验值
     * @param callback 校验回调
     */
    private checkEmail(rule: string, value: string, callback: Function) {
        if (!value) {
            return callback();
        }
        if (
            value
            .split(";")
            // eslint-disable-next-line no-useless-escape
            .every(email => new RegExp(/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/, "i").test(email))
        ) {
            return callback();
        }
        return callback(new Error(this.$t("report.create.validInfo[5]") as string));
    }

    /**
     * 覆盖el-date-picker的方法以便实现自定义结束时间
     */
    private handlePickerFocus(instance: any) {
        // 覆盖el-date-picker的方法以便实现自定义结束时间
        this.$nextTick(() => {
            const picker = instance.picker;
            const originalMethod = (this.originalPickerChangeMethod = picker.handleRangePick);
            picker.handleRangePick = (...args: any[]) => {
                const maxDate = args[0].maxDate;
                if (+get0(new Date()) === +get0(maxDate)) {
                    picker.defaultTime = [
                        picker.defaultTime[0],
                        dayjs(new Date()).format("HH:mm:ss")
                    ];
                } else {
                    picker.defaultTime = ["00:00:00", "23:59:59"];
                }
                originalMethod(...args);
            };
        });
    }

    /**
     * 恢复el-date-picker的原生方法
     */
    private handlePickerBlur(instance: any) {
        // 覆盖el-date-picker的方法以便实现自定义结束时间
        this.$nextTick(() => {
            const picker = instance.picker;
            picker.handleRangePick = this.originalPickerChangeMethod;
        });
    }


    @Watch('form.time')
    private timeChange(val: any[]) {
        this.rangePickerOptions.reset()
    }
}
</script>

<style lang="scss" scoped>
    .report-create-container {
        ::v-deep .el-scrollbar {
            margin-bottom: 0 !important;
        }
        .report-create-wrap {
            .detail-title {
                display: flex;
                margin: 0 0 20px;
                font-weight: 600;
                font-size: 12px;
                align-items: center;
                &::before {
                    content: "";
                    display: inline-block;
                    width: 4px;
                    height: 14px;
                    background: #3D73F5;
                    margin-right: 16px;
                }
            }
            ::v-deep .el-form-item__content {
                & > .el-textarea,
                & > .el-input,
                & > .el-select,
                & > .el-date-picker {
                    width: 400px;
                }
                .el-range-input {
                    background-color: transparent;
                }
            }
            .el-table + .table-append {
                text-align: center;
                padding-bottom: 10px;
                border-bottom: 1px solid #e2e5ed;
            }
            .table-span {
                display: inline-block;
                .el-button {
                    margin-right: 14px;
                }
            }
        }
        .footer-content {
            display: flex;
            justify-content: flex-end;
        }
        .fixed-append {
            height: 85px;
        }
    }
</style>
<style lang="scss">
    .aocdn-ignore-report-create-date-picker {
        > .el-picker-panel__footer .el-picker-panel__link-btn.el-button--text {
            display: none;
        }
    }
</style>
