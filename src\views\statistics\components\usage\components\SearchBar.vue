<template>
    <section class="statistics-search-bar search-bar">
        <search-bar-row v-if="useProduct || useDomain || useMultipleDomain || useIsp || useArea">
            <label slot="label" class="search-label">{{ $t("statistics.common.searchLabel1") }}</label>
            <el-select
                v-if="useProduct"
                v-model="product"
                multiple
                filterable
                collapse-tags
                :placeholder="$t('statistics.common.searchPlaceholder[0]')"
                key="product"
            >
                <el-option
                    v-for="(item, index) in productOptions"
                    v-show="item.value !== '190'"
                    :key="index"
                    :label="$t(item.label)"
                    :value="item.value"
                />
            </el-select>
            <el-select
                v-if="useDcdnProduct || useDcdnProductOther"
                v-model="dcdnProduct"
                filterable
                clearable
                collapse-tags
                :placeholder="$t('statistics.common.searchPlaceholder[0]')"
                key="dcdnProduct"
            >
                <el-option
                    v-for="(item, index) in productOptions"
                    v-show="item.value !== '190'"
                    :key="index"
                    :label="$t(item.label)"
                    :value="item.value"
                />
            </el-select>
            <lable-select v-if="useLabel" v-model="selectedLabel" :labelList="labelList" />
            <el-select
                v-if="useDomain"
                v-model="domain"
                filterable
                :placeholder="$t('statistics.common.searchPlaceholder[1]')"
                key="domain"
            >
                <el-option :label="$t('statistics.common.domainSelectOption')" value="all" />
                <el-option
                    v-for="item in domainOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                />
            </el-select>
            <domain-select
                v-show="useMultipleDomain"
                v-model="multipleDomain"
                :domainOptions="domainOptions"
                :multiple="true"
                key="multipleDomain"
                :loading="domainOptionsLoading"
            />
            <el-select
                v-if="useIsp && !isCtclouds"
                v-model="isp"
                multiple
                filterable
                collapse-tags
                class="isp-select-wrapper"
                :placeholder="$t('statistics.common.searchPlaceholder[2]')"
                key="isp"
            >
                <el-option
                    v-for="(item, index) in filteredIspOptions"
                    :key="index"
                    :label="isEn ? item.isp_enname : item.isp_cnname"
                    :value="item.isp_code"
                />
            </el-select>
            <!-- oversea 字段没用 国际站不展示输入框，国际站的其他的子项会被改写为其他 -->
            <area-select
                v-if="useArea"
                :multiple="true"
                v-model="area"
                @change="handleAreaChange"
                :area-list="areaOptions"
                :is-global="isGlobal"
                :enable-city="enableCity"
                key="area"
                :oversea="oversea"
            />
            <el-select
                v-if="useAbroad && isPvUvchartType"
                v-model="abroad"
                clearable
                :placeholder="$t('statistics.common.searchPlaceholder.8')"
                key="abroad"
            >
                <el-option
                    v-for="(item, index) in abroadOptions"
                    :key="index"
                    :label="item.label"
                    :value="item.value"
                />
            </el-select>
        </search-bar-row>
        <search-bar-row class="search-row ct-select-group" v-if="useTransportProtocol || useInternetProtocol">
            <label slot="label" class="search-label">{{ $t("statistics.common.searchLabel2") }}</label>
            <el-select
                v-if="useTransportProtocol"
                v-model="protocol"
                clearable
                filterable
                :placeholder="$t('statistics.common.searchPlaceholder[3]')"
                key="protocol"
            >
                <el-option
                    v-for="(item, index) in transportProtocolOptions"
                    :key="index"
                    :label="item.label"
                    :value="item.value"
                />
            </el-select>
            <el-select
                v-if="useInternetProtocol"
                v-model="ipProtocol"
                clearable
                filterable
                :placeholder="$t('statistics.common.searchPlaceholder[4]')"
                key="ipProtocol"
            >
                <el-option
                    v-for="(item, index) in internetProtocolOptions"
                    :key="index"
                    :label="item.label"
                    :value="item.value"
                />
            </el-select>
        </search-bar-row>
        <search-bar-row fullwidth class="search-row ct-select-group flex-start">
            <label slot="label" class="search-label" v-if="useTimePicker || useTimeCompare">
                {{ $t("statistics.common.searchLabel3") }}
            </label>
            <!-- 时间粒度 -->
            <el-select
                v-if="useTimeGranularity"
                v-model="interval"
                filterable
                :placeholder="$t('statistics.common.searchPlaceholder[7]')"
                key="interval"
            >
                <el-option
                    v-for="(item, index) in intervalOptions"
                    :key="index"
                    :label="item.label"
                    :value="item.value"
                />
            </el-select>
            <ct-time-picker
                v-if="useTimePicker && interval !== '1m'"
                v-show="!compareShow"
                v-model="timeRange"
                :current-period.sync="currentPeriod"
                :periodOptions="periodOptions"
                :maxDayBeforeNow="maxDayBeforeNow"
                style="margin-right: 8px"
                key="timePicker"
                size="medium"
            />
            <ct-hour-picker
                v-if="useTimePicker && interval === '1m'"
                v-show="!compareShow"
                v-model="hourRange"
                :current-period.sync="currentHourPeriod"
                :periodHourOptions="periodHourOptions"
                :maxDayBeforeNow="7"
                style="margin-right: 8px"
                key="hourPicker"
                size="medium"
            />

            <ct-time-compare
                ref="ctTimeCompare"
                v-if="useTimeCompare"
                :compareShow.sync="compareShow"
                v-model="timeRangeArr"
                :maxDayBeforeNow="interval !== '1m' ? maxDayBeforeNow : 7"
                key="timeCompare"
                size="medium"
            />

            <el-button type="primary" @click="() => beforeSearch(true)">{{
                $t("statistics.common.searchBtn")
            }}</el-button>
            <el-button class="reset-btn" @click="resetFilter">{{ $t("common.search.reset") }}</el-button>

            <div class="search-download" v-if="useDownload">
                <el-tooltip
                    class="item"
                    effect="dark"
                    :content="$t('statistics.common.searchDownloadContent')"
                    placement="right"
                >
                    <i class="el-icon-download download-icon" @click="download"></i>
                </el-tooltip>
            </div>
        </search-bar-row>
    </section>
</template>

<script lang="ts">
import { Component, Prop, Watch, Vue } from "vue-property-decorator";
import { DomainModule } from "@/store/modules/domain";
import { ProductModule } from "@/store/modules/ncdn/nproduct";
import { StatisticsModule } from "@/store/modules/statistics";
import { LabelModule } from "@/store/modules/label";
import { SearchParams } from "@/types/statistics/usage";
import { DomainActionEnum, DomainActionKey, getDomainAction, ProductCodeEnum } from "@/config/map";
import AreaSelect from "@/components/areasSelect/index.vue";
import DomainSelect from "@/components/domainsSelect/index.vue";
import LableSelect from "@/components/lableSelect/index.vue";
import { busiType } from "@/config/map";
import { nUserModule } from "@/store/modules/user";
import CtHourPicker from "@/components/ctHourPicker/index.vue";
import CtTimePicker from "@/components/ctTimePicker/index.vue";
import CtTimeCompare from "@/components/ctTimeCompare/index.vue";
import SearchBarRow from "../../searchBarRow.vue";
import { getAm0 as get0, getAm0 } from "@/utils";
import { CurrentMenu } from "../../statistics";
import { MainMenu } from "@/views/statistics/entryMixin";
import { cloneDeep } from "lodash-es";
import { LabelItem } from "@/store/types";

@Component({
    name: "SearchBar",
    components: {
        AreaSelect,
        DomainSelect,
        LableSelect,
        CtHourPicker,
        SearchBarRow,
        CtTimePicker,
        CtTimeCompare,
    },
})
export default class SearchBar extends Vue {
    // 所需查询条件的标识，使用 "domain, timepicker" 的方式传入，需要哪个就配置哪个
    // ps：根据业务需要，域名、时间都是必须要的
    @Prop({ default: "domain, timePicker", type: String }) private paramFlag!: string;
    // time-picker 的快捷配置项
    @Prop({ default: () => ["0", "1", "7", "30", "-1"], type: Array }) periodOptions!: string[];
    @Prop({ default: () => ["1", "2", "3", "-1"], type: Array }) periodHourOptions!: string[];
    // 是否使用自动发起请求，默认不自动发起
    @Prop({ default: false, type: Boolean }) private autoFetch!: boolean;
    @Prop({ default: false }) oversea!: boolean;
    @Prop({ default: false, type: Boolean }) private childPanelMounted!: boolean;
    @Prop({ default: () => ({ main: "", sub: "" }), type: Object })
    private currentMenu!: CurrentMenu<MainMenu>;

    // 从 paramFlag 中获取各条件是否启用
    // 说明：使用 watch 可以阻断不必要的计算属性监听
    useProduct = false; // 1、全部产品（默认）
    useDcdnProduct = false; // 3、全站加速加速类型
    useDcdnProductOther = false; // 4、全站加速排除上传加速
    useDomain = false; // 默认是多选
    useMultipleDomain = false; // 单选和多选分开2个组件，便于分开存储数据
    useTimeGranularity = false; // 时间粒度
    useTimeRangeHalfYear = false; // 时间范围最大是否为半年，否则默认一年
    stashedSearchParams: any = null; // 暂存的查询参数

    @Watch("paramFlag", { immediate: true })
    onParamFlag(flag: string) {
        // 由于 product 可以匹配多个，所以选择 product,
        this.useProduct = flag.includes("product,");
        this.useDcdnProduct = flag.includes("dcdnProduct,");
        this.useDcdnProductOther = flag.includes("dcdnProductOther");
        this.useDomain = flag.includes("domain");
        this.useMultipleDomain = flag.includes("multipleDomain");
        this.useTimeGranularity = flag.includes("timeGranularity");
        this.useTimeRangeHalfYear = flag.includes("timeRangeHalfYear");

        // 如果当前未使用 timeCompare 且使用了 timePicker，需要恢复 timePicker 的展示
        if (!flag.includes("timeCompare") && flag.includes("timePicker")) {
            this.compareShow = false;
        }
        this.$set(this, "interval", "5m");
    }
    get isCtclouds() {
        return nUserModule.isCtclouds;
    }
    get lang() {
        return nUserModule.lang;
    }

    get useLabel() {
        return this.paramFlag.includes("label");
    }

    get useIsp() {
        return this.paramFlag.includes("isp");
    }

    get useArea() {
        return this.paramFlag.includes("area");
    }
    get useAbroad() {
        return this.paramFlag.includes("abroad");
    }

    get useTimePicker() {
        return this.paramFlag.includes("timePicker");
    }

    get useTimeCompare() {
        return this.paramFlag.includes("timeCompare");
    }

    get isEn() {
        return nUserModule.lang === "en";
    }

    get useDownload() {
        return this.paramFlag.includes("download");
    }
    get useTransportProtocol() {
        return this.paramFlag.includes("protocol");
    }
    get useInternetProtocol() {
        return this.paramFlag.includes("ipProtocol");
    }
    // 用来判断是否是：下载速度tab
    get useOnly5Min() {
        return this.paramFlag.includes("isOnlyShow5Min");
    }

    get maxDayBeforeNow() {
        return this.useTimeRangeHalfYear ? 180 : 365;
    }

    get enableCity() {
        return (
            this.isGlobal &&
            this.useArea &&
            ["BandwidthFlow", "BandwidthFlowWhole"].includes(this.currentMenu.sub || "")
        );
    }

    // 协议类型选项
    transportProtocolOptions = [
        { label: "http", value: 0 },
        { label: "https", value: 1 },
        { label: "quic", value: 6 },
    ];
    // 协议版本选项
    internetProtocolOptions = [
        { label: "ipv4", value: 0 },
        { label: "ipv6", value: 1 },
    ];

    // 三种产品结果
    product: string[] = []; // 选择的加速类型
    //全站加速产品
    dcdnProduct = "";
    selectedLabel: string[] = []; // 默认标签不选
    domain = "all"; // 选择框选中的域名（单选）
    multipleDomain = []; // 选择框选中的域名（多选）
    isp: string[] = []; // 运营商
    area: any = {
        province: [],
        continent: [],
        continentRegion: [],
        city: [],
    }; // 选择的范围
    timeRange: null | Date[] = null; // 时间
    hourRange: null | Date[] = null; // 时间-小时
    currentPeriod = "0"; // 当前 ct-time-picker 选择的时间段
    currentHourPeriod = "1";
    timeRangeArr = [null, null]; // 对比时间
    protocol = ""; // 选择的协议类型
    ipProtocol = ""; // 选择的协议版本
    abroad = ""; // 选择的区域

    compareShow = false; // 是否展示时间对比
    isDcdn = false;
    initialized = false; // 首次请求是否已经触发
    interval = "5m"; // 时间粒度
    hasCitySelect = false;

    // 区域选项
    get abroadOptions() {
        const list = [
            { label: this.$t("statistics.common.abroadOptions.0"), value: 0 },
            { label: this.$t("statistics.common.abroadOptions.1"), value: 1 },
            { label: this.$t("statistics.common.abroadOptions.2"), value: 201 },
            { label: this.$t("statistics.common.abroadOptions.3"), value: 202 },
            { label: this.$t("statistics.common.abroadOptions.4"), value: 203 },
            { label: this.$t("statistics.common.abroadOptions.5"), value: 204 },
            { label: this.$t("statistics.common.abroadOptions.6"), value: 205 },
            { label: this.$t("statistics.common.abroadOptions.7"), value: 206 },
            { label: this.$t("statistics.common.abroadOptions.8"), value: 207 },
        ];

        return list.filter(region => {
            // A1控制台需先隐藏海外分区域计费功能，暂不对外开放
            if (window.__POWERED_BY_QIANKUN__) return region.value < 200;
            else return true;
        });
    }

    get domainAction() {
        let action = DomainActionEnum.Data;

        if (nUserModule.isFcdnCtyunCtclouds) {
            const sub = this.currentMenu.sub;
            action = getDomainAction(
                (!sub?.endsWith("Whole") && this.isDcdn ? `${sub}Whole` : sub) as DomainActionKey
            );
        }

        return action;
    }

    // 全部的域名列表
    get domainList() {
        return DomainModule[this.domainAction].nativeList.filter(domain => {
            return this.productOptions.some(product => product.value === domain.productCode);
        });
    }

    get domainOptionsLoading() {
        return DomainModule[this.domainAction].loading;
    }

    get productOptions() {
        const { useProduct, useDcdnProduct, useDcdnProductOther } = this;
        const options = ProductModule.allProductOptions;
        if (window.__POWERED_BY_QIANKUN__) {
            return StatisticsModule.allProductOptions.map(item => {
                return {
                    label: item.product_cname,
                    value: item.product_code,
                };
            });
        }

        const list = options.filter(opt => {
            if (!opt.label || !opt.value) return false;

            const isDcdnOptions =
                opt.value === ProductCodeEnum.Upload ||
                opt.value === ProductCodeEnum.Whole ||
                opt.value === ProductCodeEnum.Socket;
            // 筛选全站加速和普通用量分析的产品下拉选择
            if (useProduct) return !isDcdnOptions;
            if (useDcdnProduct) return isDcdnOptions;
            if (useDcdnProductOther) {
                //回源统计不含有上传加速产品
                if (this.dcdnProduct === "104") this.dcdnProduct = "";
                return isDcdnOptions && opt.value !== ProductCodeEnum.Upload;
            }
        });
        return list;
    }

    // 标签列表
    get labelList() {
        if (nUserModule.isFcdnCtyunCtclouds) {
            return (LabelModule as any)[this.domainAction].labelList;
        }

        return LabelModule.labelList;
    }

    get onLabelLoading() {
        return LabelModule.loading;
    }

    // 域名和标签的绑定关系
    get domainMapLabel() {
        return LabelModule.domainMapLabelJr;
    }

    get productAll() {
        return (this.productOptions || []).map(opt => opt.value);
    }
    get isPvUvchartType() {
        return StatisticsModule.isPvUvchartType === "PV";
    }
    get filteredIspOptions() {
        // ’其他‘选项默认放到最后
        const ispOptions = StatisticsModule.filteredIspOptions;
        const others = ispOptions.filter(isp => +isp.isp_code === 100);
        const exceptOthers = ispOptions.filter(isp => +isp.isp_code !== 100);
        return [...exceptOthers, ...others];
    }

    get areaOptions() {
        return StatisticsModule.areaOptions;
    }

    get isGlobal() {
        return StatisticsModule.isGlobal;
    }

    get childAccount() {
        return StatisticsModule.childAccount;
    }
    get domainCountLimit() {
        return StatisticsModule.domainCountLimit;
    }

    // 根据加速类型筛选展示域名列表
    get domainOptions() {
        const { isDcdn, dcdnProduct, product, selectedLabel, domainMapLabel } = this;
        return (
            this.domainList
                // 过滤逻辑：如果产品未选择，则默认所有域名；若产品有选择，则根据产品进行过滤
                .filter(item => {
                    if (!product?.length && !dcdnProduct) return true;
                    // 当加速类型不为空，则返回选择的加速类型对应的域名
                    if (!isDcdn) return product.includes(item.productCode);
                    else return dcdnProduct === item.productCode;
                })
                // 过滤逻辑2：label，全选则不过滤，非全选则查看域名是否在绑定了这些标签
                .filter(item => {
                    // 没有已选标签就不过滤了
                    if (selectedLabel.length === 0) {
                        return true;
                    } else {
                        return selectedLabel.some(labelId => domainMapLabel[item.domain]?.includes(labelId));
                    }
                })
                .map(item => ({
                    // 从接口原生数据中获取 options
                    label: item.label,
                    value: item.domain,
                }))
        );
    }

    // 已选的域名列表（在此处理单选/多选差异）
    get domainParams() {
        const { useDomain, useMultipleDomain } = this;
        const { domain, multipleDomain } = this;
        const allDomains = (this.domainOptions || []).map(d => d.value);
        if (this.childAccount) {
            if (useDomain) {
                // 单选
                return domain === "all" ? allDomains : [domain];
            } else if (useMultipleDomain) {
                // 若客户未选择域名，且可选择的域名数量大于等于100时，则不再传递域名给后端
                if (multipleDomain.length > 0) {
                    return multipleDomain;
                } else {
                    if (allDomains.length <= this.domainCountLimit) {
                        return allDomains;
                    } else {
                        return [];
                    }
                }
            }
        } else {
            if (useDomain) {
                // 单选
                return domain === "all" ? allDomains : [domain];
            } else if (useMultipleDomain) {
                // 全选时，不传递具体域名
                if (multipleDomain.length === 0 || multipleDomain.length === allDomains.length) {
                    return [];
                }
                // 若客户未选择域名，且可选择的域名数量大于等于100时，则不再传递域名给后端
                if (multipleDomain.length > 0) {
                    return multipleDomain;
                } else {
                    if (allDomains.length < this.domainCountLimit) {
                        return allDomains;
                    } else {
                        return [];
                    }
                }
            }
        }
        return [];
    }

    get canRequest() {
        return (
            this.initialized &&
            this.childPanelMounted &&
            // ctiam环境需要等待标签加载完成
            (nUserModule.isFcdnCtyunCtclouds
                ? !this.onLabelLoading && !DomainModule[this.domainAction].loading
                : true)
        );
    }

    // 基础的请求参数
    get baseSearchParams() {
        const {
            useProduct,
            useDcdnProduct,
            useDcdnProductOther,
            useDomain,
            useMultipleDomain,
            useIsp,
            useArea,
            useTransportProtocol,
            useInternetProtocol,
            useAbroad,
            isPvUvchartType,
            useTimeGranularity,
        } = this;
        const {
            product,
            dcdnProduct,
            domainParams,
            isp,
            area,
            protocol,
            ipProtocol,
            abroad,
            interval,
        } = this;

        const params: SearchParams = {
            domainList: [],
            startTime: 0,
            endTime: 0,
        };

        if (useProduct) params.product = [...product];
        //全站加速的加速类型字段
        if (useDcdnProduct || useDcdnProductOther) {
            // product统一传一级产品编码['006'](后端处理)
            params.product = dcdnProduct ? [dcdnProduct] : [];
            // websocket 特殊处理
            // 1、当产品未选择，或选择的是 006 全站加速时，不传
            // 2、当产品选择 104 上传加速，直传 [1]
            // 3、当产品选择 105 websocket 加速时，默认不传，各业务组件再按需重新定义（目前只有带宽流量需要细分，请求数需要传2，后端处理成全部）
            params.busiType = dcdnProduct === ProductCodeEnum.Upload ? [busiType[dcdnProduct]] : [];
        }
        if (useDomain || useMultipleDomain) params.domainList = [...domainParams];
        if (useIsp) params.isp = [...isp];
        if (useArea) {
            // tag-area更改标注
            params.province = this.area.province;
            params.continent_code = this.area.continent;
            params.continent_region_code = this.area.continentRegion;
            if (this.hasCitySelect && this.enableCity) {
                params.city = this.area.city
                    .map((itm: string) => (itm.includes(",") ? itm.split(",") : itm))
                    .flat();
            }
            // 查询全部地区(-1)则默认不传province参数
            if (params.province?.includes("-1")) {
                params.province = [];
            }
        }
        if (useAbroad && isPvUvchartType) {
            params.abroad = abroad;
        }
        if (useTransportProtocol) {
            params.protocol = protocol;
        }
        if (useInternetProtocol) {
            params.ipProtocol = ipProtocol;
        }
        // 时间粒度
        if (useTimeGranularity) {
            params.interval = interval;
        }
        // 不传递域名参数domainList时，需要传递product
        if (!(params.product && params.product.length > 0)) {
            const isFcdnCtyunCtclouds = nUserModule.isFcdnCtyunCtclouds;
            if (!(params.domainList.length > 0)) {
                params.product = (this.productOptions || []).map(item => {
                    return item.value;
                });
            } else if (isFcdnCtyunCtclouds) {
                // fcdn ctyun（国内+国际） 必传 product
                params.product = this.domainList
                    .filter(itm => params.domainList.includes(itm.domain))
                    .map(itm => itm.productCode);
                params.product = [...new Set(params.product)];
            }
        }
        return params;
    }

    // 请求参数1
    get searchParams1() {
        const { useTimePicker, useTimeCompare } = this;
        const { baseSearchParams, timeRange, timeRangeArr, compareShow, hourRange } = this;

        if (!useTimePicker && !useTimeCompare) return baseSearchParams;

        // 根据使用情况组装数据
        const time = compareShow ? timeRangeArr[0] : this.interval !== "1m" ? timeRange : hourRange;
        const startTime = time ? time[0] : 0;
        const endTime = time ? time[1] : 0;

        return {
            ...baseSearchParams,
            startTime: Math.floor(+startTime / 1000),
            endTime: Math.floor(+endTime / 1000),
        };
    }

    // 请求参数2
    get searchParams2() {
        if (!this.compareShow) return null;

        const { baseSearchParams, timeRangeArr } = this;

        const time = timeRangeArr[1];
        const startTime = time ? Math.floor(time[0] / 1000) : 0;
        const endTime = time ? Math.floor(time[1] / 1000) : 0;

        return {
            ...baseSearchParams,
            startTime,
            endTime,
        };
    }

    get intervalOptions() {
        if (this.useOnly5Min) {
            return [{ label: `${this.$t("statistics.common.timeGrain[0]")}`, value: "5m" }];
        }
        const intervalOptions = [
            { label: `${this.$t("statistics.common.timeGrain[3]")}`, value: "1m" },
            { label: `${this.$t("statistics.common.timeGrain[0]")}`, value: "5m" },
            { label: `${this.$t("statistics.common.timeGrain[1]")}`, value: "1h" },
        ];

        // 如果选择了城市，则不显示1分钟粒度
        if (this.hasCitySelect && this.enableCity) {
            intervalOptions.shift();
        }

        const intervalOptionsWith24h = [
            ...intervalOptions,
            { label: `${this.$t("statistics.common.timeGrain[2]")}`, value: "24h" },
        ];
        const t = 4 * 24 * 60 * 60 * 1000 - 1000;

        // 计算时间粒度, 如果时间跨度大于等于4天，则显示24h粒度
        const calculateInterval = (start: Date, end: Date) => {
            if (!start || !end) return intervalOptions;
            if (+end - +start >= t) {
                this.$set(this, "interval", "1h");
                return intervalOptionsWith24h;
            } else if (+end - +start < t) {
                this.$set(this, "interval", "5m");
                return intervalOptions;
            }
        };

        if (this.compareShow) {
            const timeRangeArr: [Date, Date][] = this.timeRangeArr as any;
            if (!timeRangeArr || !timeRangeArr[0] || !timeRangeArr[0].length) return intervalOptions;

            const [start, end] = timeRangeArr[0] as [Date, Date];
            return calculateInterval(start, end);
        }

        if (this.currentPeriod === "-1") {
            const [start, end] = this.timeRange as [Date, Date];
            return calculateInterval(start, end);
        } else {
            if (this.currentPeriod === "0" || this.currentPeriod === "1") {
                this.$set(this, "interval", "5m");
                return intervalOptions;
            } else if (this.currentPeriod === "7" || this.currentPeriod === "30") {
                this.$set(this, "interval", "1h");
                return intervalOptionsWith24h;
            }
        }

        return intervalOptions;
    }

    @Watch("currentMenu", { immediate: true, deep: true })
    async onCurrentMenuChange() {
        StatisticsModule.SET_CURRENT_ACTION(this.domainAction);
        if (nUserModule.isFcdnCtyunCtclouds) {
            this.initialized = false;
            // 暂存查询参数
            this.stashedSearchParams = this.stashedSearchParams
                ? cloneDeep(this.stashedSearchParams)
                : cloneDeep({
                      label: this.selectedLabel,
                      multipleDomain: this.multipleDomain,
                  });

            await Promise.all([
                DomainModule.GetDomainList({ action: this.domainAction, shouldCheckCache: true }),
                LabelModule.GetCtiamLabelList(this.domainAction),
            ]);
        }
    }

    @Watch("canRequest", { immediate: true })
    onCanRequestChange(val: boolean) {
        if (!val) return;

        this.$nextTick(() => {
            if (nUserModule.isFcdnCtyunCtclouds) {
                if (this.stashedSearchParams?.label) {
                    this.selectedLabel = (this.labelList as LabelItem[])
                        .map(item => item.children)
                        .flat()
                        .filter(item => this.stashedSearchParams.label.includes(item.value))
                        .map(itm => itm.value) as any;
                }
                this.$nextTick(() => {
                    if (this.stashedSearchParams?.multipleDomain) {
                        this.multipleDomain = this.domainOptions
                            .filter(item => this.stashedSearchParams.multipleDomain.includes(item.value))
                            .map(item => item.value) as any;
                    }
                    // 清空暂存的查询参数
                    this.stashedSearchParams = null;
                    this.beforeSearch();
                });
            } else {
                this.beforeSearch();
            }
        });
    }

    @Watch("domainList")
    onDomainListChange(newArr: [], oldArr: []) {
        // domain 数组长度变化，或者任何一个内容不同，则重新发起请求
        const isSearch =
            (newArr.length > 0 && newArr.length !== oldArr.length) ||
            newArr.some((val, idx) => val !== oldArr[idx]);
        // 变化时进行请求，并首次不在此请求
        if (this.autoFetch && isSearch && this.domainParams.length !== 0) {
            this.beforeSearch();
        }
    }

    @Watch("domainParams", { immediate: true })
    onDomainParamsChange(newArr: [], oldArr: []) {
        // 首次进入则进行请求
        if (
            (newArr.length !== 0 && (!oldArr || oldArr.length === 0)) ||
            (this.domainOptions || []).map(d => d.value).length > 0
        ) {
            if (this.initialized === true) {
                return;
            }
            this.initialized = true;
            this.$emit("initialized");
        }
    }

    @Watch("$route", { immediate: true })
    onRouteChange() {
        // 判断普通用量分析还是全站加速用量分析
        this.isDcdn = this.$route.name === "statistics.dcdn";
    }

    @Watch("product")
    onProductChange() {
        // 切换产品时清空域名
        this.multipleDomain = [];
        this.domain = "all";
    }

    // 查询方法
    // 子账号查询时，有以下四种情况（主账号不变，和原有逻辑保持一致）
    // 1、未做选择时超过100，触发查询需要提示
    // 2、已选择超过100 ，触发查询需要提示
    // 3、不管总域名数有没有超过100，选择的域名数小于等于100 ，需要传参，但是不需要提示
    // 4、总域名数小于100，未选择，触发查询需要传参，但是不需要提示  传的是域名列表的所有域名
    async beforeSearch(triggerByUser = false) {
        // 当不是全选域名且选则的域名数量多于100时，拦截请求
        if (
            this.baseSearchParams.domainList.length > this.domainCountLimit &&
            this.baseSearchParams.domainList.length !== this.domainOptions.length &&
            !this.childAccount
        ) {
            this.$message.error(`${this.$t("common.searchBar.errMsg1", { count: this.domainCountLimit })}`);
            return;
        }
        // 选择了标签时，必须选择域名
        if (this.selectedLabel.length > 0 && this.multipleDomain.length === 0) {
            this.$message.error(`${this.$t("common.searchBar.errMsg2")}`);
            return;
        }
        // 没有可选域名，不允许查询
        if (!this.domainOptions.length && this.childAccount) {
            this.$message.error(`${this.$t("statistics.common.chart.errMsg[0]")}`);
            return;
        }

        if (this.selectedLabel.length > 0 && this.multipleDomain.length > this.domainCountLimit) {
            this.$message.error(`${this.$t("common.searchBar.errMsg3", { count: this.domainCountLimit })}`);
            return;
        }
        if (this.selectedLabel.length > 0) {
            this.baseSearchParams.domainList = this.multipleDomain;
        }
        // 选择使用了时间则需有数据
        if ((this.useTimePicker || this.useTimeCompare) && !this.searchParams1.startTime) {
            this.$message.error(`${this.$t("common.searchBar.errMsg4")}`);
            return;
        }
        // 未做选择时域名总数超过100，触发查询需要提示
        if (
            this.childAccount &&
            this.baseSearchParams.domainList.length === 0 &&
            this.domainOptions.length > this.domainCountLimit
        ) {
            this.$message.error(`${this.$t("common.searchBar.errMsg5", { count: this.domainCountLimit })}`);
            return;
        }
        // 选中的域名数超过100个，触发查询需要提示
        if (this.childAccount && this.baseSearchParams.domainList.length > this.domainCountLimit) {
            this.$message.error(`${this.$t("common.searchBar.errMsg5", { count: this.domainCountLimit })}`);
            return;
        }
        if (
            this.baseSearchParams.domainList.length > this.domainCountLimit &&
            this.baseSearchParams.domainList.length !== this.domainOptions.length &&
            this.childAccount
        ) {
            this.$message.error(`${this.$t("common.searchBar.errMsg5", { count: this.domainCountLimit })}`);
            return;
        }
        // 如果是1分钟粒度，开始时间与结束时间间隔不能超过3小时，请重新选择时间
        if (
            this.interval === "1m" &&
            ((!this.compareShow && this.searchParams1.endTime - this.searchParams1.startTime > 3 * 60 * 60) ||
                (this.compareShow &&
                    this.searchParams2!.endTime - this.searchParams2!.startTime > 3 * 60 * 60))
        ) {
            this.$message.error(`${this.$t("common.searchBar.errMsg6")}`);
            return;
        }
        // 查询前先去获取scale
        const scaleDomainList = this.baseSearchParams.domainList?.length
            ? this.baseSearchParams.domainList
            : this.domainOptions.map(item => item.value);

        // 广播给父组件去执行查询
        this.$emit("search", {
            searchParams1: this.searchParams1,
            searchParams2: this.searchParams2,
            triggerByUser,
            scaleDomainList,
        });
    }

    handleAreaChange({ city }: { city: string[] }) {
        this.hasCitySelect = !!city?.length;
    }

    resetFilter() {
        this.dcdnProduct = "";
        this.product = [];
        this.selectedLabel = [];
        this.domain = "all";
        this.multipleDomain = [];
        this.isp = [];
        this.area = {
            province: [],
            continent: [],
            continentRegion: [],
            city: [],
        };
        this.timeRange = [getAm0(new Date()), new Date()];
        this.hourRange = null;
        this.currentPeriod = "0";
        this.currentHourPeriod = "1";
        this.timeRangeArr = [] as any;
        this.protocol = "";
        this.ipProtocol = "";
        this.abroad = "";
        this.compareShow = false;
        this.interval = "5m";

        // 解决清空后，比对时间控件仍然保留上一次的结果
        if (this.$refs.ctTimeCompare) {
            const today0 = get0(new Date());
            (this.$refs.ctTimeCompare as any).timeRangeLocal1 = [new Date(+today0 - 24 * 60 * 60 * 1000), new Date(+today0 - 1)];
        }

        this.beforeSearch(true);
    }

    //表格下载
    download() {
        this.$emit("download");
    }
}
</script>

<style lang="scss" scoped>
.statistics-search-bar.search-bar {
    margin-bottom: 16px;
}

.ct-select-group {
    margin-top: 8px;
}

.code-label {
    margin-right: 16px;
}

.search-download {
    display: flex;
    align-items: center;
    height: 32px;

    .download-icon {
        margin-left: 8px;
        padding: 4px 0;

        &:hover {
            color: $color-master-hover;
            cursor: pointer;
        }
    }
}
.search-row .reset-btn {
    margin-left: 8px;
}
</style>
