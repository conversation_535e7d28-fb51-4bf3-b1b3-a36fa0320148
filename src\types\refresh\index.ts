export * from "@cdnplus/common/types/refresh"; // 这里面TaskStatus CreateParams还未被替换为内部变量，待后续处理后再将引入删除，删除时需要确认里面定义的变量都已经没有被引用

import { RefreshTypeEnum2, RefreshTypeMap2, TaskStatusMapSchedule } from "@/config/map";
export type RefreshType2 = typeof RefreshTypeEnum2[keyof typeof RefreshTypeEnum2];
export type TaskStatusSchedule = keyof typeof TaskStatusMapSchedule;

export interface RefreshItems {
    paging: PageItem;
    refreshItem: RefreshItem[];
}
export interface PageItem {
    page: number;
    per_page: number;
    total_page: number;
    total_record: number;
}

export interface RefreshItem {
    accountId: string;
    createTime: number | string;
    id: number;
    status: number;
    submitId: string;
    type: string;
    url: string;
}

export interface ScheduleItem {
    accountId: string;
    id: number;
    status: number;
    submitId: string;
    type: string;
    createTime: number | string;
    url: string;
    optype: string;
    tasktype: string;
    xxtime: string;
    xxx: string;
}

// 任务接口查询参数
export interface TaskSearchParams2 {
    workspaceId?: string; // 在 ctFetch 中配置了
    start_time: string;
    end_time: string;
    url?: string;
    domains?: string;
    type?: typeof RefreshTypeMap2[keyof typeof RefreshTypeMap2];
    status?: string; // 任务状态
    pageIndex?: number;
    pageSize?: number;
    refresh_type?: string;
}
