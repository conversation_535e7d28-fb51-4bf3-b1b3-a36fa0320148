<template>
    <div class="skeleton-page">
        <div class="guide-box guide-bg">
            <div class="banner-bg">
                <div class="guide-title">边缘函数</div>
                <div class="guide-tip">
                    边缘函数是天翼云的无服务器边缘函数计算平台。开发者无需关注服务部署区域、无需搭建<br />
                    和维护基础设施，只需要一键部署代码，就可在天翼云的边缘节点上即时生效，就近响应终端<br />
                    用户或设备的请求
                </div>
                <div class="flex-row-style button-box">
                    <el-button type="primary" class="blue-btn-style" @click="goTo"> 立即开通 </el-button>
                    <el-button plain type="primary" @click="handleViewPackageKnowMore">了解更多</el-button>
                </div>
            </div>
        </div>
        <template v-if="showDetails">
            <div class="common-wrapper">
                <div class="title-style">功能优势</div>
                <div class="flex-style">
                    <div v-for="(item, key) in functionData" :key="key" class="common-item">
                        <div class="flex-row-style label-box">
                            <i class="icon-style" :style="iconStyle(item)" />
                            <span>{{ item.label }}</span>
                        </div>
                        <div class="content-style">
                            {{ item.content }}
                        </div>
                    </div>
                </div>
            </div>
            <div class="common-wrapper">
                <div class="title-style">产品架构图</div>
                <div class="framework-box">
                    <div class="framework-bg"></div>
                </div>
            </div>
        </template>

        <order-confirm
            :visible.sync="confirmVisible"
            :cdnUrl="cdnUrl"
            :icdnUrl="icdnUrl"
            @close="confirmVisible = false"
        />
    </div>
</template>

<script>
import { ProductModule } from "@/store/modules/ncdn/nproduct";
import { MicroAppModule } from "@/store/modules/microApp";
import OrderConfirm from "./components/orderConfirm.vue";
// import commonMixin from "@/views/skeletonPage/commonMixin";
// import { handleApplyForTrial, handleViewPackageKnowMore } from "@/utils/utils";

export default {
    name: "index",
    // mixins: [commonMixin],
    props: {
        showDetails: {
            type: Boolean,
            default: true,
        },
    },
    components: { OrderConfirm },
    data() {
        return {
            functionData: [
                {
                    label: "易上手的开发者工具",
                    icon: "1.png",
                    content:
                        "为开发者提供CLI和Web IDE。CLI提供函数全生命周期管理功能，可以和开发者已有的 CI/CD 流程进行集成。Web IDE提供了一站式、全流程的函数管理与在线开发平台，帮助开发者专注于业务代码，提高开发效率。",
                },
                {
                    label: "丰富的编程语言生态",
                    icon: "2.png",
                    content:
                        "支持JavaScript ECMAScript6，支持 TypeScript。支持 Node.js 的生态，可直接使用 Node.js 部分代码库，扩展性强。",
                },
                {
                    label: "符合W3C标准的API",
                    icon: "3.png",
                    content:
                        "提供符合W3C标准的Service Worker API、Streams API、WebCrypto API，支持常用主流加解密算法，可以方便地实现边缘自定义访问控制、边缘内容改写和边缘内容生成等功能。",
                },
            ],
            confirmVisible: false,
        };
    },
    computed: {
        cdnUrl() {
            const { canUseList, list } = ProductModule;
            const cdn = this.getProductUrl("008", list, canUseList);
            return cdn ? `${MicroAppModule.edgeOrderUrl.cdnUrl}${cdn.resource_id}` : "";
        },
        icdnUrl() {
            const { canUseList, list } = ProductModule;
            const icdn = this.getProductUrl("006", list, canUseList);
            return icdn ? `${MicroAppModule.edgeOrderUrl.icdnUrl}${icdn.resource_id}` : "";
        },
    },
    mounted() {
        ProductModule.nGetProductInfo();
    },
    methods: {
        getProductUrl(code, list, canUseList) {
            if (canUseList.find(item => item.product_code === code)) {
                return list.find(item => item.product_code === code);
            }
            return null;
        },
        /**
         * 图标样式
         */
        iconStyle(item) {
            const img = item.icon;
            // eslint-disable-next-line @typescript-eslint/no-var-requires
            const imgSrc = require("./images/" + img);
            return {
                "background-image": `url(${imgSrc})`,
            };
        },
        /**
         * 了解更多
         */
        handleViewPackageKnowMore() {
            this.$docHelp("https://www.ctyun.cn/document/10006847/10631217");
        },
        /**
         * 引导订购页，点【立即订购】增加判断主产品是否在服务中
         *  1）客户账号只开通全站加速，未开通CDN加速，跳转到全站加速增配边缘函数的地址。
         *  2）客户账号只开通CDN加速，未开通全站加速，跳转到CDN加速增配边缘函数的地址。
         *  3）客户账号CDN加速和全站加速都开通了，弹出提示框可选择【增配CDN加速边缘函数】和【增配全站加速边缘函数】，根据选择进入对应的订购页。
         */
        goTo() {
            if ((this.cdnUrl && this.icdnUrl) || !(this.cdnUrl || this.icdnUrl)) {
                this.confirmVisible = true;
            } else if (this.cdnUrl || this.icdnUrl) {
                window.open(this.cdnUrl || this.icdnUrl);
            }
        },
    },
};
</script>

<style scoped lang="scss">
@import "../common.scss";

.framework-bg {
    width: 810px;
    min-height: 402px;
    background: url("./images/framework-bg.png") 0 0 no-repeat;
    background-size: contain;
}

.guide-bg {
    background: url("./images/banner-bg.png") 0 center no-repeat;
    background-size: cover;
}
</style>
