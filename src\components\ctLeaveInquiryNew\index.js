import Vue from "vue";
import index from "./components/index";
import { has, get } from "lodash-es";
import { SecurityAbilityModule } from "@/store/modules/securityAbility";

const InquiryConstructor = Vue.extend(index);

let instance = null;

const initInstance = () => {
    instance = new InquiryConstructor();
    const el = instance.$mount().$el;
    document.body.appendChild(el);
};

/**
 * 构造promise函数，因为async await函数内，无返回值，需要包裹一层promise函数后，被外层的钩子识别，从而使得切换前阻塞成功
 * @returns {Promise<unknown>}
 */
export function constructedPromise() {
    return new Promise((resolve, reject) => {
        const submitFn = get(SecurityAbilityModule.securityModule, "handleSubmit");
        submitFn(resolve, reject);
    });
}

const LeaveInquiry = function(options) {
    if (!instance) {
        initInstance();
    }

    for (const prop in options) {
        if (has(options, prop)) {
            instance[prop] = options[prop];
        }
    }

    // 默认配置取当前组件的handleSubmit方法
    if (!instance.submitFn) {
        instance.submitFn = constructedPromise;
    }

    Vue.nextTick(() => {
        instance.visible = true;
    });

    return new Promise((resolve, reject) => {
        instance.$on("action", action => {
            const info = {
                action: action,
                instance: instance,
            };

            resolve(info);
        });
        instance.$on("close", () => {
            reject(false);
        });
    });
};

/**
 * tab离开前的判断
 * @returns {Promise<unknown>}
 */
export function beforeLeaveComponent() {
    if (SecurityAbilityModule.isIpaFormChange) {
        return LeaveInquiry();
    }
    if (SecurityAbilityModule.isCdnFixFormChange) {
        return LeaveInquiry();
    }
    if (SecurityAbilityModule.isSecurityFormSame) {
        return true;
    }

    return LeaveInquiry();
}

/**
 * 销毁弹窗
 */
export function destroyLeaveDialog() {
    if (instance && instance.$el) {
        instance.$el.parentNode.removeChild(instance.$el);
        instance = null;
    }
}

export default LeaveInquiry;
