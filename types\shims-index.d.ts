/*
 * @Description: 全局的 ts 声明垫片
 * @Author: wang yuegong
 */

/* eslint-disable @typescript-eslint/no-empty-function */

// 声明 window 下的变量
interface Window {
    // AlogicLayout: {
    //     config: AlogicLayout.Config;
    //     consoleContainer: AlogicLayout.ConsoleContainer;
    //     authCurrentPromise: Promise;
    // };
    // CtcloudLayout: CtcloudLayout.CtcloudLayout;
    __INJECTED_PUBLIC_PATH_BY_QIANKUN__: any;
    __POWERED_BY_QIANKUN__: any;
    custom: any;
}

// namespace AlogicLayout {
//     interface InitArgs {
//         baseNode?: HTMLElement;
//     }

//     interface MvvmInstance {
//         destroy(): void;
//     }

//     interface UpdateFlags {
//         topic?: string;
//         domain?: string;
//         workspaceId?: string;
//     }

//     interface MatchFlags {
//         domain?: string;
//         key?: string;
//     }

//     interface EventHandler {
//         on(EVENT_NAME: string, callback: () => void): void;
//     }

//     interface Config {
//         sessionTimeoutKey: string;
//         eventHandler: AlogicLayout.EventHandler;
//     }

//     interface ConsoleContainer {
//         init(args: AlogicLayout.InitArgs): AlogicLayout.MvvmInstance;
//         updateMenu(args: AlogicLayout.UpdateFlags): void;
//         match(args: AlogicLayout.MatchFlags): void;
//     }
// }

// namespace CtcloudLayout {
//     interface InitArgs {
//         baseNode?: HTMLElement;
//     }
//     interface InitPromiseWithArgs extends InitArgs {
//         getDropdownMenuPromise?: Promise;
//     }
//     interface PublicInfoOutput {
//         authCurrentPromise: Promise;
//     }
//     interface FixedSidebarAd {
//         init(): AlogicLayout.MvvmInstance;
//     }
//     interface ConsoleLayout {
//         init(args: CtcloudLayout.InitPromiseWithArgs): AlogicLayout.MvvmInstance;
//     }
//     interface CtcloudLayout {
//         getPublicInfo(): CtcloudLayout.PublicInfoOutput;
//         consoleLayout: CtcloudLayout.ConsoleLayout;
//         fixedSidebarAd: CtcloudLayout.FixedSidebarAd;
//         authCurrentPromise: Promise;
//     }
// }

// TODO 暂时用最简单的方式声明，后续需要补全 ctFetch 相关声明文件
declare module "alogic-base-web";
declare module "alogic-base-web/src/ctFetch/index.js" {
    function ctFetch<T>(url: string, options?: CtFetchOptions): Promise<T>;

    ctFetch.config = (options: CtFetchConfigOptios): void => {};

    ctFetch.interceptors = {
        request: {
            use: (cb: CtFetchReqInterceptorCb): void => {},
        },
        response: {
            use: (cb: CtFetchResInterceptorCb): void => {},
        },
    };

    export default ctFetch;
}
declare module "alogic-base-web/src/util/util.js";

// 直接引用 node_modules/element-ui 包的源文件
declare module "element-ui/lib/utils/clickoutside";
declare module "element-ui/lib/utils/vue-popper" {
    import Component from "vue-class-component";
    @Component
    export default class VuePopper extends Vue {
        protected createPopper: () => void;
        protected updatePopper: () => void;
        protected destroyPopper: () => void;
    }
}

declare module "vue-echarts";
declare module "lodash-es";
declare module "@cutedesign/ct-monaco";

interface Document {
    webkitFullscreenElement: Element | null;
    mozFullScreenElement: Element | null;
    msFullscreenElement: Element | null;
    webkitExitFullscreen(): void;
    mozCancelFullScreen(): void;
    msExitFullscreen(): void;
}

interface Element {
    webkitRequestFullscreen(options?: FullscreenOptions | undefined): Promise<void>;
    mozRequestFullScreen(options?: FullscreenOptions | undefined): Promise<void>;
    msRequestFullscreen(options?: FullscreenOptions | undefined): Promise<void>;
}

// 声明 window 下的变量
interface Window {
    CtcloudAnalysis: any;
}

declare module 'element-ui/lib/utils/popup' {
    export const PopupManager: {
        nextZIndex: () => void
        zIndex: number
    };
}
