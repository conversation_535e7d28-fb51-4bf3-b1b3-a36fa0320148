import Vue from "vue";
import ctUserGuide from "@/components/ctUserGuide";
import { get } from "lodash-es";

const extendVue = Vue.extend(ctUserGuide);
const href = window.location.href;
const domain = href.split("//")[1].split("/")[0];
const isCtYun = domain.includes("ctyun.cn");
let guideInstance = null;
let isShow = false;
let lastDom = null; // 上一个高亮dom
let dialogClose = false;
let currentDomList = [];

/**
 * 获取节点列表（数组）
 * 因为原生NodeList不是数组
 */
function getNodeList(nodeList) {
    const ary = [];
    nodeList.forEach(item => {
        ary.push(item);
    });
    return ary;
}

export function showUserGuide() {
    if (!guideInstance) {
        let domProp =
            process.env.PLATFORM === "bs"
                ? ".waf-bs-container .page .page-menu .el-menu"
                : "#cdn-console-container .main .content .page-menu .el-menu";

        if (isCtYun) {
            domProp = "#ctcloud-console .main .content .page-menu .el-menu";
        }

        const dom = document.querySelector(domProp);
        const nodeList = getNodeList(dom ? dom.childNodes : []);
        guideInstance = new extendVue({
            propsData: {
                renderData: [
                    {
                        nodeFilter: () => {
                            let dom = document.querySelector(`${domProp} .el-submenu .el-menu .el-menu-item`);
                            // dom降级，为了适配菜单未打开的情况
                            if (!dom || (dom && !dom.clientHeight)) {
                                dom = get(nodeList, "[1]");
                            }

                            return dom;
                        },
                        // nodeIndex: 1,
                        data: [
                            {
                                text:
                                    "配置管理用于您的资产信息以及监测任务管理，可前往域名管理新增域名开启网站安全监测服务。",
                            },
                            // { text: "我是2222" },
                            // { text: "我是3333" },
                        ],
                    },
                    {
                        nodeIndex: 2,
                        data: [
                            {
                                text: "您可以在风险记录内查看监测任务执行后产生的风险日志和告警记录。",
                            },
                            // { text: "我是555555" },
                        ],
                    },
                    {
                        nodeIndex: 3,
                        data: [{ text: "安全分析集合了数据看板和报表服务，帮助您跟踪管理业务。" }],
                    },
                ],
                positionList: nodeList,
            },
        });

        // 监听dom变化
        guideInstance.$on("dom-change", dom => {
            if (lastDom) {
                lastDom.classList.toggle("overlay-active-bg");
            }

            lastDom = dom;
            dom.classList.toggle("overlay-active-bg");

            if (!dialogClose) {
                return;
            }

            currentDomList.forEach(item => {
                if (item.classList.contains("overlay-active-bg")) {
                    item.classList.remove("overlay-active-bg");
                }
            });
        });

        // 监听向导关闭
        guideInstance.$on("close", ary => {
            dialogClose = true;
            currentDomList = ary;
        });

        const el = guideInstance.$mount().$el;
        document.body.appendChild(el);
    }

    if (isShow) {
        return;
    }

    guideInstance.openUserGuide();
    isShow = true;
}
