<template>
    <el-dialog
        :title="$t(isAddIp ? 'ipSet.新增IP' : 'ipSet.dialog.IP集新增')"
        :visible="dialogVisible"
        :close-on-click-modal="false"
        :append-to-body="true"
        :modal-append-to-body="true"
        width="600px"
        @close="handleClose"
        class="ipset-dialog"
    >
        <el-form :model="form" :rules="rules" ref="form" label-width="130px">
            <el-form-item :label="$t('ipSet.form.IP集名称')" prop="alias_name" v-if="!isAddIp">
                <el-input v-model="form.alias_name" :placeholder="$t('ipSet.form.IP集名称placeholder')" />
            </el-form-item>
            <el-form-item :label="$t('ipSet.table.类型')" prop="forbid_type" v-if="!isAddIp">
                <el-radio-group v-model="form.forbid_type" role="radiogroup" aria-label="IP集类型">
                    <el-radio :label="IpSetTypeEnum.WhiteList">{{ $t("ipSet.form.白名单") }}</el-radio>
                    <el-radio :label="IpSetTypeEnum.BlackList">{{ $t("ipSet.form.黑名单") }}</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item :label="$t('ipSet.form.配置类型')" prop="configType">
                <el-radio-group
                    v-model="form.configType"
                    role="radiogroup"
                    aria-label="配置类型"
                    @change="handleConfigTypeChange"
                >
                    <el-radio :label="ConfigTypeEnum.Manual">{{ $t("ipSet.form.手动配置") }}</el-radio>
                    <el-radio :label="ConfigTypeEnum.File">{{ $t("ipSet.form.文件上传") }}</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item
                :label="$t('ipSet.form.IP地址')"
                prop="ips"
                v-if="form.configType === ConfigTypeEnum.Manual"
            >
                <el-input
                    type="textarea"
                    :rows="8"
                    v-model="form.ips"
                    :placeholder="$t('ipSet.form.ipPlaceholder', { limit: ipsetLimit })"
                />
            </el-form-item>
            <el-form-item :label="$t('ipSet.form.IP地址')" prop="ipFile" class="is-required" v-else>
                <el-upload
                    class="upload-demo"
                    drag
                    action="#"
                    :auto-upload="false"
                    :on-change="handleFileChange"
                    :on-remove="handleFileRemove"
                    :file-list="fileList"
                    accept=".xlsx,.xls,.csv"
                >
                    <i class="el-icon-upload"></i>
                    <div class="el-upload__text" v-html="$t('ipSet.dragText')"></div>
                    <div class="el-upload__tip" slot="tip">
                        {{ $t("ipSet.form.支持格式文件") }}
                        <a href="javascript:;" @click="downloadTemplate" class="aocdn-ignore-link template-link">
                            {{ $t("ipSet.form.下载模板") }}
                        </a>
                    </div>
                </el-upload>
            </el-form-item>
            <el-form-item :label="$t('ipSet.form.生效时间')" prop="timeRange">
                <el-date-picker
                    v-model="form.timeRange"
                    type="datetimerange"
                    :placeholder="$t('ipSet.form.请选择生效时间范围')"
                    value-format="timestamp"
                    range-separator="至"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                    :default-value="null"
                    :default-time="['00:00:00', '23:59:59']"
                    clearable
                />
                <div class="time-tip">{{ $t("ipSet.form.未配置默认立即生效、永久有效") }}</div>
            </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
            <el-button @click="handleClose">{{ $t("common.dialog.cancel") }}</el-button>
            <el-button type="primary" :loading="loading" @click="handleSubmit">{{
                $t("common.dialog.submit")
            }}</el-button>
        </div>
    </el-dialog>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from "vue-property-decorator";
import { IpSetTypeEnum, IP_REGEX } from "@/types/ipSet";
import { IpSetUrl } from "@/config/url/ipSet";
import { BasicUrl } from "@/config/url/basic";
import ipSetTemplate from "@/assets/IP集文件模板.xlsx";
import ipSetTemplateEn from "@/assets/IP-Set-Template.xlsx";
import * as XLSX from "xlsx";
import { getLang } from "@/utils";
import { SecurityAbilityModule } from "@/store/modules/securityAbility";

enum ConfigTypeEnum {
    Manual = "manual",
    File = "file",
}

interface IpSetForm {
    alias_name: string;
    forbid_type: IpSetTypeEnum;
    configType: ConfigTypeEnum;
    ips: string;
    ipFile?: File;
    timeRange: number | null;
}

@Component({})
export default class IpSetDialog extends Vue {
    @Prop({ type: Boolean, default: false }) visible!: boolean;
    @Prop({ type: Boolean, default: false }) isAddIp!: boolean;
    @Prop({ type: Number, default: 0 }) addedIpCount!: number; // 当前ip集中已存在的ip个数

    // 计算属性：用于同步 visible prop
    get dialogVisible(): boolean {
        return this.visible;
    }
    get isEn() {
        return getLang() === "en";
    }

    get ipSetIpLimit() {
        return SecurityAbilityModule.ipSet.ipSetIpLimit || 0;
    }

    readonly IpSetTypeEnum = IpSetTypeEnum;
    readonly ConfigTypeEnum = ConfigTypeEnum;

    private form: IpSetForm = {
        alias_name: "",
        forbid_type: IpSetTypeEnum.WhiteList,
        configType: ConfigTypeEnum.Manual,
        ips: "",
        timeRange: null,
    };

    private loading = false;
    private fileList: any[] = [];
    private ipsetLimit = 1000; // 默认值1000

    private rules = {
        alias_name: [
            {
                required: true,
                message: this.$t("ipSet.validation.请输入IP集名称").toString(),
                trigger: "blur",
            },
            {
                max: 60,
                message: this.$t("ipSet.validation.IP集名称不能超过60个字符").toString(),
                trigger: "blur",
            },
            {
                pattern: /^[\u4e00-\u9fa5a-zA-Z0-9]+$/,
                message: this.$t("ipSet.validation.IP集名称只能包含中文、英文和数字").toString(),
                trigger: "blur",
            },
        ],
        forbid_type: [
            {
                required: true,
                message: this.$t("ipSet.validation.请选择IP集类型").toString(),
                trigger: "change",
            },
        ],
        configType: [
            {
                required: true,
                message: this.$t("ipSet.validation.请选择配置类型").toString(),
                trigger: "change",
            },
        ],
        ips: [
            {
                required: true,
                message: this.$t("ipSet.validation.请输入IP列表").toString(),
                trigger: "blur",
            },
            {
                validator: this.validateIps,
                trigger: "blur",
            },
        ],
        ipFile: [
            {
                validator: this.validateIpFile,
                trigger: "change",
            },
        ],
        timeRange: null,
    };

    private fileValidationError: string | null = null;

    @Watch("isAddIp")
    private onIsAddIpChange() {
        this.resetForm();
    }
    private handleConfigTypeChange(value: ConfigTypeEnum) {
        // 使用 nextTick 确保 DOM 更新后再进行表单验证重置
        this.$nextTick(() => {
            if (this.$refs.form) {
                (this.$refs.form as any).clearValidate();
            }
            // 重置相关字段
            this.form.ips = "";
            this.form.ipFile = undefined;
            this.fileList = [];
        });
    }

    private async mounted() {
        await this.getIpLimitConfig();
    }

    /**
     * 获取IP数量限制配置
     */
    private async getIpLimitConfig() {
        const { ipsetLimit } = (await this.$ctFetch(BasicUrl.getConfig, {
            cache: true,
        })) as { ipsetLimit: number };
        this.ipsetLimit = ipsetLimit || 1000;
    }

    /**
     * 验证IP数量是否超过限制
     */
    private validateIpCount(ips: string[]): void | never {
        if (ips.length > this.ipsetLimit) {
            throw new Error(
                this.$t("ipSet.validation.IP数量不能超过", {
                    limit: this.ipsetLimit,
                }).toString()
            );
        }
    }

    /**
     * 验证IP列表
     */
    private validateIps(rule: any, value: string, callback: Function) {
        if (!value) {
            callback();
            return;
        }

        const ips = value.split("\n").filter((ip: string) => ip.trim());
        try {
            this.validateIpCount(ips);

            for (const ip of ips) {
                const trimmedIp = ip.trim();
                if (!IP_REGEX.test(trimmedIp)) {
                    callback(new Error(this.$t("ipSet.validation.请输入有效的IPv4或IPv6地址").toString()));
                    return;
                }
            }
            callback();
        } catch (error) {
            callback(error);
        }
    }

    /**
     * 验证IP列表
     */
    private validateIpList(ips: string[]): string[] | never {
        if (ips.length === 0) {
            throw new Error(this.$t("ipSet.validation.文件内容为空").toString());
        }

        // 验证IP数量
        this.validateIpCount(ips);

        const errors: string[] = [];
        ips.forEach((ip, index) => {
            if (!IP_REGEX.test(ip)) {
                errors.push(
                    this.$t("ipSet.validation.第N行IP格式错误", {
                        line: index + 1,
                        ip,
                    }).toString()
                );
            }
        });

        if (errors.length > 0) {
            throw new Error(errors.join("\n"));
        }

        return ips;
    }

    /**
     * 验证Excel文件内容
     */
    private async validateExcelContent(file: File): Promise<string[]> {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();

            reader.onload = (e: ProgressEvent<FileReader>) => {
                try {
                    if (!e.target?.result) {
                        throw new Error(this.$t("ipSet.validation.文件读取失败").toString());
                    }

                    const data = new Uint8Array(e.target.result as ArrayBuffer);
                    const workbook = XLSX.read(data, { type: "array" });
                    const worksheet = workbook.Sheets[workbook.SheetNames[0]];

                    // 只读取第一列数据
                    const ips = XLSX.utils
                        .sheet_to_json<{ A: string }>(worksheet, {
                            header: "A",
                            raw: false,
                            defval: "",
                        })
                        .map(row => row.A?.trim())
                        .filter(Boolean);

                    resolve(this.validateIpList(ips));
                } catch (error) {
                    reject(
                        error instanceof Error
                            ? error
                            : new Error(this.$t("ipSet.validation.文件解析失败").toString())
                    );
                }
            };

            reader.onerror = () => reject(new Error(this.$t("ipSet.validation.文件读取失败").toString()));
            reader.readAsArrayBuffer(file);
        });
    }

    /**
     * 验证IP文件
     */
    private validateIpFile(rule: any, value: any, callback: Function) {
        // 如果没有文件且没有验证错误，说明是初始状态或文件被删除
        if (!this.form.ipFile && !this.fileValidationError) {
            callback(new Error(this.$t("ipSet.validation.请上传IP地址").toString()));
            return;
        }

        // 如果有验证错误，显示具体错误
        if (this.fileValidationError) {
            callback(new Error(this.fileValidationError));
            return;
        }

        // 其他情况（有文件且验证通过）
        callback();
    }

    /**
     * 处理文件删除
     */
    private handleFileRemove() {
        const form = this.$refs.form as any;
        // 清空文件相关数据
        this.form.ipFile = undefined;
        this.form.ips = "";
        this.fileList = [];
        this.fileValidationError = null;
        // 清除验证状态，回到初始状态
        form?.clearValidate("ipFile");
    }

    /**
     * 处理文件变化
     */
    private async handleFileChange(file: any) {
        const form = this.$refs.form as any;

        // 检查文件大小
        const maxSize = 5 * 1024 * 1024; // 5MB
        if (file.size > maxSize) {
            this.fileValidationError = this.$t("ipSet.validation.上传文件大小不能超过5M").toString();
            this.form.ipFile = undefined;
            this.fileList = [];
            form?.validateField("ipFile");
            return;
        }

        // 清空之前的文件列表，确保只有一个文件
        this.fileList = [file];
        this.form.ipFile = file.raw;

        try {
            // 验证文件内容
            const ips = await this.validateExcelContent(file.raw);
            // 验证通过后，将IP列表保存到表单中
            this.form.ips = ips.join("\n");
            // 清除错误信息
            this.fileValidationError = null;
            // 验证成功后清除验证状态
            form?.clearValidate("ipFile");
        } catch (error) {
            // 清空文件
            this.form.ipFile = undefined;
            this.fileList = [];
            // 清空IP列表
            this.form.ips = "";
            // 设置错误信息
            this.fileValidationError =
                error instanceof Error ? error.message : this.$t("ipSet.validation.文件验证失败").toString();
            // 触发验证显示错误
            form?.validateField("ipFile");
        }
    }

    /**
     * 处理关闭对话框
     */
    private handleClose() {
        this.$emit("update:visible", false); // emit 给 IpSetEditDialog
        this.resetForm();
    }

    /**
     * 重置表单
     */
    private resetForm() {
        this.form = {
            alias_name: "",
            forbid_type: IpSetTypeEnum.WhiteList,
            configType: ConfigTypeEnum.Manual,
            ips: "",
            timeRange: null,
        };
        this.fileList = [];
        this.fileValidationError = null;
        if (this.$refs.form) {
            const form = this.$refs.form as any;
            form.resetFields();
            form.clearValidate();
        }
    }

    /**
     * 构建参数
     */
    private buildParams() {
        const baseParams = {
            list: this.form.ips.split("\n").filter((ip: string) => ip.trim()),
        } as any;

        if (this.form.timeRange && Array.isArray(this.form.timeRange) && this.form.timeRange.length > 0) {
            baseParams.start_time = this.form.timeRange[0] / 1000;
            baseParams.end_time = this.form.timeRange[1] / 1000;
        }

        if (!this.isAddIp) {
            baseParams.alias_name = this.form.alias_name;
            baseParams.forbid_type = this.form.forbid_type;
        }

        return baseParams;
    }

    /**
     * 处理提交
     */
    private async handleSubmit() {
        const form = this.$refs.form as any;
        if (!form) return;

        await form.validate();

        const params = this.buildParams();

        // 新增ip集的addedIpCount默认为0
        if (window.__POWERED_BY_QIANKUN__ && this.addedIpCount + params.list.length > this.ipSetIpLimit) {
            this.$message.error(`${this.$t("ipSet.IP个数已达到上限", { limit: this.ipSetIpLimit })}`);
            return;
        }

        if (this.isAddIp) {
            this.$emit("success", params); // emit 给 IpSetEditDialog
        } else {
            this.loading = true;
            await this.$ctFetch(IpSetUrl.create, {
                method: "POST",
                data: params,
                headers: {
                    "Content-Type": "application/json",
                },
            }).finally(() => {
                this.loading = false;
            });
            this.$message.success(this.$t("ipSet.success.IP集添加成功").toString());
            this.$emit("success", params); // emit 给 ipSetList
            this.handleClose();
        }
    }

    /**
     * 下载模板
     */
    private async downloadTemplate() {
        const templatePath = this.isEn ? ipSetTemplateEn : ipSetTemplate;
        const fullUrl = `${window.location.protocol}//${window.location.host}${templatePath}`;
        console.log("fullUrl", fullUrl);
        window.open(fullUrl, "_blank");
    }
}
</script>

<style lang="scss" scoped>
.ipset-dialog {
    ::v-deep .el-form-item {
        .el-form-item__error {
            position: relative;
            word-break: break-word;
        }
    }
}

.upload-demo {
    width: 100%;
}
.el-upload__tip {
    line-height: 1.2;
    font-size: 12px;
    color: #7c818c;

    .template-link {
        margin-left: 10px;
        text-decoration: none;

        &:hover {
            text-decoration: underline;
        }
    }
}
.time-tip {
    font-size: 12px;
    color: #7c818c;
}
</style>
