<template>
    <ct-section-wrap :headerText="$t('billing.title')">
        <ct-box>
            <el-tabs v-model="currentComponent">
                <el-tab-pane :label="$t('billing.tabs.tab1')" name="PackageDetail" />
                <el-tab-pane :label="$t('billing.tabs.tab2')" name="ResourcePackage" />
                <!-- <el-tab-pane :label="$t('billing.tabs.tab3')" name="History" /> -->
            </el-tabs>
            <component :is="currentComponent" />
        </ct-box>
    </ct-section-wrap>
</template>

<script lang="ts">
import PackageDetail from "./packageDetail/index.vue";
import ResourcePackage from "./resourcePackage/index.vue";
import History from "./history/index.vue";
import { Component, Vue } from "vue-property-decorator";

@Component({
    components: { History, PackageDetail, ResourcePackage },
})
export default class Billing extends Vue {
    currentComponent = "PackageDetail";
}
</script>
