# 配置项模块可插拔方案设计文档

## 一、设计目标

实现配置项模块的可插拔功能，使配置项可以通过开关控制上下线，并保持代码的可维护性。模块名(即 v1/basic/grayscale 接口中的 module 值)是串起整个流程的标识。

## 二、核心实现

### 1. 配置项开关管理

```typescript
// 新建 store/modules/configModules.ts
```

### 2. 配置项注册中心

```js
// 新建 registry/configModuleRegistry.js
export const configModuleRegistry = {};

// 配置项注册接口说明
// 如果有功能锁，功能锁的标识需要跟模块名称(moduleName)一致
export function registerConfigModule(moduleName, config) {
    // config传入以下配置对象
    // {
    //      fields: { ... },              // 字段定义
    //      useCustomLock: (thisData) => { ... },  // 自定义功能锁，不传默认使用 funcName 功能锁
    //      onModuleChange(form, val) { ... },  // 更新处理（包含当前配置影响其他配置的联动逻辑）
    //      toApi(form, { isLocked }) { ... },          // API转换
    //      init(form) { ... },                      // 自定义的初始化处理方法，不传默认按照fields字段进行初始化
    //      displayCondition(form) { ... },     // 定义额外的展示条件（其他配置影响当前配置的展示联动逻辑），不满足展示条件时，不会触发toApi、不会将表单加入校验
    //      anchor: { ... }               // 锚点配置
    //  }
    configModuleRegistry[moduleName] = config;
}

// 获取注册中心配置的工具函数
export function getRegistryConfig(moduleName) {
    // 如果总开关关闭，返回空配置
    if (!ConfigModulesModule.isMasterSwitchEnabled) {
        return {
            fields: {},
            useCustomLock: () => false,
            onModuleChange: () => ({}),
            toApi: () => ({}),
            init: () => ({}),
            displayCondition: () => true, // 默认返回 true，表示默认显示
            anchor: null
        };
    }
    return configModuleRegistry[moduleName];
}
```

### 3. 配置项包装组件

```vue
// 新建 components/ModuleWrapper.vue
<template>
    <div v-if="isEnabled && isMasterSwitchEnabled">
        <slot></slot>
    </div>
</template>

<script>
import { ConfigModulesModule } from '@/store/modules/configModules';

export default {
    name: 'ModuleWrapper',
    props: {
        moduleName: {
            type: String,
            required: true
        }
    },
    computed: {
        isMasterSwitchEnabled() {
            return ConfigModulesModule.isMasterSwitchEnabled;
        },
        isEnabled() {
            return ConfigModulesModule.isModuleEnabled(this.moduleName);
        }
    }
};
</script>
```

## 三、使用方法

1. 加载配置项

```javascript
// 加载配置项状态
async function loadConfigModules(domain) {
    // 获取总开关状态
    await store.dispatch("fetchModulesStatus", domain);
}

// 在组件中使用
export default {
    async created() {
        await loadConfigModules(this.domain);
    }
};
```

## 四、注意事项

1. 配置项组件应该是自包含的，不应该依赖其他配置项

## 五、锚点处理方案

### 1. handleAnchorMixin 改造增强

```js
// 修改 mixins/handleAnchorMixin.js
import { configModuleRegistry } from '@/registry/configModuleRegistry';

export default {
    computed: {
        isMasterSwitchEnabled() {
            return ConfigModulesModule.isMasterSwitchEnabled;
        },
        isEnabled() {
            return ConfigModulesModule.isModuleEnabled(this.moduleName);
        },
        // 获取可插拔配置项的锚点
        pluginAnchors() {
            // 总开关关闭时不返回任何可插拔配置项的锚点
            if (!this.isMasterSwitchEnabled) {
                return [];
            }
            return Object.entries(configModuleRegistry)
                .filter(([module, config]) => {
                    if (!config.anchor) return false;
                    // 1. 模块必须启用
                    if (!this.isModuleEnabled(module)) return false;
                    // 2. 满足显示条件（如果有）
                    if (config.anchor.anchorCondition && !config.anchor.anchorCondition.call(this)) return false;
                    return true;
                })
                .map(([_, config]) => ({
                    label: this.$t(config.anchor.label),
                    prop: config.anchor.prop
                }));
        },

        // 增强现有的锚点列表计算属性
        basicAnchorList() {
            // 1. 保持原有的基础锚点逻辑
            const defaultList = [
                { label: this.$t("domain.editPage.label1"), prop: "#div0" },
                { label: this.$t("domain.detail.tab3"), prop: "#div1" },
                // ... 其他原有的基础锚点
            ];

            // 2. 只有在总开关开启时才合并可插拔配置项的锚点
            return this.isMasterSwitchEnabled ? this.mergePluginAnchors(defaultList) : defaultList;
        },

        cdnAnchorListExpand() {
            // 1. 保持原有的展开锚点逻辑
            const defaultList = [/* ... 原有的展开锚点列表 ... */];

            // 2. 只有在总开关开启时才合并可插拔配置项的锚点
            return this.isMasterSwitchEnabled ? this.mergePluginAnchors(defaultList) : defaultList;
        },

        cdnAnchorListCollapse() {
            // 1. 保持原有的折叠锚点逻辑
            const defaultList = [/* ... 原有的折叠锚点列表 ... */];

            // 2. 只有在总开关开启时才合并可插拔配置项的锚点
            return this.isMasterSwitchEnabled ? this.mergePluginAnchors(defaultList) : defaultList;
        }
    },

    methods: {
        // 合并可插拔配置项的锚点
        mergePluginAnchors(baseAnchors) {
            // 如果总开关关闭，直接返回原始锚点
            if (!this.isMasterSwitchEnabled) {
                return baseAnchors;
            }

            const result = [...baseAnchors];

            this.pluginAnchors.forEach(anchor => {
                const config = Object.values(configModuleRegistry)
                    .find(c => c.anchor?.prop === anchor.prop);

                if (!config?.anchor?.position) {
                    // 没有指定位置，追加到末尾
                    result.push(anchor);
                    return;
                }

                const { position } = config.anchor;
                const targetIndex = result.findIndex(item => item.prop === position.target);
                if (targetIndex === -1) {
                    result.push(anchor);
                    return;
                }

                // 根据配置的位置插入
                const insertIndex = position.type === 'before' ? targetIndex : targetIndex + 1;
                result.splice(insertIndex, 0, anchor);
            });

            return result;
        }
    }
};
```

### 2. 注意事项

1. 范围限制：
   - 只对新增的可插拔配置项使用新的锚点管理机制
   - 不修改现有的基础锚点逻辑
   - 保持原有锚点的位置和顺序不变

2. 位置控制：
   - 可以相对于任何现有锚点定位
   - 支持在基础锚点之间插入
   - 未指定位置默认追加到末尾


具体使用示例请参考"七、配置项组件示例"。

## 七、配置项组件示例

```vue
// 新建组件 cdn-front/src/views/domainConfig/basicConfig/config-components/sharedHost.vue
// 注意 ref="shared_host_form" 需由 `${moduleName}_form` 拼接而成，这会影响表单校验自动化
```

### 7.1 数据流设计注意事项

1. 配置项组件的数据流向：
   ```
   组件内部状态变更 -> 触发组件 onModuleChange -> FormWrapper onModuleChange -> 注册中心 onModuleChange -> 更新主表单 -> 通过 watch formDatas 更新组件
   ```

2. 防止循环更新：
   - 问题：组件的 `watch formDatas` 和 FormWrapper 的 `onModuleChange` 可能形成循环调用
   - 解决方案：在组件中使用内部更新标记
   ```js
   export default {
       data() {
           return {
               isInnerChange: false, // 内部更新标记
           }
       },
       watch: {
           formDatas: {
               handler(val) {
                   // 避免从 prop 更新触发新的 onModuleChange
                   if (this.isInnerChange) {
                       this.isInnerChange = false;
                       return;
                   }
                   // 更新内部表单
                    this.form.shared_cache_enable = val.shared_host ? 'on' : 'off';
                    this.form.shared_host = val.shared_host;
               }
           }
       },
       methods: {
           handleChange() {
               this.isInnerChange = true; // 标记这是一个内部更新
               this.$emit("onModuleChange", this.getFormData());
           }
       }
   }
   ```

## 八、配置项组件调用与导入说明

### 8.1 组件调用

```js
// 注意，如果有功能锁，<lock-tip> 包在组件内部，功能锁的标识需要跟模块名称(moduleName)一致
// 注意，ref 属性需要跟模块名称(moduleName)一致
<shared-host
    ref="shared_host"
    :isXxxLocked="isXxxLocked"
    :addon="isAddon"
    :formDatas="form"
    @onModuleChange="onModuleChange('shared_host', $event)"
/>

```

### 8.2 组件导入与注册

> 新增的配置项组件（如 shareHost.vue）需要在主表单页面或相关 mixin（如 gatewayDataMixin.js）中导入并注册。此处不用总开关控制，直接导入。会根据配置开关状态按需加载

```js
// src/views/domainConfig/gatewayDataMixin.js
export default {
    components: {
        // 基础组件
        BaseForm,
        // ... 其他基础组件

        // 可插拔配置组件
        shareHost
    },
    // ... 其他代码
};

```

### 8.3 组件注册说明

1. **动态注册**：
   - 使用 `createPluginComponents` 工具函数统一管理配置项组件的注册
   - 总开关关闭时不注册任何配置项组件，减少资源加载
   - 支持动态导入，优化首次加载性能

2. **使用方式**：
   - 在 mixin 或组件中通过扩展运算符注册组件
   - 配置项组件统一由 `createPluginComponents` 管理
   - 基础组件保持原有的注册方式

3. **优点**：
   - 总开关控制更加彻底，从组件注册层面就开始管理
   - 避免不必要的组件加载，优化性能
   - 集中管理配置项组件，便于维护
   - 支持按需加载，提高首屏加载速度

4. **注意事项**：
   - 总开关关闭时，配置项组件不会被注册，也不会被加载
   - 需要确保 store 在组件注册时已经初始化
   - 建议使用动态导入（import()）来加载配置项组件

## 九、FormWrapper 的改造

### （涉及基础配置、加速配置、创建、应用加速编辑，本次只改造基础配置+加速配置的主表单，其他的等后续有需求再进行改造）
### 9.1 数据定义与自动初始化

```js
import configModuleMixin from '@/mixins/configModuleMixin'; // 可插拔配置，引入 mixin

export default {
    // ... 其他原有代码
    watch: {
        securityDomain: {
            async handler(val) {
                // ....其他原有代码
                // 可插拔配置-加载配置项状态，需要在获取域名之后触发
                console.log(111, val)
                this.loadConfigModules(val); // 可插拔配置
                this.getDomainDetail();

                // ....其他原有代码
            },
            immediate: true,
        },
    },
    methods: {
        // 初始化表单数据
        init(val) {
            if (!val) return;
            this.$nextTick(async () => {
                const thisData = cloneDeep(val);
                this.temp_domain_detail = cloneDeep(val);

                // 可插拔配置-在原有初始化之前,先合并可配置模块的字段
                this.mergeConfigModuleFields();

                // ......
                // 用于自助功能锁
                this.funcName = thisData?.funcName || [];
                // 可插拔配置-同步功能锁数据到 store
                ConfigModulesModule.SET_FUNC_LOCKS(this.funcName);

                // ......
                // 可插拔配置-初始化可配置模块的数据
                this.initConfigModules(thisData);

                SecurityAbilityModule.SET_SECURITY_BASIC_ORIGIN_FORM(cloneDeep(this.form));
                // ......
                this.renderFormValidate();
            })
        },
        // 自动获取开启的配置项表单校验
        renderFormValidate() {
            this.validateForm = [];
            // 主表单
            this.validateForm.push(this.$refs.form);
            // ...原有代码

            // 可插拔配置-添加可配置模块的表单校验
            this.renderConfigModuleValidate();
        },
        async handleSubmit(resolve, reject) {
            // ...
            await this.$ctFetch(siteUrl.domainUpdate, {
                method: "POST",
                transferType: "json",
                body: {
                    ...this.formData, // 1. 先用原有 mixin 逻辑处理老配置项
                    ...this.getApiData() // 可插拔配置-合并可配置模块的数据
                },
                clearEmptyParams: false,
            });
            // ...
        }

    },
};

## 十、验证与清理自动化

- 验证：遍历所有已启用配置项的表单实例，统一调用 validate

**说明：**
- 老配置项继续走原有 mixin 逻辑，不受影响。
- 新增可插拔配置项只需在注册中心实现 toApi，FormWrapper 自动合并。
- 后续新增配置项只需注册，不用再动主表单和 mixin。


### to 配合

- 接口返回数据格式示例：默认开，后端传关的给前端，前端维护一份module的清单，拼成完整的模块数据
```js
传参：domain （后端根据具体情况考虑是否需要结合 domain 来计算 enabled）

{
    modules: [
        { module: 'shared_host', enabled: false }
    ]
}
```

- 接口返回错误时，直接公共报错

- 增加总开关（新方案的开关，默认开，如果新方案开关关闭，使用新方案流程开发的自助项，等同于一起下线）

- 这个配置项存在配置管理后台
