<template>
    <section class="domain-edit-wrap" v-loading="loading">
        <section class="domain-edit">
            <div class="domain-edit-content form-wrapper">
                <el-form
                    class="simple-create-form"
                    name="creatForm"
                    :model="creatForm"
                    :label-width="isEn ? '190px' : '140px'"
                >
                    <p class="label-name">{{ $t("domain.editPage.label1") }}</p>
                    <el-form-item :label="$t('domain.create.domainName')" prop="domain" class="is-required">
                        <n-alogic-domain-input
                            v-if="!isBatchCreate"
                            @domainChange="onDomainChange"
                            @domainEndsWithCtyun="onDomainEndsWithCtyun"
                            ref="domain"
                            :domain="creatForm.domain"
                        ></n-alogic-domain-input>
                        <div v-if="isBatchCreate">
                            <div v-for="(domain, i) in domains" :key="`batch-${i}`" class="batch-domain">
                                <n-alogic-domain-input
                                    @domainChange="onDomainChange"
                                    @domainValidating="domainValidating = true"
                                    ref="domains"
                                    :domain="domain"
                                    :i="i"
                                ></n-alogic-domain-input>
                                <el-button
                                    v-if="domains.length > 1"
                                    type="text"
                                    icon="el-icon-remove-outline"
                                    @click="deleteDomain(i)"
                                ></el-button>
                            </div>
                        </div>
                        <div class="tooltips">{{ $t("domain.create.tip1") }}</div>
                        <el-button
                            v-if="isBatchCreate && domains.length < batchCreateLimit"
                            type="text"
                            icon="el-icon-plus"
                            @click="addDomain"
                            :disabled="domainValidating"
                            >{{ $t("domain.create.btn1") }}</el-button
                        >
                    </el-form-item>
                    <el-form-item
                        :label="$t('domain.create.acceType')"
                        prop="product_code"
                        class="select-action-wrapper is-required"
                    >
                        <el-select
                            v-model="creatForm.product_code"
                            class="select"
                            size="medium"
                            ref="product_code"
                            popper-class="editor-select-popover-class"
                            @change="onProductCodeChange"
                        >
                            <el-option
                                v-for="item in codeMap"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                                :disabled="item.disabled"
                            />
                        </el-select>
                        <!-- 001|003|004|014 不展示这个文案 -->
                        <div
                            class="tooltips"
                            v-if="
                                creatForm.product_code !== '001' &&
                                    creatForm.product_code !== '003' &&
                                    creatForm.product_code !== '004' &&
                                    creatForm.product_code !== '014'
                            "
                        >
                            {{ $t("domain.create.tip2") }}
                        </div>
                        <div
                            v-if="validRes.product_code.msg"
                            class="tooltips"
                            :class="{ 'tooltips-error': validRes.product_code.msg }"
                        >
                            {{ validRes.product_code.msg }}
                        </div>
                    </el-form-item>
                    <el-form-item
                        v-if="creatForm.product_code === '006'"
                        :label="$t('domain.create.domainType')"
                        prop="product_code"
                        class="select-action-wrapper is-required"
                    >
                        <el-select
                            v-model="creatForm.domain_type"
                            class="select"
                            size="medium"
                            ref="product_code"
                            popper-class="editor-select-popover-class"
                            @change="onDomainTypeChange"
                        >
                            <el-option
                                v-for="item in wholeStationFilterProducts"
                                v-show="item.id !== '190'"
                                :key="item.id"
                                :label="item.displayValue"
                                :value="item.id"
                                :disabled="item.disabled"
                            />
                        </el-select>
                        <div class="tooltips">
                            <div v-if="isVip || isCtyun">
                                <span>{{ $t("domain.create.tip54") }}</span>
                                <a class="aocdn-ignore-link" @click="$docHelp(integratedCdnLink)">{{
                                    this.$t("domain.create.tip55")
                                }}</a>
                                <span>{{ this.$t("domain.create.tip59") }}</span>
                                <a class="aocdn-ignore-link" @click="$docHelp(uploadAccelerationLink)">{{
                                    this.$t("domain.create.tip56")
                                }}</a>
                                <span>{{ this.$t("domain.create.tip59") }}</span>
                                <a class="aocdn-ignore-link" @click="$docHelp(webSocketAccelerationLink)">{{
                                    this.$t("domain.create.tip57")
                                }}</a>
                                <span>{{ this.$t("domain.create.tip60") }}</span>
                            </div>
                            <div v-else>
                                <span>{{ $t("domain.create.tip58") }}</span>
                            </div>
                        </div>
                        <div
                            v-if="validRes.domain_type.msg"
                            class="tooltips"
                            :class="{ 'tooltips-error': validRes.domain_type.msg }"
                        >
                            {{ validRes.domain_type.msg }}
                        </div>
                    </el-form-item>
                    <el-form-item
                        v-if="showAreaScope"
                        :label="$t('domain.create.acceRegion')"
                        ref="area_scope"
                        class="is-required"
                    >
                        <n-alogic-acce-area
                            :productCode="creatForm.product_code"
                            :domainType="creatForm.domain_type"
                            ref="area_scope"
                            :areaScope="String(creatForm.area_scope)"
                            :domain="creatForm.domain"
                            @acceArea="onAcceAreaChange"
                        ></n-alogic-acce-area>
                        <div
                            v-if="validRes.area_scope.msg"
                            class="tooltips"
                            :class="{ 'tooltips-error': validRes.area_scope.msg }"
                        >
                            {{ validRes.area_scope.msg }}
                        </div>
                    </el-form-item>
                    <!-- ipv6开关 -->
                    <el-form-item
                        :label="$t('domain.create.IPv6')"
                        prop="ipv6_switch"
                        v-if="operationId !== 'isRepeat'"
                    >
                        <el-switch v-model="creatForm.ipv6_switch" :active-value="1" :inactive-value="0" />
                    </el-form-item>
                    <!-- 参照域名 -->
                    <el-form-item
                        v-if="targetDomainCreate"
                        :label="$t('domain.create.copy.tip5')"
                        prop="acce_config"
                        class="is-required"
                    >
                        <el-radio-group v-model="creatForm.acce_config" @change="onAcceConfigChange">
                            <el-radio label="custom">{{ $t("domain.create.copy.tip6") }}</el-radio>
                            <el-radio label="copy">{{ $t("domain.create.copy.tip4") }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item
                        v-if="isCopyDomain"
                        :label="$t('domain.create.copy.tip1')"
                        prop="target_domain"
                        class="is-required"
                    >
                        <div class="copy-domain-select">
                            <el-select
                                v-model="creatForm.target_domain"
                                :placeholder="$t('common.searchBar.errMsg2')"
                                :loading="allDomainListLoading"
                                filterable
                                style="width: 360px;"
                            >
                                <el-option
                                    v-for="itm in copyDomainList"
                                    :label="itm.domain"
                                    :key="itm.domain"
                                    :value="itm.domain"
                                ></el-option>
                            </el-select>
                            <el-tooltip placement="top" popper-class="aocdn-ignore-tooltip-width-fixed">
                                <ct-svg-icon
                                    icon-class="question-circle"
                                    style="font-size: 14px; color: #7c818c;"
                                ></ct-svg-icon>
                                <i18n slot="content" path="domain.create.copy.tip3"></i18n>
                            </el-tooltip>
                            <a @click="gotoDomainDetail" class="aocdn-ignore-link">{{
                                $t("domain.create.copy.tip7")
                            }}</a>
                        </div>
                        <div class="tooltips copy-domain-select-warning">
                            <i class="el-icon-warning" style="font-size: 14px;"></i>
                            {{ $t("domain.create.copy.tip8") }}
                        </div>
                        <div
                            v-if="validRes.target_domain.msg"
                            class="tooltips"
                            :class="{ 'tooltips-error': validRes.target_domain.msg }"
                        >
                            {{ validRes.target_domain.msg }}
                        </div>
                    </el-form-item>
                    <div v-if="operationId !== 'isRepeat'">
                        <p class="label-name">{{ $t("domain.detail.tab3") }}</p>
                        <el-form-item
                            :label="$t('domain.create.originServer')"
                            prop="origin"
                            ref="origin"
                            class="is-required"
                        >
                            <div class="list">
                                <el-table
                                    class="origin-table"
                                    :data="creatForm.origin"
                                    stripe
                                    size="mini"
                                    :empty-text="$t('common.table.empty')"
                                    border
                                    header-row-class-name="origin-table__header"
                                >
                                    <el-table-column
                                        :label="$t('domain.create.number')"
                                        type="index"
                                        width="60"
                                    ></el-table-column>
                                    <el-table-column prop="is_xos" :label="$t('domain.create.originType')">
                                        <template slot-scope="scope">
                                            {{ xosMap[scope.row.is_xos] }}
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="origin" :label="$t('domain.create.originServer')">
                                    </el-table-column>
                                    <el-table-column prop="role" :label="$t('domain.create.level')">
                                        <template slot-scope="scope">
                                            {{ roleMap[scope.row.role] }}
                                        </template>
                                    </el-table-column>
                                    <el-table-column
                                        prop="origin_host"
                                        :label="$t('domain.create.originHost')"
                                    ></el-table-column>
                                    <el-table-column prop="weight" :label="$t('domain.create.weight')">
                                    </el-table-column>
                                    <el-table-column :label="$t('domain.operate')">
                                        <template slot-scope="scope">
                                            <el-button
                                                type="text"
                                                size="small"
                                                @click="onOperator(scope.row, 'edit', 'origin', scope.$index)"
                                                >{{ $t("domain.modify") }}</el-button
                                            >
                                            <el-button
                                                type="text"
                                                size="small"
                                                @click="
                                                    onOperator(scope.row, 'delete', 'origin', scope.$index)
                                                "
                                                >{{ $t("domain.delete") }}</el-button
                                            >
                                        </template>
                                    </el-table-column>
                                </el-table>
                            </div>
                            <el-button
                                type="text"
                                @click="onOperator(null, 'create', 'origin')"
                                :disabled="creatForm.origin.length >= 60"
                            >
                                {{ $t("domain.create.addOrigin") }}
                            </el-button>
                            <div class="tooltips">
                                <!-- <span v-if="creatForm.product_code === '006'">
                                    {{ $t("domain.create.tip4") }}
                                </span> -->
                                <span>{{ $t("domain.create.tip5-1") }}</span>
                            </div>
                            <div
                                v-if="validRes.origin.msg"
                                class="tooltips"
                                :class="{ 'tooltips-error': validRes.origin.msg }"
                            >
                                {{ validRes.origin.msg }}
                            </div>
                        </el-form-item>
                        <el-form-item
                            :label="$t('domain.create.originPolicy')"
                            prop="backorigin_protocol"
                            class="form-item-div"
                        >
                            <div class="autoform-item">
                                <el-radio-group
                                    v-model="creatForm.backorigin_protocol"
                                    fill="#3d73f5"
                                    size="medium"
                                    class="radio-group"
                                    @change="onBackoriginProtocolChange"
                                >
                                    <template>
                                        <el-radio-button
                                            v-for="o in originProtocolOption"
                                            :key="o.value"
                                            :label="o.value"
                                            :disabled="o.disabled"
                                        >
                                            <span v-html="o.displayValue" class="radio-button-display"></span>
                                        </el-radio-button>
                                    </template>
                                </el-radio-group>
                                <div class="tooltips">
                                    {{ $t("domain.create.tip6") }}
                                </div>
                            </div>
                        </el-form-item>
                        <!-- 回源加密算法-->
                        <el-form-item
                            v-if="creatForm.backorigin_protocol !== 'http'"
                            :label="$t('domain.create.originEncodeSelf')"
                            prop="proxy_gmssl_mode"
                        >
                            <el-select
                                v-model="creatForm.proxy_gmssl_mode"
                                :placeholder="$t('domain.create.originEncodeSelfPlaceholder')"
                                class="input-wrapper"
                                clearable
                            >
                                <el-option
                                    v-for="e in proxyGmsslModeList"
                                    :label="e.displayValue"
                                    :value="e.value"
                                    :key="e.value"
                                />
                            </el-select>
                        </el-form-item>
                        <!-- VIVO客户不显示动态回源策略，直接默认值下发 -->
                        <el-form-item
                            v-if="creatForm.product_code === '006' && !isVivo"
                            :label="$t('domain.create.dynamicPolicy')"
                            prop="origin_policy.http_config.origin_type"
                            class="form-item-div is-required"
                        >
                            <el-radio-group
                                v-model="creatForm.origin_policy.http_config.origin_type"
                                fill="#3d73f5"
                                size="medium"
                                class="radio-group"
                            >
                                <template>
                                    <el-radio-button
                                        v-for="o in originPolicyOption"
                                        :key="o.value"
                                        :label="o.value"
                                    >
                                        <span v-html="o.displayValue" class="radio-button-display"></span>
                                    </el-radio-button>
                                </template>
                            </el-radio-group>
                            <div class="tooltips">
                                {{ $t("domain.create.tip7") }}
                            </div>
                        </el-form-item>
                        <!-- 回源端口-->
                        <el-form-item
                            :label="$t('domain.editPage.label4')"
                            prop="origin_port"
                            class="form-item-div"
                        >
                            <n-alogic-origin-port
                                @portChange="onPortChange"
                                @followRequestBackportChange="onFollowRequestBackportChange"
                                ref="origin_port"
                                class="autoform-item"
                                :http_origin_port="Number(creatFormCache.basic_conf.http_origin_port)"
                                :https_origin_port="Number(creatFormCache.basic_conf.https_origin_port)"
                                :backorigin_protocol="creatForm.backorigin_protocol"
                                :follow_request_backport="creatForm.follow_request_backport"
                            ></n-alogic-origin-port>
                            <div
                                v-if="validRes.origin_port.msg"
                                class="tooltips"
                                :class="{ 'tooltips-error': validRes.origin_port.msg }"
                            >
                                {{ validRes.origin_port.msg }}
                            </div>
                        </el-form-item>
                        <host-select-mod
                            v-model="creatForm.req_host"
                            prop="req_host"
                            ref="req_host"
                            :accelerate-domains="
                                (isBatchCreate ? domains : [creatForm.domain] || []).filter(itm => !!itm)
                            "
                            :origin-domains="creatForm.origin.map(itm => itm.origin).filter(itm => !!itm)"
                            :label="$t('domain.默认回源HOST')"
                        >
                            <template slot="create">
                                <div class="tooltips">
                                    {{ $t("domain.create.tip8") }}
                                </div>
                                <div
                                    v-if="validRes.req_host.msg"
                                    class="tooltips"
                                    :class="{ 'tooltips-error': validRes.req_host.msg }"
                                >
                                    {{ validRes.req_host.msg }}
                                </div>
                            </template>
                        </host-select-mod>
                        <p class="label-name">{{ $t("domain.create.https") }}</p>
                        <p class="prompt" style="max-width: calc(100% - 20px)" v-if="isNewUserAndIsCdn">
                            <span style="white-space: normal">
                                <i18n path="domain.create.tip49">
                                    <a
                                        class="aocdn-ignore-link"
                                        @click="$docHelp('https://www.ctyun.cn/document/10015932/10448893')"
                                        >{{ $t("domain.create.tip49-1") }}</a
                                    >
                                </i18n>
                            </span>
                        </p>
                        <el-form-item :label="$t('domain.create.httpsSwitch')" prop="https_status">
                            <cdn-https-support-tip
                                :isCDN="creatForm.product_code === '008'"
                                @update:support="v => (isHttpsSupport = v)"
                            >
                                <el-switch
                                    slot-scope="{ httpsDisabled }"
                                    v-model="creatForm.https_status"
                                    :disabled="httpsDisabled"
                                    inactive-color="#909399"
                                    active-value="on"
                                    inactive-value="off"
                                    @change="onHttpsStatusChange"
                                />
                            </cdn-https-support-tip>
                        </el-form-item>

                        <!-- <p class="label-name" v-if="creatForm.https_status === 'on'">{{ $t("domain.create.httpsUpload") }}</p> -->

                        <div :class="{ 'switch-wrapper': creatForm.https_status === 'on' }">
                            <el-form-item
                                v-if="creatForm.https_status === 'on'"
                                :label="$t('certificate.国际标准证书')"
                                prop="cert_name"
                            >
                                <!-- 证书 -->
                                <div style="display: flex; gap: 12px;">
                                    <el-select
                                        style="width: 250px"
                                        v-model="creatForm.cert_name"
                                        ref="cert_name"
                                        clearable
                                        :placeholder="$t('domain.create.placeholder1')"
                                        :disabled="creatForm.https_status === 'off'"
                                        filterable
                                        default-first-option
                                        :loading="cert_name_loading"
                                        @change="cert_name_change"
                                    >
                                        <el-option
                                            v-for="itm in cert_name_list"
                                            :key="itm.cert_name"
                                            :label="itm.cert_name"
                                            :value="itm.cert_name"
                                        ></el-option>
                                    </el-select>
                                    <i
                                        :style="{
                                            alignSelf: 'center',
                                            cursor: cert_name_loading ? 'not-allowed' : 'pointer',
                                            fontSize: '14px',
                                        }"
                                        :class="[
                                            'aocdn-ignore-link',
                                            cert_name_loading ? 'el-icon-loading' : 'el-icon-refresh-right',
                                        ]"
                                        @click="!cert_name_loading && getCertList('', true)"
                                    />
                                </div>
                                <div class="tooltips">
                                    {{ $t("domain.create.tip9")
                                    }}<span
                                        @click="creatForm.https_status === 'on' && showCertUpload()"
                                        class="btn-bg"
                                        >{{ $t("domain.create.tip9-1") }}</span
                                    >
                                </div>
                                <div
                                    v-if="validRes.cert_name.msg"
                                    class="tooltips"
                                    :class="{ 'tooltips-error': validRes.cert_name.msg }"
                                >
                                    {{ validRes.cert_name.msg }}
                                </div>
                            </el-form-item>
                            <el-form-item
                                v-if="creatForm.https_status === 'on'"
                                :label="$t('certificate.国密证书')"
                                prop="cert_name_gm"
                            >
                                <!-- 证书 -->
                                <div style="display: flex; gap: 12px;">
                                    <el-select
                                        style="width: 250px"
                                        v-model="creatForm.cert_name_gm"
                                        ref="cert_name_gm"
                                        clearable
                                        :placeholder="$t('domain.create.placeholder1')"
                                        :disabled="creatForm.https_status === 'off'"
                                        filterable
                                        default-first-option
                                        :loading="cert_name_loading"
                                        @change="val => cert_name_change(val, true)"
                                    >
                                        <el-option
                                            v-for="itm in cert_name_gm_list"
                                            :key="itm.cert_name"
                                            :label="itm.cert_name"
                                            :value="itm.cert_name"
                                        ></el-option>
                                    </el-select>
                                    <i
                                        :style="{
                                            alignSelf: 'center',
                                            cursor: cert_name_loading ? 'not-allowed' : 'pointer',
                                            fontSize: '14px',
                                        }"
                                        :class="[
                                            'aocdn-ignore-link',
                                            cert_name_loading ? 'el-icon-loading' : 'el-icon-refresh-right',
                                        ]"
                                        @click="!cert_name_loading && getCertList('', true, true)"
                                    />
                                </div>
                                <div class="tooltips">
                                    {{ $t("domain.create.tip9")
                                    }}<span
                                        @click="creatForm.https_status === 'on' && showCertUpload(true)"
                                        class="btn-bg"
                                        >{{ $t("domain.create.tip9-1") }}</span
                                    >
                                </div>
                                <div
                                    v-if="validRes.cert_name_gm.msg"
                                    class="tooltips"
                                    :class="{ 'tooltips-error': validRes.cert_name_gm.msg }"
                                >
                                    {{ validRes.cert_name_gm.msg }}
                                </div>
                            </el-form-item>
                            <el-form-item
                                v-if="creatForm.https_status === 'on' && hasCertName"
                                :label="$t('domain.create.https2Switch')"
                                prop="basic_conf.use_http2"
                            >
                                <el-switch
                                    v-model="creatForm.basic_conf.use_http2"
                                    inactive-color="#909399"
                                    :active-value="1"
                                    :inactive-value="0"
                                />
                            </el-form-item>
                            <el-form-item
                                v-if="creatForm.https_status === 'on' && hasCertName"
                                :label="$t('domain.create.tls')"
                                prop="ssl"
                            >
                                <el-tooltip placement="top" :content="$t('domain.create.placeholder2')">
                                    <el-select
                                        style="width: 250px"
                                        v-model="creatForm.ssl"
                                        ref="ssl"
                                        clearable
                                        multiple
                                        :placeholder="$t('domain.create.placeholder2')"
                                        filterable
                                        default-first-option
                                    >
                                        <el-option
                                            v-for="itm in ssl_list"
                                            :key="itm"
                                            :label="itm"
                                            :value="itm"
                                        ></el-option>
                                    </el-select>
                                </el-tooltip>
                            </el-form-item>

                            <!-- HSTS -->
                            <hsts
                                ref="hsts"
                                :datas="creatForm"
                                @onChange="onHstsChange"
                                :requestProtocolIncludesHttps="creatForm.https_status === 'on'"
                                isFcdnCreate
                            >
                            </hsts>
                            <!-- 加密套件 -->
                            <encryption-suite
                                ref="encryptionSuite"
                                :datas="creatForm"
                                @onChange="onEncryptionSuiteChange"
                                :isHttpsStatusOpen="creatForm.https_status === 'on'"
                                :certName="hasCertName"
                                isFcdnCreate
                                :isSm2="!!creatForm.cert_name_gm"
                                :isIntlCert="!!creatForm.cert_name"
                            ></encryption-suite>
                        </div>

                        <div v-show="!isCopyDomain">
                            <p class="label-name">{{ $t("domain.detail.tab6") }}</p>
                            <div>
                                <n-alogic-cache-table
                                    ref="cache"
                                    :productCode="creatForm.product_code"
                                    :filetypeTtl="filetypeTtl"
                                    :isRecreate="isRecreate"
                                    @on-change="getCacheList"
                                ></n-alogic-cache-table>
                                <div class="tooltips">{{ $t("domain.create.tip10") }}</div>
                                <div
                                    v-if="validRes.cache.msg"
                                    class="tooltips"
                                    :class="{ 'tooltips-error': validRes.cache.msg }"
                                >
                                    {{ validRes.cache.msg }}
                                </div>
                            </div>
                            <p class="label-name">{{ $t("domain.create.accessControl") }}</p>
                            <el-form-item :label="$t('domain.create.ip')" prop="ip_switch">
                                <el-switch
                                    v-model="ip_switch"
                                    active-text=""
                                    inactive-text=""
                                    active-value="on"
                                    inactive-value="off"
                                    @change="onIpSwitchChange"
                                />
                            </el-form-item>
                            <div :class="{ 'switch-wrapper': ip_switch === 'on' }">
                                <el-form-item
                                    :label="$t('domain.type')"
                                    prop="ip_type"
                                    v-if="ip_switch === 'on'"
                                >
                                    <n-alogic-ip-block-table
                                        ref="ip_type"
                                        :ipData="ipData"
                                        @onChange="onIpChange"
                                    ></n-alogic-ip-block-table>
                                    <div class="tooltips">
                                        {{ $t("domain.create.tip11") }}
                                    </div>
                                    <div
                                        v-if="validRes.ip_type.msg"
                                        class="tooltips"
                                        :class="{ 'tooltips-error': validRes.ip_type.msg }"
                                    >
                                        {{ validRes.ip_type.msg }}
                                    </div>
                                </el-form-item>
                            </div>
                            <el-form-item :label="$t('domain.create.referer')" prop="referer">
                                <el-switch
                                    v-model="referer"
                                    active-text=""
                                    inactive-text=""
                                    active-value="on"
                                    inactive-value="off"
                                    @change="onRefererSwitchChange"
                                />
                            </el-form-item>
                            <div :class="{ 'switch-wrapper': referer === 'on' }">
                                <!-- 是否允许空referer访问 -->
                                <el-form-item
                                    :label="$t('domain.create.referer2')"
                                    prop="allow_empty"
                                    v-if="referer === 'on'"
                                >
                                    <el-switch
                                        v-model="allow_empty"
                                        active-text=""
                                        inactive-text=""
                                        active-value="on"
                                        inactive-value="off"
                                        @change="onRefererParamsChange"
                                    />
                                </el-form-item>
                                <!-- 是否允许空协议 -->
                                <el-form-item
                                    :label="$t('domain.create.referer3')"
                                    prop="referer_empty_protocol"
                                    v-if="referer === 'on'"
                                >
                                    <el-switch
                                        v-model="referer_empty_protocol"
                                        active-text=""
                                        inactive-text=""
                                        active-value="on"
                                        inactive-value="off"
                                        @change="referer_empty_protocol_change"
                                    />
                                </el-form-item>
                                <div v-if="referer === 'on'">
                                    <!-- 匹配所有端口 -->
                                    <el-form-item
                                        :label="$t('domain.create.referer4')"
                                        prop="match_all_ports"
                                        @change="onRefererParamsChange"
                                    >
                                        <el-switch
                                            v-model="match_all_ports"
                                            active-value="on"
                                            inactive-value="off"
                                        ></el-switch>
                                    </el-form-item>

                                    <!-- 忽略大小写 -->
                                    <el-form-item
                                        :label="$t('domain.create.referer5')"
                                        prop="ignore_case"
                                        @change="onRefererParamsChange"
                                    >
                                        <el-switch
                                            v-model="ignore_case"
                                            active-value="on"
                                            inactive-value="off"
                                        ></el-switch>
                                    </el-form-item>

                                    <!-- 是否追加 -->
                                    <el-form-item prop="is_append">
                                        <span slot="label">
                                            {{ $t("domain.create.referer6") }}
                                            <span>
                                                <el-tooltip
                                                    placement="top"
                                                    :content="$t('domain.create.tip53')"
                                                >
                                                    <ct-svg-icon
                                                        icon-class="question-circle"
                                                        class-name=""
                                                    ></ct-svg-icon>
                                                </el-tooltip>
                                            </span>
                                        </span>
                                        <el-switch
                                            v-model="is_append"
                                            :active-value="1"
                                            :inactive-value="0"
                                            @change="onIsAppendChange"
                                        ></el-switch>
                                    </el-form-item>
                                </div>
                                <!-- 类型 -->
                                <el-form-item
                                    :label="$t('domain.type')"
                                    prop="referer_type"
                                    v-if="referer === 'on'"
                                >
                                    <n-alogic-referer-block-table
                                        ref="referer_type"
                                        :refererData="refererData"
                                        :refererLimit="refererLimit"
                                        @onChange="onRefererChange"
                                    ></n-alogic-referer-block-table>
                                    <div class="tooltips">{{ $t("domain.create.tip12") }}</div>
                                    <div
                                        v-if="validRes.referer_type.msg"
                                        class="tooltips"
                                        :class="{ 'tooltips-error': validRes.referer_type.msg }"
                                    >
                                        {{ validRes.referer_type.msg }}
                                    </div>
                                </el-form-item>
                            </div>
                            <!-- UA黑/白名单 -->
                            <el-form-item :label="$t('domain.create.ua')" prop="ua_switch">
                                <el-switch
                                    v-model="ua_switch"
                                    active-text=""
                                    inactive-text=""
                                    active-value="on"
                                    inactive-value="off"
                                    @change="uaSwitchChange"
                                />
                            </el-form-item>
                            <div :class="{ 'switch-wrapper': ua_switch === 'on' }">
                                <template v-if="ua_switch === 'on'">
                                    <el-form-item :label="$t('domain.create.referer5')" prop="ignore_case">
                                        <el-switch
                                            v-model="creatForm.user_agent.ignore_case"
                                            active-value="on"
                                            inactive-value="off"
                                        ></el-switch>
                                    </el-form-item>
                                    <!-- 匹配方式 -->
                                    <el-form-item :label="$t('domain.create.matchMethod.label')" prop="mode">
                                        <el-radio-group v-model="creatForm.user_agent.mode">
                                            <el-radio :label="1">{{
                                                $t("domain.create.matchMethod.option1")
                                            }}</el-radio>
                                            <el-radio :label="0">{{
                                                $t("domain.create.matchMethod.option2")
                                            }}</el-radio>
                                        </el-radio-group>
                                    </el-form-item>
                                </template>
                                <el-form-item
                                    :label="$t('domain.type')"
                                    prop="ua_type"
                                    v-if="ua_switch === 'on'"
                                >
                                    <n-alogic-ua-block-table
                                        ref="ua_type"
                                        :uaData="uaData"
                                        @onChange="onUaChange"
                                    ></n-alogic-ua-block-table>
                                    <div
                                        v-if="validRes.ua_type.msg"
                                        class="tooltips"
                                        :class="{ 'tooltips-error': validRes.ua_type.msg }"
                                    >
                                        {{ validRes.ua_type.msg }}
                                    </div>
                                </el-form-item>
                            </div>

                            <!-- URL黑白名单 -->
                            <el-form-item :label="$t('domain.create.uri')" prop="ua_switch">
                                <el-switch
                                    v-model="uri_switch"
                                    active-text=""
                                    inactive-text=""
                                    active-value="on"
                                    inactive-value="off"
                                />
                            </el-form-item>
                            <div :class="{ 'switch-wrapper': uri_switch === 'on' }">
                                <el-form-item
                                    :label="$t('domain.type')"
                                    prop="uri_type"
                                    v-if="uri_switch === 'on'"
                                >
                                    <n-alogic-uri-block-table
                                        ref="uri_type"
                                        :uriData="uriData"
                                        @onChange="onUriChange"
                                    ></n-alogic-uri-block-table>
                                    <div
                                        v-if="validRes.uri_type.msg"
                                        class="tooltips"
                                        :class="{ 'tooltips-error': validRes.uri_type.msg }"
                                    >
                                        {{ validRes.uri_type.msg }}
                                    </div>
                                </el-form-item>
                            </div>
                        </div>
                    </div>
                </el-form>
            </div>
        </section>
        <cute-fixed-footer class="submit">
            <div class="footer-content">
                <el-button :type="submitDesc.type" @click="submit" :loading="submitLoading" size="medium">
                    {{ submitDesc.text }}
                </el-button>
            </div>
        </cute-fixed-footer>
        <!-- 展示校验结果 -->
        <validation-modal
            v-model="showValidationModal"
            :validation-error="validateArrFromBe"
            :title="validationTitle"
            @input="closeModal"
        />
        <UploadCertDialog
            :addVisible="addVisible"
            :loading="loading"
            :certificate="addCertificate"
            :isFromCertList="false"
            :dataList="[]"
            @cancel="cancel"
            @submit="submitCertificate"
        />
        <origin-dialog
            :dialogVisible="originDialogVisible"
            :originForm="originForm"
            :from="currentType"
            :accelerate-domains="(isBatchCreate ? domains : [creatForm.domain] || []).filter(itm => !!itm)"
            :originList="creatForm.origin"
            :currenIndex="String(currenIndex)"
            :productCode="creatForm.product_code"
            :useEcgw="1"
            :isBatch="false"
            @cancel="cancelOrigin"
            @submit="submitOrigin"
            @bucketHandle="onBucketHandle"
        />
    </section>
</template>

<script>
// import OrderformEt from "@cdn/components/autoform";
import ValidationModal from "./ValidationModal";
import { nUserModule } from "@/store/modules/nuser";
import UploadCertDialog from "@/views/certificate/components/UploadCertDialog.vue";
import { getDefaultCertParam } from "@/views/certificate/util";
import { ctFetch, getLang } from "@/utils";
import { N_URL_FORM_OPERATION_SUBMIT, nOrderUrl, nCertificateUrl, nBasicConfig } from "@/config/url";
import minxin from "@/components/simpleform/minxin";
import { ipv4, ip, domain } from "@cdnplus/common/config/pattern";
import { NEW_PREFIX } from "@/config/url/_PREFIX";
import { nDomainUrl } from "@/config/url";
import OriginDialog from "@/views/ncdn/ndomain/batchEdit/OriginDialog.vue";
export const CertSelectorUrl = NEW_PREFIX + "/cert/selector";
import hsts from "@/views/domainConfig/basicConfig/config-components/hsts/index.vue";
import encryptionSuite from "@/views/domainConfig/basicConfig/config-components/encryptionSuite/index.vue";
import { commonLinks } from "@/utils/logic/url";
import ctSvgIcon from "@/components/ctSvgIcon";
import { isStaticProduct } from "@/utils/product";
import { StatisticsModule } from "@/store/modules/statistics";
import { DomainModule } from "@/store/modules/domain";
import { cloneDeep } from "lodash-es";
import { omit } from "lodash-es";
import { checkReqHost } from "@/utils/pattern";
import CdnHttpsSupportTip from "@/views/domainConfig/components/cdnHttpsSupportTip.vue";
import { reservedIp } from "@/config/npattern";
import HostSelectMod from "@/components/hostSelect/hostSelect.mod.vue";

const weightPattern = "^([1-9][0-9]?|100)$";

const weightReg = new RegExp(weightPattern);
const urlReg = new RegExp(domain);
const reservedIpReg = new RegExp(reservedIp); // ipv4 中的保留 ip 地址

// const defaultOrigin = {
//     origin: "",
//     weight: "10",
//     role: "master",
// };

function debounce(func, delay, context) {
    let timeoutId;
    return function(...args) {
        const ctx = context || this;
        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => {
            func.apply(ctx, args);
        }, delay);
    };
}
export default {
    components: {
        ValidationModal,
        UploadCertDialog,
        OriginDialog,
        hsts,
        encryptionSuite,
        ctSvgIcon,
        CdnHttpsSupportTip,
        HostSelectMod,
    },
    mixins: [minxin],
    data() {
        return {
            domainValidating: true,
            isDomainEndsWithCtyun: false,
            batchCreateLimit: 20, // 批量新增的域名的数量限制
            refererLimit: 400, // 白名单上限
            isBatchCreate: this.$route.query.type === "batch",
            xosMap: {
                0: this.$t("domain.create.xos0"),
                1: this.$t("domain.create.xos1"),
                2: this.$t("domain.create.zos.tip1"),
            },
            refererObjectModel: {},
            operationId: this.$route.query.operationId,
            items: {},
            namespace: "",
            addVisible: false,
            addCertificate: getDefaultCertParam(),
            submitLoading: false,
            submitDesc: {
                text: this.$t("domain.create.title"),
                type: "primary",
            },
            config: {
                // operationId: this.$route.query.operationId || "100000000",
                workspaceId: this.$route.query.workspaceId,
                orderId: this.$route.query.orderId,
                signature: this.$route.query.signature,
            },
            signature: "",
            targetDomain: "",
            loading: false,
            showValidationModal: false,
            validateArrFromBe: [],
            validationTitle: "",
            orderInfo: null,
            handleModalType: false,
            ip_switch: "off",
            // ip_type: "",
            referer: "off",
            allow_empty: "on",
            referer_empty_protocol: "off",
            match_all_ports: "off",
            ignore_case: "off",
            is_append: 0,
            ua_switch: "off",
            uri_switch: "off",
            filetypeTtl: [],
            domains: [""],
            allDomainList: [],
            allDomainListLoading: false,
            isHttpsSupport: true,

            creatForm: {
                ip_black_list: "",
                ip_white_list: "",
                domain: "",
                product_code: "",
                area_scope: "",
                ipv6_switch: 1,
                acce_config: "custom",
                target_domain: "",
                origin: [
                    // {
                    //     origin: "",
                    //     role: "master",
                    //     weight: "10",
                    // },
                ],
                backorigin_protocol: "http",
                origin_port: "",
                req_host: "",
                https_status: "off",
                ssl: [],
                hsts: {
                    switch: 0,
                    max_age: 1200,
                    include_sub_domains: null,
                },
                cert_name: "",
                cert_name_gm: "",
                basic_conf: {
                    use_http2: 0,
                    https_origin_port: 443,
                    http_origin_port: 80,
                },
                referer_type: "",
                origin_policy: {
                    http_config: {
                        origin_type: "fastest_simple",
                    },
                },
                filetype_ttl: [],
                user_agent: {
                    type: "",
                    ua: [],
                    ignore_case: "on",
                    mode: 1,
                },
                uri_deny: {
                    type: "",
                    uri: [],
                },
                xos_origin_is: 0,
                origin_host_http: {},
                follow_request_backport: 0,
                ssl_ciphers: "", // 加密套件
                custom_ssl_ciphers: [], // 自定义加密套件
                proxy_gmssl_mode: "",
            },
            roleMap: {
                master: this.$t("domain.create.primary"),
                slave: this.$t("domain.create.secondary"),
            },
            cert_name_list: [],
            cert_name_gm_list: [],
            lastSelectedCertName: "",
            lastSelectedCertNameGm: "",
            validRes: {
                domain: {
                    valid: true,
                    msg: "",
                },
                origin: {
                    valid: true,
                    msg: "",
                },
                origin_port: {
                    valid: true,
                    msg: "",
                },
                product_code: {
                    valid: true,
                    msg: "",
                },
                area_scope: {
                    valid: true,
                    msg: "",
                },
                ip_type: {
                    valid: true,
                    msg: "",
                },
                referer_type: {
                    valid: true,
                    msg: "",
                },
                req_host: {
                    valid: true,
                    msg: "",
                },
                cert_name: {
                    valid: true,
                    msg: "",
                },
                cert_name_gm: {
                    valid: true,
                    msg: "",
                },
                cache: {
                    valid: true,
                    msg: "",
                },
                domain_type: {
                    valid: true,
                    msg: "",
                },
                ua_type: {
                    valid: true,
                    msg: "",
                },
                uri_type: {
                    valid: true,
                    msg: "",
                },
                target_domain: {
                    valid: true,
                    msg: "",
                },
            },
            isRecreate: !!this.$route.query.orderId,
            refererData: null,
            ipData: null,
            uaData: null,
            uriData: null,
            cert_name_cache: "", // 提交保存时用来存放证书名缓存的
            cert_name_loading: false, // 证书列表加载状态
            creatFormCache: {
                // 重新发起时，初始化回填数据的缓存
                basic_conf: {
                    http_origin_port: 80,
                    https_origin_port: 443,
                },
                cert_name: "",
                cert_name_gm: "",
            },
            isVivo: false,
            originDialogVisible: false,
            currentType: "create",
            currenIndex: "",
            originForm: { origin: "", role: "master", weight: "10", is_xos: 0, origin_host: "" },
            temp_bucket: "",
            reshow: true,
        };
    },
    // provide() {
    // return {
    // 当前页面为创建域名
    // isCreate: true,
    // 是否为重新发起，如果存在工单 id 则认为是重新发起
    // isRecreate: !!this.$route.query.orderId,
    // };
    // },
    async mounted() {
        if (DomainModule.createDomainStage.isFromDetail) {
            Object.keys(DomainModule.createDomainStage.data).forEach(key => {
                this.$set(this.$data, key, DomainModule.createDomainStage.data[key]);
            });
            return;
        }
        this.getDomainList();
        // 确保this.list有值后，处理option
        setTimeout(async () => {
            this.renderDomainTypeOptions();
            // 重新发起功能已下线
            if (this.$route.query.orderId) {
                this.submitDesc = {
                    text: this.$t("domain.create.resubmit"),
                    type: "primary",
                };
                await this.getInfoData();
                // 如果是重新发起，则默认可用证书列表请求
                if (this.$route.query.orderId) {
                    this.getCertList(this.creatFormCache.domain);
                }
            }
        }, 600);
        this.checkBatcCreate();
        // 是否vivo客户
        const res = await this.$ctFetch(nDomainUrl.domainIsVivo);
        this.isVivo = res.vivo;
    },
    beforeDestroy() {
        // 清空参照域名相关状态
        DomainModule.SET_CREATE_DOMAIN_STATE_DATA({ key: "isFromDetail", data: false });
    },
    computed: {
        ssl_list() {
            const intl = ["TLSv1", "TLSv1.1", "TLSv1.2", "TLSv1.3"];
            const sm2 = ["GMTLSv1.1"];
            const list = [];

            if (this.creatForm.cert_name) {
                list.push(...intl);
            }

            if (this.creatForm.cert_name_gm) {
                list.push(...sm2);
            }

            return list;
        },
        hasCertName() {
            return this.creatForm.cert_name || this.creatForm.cert_name_gm;
        },
        /**
         * 可选择的参考域名列表
         */
        copyDomainList() {
            const isStatic = isStaticProduct(this.creatForm.product_code);
            return this.allDomainList
                .filter(itm => {
                    // 已启用的域名 或者 配置中（更新）
                    return (
                        itm.enable === "true" &&
                        (itm.status === 4 || (itm.status === 3 && [5, 6, 7].includes(itm.action)))
                    );
                })
                .filter(itm => {
                    // 根据当前域名的产品类型过滤
                    if (!this.creatForm.product_code) return true;
                    if (isStatic) return isStaticProduct(itm.productCode);
                    else {
                        if (this.creatForm.domain_type === "006") {
                            return !itm.subProductCode && itm.productCode === this.creatForm.product_code;
                        }
                        return (
                            !isStaticProduct(itm.productCode) &&
                            itm.subProductCode === this.creatForm.domain_type
                        );
                    }
                });
        },
        isEn() {
            return getLang() === "en";
        },
        targetDomainCreate() {
            return StatisticsModule.targetDomainCreate;
        },
        wholeStationFilterProducts() {
            const whole_station_products = ["006", "104", "105"];
            return this.domainTypeMap?.filter(item => whole_station_products?.includes(item.id));
        },
        // 新用户 & 产品为 cdn加速
        isNewUserAndIsCdn() {
            return (
                nUserModule.isNewUser &&
                this.creatForm?.product_code === "008" &&
                nUserModule.userInfo.saleChannel === "2"
            );
        },
        // 泛静态产品
        isOld() {
            // // "014" 去掉文案展示
            return /^(001|003|004)$/.test(this.productCode);
        },
        isCtclouds() {
            return nUserModule.isCtclouds;
        },
        isBcp() {
            return nUserModule.isBcp;
        },
        isVip() {
            return nUserModule.isVip;
        },
        isCtyun() {
            return nUserModule.isCtyun;
        },
        ipReg() {
            const useIpv6 = Number(this.creatForm.ipv6_switch);
            return new RegExp(useIpv6 ? ip : ipv4);
        },
        // 是否选择参照域名
        isCopyDomain() {
            return this.creatForm.acce_config === "copy" && this.targetDomainCreate;
        },
        showAreaScope() {
            return (
                ["001", "003", "004", "008", "014"].indexOf(this.creatForm.product_code) > -1 ||
                (this.creatForm.product_code === "006" && this.creatForm.domain_type === "006")
            );
        },
        submitUrlWithQuery() {
            const { config } = this;
            const query = Object.keys(config)
                .filter(name => config[name])
                .map(name => `${name}=${config[name]}`)
                .join("&");
            return `${N_URL_FORM_OPERATION_SUBMIT}?${query}`;
        },
        orderRouter() {
            const { config, orderInfo } = this;
            // 拿不到工单信息时，跳到列表页
            const result = {
                name: "ndomain.list",
                query: {
                    workspaceId: config.workspaceId,
                },
            };
            // if (orderInfo) {
            //     // const operationIdMap = { "005": "100005002", "006": "100006002" };
            //     // const productType = this.params.data.attr
            //     //     ?.find(item => item.pId === "product_code")
            //     //     ?.pValue.split("=")[1];
            //     result = {
            //         name: "norder.detail",
            //         query: {
            //             workspaceId: config.workspaceId,
            //             orderId: orderInfo.orderId,
            //             signature: orderInfo.signature,
            //         },
            //     };
            // }
            return result;
        },
        orderLink() {
            return commonLinks.orderLink;
        },
        documentOriginLink() {
            return this.isCtclouds
                ? nUserModule.lang === "zh"
                    ? "https://www.esurfingcloud.com/document/zh-cn/10015932/20688145"
                    : "https://www.esurfingcloud.com/document/10015932/20688145"
                : "https://www.ctyun.cn/document/10015932/10639779";
        },
        rangeOriginLink() {
            return this.isCtclouds
                ? nUserModule.lang === "zh"
                    ? "https://www.esurfingcloud.com/document/zh-cn/10015932/20688146"
                    : "https://www.esurfingcloud.com/document/10015932/20688146"
                : "https://www.ctyun.cn/document/10015932/10639835";
        },
        // 全站加速点击跳转链接
        integratedCdnLink() {
            return "https://www.ctyun.cn/document/10006847/10024025";
        },
        // 上传加速点击跳转链接
        uploadAccelerationLink() {
            return "https://www.ctyun.cn/document/10006847/10094554";
        },
        // websocket加速点击跳转链接
        webSocketAccelerationLink() {
            return "https://www.ctyun.cn/document/10006847/10094555";
        },
    },
    watch: {
        $route: {
            handler(val) {
                if (val.query.operationId === "isRepeat") {
                    this.operationId = "isRepeat";
                }
            },
        },
        ssl_list(list) {
            if (this.creatForm.ssl?.length) {
                this.creatForm.ssl = this.creatForm.ssl.filter(item => list.includes(item));
            }
        },
    },
    methods: {
        /**
         * 证书的值改变时，需要将: 加密套件和自定义加密套件值清空
         */
        async cert_name_change(val, isSm2 = false) {
            this.creatForm.ssl_ciphers = "";
            this.creatForm.custom_ssl_ciphers = [];

            const certList = isSm2 ? this.cert_name_gm_list : this.cert_name_list;
            const certName = isSm2 ? "cert_name_gm" : "cert_name";
            const lastSelectedCertName = isSm2 ? this.lastSelectedCertNameGm : this.lastSelectedCertName;

            const selected = certList.find(cert => cert.cert_name === val);
            if (!selected || !val) {
                this[lastSelectedCertName] = "";
                return;
            }

            if (!selected.is_chain_complete) {
                try {
                    this.creatForm[certName] = lastSelectedCertName;
                    await this.$confirm(
                        `${this.$t(
                            "domain.您使用的证书存在证书链不完整的情况，存在一定安全隐患，如您仍需提交则点击“继续”按钮进行提交，如您想取消操作则点击“取消”按钮。"
                        )}`,
                        `${this.$t("certificate.chain.title")}`,
                        {
                            confirmButtonText: `${this.$t("certificate.chain.ensure")}`,
                            cancelButtonText: `${this.$t("common.dialog.cancel")}`,
                            showClose: false,
                            iconClass: "el-icon-warning",
                        }
                    );
                    this.creatForm[certName] = selected.cert_name;
                    this[lastSelectedCertName] = this.creatForm[certName];
                } catch (err) {
                    this.creatForm[certName] = lastSelectedCertName;
                } finally {
                    this.$refs[certName].blur();
                }
            } else {
                this[lastSelectedCertName] = this.creatForm[certName];
            }
        },
        async getDomainList() {
            this.allDomainListLoading = true;
            const rst = await this.$ctFetch(nDomainUrl.domainList, {
                data: {
                    pageIndex: 1,
                    pageSize: 1000,
                },
            }).catch(() => {
                this.allDomainListLoading = false;
            });
            this.allDomainList = rst?.list || [];
            this.allDomainListLoading = false;
        },
        /**
         * 跳转到域名详情页，同时需要保存当前配置信息
         */
        gotoDomainDetail(e) {
            e.preventDefault();
            if (!this.creatForm.target_domain) {
                this.$message.error(this.$t("domain.create.copy.tip2"));
                return;
            }

            DomainModule.SET_CREATE_DOMAIN_STATE_DATA({ key: "data", data: cloneDeep(this.$data) });
            const domainInfo = this.allDomainList.find(itm => itm.domain === this.creatForm.target_domain);
            this.$router.push({
                name: "ndomain.detail",
                query: {
                    workspaceId: this.config.workspaceId,
                    domain: this.creatForm.target_domain,
                    status: domainInfo.status,
                    from: "copy",
                },
            });
        },
        onHstsChange(val) {
            if (!val) return;
            this.creatForm.hsts.switch = val?.switch;
            this.creatForm.hsts.max_age = val?.max_age;
            this.creatForm.hsts.include_sub_domains = val?.include_sub_domains || null;
        },
        onEncryptionSuiteChange(val) {
            if (!val) return;
            this.creatForm.ssl_ciphers = val?.ssl_ciphers;
            this.creatForm.custom_ssl_ciphers = val?.custom_ssl_ciphers;
        },
        deleteDomain(index) {
            this.domains.splice(index, 1);
            this.domainValidating = false;
        },
        addDomain() {
            this.domains.push("");
        },
        async getInfoData() {
            this.loading = true;
            const data = await this.$ctFetch(nOrderUrl.orderDetail, {
                data: {
                    ...this.$route.query,
                },
            });
            this.loading = false;
            this.creatFormCache = {
                ...data.cdnCfg,
            };
            this.filetypeTtl = this.creatFormCache.filetype_ttl;

            for (const prop in data.cdnCfg) {
                // 不回填域名
                if (prop !== "domain") {
                    this.creatForm[prop] = data.cdnCfg[prop];
                }
            }

            if (data.cdnCfg?.black_referer || data.cdnCfg?.white_referer) {
                this.referer = "on";
                this.allow_empty =
                    data.cdnCfg?.black_referer?.allow_empty || data.cdnCfg?.white_referer?.allow_empty;
                // 是否允许空协议
                this.referer_empty_protocol =
                    data.cdnCfg?.black_referer?.referer_empty_protocol ||
                    data.cdnCfg?.white_referer?.referer_empty_protocol;
                this.match_all_ports =
                    data.cdnCfg?.black_referer?.match_all_ports ||
                    data.cdnCfg?.white_referer?.match_all_ports;
                this.ignore_case =
                    data.cdnCfg?.black_referer?.ignore_case || data.cdnCfg?.white_referer?.ignore_case;
                this.is_append =
                    data.cdnCfg?.black_referer?.is_append || data.cdnCfg?.white_referer?.is_append;
                this.refererData = {
                    type: data.cdnCfg?.black_referer ? "block" : "allow",
                    domainList: data.cdnCfg?.black_referer
                        ? data.cdnCfg?.black_referer?.allow_list?.join("\n")
                        : data.cdnCfg?.white_referer?.allow_list?.join("\n"),
                };
            }
            if (data.cdnCfg?.ip_black_list || data.cdnCfg?.ip_white_list) {
                this.ip_switch = "on";
                this.ipData = {
                    type: data.cdnCfg?.ip_black_list ? "block" : "allow",
                    ip: data.cdnCfg?.ip_black_list ? data.cdnCfg?.ip_black_list : data.cdnCfg?.ip_white_list,
                };
            }
            if (data.cdnCfg?.user_agent) {
                this.ua_switch = "on";
                this.uaData = {
                    type: data.cdnCfg?.user_agent?.type,
                    ua: data.cdnCfg?.user_agent?.ua,
                    ignore_case: data.cdnCfg?.user_agent?.ignore_case,
                    mode: data.cdnCfg?.user_agent?.mode,
                };
            }
        },
        onProductCodeChange(val) {
            if (val === "006") {
                // 非全站产品类型默认为"hash" 全站产品类型默认 "fastest_simple"
                this.creatForm.origin_policy = {
                    http_config: {
                        origin_type: "fastest_simple",
                    },
                };
                // 全站加速不支持媒体存储源站配置
                // if (this.creatForm.origin.some(item => item.is_xos === 1)) {
                //     this.$message(this.$t("domain.create.tip47"));
                // }
            } else {
                // 如果是CDN加速，需要判断是否支持开启HTTPS
                if (val === "008" && this.isHttpsSupport) {
                    this.creatForm.https_status = "off";
                }

                this.creatForm.origin_policy = {
                    http_config: {
                        origin_type: "hash",
                    },
                };
            }

            if (this.isCopyDomain && this.creatForm.target_domain) {
                const domainInfo = this.allDomainList.find(
                    itm => itm.domain === this.creatForm.target_domain
                );
                if (isStaticProduct(domainInfo.productCode) !== isStaticProduct(val))
                    this.creatForm.target_domain = "";
            }

            // 避免参照域名跳转回来回显后导致加速区域被清空
            if (!DomainModule.createDomainStage.isFromDetail) {
                this.creatForm.area_scope = "";
            }
        },
        onPortChange(val) {
            this.creatForm.basic_conf.http_origin_port = val.http_origin_port;
            this.creatForm.basic_conf.https_origin_port = val.https_origin_port;
        },
        onFollowRequestBackportChange(val) {
            this.creatForm.follow_request_backport = val;
        },
        onIpSwitchChange(val) {
            this.creatForm.ip_black_list = "";
            this.creatForm.ip_white_list = "";
        },
        onIpChange(val) {
            if (this.ip_switch === "on" && val.type === "block") {
                this.creatForm.ip_black_list = val.ip;
                delete this.creatForm.ip_white_list;
            } else if (this.ip_switch === "on" && val.type === "allow") {
                this.creatForm.ip_white_list = val.ip;
                delete this.creatForm.ip_black_list;
            }
        },
        // 是否允许空referer访问 change事件
        onRefererSwitchChange() {
            this.allow_empty = "on";
            this.referer_empty_protocol = "off";
            this.match_all_ports = "off";
            this.ignore_case = "off";
            this.is_append = 0;
            this.refererObjectModel = { type: "allow", domainList: "" };
            delete this.creatForm.black_referer;
            delete this.creatForm.white_referer;
        },
        allow_empty_change(val) {
            if (this.referer === "on" && this.refererObjectModel.type === "allow") {
                this.creatForm.white_referer = {
                    allow_empty: val,
                    referer_empty_protocol: this.referer_empty_protocol,
                    match_all_ports: this.match_all_ports,
                    ignore_case: this.ignore_case,
                    is_append: this.is_append,
                    allow_list:
                        (this.refererObjectModel.domainList.length > 0 &&
                            this.refererObjectModel.domainList.split(",")) ||
                        [],
                };
                delete this.creatForm.black_referer;
            }
            if (this.referer === "on" && this.refererObjectModel.type === "block") {
                this.creatForm.black_referer = {
                    allow_empty: val,
                    referer_empty_protocol: this.referer_empty_protocol,
                    match_all_ports: this.match_all_ports,
                    ignore_case: this.ignore_case,
                    is_append: this.is_append,
                    allow_list:
                        (this.refererObjectModel.domainList.length > 0 &&
                            this.refererObjectModel.domainList.split(",")) ||
                        [],
                };
                delete this.creatForm.white_referer;
            }
        },
        // 是否允许空协议 change事件
        referer_empty_protocol_change(val) {
            if (this.referer === "on" && this.refererObjectModel.type === "allow") {
                this.creatForm.white_referer = {
                    allow_empty: this.allow_empty,
                    referer_empty_protocol: val,
                    match_all_ports: this.match_all_ports,
                    ignore_case: this.ignore_case,
                    is_append: this.is_append,
                    allow_list:
                        (this.refererObjectModel.domainList.length > 0 &&
                            this.refererObjectModel.domainList.split(",")) ||
                        [],
                };
                delete this.creatForm.black_referer;
            }
            if (this.referer === "on" && this.refererObjectModel.type === "block") {
                this.creatForm.black_referer = {
                    allow_empty: this.allow_empty,
                    referer_empty_protocol: val,
                    match_all_ports: this.match_all_ports,
                    ignore_case: this.ignore_case,
                    is_append: this.is_append,
                    allow_list:
                        (this.refererObjectModel.domainList.length > 0 &&
                            this.refererObjectModel.domainList.split(",")) ||
                        [],
                };
                delete this.creatForm.white_referer;
            }
        },
        onRefererChange(val) {
            this.refererObjectModel = JSON.parse(JSON.stringify(val));
            if (this.referer === "on" && val.type === "allow") {
                this.creatForm.white_referer = {
                    allow_empty: this.allow_empty,
                    referer_empty_protocol: this.referer_empty_protocol,
                    match_all_ports: this.match_all_ports,
                    ignore_case: this.ignore_case,
                    is_append: this.is_append,
                    allow_list: (val.domainList.length > 0 && val.domainList.split(",")) || [],
                };
                delete this.creatForm.black_referer;
            }
            if (this.referer === "on" && val.type === "block") {
                this.creatForm.black_referer = {
                    allow_empty: this.allow_empty,
                    referer_empty_protocol: this.referer_empty_protocol,
                    match_all_ports: this.match_all_ports,
                    ignore_case: this.ignore_case,
                    is_append: this.is_append,
                    allow_list: (val.domainList.length > 0 && val.domainList.split(",")) || [],
                };
                delete this.creatForm.white_referer;
            }
        },
        uaSwitchChange(val) {
            if (val === "off") {
                this.creatForm.user_agent = {
                    type: "",
                    ua: [],
                    ignore_case: "on",
                    mode: 1,
                };
            }
        },
        onRefererParamsChange() {
            if (this.referer === "on") {
                if (this.refererObjectModel.type === "allow") {
                    this.creatForm.white_referer = {
                        allow_empty: this.allow_empty,
                        referer_empty_protocol: this.referer_empty_protocol,
                        match_all_ports: this.match_all_ports,
                        ignore_case: this.ignore_case,
                        is_append: this.is_append,
                        allow_list:
                            (this.refererObjectModel.domainList.length > 0 &&
                                this.refererObjectModel.domainList.split(",")) ||
                            [],
                    };
                    delete this.creatForm.black_referer;
                } else {
                    this.creatForm.black_referer = {
                        allow_empty: this.allow_empty,
                        referer_empty_protocol: this.referer_empty_protocol,
                        match_all_ports: this.match_all_ports,
                        ignore_case: this.ignore_case,
                        is_append: this.is_append,
                        allow_list:
                            (this.refererObjectModel.domainList.length > 0 &&
                                this.refererObjectModel.domainList.split(",")) ||
                            [],
                    };
                    delete this.creatForm.white_referer;
                }
            }
        },
        onIsAppendChange(val) {
            val && (this.$refs.referer_type.objectModel.domainList = "");
            this.onRefererParamsChange();
        },
        onUaChange(val) {
            if (this.ua_switch === "on") {
                this.creatForm.user_agent.type = val.type;
                this.creatForm.user_agent.ua = val.ua?.split(",") || [];
            }
        },
        onUriChange(val) {
            if (this.uri_switch === "on") {
                this.creatForm.uri_deny.type = val.type;
                this.creatForm.uri_deny.uri = val.uri?.split(",") || [];
            }
        },
        onDomainChange(val, valid, i) {
            if (!this.isBatchCreate) {
                this.creatForm.domain = val;
                if (valid) {
                    // 如果是由于提交域名触发的校验，不清空证书名
                    if (!this.submitLoading) {
                        this.creatForm.cert_name = "";
                        this.creatForm.cert_name_gm = "";
                    }
                    this.getCertList("", this.submitLoading);
                }
            } else {
                // 批量处理逻辑
                this.$set(this.domains, i, val); // 单个选择，用 value 存
                if (valid) {
                    if (!this.submitLoading) {
                        this.creatForm.cert_name = "";
                        this.creatForm.cert_name_gm = "";
                    }
                    this.domainValidating = false;
                    debounce(this.getCertList(this.domains), 1000);
                }
            }
        },
        onDomainEndsWithCtyun(data) {
            this.isDomainEndsWithCtyun = data;
        },
        onAcceAreaChange(val) {
            this.creatForm.area_scope = Number(val);
        },
        onBackoriginProtocolChange() {
            // 切换回源协议重置回源加密算法
            this.creatForm.proxy_gmssl_mode = "";
            this.$nextTick(async () => {
                // 切换回源协议后重置回源端口校验结果
                const { dom, valid, msg } = await this.$refs["origin_port"].validateProcedure();
                this.validRes[dom] = { valid, msg };
            });
        },
        // 源站校验
        validateOrigin() {
            const result = {
                valid: true,
                msg: "",
                dom: "origin",
            };
            let hasMaster = false;
            const originMap = {};
            const { ipReg } = this;

            // 源站地址不能和加速域名重复
            if (
                this.creatForm.domain &&
                this.creatForm.origin.some(o => o.origin === this.creatForm.domain)
            ) {
                result.valid = false;
                result.msg = this.$t("domain.create.tip13");
                return Promise.resolve(result);
            }

            for (let index = 0; index < this.creatForm.origin.length; index += 1) {
                const element = this.creatForm.origin[index];
                //地址唯一
                if (originMap[element.origin]) {
                    result.valid = false;
                    result.msg = this.$t("domain.create.tip14");
                    return Promise.resolve(result);
                } else {
                    originMap[element.origin] = true;
                }

                if (element.role === "master") {
                    hasMaster = true;
                }

                if (!ipReg.test(element.origin) && !urlReg.test(element.origin)) {
                    result.valid = false;
                    result.msg = this.$t("domain.create.tip15");
                } else if (reservedIpReg.test(element.origin)) {
                    result.valid = false;
                    result.msg = this.$t("domain.create.tip16");
                }
                // 校验权重为1-100的整数，注：当开启高级源的时候才有必要校验权限字段
                if (!weightReg.test(element.weight)) {
                    result.valid = false;
                    result.msg = this.$t("domain.create.tip17");
                }

                if (!result.valid) {
                    return Promise.resolve(result);
                }
            }
            if (!hasMaster) {
                result.valid = false;
                result.msg = this.$t("domain.create.tip18");
            }
            return Promise.resolve(result);
        },
        // 回源host校验
        validateReqhost() {
            const result = {
                valid: true,
                msg: "",
                dom: "req_host",
            };
            if (this.creatForm.req_host && !checkReqHost(this.creatForm.req_host)) {
                result.valid = false;
                result.msg = this.$t("domain.create.tip19");
            } else if (this.creatForm.origin.some(o => o.origin_host) && this.creatForm.req_host) {
                result.valid = false;
                result.msg = this.$t("domain.create.tip20");
            }
            return Promise.resolve(result);
        },
        // 证书备注名校验
        validateCert() {
            const result = {
                valid: true,
                msg: "",
                dom: "cert_name",
            };
            if (!this.hasCertName) {
                result.valid = false;
                result.msg = this.$t("certificate.国际标准证书和国密证书至少要有一种有配置");
            }
            return Promise.resolve(result);
        },
        validateCertGm() {
            const result = {
                valid: true,
                msg: "",
                dom: "cert_name_gm",
            };
            if (!this.hasCertName) {
                result.valid = false;
                result.msg = this.$t("certificate.国际标准证书和国密证书至少要有一种有配置");
            }
            return Promise.resolve(result);
        },
        validateProductCode() {
            const res = {
                valid: true,
                dom: "product_code",
                msg: "",
            };
            if (!this.creatForm.product_code) {
                res.valid = false;
                res.msg = this.$t("domain.create.tip22");
            }
            // 全站加速不支持媒体存储源站配置
            // if (this.creatForm.product_code === "006" &&
            //     this.creatForm.origin.some(item => item.is_xos === 1)
            // ) {
            //     res.valid = false;
            //     res.msg = this.$t("domain.create.tip47");
            // }
            return Promise.resolve(res);
        },
        validateAcce() {
            const res = {
                valid: true,
                msg: "",
                dom: "area_scope",
            };
            if (!this.creatForm.area_scope) {
                res.valid = false;
                res.msg = this.$t("domain.create.tip23");
            }
            return Promise.resolve(res);
        },
        validateDomainType() {
            const res = {
                valid: true,
                msg: "",
                dom: "domain_type",
            };
            if (!this.creatForm.domain_type) {
                res.valid = false;
                res.msg = this.$t("domain.create.tip24");
            }
            return Promise.resolve(res);
        },
        handleReady(data) {
            this.namespace = data.namespace;
            this.sortedIdArr = data.sortedIdArr;
        },
        async submit() {
            // 国际站实名认证
            // 1. 加速区域选择中国内地、全球需提示
            // 2. 全站的两个子产品要进行实名校验(全站加速-上传加速、全站加速-websocket加速)
            if (this.isCtclouds && nUserModule.userInfo.realname !== "authzed") {
                if (this.creatForm.area_scope === 1 || this.creatForm.area_scope === 3) {
                    this.$message.error(this.$t("domain.create.tip25"));
                    return;
                }
                // "105" 全站加速-websocket加速 "104" 全站加速-上传加速
                if (
                    this.creatForm.product_code === "006" &&
                    (this.creatForm.domain_type === "104" || this.creatForm.domain_type === "105")
                ) {
                    this.$message.error(this.$t("domain.create.tip25"));
                    return;
                }
            }
            let body = {},
                promises = [];
            if (this.operationId === "isRepeat") {
                body = {
                    domain: this.creatForm.domain,
                    product_code: this.creatForm.product_code,
                    area_scope: this.creatForm.area_scope,
                    domain_type: this.creatForm.domain_type,
                };
                promises = [this.$refs["domain"].validateProcedure(), this.validateProductCode()];
                if (this.showAreaScope) {
                    promises.push(this.validateAcce());
                }
                if (this.creatForm.product_code === "006") {
                    promises.push(this.validateDomainType());
                }
            } else {
                body = {
                    ...this.creatForm,
                };
                this.submitLoading = true;
                // 需要主动触发子组件内的 validateProcedure 函数
                this.cert_name_cache = this.creatForm.cert_name; // 因触发 domain validateProcedure时会重置cert_name，所以这里加一个缓存
                this.cert_name_gm_cache = this.creatForm.cert_name_gm; // 因触发 domain validateProcedure时会重置cert_name_gm，所以这里加一个缓存
                if (!this.isBatchCreate) {
                    promises = [this.$refs["domain"].validateProcedure(), this.validateProductCode()];
                } else {
                    // 批量
                    this.$refs["domains"].forEach(data => {
                        promises.push(data.validateProcedure());
                    });
                    promises.push(this.validateProductCode());
                }
                if (this.showAreaScope) {
                    promises.push(this.validateAcce());
                }
                if (this.creatForm.product_code === "006") {
                    promises.push(this.validateDomainType());
                }
                promises.push(this.$refs["origin_port"].validateProcedure());
                promises.push(this.validateOrigin());
                promises.push(this.validateReqhost());
                if (this.creatForm.https_status === "on") {
                    promises.push(this.validateCert());
                    promises.push(this.validateCertGm());
                    if (this.creatForm.hsts?.switch === 1) {
                        promises.push(this.$refs["hsts"]?.validateProcedure());
                    }
                    if (this.hasCertName && this.creatForm?.ssl_ciphers === "custom") {
                        promises.push(this.$refs["encryptionSuite"]?.validateProcedure());
                    }
                }

                if (!this.isCopyDomain) {
                    promises.push(this.$refs["cache"].validateProcedure());
                    if (this.ip_switch === "on") {
                        promises.push(this.$refs["ip_type"].validateProcedure());
                    }
                    if (this.referer === "on") {
                        promises.push(this.$refs["referer_type"].validateProcedure());
                    }
                    if (this.ua_switch === "on") {
                        promises.push(this.$refs["ua_type"].validateProcedure());
                    }
                    if (this.uri_switch === "on") {
                        promises.push(this.$refs["uri_type"].validateProcedure());
                    }
                } else {
                    !this.creatForm.target_domain &&
                        promises.push(
                            Promise.resolve({
                                valid: false,
                                msg: this.$t("domain.create.copy.tip2"),
                                dom: "target_domain",
                            })
                        );
                }
            }

            const validres = await Promise.all(promises); // 获取校验结果
            // 转换成以dom字段为key值的 json，方便在界面上展示错误信息
            const tojson = validres.reduce((result, item) => {
                if (item.dom) {
                    result[item.dom] = item;
                }
                return result;
            }, {});
            Object.assign(this.validRes, tojson); // validRes 在界面展示
            let valid = true;
            // 找到第一个 valid 为 false 的配置项
            let elementDom;
            for (const element of validres) {
                if (!element.valid) {
                    elementDom =
                        element.dom === "domains"
                            ? this.$refs[element.dom][0].$el
                            : this.$refs[element.dom]?.$el;
                    valid = element.valid;
                    this.$message.error(element.msg);
                    break;
                }
            }
            this.submitLoading = false;
            this.creatForm.cert_name = this.cert_name_cache;
            this.creatForm.cert_name_gm = this.cert_name_gm_cache;
            if (valid) {
                this.submitLoading = true;

                const filetype_ttl_data = JSON.parse(JSON.stringify(this.creatForm.filetype_ttl));
                const filetype_ttl = filetype_ttl_data.map(item => {
                    delete item.time;
                    delete item.timeType;
                    // 缓存规则值为：不缓存时(参数 === 1)，过期时间字段传0给后端
                    if (item.cache_type === 1) {
                        item.ttl = 0;
                    }
                    return item;
                });
                if (filetype_ttl && filetype_ttl.length > 0) {
                    body.filetype_ttl = filetype_ttl;
                }

                if (this.creatForm.origin.some(item => item.is_xos === 1)) {
                    body.xos_origin_is = 1;
                }
                (this.creatForm.origin || []).map(item => {
                    const xos_origin = { origin: "" };
                    const zos_origin = { switch: 1, origin: "" };
                    if (item.is_xos === 1) {
                        xos_origin.origin = item.origin;
                        body.xos_origin = xos_origin;
                    }
                    if (item.is_xos === 2) {
                        zos_origin.origin = item.origin;
                        body.zos_origin = zos_origin;
                    }
                });
                const originData = JSON.parse(JSON.stringify(this.creatForm.origin));
                const origin = originData.map(item => {
                    delete item.is_xos;
                    return item;
                });
                if (origin && origin.length > 0) {
                    body.origin = origin;
                }
                // 指定源站回源HOST：origin_host有值，origin_host_type传1，没值传0
                const origin_host_http = {};
                let originHostCount = 0;
                (body.origin || []).map(item => {
                    if (
                        item.origin_host !== "" &&
                        item.origin_host !== null &&
                        item.origin_host !== undefined
                    ) {
                        originHostCount++;
                        origin_host_http[item?.origin] = item?.origin_host;
                    }
                });
                if (originHostCount > 0) {
                    body.origin_host_type = 1;
                } else {
                    body.origin_host_type = 0;
                }
                this.$set(body, "origin_host_http", origin_host_http);
                // 传bucket
                if (this.temp_bucket && body.product_code !== "006" && body.xos_origin) {
                    this.$set(body.xos_origin, "bucket", this.temp_bucket);
                }
                // TSL版本
                if (this.creatForm.ssl && this.creatForm.ssl.length > 0) {
                    body.ssl = this.creatForm.ssl.join(",");
                } else {
                    body.ssl = "";
                }

                // 自定义加密套件
                if (this.creatForm.custom_ssl_ciphers && this.creatForm.custom_ssl_ciphers.length > 0) {
                    body.custom_ssl_ciphers = this.creatForm.custom_ssl_ciphers?.join(":");
                } else {
                    body.custom_ssl_ciphers = "";
                }

                // https关闭，自定义加密套件不传递
                if (this.creatForm.https_status === "off") {
                    delete body.ssl_ciphers;
                    delete body.custom_ssl_ciphers;
                }

                // UA黑白名单
                if (this.ua_switch === "on") {
                    body.user_agent = this.creatForm.user_agent;
                } else {
                    delete body.user_agent;
                }
                // URL黑白名单
                if (this.uri_switch === "on") {
                    body.uri_deny = this.creatForm.uri_deny;
                } else {
                    delete body.uri_deny;
                }

                try {
                    if (body.product_code !== "006") {
                        delete body.origin_policy;
                        delete body.domain_type;
                    }
                    if (body.https_status === "off") {
                        delete body.cert_name;
                        delete body.cert_name_gm;
                        delete body.hsts;
                        body.ssl = "";
                    } else {
                        if (!this.hasCertName) {
                            delete body.ssl;
                            delete body.basic_conf.use_http2;
                        }
                        // hsts
                        if (this.creatForm.hsts.switch === 1) {
                            const { max_age, include_sub_domains } = this.creatForm.hsts;
                            body.hsts = {
                                switch: 1,
                                max_age,
                                include_sub_domains: ["on", "off"].includes(include_sub_domains)
                                    ? include_sub_domains
                                    : null,
                            };
                        } else {
                            body.hsts = { switch: 0 };
                        }
                    }

                    // 如果是参照域名新增, 忽略缓存配置&访问控制
                    if (this.isCopyDomain) {
                        body = omit(body, [
                            "filetype_ttl",
                            "ip_white_list",
                            "ip_black_list",
                            "referer_type",
                            "uri_deny",
                            "user_agent",
                            "white_referer",
                            "black_referer",
                        ]);
                        body.target_domain = this.creatForm.target_domain;
                    } else {
                        delete body.target_domain;
                    }
                    delete body.acce_config;

                    if (this.isBatchCreate) {
                        // 批量修改提交
                        delete body.domain;
                        body.domains = this.domains;
                        const data = await ctFetch(nDomainUrl.domainBatchCreate, {
                            method: "POST",
                            body: body,
                            clearQsWithPost: false,
                            clearEmptyParams: false,
                            headers: {
                                "Content-Type": "application/json",
                            },
                        });
                        if (data?.length > 0) {
                            // 当失败数(data.length)等于新增数(body.domains.length)：点弹窗上的确定，关闭弹窗、停留当前界面
                            // 当失败数(data.length)小于新增数(body.domains.length)：点弹窗上的关闭，关闭弹窗、停留当前界面；点弹窗上的继续，关闭弹窗、跳转域名列表，仅新增成功的域名。
                            const wholeFailed = data.length === body.domains.length; // 全部新增失败
                            if (wholeFailed) {
                                this.$confirm(data.join("<br/>"), this.$t("domain.create.tip26"), {
                                    confirmButtonText: this.$t("common.dialog.submit"),
                                    dangerouslyUseHTMLString: true,
                                    showClose: false,
                                    showCancelButton: false,
                                });
                            } else {
                                this.$confirm(data.join("<br/>"), this.$t("domain.create.tip26"), {
                                    confirmButtonText: this.$t("certificate.chain.ensure"),
                                    cancelButtonText: this.$t("common.dialog.close"),
                                    dangerouslyUseHTMLString: true,
                                }).then(() => {
                                    this.$message(this.$t("domain.create.tip46"));
                                    setTimeout(() => {
                                        this.$router.push({
                                            name: "ndomain.list",
                                        });
                                        this.$ctBus.$emit("refershDomainList");
                                    }, 2000);
                                });
                            }
                        } else {
                            this.$message.success(this.$t("domain.create.tip27"));

                            setTimeout(() => {
                                this.$router.push({
                                    name: "ndomain.list",
                                });
                                this.$ctBus.$emit("refershDomainList");
                            }, 1000);
                        }
                        return;
                    }
                    const data = await ctFetch(this.submitUrlWithQuery, {
                        method: "POST",
                        body: body,
                        clearQsWithPost: false,
                        clearEmptyParams: false,
                        headers: {
                            "Content-Type": "application/json",
                        },
                    });

                    if (data.state === "online" && data.success === "true") {
                        this.targetDomain = this.creatForm.domain;
                        const c = data.validate.find(valid => valid.pass !== "true");
                        if (c) {
                            this.validateArrFromBe = data.validate;
                            this.validationTitle = this.$t("domain.create.tip28");
                            this.showValidationModal = true;
                            this.handleModalType = true;
                        } else {
                            this.$message.success(`${this.submitDesc.text}${this.$t("domain.create.tip29")}`);
                            // await this.getDomainOrder({
                            //     domain: this.targetDomain,
                            // });
                            setTimeout(() => {
                                this.$router.push(this.orderRouter);
                                this.$ctBus.$emit("refershDomainList");
                            }, 1000);
                        }
                    } else {
                        this.validateArrFromBe = data.validate;
                        this.validationTitle = `${this.submitDesc.text}${this.$t("domain.create.tip30")}`;
                        this.showValidationModal = true;
                        this.handleModalType = false;
                    }
                } catch (err) {
                    const validate = err.data && err.data.data && err.data.data.validate;
                    let v = true;
                    if (validate) {
                        v = validate.reduce((acc, cur) => {
                            return acc && cur.pass === "true";
                        }, true);
                        if (!v) {
                            this.validateArrFromBe = validate;
                            this.validationTitle = `${this.submitDesc.text}${this.$t("domain.create.tip30")}`;
                            this.showValidationModal = true;
                        }
                    }
                    if (v) {
                        // 默认展示接口报错信息
                        // const errMsg = (err.data && err.data.reason) || err.reason;
                        // this.$message.error(errMsg || "操作失败");
                        this.$errorHandler(err);
                    }
                } finally {
                    this.submitLoading = false;
                }
            } else {
                elementDom?.scrollIntoView({ behavior: "smooth" });
            }
        },
        async showCertUpload(isSm2 = false) {
            let domainData = this.creatForm.domain;
            if (this.isBatchCreate) {
                const promises = [];
                this.$refs["domains"].forEach(data => {
                    promises.push(data.validateProcedure());
                });
                const validres = await Promise.all(promises);
                domainData = validres.find(item => !item.valid) ? false : true;
            }
            if (!domainData) {
                this.$message.error(this.$t("domain.create.tip31"));
                document.querySelector(".domain-input").scrollIntoView({ behavior: "smooth" });
            } else {
                this.addVisible = true;
                this.addCertificate = getDefaultCertParam();
                this.addCertificate.algorithm_type = isSm2 ? 1 : 0;
            }
        },
        async submitCertificate() {
            const url = nCertificateUrl.createDomainCert;
            this.loading = true;
            const data = {
                ...this.addCertificate,
                domain: this.isBatchCreate ? this.domains.join(",") : this.creatForm.domain,
            };
            if (this.addCertificate.algorithm_type !== 1) {
                delete data.certs_sign;
                delete data.key_sign;
            }
            await this.$ctFetch(url, {
                method: "POST",
                body: data,
                headers: {
                    "Content-Type": "application/json",
                },
            });
            this.$message.success(this.$t("domain.create.tip32"));
            this.addVisible = false;
            this.getCertList();
        },
        cancel() {
            this.addVisible = false;
        },
        async getDomainOrder({ domain }) {
            try {
                this.submitLoading = true;
                const params = {
                    domainList: [domain],
                    action: "",
                    status: "2",
                    startTime: "",
                    endTime: "",
                };
                const result = await ctFetch(nOrderUrl.orderList, {
                    method: "POST",
                    clearQsWithPost: false,
                    body: {
                        data: params,
                    },
                    headers: {
                        "Content-Type": "application/json",
                    },
                });
                const { list: orderList } = result;
                if (orderList.length > 0) {
                    [this.orderInfo] = orderList;
                }
            } catch (err) {
                this.$message(this.$t("domain.create.tip33"));
            } finally {
                this.submitLoading = false;
            }
        },

        async closeModal(visible) {
            if (!this.handleModalType) return;
            if (!visible) {
                // await this.getDomainOrder({
                //     domain: this.targetDomain,
                // });
                this.handleModalType = false;
                this.$router.push(this.orderRouter);
            }
        },
        onDomainTypeChange() {
            // 清空参照域名
            this.creatForm.target_domain = "";
        },
        onHttpsStatusChange(v) {
            if (v === "on") {
                this.creatForm.cert_name = "";
                this.creatForm.cert_name_gm = "";
                this.creatForm.basic_conf.use_http2 = 0;
                this.creatForm.hsts = {
                    switch: 0,
                    max_age: 1200,
                    include_sub_domains: null,
                };
                this.creatForm.ssl = [];
                this.getCertList();
            }
        },
        // 证书选择下拉框
        async getCertList(domain, checkDomain = false) {
            const domainData = domain || (this.isBatchCreate ? this.domains : this.creatForm.domain);
            if (!this.isBatchCreate && !domainData) {
                checkDomain && this.$message.error(this.$t("domain.create.tip31"));
                // document.querySelector(".domain-input").scrollIntoView({ behavior: "smooth" });
                // this.creatForm.https_status = "off";
                return;
            }
            if (this.isBatchCreate) {
                if (domainData.toString() === "") {
                    checkDomain && this.$message.error(this.$t("domain.create.tip31"));
                    return;
                }
            }
            // 如果是手动刷新证书列表，需要缓存证书名
            if (checkDomain) {
                this.creatFormCache.cert_name = this.creatForm.cert_name;
                this.creatFormCache.cert_name_gm = this.creatForm.cert_name_gm;
            }
            const params = {
                domain: domainData,
            };
            if (!this.isDomainEndsWithCtyun) {
                params.biz_type = 0;
            }
            this.cert_name_loading = true;
            const res = await this.$ctFetch(CertSelectorUrl, {
                method: "GET",
                data: params,
            })
                .catch(err => {
                    this.cert_name_list = [];
                    this.cert_name_gm_list = [];
                    this.$errorHandler(err);
                })
                .finally(() => {
                    this.cert_name_loading = false;
                });
            // 回填数据
            if (res) {
                this.cert_name_list = res.filter(item => item.algorithm_type === 0);
                this.cert_name_gm_list = res.filter(item => item.algorithm_type === 1);
                // 默认回填上次的证书：如果用户修改域名后也可以跟上次回填的证书匹配，那也默认选中上次回填的证书
                if (this.cert_name_list.indexOf(this.creatFormCache.cert_name) > -1) {
                    this.creatForm.cert_name = this.creatFormCache.cert_name;
                }
                if (this.cert_name_gm_list.indexOf(this.creatFormCache.cert_name_gm) > -1) {
                    this.creatForm.cert_name_gm = this.creatFormCache.cert_name_gm;
                }
            }
        },
        // addOrigin() {
        //     // this.creatForm.origin.push({ ...defaultOrigin });
        //     this.originForm = { origin: "", role: "master", weight: "10" };
        //     this.originDialogVisible = true;
        // },
        async onOperator(row, currentType, tabName, i) {
            if (currentType === "create" && !this.creatForm.product_code) {
                this.$message(this.$t("domain.create.tip48"));
                return;
            }
            this.currentType = currentType;
            this.currenIndex = i;
            if (currentType === "create") {
                const defaultFormMap = {
                    origin: { origin: "", role: "master", weight: "10", is_xos: 0, origin_host: "" },
                };
                row = defaultFormMap[tabName];
            }
            if (currentType === "delete") {
                const msgMap = {
                    origin: `${this.$t("domain.create.tip34")}${row.origin}${this.$t("domain.create.tip35")}`,
                };

                // 二次确认弹窗
                await this.$confirm(msgMap[tabName], this.$t("domain.delete"), {
                    type: "warning",
                });
                this.creatForm[tabName].splice(i, 1);
            } else {
                this.originDialogVisible = true;
                row.weight = row.weight ? row.weight : "10";
                this.originForm = { ...row };
            }
        },
        delOrigin(index) {
            this.creatForm.origin.splice(index, 1);
        },
        getCacheList(val) {
            this.creatForm.filetype_ttl = [...val];
        },
        async submitOrigin() {
            if (this.currentType === "create") {
                this.creatForm.origin.push({
                    ...this.originForm,
                });
            } else {
                // 修改
                this.$set(this.creatForm.origin, this.currenIndex, this.originForm);
            }
            this.originDialogVisible = false;
        },
        cancelOrigin() {
            this.originDialogVisible = false;
        },
        onBucketHandle(val) {
            this.temp_bucket = val;
        },
        // 获取批量新增域名个数上限、refererLimit
        async checkBatcCreate() {
            const rst = await this.$ctFetch(nBasicConfig, { cache: true });
            this.batchCreateLimit = rst.batchCreateLimit || 20;
            this.refererLimit = rst.refererLimit || 400;
        },
        onAcceConfigChange() {
            this.creatForm.target_domain = "";
        },
    },
};
</script>

<style lang="scss" scoped>
@import "@/components/index.scss";
.input-wrapper {
    width: #{$wrapper-width}px;
}
.simple-create-form {
    .prompt {
        display: inline-block;
        color: $neutral-7;
        font-size: 12px;
        line-height: 12px;
        font-weight: normal;
        margin-left: 4px;
    }
    .tooltips {
        font-size: 12px;
        line-height: 12px;
        margin-top: 8px;
        color: $neutral-7;
        white-space: initial;
    }
    .tooltips-error {
        color: $color-danger !important;
    }
    .select-action-wrapper {
        .select.el-select {
            width: #{$wrapper-width}px;
            margin-right: 8px;
        }
    }
}
.label-name {
    font-weight: bold;
    width: 600px;
    overflow: hidden;
    border-left: 4px solid $color-master;
    line-height: 1;
    padding-left: 12px;
    margin: 16px 0;
}
.domain-edit-wrap {
    overflow: hidden;
    flex: 1;
    padding-bottom: 70px;
    min-height: 300px;
}
.domain-edit {
    overflow: hidden;
    padding: 20px;
    display: flex;
    background: #fff;
    margin-bottom: 12px;

    @include g-media-attr(
        (
            attr: "flex-direction",
            sm: row,
            xs: column,
        )
    );
}

// 大屏左右布局，小屏上下布局
.domain-edit-menu {
    @include g-height(auto, 100px);
    background: #fff;
}

.domain-edit-content {
    flex: 1;

    ::v-deep .el-scrollbar__wrap {
        overflow-x: hidden;
    }
}

.origin-table {
    margin-top: 0;
}

.submit {
    .footer-content {
        display: flex;
        justify-content: flex-end;
    }
}

.result-panel {
    font-size: 20px;
    padding: 150px 0;
    text-align: center;
    i {
        color: $color-success;
    }
    a {
        color: $color-warning;
    }
}
.btn-bg {
    color: $color-master;
    cursor: pointer;
}
.radio-group {
    display: inline-block !important;
    ::v-deep {
        .el-radio.is-bordered + .el-radio.is-bordered {
            margin-left: 0;
            margin-bottom: 8px;
        }

        .el-radio-button__inner {
            min-width: 150px;
        }
    }
}
.radio-button-display {
    line-height: 24px;
}

.list {
    & + .list {
        margin-top: 20px;
    }
    .label {
        margin: 0 8px 0 12px;
        font-size: 12px;
        color: $color-neutral-7;
    }
    .label + div {
        width: calc(100% - 70px);
    }
    .el-row {
        margin-bottom: 8px;
    }
}
.batch-domain {
    display: flex;
    .input-wrapper {
        width: auto;
        margin-right: 4px;
    }
    .el-button {
        height: 32px;
    }
}

.btn-disabled {
    color: $color-master-disabled;
    cursor: not-allowed;
}
.copy-domain-select {
    display: flex;
    gap: 8px;
    align-items: center;
}
.copy-domain-select-warning {
    color: $color-warning !important;
}
</style>
<style lang="scss">
// 被按钮控制显隐的模块样式
.switch-wrapper {
    padding: 12px 12px 4px 12px;
    margin-bottom: 20px;
    // width: calc(100% - 261px);
    margin-left: 141px; // 由label-width决定
    background-color: #f7f8fa;
    overflow: auto;
}
.aocdn-ignore-tooltip-width-fixed {
    max-width: 520px !important;
}
</style>
