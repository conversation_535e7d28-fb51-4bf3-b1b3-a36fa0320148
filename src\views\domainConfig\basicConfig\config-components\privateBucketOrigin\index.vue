<template>
    <div class="bucket-origin-wrapper">
        <el-form
            label-width="140px"
            label-position="left"
            :model="form"
            :rules="rules"
            ref="privateBucketOriginForm"
            :disabled="!isEdit || !isService || isLockXosOrigin || isLockZosOrigin"
        >
            <div v-if="isShowPrivateBucketOrigin">
                <el-form-item :label="$t('domain.detail.label28')" prop="switch">
                    <el-switch
                        style="margin-top:-3px;"
                        v-model="form.bucket_status"
                        :active-value="true"
                        :inactive-value="false"
                        @change="bucket_status_change"
                    ></el-switch>
                    <span class="tips">
                        <ct-svg-icon icon-class="info-circle" class-name="icon-column-label" />
                        <span>{{ bucketTips }}</span>
                    </span>
                </el-form-item>

                <div v-if="form.bucket_status" class="switch-wrapper">
                    <el-form-item label="AK" prop="ak" :rules="rules.ak" class="is-required">
                        <el-input
                            v-model.trim="form.ak"
                            placeholder=""
                            @blur="akBlur"
                            class="input-style"
                        ></el-input>
                    </el-form-item>

                    <el-form-item label="SK" prop="sk" :rules="rules.sk" class="is-required">
                        <el-input
                            v-model.trim="form.sk"
                            placeholder=""
                            @blur="skBlur"
                            type="password"
                            :show-password="true"
                            class="input-style"
                        ></el-input>
                    </el-form-item>
                </div>
            </div>

        </el-form>
    </div>
</template>

<script>
import ctSvgIcon from "@/components/ctSvgIcon";
import componentMixin from "@/views/domainConfig/componentMixin";
import { StatisticsModule } from '@/store/modules/statistics';

export default {
    name: "privateBucketOrigin",
    components: {
        ctSvgIcon,
    },
    mixins: [componentMixin],
    props: {
        datas: Object,
        isLockXosOrigin: Boolean,
        isLockZosOrigin: Boolean,
        isShowPrivateBucketOrigin: Boolean,
    },
    data() {
        return {
            form: {
                bucket_status: false,
                ak: "",
                sk: "",
            },
            rules: {
                ak: [
                    // { required: true, message: "请输入AK", trigger: "blur" },
                    {
                        validator: (rule, value, callback) => {
                            if (this.isLockXosOrigin || this.isLockZosOrigin) return callback();
                            const sn = /^[a-z0-9]+$/i;
                            const akLimit = StatisticsModule.privateBucketLimit.ak;
                            if (value === "" || value === null || value === undefined)
                                callback(new Error(this.$t("domain.detail.tip81")));
                            else if (!sn.test(value) && this.isEdit)
                                callback(new Error(this.$t("domain.detail.tip84")));
                            else if (value.length > akLimit && this.isEdit)
                                callback(new Error(this.$t("domain.detail.tip85", { num: akLimit })));
                            else callback();

                        },
                        trigger: ["blur", "change"],
                    },
                ],
                sk: [
                    // { required: true, message: "请输入SK", trigger: "blur" },
                    {
                        validator: (rule, value, callback) => {
                            if (this.isLockXosOrigin || this.isLockZosOrigin) return callback();
                            const sn = /^[a-z0-9]+$/i;
                            const skLimit = StatisticsModule.privateBucketLimit.sk;
                            if (value === "" || value === null || value === undefined)
                                callback(new Error(this.$t("domain.detail.tip82")));
                            else if (!sn.test(value) && this.isEdit)
                                callback(new Error(this.$t("domain.detail.tip84")));
                            else if (value.length > skLimit && this.isEdit)
                                callback(new Error(this.$t("domain.detail.tip86", { num: skLimit })));
                            else callback();

                        },
                        trigger: ["blur", "change"],
                    },
                ],
            },
        };
    },
    computed: {
        bucketTips() {
            if (this.datas.origin?.find(itm => itm.is_xos === 1)) {
                return this.$t("domain.detail.tip68");
            } else {
                return this.$t("domain.detail.tip68-1");
            }
        }
    },
    watch: {
        form: {
            deep: true,
            handler(val) {
                this.$emit("onChange", val);
            },
            immediate: true,
        },
        datas: {
            deep: true,
            handler(val) {
                this.init(val);
            },
            immediate: true,
        },
    },
    methods: {
        bucket_status_change(val) {
            if (!val) {
                this.form.ak = "";
                this.form.sk = "";
            }
        },
        akBlur() {
            this.$refs.privateBucketOriginForm.validateField("ak");
        },
        skBlur() {
            this.$refs.privateBucketOriginForm.validateField("sk");
        },
        init(v) {
            if (!v) return;
            this.form.bucket_status = v?.bucket_status;
            this.form.ak = v?.ak || "";
            this.form.sk = v?.sk || "";
        },
    },
};
</script>

<style lang="scss" scoped>
.bucket-origin-wrapper {
    ::v-deep {
        .el-form--label-left .el-form-item__label {
            text-align: right;
        }
    }
}
.hsts-bg {
    display: flex;
    flex-direction: column;
    justify-content: center;
    width: 70%;
    height: 108px;
    background: $color-neutral-1;
    margin-left: 140px;
    ::v-deep {
        .el-form-item.el-form-item--medium {
            margin-bottom: 0px;
        }
    }
}
.sub-domain-wrapper {
    margin-top: 20px;
}
.input-style {
    width: 380px;
}
.question-circle {
    font-size: $text-size-md;
    cursor: pointer;
}
.icon-column-label {
    color: $text-color-light;
    // margin-left: $margin-2x;
    font-size: $text-size-md;
    line-height: 18px;
    height: 14px;
    width: 14px;
}
.tips {
    font-size: 12px;
    color: $color-neutral-7;
    font-weight: 400;
    margin-left: $margin;
    svg {
        margin-right: $margin;
    }
}
</style>
