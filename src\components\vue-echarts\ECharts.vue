<template>
    <div class="cdn-echarts">
        <span class="echarts-tip">{{ $t("tips") }}</span>
    </div>
</template>

<style lang="scss">
// 不是用 scoped 模式，避免样式权重高于业务中的配置
.cdn-echarts {
    position: relative;
    width: 600px;
    height: 400px;
    background: rgba(240, 240, 240, 0.2);

    .echarts-tip {
        position: absolute;
        top: 40%;
        left: 50%;
        font-size: 12px;
        color: $color-info;
        transform: translate(-50%, -50%);
    }

    // 取消蒙层的透明度，配合上面的文案
    .el-loading-mask {
        background: rgb(252, 252, 252);
    }
}
</style>

<script>
// import * as echarts from "echarts"; // 改成下面的按需加载
// 引入 echarts 核心模块，核心模块提供了 echarts 使用必须要的接口。
import * as echarts from "echarts/lib/echarts";
// import "../../config/echart/china";
import { addListener, removeListener } from "resize-detector";

// 注意，新的接口中默认不再包含 Canvas 渲染器，需要显示引入，如果需要使用 SVG 渲染模式则使用 SVGRenderer
import { CanvasRenderer } from "echarts/renderers";
// 引入提示框，标题，直角坐标系组件，组件后缀都为 Component
import {
    TitleComponent, // 标题
    LegendComponent, // 图例
    GridComponent, // 坐标系
    DataZoomComponent, // 区域缩放
    TooltipComponent, // 提示框
    ToolboxComponent, // 工具栏
} from "echarts/components";
// 引入柱状图图表，图表后缀都为 Chart
import { BarChart, LineChart, PieChart } from "echarts/charts";

// 注册必须的组件
echarts.use([
    CanvasRenderer,

    TitleComponent,
    LegendComponent,
    GridComponent,
    DataZoomComponent,
    TooltipComponent,
    ToolboxComponent,

    BarChart,
    LineChart,
    PieChart,
]);

// 主题
import cdnTheme from "@/config/echart/cdn-theme";

// 调整配置
cdnTheme.line.showSymbol = true;

// 注册 ecahrt 主题
echarts.registerTheme("cdn", cdnTheme);

const INIT_TRIGGERS = ["theme", "initOptions", "autoresize"];
const REWATCH_TRIGGERS = ["manualUpdate", "watchShallow"];

export default {
    name: "VChart",
    props: {
        options: Object,
        theme: [String, Object],
        initOptions: Object,
        group: String,
        autoresize: Boolean,
        watchShallow: Boolean,
        manualUpdate: Boolean,
    },
    i18n: {
        messages: {
            zh: {
                tips: "抱歉，暂无数据，请换其它条件查询试试",
            },
            en: {
                tips: "Sorry, there is no data, please change other conditions to try",
            },
        },
    },
    data() {
        return {
            lastArea: 0,
        };
    },
    watch: {
        group(group) {
            this.chart.group = group;
        },
    },
    methods: {
        // provide an explicit merge option method
        mergeOptions(options, notMerge, lazyUpdate) {
            if (this.manualUpdate) {
                this.manualOptions = options;
            }

            if (!this.chart) {
                this.init(options);
            } else {
                this.delegateMethod("setOption", options, notMerge, lazyUpdate);
            }
        },
        // just delegates ECharts methods to Vue component
        // use explicit params to reduce transpiled size for now
        appendData(params) {
            this.delegateMethod("appendData", params);
        },
        resize(options) {
            this.delegateMethod("resize", options);
        },
        dispatchAction(payload) {
            this.delegateMethod("dispatchAction", payload);
        },
        convertToPixel(finder, value) {
            return this.delegateMethod("convertToPixel", finder, value);
        },
        convertFromPixel(finder, value) {
            return this.delegateMethod("convertFromPixel", finder, value);
        },
        containPixel(finder, value) {
            return this.delegateMethod("containPixel", finder, value);
        },
        showLoading(type, options) {
            this.delegateMethod("showLoading", type, options);
        },
        hideLoading() {
            this.delegateMethod("hideLoading");
        },
        getDataURL(options) {
            return this.delegateMethod("getDataURL", options);
        },
        getConnectedDataURL(options) {
            return this.delegateMethod("getConnectedDataURL", options);
        },
        clear() {
            this.delegateMethod("clear");
        },
        dispose() {
            this.delegateMethod("dispose");
        },
        delegateMethod(name, ...args) {
            if (!this.chart) {
                this.init();
            }
            return this.chart[name](...args);
        },
        delegateGet(methodName) {
            if (!this.chart) {
                this.init();
            }
            return this.chart[methodName]();
        },
        getArea() {
            return this.$el.offsetWidth * this.$el.offsetHeight;
        },
        init(options) {
            if (this.chart) {
                return;
            }

            const chart = echarts.init(this.$el, this.theme, this.initOptions);

            if (this.group) {
                chart.group = this.group;
            }

            chart.setOption(options || this.manualOptions || this.options || {}, true);

            Object.keys(this.$listeners).forEach(event => {
                const handler = this.$listeners[event];

                if (event.indexOf("zr:") === 0) {
                    chart.getZr().on(event.slice(3), handler);
                } else {
                    chart.on(event, handler);
                }
            });

            if (this.autoresize) {
                this.lastArea = this.getArea();
                this.__resizeHandler = this.$ctUtil.debounce(
                    () => {
                        if (this.lastArea === 0) {
                            // emulate initial render for initially hidden charts
                            this.mergeOptions({}, true);
                            this.resize();
                            this.mergeOptions(this.options || this.manualOptions || {}, true);
                        } else {
                            this.resize();
                        }
                        this.lastArea = this.getArea();
                    },
                    100,
                    { leading: true }
                );
                addListener(this.$el, this.__resizeHandler);
            }

            Object.defineProperties(this, {
                // Only recalculated when accessed from JavaScript.
                // Won't update DOM on value change because getters
                // don't depend on reactive values
                width: {
                    configurable: true,
                    get: () => {
                        return this.delegateGet("getWidth");
                    },
                },
                height: {
                    configurable: true,
                    get: () => {
                        return this.delegateGet("getHeight");
                    },
                },
                isDisposed: {
                    configurable: true,
                    get: () => {
                        return !!this.delegateGet("isDisposed");
                    },
                },
                computedOptions: {
                    configurable: true,
                    get: () => {
                        return this.delegateGet("getOption");
                    },
                },
            });

            this.chart = chart;
        },
        initOptionsWatcher() {
            if (this.__unwatchOptions) {
                this.__unwatchOptions();
                this.__unwatchOptions = null;
            }

            if (!this.manualUpdate) {
                this.__unwatchOptions = this.$watch(
                    "options",
                    (val, oldVal) => {
                        if (!this.chart && val) {
                            this.init();
                        } else {
                            // mutating `options` will lead to merging
                            // replacing it with new reference will lead to not merging
                            // eg.
                            // `this.options = Object.assign({}, this.options, { ... })`
                            // will trigger `this.chart.setOption(val, true)
                            // `this.options.title.text = 'Trends'`
                            // will trigger `this.chart.setOption(val, false)`
                            this.chart.setOption(val, val !== oldVal);
                        }
                    },
                    { deep: !this.watchShallow }
                );
            }
        },
        destroy() {
            if (this.autoresize) {
                removeListener(this.$el, this.__resizeHandler);
            }
            this.dispose();
            this.chart = null;
        },
        refresh() {
            if (this.chart) {
                this.destroy();
                this.init();
            }
        },
    },
    created() {
        this.initOptionsWatcher();

        INIT_TRIGGERS.forEach(prop => {
            this.$watch(
                prop,
                () => {
                    this.refresh();
                },
                { deep: true }
            );
        });

        REWATCH_TRIGGERS.forEach(prop => {
            this.$watch(prop, () => {
                this.initOptionsWatcher();
                this.refresh();
            });
        });
    },
    mounted() {
        // auto init if `options` is already provided
        // 为了防止过早的初始化导致图表跳动，关闭这个 init ，等待 options 变更后再执行初始化
        // if (this.options) {
        //     this.init();
        // }
    },
    activated() {
        if (this.autoresize) {
            this.chart && this.chart.resize();
        }
    },
    destroyed() {
        if (this.chart) {
            this.destroy();
        }
    },
    connect(group) {
        if (typeof group !== "string") {
            group = group.map(chart => chart.chart);
        }
        echarts.connect(group);
    },
    disconnect(group) {
        echarts.disConnect(group);
    },
    registerMap(mapName, geoJSON, specialAreas) {
        echarts.registerMap(mapName, geoJSON, specialAreas);
    },
    registerTheme(name, theme) {
        echarts.registerTheme(name, theme);
    },
    graphic: echarts.graphic,
};
</script>
