<template>
    <div class="dictionary-select">
        <template v-if="dicType === 'select'">
            <el-select
                v-model="valueInside"
                v-bind="$attrs"
                v-on="$listeners"
                :disabled="isDisabled"
                @change="handleChange"
            >
                <el-option v-for="item in data" :key="item.value" :value="item.value" :label="item.label" />
            </el-select>
        </template>
        <template v-if="dicType === 'cascade'">
            <el-cascader
                v-model="valueInside"
                :options="data"
                collapse-tags
                v-bind="$attrs"
                v-on="$listeners"
                :disabled="isDisabled"
                @change="handleChange"
            />
        </template>
        <template v-if="dicType === 'ctSelect'">
            <ct-select
                v-model="valueInside"
                :data="data"
                v-bind="$attrs"
                v-on="$listeners"
                :disabled="isDisabled"
            />
        </template>
    </div>
</template>

<script>
import { get } from "lodash-es";
import { mapGetters } from "vuex";

export default {
    name: "index",
    components: {
        // 懒加载，当dicType为ctSelect时才进行加载
        ctSelect: () => import("@/components/ctSelect"),
    },
    model: {
        prop: "value",
        event: "change",
    },
    props: {
        value: {
            type: [String, Array],
            required: true,
            default: "",
        },
        // 字典值描述，固定格式root,value
        dicStr: {
            type: String,
            default: "",
        },
        // 取字典所用到的实际value，如翻译字典值，需要用到dicValue对应的数据
        dicValue: {
            type: String,
            default: "",
        },
        // 字典组件类型，默认select,cascade(级联选择),ctSelect(控制台通用选择器)
        dicType: {
            type: String,
            default: "select",
        },
        dicConfig: {
            type: Object,
            default: null,
        },
        // 关联数据,从外部传入，若在内部处理比较难拿到父级的数据
        outsideAry: {
            type: Array,
            default: () => [],
        },
        disabled: {
            type: [Boolean, Function],
            default: false,
        },
    },
    data() {
        return {
            valueInside: "",
            dicValueInside: "",
        };
    },
    computed: {
        ...mapGetters({
            optionsMap: "optionsMap",
        }),
        // 渲染数据
        data() {
            // 无字典值，则当作data需从外部传入
            if (!this.dicStr) {
                return this.outsideAry;
            }

            if (!this.dicValueInside) {
                return [];
            }

            const ary = get(this.optionsMap, this.dicValue, []) || [];
            return ary;
        },
        /**
         * 是否禁用
         */
        isDisabled() {
            if (typeof this.disabled === "function") {
                return this.disabled();
            }

            return this.disabled;
        },
    },
    watch: {
        value: {
            handler(val) {
                this.valueInside = val;
            },
            immediate: true,
        },
    },
    created() {
        this.initDictionary();
    },
    methods: {
        /**
         * 初始化字典
         */
        initDictionary() {
            if (!this.dicStr) {
                return;
            }

            if (!this.dicValue) {
                this.dicValueInside = get(this.dicStr.split(","), "[1]", "");
            } else {
                this.dicValueInside = this.dicValue;
            }

            const info = {
                prop: this.dicValue,
            };
            if (this.dicType === "cascade") {
                info.isMulti = true;
            }
            const config = Object.assign({}, info, this.dicConfig);
            const map = {
                [this.dicStr]: config,
            };
            const data = {
                type: "queryPageDictionary",
                data: map,
            };
            this.$store.dispatch(data);
        },
        /**
         * 处理改变数据
         */
        handleChange(val) {
            this.$emit("change", val);
            // 外部调用handle-change,由于组件内使用v-on=$listeners,外部若调用则会触发两次
            this.$emit("handle-change", {
                val: val,
                data: this.data,
            });
        },
    },
};
</script>

<style scoped></style>
