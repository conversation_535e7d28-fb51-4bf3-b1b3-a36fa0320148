<template>
    <ct-section-wrap :headerText="headerTitle" :headerTip="headerTip">
        <ct-box class="table-scroll-wrap">
            <div class="search-bar-wrapper">
                <div>
                    <el-button class="el-icon-plus" type="primary" @click="showAdd">
                        {{ $t("certificate.addBtn") }}
                    </el-button>
                </div>
                <div class="search-bar-container">
                    <div class="search-bar">
                        <label class="search-label">{{ $t("certificate.search.label1") }}</label>
                        <el-input size="medium" maxlength="255" v-model="keyword">
                            <el-select
                                slot="prepend"
                                v-model="keywordType"
                                :class="[isEn && 'increse-width']"
                            >
                                <el-option
                                    v-for="item in searchOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                />
                            </el-select>
                        </el-input>

                        <label class="search-label ml-12">{{ $t("certificate.search.label2") }}</label>
                        <el-date-picker
                            size="medium"
                            v-model="searchDate"
                            type="datetimerange"
                            range-separator="-"
                            :start-placeholder="$t('common.datePicker.start')"
                            :end-placeholder="$t('common.datePicker.end')"
                            value-format="timestamp"
                            :picker-options="pickerOptions"
                        />

                        <el-button type="primary" plain @click="fetchList">
                            {{ $t("common.search.start") }}
                        </el-button>
                        <el-button @click="reset" class="search-bar-reset-btn">{{ $t("common.search.reset") }} </el-button>
                    </div>
                </div>
            </div>
            <el-table :empty-text="$t('common.table.empty')" :data="dataList" v-loading="loading">
                <el-table-column :label="$t('common.table.index')" width="60">
                    <template #default="scope">
                        {{ (page - 1) * perPage + +scope.$index + 1 }}
                    </template>
                </el-table-column>
                <el-table-column
                    min-width="120"
                    :label="$t('certificate.table.label[0]')"
                    prop="name"
                    align="left"
                    show-overflow-tooltip
                />
                <el-table-column
                    min-width="120"
                    :label="$t('certificate.证书类型')"
                    prop="algorithm_type"
                    align="left"
                    show-overflow-tooltip
                >
                    <template #default="{ row }">
                        {{ row.algorithm_type === 1 ? $t('certificate.国密证书') : $t('certificate.国际标准证书') }}
                    </template>
                </el-table-column>
                <el-table-column
                    :label="$t('certificate.table.label[1]')"
                    prop="cn"
                    align="left"
                    min-width="120"
                    show-overflow-tooltip
                />
                <el-table-column
                    min-width="120"
                    :label="$t('certificate.table.label[2]')"
                    prop="issuer"
                    align="left"
                    show-overflow-tooltip
                />
                <!-- 证书状态 -->
                <el-table-column
                    :min-width="isEn ? '140' : '110'"
                    :label="$t('certificate.table.label[9]')"
                    prop="state"
                    align="left"
                    show-overflow-tooltip
                >
                    <template v-slot="{ row }">
                        <span :class="{ 'danger-style': row.is_chain_complete === 0 }">
                             {{ stateFormatter(row )}}
                        </span>
                        <el-tooltip
                            v-if="row.is_chain_complete === 0"
                            placement="top"
                            effect="dark"
                            :content="$t('certificate.incompleteTip')"
                        >
                            <ct-svg-icon icon-class="info-circle" class-name="icon-alert danger-style" />
                        </el-tooltip>
                    </template>
                </el-table-column>
                <el-table-column :label="$t('certificate.table.label[3]')" width="180">
                    <template #default="{ row }">
                        {{ (row.issue * 1000) | timeFormat }}
                    </template>
                </el-table-column>
                <el-table-column :label="$t('certificate.table.label[4]')" width="180">
                    <template #default="{ row }">
                        <template>
                            <div :class="{ alert: row.remainTime < 30 * 24 * 60 * 60 }">
                                {{ row.expires | timeFormat }}
                                <el-tooltip
                                    v-if="row.remainTime < 30 * 24 * 60 * 60"
                                    effect="dark"
                                    placement="top"
                                    :content="
                                        row.remainTime < 0
                                            ? $t('certificate.table.label5Tip1')
                                            : $t('certificate.table.label5Tip2')
                                    "
                                >
                                    <i class="el-icon-warning"></i>
                                </el-tooltip>
                            </div>
                        </template>
                    </template>
                </el-table-column>
                <el-table-column :label="$t('certificate.table.label[5]')" width="180">
                    <template #default="{ row }">
                        {{ (row.created * 1000) | timeFormat }}
                    </template>
                </el-table-column>
                <el-table-column :label="$t('certificate.table.label[6]')" width="180">
                    <template #default="{ row }">
                        {{ (row.updated * 1000) | timeFormat }}
                    </template>
                </el-table-column>
                <el-table-column :label="$t('common.table.operation')" min-width="220">
                    <template #default="{ row }">
                        <el-button type="text" @click="detail(row)">
                            {{ $t("certificate.table.operation[0]") }}
                        </el-button>
                        <el-button type="text" @click="edit(row)" :disabled="row.usage_mode !== 0 || row.free_mode === 1" v-if="![1, 2].includes(row.biz_type)">
                            {{ $t("domain.edit") }}
                        </el-button>
                        <el-button type="text" @click="del(row)" :loading="row.delLoading" v-if="![1, 2].includes(row.biz_type)">
                            {{ $t("certificate.table.operation[1]") }}
                        </el-button>
                        <!-- 不需要更新按钮 -->
                        <!-- <el-button type="text" @click="update(row)" v-if="showUpdateBtn">
                            {{ $t("certificate.table.operation[2]") }}
                        </el-button> -->
                        <template
                            v-if="
                                row.remainTime > 0 &&
                                    row.usage_mode !== 1 &&
                                    row.usage_mode !== 2 &&
                                    row.usage_mode !== 3 &&
                                    row.usage_mode !== 4 &&
                                    ![1, 2].includes(row.biz_type)
                            "
                        >
                            <template v-if="isAOne">
                                <el-tooltip
                                    effect="dark"
                                    placement="top"
                                    :content="$t('certificate.bindDomain.buttonTip')"
                                >
                                    <el-button
                                        type="text"
                                        @click="batchBindDomain(row)"
                                    >
                                        {{ $t("certificate.table.operation[3]") }}
                                    </el-button>
                                </el-tooltip>
                            </template>
                            <template v-else>
                                <el-button
                                    type="text"
                                    @click="batchBindDomain(row)"
                                >
                                    {{ $t("certificate.table.operation[3]") }}
                                </el-button>
                            </template>
                        </template>
                    </template>
                </el-table-column>
            </el-table>

            <div class="pager">
                <el-pagination
                    :small="isXs"
                    :layout="isXs ? 'prev, pager, next' : 'total, sizes, prev, pager, next,jumper'"
                    :total="totalRecord"
                    :current-page.sync="page"
                    :page-size="perPage"
                    :page-sizes="[10, 30, 50, 100]"
                    :hide-on-single-page="false"
                    @size-change="sizeChange"
                ></el-pagination>
            </div>
            <detail-dialog
                :detailLoading="detailLoading"
                :detailVisible="detailVisible"
                :certificate="currentCertificate"
                @cancel="cancel"
            />
            <upload-cert-dialog
                :addVisible="addVisible"
                :loading="updateLoading"
                :certificate="addCertificate"
                :dataList="dataList"
                @cancel="cancel"
                @submit="submitCertificate"
            />
            <edit-cert-dialog
                ref="editDialog"
                :certificate="editCertificate"
                @cancel="cancel"
            ></edit-cert-dialog>
        </ct-box>
    </ct-section-wrap>
</template>

<script lang="ts">
import { Component, Vue, Watch } from "vue-property-decorator";
import { timeFormat } from "@/filters/index";
import { ScreenModule } from "@/store/modules/screen";
import { nUserModule } from "@/store/modules/nuser";
import { CertificateUrl } from "@/config/url/certificate";
import type { SecretsItem, DetailItem } from "@/types/certificate";
import DetailDialog from "./components/DetailDialog.vue";
import MessageDialog from "./components/MessageDialog.vue";
import UploadCertDialog from "./components/UploadCertDialog.vue";
import EditCertDialog from "./components/EditCert.vue";
import ChainDialog from "./components/ChainDialog.vue";
import { getLang } from "@/utils";
import { shortcuts_arr } from "@/utils/pickerOption";
import store from "@/store";
import { checkCtiamButtonAuth } from "@/store/modules/menu";
import { GetCtiamButtonAction } from "@/store/config";
import { CtiamCode } from "@/utils/ctFetch/errorConfig";
import ctSvgIcon from "@/components/ctSvgIcon/index.vue";
import { certParam, domainParam, getDefaultCertParam } from "./util";

@Component({
    filters: {
        timeFormat(data: string): string {
            return timeFormat(data) || data;
        },
    },
    components: {ctSvgIcon, DetailDialog, MessageDialog, UploadCertDialog, ChainDialog, EditCertDialog },
})
export default class Certificate extends Vue {
    private pickerOptions = {
        shortcuts: shortcuts_arr,
    };
    private searchOptions = [
        {
            value: "NAME",
            label: this.$t("certificate.keyword.opt[0]"),
        },
        {
            value: "CN",
            label: this.$t("certificate.keyword.opt[1]"),
        },
        {
            value: "ISSUER",
            label: this.$t("certificate.keyword.opt[2]"),
        },
    ];

    addCertificate: certParam = getDefaultCertParam();
    addVisible = false;
    detailVisible = false;
    loading = false;
    updateLoading = false;
    detailLoading = false;
    // showUpdateBtn = false;
    domainData: domainParam[] = [];
    certificate: SecretsItem = {
        cn: "",
        created: 0,
        expires: 0,
        issue: 0,
        issuer: "",
        name: "",
        remainTime: 0,
        sans: [],
        is_chain_complete: 0,
    };
    currentCertificate: SecretsItem = {
        cn: "",
        created: "",
        expires: 0,
        issue: 0,
        issuer: "",
        name: "",
        remainTime: 0,
        sans: [],
        validTime: "",
        is_chain_complete: 0,
    };
    dataList: SecretsItem[] = []; // 最终展示的分页数据
    keyword = "";
    keywordType = "NAME";
    page = 1;
    perPage = 10;
    searchDate = [];
    totalRecord = 0;
    editCertificate = {};

    mounted() {
        this.fetchList();
        // this.getShowBtn();
        this.$ctBus.$on("submitCertificateSuccess", () => {
            this.fetchList();
        });
    }

    beforeDestroy() {
        this.$ctBus.$off("submitCertificateSuccess");
    }

    @Watch("page")
    onPageChange() {
        this.fetchList();
    }
    @Watch("searchDate")
    onSearchDateChange(newVal: string[]) {
        // 清空时防止变为null
        if (newVal === null) {
            this.searchDate = [];
        }
    }
    get isEn() {
        return getLang() === "en";
    }
    get isXs() {
        return this.screenWidth < 600;
    }
    get startTime() {
        return this.searchDate[0] ? Math.floor(this.searchDate[0] / 1000) : "";
    }
    get endTime() {
        return this.searchDate[1] ? Math.floor(this.searchDate[1] / 1000) + 24 * 60 * 60 : "";
    }
    get screenWidth() {
        return ScreenModule.width;
    }
    get email() {
        return nUserModule.userInfo.email;
    }
    get userName() {
        return nUserModule.userInfo.name;
    }
    get lang() {
        return nUserModule.lang;
    }
    get headerTitle() {
        return window.__POWERED_BY_QIANKUN__ ? this.$t("certificate.title1") : this.$t("certificate.title3");
    }

    get isAOne() {
        return window.__POWERED_BY_QIANKUN__;
    }

    get headerTip() {
        return this.isAOne ? this.$t('certificate.tipA1') : this.$t('certificate.tip');
    }

    // private async getShowBtn() {
    //     try {
    //         const rst = await this.$ctFetch<{ list: BaseBannerItem[] }>(nBannerList, {
    //             cache: true,
    //             data: {
    //                 domain: domainLangAutoComple("cdn.certificate.update", nUserModule.lang),
    //             },
    //         });
    //         this.showUpdateBtn = (rst.list || []).length !== 0;
    //     } catch (e) {
    //         // 操作按钮报错不做提示
    //     }
    // }
    cancel() {
        this.addVisible = false;
        this.detailVisible = false;
    }
    async edit(row: any) {
        await checkCtiamButtonAuth(GetCtiamButtonAction("certModify"));
        // this.editVisible = true;
        (this.$refs.editDialog as any)?.open();
        this.editCertificate = row;
    }
    async del(certificate: SecretsItem) {
        await checkCtiamButtonAuth(GetCtiamButtonAction("certListDelete"));
        this.$set(certificate, "delLoading", true);
        try {
            const sans = (
                await this.$ctFetch<{ result: string[] }>(CertificateUrl.listDomainByCert, {
                    encodeParams: true,
                    data: {
                        certName: certificate.name,
                    },
                })
            ).result;

            this.$set(certificate, "delLoading", false);
            await this.delMsg(sans, certificate);
        } catch(err) {
            this.$errorHandler(err);
        } finally {
            this.$set(certificate, "delLoading", false);
        }

    }
    async delMsg(sans: string[], certificate: SecretsItem) {
        if (sans && sans.length) {
            const sansArr = sans.map((item: any) => {
                return item.domain;
            });
            await this.$alert(
                `${this.$t("certificate.del.tip1", { domain: sansArr.join("、") })}`,
                this.$t("certificate.del.title") as string
            );
            return;
        }

        await this.$confirm(
            this.$t("certificate.del.tip2") as string,
            this.$t("certificate.del.title") as string,
            {
                type: "warning",
            }
        );

        this.loading = true;
        await this.$ctFetch(CertificateUrl.deleteCert, {
            method: "POST",
            data: {
                certName: certificate.name,
            },
        });

        await this.fetchList();
        this.$message.success(this.$t("certificate.deleteCert") as string);
        this.loading = false;
    }
    async detail(certificate: SecretsItem) {
        await checkCtiamButtonAuth(GetCtiamButtonAction("certListDetail"));
        this.getCertificateDetail(certificate);
        this.detailVisible = true;
    }
    async getCertificateDetail(certificate: SecretsItem) {
        this.detailLoading = true;
        this.domainData = [];
        try {
            const rst = await this.$ctFetch<{ result: DetailItem[] }>(CertificateUrl.listDomainByCert, {
                encodeParams: true,
                data: {
                    certName: certificate.name,
                },
            });
            this.currentCertificate = {
                ...certificate,
                created: timeFormat(+certificate.created * 1000),
                validTime: this.transformTime(certificate.expires - certificate.issue),
                sans: rst.result || [],
            };
            (this.currentCertificate.sans || []).forEach(item => {
                const domain = (item.domain || "").split("@");
                //const productIndex = domain[1] as "000" | "001" | "002" | "003" | "004" | "005" | "006" | "007";
                //const domainType = this.productType[productIndex];
                this.domainData.push({
                    domain: domain[0],
                    //domainType: domainType,
                    //updateType: domain[1] === "005" || domain[1] === "006" ? "手动更新" : "自动更新",
                });
            });
        } catch (e) {
            this.detailVisible = false;
            setTimeout(() => {
                this.$errorHandler(e);
            }, 500);
        } finally {
            this.detailLoading = false;
        }
    }
    async fetchList({
        keyword = this.keyword,
        keywordType = this.keyword && this.keywordType,
        startTime = this.startTime,
        endTime = this.endTime,
        pageIndex = this.page,
        pageSize = this.perPage,
    } = {}) {
        this.loading = true;
        try {
            const rst = await this.$ctFetch<{ secrets: SecretsItem[]; paging: { total_record: number } }>(
                CertificateUrl.certList,
                {
                    encodeParams: true,
                    data: {
                        keyword: keyword.replace(/\?/g, "？"), // 兼容输入问号导致查询报错
                        keywordType,
                        startTime,
                        endTime,
                        pageIndex,
                        pageSize,
                    },
                }
            );
            // this.dataList = rst.list;
            // this.totalRecord = rst.total;
            this.dataList = rst.secrets;
            this.totalRecord = rst.paging.total_record;
        } catch (err) {
            const isCtiamError = CtiamCode.includes((err as any)?.data?.code);
            if (isCtiamError) {
                // 权限报错时清空数据
                this.dataList = [];
            }
            this.$errorHandler(err);
        } finally {
            this.loading = false;
        }
    }
    reset() {
        this.keyword = "";
        this.searchDate = [];

        // 重置查询条件后执行查询
        this.fetchList();
    }
    async showAdd() {
        await checkCtiamButtonAuth(GetCtiamButtonAction("certCreate"));
        this.addVisible = true;
        this.addCertificate = getDefaultCertParam();
        // this.$ctBus.$emit("fromListToUpdateDialog", false);
    }
    sizeChange(val: number) {
        // 恢复到第一页
        this.page = 1;
        this.perPage = val;
        this.fetchList();
    }
    async submitCertificate() {
        // 新增增加证书链校验
        this.loading = true;
        this.updateLoading = true;
        const data = {
            ...this.addCertificate,
            email: this.email, //新增email字段
            userName: ""
        }

        if (this.addCertificate.algorithm_type !== 1) {
            delete data.certs_sign;
            delete data.key_sign;
        }

        try {
            await this.$ctFetch(CertificateUrl.createCert, {
                method: "POST",
                body: data,
            });
        } catch (e) {
            store.$errorHandler(e);
            this.updateLoading = false;
            return;
        }

        await this.fetchList();
        this.updateLoading = false;
        this.$message.success(this.$t("domain.create.tip32") as string);
        this.addVisible = false;
    }
    transformTime(second: number) {
        const minute = Math.floor(second / 60);
        const space = this.isEn ? " " : "";
        if (minute < 60)
            return minute + space + (this.$t(`common.date.minute${minute > 1 ? "s" : ""}`) as string);
        const hour = Math.floor(minute / 60);
        if (hour < 24) return hour + space + (this.$t(`common.date.hour${hour > 1 ? "s" : ""}`) as string);
        const day = Math.floor(hour / 24);
        return day + space + (this.$t(`common.date.day${day > 1 ? "s" : ""}`) as string);
    }
    async batchBindDomain(certificate: SecretsItem) {
        await checkCtiamButtonAuth(GetCtiamButtonAction("certBind"));

        const isContinue = await this.checkCertIntegrity(certificate);
        if (!isContinue) return;

        this.$router.push({
            name: "certificate.batchBind",
            query: {
                name: certificate.name,
            },
        });
    }
    async checkCertIntegrity(certificate: SecretsItem) {
        if (!certificate.is_chain_complete) {
            await this.$confirm(
                `${this.$t("domain.您使用的证书存在证书链不完整的情况，存在一定安全隐患，如您仍需提交则点击“继续”按钮进行提交，如您想取消操作则点击“取消”按钮。")}`,
                `${this.$t("certificate.chain.title")}`,
                {
                    confirmButtonText: `${this.$t("certificate.chain.ensure")}`,
                    cancelButtonText: `${this.$t("common.dialog.cancel")}`,
                    showClose: false,
                    iconClass: "el-icon-warning",
                }
            );
        }

        return true
    }
    private stateFormatter(row: any) {
        if (row.state === 0) {
            return this.$t("certificate.status.opt[0]");
        } else if (row.state === 1) {
            return this.$t("certificate.status.opt[1]");
        } else if (row.state === 2) {
            return this.$t("certificate.status.opt[2]");
        } else {
            return "";
        }
    }
}
</script>

<style lang="scss" scoped>
.pager {
    text-align: right;
    margin-top: 8px;
}

.el-form-item {
    margin-bottom: 20px;
}

.alert {
    color: $g-color-yellow;
}

.search-bar-wrapper {
    display: flex;
    justify-content: space-between;
    @media (max-width: 1412px) {
        display: grid;
        grid-template: 1fr / 1fr;
        gap: 12px;
    }
    .search-bar-container {
        min-width: 970px;
        display: flex;
        justify-content: flex-end;
    }
    .search-bar-reset-btn {
        margin-right: 0;
    }
}

.search-label {
    margin-right: 16px;
    font-size: 12px;
    color: #333 !important;
}
.ml-12 {
    margin-left: 12px;
}

.increse-width {
    min-width: 140px !important;
}

.icon-alert {
    font-size: 14px;
}

.danger-style {
    color: $color-danger;
}
</style>
