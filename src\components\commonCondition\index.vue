<template>
    <div class="common-condition">
        <el-form-item :prop="`${propPrefix}mode`" :rules="getRules.mode">
            <span slot="label">
                {{ $t("domain.type") }}
                <el-tooltip
                    v-if="!required"
                    placement="top"
                    effect="dark"
                    :content="$t('domain.editPage.placeholder14')"
                >
                    <ct-svg-icon icon-class="question-circle" class-name="ct-sort-drag-icon"></ct-svg-icon>
                </el-tooltip>
            </span>
            <el-radio-group v-model.number="form.mode" @change="typeChange" class="cache-form--selector">
                <el-radio
                    v-for="opt in CacheModeOptions"
                    :key="opt.value"
                    :label="opt.value"
                    :disabled="opt.disabled"
                >
                    {{ opt.label }}
                </el-radio>
            </el-radio-group>
            <el-tooltip :disabled="!isEdit" :content="$t('ipSet.重置')" placement="top">
                <i
                    v-if="showResetBtn && !required"
                    :style="{
                        alignSelf: 'center',
                        cursor: isEdit ? 'pointer' : 'not-allowed',
                        fontSize: '14px',
                        marginLeft: '12px',
                    }"
                    :class="['aocdn-ignore-link', 'el-icon-refresh-right']"
                    @click="isEdit && reset()"
                />
            </el-tooltip>
        </el-form-item>
        <el-form-item
            key="content"
            :label="$t('domain.content')"
            :prop="`${propPrefix}content`"
            :rules="getRules.content"
        >
            <el-input
                v-model="form.content"
                :placeholder="fileTypePlaceholder"
                :disabled="form.mode === 2 || form.mode === 3"
                @focus="showNameSet"
                @change="contentChange"
            ></el-input>
        </el-form-item>

        <file-suffix-dialog
            :title="suffixTitle"
            :form="suffixDialogForm.form"
            :visible="suffixDialogForm.visible"
            @cancel="suffixDialogForm.visible = false"
            @submit="suffixDialogFormSubmit"
        />
    </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from "vue-property-decorator";
import { cacheModeOptions } from "@/components/simpleform/nAlogicCacheTable/mixin";
import fileSuffixDialog from "@/components/fileSuffixDialog/index.vue";
import ctSvgIcon from "@/components/ctSvgIcon/index.vue";
import { getConditionContentPlaceholder } from "./utils";

type InnerForm = {
    mode: number;
    content: string;
};

@Component({
    name: "commonCondition",
    components: {
        fileSuffixDialog,
        ctSvgIcon,
    },
})
export default class CommonCondition extends Vue {
    @Prop({ default: { mode: "", content: "" } }) public value!: InnerForm;
    // 当前已添加的condition列表，用于禁用、校验类型选项
    @Prop({ default: [] }) public list!: InnerForm[];
    // 后缀名弹窗title透传
    @Prop({ default: "" }) public suffixTitle!: string;
    // 是否需要校验必填，默认true
    @Prop({ default: true }) public required!: boolean;
    // prop前缀
    @Prop({ default: "" }) public propPrefix!: string;
    // 是否显示重置按钮,仅非必填时有效; 重置时会触发clearValidate，需要父级表单调用clearValidate方法
    @Prop({ default: false }) public showResetBtn!: boolean;
    // 是否可编辑
    @Prop({ default: true }) public isEdit!: boolean;

    public form = this.value;
    public suffixDialogForm: {
        visible: boolean;
        form: InnerForm | null;
    } = {
        visible: false,
        form: null,
    };

    @Watch("value", { immediate: true, deep: true })
    onValueChange(val: InnerForm) {
        this.form = { ...val };
    }

    public async typeChange(val: number) {
        if (val === 2 || val === 3) {
            this.form.content = "/";
        } else {
            this.form.content = "";
        }

        this.$emit("input", this.form);
        this.$emit("validate");
    }

    public contentChange() {
        this.$emit("input", this.form);
    }

    public showNameSet(e: { srcElement?: HTMLInputElement }) {
        // 内容为后缀名才弹出弹框
        if (this.form.mode !== 0) return;
        this.suffixDialogForm = {
            visible: true,
            form: { ...this.form },
        };
        e?.srcElement?.blur();
    }

    public suffixDialogFormSubmit(content: string) {
        this.form.content = content;
        this.$emit("input", this.form);
        this.suffixDialogForm.visible = false;
    }

    public reset() {
        this.form.mode = null!;
        this.form.content = "";
        this.$nextTick(() => this.$emit("clear-validate"));
        this.$emit("input", this.form);
    }

    get CacheModeOptions() {
        return cacheModeOptions?.map(option => {
            let disabled = false;

            // mode 2、3 只支持选一个
            if (option.value === 2 || option.value === 3) {
                disabled = this.list.some(item => item.mode === option.value);
            }

            return {
                disabled,
                ...option,
            };
        });
    }

    get fileTypePlaceholder() {
        return getConditionContentPlaceholder(this.CacheModeOptions, this.form.mode);
    }

    get getRules() {
        return {
            mode: [
                { required: this.required, message: this.$t("domain.detail.tip42"), trigger: "change" },
                {
                    validator: (rule: any, value: number, callback: Function) => {
                        // 如果只填内容则在类型上提示：“类型和内容需要同时配置”
                        if (
                            this.form.content !== "" &&
                            !this.CacheModeOptions.map(opt => opt.value).includes(this.form.mode)
                        ) {
                            callback(new Error(this.$t("domain.htmlForbid.forbid5") as string));
                        }
                        callback();
                    },
                },
            ],
            content: [
                {
                    validator: (rule: any, value: string, callback: Function) => {
                        const paths = value?.split(",");
                        const allPathPattern = new RegExp("^([A-Za-z0-9]|[!#$&'()*+,-./:;=?@_~])*$");
                        paths?.forEach(path => {
                            if (this.form.mode === 4 && (path.includes("?") || path.includes("？")))
                                callback(new Error(this.$t("domain.detail.placeholder48") as string));
                            if (this.form.mode === 4 && path.length > 0 && path[0] !== "/")
                                callback(this.$t("domain.detail.placeholder49"));
                        });
                        if (this.form.mode === 0 && value === "")
                            callback(new Error(this.$t("domain.htmlForbid.forbid4") as string));
                        if (this.form.mode === 0 && !/^\w{1,9}(,\w{1,9})*$/.test(value))
                            callback(this.$t("domain.detail.placeholder50"));
                        if (this.form.mode === 1 && value === "")
                            callback(new Error(this.$t("domain.detail.placeholder73") as string));
                        if (this.form.mode === 1 && !/^((\/[\w-.]+)+,)*(\/[\w-.]+)+$/.test(value))
                            callback(this.$t("domain.detail.placeholder51"));
                        if (this.form.mode === 2 && value === "")
                            callback(this.$t("domain.detail.placeholder52"));
                        if (this.form.mode === 3 && value === "")
                            callback(this.$t("domain.detail.placeholder53"));
                        if (this.form.mode === 4 && value.trim() === "")
                            callback(new Error(this.$t("domain.detail.placeholder74") as string));
                        if (this.form.mode === 4 && !allPathPattern.test(value))
                            callback(this.$t("domain.detail.placeholder14"));
                        else callback();
                    },
                    trigger: ["blur", "change"],
                },
                { required: this.required, message: this.$t("domain.detail.placeholder54"), trigger: "blur" },
            ],
        };
    }
}
</script>
