<template>
    <el-dialog
        :title="$t('domain.list.opennote')"
        :append-to-body="true"
        :close-on-click-modal="false"
        :visible.sync="dialogVisible"
        :before-close="cancel"
        class="tip-dialog"
        width="480px"
        :show-close="true"
    >
        <!-- <div class="text1">
            功能实现原理:
            边缘安全加速通过代理访问外链源站，在边缘云节点进行外链地址改写，并对外链提供IPv6加速，能够解决网站'天窗问题'。
        </div> -->
        <el-alert title="" type="info" :closable="false" show-icon class="ct-alert">
            <template slot="title">
                <div>
                    {{ $t("domain.detail.placeholder34") }}
                </div>
            </template>
        </el-alert>
        <div class="text2">
            {{ $t("domain.detail.placeholder35") }}
        </div>
        <div slot="footer" class="btns">
            <el-button @click="cancel">{{ $t("domain.cancel") }}</el-button>
            <el-button type="primary" @click="submit">{{ $t("domain.comfirm") }}</el-button>
        </div>
    </el-dialog>
</template>

<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";
import pattern from "@/config/pattern";

@Component({})
export default class UpdateDialog extends Vue {
    private pattern = pattern;
    @Prop({ default: false, type: Boolean }) private dialogVisible!: boolean;

    private async submit() {
        this.$emit("submit");
    }
    private cancel() {
        this.$emit("cancel", "tipDialogVisible");
    }
}
</script>

<style lang="scss" scoped>
.tip-dialog {
    .ct-alert {
        ::v-deep {
            .el-alert {
                align-items: center;
                padding: 12px;
            }
        }
    }
    // .btns {
    //     text-align: end;
    // }
    .text1 {
        width: 416px;
        height: 56px;
        padding-top: 20px;
        background-color: $color-master-bg-light-2;
    }
    .text2 {
        margin-top: 20px;
        color: $color-warning;
    }
    // ::v-deep {
    //     .el-dialog__header {
    //         border-bottom: 0;
    //     }
    // }
}
</style>
