<template>
    <ct-section-wrap headerText="违规图片" headerTip="支持一年内、最长时间跨度为一个月的违规图片查询。">
        <ct-box :class="[{ 'fix-box': dataList.length > 11 }, 'table-scroll-wrap']">
            <div class="search-bar">
                <el-select v-model="domain" placeholder="全部域名" filterable :loading="domainOptionsLoading" :loading-text="$t('common.loading')">
                    <el-option
                        v-for="opt in domainOptions"
                        :key="opt.value"
                        :label="opt.label"
                        :value="opt.value"
                    />
                </el-select>
                <el-select class="mini" v-model="authState" placeholder="系统判定" filterable clearable>
                    <el-option
                        v-for="opt in authStateOptions"
                        :key="opt.value"
                        :label="opt.label"
                        :value="opt.value"
                    />
                </el-select>
                <el-select class="mini" v-model="markingState" placeholder="人工判定" filterable clearable>
                    <el-option
                        v-for="opt in markingStateOptions"
                        :key="opt.value"
                        :label="opt.label"
                        :value="opt.value"
                    />
                </el-select>
            </div>
            <div class="search-bar">
                <el-date-picker
                    v-model="dateRange"
                    type="datetimerange"
                    :clearable="false"
                    value-format="timestamp"
                    :default-time="['00:00:00', '23:59:59']"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    :picker-options="pickerOptions"
                    @blur="blurDate"
                    class="tip"
                />
                <el-button type="primary" @click="refresh = true">查 询</el-button>
                <el-button @click="download">导 出</el-button>
            </div>

            <ct-table
                :dataList="dataList"
                :columnConfig="conlumnConfig"
                :tableConfig="{ stripe: false }"
                :pagerData="{ pageNum, pageSize }"
                @selection-change="handleSelectionChange"
                v-loading="loading"
            >
                <template slot="url-slot" slot-scope="{ scope }">
                    <span class="operation" @click="openImg(scope.row)">{{ scope.row.blockUrl }}</span>
                </template>
                <template slot="judgement-slot" slot-scope="{ scope }">
                    <el-dropdown @command="handleCommand">
                        <span class="el-dropdown-link">
                            {{ scope.row.markingState }}<i class="el-icon-arrow-down el-icon--right"></i>
                        </span>
                        <el-dropdown-menu slot="dropdown">
                            <!-- <el-dropdown-item command="未打标">未打标</el-dropdown-item> -->
                            <el-dropdown-item disabled> 未打标 </el-dropdown-item>
                            <el-dropdown-item :command="beforeHandleCommand(scope, 1)">正常</el-dropdown-item>
                            <el-dropdown-item :command="beforeHandleCommand(scope, 2)">涉黄</el-dropdown-item>
                            <el-dropdown-item :command="beforeHandleCommand(scope, 3)">涉政</el-dropdown-item>
                            <el-dropdown-item :command="beforeHandleCommand(scope, 4)">
                                涉暴恐
                            </el-dropdown-item>
                        </el-dropdown-menu>
                    </el-dropdown>
                </template>
                <template slot="operation-slot" slot-scope="{ scope }">
                    <template v-if="scope.row.blockState === 1 || scope.row.blockState === 4">
                        <el-button type="text" @click="doUnblock(scope.row)">一键解封</el-button>
                    </template>
                    <template v-else>
                        <el-button type="text" @click="doBlock(scope.row)">一键封禁</el-button>
                    </template>
                </template>
            </ct-table>
            <ct-pager class="pager" :refresh.sync="refresh" :loadData="search" ref="pager"></ct-pager>
        </ct-box>
    </ct-section-wrap>
</template>

<script>
import TableAndPagerActionMixin from "@/mixins/tableAndPagerAction";
import { DomainModule } from "@/store/modules/domain";
import { DomainActionEnum, getDomainAction } from "@/config/map";
import { getAm0 } from "@/utils";
import { downloadCsv } from "@/utils";
import { nReviewUrl } from "@/config/url/ncdn/nreview";
import { nUserModule } from "@/store/modules/nuser";

export default {
    mixins: [TableAndPagerActionMixin],
    computed: {
        isFcdnCtyunCtclouds() {
            return nUserModule.isFcdnCtyunCtclouds;
        },
        domainAction() {
            return this.isFcdnCtyunCtclouds
                ? getDomainAction("ContentAuditViolations")
                : DomainActionEnum.Review;
        },
        // 这个变量貌似没有用到
        domainList() {
            // fcdn-ctyun 体系（国内+国际）需要取ctiam权限管控下的域名数据
            return DomainModule[this.domainAction].list;
        },
        domainOptionsLoading() {
            return DomainModule[this.domainAction].loading;
        },
        domainOptions() {
            // fcdn-ctyun 体系（国内+国际）需要取ctiam权限管控下的域名数据
            const list = [...DomainModule[this.domainAction].options];
            // 不传域名则默认传递全部域名
            list.unshift({
                value: "",
                label: "全部域名",
            });
            return list;
        },
        searchParams: function () {
            const startTime = this.dateRange[0] ? this.timeFormat(this.dateRange[0]) : this.dateRange[0];
            const endTime = this.dateRange[1] ? this.timeFormat(this.dateRange[1]) : this.dateRange[1];
            return {
                domain: this.domain,
                authState: this.authState,
                markingState: this.markingState,
                startTime,
                endTime,
            };
            // 获取所有后端假数据
            // return {
            //     domain: "",
            // };
        },
    },
    data() {
        return {
            routeName: this.$route.name,
            domain: "",
            searchUrl: nReviewUrl.contentBlockList,
            authState: "",
            authStateOptions: [
                // 初始状态不需要展示
                // {
                //     value: 0,
                //     label: "初始状态",
                // },
                {
                    value: 1,
                    label: "涉黄",
                },
                {
                    value: 2,
                    label: "涉政",
                },

                {
                    value: 3,
                    label: "涉暴恐",
                },
                {
                    value: 4,
                    label: "疑似涉黄",
                },
                {
                    value: 5,
                    label: "疑似涉政",
                },

                {
                    value: 6,
                    label: "疑似涉暴恐",
                },
            ],
            markingState: "",
            markingStateOptions: [
                {
                    value: 0,
                    label: "未打标",
                },
                {
                    value: 1,
                    label: "正常",
                },
                {
                    value: 2,
                    label: "涉黄",
                },
                {
                    value: 3,
                    label: "涉政",
                },
                {
                    value: 4,
                    label: "涉暴恐",
                },
            ],
            dateRange: "",
            pickerMinDate: "",
            pickerOptions: {
                onPick: obj => {
                    this.pickerMinDate = new Date(obj.minDate).getTime();
                },
                disabledDate: time => {
                    if (this.pickerMinDate) {
                        const day31 = 31 * 24 * 3600 * 1000;
                        const maxTime = this.pickerMinDate + day31;
                        const minTime = this.pickerMinDate - day31;
                        return (
                            time.getTime() > maxTime - 1 ||
                            time.getTime() < minTime + 1 ||
                            time.getTime() > +getAm0(new Date()) + 24 * 3600 * 1000 - 1 ||
                            time.getTime() < Date.now() - 365 * 24 * 3600 * 1000
                        );
                    } else {
                        return (
                            time.getTime() > +getAm0(new Date()) + 24 * 3600 * 1000 - 1 ||
                            time.getTime() < Date.now() - 365 * 24 * 3600 * 1000
                        );
                    }
                },
            },
            multipleSelection: [],
            authStateList: new Map([
                // 初始状态不需要展示
                // [0, "初始状态"],
                [1, "涉黄"],
                [2, "涉政"],
                [3, "涉暴恐"],
                [4, "疑似涉黄"],
                [5, "疑似涉政"],
                [6, "疑似涉暴恐"],
            ]),
            markingStateList: new Map([
                [0, "未打标"],
                [1, "正常"],
                [2, "涉黄"],
                [3, "涉政"],
                [4, "涉暴恐"],
            ]),
            dataList: [],
            conlumnConfig: [
                {
                    prop: "authTime",
                    label: "日期",
                    width: "110",
                    align: "left",
                },
                {
                    type: "slot",
                    name: "url-slot",
                    baseConfig: {
                        prop: "blockUrl",
                        label: "url",
                        minWidth: "200",
                        align: "center",
                    },
                },
                {
                    prop: "authState",
                    label: "系统判定结果",
                    minWidth: "120",
                    align: "left",
                },
                {
                    type: "slot",
                    name: "judgement-slot",
                    baseConfig: {
                        prop: "markingState",
                        label: "人工判定结果",
                        minWidth: "120",
                        align: "left",
                    },
                },
                {
                    type: "slot",
                    name: "operation-slot",
                    baseConfig: {
                        label: "操作",
                        width: "150",
                        align: "left",
                    },
                },
            ],
        };
    },

    created() {
        const now = new Date();
        const d0am = getAm0(now); // 当日0点
        // const d31ago = new Date(+d0am - 30 * 24 * 3600 * 1000); // 31天前
        // 默认以当前时间结束
        this.dateRange = [+d0am, +now];
    },

    // watch: {
    //     domainOptions: function(arr) {
    //         if (arr.length > 0) {
    //             this.domain = arr[0].value;
    //             this.refresh = true;
    //         }
    //     },
    // },

    watch: {
        domainOptions: {
            handler(newval) {
                if (newval.length > 1) {
                    this.$nextTick(() => {
                        this.domain = newval[0].value;
                        this.refresh = true;
                    });
                }
            },
            immediate: true,
        },
    },

    methods: {
        // 将时间戳格式化为yyyy-MM-dd
        timeFormat(time) {
            const date = new Date(time);
            const year = date.getFullYear();
            let month = date.getMonth() + 1;
            let day = date.getDate();
            month = month < 10 ? "0" + month : month;
            day = day < 10 ? "0" + day : day;
            return year + "-" + month + "-" + day;
        },
        dataListFormat(list) {
            for (const item of list) {
                // 将10位时间戳 * 1000转化为13位时间戳
                item.authTime = this.timeFormat(item.authTime * 1000);
                item.authState = this.authStateList.get(item.authState);
                item.markingState = this.markingStateList.get(item.markingState);
            }
            return list;
        },
        async download() {
            const startTime = this.dateRange[0] ? this.timeFormat(this.dateRange[0]) : this.dateRange[0];
            const endTime = this.dateRange[1] ? this.timeFormat(this.dateRange[1]) : this.dateRange[1];
            const result = await this.$ctFetch(nReviewUrl.contentBlockList, {
                data: {
                    domain: this.domain,
                    authState: this.authState,
                    markingState: this.markingState,
                    startTime,
                    endTime,
                    pageIndex: 1,
                    pageSize: 9999,
                },
            });
            const list = this.dataListFormat(result.list);
            let str = `日期,url,系统判定结果,人工判定结果,操作\n`;
            for (const item of list) {
                str += item.authTime + ",";
                str +=
                    (item.blockUrl.includes(",")
                        ? '"' + item.blockUrl.replace(/"/g, '""') + '"'
                        : item.blockUrl) + ",";
                str += item.authState + ",";
                str += item.markingState + ",";
                if (item.blockState === 1 || item.blockState === 4) {
                    str += "一键解封" + ",\n";
                } else {
                    str += "一键封禁" + ",\n";
                }
            }
            downloadCsv("违规图片", str);
        },
        handleSelectionChange(val) {
            this.multipleSelection = val;
        },
        beforeSearch() {
            // 默认展示暂定为所有域名，所有系统判定类型，所有人工判定类型的近30日数据，因此不再进行以下检查
            // if (this.authState !== 0 && !this.authState) {
            //     this.$message("请选择系统判定类型");
            //     return false;
            // }
            // if (this.markingState !== 0 && !this.markingState) {
            //     this.$message("请选择人工判定类型");
            // }
            return true;
        },
        // 重新选择日期时，一年内的日期均可选
        blurDate() {
            this.pickerMinDate = "";
        },
        openImg(row) {
            window.open(row.blockUrl);
        },
        beforeHandleCommand(scope, command) {
            return { scope: scope, command: command };
        },
        // 违规图片打标
        async handleCommand(command) {
            command.scope.row.markingState = this.markingStateList.get(command.command);
            let markingStateNum = 0;
            this.markingStateList.forEach(function (value, key) {
                // 根据选择的标记类型，取得对应的数字标号
                if (value === command.scope.row.markingState) {
                    markingStateNum = key;
                }
            });
            await this.$ctFetch(nReviewUrl.contentBlockMarking, {
                data: {
                    blockInfoId: command.scope.row.blockInfoId,
                    markingState: markingStateNum,
                    signature: command.scope.row.signature,
                },
            });
        },
        // 一键解封
        async doUnblock(row) {
            this.loading = true;
            await this.$ctFetch(nReviewUrl.contentUnblock, {
                method: "POST",
                transferType: "json",
                data: {
                    blockIds: [
                        {
                            blockInfoId: String(row.blockInfoId),
                            signature: row.signature,
                        },
                    ],
                },
                headers: {
                    "Content-Type": "application/json",
                },
            });
            this.$refs["pager"].getData();
        },
        // 一键封禁
        async doBlock(row) {
            this.loading = true;
            await this.$ctFetch(nReviewUrl.contentBlock, {
                method: "POST",
                transferType: "json",
                data: {
                    blockIds: [
                        {
                            blockInfoId: String(row.blockInfoId),
                            signature: row.signature,
                        },
                    ],
                },
                headers: {
                    "Content-Type": "application/json",
                },
            });
            this.$refs["pager"].getData();
        },
    },
};
</script>

<style lang="scss" scoped>
.search-bar {
    display: flex;
}
.search-bar + .search-bar {
    margin-top: 8px;
}
.el-dropdown-link {
    cursor: pointer;
}
.operation {
    text-decoration: underline;
    cursor: pointer;
}
// 非小屏才需要，小屏下还是滚动吧
@media screen and (min-width: $g-media-xs) {
    .fix-box {
        display: flex;
        flex-direction: column;
        height: calc(100vh - 50px - 90px - 40px);
        @include g-height(calc(100vh - 50px - 90px - 40px), calc(100vh - 50px - 90px - 40px - 60px));
        .el-table {
            flex: 1;
        }
    }
    ::v-deep .el-table__body-wrapper {
        height: 100%;
        overflow-y: auto;
    }
}
//更改表格滚动条样式
::v-deep {
    .el-table {
        ::-webkit-scrollbar {
            width: 0.25rem;
            height: 0.25rem;
            background-color: rgba(144, 147, 153, 0.1);
        }
        ::-webkit-scrollbar-track {
            border-radius: 0;
        }
        ::-webkit-scrollbar-thumb {
            border-radius: 0;
            background-image: linear-gradient(
                135deg,
                rgba(144, 147, 153, 0.3) 0%,
                rgba(144, 147, 153, 0.3) 72%,
                rgba(144, 147, 153, 0.3) 100%
            );
            transition: all 0.2s;
            border-radius: 0.25rem;
            &:hover {
                background-color: rgba(144, 147, 153, 0.5);
            }
        }
    }
    .el-date-editor .el-range-separator {
        padding: 0;
    }
}
</style>
