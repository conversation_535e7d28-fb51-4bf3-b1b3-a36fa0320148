const path = require("path");
const fs = require("fs");
const isProduction = process.env.NODE_ENV === "production";

const defaultCatchClause = identifier => `
    console.error(${identifier});
    var errMsg = ${identifier} && ${identifier}.data && ${identifier}.data.reason || ${identifier}.reason || ${identifier};
    if(${identifier} !== 'cancel' && typeof errMsg === 'string') {
        if(this && this.$message) {
            this.$message.error(errMsg);
        }
        if(this && this.store && this.store.$message) {
            this.store.$message.error(errMsg);
        }
    }
`;
const defaultFinallyClause = `
    if(this && this.hasOwnProperty('loading')) {
        this.loading = false;
    }
`;

const defaultPolyfills = ["es.array.iterator", "es.promise", "es.object.assign", "es.promise.finally", "es.typed-array.float32-array"];

module.exports = (context, options = {}) => {
    const presets = [];
    const plugins = [];

    // 解构配置参数
    const {
        targets,
        asyncCatchClause: catchClause,
        asyncFinallyClause: finallyClause,
        polyfills = [],
        ...vueAppOptions
    } = options;

    // 根据 ie 兼容性设定的情况，选择是否接入
    // 参数中的 targets 或者 package 中的 browserslist
    const isIe = b => b.includes("ie") || b.includes("IE");
    const includeIe = () => {
        let browserslist = targets;
        if (!browserslist) {
            let pkg = fs.readFileSync(path.join(process.cwd(), "package.json"));
            pkg = JSON.parse(pkg.toString());
            browserslist = pkg.browserslist;
        }

        if (browserslist) {
            if (typeof browserslist === "string" && isIe(browserslist)) {
                return true;
            }
            if (Array.isArray(browserslist) && browserslist.some(b => isIe(b))) {
                return true;
            }
        }

        return false;
    };
    if (includeIe()) {
        plugins.unshift([require("@babel/plugin-transform-object-set-prototype-of-to-assign")]);
        defaultPolyfills.push("es.symbol");
    }

    // 移除 log
    if (isProduction) {
        plugins.unshift([
            require("babel-plugin-transform-remove-console"),
            {
                exclude: ["warn", "error"], // 移除console.log console.info
            },
        ]);
    }

    // try catch
    plugins.push([
        require("./babel-plugin-async-catch"),
        {
            catchClause: catchClause || defaultCatchClause,
            finallyClause: finallyClause || defaultFinallyClause,
        },
    ]);

    // 接入 @vue/babel-preset-app
    presets.push([
        require("@vue/babel-preset-app"),
        {
            exclude: ["transform-async-to-generator"], // 配合 babel-plugin-async-catch
            ...vueAppOptions,
            // 简化配置，做增量而不是覆盖
            polyfills: [...defaultPolyfills, ...polyfills],
        },
    ]);

    return {
        presets,
        plugins,
    };
};
