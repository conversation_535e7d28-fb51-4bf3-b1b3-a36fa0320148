/*
 * @Description: 统计分析 QueryDataQpsList 接口类型注解，涉及页面：统计分析-请求数
 * @Author: wang yuegong
 */

// QueryDataQpsList 接口中按 5min 的统计数据（用于请求数 tab）
export interface RequestQps5Min {
    all: number;
    httpDynamic: number;
    httpsDynamicQPS: number;
    httpStaticQPS: number;
    httpDynamicQPS: number;
    httpsStatic: number;
    allQPS: number;
    httpStatic: number;
    httpsStaticQPS: number;
    httpsDynamic: number;
    websocket: number;
    websocketQPS: number;
    timestamp: number;
    optimizeDynamic: number;
    optimizeDynamicQPS: number;
}

export interface RequestQpsDaily {
    all: number;
    httpDynamic: number;
    httpStatic: number;
    httpsStatic: number;
    httpsDynamic: number;
    timestamp: string;
    websocket: number;
}

// QueryFetchData 接口数据（用于请求数 tab）
export interface RequestQpsFetchData {
    "5min": RequestQps5Min[];
    daily: RequestQpsDaily[];
    total: RequestQps5Min;
    totalHttpDynamic: string;
    totalHttpStatic: string;
    totalHttpsDynamic: string;
    totalHttpsStatic: string;
    scale: number;
}
