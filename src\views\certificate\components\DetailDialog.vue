<template>
    <el-dialog
        :title="$t('certificate.detail.title')"
        :close-on-click-modal="false"
        :visible.sync="detailVisible"
        :before-close="cancel"
        class="detail-dialog"
        append-to-body
    >
        <el-alert
            type="info"
            class="mgb20"
            :title="$t('certificate.detail.tip2')"
            :closable="false"
        />
        <div class="detail-list" v-loading="detailLoading">
            <div class="detail-content-wrapper">
                <label>{{ $t("certificate.证书类型") }}</label>
                <div class="detail-content">{{ certificate.algorithm_type === 1 ? $t("certificate.国密证书") : $t("certificate.国际标准证书") }}</div>
            </div>
            <div class="detail-content-wrapper">
                <label>{{ $t("certificate.detail.label[0]") }}</label>
                <div class="detail-content">{{ certificate.name }}</div>
            </div>
            <div class="detail-content-wrapper">
                <label>{{ $t("certificate.detail.label[1]") }}</label>
                <div class="detail-content">{{ certificate.cn }}</div>
            </div>
            <div class="detail-content-wrapper">
                <label>{{ $t("certificate.detail.label[2]") }}</label>
                <div class="detail-content">{{ certificate.created }}</div>
            </div>
            <div class="detail-content-wrapper">
                <label>{{ $t("certificate.detail.label[3]") }}</label>
                <div class="detail-content">{{ certificate.issuer }}</div>
            </div>
            <div class="detail-content-wrapper">
                <label>{{ $t("certificate.detail.label[4]") }}</label>
                <div class="detail-content">{{ certificate.validTime }}</div>
            </div>
            <div class="detail-content-wrapper">
                <label>{{ $t("certificate.detail.label[5]") }}</label>
                <div class="detail-content">
                    <p v-for="(item, key) in certificate.sans" :key="key" class="tip-style">
                        <span>{{ domainSans(item) }}</span>
                        <span v-if="getProductName(item)">
                            {{ getProductName(item) }}
                        </span>
                    </p>
                </div>
            </div>
        </div>
    </el-dialog>
</template>

<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";
import { nUserModule } from "@/store/modules/nuser";
import { get } from "lodash-es";
import type { DetailItem, SecretsItem } from "@/types/certificate";

@Component({})
export default class DetailDialog extends Vue {
    // 弹框显示标志位
    @Prop({ default: false, type: Boolean }) private detailVisible!: boolean;
    // 加载状态标志位
    @Prop({ default: false, type: Boolean }) private detailLoading!: boolean;
    // 证书详情
    @Prop({ default: {}, type: Object }) private certificate!: SecretsItem;
    // 接口字段处理
    private domainSans(item: DetailItem) {
        return (item.domain || "").split("@")[0];
    }

    // 当前语言
    get isEn() {
        return nUserModule.lang === "en";
    }

    // 产品名称
    private getProductName(item: DetailItem) {
        const product = item.product_code;
        const productName = get(this.$t("common.allProductName"), product);
        if (!productName) return "";

        return this.isEn ? `(${productName})` : `（${productName}）`;
    }

    private cancel() {
        this.$emit("cancel");
    }
}
</script>

<style scoped lang="scss">
.detail-dialog {
    ::v-deep .el-dialog {
        @include g-width(90%, 50%, 30%);
    }

    .detail-list {
        margin-top: 10px;
        .detail-content-wrapper {
            margin-bottom: 16px;
        }

        label {
            display: inline-block;
            width: 150px;
            text-align: left;
            color: #666;
        }

        .detail-content {
            width: calc(100% - 150px);
            color: #292b32;
            display: inline-block;
            vertical-align: top;
        }
    }

    .tip-style {
        margin-bottom: $margin-3x;
    }
}
</style>
