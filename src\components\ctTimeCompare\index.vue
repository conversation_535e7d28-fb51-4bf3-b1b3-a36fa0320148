<!--
 * @Description: 有对比关系的2个时间选择组件
 * @Author: wang yuegong
-->

<template>
    <div class="ct-time-compare">
        <el-button :size="size" v-show="!compareShowLocal" @click="handleDataCompare">{{
            $t("common.ctTimeCompare.buttonText")
        }}</el-button>

        <template v-if="compareShowLocal">
            <el-date-picker
                :size="size"
                v-model="timeRangeLocal1"
                type="datetimerange"
                @change="pickerOptions1.reset()"
                :start-placeholder="$t('common.datePicker.start')"
                :end-placeholder="$t('common.datePicker.end')"
                :default-time="['00:00:00', '23:59:59']"
                :picker-options="pickerOptions1"
                @focus="handlePickerFocus"
                @blur="handlePickerBlur"
            />
            <span style="margin: 0 8px">{{ $t("common.ctTimeCompare.spanText") }}</span>
            <el-date-picker
                :size="size"
                v-model="timeRangeLocal2"
                type="datetimerange"
                @change="pickerOptions2.reset()"
                :start-placeholder="$t('common.datePicker.start')"
                :end-placeholder="$t('common.datePicker.end')"
                :default-time="['00:00:00', '23:59:59']"
                :picker-options="pickerOptions2"
                @focus="handlePickerFocus"
                @blur="handlePickerBlur"
            />
            <el-button type="text" class="el-icon-close" :size="size" @click="compareShowLocal = false" />
        </template>
    </div>
</template>
<script lang="ts">
import { Component, Prop, Watch, Vue } from "vue-property-decorator";
import { DatePickerOptions } from "element-ui/types/date-picker";
import { getAm0 as get0 } from "../../utils";
import CtTimePicker from "../ctTimePicker/index.vue";
import dayjs from "dayjs";

interface MyDatePickerOptions extends DatePickerOptions {
    reset(): void;
}

@Component({
    name: "CtTimeCompare",
    components: { CtTimePicker },
    model: {
        prop: "timeRangeArr",
        event: "select",
    },
})
export default class CtTimeCompare extends Vue {
    @Prop({ type: Number, default: 365 }) private maxDayBeforeNow!: number;
    @Prop({ type: Number, default: 31 * 24 * 60 * 60 * 1000 }) private maxTimeRangeLength!: number;
    @Prop({ type: String, default: "small" }) private size!: string;
    @Prop({ type: Boolean, default: false }) private compareShow!: boolean; // 是否展示对比选择
    @Prop({ type: Array, default: () => [null, null] }) private timeRangeArr!: Date[][];

    private compareShowLocal = false;
    private originalPickerChangeMethod: any = null; // 保存原始的 picker change 方法

    private timeRangeLocal1: null | Date[] = this.timeRangeArr[0];
    private timeRangeLocal2: null | Date[] = this.timeRangeArr[1];

    private minDate1: null | number = null;
    private minDate2: null | number = null;
    private pickerOptions1: MyDatePickerOptions = {
        // 设置禁用时间
        disabledDate: (time: Date) => {
            // 获取今天0点数据
            const today0 = get0(new Date());
            // 禁用大于今天时间23：59：59
            if (+time > +new Date(+today0 + 24 * 60 * 60 * 1000 - 1)) return true;

            // 禁用小于设置的最早期限的，由于今天会算1天，需要倒推 x-1 天
            if (+time < +today0 - (this.maxDayBeforeNow - 1) * 24 * 60 * 60 * 1000) return true;
            //当最小时间为null的时候则关闭禁用，点清空时会变为0，所以0也要判断
            if (this.minDate1 === null || this.minDate1 === 0) return false;

            // 由于现在选择时间后，默认会调整到当日23:59:59，跨度30天即为31天，需要减掉 1s
            const oneMonth = this.maxTimeRangeLength - 1000;
            // 超出当前选择时间前后31天的不可选择，time为选择框中出现的日期

            return +time > this.minDate1 + oneMonth || +time < this.minDate1 - oneMonth;
        },
        onPick: ({ minDate }) => {
            this.minDate1 = +minDate;
        },
        reset: () => {
            this.minDate1 = null;
        },
    };
    private pickerOptions2: MyDatePickerOptions = {
        // 设置禁用时间
        disabledDate: (time: Date) => {
            // 获取今天0点数据
            const today0 = get0(new Date());
            // 禁用大于今天时间23：59：59
            if (+time > +new Date(+today0 + 24 * 60 * 60 * 1000 - 1)) return true;

            // 禁用小于设置的最早期限的，由于今天会算1天，需要倒推 x-1 天
            if (+time < +today0 - (this.maxDayBeforeNow - 1) * 24 * 60 * 60 * 1000) return true;
            //当最小时间为null的时候则关闭禁用，点清空时会变为0，所以0也要判断
            if (this.minDate2 === null || this.minDate2 === 0) return false;

            // 由于现在选择时间后，默认会调整到当日23:59:59，跨度30天即为31天，需要减掉 1s
            const oneMonth = this.maxTimeRangeLength - 1000;
            // 超出当前选择时间前后31天的不可选择，time为选择框中出现的日期

            return +time > this.minDate2 + oneMonth || +time < this.minDate2 - oneMonth;
        },
        onPick: ({ minDate }) => {
            this.minDate2 = +minDate;
        },
        reset: () => {
            this.minDate2 = null;
        },
    };

    @Watch("compareShow")
    onCompareShowUpdate(state: boolean) {
        if (this.compareShowLocal !== state) {
            this.compareShowLocal = state;
        }

        // 解决重置后 又打开，界面展示了时间数据，但是父组件仍为空数组的问题
        if (state && this.timeRangeLocal1 && this.timeRangeLocal2) {
            this.$emit("select", [this.timeRangeLocal1, this.timeRangeLocal2]);
        }
    }

    // 父组件需要 sync 同步
    @Watch("compareShowLocal")
    async onCompareShowLocalUpdate(state: boolean) {
        this.$emit("update:compareShow", state);
    }

    @Watch("timeRangeLocal1")
    ontimeRangeLocal1Change(val: Date[]) {
        if (val === null) return;

        // 如果 timeRangeLocal1 选择的时间大于当前时间，则调整为当前时间
        const today0 = get0(new Date());
        const todayNow = new Date();
        if (+val[0] > +todayNow) {
            this.timeRangeLocal1?.[0] && (this.timeRangeLocal1[0] = today0);
        }
        if (+val[1] > +todayNow) {
            this.timeRangeLocal1?.[1] && (this.timeRangeLocal1[1] = todayNow);
        }

        // timeRangeLocal2 跟随 timeRangeLocal1 变化，时间变为 timeRangeLocal1 前一天
        this.timeRangeLocal2 = [
            new Date(+val[0] - 24 * 60 * 60 * 1000),
            new Date(+val[1] - 24 * 60 * 60 * 1000),
        ];

        this.$emit("select", this.timeRangeArr);
    }
    @Watch("timeRangeLocal2")
    ontimeRangeLocal2Change(val: Date[]) {
        if (val === null || this.timeRangeLocal1 === null) return;

        // timeRangeLocal2 的跨度保持跟 timeRangeLocal1 一致
        if (+val[1] - +val[0] !== +this.timeRangeLocal1[1] - +this.timeRangeLocal1[0]) {
            this.timeRangeLocal2 = [
                val[0],
                new Date(+this.timeRangeLocal1[1] - +this.timeRangeLocal1[0] + Number(val[0])),
            ];
        }
        // 当天 23:59:59
        const today0 = get0(new Date());
        const nowDate24h = new Date(+today0 + 24 * 60 * 60 * 1000 - 1);
        if (val[1] > nowDate24h) {
            this.timeRangeLocal2 = [
                new Date(+nowDate24h - (+this.timeRangeLocal1[1] - +this.timeRangeLocal1[0])),
                nowDate24h,
            ];
        }

        this.$emit("select", [this.timeRangeLocal1, this.timeRangeLocal2]);
    }

    /**
     * 展示数据对比
     */
    handleDataCompare() {
        this.compareShowLocal = true;
        // 未设置时间时默认设置为昨天
        if (this.timeRangeLocal1 === null) {
            const today0 = get0(new Date());
            this.timeRangeLocal1 = [new Date(+today0 - 24 * 60 * 60 * 1000), new Date(+today0 - 1)];
        }
    }
    /**
     * 覆盖el-date-picker的方法以便实现自定义结束时间
     */
    private handlePickerFocus(instance: any) {
        // 覆盖el-date-picker的方法以便实现自定义结束时间
        this.$nextTick(() => {
            const picker = instance.picker;
            const originalMethod = this.originalPickerChangeMethod = picker.handleRangePick;
            picker.handleRangePick = (...args: any[]) => {
                const maxDate = args[0].maxDate;
                if (+get0(new Date()) === +get0(maxDate)) {
                    picker.defaultTime = [picker.defaultTime[0], dayjs(new Date()).format("HH:mm:ss")];
                } else {
                    picker.defaultTime = ["00:00:00", "23:59:59"];
                }
                originalMethod(...args);
            };
        });
    }

    /**
     * 恢复el-date-picker的原生方法
     */
    private handlePickerBlur(instance: any) {
        // 覆盖el-date-picker的方法以便实现自定义结束时间
        this.$nextTick(() => {
            const picker = instance.picker;
            picker.handleRangePick = this.originalPickerChangeMethod;
        });
    }
}
</script>

<style lang="scss" scoped>
.ct-time-compare {
    display: inline-block;

    .el-button {
        margin-top: 0;
        margin-bottom: 0;
    }
}
</style>
