<template>
    <!-- 共享缓存，使用可插拔配置方式，无功能锁 -->
    <module-wrapper module-name="remote_sync_auth" :addon="isNewEcgw">
        <p v-if="$PoweredByQiankun" id="div_remoteSyncAuth" class="rsa-label">
            {{ $t("domain.remoteAuth.远程同步鉴权") }}
        </p>
        <lock-tip :lock="isLockRemoteSyncAuth">
            <el-form
                ref="remote_sync_auth_form"
                :model="remoteSyncAuthForm"
                :rules="rules"
                label-width="140px"
                class="rsa-container"
                :disabled="disabled"
            >
                <el-form-item :label="$t('domain.remoteAuth.远程同步鉴权')" prop="switch">
                    <el-switch v-if="!isLockRemoteSyncAuth" v-model="remoteSyncAuthForm.switch" @change="onGlobalSwitchChange"></el-switch>
                    <el-switch v-else :value="true"></el-switch>
                </el-form-item>
                <template v-if="remoteSyncAuthForm.switch">
                    <div
                        v-for="(item, idx) in remoteSyncAuthForm.remote_sync_auth"
                        :key="item.id"
                        class="switch-wrapper"
                    >
                        <div class="rsa-header">
                            <h3 class="rsa-subtitle">{{ $t("domain.remoteAuth.基础信息") }}</h3>
                            <el-button
                                class="rsa-delete-btn"
                                type="text"
                                :disabled="remoteSyncAuthForm.remote_sync_auth.length === 1 || disabled"
                                @click="deleteRemoteSyncAuth(idx)"
                            >
                                {{ $t("domain.delete") }}
                            </el-button>
                        </div>
                        <el-form-item
                            :label="$t('domain.detail.label48')"
                            :prop="`remote_sync_auth.${idx}.priority`"
                            :rules="rules.priority"
                        >
                            <el-input v-model.number="item.priority" class="rsa-input" />
                        </el-form-item>
                        <common-condition
                            v-model="remoteSyncAuthForm.remote_sync_auth[idx]"
                            :list="remoteSyncAuthForm.remote_sync_auth"
                            :required="false"
                            :prop-prefix="`remote_sync_auth.${idx}.`"
                            :show-reset-btn="true"
                            :is-edit="isEdit && isService && !isLockRemoteSyncAuth"
                            @clear-validate="() => clearValidate([`remote_sync_auth.${idx}.content`])"
                            class="rsa-condition"
                        />
                        <el-form-item
                            :label="$t('domain.remoteAuth.鉴权源站')"
                            :prop="`remote_sync_auth.${idx}.auth_host`"
                            :rules="rules.auth_host"
                        >
                            <el-input
                                v-model.trim.lazy="item.auth_host"
                                :placeholder="$t('domain.remoteAuth.ip/域名，多个逗号分隔')"
                                class="rsa-input"
                            ></el-input>
                        </el-form-item>
                        <el-form-item
                            :label="$t('domain.remoteAuth.鉴权请求uri')"
                            :prop="`remote_sync_auth.${idx}.subject`"
                            :rules="rules.auth_uri"
                        >
                            <el-input
                                v-model.lazy="item.subject"
                                :placeholder="$t('domain.remoteAuth.格式:/auth 或 $uri')"
                                class="rsa-input"
                            ></el-input>
                        </el-form-item>
                        <el-form-item :label="$t('domain.remoteAuth.请求配置')">
                            <div style="display: flex; gap: 12px; flex-wrap: wrap; width: calc(100% - 120px)">
                                <el-form-item
                                    :label="$t('domain.remoteAuth.请求协议')"
                                    :prop="`remote_sync_auth.${idx}.auth_scheme`"
                                    label-width="70px"
                                    label-position="left"
                                >
                                    <el-select
                                        v-model="item.auth_scheme"
                                        clearable
                                        :placeholder="$t('domain.remoteAuth.默认http')"
                                    >
                                        <el-option value="http"></el-option>
                                        <el-option value="https"></el-option>
                                    </el-select>
                                </el-form-item>
                                <el-form-item
                                    :label="$t('domain.remoteAuth.请求端口')"
                                    :prop="`remote_sync_auth.${idx}.auth_port`"
                                    :rules="rules.auth_port"
                                    label-width="70px"
                                    label-position="left"
                                >
                                    <el-input
                                        v-model.lazy="item.auth_port"
                                        :placeholder="
                                            item.auth_scheme === 'https'
                                                ? $t('domain.remoteAuth.默认443')
                                                : $t('domain.remoteAuth.默认80')
                                        "
                                    ></el-input>
                                </el-form-item>
                                <el-form-item
                                    :label="$t('domain.remoteAuth.请求方法')"
                                    :prop="`remote_sync_auth.${idx}.auth_method`"
                                    label-width="70px"
                                    label-position="left"
                                >
                                    <el-select
                                        v-model.lazy="item.auth_method"
                                        clearable
                                        :placeholder="$t('domain.remoteAuth.默认GET')"
                                    >
                                        <el-option value="GET"></el-option>
                                        <el-option value="POST"></el-option>
                                        <el-option value="HEAD"></el-option>
                                        <el-option value="OPTIONS"></el-option>
                                    </el-select>
                                </el-form-item>
                                <el-form-item
                                    v-if="item.auth_method === 'POST'"
                                    :label="$t('domain.remoteAuth.请求体')"
                                    :prop="`remote_sync_auth.${idx}.auth_body_data`"
                                    label-width="70px"
                                    label-position="left"
                                >
                                    <el-input type="textarea" v-model.lazy="item.auth_body_data" :rows="1">
                                    </el-input>
                                </el-form-item>
                            </div>
                        </el-form-item>
                        <el-divider class="rsa-divider"></el-divider>
                        <div class="rsa-header">
                            <h3 class="rsa-subtitle">{{ $t("domain.remoteAuth.鉴权请求参数") }}</h3>
                        </div>
                        <el-form-item
                            :label="$t('domain.remoteAuth.保留参数设置')"
                            :prop="`remote_sync_auth.${idx}.use_original_request_args`"
                            :rules="rules.use_original_request_args"
                        >
                            <el-radio-group v-model="item.use_original_request_args">
                                <el-radio label="on">{{ $t("domain.remoteAuth.保留所有参数") }}</el-radio>
                                <el-radio label="off">{{ $t("domain.remoteAuth.删除所有参数") }}</el-radio>
                                <el-radio label="custom">{{ $t("domain.remoteAuth.自定义参数") }}</el-radio>
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item
                            :prop="`remote_sync_auth.${idx}.use_main_request_args`"
                            :label="$t('domain.remoteAuth.自定义参数')"
                            v-if="item.use_original_request_args === 'custom'"
                            :rules="rules.atLeastOneArg"
                        >
                            <div class="ct-table-wrapper rsa-input">
                                <el-table :data="item.use_main_request_args" class="origin-table auto-table">
                                    <el-table-column :label="$t('domain.remoteAuth.参数名')" prop="arg_name">
                                        <template #default="{ row, $index }">
                                            <el-form-item
                                                :prop="`remote_sync_auth.${idx}.use_main_request_args.${$index}.arg_name`"
                                                :rules="rules.arg_name_1"
                                                label-width="0"
                                            >
                                                <el-autocomplete
                                                    v-model="row.arg_name"
                                                    :fetch-suggestions="queryArgNameSearch"
                                                    :placeholder="$t('domain.remoteAuth.请选择或输入参数名')"
                                                    class="width-100"
                                                ></el-autocomplete>
                                            </el-form-item>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="arg_value">
                                        <template #header>
                                            {{ $t("domain.remoteAuth.参数值")
                                            }}<el-tooltip
                                                style="margin-left: 4px"
                                                effect="dark"
                                                placement="top"
                                                :content="
                                                    $t(
                                                        'domain.remoteAuth.有值代表添加/修改，值为空代表删除。'
                                                    )
                                                "
                                            >
                                                <i class="el-icon-question" />
                                            </el-tooltip>
                                        </template>
                                        <template #default="{ row, $index }">
                                            <el-form-item
                                                :prop="`remote_sync_auth.${idx}.use_main_request_args.${$index}.arg_value`"
                                                label-width="0"
                                            >
                                                <el-autocomplete
                                                    v-model="row.arg_value"
                                                    :fetch-suggestions="queryArgValueSearch"
                                                    :placeholder="$t('domain.remoteAuth.请选择或输入参数值')"
                                                    class="width-100"
                                                ></el-autocomplete>
                                            </el-form-item>
                                        </template>
                                    </el-table-column>
                                    <el-table-column :label="$t('domain.operate')" width="120">
                                        <template #default="{ row, $index }">
                                            <el-button
                                                type="text"
                                                @click="commonDelete([idx, 'use_main_request_args'], $index)"
                                            >
                                                {{ $t("domain.delete") }}
                                            </el-button>
                                        </template>
                                    </el-table-column>
                                </el-table>
                                <div class="flex-row-style button-box">
                                    <el-button
                                        class="btn"
                                        type="text"
                                        :disabled="disabled"
                                        @click="addMainRequestArg(idx)"
                                    >
                                        + {{ $t("domain.remoteAuth.增加参数") }}
                                    </el-button>
                                </div>
                            </div>
                        </el-form-item>
                        <el-form-item :label="$t('domain.remoteAuth.参数配置')">
                            <div style="display: flex; gap: 12px">
                                <el-form-item
                                    :label="$t('domain.remoteAuth.参数是否区分大小写')"
                                    :prop="`remote_sync_auth.${idx}.auth_args_case_sensitive`"
                                >
                                    <el-select
                                        v-model.lazy="item.auth_args_case_sensitive"
                                        clearable
                                        :placeholder="$t('domain.editPage.placeholder13')"
                                    >
                                        <el-option
                                            value="on"
                                            :label="$t('domain.editPage.label24')"
                                        ></el-option>
                                        <el-option
                                            value="off"
                                            :label="$t('domain.editPage.label25')"
                                        ></el-option>
                                    </el-select>
                                </el-form-item>
                                <el-form-item
                                    :label="$t('domain.remoteAuth.参数是否编码')"
                                    :prop="`remote_sync_auth.${idx}.auth_args_not_encode`"
                                    label-width="90px"
                                >
                                    <el-select
                                        v-model.lazy="item.auth_args_not_encode"
                                        clearable
                                        :placeholder="$t('domain.remoteAuth.默认是')"
                                    >
                                        <el-option
                                            value="on"
                                            :label="$t('domain.editPage.label24')"
                                        ></el-option>
                                        <el-option
                                            value="off"
                                            :label="$t('domain.editPage.label25')"
                                        ></el-option>
                                    </el-select>
                                </el-form-item>
                            </div>
                        </el-form-item>
                        <el-divider class="rsa-divider"></el-divider>
                        <div class="rsa-header">
                            <h3 class="rsa-subtitle">{{ $t("domain.remoteAuth.鉴权请求头") }}</h3>
                        </div>
                        <el-form-item
                            :label="$t('domain.remoteAuth.保留请求头设置')"
                            :prop="`remote_sync_auth.${idx}.use_original_request_headers`"
                            :rules="rules.use_original_request_headers"
                        >
                            <el-radio-group v-model="item.use_original_request_headers">
                                <el-radio label="on">{{ $t("domain.remoteAuth.保留所有请求头") }}</el-radio>
                                <el-radio label="off">{{ $t("domain.remoteAuth.删除所有请求头") }}</el-radio>
                                <el-radio label="custom">{{ $t("domain.remoteAuth.自定义请求头") }}</el-radio>
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item
                            :prop="`remote_sync_auth.${idx}.use_main_request_headers`"
                            :label="$t('domain.remoteAuth.自定义请求头')"
                            v-if="item.use_original_request_headers === 'custom'"
                            :rules="rules.atLeastOneArg"
                        >
                            <div class="ct-table-wrapper rsa-input">
                                <el-table
                                    :data="item.use_main_request_headers"
                                    class="origin-table auto-table"
                                >
                                    <el-table-column
                                        :label="$t('domain.remoteAuth.请求头名称')"
                                        prop="arg_name"
                                    >
                                        <template #default="{ row, $index }">
                                            <el-form-item
                                                :prop="`remote_sync_auth.${idx}.use_main_request_headers.${$index}.arg_name`"
                                                :rules="rules.arg_name_2"
                                                label-width="0"
                                            >
                                                <el-autocomplete
                                                    v-model="row.arg_name"
                                                    :fetch-suggestions="queryRequestArgNameSearch"
                                                    :placeholder="
                                                        $t('domain.remoteAuth.请选择或输入请求头名称')
                                                    "
                                                    class="width-100"
                                                ></el-autocomplete>
                                            </el-form-item>
                                        </template>
                                    </el-table-column>
                                    <el-table-column
                                        :label="$t('domain.remoteAuth.请求头值')"
                                        prop="arg_value"
                                    >
                                        <template #header>
                                            {{ $t("domain.remoteAuth.请求头值")
                                            }}<el-tooltip
                                                style="margin-left: 4px"
                                                effect="dark"
                                                placement="top"
                                                :content="
                                                    $t(
                                                        'domain.remoteAuth.有值代表添加/修改，值为空代表删除。'
                                                    )
                                                "
                                            >
                                                <i class="el-icon-question" />
                                            </el-tooltip>
                                        </template>
                                        <template #default="{ row, $index }">
                                            <el-form-item
                                                :prop="`remote_sync_auth.${idx}.use_main_request_headers.${$index}.arg_value`"
                                                label-width="0"
                                            >
                                                <el-autocomplete
                                                    v-model="row.arg_value"
                                                    :fetch-suggestions="queryRequestArgValueSearch"
                                                    :placeholder="
                                                        $t('domain.remoteAuth.请选择或输入请求头值')
                                                    "
                                                    class="width-100"
                                                    clearable
                                                ></el-autocomplete>
                                            </el-form-item>
                                        </template>
                                    </el-table-column>
                                    <el-table-column :label="$t('domain.operate')" width="120">
                                        <template #default="{ row, $index }">
                                            <el-button
                                                type="text"
                                                @click="
                                                    commonDelete([idx, 'use_main_request_headers'], $index)
                                                "
                                            >
                                                {{ $t("domain.delete") }}
                                            </el-button>
                                        </template>
                                    </el-table-column>
                                </el-table>
                                <div class="flex-row-style button-box">
                                    <el-button
                                        class="btn"
                                        type="text"
                                        :disabled="disabled"
                                        @click="addMainRequestHeader(idx)"
                                    >
                                        + {{ $t("domain.remoteAuth.增加请求头") }}
                                    </el-button>
                                </div>
                            </div>
                        </el-form-item>
                        <el-divider class="rsa-divider"></el-divider>
                        <div class="rsa-header">
                            <h3 class="rsa-subtitle">{{ $t("domain.remoteAuth.基于状态码鉴权") }}</h3>
                        </div>
                        <el-form-item
                            :label="$t('domain.remoteAuth.鉴权状态码类型')"
                            :prop="`remote_sync_auth.${idx}.auth_respond_action`"
                            :rules="rules.auth_respond_action"
                        >
                            <el-radio-group
                                v-model="item.auth_respond_action"
                                @change="val => onAuthRespondActionChange(val, item, idx)"
                            >
                                <el-radio label="allow">{{
                                    $t("domain.remoteAuth.鉴权成功状态码")
                                }}</el-radio>
                                <el-radio label="deny">{{ $t("domain.remoteAuth.鉴权失败状态码") }}</el-radio>
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item
                            :prop="`remote_sync_auth.${idx}.auth_respond_status`"
                            :rules="
                                item.auth_respond_action === 'allow'
                                    ? rules.auth_respond_status
                                    : rules.auth_respond_status[1]
                            "
                        >
                            <span slot="label">
                                {{ $t("domain.remoteAuth.状态码") }}
                                <el-tooltip
                                    :content="
                                        $t(
                                            'domain.remoteAuth.鉴权服务器根据鉴权结果返回给CDN的HTTP状态码。例如：配置鉴权成功状态码：200，则代表鉴权服务器返回200时鉴权通过，其他状态码均不通过；配置鉴权失败状态码：403，则代表鉴权服务器返回403时鉴权不通过，其他状态码均通过。'
                                        )
                                    "
                                    placement="top-start"
                                >
                                    <ct-svg-icon
                                        icon-class="question-circle"
                                        class-name="ct-sort-drag-icon"
                                        class="rsa-icon"
                                    ></ct-svg-icon>
                                </el-tooltip>
                            </span>
                            <el-input
                                v-model.trim.lazy="item.auth_respond_status"
                                style="width: 300px"
                                :placeholder="$t('domain.remoteAuth.多个状态码用逗号分隔')"
                            >
                            </el-input>
                        </el-form-item>
                        <el-divider class="rsa-divider"></el-divider>
                        <div class="rsa-header">
                            <el-form-item style="margin-bottom: 12px">
                                <h3 slot="label" class="rsa-subtitle" style="text-align: left">
                                    {{ $t("domain.remoteAuth.基于响应body鉴权") }}
                                </h3>
                                <el-switch
                                    style="padding-bottom: 14px"
                                    v-model="item.direct_judge_response_body_switch"
                                ></el-switch>
                            </el-form-item>
                        </div>
                        <el-form-item
                            v-if="item.direct_judge_response_body_switch"
                            :prop="`remote_sync_auth.${idx}.direct_judge_response_body`"
                            :rules="rules.direct_judge_response_body"
                        >
                            <span slot="label">
                                {{ $t("domain.remoteAuth.响应body") }}
                                <el-tooltip
                                    :content="
                                        $t(
                                            'domain.remoteAuth.状态码鉴权通过时，如果有开启“基于响应body鉴权”，还会结合响应body的内容最终判断是否鉴权通过。'
                                        )
                                    "
                                    placement="top-start"
                                >
                                    <ct-svg-icon
                                        icon-class="question-circle"
                                        class-name="ct-sort-drag-icon"
                                        class="rsa-icon"
                                    ></ct-svg-icon>
                                </el-tooltip>
                            </span>
                            <el-input
                                v-model.lazy="item.direct_judge_response_body"
                                :placeholder="$t('domain.remoteAuth.输入响应body内容')"
                                style="width: 200px"
                            ></el-input>
                        </el-form-item>
                        <el-divider class="rsa-divider"></el-divider>
                        <div class="rsa-header">
                            <h3 class="rsa-subtitle">{{ $t("domain.remoteAuth.鉴权失败时响应状态码") }}</h3>
                        </div>
                        <el-form-item
                            :label="$t('domain.remoteAuth.响应状态码类型')"
                            :prop="`remote_sync_auth.${idx}.forbidden_code_state`"
                            :rules="rules.forbidden_code_state"
                        >
                            <el-radio-group v-model="item.forbidden_code_state">
                                <el-radio label="regular">{{ $t("domain.remoteAuth.固定状态码") }}</el-radio>
                                <el-radio label="follow">{{ $t("domain.remoteAuth.跟随鉴权源站") }}</el-radio>
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item
                            v-if="item.forbidden_code_state === 'regular'"
                            :label="$t('domain.remoteAuth.固定状态码')"
                            :prop="`remote_sync_auth.${idx}.forbidden_code`"
                            :rules="rules.forbidden_code"
                        >
                            <el-input
                                v-model.number="item.forbidden_code"
                                style="width: 200px"
                                :placeholder="$t('domain.remoteAuth.默认403')"
                            ></el-input>
                        </el-form-item>
                        <el-divider class="rsa-divider"></el-divider>
                        <div class="rsa-header">
                            <h3 class="rsa-subtitle">{{ $t("domain.remoteAuth.鉴权超时设置") }}</h3>
                        </div>
                        <el-form-item
                            :label="$t('domain.remoteAuth.鉴权超时时间')"
                            :prop="`remote_sync_auth.${idx}.auth_timeout`"
                            :rules="rules.auth_timeout"
                        >
                            <el-input
                                v-model.trim.lazy="item.auth_timeout"
                                style="width: 200px"
                                :placeholder="$t('domain.remoteAuth.默认3')"
                            >
                                <span slot="append">{{ $t("domain.remoteAuth.秒") }}</span>
                            </el-input>
                        </el-form-item>
                        <el-form-item
                            :label="$t('domain.remoteAuth.鉴权超时后动作')"
                            :prop="`remote_sync_auth.${idx}.auth_timeout_pass`"
                        >
                            <el-radio-group v-model="item.auth_timeout_pass">
                                <el-radio label="on">{{ $t("domain.remoteAuth.通过") }}</el-radio>
                                <el-radio label="off">{{ $t("domain.remoteAuth.拒绝") }}</el-radio>
                            </el-radio-group>
                        </el-form-item>
                    </div>
                    <div
                        @click="addRemoteSyncAuth"
                        :class="{
                            'rsa-subtitle': true,
                            'rsa-add-item': true,
                            'disabled-add-remote-sync-auth': disabled,
                        }"
                    >
                        <i class="el-icon-plus"></i>
                        {{ $t("domain.remoteAuth.添加鉴权配置") }}
                    </div>
                </template>
            </el-form>
        </lock-tip>
        <el-divider v-if="$PoweredByQiankun"></el-divider>
    </module-wrapper>
</template>

<script>
import componentMixin from "@/views/domainConfig/componentMixin";
import ModuleWrapper from "@/views/domainConfig/basicConfig/components/ModuleWrapper.vue";
import lockTip from "@/views/domainConfig/components/lockTIp.vue";
import { Component, Vue, Prop, Watch } from "vue-property-decorator";
import {
    initRemoteSyncAuthModule,
    getDefaultRemoteSyncAuthItem,
    getRemoteSyncAuthArgs,
    getArgNameList,
    getArgValueList,
    getRequestArgNameList,
    getRequestArgValueList,
} from "./module.register";
import CommonCondition from "@/components/commonCondition/index.vue";
import { get } from "lodash-es";
import ctSvgIcon from "@/components/ctSvgIcon/index.vue";
import { portRegex } from "@/utils/validator.utils";
import { cloneDeep } from "lodash";
import { conditionDestruct } from "@/utils/utils";
import { SecurityAbilityModule } from "@/store/modules/securityAbility";

initRemoteSyncAuthModule();

@Component({
    mixins: [componentMixin],
    name: "RemoteSyncAuthConfig",
    components: {
        ModuleWrapper,
        lockTip,
        CommonCondition,
        ctSvgIcon,
    },
})
export default class RemoteSyncAuth extends Vue {
    @Prop({ type: Boolean, default: false }) isLockRemoteSyncAuth;
    @Prop({ type: Boolean, default: true }) isNewEcgw;

    remoteSyncAuthForm = {
        switch: false,
        remote_sync_auth: [],
    };

    get rules() {
        if (this.isLockRemoteSyncAuth)
            return {
                auth_host: [],
                auth_uri: [],
                auth_port: [],
                use_original_request_args: [],
                use_original_request_headers: [],
                auth_respond_status: [],
                auth_respond_action: [],
                forbidden_code_state: [],
                direct_judge_response_body: [],
                forbidden_code: [],
                auth_timeout: [],
                arg_name_1: [],
                arg_name_2: [],
                atLeastOneArg: [],
                priority: [],
            };

        return {
            auth_host: [
                { required: true, message: this.$t("domain.remoteAuth.请输入鉴权源站") },
                {
                    validator: (rule, value, callback) => {
                        if (!value) {
                            return callback();
                        }
                        const values = value.split(",");
                        if (values.some(v => !v.trim())) {
                            return callback(new Error(this.$t("domain.remoteAuth.格式错误")));
                        }
                        if (`${value}`.includes(" ")) {
                            return callback(new Error(this.$t("domain.remoteAuth.不能包含空字符串")));
                        }
                        callback();
                    },
                    trigger: "blur",
                },
            ],
            auth_uri: [{ required: true, message: this.$t("domain.remoteAuth.请输入鉴权请求uri") }],
            auth_port: [{ pattern: portRegex, message: this.$t("domain.detail.tip93") }],
            use_original_request_args: [
                { required: true, message: this.$t("domain.remoteAuth.请选择保留参数设置") },
            ],
            use_original_request_headers: [
                { required: true, message: this.$t("domain.remoteAuth.请选择保留请求头设置") },
            ],
            auth_respond_status: [
                { required: true, message: this.$t("domain.detail.placeholder8") },
                {
                    pattern: /^(\d{3},)*\d{3}$/,
                    message: this.$t("domain.remoteAuth.请输入正确的状态码"),
                },
            ],
            auth_respond_action: [
                { required: true, message: this.$t("domain.remoteAuth.请选择鉴权状态码类型") },
            ],
            forbidden_code_state: [
                { required: true, message: this.$t("domain.remoteAuth.请选择响应状态码类型") },
            ],
            direct_judge_response_body: [
                { required: true, message: this.$t("domain.remoteAuth.请输入响应body内容") },
            ],
            forbidden_code: [
                { pattern: /^\d{3}$/, message: this.$t("domain.remoteAuth.请输入正确的状态码") },
            ],
            auth_timeout: [
                {
                    validator: (rule, value, callback) => {
                        if (!value && `${value}` !== "0") {
                            callback();
                            return;
                        }
                        // Handle leading zeros
                        if (/^0+\d/.test(value)) {
                            callback(new Error(this.$t("domain.remoteAuth.请输入正确的超时时间")));
                            return;
                        }
                        const num = Number(value);
                        if (isNaN(num)) {
                            callback(new Error(this.$t("domain.remoteAuth.请输入正确的超时时间")));
                            return;
                        }
                        if (num < 0 || num > 3600) {
                            callback(
                                new Error(this.$t("domain.remoteAuth.鉴权超时时间取值范围为：0 到 3600"))
                            );
                            return;
                        }
                        if (!/^\d+(\.\d{1,3})?$/.test(value)) {
                            callback(new Error(this.$t("domain.remoteAuth.小数位数最大3位")));
                            return;
                        }
                        callback();
                    },
                },
            ],
            arg_name_1: [
                {
                    required: true,
                    message: this.$t("domain.remoteAuth.请选择或输入参数名"),
                    trigger: "change",
                },
            ],
            arg_name_2: [
                {
                    required: true,
                    message: this.$t("domain.remoteAuth.请选择或输入请求头名称"),
                    trigger: "change",
                },
            ],
            atLeastOneArg: [
                {
                    required: true,
                    type: "array",
                    min: 1,
                    message: this.$t("domain.detail.placeholder78"),
                    trigger: "change",
                },
            ],
            priority: [
                {
                    required: true,
                    validator: (rule, value, callback) => {
                        if (this.isLockRemoteSyncAuth) callback();
                        if (value === "" || value === null || value === undefined)
                            callback(this.$t("domain.detail.placeholder22"));
                        const num = /^(-[0-9])?[0-9]*$/;
                        if (!num.test(value)) {
                            return callback(new Error(this.$t("domain.detail.placeholder75")));
                        }
                        if (value > 100) callback(this.$t("domain.detail.placeholder76"));
                        if (value < 1) callback(this.$t("domain.detail.placeholder77"));
                        else callback();
                    },
                    trigger: ["blur", "change"],
                },
            ],
        };
    }

    get disabled() {
        return !this.isEdit || !this.isService || this.isLockRemoteSyncAuth;
    }

    init(thisData) {
        let remote_sync_auth = (
            Array.isArray(thisData.remote_sync_auth) ? cloneDeep(thisData.remote_sync_auth) : []
        ).map(item => {
            item.direct_judge_response_body_switch = !!item.direct_judge_response_body;

            item.auth_host = Array.isArray(item.auth_host) ? item.auth_host.join(",") : "";

            if (
                item.use_original_request_args === "off" &&
                item.use_main_request_args_type === "inherit" &&
                item.use_main_request_args?.length
            ) {
                item.use_original_request_args = "custom";
            }

            if (
                item.use_original_request_headers === "off" &&
                item.use_main_request_headers_type === "inherit" &&
                item.use_main_request_headers?.length
            ) {
                item.use_original_request_headers = "custom";
            }

            item.auth_respond_status = Array.isArray(item.auth_respond_status)
                ? item.auth_respond_status.join(",")
                : "";

            return item;
        });

        remote_sync_auth = conditionDestruct(
            thisData.remote_sync_auth_condition || {},
            remote_sync_auth || []
        );

        this.remoteSyncAuthForm.remote_sync_auth = remote_sync_auth.map(itm =>
            Object.assign(getDefaultRemoteSyncAuthItem(), itm)
        );
        this.remoteSyncAuthForm.switch = remote_sync_auth?.length > 0;

        return {
            remote_sync_auth: this.remoteSyncAuthForm.remote_sync_auth,
        };
    }

    @Watch("remoteSyncAuthForm", { deep: true, immediate: false })
    remoteSyncAuthFormHandler() {
        this.$emit("onModuleChange", {
            remote_sync_auth: this.remoteSyncAuthForm.switch ? this.remoteSyncAuthForm.remote_sync_auth : [],
        });
    }

    onGlobalSwitchChange(val) {
        const originalConf = SecurityAbilityModule.securityBasicConfigOriginForm;
        if (val && originalConf?.remote_sync_auth?.length) {
            this.remoteSyncAuthForm.remote_sync_auth.push(...cloneDeep(originalConf.remote_sync_auth));
        }

        if (val && this.remoteSyncAuthForm.remote_sync_auth.length === 0) {
            this.remoteSyncAuthForm.remote_sync_auth.push(getDefaultRemoteSyncAuthItem());
        }

        if (!val) {
            this.remoteSyncAuthForm.remote_sync_auth.splice(
                0,
                this.remoteSyncAuthForm.remote_sync_auth.length
            );
        }
    }

    onAuthRespondActionChange(val, item, idx) {
        if (val === "allow") {
            item.auth_respond_status = "200";
        } else {
            item.auth_respond_status = "";
            this.$nextTick(() =>
                this.$refs.remote_sync_auth_form.clearValidate(`remote_sync_auth.${idx}.auth_respond_status`)
            );
        }
    }

    addRemoteSyncAuth() {
        if (this.disabled) {
            return;
        }

        this.remoteSyncAuthForm.remote_sync_auth.push(getDefaultRemoteSyncAuthItem());
    }

    deleteRemoteSyncAuth(idx) {
        this.remoteSyncAuthForm.remote_sync_auth.splice(idx, 1);
    }

    addMainRequestArg(idx) {
        this.remoteSyncAuthForm.remote_sync_auth[idx].use_main_request_args.push({
            ...getRemoteSyncAuthArgs(),
        });

        this.$nextTick(() => {
            this.$refs.remote_sync_auth_form.validateField(`remote_sync_auth.${idx}.use_main_request_args`);
        });
    }

    clearValidate(props) {
        this.$refs.remote_sync_auth_form.clearValidate(props);
    }

    addMainRequestHeader(idx) {
        this.remoteSyncAuthForm.remote_sync_auth[idx].use_main_request_headers.push({
            ...getRemoteSyncAuthArgs(),
        });

        this.$nextTick(() => {
            this.$refs.remote_sync_auth_form.validateField(
                `remote_sync_auth.${idx}.use_main_request_headers`
            );
        });
    }

    commonDelete(propPath, idx) {
        const itm = get(this.remoteSyncAuthForm.remote_sync_auth, propPath);
        itm.splice(idx, 1);

        this.$nextTick(() => {
            this.$refs.remote_sync_auth_form.validateField(`remote_sync_auth.${propPath.join(".")}`);
        });
    }

    // 自动完成搜索方法
    queryArgNameSearch(queryString, cb) {
        const argNameList = getArgNameList();
        const results = queryString
            ? argNameList.filter(item => item.toLowerCase().includes(queryString.toLowerCase()))
            : argNameList;
        cb(results.map(item => ({ value: item })));
    }

    queryArgValueSearch(queryString, cb) {
        const argValueList = getArgValueList();
        const results = queryString
            ? argValueList.filter(item => item.toLowerCase().includes(queryString.toLowerCase()))
            : argValueList;
        cb(results.map(item => ({ value: item })));
    }

    queryRequestArgNameSearch(queryString, cb) {
        const requestArgNameList = getRequestArgNameList();
        const results = queryString
            ? requestArgNameList.filter(item => item.toLowerCase().includes(queryString.toLowerCase()))
            : requestArgNameList;
        cb(results.map(item => ({ value: item })));
    }

    queryRequestArgValueSearch(queryString, cb) {
        const requestArgValueList = getRequestArgValueList();
        const results = queryString
            ? requestArgValueList.filter(item => item.toLowerCase().includes(queryString.toLowerCase()))
            : requestArgValueList;
        cb(results.map(item => ({ value: item })));
    }
}
</script>

<style lang="scss" scoped>
.rsa-label {
    // font-weight: bold;
    width: 600px;
    overflow: hidden;
    border-left: 4px solid $color-master;
    line-height: 1;
    padding-left: 12px;
    margin: 16px 0;
}
.rsa-container {
    .rsa-input {
        width: calc(100% - 120px);
    }

    .rsa-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 4px;

        .rsa-subtitle {
            font-size: 12px;
            color: #333333;
            letter-spacing: 0;
            line-height: 18px;
            font-weight: 600;
        }
    }

    .rsa-grid {
        display: grid;
        grid-template-columns: 140px calc(100% - 140px - 120px);
        line-height: 32px;

        .rsa-grid-label {
            line-height: 32px;
        }

        .rsa-grid-content {
            display: flex;
            align-items: center;
            gap: 12px;
            flex-wrap: wrap;
        }
    }

    .rsa-condition {
        ::v-deep {
            .el-input {
                width: calc(100% - 120px);
            }
        }
    }

    ::v-deep {
        .el-table {
            margin: 0;
        }
        .el-table th.el-table__cell {
            background-color: #eff1f4;
        }
    }

    .rsa-divider {
        margin: 0 4px 12px 4px;
        width: calc(100% - 120px);
    }
}

.flex-row-style {
    display: flex;
    flex-direction: row;
    align-items: center;
    background-color: #fff;
    height: 40px;
    justify-content: center;

    &.space-between {
        justify-content: space-between;
    }
}

.rsa-tip {
    font-size: 12px;
    color: #7c818c;
    line-height: 18px;
    font-weight: 400;
    margin-top: 8px;
    width: calc(100% - 120px);

    .rsa-icon {
        width: 14px;
        height: 14px;
    }
}

.width-100 {
    width: 100%;

    ::v-deep .el-input__inner {
        width: 100%;
    }
}
.rsa-add-item {
    margin: 16px 0;
    text-align: center;
    cursor: pointer;
    border: 1px dashed rgba(226, 229, 237, 1);
    padding: 16px;
    border-radius: 3px;
    width: calc(100% - 140px);
    margin-left: 140px;
    user-select: none;
}
.disabled-add-remote-sync-auth {
    cursor: not-allowed;
    color: #999;
}
</style>
