.form-wrapper {
    width: 100%;
    display: flex;
    flex-direction: column!important;
    align-items: flex-start!important;

    .wrapper-form-block {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: space-between;
        width: 100%;

        ::v-deep .el-form-item {
            display: flex;
            flex-direction: row;
            align-items: flex-start;
            //margin-right: 10px;

            .el-form-item__label {
                line-height: 32px;
            }

            .el-form-item__content {
                display: flex;
                flex-direction: column;
                margin-left: unset !important;
                //flex: 1;
                position: relative;

                .tip-div {
                    position: absolute;
                    bottom: -20px;
                    font-size: 12px;
                    font-family: Microsoft YaHei;
                    font-weight: 400;
                    color: #788296;
                    line-height: 20px;
                }

                .el-select {
                    flex: 1;
                    width: 210px;
                }
            }
        }
        ::v-deep &>.el-form-item:last-child{
            margin-right: unset;
        }
    }

    & > .wrapper-form-margin:last-child {
        ::v-deep.el-form-item {
            margin-bottom: unset;
        }
    }
}

.btn-div {
    justify-content: flex-end;
}
