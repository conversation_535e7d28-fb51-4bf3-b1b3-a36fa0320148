<template>
    <el-dialog :title="$t('domain.batch.tip2')" :visible="visible" width="800px" :show-close="false">
        <el-table :data="parsedMsgList">
            <el-table-column :label="$t('domain.list.tableLabel2')" prop="domain" />
            <el-table-column :label="$t('certificate.bindDomain.failedReason')" prop="reason" />
        </el-table>

        <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="handleClose">{{ $t("common.dialog.submit") }}</el-button>
        </div>
    </el-dialog>
</template>

<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";

@Component({
    components: {},
})
export default class FailedDomainDialog extends Vue {
    @Prop({ type: Boolean, default: false }) visible!: boolean;
    @Prop({ type: Array, default: () => [] }) msgList!: string[];

    /**
     * 解析后的失败域名列表
     */
    get parsedMsgList() {
        return this.msgList.map(itm => {
            const [domain, ...reason] = itm.split(":");
            return { domain, reason: reason.join(":")?.trim() };
        });
    }

    /**
     * 跳转域名列表
     */
    handleClose() {
        this.$router.push({
            name: "ndomain.list",
        });
    }
}
</script>
