<template>
    <div class="ct-edit-wrapper">
        <el-form
            label-width="140px"
            label-position="left"
            :model="uriDenyForm"
            :rules="rules"
            ref="uriDenyForm"
            :disabled="!isEdit || !isService"
        >
            <div v-if="!isPoweredByQiankun">
                <!-- URL黑白名单 开关 -->
                <el-form-item :label="$t('domain.create.uri')" prop="uri_switch">
                    <el-switch
                        v-model="uriDenyForm.uri_switch"
                        active-value="on"
                        inactive-value="off"
                        @change="uri_switch_change"
                    ></el-switch>
                </el-form-item>

                <!-- 类型 -->
                <div v-if="uriDenyForm.uri_switch === 'on'" class="switch-wrapper">
                    <el-form-item :label="$t('domain.type')" prop="uri_deny.uri" :rules="rules.uri">
                        <div>
                            <div class="radio-row">
                                <el-radio-group v-model="uriDenyForm.uri_deny.type" @change="uriTypeChange">
                                    <el-radio :label="1">{{ $t("domain.detail.trustlist") }}</el-radio>
                                    <el-radio :label="0">{{ $t("domain.detail.blocklist") }}</el-radio>
                                </el-radio-group>
                            </div>
                            <div>
                                <el-input
                                    class="textarea-wrapper"
                                    v-model="uriDenyForm.uri_deny.uri"
                                    type="textarea"
                                    :rows="5"
                                    :placeholder="uriPlaceholder"
                                    @change="onUriChange"
                                />
                            </div>
                        </div>
                    </el-form-item>
                </div>
            </div>
        </el-form>
    </div>
</template>

<script>
import componentMixin from "@/views/domainConfig/componentMixin";
import { SecurityAbilityModule } from "@/store/modules/securityAbility";
import { cloneDeep } from "lodash-es";

export default {
    name: "uriDeny",
    components: {},
    mixins: [componentMixin],
    props: {
        datas: Object,
    },
    data() {
        return {
            uriDenyForm: {
                uri_switch: "off",
                uri_deny: {
                    type: 1,
                    uri: "",
                },
            },
            maxNum: 400,

            rules: {
                uri: [{ required: false, validator: this.valid_uri, trigger: "blur" }],
            },
        };
    },
    computed: {
        uriPlaceholder() {
            return this.$t("domain.detail.tip41-1");
        },
        uriList() {
            return this.uriDenyForm.uri_deny?.uri
                ?.split("\n")
                .map(i => i?.trim())
                .filter(i => i);
        },
    },
    watch: {
        datas: {
            deep: true,
            handler(val) {
                this.init(val);
            },
            immediate: true,
        },
    },
    methods: {
        init(v) {
            if (!v) return;
            this.uriDenyForm.uri_switch = v?.uri_switch;
            this.uriDenyForm.uri_deny.type = v?.uri_deny?.type;
            this.uriDenyForm.uri_deny.uri = v?.uri_deny?.uri;
        },
        // uri黑白名单开关关闭重新打开，需要将类型设置为白名单
        uri_switch_change(val) {
            const originalConf = SecurityAbilityModule.securityBasicConfigOriginForm;
            this.uriDenyForm = {
                uri_switch: val,
                uri_deny: cloneDeep(originalConf.uri_deny),
            }

            this.$emit("onChange", this.uriDenyForm, this.uriList);
        },
        // 类型 事件
        uriTypeChange() {
            this.$emit("onChange", this.uriDenyForm, this.uriList);
        },
        // 输入框触发事件
        onUriChange(val) {
            this.$emit("onChange", this.uriDenyForm, this.uriList);
        },
        valid_uri(rule, value, callback) {
            const { uriList, maxNum, uriDenyForm } = this;

            if (uriDenyForm.uri_deny?.type === null && uriDenyForm.uri_deny?.uri) {
                return callback(new Error(this.$t("domain.detail.tip42")));
            } else if (!uriDenyForm.uri_deny?.uri?.trim()) {
                return callback(new Error(this.$t("domain.detail.tip49-1")));
            } else if (uriList && uriList.length > maxNum) {
                return callback(new Error(this.$t("simpleForm.UriBlock.maxNum", { maxNum: maxNum })));
            } else {
                return callback();
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.ct-edit-wrapper {
    ::v-deep {
        .el-form--label-left .el-form-item__label {
            text-align: right;
        }
    }
}
.textarea-wrapper {
    width: calc(100% - 120px);
}
</style>
