<template>
    <el-dialog
        :title="$t('refresh.create.dialogTitle')"
        :visible.sync="dialogVisible"
        :modal-append-to-body="false"
        :close-on-click-modal="false"
        :before-close="cancel"
        :append-to-body="true"
        width="600px"
        class="feadback-dialog"
    >
        <span class="form-info">
            <i class="el-alert__icon cute-icon-info-circle-fill"></i>
            {{ $t('refresh.create.msg8') }}
        </span>
        <el-form :label-width="isEn ? '110px' : '80px'" label-position="left">
            <el-form-item :label="$t('refresh.create.dialogLabel')"  prop="name">
                <el-input type="textarea" :value="domainString" rows="5" disabled />
            </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
            <slot name="footer">
                <el-button @click="cancel">
                    {{ $t("refresh.create.cancel") }}
                </el-button>
                <el-button v-if="hasContinue" type="primary" @click="operateContinue">
                    {{ $t("refresh.create.continue") }}
                </el-button>
            </slot>
        </div>
    </el-dialog>
</template>

<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";
import { getLang } from "@/utils";

@Component
export default class OperationFeedbackDialog extends Vue {
    @Prop({ default: true, type: Boolean }) private dialogVisible!: boolean;
    @Prop({ default: () => [] }) private domainList!: Array<string>;
    @Prop({ default: false, type: Boolean }) private hasContinue!: boolean;

    get isEn() {
        return getLang() === "en";
    }

    private get domainString() {
        return this.domainList.map((item: any) => item.domain).join("\n");
    }
    /**
     * 复制ip
     */
    private operateContinue() {
        this.$emit("continue");
        this.$emit("cancel");
    }

    /**
     * 关闭当前弹窗
     */
    private cancel() {
        this.$emit("cancel");
    }
}
</script>
<style lang="scss" scoped>
.feadback-dialog {
    .el-form {
        ::v-deep .el-form-item__label {
            word-break: normal;
        }
    }
    .form-info {
        display: flex;
        margin-bottom: 20px;
        .el-alert__icon {
            color: #ff842e;
            margin-right: 8px;
        }
        font-size: 12px;
        color: #333333;
        line-height: 18px;
    }
}
</style>
