import { ElDatePicker } from "element-ui/types/date-picker";
import i18n from "@/i18n";

export const shortcuts_obj = {
  recent_15_minutes: {
    text: i18n.t("common.pickerOptions.option1"),
    onClick(picker: ElDatePicker) {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 15 * 60 * 1000);
      picker.$emit('pick', [start, end]);
    }
  },
  recent_1_hour: {
    text: i18n.t("common.pickerOptions.option2"),
    onClick(picker: ElDatePicker) {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000);
      picker.$emit('pick', [start, end]);
    }
  },
  recent_6_hours: {
    text: i18n.t("common.pickerOptions.option3"),
    onClick(picker: ElDatePicker) {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 6);
      picker.$emit('pick', [start, end]);
    }
  },
  recent_24_hours: {
    text: i18n.t("common.pickerOptions.option4"),
    onClick(picker: ElDatePicker) {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24);
      picker.$emit('pick', [start, end]);
    }
  },
  recent_1_week: {
    text: i18n.t("common.pickerOptions.option5"),
    onClick(picker: ElDatePicker) {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      picker.$emit('pick', [start, end]);
    }
  },
  recent_1_month: {
    text: i18n.t("common.pickerOptions.option6"),
    onClick(picker: ElDatePicker) {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      picker.$emit('pick', [start, end]);
    }
  },
  recent_3_months: {
    text: i18n.t("common.pickerOptions.option7"),
    onClick(picker: ElDatePicker) {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
      picker.$emit('pick', [start, end]);
    }
  }
}

export const shortcuts_arr = [
  ...Object.values(shortcuts_obj)
]
