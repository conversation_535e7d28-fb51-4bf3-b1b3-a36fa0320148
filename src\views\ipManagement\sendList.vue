<template>
    <div>
        <div class="search-bar">
            <div class="search-bar-item">
                <label>{{ $t("ipManagement.sendList.label2") }}</label>
                <el-date-picker
                    size="medium"
                    v-model="searchDate"
                    type="datetimerange"
                    range-separator="-"
                    :start-placeholder="$t('common.datePicker.start')"
                    :end-placeholder="$t('common.datePicker.end')"
                    value-format="timestamp"
                    :picker-options="pickerOptions"
                />
            </div>
            <div class="search-bar-item">
                <label>{{ $t("ipManagement.planList.formTitle[0]") }}</label>
                <el-select v-model="product">
                    <el-option
                        v-for="item in productOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    />
                </el-select>
            </div>
            <div class="search-bar-item">
                <el-button type="primary" @click="onSearch">{{ $t("common.search.start") }}</el-button>
                <el-button @click="onDiff" :disabled="tableSelected.length != 2">{{
                    $t("ipManagement.sendList.label3")
                }}</el-button>
            </div>
        </div>
        <el-table
            class="send-list"
            :data="dataList"
            v-loading="loading"
            @selection-change="handleSelectionChange"
        >
            <el-table-column type="selection" :selectable="selectable"></el-table-column>
            <el-table-column :label="$t('ipManagement.sendList.label2')" width="180">
                <template slot-scope="{ row }">
                    {{ (row.create_time * 1000) | timeFormat }}
                </template>
            </el-table-column>
            <el-table-column :label="$t('ipManagement.planList.tableTitle[0]')" prop="config_name">
                <template slot-scope="{ row }">
                    {{ configNameOptions[row.product] }}
                </template>
            </el-table-column>
            <el-table-column
                :label="$t('ipManagement.planList.tableTitle[1]')"
                prop="domain"
                show-overflow-tooltip
            >
                <template slot-scope="{ row }">
                    {{ row.domain.join(",") }}
                </template>
            </el-table-column>
            <el-table-column :label="$t('common.table.operation')">
                <template slot-scope="{ row }">
                    <el-button type="text" @click="viewIpDetails(row)">
                        {{ $t("ipManagement.sendList.btn2") }}
                    </el-button>
                    <el-button type="text" @click="decryptionKey(row)">
                        {{ $t("ipManagement.sendList.btn3") }}
                    </el-button>
                </template>
            </el-table-column>
        </el-table>

        <div class="pager">
            <el-pagination
                :small="isXs"
                :layout="isXs ? 'prev, pager, next' : 'total, sizes, prev, pager, next,jumper'"
                :total="totalRecord"
                :current-page.sync="page"
                :page-size="pageSize"
                :page-sizes="[10, 30, 50, 100]"
                :hide-on-single-page="false"
                @size-change="sizeChange"
            ></el-pagination>
        </div>

        <ip-diff-dialog v-if="diffVisible" :tableSelected="tableSelected" @cancel="diffVisible = false" />
        <secret-dialog v-if="keyVisible" :secretKey="secretKey" @cancel="keyVisible = false" />
        <back-origin-ip-dialog v-if="ipVisible" :ip-info="ipInfo" @cancel="ipVisible = false" />
    </div>
</template>

<script lang="ts">
import { Component, Vue, Watch } from "vue-property-decorator";
import { ScreenModule } from "@/store/modules/screen";
import { timeFormat } from "@/filters/index";
import { shortcuts_arr } from "@/utils/pickerOption";
import BackOriginIpDialog from "./components/BackOriginIpDialog.vue";
import IpDiffDialog from "./components/IpDiffDialog.vue";
import SecretDialog from "./components/SecretDialog.vue";
import { SendData } from "@/types/ipManagement";
import { ipManagementUrl } from "@/config/url/ipManagement";

interface LogData {
    id: string;
}

@Component({
    filters: {
        timeFormat(data: string): string {
            return timeFormat(data) || data;
        },
    },
    components: { IpDiffDialog, BackOriginIpDialog, SecretDialog },
})
export default class SendList extends Vue {
    private pickerOptions = {
        shortcuts: shortcuts_arr,
    };
    get productOptions() {
        return [{ label: this.$t("ipManagement.planList.productOption[0]"), value: "020" }];
    }
    configNameOptions = {
        "020": this.$t("ipManagement.sendList.configNameOption1"),
    };
    product = "020";
    searchDate = [];
    page = 1;
    pageSize = 10;
    loading = false;
    totalRecord = 0;
    dataList: SendData[] = [];
    diffVisible = false;
    ipVisible = false;
    keyVisible = false;
    ipInfo = "";
    secretKey = "";
    private tableSelected: LogData[] = [];

    get screenWidth() {
        return ScreenModule.width;
    }
    get isXs() {
        return this.screenWidth < 600;
    }
    get startTime() {
        if (!this.searchDate) return "";
        return this.searchDate[0] ? Math.floor(this.searchDate[0] / 1000) : "";
    }
    get endTime() {
        if (!this.searchDate) return "";
        return this.searchDate[1] ? Math.floor(this.searchDate[1] / 1000) : "";
    }
    private handleSelectionChange(val: []) {
        this.tableSelected = val;
    }

    private selectable(row: LogData) {
        const selected = this.tableSelected.some(item => item.id === row.id);
        return this.tableSelected.length < 2 || selected;
    }
    @Watch("page")
    onPageChange() {
        this.fetchList();
    }

    mounted() {
        this.fetchList();
    }
    async fetchList({
        start_time = this.startTime,
        end_time = this.endTime,
        page = this.page,
        page_size = this.pageSize,
    } = {}) {
        const params = {
            start_time,
            end_time,
            page,
            page_size,
            product: this.product,
        };
        this.loading = true;
        const rst = await this.$ctFetch<{ data: []; paging: { total_record: number } }>(
            ipManagementUrl.sendLog,
            {
                encodeParams: true,
                data: params,
            }
        );
        this.dataList = rst.data;
        this.totalRecord = rst.paging.total_record;
    }
    onSearch() {
        this.fetchList();
    }
    /**
     * 查看回源IP
     * @param row 待查看方案信息
     */
    private viewIpDetails(row: SendData) {
        this.ipInfo = row.ip_infos;
        this.ipVisible = true;
    }
    decryptionKey(row: SendData) {
        this.secretKey = row.secret_key;
        this.keyVisible = true;
    }
    onDiff() {
        this.diffVisible = true;
    }
    sizeChange(val: number) {
        // 恢复到第一页
        this.page = 1;
        this.pageSize = val;
        this.fetchList();
    }
}
</script>

<style lang="scss" scoped>
/* 隐藏表头的全选按钮 */
.send-list {
    ::v-deep {
        .el-table__header .el-checkbox {
            display: none;
        }
    }
}
.search-bar {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
    flex-shrink: 0;
    &-item {
        display: flex;
        gap: 12px;
        align-items: center;
        label {
            flex-shrink: 0;
            font-size: 12px;
        }
    }
    .custom-input {
        ::v-deep {
            .el-input__inner {
                padding-right: 12px;
            }
        }
    }
}
.pager {
    margin-top: 8px;
    ::v-deep.el-pagination {
        text-align: right !important;
    }
}
</style>
