<template>
    <ct-section-wrap :headerText="headerText" :headerTip="headerTip" class="statistics-entry-wrapper">
        <div class="statistics-entry">
            <el-card>
                <nested-menu
                    v-model="currentMenu"
                    :menuList="getMenuList"
                    :searchbarLoading="searchBarLoading"
                >
                    <transition name="search-bar-fade" mode="out-in">
                        <component
                            class="statistics-search-bar"
                            :is="getSearchBar"
                            @search="getData"
                            @download="download"
                            ref="search"
                            :paramFlag="getSearchParamsFlag"
                            @initialized="onSearchBarInitialized"
                            :periodOptions="getPeriodOptions"
                            :childPanelMounted="childPanelMounted"
                            :currentMenu="currentMenu"
                        />
                    </transition>
                </nested-menu>
            </el-card>
            <chart-wrapper :useWrapper="useWrapper">
                <transition name="component-fade" mode="out-in">
                    <component
                        :is="getChart"
                        ref="childPane"
                        class="child-pane"
                        @childPanelMounted="onChildPanelMounted"
                        :currentMenu="currentMenu"
                    />
                </transition>
            </chart-wrapper>
        </div>
    </ct-section-wrap>
</template>

<script lang="tsx">
import { Component } from "vue-property-decorator";
import EntryMixin from "./entryMixin";
import type { MenuListItem } from "./components/statistics.d";
import { cloneDeep } from "lodash-es";
import ChartWrapper from "./components/chartWrapper.vue";

@Component({
    components: { ChartWrapper },
})
export default class StatisticsEntry extends EntryMixin {
    get getMenuList() {
        let defaultMenuList: MenuListItem[] = cloneDeep(this.defaultMenuList);

        defaultMenuList[0].children = (defaultMenuList[0].children || []).map(itm => {
            if (itm.prop === "Request")
                return {
                    ...itm,
                    prop: "RequestWhole",
                };

            return itm;
        });

        defaultMenuList = this.filterAuthTab(this.getAuthTabs, defaultMenuList);

        this.setCanAuthorizedTab(defaultMenuList);

        return defaultMenuList;
    }

    get getSearchParamsFlag() {
        const paramsMap: Record<string, string> = {
            BandwidthFlow:
                "product, label, multipleDomain, isp, area, timePicker, timeCompare, protocol, ipProtocol,abroad, timeGranularity",
            Miss: "product, label, multipleDomain, area, timePicker,abroad, timeGranularity",
            RequestWhole:
                "product, label, multipleDomain, isp, area, timePicker, ipProtocol,abroad, timeGranularity",
            Hit: "product, label, multipleDomain, timePicker, protocol, isp, area, ipProtocol,abroad, timeGranularity",
            StatusCode:
                "product, label, multipleDomain, isp, area, timePicker, protocol, ipProtocol,abroad, timeGranularity",
            BackToOriginStatusCode:
                "product, label, multipleDomain, isp, area, timePicker, protocol, ipProtocol,abroad, timeGranularity",
            PvUv: "product, label, multipleDomain, area, timePicker,abroad",
            Provider: "product, label, multipleDomain, isp, area, timePicker, download,abroad, ipProtocol",
            DownloadSpeed:
                "product, label, multipleDomain, isp, area, timePicker, timeCompare, abroad, timeGranularity, isOnlyShow5Min",
            PrivateNetworkAccelerator:
                "product, label, multipleDomain, timePicker, timeCompare, timeGranularity, isOnlyShow5Min, timeRangeHalfYear",
            HotUrl: "product, label, multipleDomain, code, sort, timePicker, download, hourly",
            HotUrlMiss: "product, label, multipleDomain, code, sort, timePicker, download, hourly",
            HotReferer: "product, label, multipleDomain, sort, timePicker, download, hourly",
            DomainRank: "product, label, multipleDomain, sort, timePicker, download",
            TopIp: "product, label, multipleDomain, area, code, sort, timePicker, download, hourly",
        };

        return paramsMap[this.getSubMenu] || "";
    }
}
</script>

<style scoped lang="scss">
@import "./index.scss";
</style>
