{"acceArea": {"acceTipsCtclouds": "加速区域选择“中国内地”或“全球”时：<br />1、如您的账号还未完成实名认证，请先通过指引文档：<a href=\"{ctcloudLink}\" target=\"_blank\">实名认证</a>，完成实名认证。完成账号实名认证后，请重新登录账号后再试。<br />2、如您的加速域名尚未完成中国大陆的ICP备案，请先完成在中国大陆的ICP备案。", "acceTipsNonCtclouds": "加速区域包含中国内地时，加速域名请先完成在中国大陆的ICP备案，同时完成公安网备案。", "isCtcCloudTip": {"tip1": "当前仅CDN加速支持开通全球加速服务，请提交", "tip2": "加速类型为CDN加速，并确保已开通", "tip3": "CDN加速-全球(不含中国内地)", "tip4": "服务。"}, "isCtcCloudTipOld": {"tip1": "当前仅CDN加速支持开通全球加速服务，确保已开通", "tip2": "CDN加速-全球（不含中国内地）", "tip3": "服务，并提交工单将加速类型变更为CDN加速后即可变更加速范围。"}, "billingNote": "不同加速区域对应不同资费标准。", "productTip": {"008": {"tip1": "您当前尚未开通CDN加速境外服务，请先前往开通{0}服务。", "tip2": "CDN加速-全球（不含中国内地）", "tip3": "服务。", "tip4": "您当前未开通CDN加速境外服务，如有需，可通过【变更加速区域】开通CDN加速-全球（不含中国内地）。{1}"}, "006": {"tip1": "全站加速支持全球范围加速，先前往开通{2}服务。", "tip2": "全站加速-全球（不含中国内地）", "tip3": "服务。", "tip4": "您当前未开通全站加速境外服务，如有需，可通过【变更加速区域】开通全站加速-全球（不含中国内地）{3}。"}, "004": {"tip1": "当前仅CDN加速支持开通全球加速服务，请选择CDN加速类型，并确保已开通{4}服务。", "tip2": "CDN加速-全球（不含中国内地）", "tip3": "服务。"}, "014": {"tip1": "当前仅CDN加速支持开通全球加速服务，请选择CDN加速类型，并确保已开通{5}服务。", "tip2": "CDN加速-全球（不含中国内地）", "tip3": "服务。"}, "common": {"tip1": "变更链接", "tip2": "如需支持全球加速服务，请先联系客户经理，通过【变更】开通对应产品的全球（不含中国内地）加速区域。"}}, "confirmChangeAccelerationType": "确认要将加速类型变更为CDN加速？变更加速类型可能会影响计费，请谨慎操作。"}, "mixin": {"Note": "目前仅HTTP协议回源支持自定义端口，HTTPS协议回源使用443端口，跟随请求协议回源将根据请求指定的协议回源到您源站的80或443端口", "ttlTip": {"itm1": "请设置正确的缓存时间，输入数字"}, "follow": "跟随"}, "alogicCache": {"Type": "类&emsp;&emsp;型", "Content": "内&emsp;&emsp;容", "CacheTime": "缓存时间", "Priority": "权&emsp;&emsp;重", "CacheURL": "缓存 URL", "OK": "确 定", "Cancel": "取 消"}, "location": {"chinaMainland": "中国内地", "oversea": "海外", "global": "全球", "globalExcludingMainland": "全球（不含中国内地）"}, "extention": {"dynamic": "动态文件", "image": "图片文件", "style": "样式文件", "media": "音视频文件", "download": "下载文件", "page": "页面文件"}, "alogicCacheMixin": {"CacheTtlMap": {"2": "天", "3": "小时", "4": "分钟", "5": "秒"}, "CacheTypeMap": {"1": "不缓存", "2": "优先遵循源站", "3": "强制缓存"}, "timeTypeMap": {"2": "天", "3": "小时", "4": "分钟", "5": "秒"}, "ValidationErrorMessages": {"RequiredCacheTime": "请输入过期时间", "CacheTimeExceedsLimit": "请设置正确的缓存时间，单位为{type}，过期时间最大值为{value}", "RequiredPriority": "请输入权重", "InvalidPriority": "请设置正确的权重，1-100整数", "DuplicateValidation": "请避免出现重复{type}，请检查 {repeatedName} {type}", "selectCache": "请选择{mode}"}, "FileSuffix": {"0": "jpg, png, css（以“,”分割）", "1": "/test, /a/b/c（不能以“/”结尾）", "4": "/index.html, /test/a.jpg"}}, "domainInput": {"placeholder": "请输入加速域名，如www.ctyun.cn", "orderLink": "工单咨询", "verify": "进入验证环节", "ownershipVerify": "认证域名归属权", "ownershipVerifyTip": "域名【{model}】需要完成归属权验证，您可以通过DNS解析验证或文件验证，若操作失败请", "serviceTicket": "前往客服工单系统", "ownershipVerifyTip2": "提客户工单。", "dnsVerify": {"itm1": "1、请在您的域名DNS服务商添加以下 TXT 记录。", "itm2": "2、等待TXT解析生成。", "itm3": "3、点击下方按钮进行验证。", "label": "DNS 解析认证"}, "verifyLink": "验证操作指南", "descriptions": {"itm1": "记录类型", "itm2": "主机记录", "itm3": "记录值"}, "verifyText": "验证", "verifyRst1": "验证结果：", "fileVerify": {"itm1": "1、请在您的{domainZone}域名源站根目录创建一个文件。", "itm2": "文件名：{filename}，文件内容：{zoneRecord}", "itm3": "2、通过 http(s)://{domainZone}/{filename} 访问到该文件。", "itm4": "3、点击下方按钮进行验证。", "label": "文件认证"}, "verifyRst2": "验证结果：", "empty": "请输入加速域名", "invalid": "输入的加速域名格式不正确", "validating": "域名校验中", "blacklist": "该域名存在历史违规行为记录，目前不允许接入", "topError": "顶级域名错误", "verifyFailed": "域名【{model}】需要完成归属权认证，点击", "repeat": "该域名已在其他账号下存在，无法添加，如有疑问请转", "existDomain": "您的域名已存在，如有疑问请转", "orderExist": "当前域名已有进行中的工单，请等待工单处理完成再重新操作，如有疑问请转", "repeatProduct": "当前域名已存在其他产品，不可叠加使用，如有疑问请转", "validSuccess": "验证成功", "validFailed": "验证失败，失败原因为", "ownershipFailed": "域名归属权认证失败，"}, "blockTable": {"allow": "白名单", "block": "黑名单"}, "origin": {"slave": {"itm1": "备二层", "itm2": "备三层", "itm3": "备四层", "itm4": "备五层"}, "addOSS": "添加OSS域名", "numberPlaceholder": "请输入1至100的数字", "resourcePool": "资源池", "numberValidate": "请输入1-100间的整数", "selectOSS": "请选择OSS资源池"}, "originPort": {"httpTip": "请输入正确的https端口号，1-65535"}, "UaBlock": {"maxNum": "输入的UA大于{maxNum}条，请检查"}, "UriBlock": {"maxNum": "输入的URL大于{maxNum}条，请检查"}}