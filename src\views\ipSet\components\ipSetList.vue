<template>
    <div class="ip-set-list">
        <div class="search-bar-wrapper">
            <el-button
                size="small"
                icon="el-icon-plus"
                type="primary"
                @click="showAddDialog"
                :disabled="!isBillingSuccess || isIpLimited"
            >
                {{ $t("ipSet.新增") }}
            </el-button>
            <div class="search-bar">
                <div class="search-bar-item">
                    <label class="label-name">{{ $t("ipSet.form.IP集名称") }}</label>
                    <el-input
                        class="custom-input"
                        v-model="alias_name"
                        clearable
                        :placeholder="$t('ipSet.请输入IP集名称')"
                    />
                </div>
                <div class="search-bar-item">
                    <el-button type="primary" plain @click="searchIpSets">{{ $t("ipSet.查询") }}</el-button>
                    <el-button type="primary" plain @click="refreshIpSets">{{ $t("ipSet.重置") }}</el-button>
                </div>
            </div>
        </div>

        <div class="table-wrapper">
            <el-table :data="ipSetList" v-loading="loading">
                <el-table-column :label="$t('common.table.index')" type="index" width="60" />
                <el-table-column :label="$t('ipSet.table.类型')" prop="forbid_type">
                    <template #default="{ row }">
                        {{ getForbidTypeText(row.forbid_type) }}
                    </template>
                </el-table-column>
                <el-table-column :label="$t('ipSet.form.IP集名称')" prop="alias_name" min-width="200" />
                <el-table-column>
                    <template #header>
                        <el-tooltip placement="top" effect="dark">
                            <div slot="content">
                                {{ $t("ipSet.status.部署任务已过期：近15天无相关部署任务。") }}<br />
                                {{ $t("ipSet.status.未部署：新集合没有ip，无需部署。") }}
                            </div>
                            <span>
                                {{ $t("ipSet.table.部署状态") }}
                                <i class="el-icon-info"></i>
                            </span>
                        </el-tooltip>
                    </template>
                    <template #default="{ row }">
                        <cute-state :type="getDeployStatusType(row.deploy_status)">
                            {{ getDeployStatusText(row.deploy_status) }}
                        </cute-state>
                    </template>
                </el-table-column>
                <el-table-column :label="$t('ipSet.table.创建时间')" prop="create_time" />
                <el-table-column :label="$t('ipSet.table.修改时间')" prop="update_time" />
                <el-table-column :label="$t('common.table.operation')" width="300">
                    <template #default="{ row }">
                        <el-button type="text" size="small" @click="viewIpSet(row)">{{
                            $t("ipSet.operation.查看")
                        }}</el-button>
                        <el-button
                            type="text"
                            size="small"
                            @click="editIpSet(row)"
                            :disabled="warningStatus.includes(row.deploy_status) || !isBillingSuccess"
                        >
                            {{ $t("ipSet.operation.编辑") }}
                        </el-button>
                        <el-button
                            type="text"
                            size="small"
                            @click="deleteIpSet(row)"
                            :disabled="!isBillingSuccess"
                            >{{ $t("ipSet.operation.删除") }}</el-button
                        >
                        <el-button type="text" size="small" @click="downloadIpSet(row)">{{
                            $t("ipSet.operation.下载")
                        }}</el-button>
                        <el-button
                            type="text"
                            size="small"
                            @click="bindDomain(row)"
                            :disabled="!isBillingSuccess"
                            >{{ $t("ipSet.operation.绑定域名") }}</el-button
                        >
                        <el-button
                            type="text"
                            size="small"
                            @click="retryIpSet(row)"
                            :disabled="!errorStatus.includes(row.deploy_status) || !isBillingSuccess"
                        >
                            {{ $t("ipSet.operation.重试") }}
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>

        <!-- IP集新增对话框 -->
        <ip-set-dialog :visible.sync="dialogVisible" @success="handleDialogSuccess" />

        <!-- IP集编辑/查看弹窗 -->
        <ip-set-edit-dialog
            :visible.sync="editDialogVisible"
            :is-view="isView"
            :ip-set="currentIpSet"
            @success="handleEditDialogSuccess"
            v-if="editDialogVisible"
        />
    </div>
</template>

<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import { ScreenModule } from "@/store/modules/screen";
import { IpSetStatusEnum, IpSetItem, IpSetStatusMap } from "@/types/ipSet";
import { IpSetUrl } from "@/config/url/ipSet";
import IpSetDialog from "./IpSetDialog.vue";
import IpSetEditDialog from "./IpSetEditDialog.vue";
import { nUserModule } from "@/store/modules/nuser";
import { SecurityAbilityModule } from "@/store/modules/securityAbility";
import { checkCtiamButtonAuth } from "@/store/modules/menu";
import { GetCtiamButtonAction } from "@/store/config";

@Component({
    components: {
        IpSetDialog,
        IpSetEditDialog,
    },
})
export default class IpSetList extends Vue {
    private loading = false;
    private alias_name = "";
    private ipSetList: IpSetItem[] = [];
    // private total = 0;
    // private page = 1;
    // private pageSize = 10;

    // 对话框相关数据
    private dialogVisible = false;
    private editDialogVisible = false;
    private isView = false;
    private currentIpSet: IpSetItem = {} as IpSetItem;

    // 类型枚举
    private IpSetStatusEnum = IpSetStatusEnum;

    private successStatus = [IpSetStatusEnum.AllSuccess, IpSetStatusEnum.AllSuccess3];
    private warningStatus = [IpSetStatusEnum.Deployed, IpSetStatusEnum.Pending];
    private errorStatus = [
        IpSetStatusEnum.DeployFailed,
        IpSetStatusEnum.PartialFailed,
        IpSetStatusEnum.Exception,
    ];
    private infoStatus = [IpSetStatusEnum.TaskCleared, IpSetStatusEnum.NewSet];

    // 计算属性
    get isXs() {
        return ScreenModule.width < 600;
    }

    get isBillingSuccess() {
        return window.__POWERED_BY_QIANKUN__ ? SecurityAbilityModule.isBilling : true;
    }

    get isIpLimited() {
        if (!window.__POWERED_BY_QIANKUN__) {
            return false;
        }
        return this.ipSetList.length >= SecurityAbilityModule.ipSet.ipSetLimit;
    }

    // 获取禁止类型文本
    private getForbidTypeText(type: number): string {
        const typeMap: Record<number, string> = {
            0: this.$t("ipSet.form.黑名单").toString(),
            1: this.$t("ipSet.form.白名单").toString(),
        };
        return typeMap[type] || "-";
    }

    // 获取部署状态文本
    private getDeployStatusText(status: IpSetStatusEnum): string {
        return this.$t(IpSetStatusMap[status] || "-").toString();
    }

    // 获取部署状态类型（用于显示不同的状态样式）
    private getDeployStatusType(status: IpSetStatusEnum): string {
        if (this.successStatus.includes(status)) return "success";
        if (this.warningStatus.includes(status)) return "warning";
        if (this.errorStatus.includes(status)) return "danger";
        if (this.infoStatus.includes(status)) return "info";
        return "info";
    }

    created() {
        this.loadIpSets();
    }
    /**
     * 异步加载IP集合数据
     *
     * 此方法用于从服务器获取IP集合列表它使用GET请求发送到指定的URL，
     * 并根据当前的别名名称筛选结果请求过程中会设置loading状态为true，
     * 以用于UI更新请求完成后，无论是成功还是失败，都会将loading状态设置为false
     *
     */
    private async loadIpSets() {
        this.loading = true;
        const rst = await this.$ctFetch<{ list: IpSetItem[] }>(IpSetUrl.list, {
            method: "GET",
            transferType: "json",
            data: {
                // pageIndex: this.page,
                // pageSize: this.pageSize,
                alias_name: this.alias_name,
            },
        });
        this.ipSetList = Array.isArray(rst.list) ? rst.list : [];
        // this.total = rst.total;
        this.loading = false;
    }
    /**
     * 刷新IP集合
     *
     * 该方法用于重置别名名称，并加载新的IP集合
     * 它没有参数，也不返回任何值
     */
    private refreshIpSets() {
        this.alias_name = "";
        this.loadIpSets();
    }
    /**
     * 搜索IP设置
     * 该方法用于触发IP设置的搜索功能
     * 在搜索IP设置之前，将当前页面重置为第一页
     */
    private searchIpSets() {
        // this.page = 1;
        this.loadIpSets();
    }

    // private handleSizeChange(size: number) {
    //     this.pageSize = size;
    //     this.loadIpSets();
    // }

    // private handlePageChange() {
    //     this.loadIpSets();
    // }

    private async showAddDialog() {
        await checkCtiamButtonAuth(GetCtiamButtonAction("ipSetListAdd"));

        this.dialogVisible = true;
    }

    private async viewIpSet(row: IpSetItem) {
        await checkCtiamButtonAuth(GetCtiamButtonAction("ipSetListDetail"));

        this.isView = true;
        this.currentIpSet = row;
        this.editDialogVisible = true;
    }
    /**
     * 编辑IP集合项
     *
     * 此方法用于准备编辑一个已存在的IP集合项它将当前选中的IP集合项信息加载到编辑对话框中
     * 主要执行以下操作：
     * 1. 将isView设置为false，表示当前操作不是查看而是编辑
     * 2. 将当前选中的IP集合项赋值给currentIpSet，以便在编辑对话框中显示当前项的信息
     * 3. 设置editDialogVisible为true，以显示编辑对话框
     *
     * @param row 要编辑的IP集合项
     */
    private async editIpSet(row: IpSetItem) {
        await checkCtiamButtonAuth(GetCtiamButtonAction("ipSetListEdit"));

        this.isView = false;
        this.currentIpSet = row;
        this.editDialogVisible = true;
    }
    /**
     * 异步删除IP集
     *
     * 此函数首先显示一个确认对话框，以确保用户确实想要删除指定的IP集如果用户确认删除，
     * 则发送一个POST请求到IpSetUrl.delete，请求中包含要删除的IP集的唯一名称
     *
     * @param row 包含要删除的IP集信息的IpSetItem对象
     */
    private async deleteIpSet(row: IpSetItem) {
        await checkCtiamButtonAuth(GetCtiamButtonAction("ipSetListDelete"));

        await this.$confirm(
            this.$t("ipSet.confirm.确定要删除该IP集吗？").toString(),
            this.$t("common.messageBox.title").toString(),
            {
                confirmButtonText: this.$t("common.dialog.submit").toString(),
                cancelButtonText: this.$t("common.dialog.cancel").toString(),
                type: "warning",
            }
        );

        const res = await this.$ctFetch(IpSetUrl.delete, {
            method: "POST",
            data: {
                unique_name: row.unique_name,
            },
            headers: {
                "Content-Type": "application/json",
            },
        });

        if (res) {
            this.$message.success(this.$t("ipSet.success.IP集删除成功").toString());
            this.loadIpSets();
        }
    }

    private async downloadIpSet(row: IpSetItem) {
        await checkCtiamButtonAuth(GetCtiamButtonAction("ipSetListDownload"));

        const url = `${window.location.origin}${IpSetUrl.download}?unique_name=${row.unique_name}&workspaceId=${nUserModule.workspaceId}`;
        window.open(url);
    }

    /**
     * 根据所选的IP集合项导航到批量绑定域名页面
     * 此方法解释了如何在用户界面中选择一个IP集合项后，导航到批量绑定域名的页面
     * 它使用路由参数传递IP集合的别名和禁用类型，以便在新页面中进行相应的操作
     *
     * @param row IpSetItem类型，代表当前选中的IP集合项
     *            该参数包含IP集合的别名和禁用类型，用于导航到批量绑定域名页面
     */

    private async bindDomain(row: IpSetItem) {
        await checkCtiamButtonAuth(GetCtiamButtonAction("ipSetListBindDomain"));

        this.$router.push({
            name: "ipset.batchBind",
            query: {
                name: row.alias_name,
                sourceType: "ipset",
                forbid_type: String(row.forbid_type),
            },
        });
    }

    private handleDialogSuccess() {
        this.loadIpSets();
    }

    private handleEditDialogSuccess() {
        this.loadIpSets();
    }
    /**
     * 异步重试IP设置操作
     * 该方法用于在IP设置失败时，重新尝试设置
     *
     * @param row {IpSetItem} - 表示一个IP设置项的对象，包含需要重试设置的IP信息
     */
    private async retryIpSet(row: IpSetItem) {
        await checkCtiamButtonAuth(GetCtiamButtonAction("ipSetListRetry"));

        await this.$ctFetch(IpSetUrl.retry, {
            method: "POST",
            data: {
                unique_name: row.unique_name,
            },
        });
        this.$message.success(this.$t("common.message.success").toString());
        this.loadIpSets();
    }
}
</script>

<style lang="scss" scoped>
.ip-set-list {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.search-bar-wrapper {
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px;
    flex-shrink: 0;
}

.search-bar {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
    flex-shrink: 0;
    .label-name {
        margin-right: 12px;
    }
    &-item {
        display: flex;
        align-items: center;
        label {
            flex-shrink: 0;
            font-size: 12px;
        }
    }
    .custom-input {
        ::v-deep {
            .el-input__inner {
                padding-right: 12px;
            }
        }
    }
}

.table-wrapper {
    flex: 1;
    min-height: 0;
    overflow: hidden;
    ::v-deep .el-table {
        height: calc(100% - 60px);
    }
}
</style>
