import { DomainActionEnum } from "@/store/config";
import {
    DomainMapLabel,
    DomainMapLabelJr,
    InterfaceLabelItem,
    LabelItem,
    LabelMapDomain,
} from "@/store/types";
import { VuexModule } from "vuex-module-decorators";

type LabelUnit = {
    labelList: LabelItem[];
    nativeList: InterfaceLabelItem[];
    requestTime: number;
};

/**
 * 创建一个新的标签单元实例
 */
export const getLabelUnit = (): LabelUnit => ({
    labelList: [],
    nativeList: [],
    requestTime: 0,
});

interface LabelState {
    loading: boolean;
    nativeList: InterfaceLabelItem[]; // 接口数据，业务可以自由扩展用法
    labelList: LabelItem[]; // 简化版的树结构，用于 labelSelect 组件
    domainMapLabel: DomainMapLabel; // 域名已绑定的标签信息映射关系，存储的标签包含详细信息
    domainMapLabelJr: DomainMapLabelJr; // 简化版的映射关系，只存储标签 id
    lableMapDomain: LabelMapDomain; // 标签已绑定的域名列表映射关系，只存储域名
    subtleLabelMap: Map<string, Map<string, string>>; // 二级标签映射，key为标签 id
    labelGroupMap: Map<string, string>; // 标签组的映射
}

export class BaseLabel extends VuexModule implements LabelState {
    public loading = false;
    public nativeList: InterfaceLabelItem[] = [];
    public labelList: LabelItem[] = [];
    public domainMapLabel: DomainMapLabel = {};
    public domainMapLabelJr: DomainMapLabelJr = {};
    public lableMapDomain: LabelMapDomain = {};
    public subtleLabelMap: Map<string, Map<string, string>> = new Map();
    public labelGroupMap: Map<string, string> = new Map();

    public [DomainActionEnum.BandwidthFlow]: LabelUnit = getLabelUnit();
    public [DomainActionEnum.BandwidthFlowWhole]: LabelUnit = getLabelUnit();
    public [DomainActionEnum.Miss]: LabelUnit = getLabelUnit();
    public [DomainActionEnum.MissWhole]: LabelUnit = getLabelUnit();
    public [DomainActionEnum.Request]: LabelUnit = getLabelUnit();
    public [DomainActionEnum.RequestWhole]: LabelUnit = getLabelUnit();
    public [DomainActionEnum.Hit]: LabelUnit = getLabelUnit();
    public [DomainActionEnum.HitWhole]: LabelUnit = getLabelUnit();
    public [DomainActionEnum.StatusCode]: LabelUnit = getLabelUnit();
    public [DomainActionEnum.StatusCodeWhole]: LabelUnit = getLabelUnit();
    public [DomainActionEnum.BackToOriginStatusCode]: LabelUnit = getLabelUnit();
    public [DomainActionEnum.BackToOriginStatusCodeWhole]: LabelUnit = getLabelUnit();
    public [DomainActionEnum.PvUv]: LabelUnit = getLabelUnit();
    public [DomainActionEnum.PvUvWhole]: LabelUnit = getLabelUnit();
    public [DomainActionEnum.Provider]: LabelUnit = getLabelUnit();
    public [DomainActionEnum.ProviderWhole]: LabelUnit = getLabelUnit();
    public [DomainActionEnum.DownloadSpeed]: LabelUnit = getLabelUnit();
    public [DomainActionEnum.PrivateNetworkAccelerator]: LabelUnit = getLabelUnit();
    public [DomainActionEnum.PrivateNetworkAcceleratorWhole]: LabelUnit = getLabelUnit();
    public [DomainActionEnum.HotUrl]: LabelUnit = getLabelUnit();
    public [DomainActionEnum.HotUrlWhole]: LabelUnit = getLabelUnit();
    public [DomainActionEnum.HotUrlMiss]: LabelUnit = getLabelUnit();
    public [DomainActionEnum.HotUrlMissWhole]: LabelUnit = getLabelUnit();
    public [DomainActionEnum.HotReferer]: LabelUnit = getLabelUnit();
    public [DomainActionEnum.HotRefererWhole]: LabelUnit = getLabelUnit();
    public [DomainActionEnum.DomainRank]: LabelUnit = getLabelUnit();
    public [DomainActionEnum.DomainRankWhole]: LabelUnit = getLabelUnit();
    public [DomainActionEnum.TopIp]: LabelUnit = getLabelUnit();
    public [DomainActionEnum.TopIpWhole]: LabelUnit = getLabelUnit();
}
