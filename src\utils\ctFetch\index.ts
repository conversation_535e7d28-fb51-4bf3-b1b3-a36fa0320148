/*
 * @Description: ctFetch 统一配置
 * @Author: wang y<PERSON><PERSON>
 */
import _ctFetch from "alogic-base-web/src/ctFetch/index.js";
import { getHashRoute } from "./errorHandler";
import { get } from "lodash-es";

export { errorHandler } from "./errorHandler";
import i18n from "./i18n";

type ctFetchType = {
    <T>(url: string, options?: CtFetchOptions): Promise<T>;
    config?: (options: CtFetchConfigOptios) => void;
    interceptors?: {
        request?: {
            use: (cb: CtFetchReqInterceptorCb) => void;
        };
        response?: {
            use: (cb: CtFetchResInterceptorCb) => void;
        };
    };
};

// 改写ctFetch，支持options中配置signal取消请求
const ctFetch: ctFetchType = (url: string, options = {}) => {
    const { signal } = options;
    // 统一配置超时时间 60s
    _ctFetch.config({
        timeout: 60,
        transferType: "json", // 默认 post 请求转换为 json 格式，特殊类型自行定义
    });

    _ctFetch.interceptors.request.use(({ config } : { config: any }) => {
        // 提供一个请求所处的路由，用于后续判断
        setTimeout(() => {
            // 使用宏任务滞后，确保触发时的 route 获取正确
            const hashRoute = getHashRoute();
            config.hashRoute = hashRoute;
        });
        // 设置 i18n 请求 header
        config.headers = config.headers || {};
        config.headers.language = i18n.locale;
        config.signal = signal; // 传入signal

        return true;
    });

    _ctFetch.interceptors.response.use((options: any) => {
        const { data, url, config } = options;
        // i18n 对 ctFetch 内置报错信息进行处理
        if (get(data, "code", "").startsWith("err")) {
            if (data.code === "err_timeout" || data.code === "err_svr") {
                const number = data.reason.match(/\d+/)?.[0];
                data.reason = (i18n.t(`errMsg.${data.code}`, { number }) as string) || data.reason;
            } else {
                data.reason = (i18n.t(`errMsg.${data.code}`) as string) || data.reason;
            }
        }
        /**
         * 统一数据格式：
         *  接口正常报错是 options: { data, url, config }
         *  ctFetch 内置错误 options: { data: { code, reason, cofig, row }, url } ，见 node_modules\alogic-base-web\s\\\rc\ctFetch\err.js
         */
        let hashRoute;
        if ("config" in data) {
            hashRoute = data.config?.hashRoute;
        } else {
            hashRoute = config?.hashRoute;
        }

        // 缓存请求 url 数据
        data.url = url;
        (data as CtFetchResponse).hashRoute = hashRoute;

        // if (data.code === "core.ok") return true;

        return true;

        // 弃用，交由 errorHandler 统一处理
        // 1019 表示无法获取当前用户，即登录已超时
        // if (data.code === "core.e1019" || (data.data && data.data.code === "core.e1019")) {
        //     window.location.href = LoginUrl;
        //     return false;
        // }
    });

    return _ctFetch(url, options);
};

Object.assign(ctFetch, _ctFetch);
Object.setPrototypeOf(ctFetch, Object.getPrototypeOf(_ctFetch));

export { ctFetch };
