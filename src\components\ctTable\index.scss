.ct-table {
    display: flex;
    flex-direction: column;
}
.center-row {
    //justify-content: center;

    .button-box {
        margin-right: 10px;
    }

    & > .button-box:last-child {
        margin-right: unset;
    }
}

::v-deep.el-pagination {
    display: flex;
    align-items: center;
    height: 50px;
    //margin-top: 12px;
    flex-shrink: 0;

    .el-pagination__total, .el-pagination__sizes, .el-pager .number, .el-pagination__jump {
        font-size: 12px !important;
    }

    .el-pager {
        li {
            margin: 0 5px;
            background-color: #f4f4f5;
            color: #606266;
            min-width: 30px;
            border-radius: 2px;
        }

        .number {
            &.active {
                color: white;
                background-color: #ff9831;
            }
        }
    }

    .el-pagination__sizes {
        .el-input__inner {
            height: 28px;
        }
    }
}

.icon-column-label {
    margin-left: 5px;
}
