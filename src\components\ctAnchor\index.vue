<template>
    <div class="ct-anchor">
        <div
            v-for="(item, key) in data"
            :id="`anchor${item.prop.replace(/^\#/, '_')}`"
            :key="item.prop + new Date().getTime()"
            class="flex-row-style anchor-item"
            :class="{ active: currentIndex === key }"
            @click="handleScrollToTarget(item)"
        >
            <i class="icon-circle" :class="{ 'line-style': key !== data.length - 1 }" />
            <span class="label-style">{{ item.label }}</span>
        </div>
    </div>
</template>

<script>
import { get } from "lodash-es";

export default {
    name: "index",
    props: {
        data: {
            type: Array,
            default: () => [],
        },
        // 监听的滚动元素
        scrollDom: {
            type: String,
            required: true,
            default: "",
        },
    },
    data() {
        return {
            currentIndex: 0,
            containerToTop: 0, // 滚动容器至定点的距离
        };
    },
    computed: {
        maxIndex() {
            return this.data.length - 1;
        },
    },
    updated() {
        this.$emit("update-child", new Date().getTime());
    },
    mounted() {
        if (!this.scrollDom) {
            return;
        }

        const scrollEle = document.querySelector(this.scrollDom);
        if (!scrollEle) {
            return;
        }

        this.containerToTop = get(scrollEle.getBoundingClientRect(), "top", 0);
        scrollEle.addEventListener("scroll", this.handleScroll);
    },
    beforeDestroy() {
        const scrollEle = document.querySelector(this.scrollDom);
        if (!scrollEle) {
            return;
        }

        scrollEle.removeEventListener("scroll", this.handleScroll);
    },
    methods: {
        /**
         * 处理滚动事件
         */
         handleScroll(e) {
            this.containerToTop = get(document.querySelector(this.scrollDom)?.getBoundingClientRect(), "top", 0);
            const scrollDom = e.target;
            const contentAreaHeight = scrollDom.offsetHeight; // 内容区域的高度
            const contentAreaTopPadding = this.containerToTop; // 内容区域到网页可视区顶部的距离
            const distance = [];
            const switchAnchorArr = this.data.map(item => {
                const labelDom = document.querySelector(item.prop);
                // 获取标题到内容区顶部的距离
                let labelDomToContentAreaTop = Infinity;
                // console.log('prop = ', item.prop, 'labelDom = ', labelDom)
                if (labelDom) {
                   labelDomToContentAreaTop = get(labelDom?.getBoundingClientRect(), "top", 0) - contentAreaTopPadding;
                }
                distance.push(labelDomToContentAreaTop);
                // 当标题移动到内容区可视区中间的时候，需要切换锚点到下一个标题
                // TODO: 待优化 URL鉴权
                if (labelDomToContentAreaTop >= 0 && labelDomToContentAreaTop <= contentAreaHeight / 4) {
                    return true;
                }
                return false
            })
            if (switchAnchorArr.includes(true)) {
                let index = switchAnchorArr.findIndex(item => item === true);
                let anchorArr = switchAnchorArr.slice(index + 1);
                while (anchorArr.length) {
                    const cur = anchorArr.findIndex(item => item === true);
                    if (cur < 0) {
                        anchorArr = [];
                    } else {
                        distance[cur + index + 1] < distance[index] && (index = cur + index + 1);
                        anchorArr = anchorArr.slice(index + 1);
                    }
                }
                this.currentIndex = index;
            } else {
                // 没有滚动到内容区前1/4的时候，取距离绝对值最小的
                const positiveDistance = distance.map(item => Math.abs(item));
                this.currentIndex = positiveDistance.indexOf(Math.min(...positiveDistance));
            }
        },
        /**
         * 滚动到目标元素
         */
        handleScrollToTarget(item) {
            const target = document.querySelector(item.prop);
            if (!target) {
                return;
            }

            const scrollTopDom = document.getElementsByClassName("ct-config")[0] || {}; // 滚动元素的父级
            scrollTopDom.scrollTop = target.offsetTop + target.offsetParent.offsetTop - 20; // 设置父元素的滚动条位置
        },
    },
};
</script>

<style scoped lang="scss">
.ct-anchor {
    .anchor-item {
        height: 40px;
        cursor: pointer;

        .icon-circle {
            width: 10px;
            height: 10px;
            display: inline-block;
            background: white;
            border-radius: 50%;
            //border: 2px solid $color-master;
            border: 2px solid $color-neutral-6;
            margin-right: $margin-2x;
            position: relative;

            &.line-style {
                &:after {
                    content: "";
                    width: 2px;
                    height: 30px;
                    background: $color-bg-3;
                    position: absolute;
                    left: 50%;
                    top: 10px;
                    transform: translateX(-50%);
                }
            }
        }

        .label-style {
            font-family: PingFangSC-Regular;
            font-size: 12px;
            //color: $text-color-light;
            color: $color-neutral-6;
            line-height: 18px;
            font-weight: 400;
            white-space: nowrap;
        }

        &:hover,
        &.active {
            .icon-circle {
                border-color: $color-master;
            }

            .label-style {
                color: $text-color-light;
            }
        }
    }
}
</style>
