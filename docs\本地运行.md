# 本地运行

## Prerequisites

- 申请相关权限，请查阅[这里](https://devops.ctcdn.cn/wone/pages/viewpage.action?pageId=15028921)
- 安装 node.js (12.22.12)
- 确保已拉取最新的代码库并切换至相应的分支。
- 安装依赖，**请确保安装时的node版本为12.22.12，否则运行项目后会报错**
  > npm install --registry=<http://verdaccio.ctcdn.cn/>
- 完成本地[环境配置](./环境配置.md)

## 开发模式下启动项目

- 运行vip测试环境
  > 登录[vip测试环境](https://www-test.ctcdn.com/h5/fcdn)
  > npm run serve:vip-test
- 运行vip uat环境
  > 登录[vip uat环境](https://console-pre.ctcdn.com/h5/fcdn)
  > npm run serve:vip-pre
- 运行ctyun测试环境
  > 登录[ctyun测试环境](https://cdn-test.ctyun.cn/h5/fcdn)
  > npm run serve:ctyun-test
- 运行ctyun uat环境
  > 登录[ctyun uat环境](https://cdn-pre.ctyun.cn/h5/fcdn)
  > npm run serve:ctyun-pre
- 运行国际站测试环境
  > 登录 [国际站测试环境](https://cdn-test.esurfingcloud.com/h5/fcdn/)
  > npm run serve:intl-test
- 运行国际站 uat环境
  > 登录 [国际站 uat环境](https://cdn-pre.esurfingcloud.com/h5/fcdn/)
  > npm run serve:intl-pre
- aocdn 访问 www-dev(开发环境)
  > 登录 [开发环境](https://www-test-dev.ctcdn.cn/h5/fcdn/)
  > npm run serve:dev
  > 如果需要访问 aocdn 的开发环境，需要将主应用 ./vue.config.js 中的 SVR_URL 同步改为 http://www-test-dev.ctcdn.cn

## 关闭安全策略启动 chrome 浏览器

### windows
因部分接口会根据后台服务重写到不同 url 而出现跨域

```bash
# 通过命令行操作，先杀掉所有进程，再带参启动 chrome （命令行启动需要配置对应环境变量），出现“稳定性和安全性会有所下降”即为成功
taskkill /F /IM chrome.exe
start chrome https://local.ctcdn.cn:9991 –disable-web-security –user-data-dir="C:\local.ctcdn.cn"

# edge
"C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe" --user-data-dir=C:\msedge-dev-data\ --disable-web-security
```

### mac

1. `在/Users/<USER>/Documents` 目录下创建一个名为 `ChromeDevUserData` 的文件夹
2. 打开终端运行下面的名称，记得将 `/Users/<USER>/Documents/ChromeDevUserData` 替换成你创建的文件夹路径
3. 如果出现“稳定性和安全性会有所下降”即为成功，否则使用管理员权限运行终端后再运行命令
```sh
open -n /Applications/Google\ Chrome.app/ --args --disable-web-security --user-data-dir=/Users/<USER>/Documents/ChromeDevUserData
```
