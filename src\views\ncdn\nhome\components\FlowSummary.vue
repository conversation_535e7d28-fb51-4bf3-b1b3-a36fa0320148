<template>
    <ct-box :tags="$t('home.chart.title1')">
        <template #tags-slot>
            <el-radio-group v-model="duration" size="small" class="time-radio" @change="onChangeDuration">
                <el-radio-button class="time-radio-button" label="1">{{
                    $t("home.chart.today")
                }}</el-radio-button>
                <el-radio-button class="time-radio-button" label="3">{{
                    $t("home.chart.instant")
                }}</el-radio-button>
            </el-radio-group>
            <div class="chart-title" v-if="!isCtclouds">
                <el-link :underline="false" class="more" @click="moreData"> {{ $t("home.more") }} </el-link>
            </div>
        </template>
        <div class="data-wrapper" v-loading="loading">
            <span class="title">{{ $t("home.chart.totalFlow") }}：</span>
            <span class="data" style="margin-right: 10%">
                <span class="number">{{ flow.num || 0 }}</span>
                <span>{{ flow.unit || "MB" }}</span>
            </span>
            <span class="title">{{ $t("home.chart.peakBandwidth") }}：</span>
            <span class="data">
                <span class="number">{{ bandwidth.num || 0 }}</span>
                <span>{{ bandwidth.unit || "Mbps" }}</span>
            </span>
        </div>
    </ct-box>
</template>

<script lang="ts">
import { Component, Prop, Vue, Watch } from "vue-property-decorator";
import { StatisticsUsageUrl } from "@/config/url/statistics";
import { get0AM } from "@/utils";
import { DomainModule } from "@/store/modules/domain";
import { ScaleModule } from "@/store/modules/scale";
import { convertBandwidthM2P, convertFlowM2P } from "@/utils";
import { divideScale } from "@/utils/unit";
import { DomainActionEnum, GetCtiamButtonAction, getDomainAction } from "@/config/map";
import { StatisticsModule } from "@/store/modules/statistics";
import { nUserModule } from "@/store/modules/nuser";
import { checkCtiamButtonAuth } from "@/store/modules/menu";

@Component
export default class FlowSummary extends Vue {
    @Prop({ default: true }) onInit!: boolean;

    public loading = true;
    public duration = "1";
    private method = "flow";
    private totalFlow = 0;
    private peekBandwidth = 0;

    // 是否子账号
    get childAccount() {
        return StatisticsModule.childAccount;
    }
    get domainCountLimit() {
        return StatisticsModule.domainCountLimit;
    }

    get isFcdnCtyunCtclouds() {
        return nUserModule.isFcdnCtyunCtclouds;
    }
    // 域名列表
    get domainList() {
        // fcdn-ctyun 体系（国内+国际）需要取ctiam权限管控下的域名数据
        return this.isFcdnCtyunCtclouds
            ? DomainModule[getDomainAction("Home")].list
            : DomainModule[DomainActionEnum.Data].list;
    }

    get scale() {
        return ScaleModule.scale;
    }

    get isCtclouds() {
        return nUserModule.isCtclouds;
    }

    get flow() {
        const flow = this.totalFlow;
        return this.getIndentationRst(flow, "flow");
    }

    get bandwidth() {
        const bandwidth = this.peekBandwidth;
        return this.getIndentationRst(bandwidth, "bandwidth");
    }

    onChangeDuration(val: string) {
        this.duration = val;
        if (this.onInit) return;
        this.getData();
    }

    @Watch("onInit")
    onInitChange(val: boolean) {
        if (val) return;
        this.getData();
    }

    // 处理带宽和流量缩进
    getIndentationRst(data = 0, type: string) {
        //传入M，转化为M、G、T、 P
        return type === "bandwidth"
            ? convertBandwidthM2P(data, this.scale)
            : convertFlowM2P(data, this.scale);
    }
    // 获取流量 flow 、带宽 bandwidth
    async getData() {
        // 如果还在请求初始化数据，拦截请求
        if (this.onInit) return;
        // 无可选域名时，拦截请求
        if (this.domainList.length === 0) return;
        // 子账号，域名数量 > 100个，不调用接口
        if (this.childAccount && this.domainList.length > this.domainCountLimit) {
            return;
        }
        const timeArr: number[] = get0AM(new Date());
        this.loading = true;
        const rst = await this.$ctFetch<{ [key: string]: number }>(StatisticsUsageUrl.queryOverviewList, {
            method: "POST",
            body: {
                data: {
                    type: "flow",
                    // domainList: this.domainList,
                    // 域名数量大于等于100时，不传递域名参数
                    domainList: this.childAccount ? this.domainList : [],
                    startTime: timeArr[+this.duration - 1],
                    endTime: Math.floor(+new Date() / 1000),
                    // 不传递域名参数domainList时，需要传递productType
                    productType: this.childAccount ? [] : ["008", "005", "001", "003", "004", "006", "104", "105", "014"],
                },
            },
            headers: {
                "Content-Type": "application/json",
            },
        });
        this.loading = false;
        this.totalFlow = +divideScale(rst.totalFlow);
        this.peekBandwidth = +divideScale(rst.topBandwidth);
    }
    async moreData() {
        await checkCtiamButtonAuth(GetCtiamButtonAction("overviewStatisticMore"));
        //sessionStorage.setItem("index2statistics", true);
        this.$router.push({
            name: "statistics.cdn",
            params: {
                chartType:
                    this.method === "flow"
                        ? (this.$t("home.chart.flow") as string)
                        : (this.$t("home.chart.bandwidth") as string),
                timePeriod: "7",
            },
        });
    }
}
</script>

<style lang="scss" scoped>
.time-radio {
    text-align: center;

    ::v-deep {
        .time-radio-button .el-radio-button__inner {
            display: inline-block;
            width: auto;
        }
    }
}

.chart-title {
    float: right;
}

.more {
    line-height: 28px;
}

.data-wrapper {
    padding-top: 20px;
    text-align: center;

    // .title {
    // font-size: 12px;
    // }
    .data {
        .number {
            color: $color-master;
            font-size: 26px;
            margin-right: 4px;
        }
    }
}
</style>
