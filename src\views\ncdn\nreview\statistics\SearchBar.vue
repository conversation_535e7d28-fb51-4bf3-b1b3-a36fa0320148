<!--
 * @Description:审核分析 searchbar
        注意：1、显式声明各组件的 key ，避免因 vue 内部优化导致时重用组件，导致显示异常
 * @Author: wang yuegong
-->
<template>
    <section class="statistics-search-bar search-bar">
        <div
            class="search-row ct-select-group"
            v-if="useProduct || useDomain || useMultipleDomain || useIsp || useArea"
        >
            <label>范围</label>
            <el-select
                v-if="useProduct"
                v-model="product"
                multiple
                filterable
                collapse-tags
                placeholder="请选择加速类型"
                key="product"
            >
                <el-option
                    v-for="(item, index) in productOptions"
                    :key="index"
                    :label="item.label"
                    :value="item.value"
                />
            </el-select>
            <el-select
                v-if="useDcdnProduct || useDcdnProductOther"
                v-model="dcdnProduct"
                filterable
                clearable
                collapse-tags
                placeholder="请选择加速类型"
                key="dcdnProduct"
            >
                <el-option
                    v-for="(item, index) in productOptions"
                    :key="index"
                    :label="item.label"
                    :value="item.value"
                />
            </el-select>
            <el-select v-if="useDomain" v-model="domain" filterable placeholder="请选择域名" key="domain">
                <el-option label="全部域名" value="all" />
                <el-option
                    v-for="item in domainOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                />
            </el-select>
            <domain-select
                v-show="useMultipleDomain"
                v-model="multipleDomain"
                :domainOptions="domainOptions"
                :multiple="true"
                :loading="domainOptionsLoading"
                key="multipleDomain"
            />
            <el-select
                v-if="useIsp"
                v-model="isp"
                multiple
                filterable
                collapse-tags
                placeholder="请选择运营商"
                key="isp"
            >
                <el-option
                    v-for="(item, index) in ispOptions"
                    :key="index"
                    :label="item.isp_cnname"
                    :value="item.isp_code"
                />
            </el-select>
            <area-select
                v-if="useArea"
                :multiple="true"
                v-model="area"
                :area-list="areaOptions"
                :is-global="isGlobal"
                key="area"
            />
            <label class="slibing-label" v-if="useTimePicker || useTimeCompare">时间</label>
            <ct-time-picker
                v-if="useTimePicker"
                v-show="!compareShow"
                v-model="timeRange"
                :current-period.sync="currentPeriod"
                :periodOptions="periodOptions"
                style="margin-right: 8px"
                key="timePicker"
            />

            <ct-time-compare
                v-if="useTimeCompare"
                :compareShow.sync="compareShow"
                v-model="timeRangeArr"
                key="timeCompare"
                size="medium"
            />

            <el-button type="primary" @click="beforeSearch">查询</el-button>
            <div class="search-download" v-if="useDownload">
                <el-tooltip class="item" effect="dark" content="下载数据" placement="right">
                    <i class="el-icon-cdn-download download-icon" @click="download"></i>
                </el-tooltip>
            </div>
        </div>
    </section>
</template>

<script lang="ts">
import { Component, Prop, Watch, Vue } from "vue-property-decorator";
import { DomainModule } from "@/store/modules/domain";
import { StatisticsModule } from "@/store/modules/statistics";
import { SearchParams } from "@/types/statistics/usage";
import { DomainActionEnum, getDomainAction, ProductCodeEnum } from "@/config/map";
import AreaSelect from "@/components/areasSelect/index.vue";
import DomainSelect from "@/components/domainsSelect/index.vue";
import { busiType } from "@/config/map";
import { nUserModule } from "@/store/modules/nuser";
import { ProductModule } from "@/store/modules/ncdn/nproduct";

@Component({
    name: "SearchBar",
    components: { AreaSelect, DomainSelect },
})
export default class SearchBar extends Vue {
    // 所需查询条件的标识，使用 "domain, timepicker" 的方式传入，需要哪个就配置哪个
    // ps：根据业务需要，域名、时间都是必须要的
    @Prop({ default: "domain, timePicker", type: String }) private paramFlag!: string;
    // time-picker 的快捷配置项
    @Prop({ default: () => ["0", "1", "7", "30", "-1"], type: Array }) private periodOptions!: string[];
    // 是否使用自动发起请求，默认不自动发起
    @Prop({ default: false, type: Boolean }) private autoFetch!: boolean;

    // 从 paramFlag 中获取各条件是否启用
    // 说明：使用 watch 可以阻断不必要的计算属性监听
    private useProduct = false; // 1、全部产品（默认）
    private useDcdnProduct = false; // 3、全站加速加速类型
    private useDcdnProductOther = false; // 4、全站加速排除上传加速
    private useDomain = false; // 默认是多选
    private useMultipleDomain = false; // 单选和多选分开2个组件，便于分开存储数据
    @Watch("paramFlag", { immediate: true })
    onParamFlag(flag: string) {
        // 由于 product 可以匹配多个，所以选择 product,
        this.useProduct = flag.includes("product,");
        this.useDcdnProduct = flag.includes("dcdnProduct,");
        this.useDcdnProductOther = flag.includes("dcdnProductOther");
        this.useDomain = flag.includes("domain");
        this.useMultipleDomain = flag.includes("multipleDomain");

        // 如果当前未使用 timeCompare 且使用了 timePicker，需要恢复 timePicker 的展示
        if (!flag.includes("timeCompare") && flag.includes("timePicker")) {
            this.compareShow = false;
        }
    }

    get useIsp() {
        return this.paramFlag.includes("isp");
    }

    get useArea() {
        return this.paramFlag.includes("area");
    }

    get useTimePicker() {
        return this.paramFlag.includes("timePicker");
    }

    get useTimeCompare() {
        return this.paramFlag.includes("timeCompare");
    }

    get useDownload() {
        return this.paramFlag.includes("download");
    }
    get isFcdnCtyunCtclouds() {
        return nUserModule.isFcdnCtyunCtclouds;
    }

    // 三种产品结果
    private product: string[] = []; // 选择的加速类型
    //全站加速产品
    private dcdnProduct = "";

    private domain = "all"; // 选择框选中的域名（单选）
    private multipleDomain = []; // 选择框选中的域名（多选）
    private isp: string[] = []; // 运营商
    private area: any = {
        province: [],
        continent: [],
        continentRegion: [],
    }; // 选择的范围
    private timeRange: null | Date[] = null; // 时间
    private currentPeriod = "0"; // 当前 ct-time-picker 选择的时间段
    private timeRangeArr = [null, null]; // 对比时间

    private compareShow = false; // 是否展示时间对比

    private isDcdn = false;

    get domainAction() {
        return this.isFcdnCtyunCtclouds ? getDomainAction("ContentAuditStatistics") : DomainActionEnum.Review;
    }

    // 全部的域名列表
    get domainList() {
        // 由于需要进行二次判断过滤，选择使用接口原生数据
        // fcdn-ctyun 体系（国内+国际）需要取ctiam权限管控下的域名数据
        return DomainModule[this.domainAction].nativeList;
    }

    get domainOptionsLoading() {
        return DomainModule[this.domainAction].loading;
    }

    get productOptions() {
        const { useProduct, useDcdnProduct, useDcdnProductOther } = this;
        const options = ProductModule.allProductOptions;
        return options.filter(opt => {
            const isDcdnOptions =
                opt.value === ProductCodeEnum.Upload ||
                opt.value === ProductCodeEnum.Whole ||
                opt.value === ProductCodeEnum.Socket;
            // 筛选全站加速和普通用量分析的产品下拉选择
            if (useProduct) return !isDcdnOptions;
            if (useDcdnProduct) return isDcdnOptions;
            if (useDcdnProductOther) {
                //回源统计不含有上传加速产品
                if (this.dcdnProduct === "104") this.dcdnProduct = "";
                return isDcdnOptions && opt.value !== ProductCodeEnum.Upload;
            }
        });
    }

    get productAll() {
        return this.productOptions.map(opt => opt.value);
    }

    get ispOptions() {
        return StatisticsModule.ispOptions;
    }

    get areaOptions() {
        return StatisticsModule.areaOptions;
    }

    get isGlobal() {
        return StatisticsModule.isGlobal;
    }

    // 根据加速类型筛选展示域名列表
    get domainOptions() {
        return this.domainList.map(item => ({
            // 从接口原生数据中获取 options
            label: item.domain,
            value: item.domain,
        }));
    }

    // 已选的域名列表（在此处理单选/多选差异）
    get domainParams() {
        const { useDomain, useMultipleDomain } = this;
        const { domain, multipleDomain } = this;
        const allDomains = this.domainOptions.map(d => d.value);
        if (useDomain) {
            // 单选
            return domain === "all" ? allDomains : [domain];
        } else if (useMultipleDomain) {
            // 多选，未选时表示所有域名
            return multipleDomain.length === 0 ? allDomains : multipleDomain;
        }
        return [];
    }

    // 基础的请求参数
    get baseSearchParams() {
        const {
            useProduct,
            useDcdnProduct,
            useDcdnProductOther,
            useDomain,
            useMultipleDomain,
            useIsp,
            useArea,
        } = this;
        const { product, dcdnProduct, domainParams, isp, area } = this;

        const params: SearchParams = {
            domainList: [],
            startTime: 0,
            endTime: 0,
        };

        if (useProduct) params.productType = [...product];
        //全站加速的加速类型字段
        if (useDcdnProduct || useDcdnProductOther) {
            // productType统一传一级产品编码['006'](后端处理)
            params.productType = dcdnProduct ? [dcdnProduct] : [];
            // websocket 特殊处理
            // 1、当产品未选择，或选择的是 006 全站加速时，不传
            // 2、当产品选择 104 上传加速，直传 [1]
            // 3、当产品选择 105 websocket 加速时，默认不传，各业务组件再按需重新定义（目前只有带宽流量需要细分，请求数需要传2，后端处理成全部）
            params.busiType = dcdnProduct === ProductCodeEnum.Upload ? [busiType[dcdnProduct]] : [];
        }
        if (useDomain || useMultipleDomain) params.domainList = [...domainParams];
        if (useIsp) params.isp = [...isp];
        if (useArea) {
            // tag-area更改标注
            params.province = this.area.province;
            params.continent_code = this.area.continent;
            params.continent_region_code = this.area.continentRegion;
            // 查询全部地区(-1)则默认不传province参数
            if (params.province?.includes("-1")) {
                params.province = [];
            }
        }

        return params;
    }

    // 请求参数1
    get searchParams1() {
        const { useTimePicker, useTimeCompare } = this;
        const { baseSearchParams, timeRange, timeRangeArr, compareShow } = this;

        if (!useTimePicker && !useTimeCompare) return baseSearchParams;

        // 根据使用情况组装数据
        const time = compareShow ? timeRangeArr[0] : timeRange;
        const startTime = time ? time[0] : 0;
        const endTime = time ? time[1] : 0;

        return {
            ...baseSearchParams,
            startTime: Math.floor(+startTime / 1000),
            endTime: Math.floor(+endTime / 1000),
        };
    }

    // 请求参数2
    get searchParams2() {
        if (!this.compareShow) return null;

        const { baseSearchParams, timeRangeArr } = this;

        const time = timeRangeArr[1];
        const startTime = time ? Math.floor(time[0] / 1000) : 0;
        const endTime = time ? Math.floor(time[1] / 1000) : 0;

        return {
            ...baseSearchParams,
            startTime,
            endTime,
        };
    }

    @Watch("domainList")
    onDomainListChange(newArr: [], oldArr: []) {
        // domain 数组长度变化，或者任何一个内容不同，则重新发起请求
        const isSearch =
            (newArr.length > 0 && newArr.length !== oldArr.length) ||
            newArr.some((val, idx) => val !== oldArr[idx]);
        // 变化时进行请求，并首次不在此请求
        if (this.autoFetch && isSearch && this.domainParams.length !== 0) {
            this.beforeSearch();
        }
    }

    @Watch("domainParams", { immediate: true })
    onDomainParamsChange(newArr: [], oldArr: []) {
        // 首次进入则进行请求
        if (newArr.length !== 0 && (!oldArr || oldArr.length === 0))
            //使用nextTick让Vue组件初始化后再请求，否则子组件的Ref拿不到
            this.$nextTick(() => {
                this.beforeSearch();
                this.$emit("domain-init");
            });
    }

    @Watch("$route", { immediate: true })
    onRouteChange() {
        // 判断普通用量分析还是全站加速用量分析
        this.isDcdn = this.$route.name === "statistics.dcdn";
    }

    // 查询方法
    beforeSearch() {
        // 选择使用了时间则需有数据
        if ((this.useTimePicker || this.useTimeCompare) && !this.searchParams1.startTime) {
            this.$message.error("请选择时间");
            return;
        }
        // 广播给父组件去执行查询
        this.$emit("search", this.searchParams1, this.searchParams2);
    }

    //表格下载
    download() {
        this.$emit("download");
    }
}
</script>

<style lang="scss" scoped>
.statistics-search-bar.search-bar {
    margin-bottom: 16px;
}

.search-row {
    margin: 12px 0;

    > * {
        vertical-align: middle;
    }

    > label {
        margin-right: 16px;
        color: #333333;
    }

    .el-button {
        margin-top: 0;
        margin-bottom: 0;
        height: 24px;
        padding: 4px 8px;
    }

    .slibing-label {
        margin-left: 16px;
    }
}

.code-label {
    margin-right: 16px;
}

.search-download {
    float: right;

    .download-icon {
        padding: 4px 0;

        &:hover {
            color: $color-master-hover;
            cursor: pointer;
        }
    }
}
</style>
