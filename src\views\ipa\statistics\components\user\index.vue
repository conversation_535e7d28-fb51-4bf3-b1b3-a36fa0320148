<template>
    <div class="user-wrapper">
        <!-- 访问用户区域 -->
        <user-area ref="userArea" class="user-area" />

        <!-- 独立 ip (uv) -->
        <user-uv ref="userUv" class="user-uv" />

        <!-- 运营商 -->
        <user-isp ref="userIsp" class="user-isp" />
    </div>
</template>
<script>
import userArea from "./userArea.vue";
import userUv from "./userUv.vue";
import userIsp from "./userIsp.vue";

export default {
    name: "ipa-statistics-user",
    components: { userArea, userUv, userIsp },
    props: {
        instType: {
            type: Number,
            default: 1
        },
        instName: {
            type: String,
            default: ""
        },
    },
    data() {
        return {
            chartRefs: ["userArea", "userUv", "userIsp"],
        };
    },
    methods: {
        // 用户模块不是通过具体的图表组件调用的，而是通过父组件调用的，需要做一次转发
        updateAgency(payload) {
            // 接收父组件传来的参数，并调用各个子组件的更新方法
            this.chartRefs.forEach(ref => {
                this.$refs[ref].updateAgency(payload, ref);
            });
        },
        handleDownload(ref) {
            this.$refs[ref].handleDownloadAgency();
        },
        resetSubComponents() {
            this.chartRefs.forEach(ref => {
                this.$refs[ref]?.resetQuery && this.$refs[ref][0]?.resetQuery();
            });
        },
    },
};
</script>
<style lang="scss" scoped>
// 各子模块
.user-wrapper {
    ::v-deep {
        .user-section {
            margin-bottom: 10px;
            display: inline-block;
            @include g-width(100%, 100%, 50%);
            vertical-align: middle;
        }

        .download-icon {
            padding-left: 12px;
            font-size: 12px;
            vertical-align: middle;

            &:hover {
                color: $color-master-hover;
                cursor: pointer;
            }
        }
    }
}

.user-uv,
.user-isp {
    height: 378px;
}

.user-area {
    min-height: unset !important;
    margin-bottom: 20px;
}
</style>
