<template>
    <div class="ct-descriptions-style">
        <ct-descriptions
            :form="form"
            :defaultData="defaultData"
            :columns="baseInfo"
            :column="2"
            border
        />
    </div>
</template>

<script>
import ctDescriptions from "@/components/ctDescriptions";

export default {
    name: "basicInfo",
    components: { ctDescriptions },
    props: {
        baseInfo: Array,
        defaultData: Object,
    },
    data() {
        return {
            form: {
                a: "ON",
            },
            // baseInfo: [
            //     { label: "CNAME", prop: "cname" },
            //     { label: "加速区域", prop: "a", value: "中国大陆" },
            //     { label: "产品类型", prop: "b", value: "边缘安全与加速" },
            //     { label: "创建时间", prop: "d", value: "2023-09-05 11:41:57" },
            // ],
        };
    },
    computed: {},
    methods: {},
};
</script>

<style scoped lang="scss">
.ct-descriptions-style {
    // width: 100%;
    margin: 20px 20px 0px 20px;
    ::v-deep {
        .el-descriptions .is-bordered {
            table-layout: fixed !important;
        }
    }
}
</style>
