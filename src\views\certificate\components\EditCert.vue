<template>
    <el-dialog
        :title="$t('certificate.edit.title')"
        :close-on-click-modal="false"
        :visible="visibleDialog"
        :before-close="cancel"
        class="add-dialog"
        append-to-body
        width="800px"
    >
        <div class="cert-wrapper">
            <div class="progress-wrapper">
                <el-steps :active="currentActiveStep" class="progress" finish-status="success" align-center>
                    <el-step :title="this.$t('certificate.edit.title')"></el-step>
                    <el-step :title="this.$t('certificate.edit.parseCert')"></el-step>
                </el-steps>
            </div>
            <div class="content-wrapper" v-show="currentActiveStep === 0">
                <el-alert title="" type="info" :closable="false" show-icon style="margin-bottom: 16px">
                    <template slot="title">
                        <div>{{ $t("certificate.update.tip1") }}</div>
                        <div v-html="$t('certificate.update.tip2', { orderLink })"></div>
                    </template>
                </el-alert>
                <el-form
                    :disabled="loading"
                    :rules="addRules"
                    :model="form"
                    ref="editForm"
                    class="add-form"
                    @validate="onValidate"
                    :label-width="isEn ? '220px' : '160px'"
                    :validate-on-rule-change="false"
                >
                    <el-form-item
                        :label="$t('certificate.update.label1')"
                        prop="certName"
                        :class="[isAddMargin ? 'mb-36' : '']"
                    >
                        <el-input :value="form.certName" :placeholder="$t('certificate.text1')" disabled />
                    </el-form-item>
                    <template v-if="isSM2 && visibleDialog">
                        <el-form-item :label="$t('certificate.签名证书公钥')" prop="certs_sign">
                            <!-- 尝试禁用语法检查，避免某些浏览器因为语法检查出现的红下划线 -->
                            <el-input
                                type="textarea"
                                :spellcheck="false"
                                :placeholder="$t('certificate.请输入PEM格式的签名证书公钥内容')"
                                v-model="form.certs_sign"
                            ></el-input>
                            <div class="pem-sample-link">
                                <el-button
                                    type="text"
                                    @click="
                                        pemSampleType = 'sm2PublicKey';
                                        showPemSample = true;
                                    "
                                >
                                    {{ $t("certificate.PEM编码参考样例") }}
                                </el-button>
                            </div>
                        </el-form-item>
                        <el-form-item :label="$t('certificate.签名证书私钥')" prop="key_sign">
                            <el-input
                                type="textarea"
                                :spellcheck="false"
                                :placeholder="$t('certificate.请输入PEM格式的签名证书私钥内容')"
                                v-model="form.key_sign"
                            ></el-input>
                            <div class="pem-sample-link">
                                <el-button
                                    type="text"
                                    @click="
                                        pemSampleType = 'sm2PrivateKey';
                                        showPemSample = true;
                                    "
                                >
                                    {{ $t("certificate.PEM编码参考样例") }}
                                </el-button>
                            </div>
                        </el-form-item>
                    </template>
                    <el-form-item
                        :label="isSM2 ? $t('certificate.加密证书公钥') : $t('domain.create.certs')"
                        prop="certs"
                        v-if="visibleDialog"
                    >
                        <!-- 尝试禁用语法检查，避免某些浏览器因为语法检查出现的红下划线 -->
                        <el-input
                            type="textarea"
                            :spellcheck="false"
                            :placeholder="
                                isSM2
                                    ? $t('certificate.请输入PEM格式的加密证书公钥内容')
                                    : $t('certificate.请输入PEM格式的证书公钥内容')
                            "
                            v-model.trim="form.certs"
                        />
                        <div class="pem-sample-link">
                            <el-button
                                type="text"
                                @click="
                                    pemSampleType = isSM2 ? 'sm2PublicKey' : 'publicKey';
                                    showPemSample = true;
                                "
                            >
                                {{ $t("certificate.PEM编码参考样例") }}
                            </el-button>
                        </div>
                    </el-form-item>
                    <el-form-item
                        :label="isSM2 ? $t('certificate.加密证书私钥') : $t('domain.create.key')"
                        prop="key"
                        v-if="visibleDialog"
                    >
                        <el-input
                            type="textarea"
                            :spellcheck="false"
                            :placeholder="
                                isSM2
                                    ? $t('certificate.请输入PEM格式的加密证书私钥内容')
                                    : $t('certificate.请输入PEM格式的证书私钥内容')
                            "
                            v-model.trim="form.key"
                        />
                        <div class="pem-sample-link">
                            <el-button
                                type="text"
                                @click="
                                    pemSampleType = isSM2 ? 'sm2PrivateKey' : 'privateKey';
                                    showPemSample = true;
                                "
                            >
                                {{ $t("certificate.PEM编码参考样例") }}
                            </el-button>
                        </div>
                    </el-form-item>
                </el-form>
            </div>
        </div>
        <div v-loading="loading" slot="footer">
            <el-button @click="cancel">
                {{ $t("common.dialog.cancel") }}
            </el-button>
            <el-button type="primary" @click="submitEdit">
                {{ $t("domain.batch.next") }}
            </el-button>
        </div>
        <chain-dialog
            :chainVisible="chainVisible"
            :chainDetail="chainDetail"
            @chainEnsure="onChainEnsure"
            @certClose="onCertClose"
            :isAddCert="false"
        ></chain-dialog>

        <cert-info-dialog :certificate="certificate"></cert-info-dialog>

        <pem-sample-dialog
            v-model="showPemSample"
            :show-s-m2="true"
            :sample-type="pemSampleType"
        ></pem-sample-dialog>

        <el-dialog
            :title="$t('certificate.chain.title')"
            :visible.sync="customPromptVisible"
            :close-on-click-modal="false"
            append-to-body
        >
            <h3 class="err-header">
                <i class="el-icon-warning icon-wrapper" />
                <template>
                    {{ customPromptDetail.reason }}
                </template>
            </h3>
            <div slot="footer" class="dialog-footer">
                <el-button @click="submit" type="primary" size="medium">
                    {{ $t("common.dialog.submit") }}
                </el-button>
            </div>
        </el-dialog>
    </el-dialog>
</template>

<script>
import { nUserModule } from "@/store/modules/nuser";
import { formValidate2Promise, getLang } from "@/utils";
import { CertificateUrl } from "@/config/url/certificate";
import ChainDialog from "./ChainDialog.vue";
import store from "@/store";
import { cloneDeep } from "lodash-es";
import { timeFormat } from "@/filters/index";
import CertInfoDialog from "./CertInfoDialog.vue";
import PemSampleDialog from "./PemSampleDialog.vue";
import { Encrypt } from "@/utils/cryptoJS.js";
import { commonLinks } from "@/utils/logic/url";
import { CODE_CERT_ERROR_3001 } from "@/utils/ctFetch/errorConfig";
import { certsRules, getDefaultCertParam } from "../util";

export default {
    name: "CertEditDialog",
    components: { ChainDialog, CertInfoDialog, PemSampleDialog },
    props: {
        certificate: {
            type: Object,
            default: () => ({}),
        },
    },
    data() {
        return {
            certId: "",
            visibleDialog: false,
            currentActiveStep: 0,
            form: getDefaultCertParam(),
            isAddMargin: false,
            loading: false,
            oldForm: {
                // certName: "",
                certs: "",
                key: "",
                certs_sign: "",
                key_sign: "",
            },
            updateDetail: {
                name: "", // 证书备注名
                cn: "", // 证书通用名称
                issuer: "", // 证书品牌
                expires: "", // 过期时间
                validTime: "", // 证书有效期
                addDomainChange: "", // 新增域名变化
                reduceDomainChange: "", // 减少域名变化
            },
            chainVisible: false,
            chainEnsure: false,
            chainError: false,
            chainDetail: { reason: "", data: { metadata: [] } },
            customPromptDetail: { reason: "" },
            customPromptVisible: false,
            showPemSample: false,
            pemSampleType: "publicKey", // 当前显示的样例类型
        };
    },
    computed: {
        isSM2() {
            return this.certificate.algorithm_type === 1;
        },
        isEn() {
            return getLang() === "en";
        },
        orderLink() {
            return commonLinks.orderLink;
        },
        addRules() {
            return {
                certName: [
                    {
                        required: true,
                        message: this.$t("certificate.update.label1Tip1"),
                        trigger: "blur",
                    },
                    {
                        pattern: "^[\\u4e00-\\u9fa5\\w-\\*\\.\\(\\)]+$",
                        message: this.$t("certificate.update.label1Tip2"),
                        trigger: ["blur", "change"],
                    },
                    {
                        max: 255,
                        message: this.$t("certificate.update.label1Tip3"),
                        trigger: ["blur", "change"],
                    },
                ],
                ...certsRules(this.isSM2),
            };
        },
        email() {
            return nUserModule.userInfo.email;
        },
        userName() {
            return nUserModule.userInfo.name;
        },
    },
    watch: {
        // visible: {
        //     handler(val) {
        //         this.visibleDialog = val;
        //     },
        //     deep: true,
        //     immediate: true,
        // },
        certificate: function(val) {
            this.init(cloneDeep(val));
        },
    },
    mounted() {
        this.$ctBus.$on("previous", () => {
            this.currentActiveStep = 0;
        });
        this.$ctBus.$on("certInfoSubmit", () => {
            this.currentActiveStep = 0;
            this.visibleDialog = false;
            this.chainEnsure = true;
            this.submitCertificate();
        });
        // this.certId = this.$route.query?.id || "";
    },

    beforeDestroy() {
        this.$ctBus.$off("previous");
        this.$ctBus.$off("certInfoSubmit");
    },

    methods: {
        onChainEnsure() {
            this.chainVisible = false;
            this.chainEnsure = true;
            this.certCheckDomain();
        },
        // 证书更新授权域名校验接口：用于返回展示：证书备注名、证书通用名称、证书品牌、创建时间、证书有效期、新增域名变化、减少域名变化
        async certCheckDomain() {
            try {
                const data = {
                    certName: this.form.certName,
                    certs: this.form.certs,
                    algorithm_type: 0,
                };
                if (this.isSM2) {
                    data.certs_sign = this.form.certs_sign;
                    data.algorithm_type = 1;
                }
                const rst = await this.$ctFetch(CertificateUrl.certCheckDomain, {
                    method: "POST",
                    body: {
                        data,
                    },
                    headers: {
                        "Content-Type": "application/json",
                    },
                });
                if (rst) {
                    this.currentActiveStep++;
                    this.updateDetail.name = rst?.metadata?.name || "";
                    this.updateDetail.cn = rst?.metadata?.cn || "";
                    this.updateDetail.issuer = rst?.metadata?.issuer || "";
                    this.updateDetail.expires = timeFormat(+rst?.metadata?.expires * 1000);
                    this.updateDetail.validTime = this.transformTime(
                        rst?.metadata?.expires - rst?.metadata?.issue
                    );

                    this.updateDetail.addDomainChange = rst?.addDomainChange || "";
                    this.updateDetail.reduceDomainChange = rst?.reduceDomainChange || "";
                    this.$ctBus.$emit("fromEditCertToCertInfo", this.updateDetail);
                }
            } catch (e) {
                this.loading = false;
                // 展示证书时效性等错误信息
                store.$errorHandler(e);
            }
        },
        async getCertDetail(id) {
            this.loading = true;
            try {
                const rst = await this.$ctFetch(CertificateUrl.getCertById, {
                    data: {
                        certId: id,
                    },
                });
                const currentForm = {
                    certs: rst?.secret?.certs,
                    key: rst?.secret?.key,
                };

                if (this.isSM2) {
                    currentForm.certs_sign = rst?.secret_sign?.certs;
                    currentForm.key_sign = rst?.secret_sign?.key;
                }

                this.oldForm = cloneDeep(currentForm);
            } catch (e) {
                store.$errorHandler(e);
            }
            this.loading = false;
        },
        resetForm() {
            this.form.certs = "";
            this.form.key = "";
            this.form.certs_sign = "";
            this.form.key_sign = "";
        },
        open() {
            this.resetForm();
            this.visibleDialog = true;
        },
        cancel() {
            this.resetForm();
            this.visibleDialog = false;
            // this.$emit("cancel");
        },

        onCertClose() {
            this.chainVisible = false;
        },
        onValidate(prop, valid, msg) {
            if (prop === "certName") {
                if (valid) return (this.isAddMargin = false);
                if (msg.length > 70) this.isAddMargin = true;
            }
        },
        submit() {
            this.visibleDialog = false;
            this.customPromptVisible = false;
            this.currentActiveStep = 0;
            this.$router.push({
                name: "certificate.list",
            });
        },
        async submitEdit() {
            await formValidate2Promise(this.$refs.editForm);
            await this.getCertDetail(this.certificate.id);
            const encrypted = Encrypt(this.form.key);

            // 判断是否和oldForm相同
            if (
                this.form.certs?.trim() === this.oldForm.certs &&
                encrypted === this.oldForm.key &&
                this.isSM2 &&
                this.form.certs_sign?.trim() === this.oldForm.certs_sign &&
                Encrypt(this.form.key_sign) === this.oldForm.key_sign
            ) {
                try {
                    await this.$confirm(
                        this.$t("certificate.detail.tip1"),
                        this.$t("certificate.message.title"),
                        {
                            confirmButtonText: this.$t("common.dialog.submit"),
                            cancelButtonText: this.$t("common.dialog.cancel"),
                            type: "warning",
                        }
                    );
                } catch (err) {
                    return;
                }
            }
            this.chainEnsure = false;
            this.submitCertificate();
        },
        async submitCertificate() {
            if (!this.chainEnsure) {
                this.chainVisible = false;
                this.chainEnsure = false;
                this.chainError = false;
                this.loading = true;
                const data = {
                    certs: this.form.certs,
                    language: this.lang,
                };
                if (this.isSM2) {
                    data.certs_sign = this.form.certs_sign;
                }

                await this.$ctFetch(CertificateUrl.chains, {
                    method: "POST",
                    body: {
                        data,
                    },
                    headers: {
                        "Content-Type": "application/json",
                    },
                })
                    .then(() => {
                        this.chainError = true;
                        this.certCheckDomain();
                    })
                    .catch(e => {
                        this.chainError = true;
                        if (e?.data?.code === CODE_CERT_ERROR_3001) {
                            this.chainVisible = true;
                            this.chainDetail = e?.data || { reason: "", data: { metadata: [] } };
                        } else {
                            store.$errorHandler(e);
                        }
                    });
                this.loading = false;
                if (this.chainError && !this.chainEnsure) return;
            }

            this.loading = true;
            this.chainEnsure = false;

            const data = {
                ...this.form,
                algorithm_type: this.isSM2 ? 1 : 0,
                email: this.email, //新增email字段
                userName: this.userName, //新增不传userName字段
                cert_id: this.certificate.id,
            };
            if (!this.isSM2) {
                delete data.certs_sign;
                delete data.key_sign;
            }

            try {
                await this.$ctFetch(CertificateUrl.updateAndDeploy, {
                    method: "POST",
                    body: {
                        data,
                    },
                    headers: {
                        "Content-Type": "application/json",
                    },
                });
                this.$ctBus.$emit("submitCertificateSuccess");
            } catch (e) {
                this.loading = false;
                // 展示证书时效性等错误信息
                store.$errorHandler(e);
            }
        },
        transformTime(second) {
            const minute = Math.floor(second / 60);
            const space = this.isEn ? " " : "";
            if (minute < 60) return minute + space + this.$t(`common.date.minute${minute > 1 ? "s" : ""}`);
            const hour = Math.floor(minute / 60);
            if (hour < 24) return hour + space + this.$t(`common.date.hour${hour > 1 ? "s" : ""}`);
            const day = Math.floor(hour / 24);
            return day + space + this.$t(`common.date.day${day > 1 ? "s" : ""}`);
        },
        // async deployCert() {
        //     await this.$ctFetch(CertificateUrl.deploys, {
        //         method: "POST",
        //         body: {
        //             data: {
        //                 cert_id: this.certificate.id,
        //             },
        //         },
        //         headers: {
        //             "Content-Type": "application/json",
        //         },
        //     }).catch(e => {
        //         store.$errorHandler(e);
        //     });
        // },
        init(val) {
            if (!val) return;
            this.$nextTick(() => {
                this.form.certName = val?.name;
            });
        },
    },
};
</script>

<style lang="scss" scoped>
.add-dialog {
    ::v-deep a {
        color: #3d73f5;
    }
    .table-scroll-wrap {
        width: 100%;
        display: flex;
        flex-direction: row;
        justify-content: center;
        .cert-wrapper {
            width: 50%;
        }
    }
    .content-wrapper {
        padding: 20px 0;
    }
    .progress-wrapper {
        width: 100%;
        display: flex;
        justify-content: center;
        .progress {
            width: 500px;
        }
    }
    .cert-text-wrapper {
        margin-top: 44px;
        margin-left: 16%;
        ::v-deep {
            .el-form-item.el-form-item--medium,
            .el-form-item.el-form-item--medium .el-form-item__content,
            .el-form-item.el-form-item--medium .el-form-item__label {
                font-size: 12px !important;
                line-height: 16px !important;
            }
        }
    }
    .icon-wrapper {
        color: #ff8f34 !important;
    }
}
</style>
