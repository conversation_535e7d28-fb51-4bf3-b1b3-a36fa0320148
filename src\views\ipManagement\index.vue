<template>
    <ct-section-wrap :headerText="$t('ipManagement.title')" :headerTip="$t('ipManagement.description')">
        <ct-box>
            <el-tabs v-model="activeName">
                <el-tab-pane v-for="(item, key) in tablist" :label="item.label" :key="key" :name="item.value">
                    <component
                        v-if="activeName === item.value"
                        :is="item.component"
                        :is-whitelist-user="isWhitelistUser"
                        :is-whitelist-enable="isWhitelistEnabel"
                        :white-list-limit="whiteListLimit"
                        v-loading="loading"
                    />
                </el-tab-pane>
            </el-tabs>
        </ct-box>
    </ct-section-wrap>
</template>

<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import planList from "./planList.vue";
import notificationConfig from "./notificationConfig.vue";
import sendList from "./sendList.vue";
import { BasicUrl } from "@/config/url/basic";

@Component({
    name: "IpManagement",
    components: {
        planList,
        notificationConfig,
        sendList,
    },
})
export default class IpManagement extends Vue {
    private loading = false;
    private activeName = "0";
    private isWhitelistUser = false;
    private isWhitelistEnabel = false;
    private whiteListLimit = 20;
    private tablist = [
        { label: this.$t("ipManagement.tabTitle[0]"), value: "0", component: "planList" },
        { label: this.$t("ipManagement.tabTitle[1]"), value: "1", component: "notificationConfig" },
        { label: this.$t("ipManagement.tabTitle[2]"), value: "2", component: "sendList" },
    ];

    private async mounted() {
        await this.checkIsWhitelistUserAndEnabel();
    }

    /**
     * 判断是否为白名单用户与是否支持回源白名单配置
     */
    private async checkIsWhitelistUserAndEnabel() {
        this.loading = true;
        // 是否为白名单用户
        const { backoriginWhiteListAccount, batchBackoriginWhiteListLimit } = (await this.$ctFetch(BasicUrl.getConfig, { cache: true })) as any;
        this.isWhitelistUser = backoriginWhiteListAccount;
        this.whiteListLimit = batchBackoriginWhiteListLimit
        // 是否支持回源白名单配置
        const { backoriginWhiteListEnable } = (await this.$ctFetch(BasicUrl.isBilling)) as any;
        this.isWhitelistEnabel = backoriginWhiteListEnable;
    }
}
</script>

<style lang="scss" scoped>
</style>
