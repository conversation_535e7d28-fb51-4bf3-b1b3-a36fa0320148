<template>
    <div>
        <el-radio-group v-model="region">
            <el-radio-button :label="0">{{ $t('statistics.provider.mainland') }}</el-radio-button>
            <el-radio-button :label="1">{{ $t('statistics.provider.overseas') }}</el-radio-button>
        </el-radio-group>
        <el-table :data="dataList" v-loading="loading" :element-loading-text="$t('statistics.common.table.loading')"
            :empty-text="$t('common.table.empty')" @sort-change="setSort">
            <el-table-column :label="$t('statistics.provider.area')" prop="provinceName" />
            <el-table-column :label="$t('statistics.common.tableColumn.peakBandwidth2', { unit: unitConfig.bandwidth.unit })" sortable="custom" prop="bandwidth">
                <template slot-scope="{ row }">
                    {{ (row.bandwidth / unitConfig.bandwidth.scale).toFixed(2) }}
                </template>
            </el-table-column>
            <el-table-column :label="$t('statistics.common.tableColumn.traffic', {unit: unitConfig.flow.unit})" sortable="custom" prop="flow">
                <template slot-scope="{ row }">
                    {{ (row.flow / unitConfig.flow.scale).toFixed(2) }}
                </template>
            </el-table-column>
            <el-table-column :label="$t('statistics.provider.flowPercent')" :prop="region === 0 ? 'mainlandFlowProportion' : 'overseaFlowProportion'" />
            <el-table-column :label="$t('statistics.common.tab[2]') + getTimesUnit()" prop="requestCnt" sortable="custom" />
            <el-table-column :label="$t('statistics.provider.requestPercent')" :prop="region === 0 ? 'mainlandRequestProportion' : 'overseaRequestProportion'" />
        </el-table>
    </div>
</template>

<script lang="ts">
import { Component } from "vue-property-decorator";
import { mixins } from "vue-class-component";
import { StatisticsUsageUrl } from "@/config/url/statistics";
import { SearchParams } from "@/types/statistics/usage";
import { ProviderItem } from "@/types/statistics/usage";
import { StatisticsModule } from "@/store/modules/statistics";
import ChartMixin from "../chartMixin";
import { convertBandwidthB2G, convertFlowB2G, getLang } from "@/utils";
import { exportExcelFile } from "@/utils/logic/excel";
import { timeFormat } from "@/filters/index";

@Component({
    name: "Provider",
})
export default class Provider extends mixins(ChartMixin) {
    private region = 0;
    fetchData: ProviderItem[] = [];
    private sort = "bandwidth"; // 排序字段
    private order = "descending"; // 排序顺序 descending ascending

    // 排序后重新查询
    setSort(data: { prop: string; order: string }) {
        this.sort = data.prop;
        this.order = data.order;

        // 请求数需要传request，而不是requestCnt
        if (this.sort === "requestCnt") this.sort = "request";

        this.beforeGetData(this.searchParams);
    }
    private async localFetchGenerator<T>(url: string, params: SearchParams) {
        const rst = await this.fetchGenerator<T>(url, params)
        return rst;
    }
    // 接口获取数据
    protected async getData(params: SearchParams) {
        let rst = await this.localFetchGenerator<{ list: ProviderItem[] }>(StatisticsUsageUrl.areaDataList, {
            ...params,
            sort: this.sort,
            sortOrder: this.order,
        });

        if (!rst) rst = { list: [] };

        this.downloadDataList = this.fetchData = rst.list || [];
        // 返回结果没有海外则自动切换tab
        if (this.fetchData.length) {
            if (!this.fetchData.some(item => item.overseas === 0)) {
                this.region = 1;
            } else if (!this.fetchData.some(item => item.overseas === 1)) {
                this.region = 0;
            }
        }
    }

    // tab过滤后的数据
    get dataList() {
        return this.fetchData.filter(item => item.overseas === this.region)
    }

    // 获取各种缩进计算的配置（根据最大值获取）
    get unitConfig() {
        // 获取列表中的最大值
        const maxBandwidth = this.getMaxFromList(this.fetchData, "bandwidth");
        const maxFlow = this.getMaxFromList(this.fetchData, "flow");
        // 根据最大值获取缩进单位
        const { unit: bandwidthUnit, _unit: bwOriginUnit } = convertBandwidthB2G(maxBandwidth, this.scale);
        const { unit: flowUnit, _unit: flowOriginUnit } = convertFlowB2G(maxFlow, this.scale);
        // 根据计算的缩进单位指定全局的缩进配置
        return {
            bandwidth: {
                unit: bandwidthUnit,
                scale: this.convertScale.bandwidth[bwOriginUnit],
            },
            flow: {
                unit: flowUnit,
                scale: this.convertScale.flow[flowOriginUnit],
            },
        };
    }

    get isEn() {
        return getLang() === "en";
    }

    // 表格内展现当前运营商
    get ispStrList() {
        return (this.searchParams.isp || [])
            .map(ispCode => {
                const isp = StatisticsModule.ispOptions.find(isp => String(isp.isp_code) === ispCode);
                if (!isp) return "";
                return this.isEn ? isp?.isp_enname : isp?.isp_cnname;
            })
            .filter(Boolean)
            .join(", ");
    }

    // 重写 excel 数据拼接方法
    protected tableToExcel() {
        const sheethead = [];

        sheethead.push([`${this.$t("statistics.provider.statisticsDomain")}:`, `${this.searchParams.domainList!.length === 0 ? this.$t("statistics.provider.all") : this.searchParams.domainList}`]);
        sheethead.push([`${this.$t("statistics.provider.statisticsIsp")}:`, `${this.searchParams.isp!.length === 0 ? this.$t("statistics.provider.all") : this.ispStrList}`]);

        // 空白行
        sheethead.push([]);

        //运营商分布内容
        sheethead.push([`${this.$t("statistics.provider.area")}`, `${this.$t('statistics.common.tableColumn.peakBandwidth2', { unit: this.Mbps })}`, `${this.$t("statistics.common.tableColumn.traffic", { unit: this.MB })}`, `${this.$t("statistics.provider.flowPercent")}`, `${this.$t("statistics.common.tab[2]")}${this.getTimesUnit()}`, `${this.$t("statistics.provider.requestPercent")}`]);

        const sheets = [[...sheethead], [...sheethead]];

        this.fetchData.forEach(item => {
            const row = [
                item.provinceName, 
                (+item.bandwidth / Math.pow(this.scale, 2)).toFixed(2), 
                (+item.flow / Math.pow(this.scale, 2)).toFixed(2), 
                item.overseas === 0 ? item.mainlandFlowProportion : item.overseaFlowProportion,
                item.requestCnt, 
                item.overseas === 0 ? item.mainlandRequestProportion : item.overseaRequestProportion
            ];

            if (item.overseas === 1) {
                sheets[1].push(row);
            } else if (item.overseas === 0) {
                sheets[0].push(row);
            }
        });

        const t1 = timeFormat(+this.searchParams.startTime)
            .replace(/-|\s|:/g, "")
            .slice(0, 12);
        const t2 = timeFormat(+this.searchParams.endTime)
            .replace(/-|\s|:/g, "")
            .slice(0, 12);

        exportExcelFile({
            [`${this.$t('statistics.provider.mainland')}`]: sheets[0],
            [`${this.$t('statistics.provider.overseas2')}`]: sheets[1],
        }, `${this.$t("statistics.provider.providerData")}${t1}-${t2}.xlsx`);
    }
}
</script>

<style lang="scss" scoped></style>
