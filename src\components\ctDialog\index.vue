<template>
    <el-dialog
        :visible.sync="visible"
        :close-on-click-modal="false"
        :show-close="false"
        v-bind="$attrs"
        v-on="$listeners"
        width="500px"
    >
        <!--标题-->
        <template slot="title">
            <slot name="title">
                <div class="flex-row-style el-dialog__title g-dialog-title">
                    <span class="title">{{ title }}</span>
                    <i class="el-icon-close icon-close" @click="handleClose" />
                </div>
            </slot>
        </template>
        <div>
            <slot></slot>
        </div>
        <div v-if="needFooter" slot="footer" class="dialog-footer">
            <slot name="footer">
                <el-button @click="handleClose">{{ cancelText }}</el-button>
                <el-button v-if="showSubmitBtn" type="primary" :loading="loading" @click="onSubmit"
                    >确 定</el-button
                >
            </slot>
        </div>
    </el-dialog>
</template>

<script>
export default {
    name: "index",
    props: {
        visible: {
            type: Boolean,
            required: true,
            default: false,
        },
        title: {
            type: String,
            required: false,
            default: "",
        },
        loading: {
            type: Boolean,
            default: false,
        },
        needFooter: {
            type: Boolean,
            default: true,
        },
        cancelText: {
            type: String,
            default: "取 消",
        },
        showSubmitBtn: {
            type: Boolean,
            default: true,
        },
    },
    methods: {
        /**
         * 提交
         */
        onSubmit() {
            this.$emit("submit");
        },
        /**
         * 关闭
         */
        handleClose() {
            this.$emit("close", false);
        },
    },
};
</script>

<style scoped lang="scss">
::v-deep {
    .el-dialog {
        .g-dialog-title {
            display: flex;
            justify-content: space-between;
        }
        .el-dialog__header {
            border-bottom: 1px solid #e2e5ed;
            padding: 16px 32px;
        }

        .el-dialog__body {
            padding: 24px 32px;
        }

        .el-dialog__footer {
            background-color: #f7f8fa;
            padding: 12px 32px;
            border-radius: 0 0 3px 3px;

            .dialog-footer {
                background: unset;
            }
        }

        .icon-close {
            cursor: pointer;
        }
    }
}
</style>
