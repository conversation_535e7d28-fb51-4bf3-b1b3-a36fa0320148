{
    "compilerOptions": {
        "noImplicitThis": true, // 忽略 this 的类型检查
        "target": "esnext",
        "module": "esnext",
        "strict": true, // 开启严格模式解析
        "jsx": "preserve",
        "importHelpers": true, // 从 tslib 导入外部帮助库: 比如__extends，__rest等
        "moduleResolution": "node", // 模块解析策略，使用 node 标准
        "experimentalDecorators": true, // 允许使用实验性的 Decorator 装饰器（尚未成为标准）
        "allowJs": true,
        "skipLibCheck": true,
        "esModuleInterop": true,
        "allowSyntheticDefaultImports": true, // 允许从没有设置默认导出的模块中默认导入
        "sourceMap": true,
        "types": [
            "webpack-env" // 默认引入了该类型声明，即 @types/webpack-env
        ],
        "lib": [    // 编译过程中需要引入的库文件的列表
            "esnext",
            "dom",
            "dom.iterable",
            "scripthost"
        ],
        "baseUrl": "./",
        "paths": {
            "@/*": [
                "src/*"
            ],
        },
        "resolveJsonModule":true // 允许json导入
    },
    "include": [
        "src/**/*.ts",
        "src/**/*.tsx",
        "src/**/*.vue",
        "scripts/*.ts",
        "types/*.ts",
        "tests/**/*.spec.ts"
    ],
    "exclude": [
        "**/node_modules"
    ]
}
