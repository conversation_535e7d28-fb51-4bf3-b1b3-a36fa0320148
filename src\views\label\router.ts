import { RouteConfig } from "vue-router";
import { DomainModule } from "@/store/modules/domain";
import { DomainActionEnum, getDomainAction } from "@/config/map";
import { nUserModule } from "@/store/modules/nuser";

const labelRouter: RouteConfig[] = [
    {
        path: "/label",
        name: "label",
        component: () => import("./list/index.vue"),
        meta: {
            breadcrumb: {
                title: "$t('label.title')",
                route: ["ndomain", "label"],
            },
            perm: "nlabel",
        },
        beforeEnter(to, from, next) {
            // 不阻塞页面加载
            const isFcdnCtyunCtclouds = nUserModule.isFcdnCtyunCtclouds;
            isFcdnCtyunCtclouds && DomainModule.GetDomainList({ action: getDomainAction("Label") });
            next();
        },
    },
];

export default labelRouter;
