<template>
    <div class="ja3-fingerprint-wrapper">
        <el-form
            ref="ja3Form"
            label-width="140px"
            :model="ja3Form"
            :disabled="!isEdit || !isService || isLocked"
        >
            <el-form-item
                :label="$t('domain.detail.ja3_fingerprint_log')"
                class="dynamic-box"
                prop="ssl_ja3_enable"
            >
                <span slot="label"
                    >{{ $t("domain.detail.ja3_fingerprint_log") }}
                    <span>
                        <el-tooltip placement="top">
                            <div slot="content">
                                <span>{{ $t("domain.detail.ja3_fingerprint_tip2") }}</span>
                                <a
                                    class="word-wrap aocdn-ignore-link"
                                    style="color:#3d73f5"
                                    @click="handleSecurityClick"
                                >
                                    {{ $t("domain.detail.ja3_fingerprint_tip2-1") }}
                                </a>
                                <span>{{ $t("domain.detail.ja3_fingerprint_tip2-2") }}</span>
                            </div>
                            <ct-svg-icon
                                icon-class="question-circle"
                                class-name="ct-sort-drag-icon"
                            ></ct-svg-icon>
                        </el-tooltip>
                    </span>
                </span>
                <el-switch
                    v-model="ja3Form.ssl_ja3_enable"
                    active-value="on"
                    inactive-value="off"
                    @change="onChange"
                    :disabled="isLocked"
                ></el-switch>
                <span class="tips">{{ $t("domain.detail.ja3_fingerprint_tip") }}</span>
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
import componentMixin from "@/views/domainConfig/componentMixin";
import ctSvgIcon from "@/components/ctSvgIcon";
import actions from "@/microApp/actions";
import { get } from "lodash-es";

export default {
    mixins: [componentMixin],
    components: {
        ctSvgIcon,
    },
    props: {
        formDatas: {
            type: Object,
            default: () => ({}),
        },
        isLocked: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            securityLink:
                "#/securityAbility/controlLimitConfig?currentTab=accessControl&productType=security",
            ja3Form: {
                ssl_ja3_enable: null,
            },
        };
    },
    watch: {
        formDatas: {
            deep: true,
            handler(val) {
                this.ja3Form.ssl_ja3_enable = val.ssl_ja3_enable;
            },
            immediate: true,
        },
    },
    methods: {
        onChange() {
            this.$emit("onChange", this.ja3Form);
        },
        handleSecurityClick(e) {
            const workspaceId = this.$route.query.workspaceId;
            // 构建完整URL
            const fullUrl = `#/securityAbility/controlLimit?currentTab=accessControl&productType=security&workspaceId=${workspaceId}`;
            // 在新窗口打开
            window.open(fullUrl, "_blank");
        },
    },
};
</script>

<style lang="scss" scoped>
.ja3-fingerprint-wrapper {
    .dynamic-box {
        .tips {
            font-size: 12px;
            color: $color-neutral-7;
            font-weight: 400;
            margin-left: $margin-3x;
        }
    }
}
</style>
