import Vue from "vue";
import Vuex from "vuex";
import { Route } from "vue-router";
import { Message } from "element-ui";
import { errorHandler } from "@/utils/ctFetch/errorHandler";
import { UserState } from "./modules/nuser";
import { ScreenState } from "./modules/screen";
import { MenuState, ProductState, OperationState, StatisticsState } from "./types";
import { ScaleState } from "./modules/scale";
import dictionaryModules from "./modules/dictionary.js";

Vue.use(Vuex);

export interface RootState {
    route: Route; // vuex-router-sync 自动添加
    user: UserState;
    screen: ScreenState;
    menu: MenuState;
    product: ProductState;
    operation: OperationState;
    scale: ScaleState;
    statistics: StatisticsState;
}

// 先声明的是空 store ，后面会动态注册所有模块
const store = new Vuex.Store<RootState>({});

// 提供报错信息展示功能
(store as any).$message = Message;
(store as any).$errorHandler = errorHandler;
store.registerModule("dictionary", dictionaryModules);

export default store;