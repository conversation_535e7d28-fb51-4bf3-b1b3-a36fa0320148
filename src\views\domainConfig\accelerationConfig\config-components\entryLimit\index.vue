<template>
    <div class="ct-edit-wrapper">
        <el-form
            label-width="140px"
            label-position="left"
            :model="form"
            :rules="rules"
            ref="cacheKeyArgsForm"
            :disabled="!isEdit || !isService || lock"
        >
            <el-form-item
                :label="$t('domain.entryLimit.IP访问限频')"
                prop="entry_limits"
                class="cache-table-style"
            >
                <template #label>
                    <span>{{ $t("domain.entryLimit.IP访问限频") }}</span>
                    <span>
                        <el-tooltip :content="$t('domain.entryLimit.tips')" placement="top">
                            <ct-svg-icon
                                icon-class="question-circle"
                                class-name="ct-sort-drag-icon"
                                style="font-size: 12px; margin-left: 4px;"
                            ></ct-svg-icon>
                        </el-tooltip>
                    </span>
                </template>
                <div class="ct-table-wrapper">
                    <el-table :data="form.entry_limits">
                        <el-table-column prop="mode" :label="$t('domain.type')">
                            <template #default="{ row }">{{ conditionModeMap[row.mode] }}</template>
                        </el-table-column>
                        <el-table-column prop="content" :label="$t('domain.content')" />
                        <el-table-column
                            prop="frequency_threshold"
                            :label="`${$t('domain.entryLimit.访问阈值')}(${$t('domain.entryLimit.次/秒')})`"
                        />
                        <el-table-column prop="priority" :label="$t('domain.detail.label48')" />
                        <el-table-column :label="$t('domain.operate')" width="120">
                            <template #default="{ row, $index }">
                                <el-button
                                    type="text"
                                    :disabled="!isEdit || !isService || lock"
                                    @click="handleOperate(row, 'edit', $index)"
                                    >{{ $t("domain.modify") }}</el-button
                                >
                                <el-button
                                    type="text"
                                    :disabled="!isEdit || !isService || lock"
                                    @click="handleOperate(row, 'delete', $index)"
                                    >{{ $t("domain.delete") }}</el-button
                                >
                            </template>
                        </el-table-column>
                    </el-table>
                    <div class="flex-row-style button-box">
                        <el-button
                            class="btn"
                            type="text"
                            :disabled="!isEdit || !isService || lock"
                            @click="handleOperate(null, 'create')"
                        >
                            + {{ $t("domain.editPage.label10") }}
                        </el-button>
                    </div>
                </div>
            </el-form-item>
        </el-form>

        <entry-limit-dialog
            :dialog-info="dialogInfo"
            @cancel="dialogInfo.visible = false"
            @submit="handleDialogConfirm"
        />
    </div>
</template>

<script lang="ts">
import { Component, Prop, Watch, Mixins } from "vue-property-decorator";
import { cloneDeep } from "lodash-es";
import { conditionModeMap } from "@/components/commonCondition/dict";
import ComponentMixinTyped from "@/views/domainConfig/mixins/componentMixin";
import { EntryLimitItem, getEntryLimitDefaultItem } from "./util";
import EntryLimitDialog from "./dialog.vue";
import ctSvgIcon from "@/components/ctSvgIcon/index.vue";

@Component({
    components: { EntryLimitDialog, ctSvgIcon },
    model: {
        prop: "value",
        event: "input",
    },
})
export default class EntryLimit extends Mixins(ComponentMixinTyped) {
    @Prop({ type: Array, default: () => [] })
    private value!: EntryLimitItem[];
    @Prop({ type: Boolean, default: false })
    private lock!: boolean;
    conditionModeMap = conditionModeMap as Record<number, string>;
    private form: {
        entry_limits: EntryLimitItem[];
    } = {
        entry_limits: [],
    };
    private rules = {};
    private dialogInfo: {
        visible: boolean;
        type: string;
        form: EntryLimitItem;
        list: EntryLimitItem[];
        currentIndex: number;
    } = {
        visible: false,
        type: "create",
        form: getEntryLimitDefaultItem(),
        list: [],
        currentIndex: 0,
    };

    @Watch("value", { immediate: true })
    onInit() {
        if (!this.value) return;
        this.form.entry_limits = cloneDeep(this.value);
    }

    async handleOperate(row: EntryLimitItem | null, type: string, index = 0) {
        if (type === "delete") {
            // 二次确认弹窗
            await this.$confirm(
                this.$t("domain.editPage.tip20") as string,
                this.$t("domain.delete") as string,
                {
                    type: "warning",
                }
            );
            this.form.entry_limits.splice(index, 1);
            this.$emit("input", this.form.entry_limits);
            return;
        }
        let rowData = null;
        if (type === "create") {
            rowData = getEntryLimitDefaultItem();
            this.dialogInfo.type = "create";
        }

        if (type === "edit") {
            rowData = {
                ...getEntryLimitDefaultItem(),
                ...row,
            };
            this.dialogInfo.type = "edit";
            this.dialogInfo.currentIndex = index;
        }
        this.dialogInfo.visible = true;
        this.dialogInfo.form = rowData || getEntryLimitDefaultItem();
        this.dialogInfo.list = cloneDeep(this.form.entry_limits);
        this.dialogInfo.currentIndex = index;
    }

    handleDialogConfirm(val: EntryLimitItem) {
        this.dialogInfo.visible = false;
        if (this.dialogInfo.type === "create") {
            this.form.entry_limits.push(val);
        } else {
            this.form.entry_limits.splice(this.dialogInfo.currentIndex, 1, val);
        }
        this.$emit("input", this.form.entry_limits);
    }
}
</script>

<style lang="scss" scoped>
@import "@/views/domainConfig/commonStyle.scss";
.ct-edit-wrapper {
    ::v-deep {
        .el-form--label-left .el-form-item__label {
            text-align: right;
        }
    }
}
</style>
