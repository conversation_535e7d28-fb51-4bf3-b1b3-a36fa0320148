import $https from "@/utils/request";
import { ROUTE_PREFI } from "@/config/url/_PREFIX";

// const preFix = "/mock";
const preFix = ROUTE_PREFI;

// 字典管理
export function getDictionary(params) {
    return $https({
        url: `${preFix}/ctaccessone/dictionary/queryDict`,
        method: "get",
        params,
    });
}

// 任务总开关操作
export function commonUpdateMonitorAct(params) {
    return $https({
        url: `${preFix}/aocdn/common/updateMonitorAct`,
        params,
    });
}

// 任务检测开关操作
export function commonUpdateTaskAct(params) {
    return $https({
        url: `${preFix}/aocdn/common/updateTaskAct`,
        params,
    });
}

// 监测任务删除
export function commonTaskDelete(params) {
    return $https({
        url: `${preFix}/aocdn/common/taskDel`,
        params,
    });
}

// 监测区域获取接口
export function getMonitorAreas(params) {
    return $https({
        url: `${preFix}/aocdn/common/getMonitorAreas`,
        params,
        method: "get",
    });
}

// 获取客户下所有域名
export function getAllDomain() {
    return $https({
        url: `${preFix}/aocdn/common/getAllDomain`,
        method: "get",
    });
}

// 获取客户下已启用的域名
export function getEnableDomain() {
    return $https({
        url: `${preFix}/aocdn/common/getEnableDomain`,
        method: "get",
    });
}

// 敏感词获取接口
export function getSensitiveWords() {
    return $https({
        url: `${preFix}/aocdn/common/getSensitiveWords`,
        method: "get",
    });
}

// 获取客户购买版本配置信息
export function getVersionConfigs() {
    return $https({
        url: `${preFix}/aocdn/common/getVersionConfigs`,
    });
}

// 预校验检测任务数是否达到版本上限
export function validateTaskCountLimit(params) {
    return $https({
        url: `${preFix}/aocdn/common/validateTaskCountLimit`,
        params,
    });
}

// 监测任务下拉框
export function selectorCheckTask(params) {
    return $https({
        url: `${preFix}/aocdn/selector/checkTask`,
        params,
    });
}
