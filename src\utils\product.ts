import i18n from "@/i18n";

/**
 * 是否为静态类产品
 *
 * 静态类：下载加速、静态加速、cdn加速、下载闲时、点播
 */
export function isStaticProduct(product: string) {
    return ["001", "003", "008", "014", "004"].includes(product);
}

/**
 * 底座类型映射
 */
export const gatewayTypeMap = {
    STATIC: 1, // 静态底座
    WHOLE: 6, // 全站底座
};

/**
 * 回源模式映射
 */
export const backoriginModeMap = {
    ATS: 0, // ATS回源
    GATEWAY: 1, // 网关回源
    ATS_and_GATEWAY: 2, // ATS回源&网关回源
};

export const getGatewayType = (product: string, gateway_type: number | undefined) => {
    // 产品类型为6，没有这个字段默认gateway_type=6,产品类型不等于6，没有这个字段默认gateway_type=1

    if (Object.values(gatewayTypeMap).includes(gateway_type as number)) {
        return gateway_type;
    }

    if (product === "006") {
        return gatewayTypeMap.WHOLE;
    }

    return gatewayTypeMap.STATIC;
};

/**
 * 检查域名是否以特殊后缀结尾
 * 该函数用于判断给定域名是否以(suffixList中的一项)特定后缀结尾
 * 
 * @param domain 域名字符串
 * @param suffixList 后缀列表，包含可能的后缀字符串数组
 * @returns 如果域名以suffixList中的一个后缀结尾，则返回true；否则返回false
 */
export const domainEndsWithSpecialSuffix = (domain: string, suffixList: string[]) => {
    if (suffixList.every(suffix => suffix === "" || suffix === undefined || suffix === null)) {
        return false;
    }

    return suffixList
        .filter(v => !!v)
        .map(suffix => suffix?.split(",") || [])
        .map(suffixItems => suffixItems?.some(item => domain?.endsWith(item)))
        .includes(true);
};

/**
 * 将产品编码转换为产品名称
 * 
 * 本函数通过产品代码查找对应的国际化产品名称。
 * 
 * @param productCode 产品代码，一个唯一标识产品的字符串
 * @returns 返回对应产品代码的产品名称，如果找不到对应的产品代码，则返回空字符串
 */
export const productCodeToName = (productCode: string) => {
    let label = "";
    switch (productCode) {
        case "001":
            label = i18n.t("domain.detail.label98") as string;
            break;
        case "003":
            label = i18n.t("domain.detail.label99") as string;
            break;
        case "004":
            label = i18n.t("domain.detail.label100") as string;
            break;
        case "005":
            label = i18n.t("domain.detail.label101") as string;
            break;
        case "006":
            label = i18n.t("domain.detail.label102") as string;
            break;
        case "007":
            label = i18n.t("domain.detail.label103") as string;
            break;
        case "008":
            label = i18n.t("domain.detail.label104") as string;
            break;
        case "009":
            label = i18n.t("domain.detail.label105") as string;
            break;
        case "010":
            label = i18n.t("domain.detail.label106") as string;
            break;
        case "011":
            label = i18n.t("domain.detail.label107") as string;
            break;
        case "012":
            label = i18n.t("domain.detail.label108") as string;
            break;
        case "013":
            label = i18n.t("domain.detail.label109") as string;
            break;
        case "014":
            label = i18n.t("domain.detail.label110") as string;
            break;
        case "015":
            label = i18n.t("domain.detail.label111") as string;
            break;
        case "018":
            label = i18n.t("domain.detail.label114") as string;
            break;
        case "019":
            label = i18n.t("domain.detail.label115") as string;
            break;
        case "020":
            label = i18n.t("domain.detail.label116") as string;
            break;
        case "021":
            label = i18n.t("domain.detail.label117") as string;
            break;
        case "022":
            label = i18n.t("domain.detail.label118") as string;
            break;
        case "024":
            label = i18n.t("domain.detail.label119") as string;
            break;
    }
    return label;
}
