<template>
    <div></div>
</template>

<script>
import <PERSON><PERSON> from "element-ui/src/utils/vue-popper";

export default {
    name: "popper",
    mixins: [Popper],
    methods: {
        updateDom(dom) {
            if (this.popperJS) {
                this.popperJS._reference = dom;
                this.updatePopper();
            } else {
                this.updatePopper();
            }
        },
    },
};
</script>

<style scoped></style>
