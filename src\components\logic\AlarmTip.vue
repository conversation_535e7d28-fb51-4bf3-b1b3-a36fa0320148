<template>
    <el-alert v-if="showTip" class="alarm-tip" type="warning" @close="onTipClose" show-icon>
        <div slot="title">
            <i18n :path="tipPath">
                <a
                    class="aocdn-ignore-link"
                    @click="$docHelp('https://www.ctyun.cn/document/10015932/10136729')"
                    >{{ $t("common.alarmTip.tip2") }}</a
                >
            </i18n>
        </div>
    </el-alert>
</template>

<script lang="ts">
import { CdnConfigModule } from "@/store/modules/cdnConfig";
import { nUserModule } from "@/store/modules/nuser";
import { Component, Vue } from "vue-property-decorator";

@Component({
    name: "AlarmTip",
})
export default class AlarmTip extends Vue {
    get tipPath() {
        return this.$route.name === 'nhome' ? 'common.alarmTip.tip1' : 'common.alarmTip.tip3';
    }
    get showTip() {
        if (!nUserModule.isCtyun && !nUserModule.isVip) return false;
        if (!["nhome", "ndomain.list"].includes(this.$route.name || "")) return false;

        const map = {
            nhome: CdnConfigModule.showAlarm.home,
            "ndomain.list": CdnConfigModule.showAlarm.domainList,
        };

        return !map[this.$route.name as keyof typeof map];
    }

    public onTipClose() {
        CdnConfigModule.SET_SHOW_ALARM({
            key: this.$route.name === "nhome" ? "home" : "domainList",
            value: true,
        });
    }
}
</script>
<style scoped lang="scss">
.alarm-tip {
    padding: 0 $common-space-5x;
    padding-top: $common-space-5x;
}
</style>
