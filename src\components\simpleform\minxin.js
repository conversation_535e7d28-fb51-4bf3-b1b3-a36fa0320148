import { ProductCodeMap } from "@/store/config/product";
import { nBasicUrl } from "@/config/url/ncdn/ndomain.ts";
import { ctFetch } from "@cdnplus/common/utils";
import { getI18nLabel } from "@/store/modules/ncdn/nproduct";

export default {
    data() {
        return {
            isDetail: false,
            isEdit: false,
            isRecreate: false,
            product_type_list: [],
            originForm: {
                origin: "",
                roleLevel: "master1", // role + level 组合值
                weight: "10",
                protocol: "http",
            },
            codeMap: [],
            domainTypeMap: [],
            originProtocolOption: [
                {
                    displayValue: "HTTP",
                    note: this.$t("simpleForm.mixin.Note"),
                    value: "http",
                },
                {
                    displayValue: "HTTPS",
                    note: this.$t("simpleForm.mixin.Note"),
                    value: "https",
                },
                {
                    displayValue: this.$t("simpleForm.mixin.follow"),
                    note: this.$t("simpleForm.mixin.Note"),
                    value: "follow_request",
                },
            ],
            // 回源加密算法下拉框
            proxyGmsslModeList: [
                {
                    displayValue: this.$t("domain.create.proxyGmsslModeList.0"),
                    value: "on",
                },
                {
                    displayValue: this.$t("domain.create.proxyGmsslModeList.1"),
                    value: "off",
                },
                {
                    displayValue: this.$t("domain.create.proxyGmsslModeList.2"),
                    value: "follow",
                },
            ],
            // 动态回源策略下拉选项
            originPolicyOption: [
                {
                    displayValue: this.$t("domain.detail.originType.0"),
                    value: "fastest_simple",
                },
                {
                    displayValue: this.$t("domain.detail.originType.1"),
                    value: "poll",
                },
                {
                    displayValue: this.$t("domain.detail.originType.2"),
                    value: "keep_hash",
                },
            ],
        };
    },
    // inject: {
    //     isCreate: { default: () => false }, // 是否为新增表单
    //     isDetail: { default: () => false }, // 是否为查看表单
    //     isEdit: { default: () => false }, // 是否为修改表单
    //     isRecreate: { default: () => false }, // 是否为重新发起表单
    // },
    methods: {
        /**
         * 验证主机域名的格式是否有效
         *
         * 此函数主要用来校验输入的主机域名是否符合一定的格式规则
         * 规则包括但不限于域名长度、字符类型以及顶级域名的合法性
         *
         * @param {Object} rule 验证规则对象，通常由验证框架提供
         * @param {string} value 用户输入的域名，将被验证的值
         * @param {function} callback 回调函数，验证完成后必须调用以通知结果
         */
        hostValidate(rule, value, callback) {
            // 创建一个正则表达式对象，用于验证域名格式
            // 域名长度必须在3到255个字符之间
            // 域名部分由英文字母或数字组成，可以包含连字符(-)和下划线(_)
            // 最后一部分是2到6个字母组成的顶级域名
            const rtgValidator = new RegExp(
                "^(?=^.{3,255}$)([a-zA-Z0-9][-_a-zA-Z0-9]{0,62}.)+[a-zA-Z]{2,6}$"
            );

            // 当输入了域名且不符合正则表达式规则时，通过callback返回错误
            if (value && !rtgValidator.test(value)) {
                callback(new Error(this.$t("domain.create.tip19")));
                return;
            }

            // 如果输入的域名符合规则或者没有输入，则执行回调无错误
            callback();
        },
        listProcess(listTmp) {
            const list = listTmp || [];
            // 1. 排序
            const sortedList = list.sort(
                (a, b) => parseInt(a.displayOrder, 10) - parseInt(b.displayOrder, 10)
            );
            // 2. 不展示offline。展示的有online和disable
            const showList = sortedList.filter(item => item.state !== "offline");
            // 3. 把disable专门拿出来做一个字段disabled
            const listWithDisabled = showList.map(item => ({
                ...item,
                disabled: item.state === "disable",
            }));
            return listWithDisabled;
        },
        async queryProp(prop) {
            let rst = {};
            const data = {
                prop,
            };
            rst = await ctFetch(nBasicUrl.getProp, {
                data,
            });

            if (rst) {
                if (prop === "product_type") this.product_type_list = [...rst];
                if (prop === "domain_type") {
                    rst.forEach(item => {
                        let obj = {};
                        if (ProductCodeMap[item.id]) {
                            obj = {
                                id: item.id,
                                displayValue: getI18nLabel(item.id),
                            };
                        }
                        this.domainTypeMap.push(obj);
                    });
                }
            }
        },
        async renderDomainTypeOptions() {
            await this.queryProp("product_type");
            const lists = this.listProcess(this.product_type_list);
            await this.queryProp("domain_type");
            lists.map(item => {
                // 过滤直播
                if (item.id !== "005" && ProductCodeMap[item.id]) {
                    this.codeMap.push({
                        label: getI18nLabel(item.id),
                        value: item.id,
                        disabled: item.disabled,
                    });
                }
            });
        },
    },
    mounted() {
        // 判断当前是否为创建页面
        this.isCreate =
            !this.$route.query.domainId &&
            !this.$route.query.orderId &&
            this.$route.name === "ndomain.create";
        // 判断当前是否为详情页面
        this.isDetail = this.$route.query.opType === "view" || this.from === "order";
        // 判断当前是否为编辑页面
        this.isEdit = this.$route.query.domainId && this.$route.query.opType !== "view";
        // 判断当前是否为重建页面
        this.isRecreate = !!this.$route.query.orderId;
    },
};
