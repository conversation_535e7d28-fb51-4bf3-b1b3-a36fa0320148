.time-picker {
    .picker-item {
        height: 32px;
        line-height: 32px;
        padding: 0 22px;
        white-space: nowrap;
        border-style: solid;
        border-color: rgba(187, 192, 202, 1);
        border-width: 1px 0 1px 1px;
        cursor: pointer;
        color: rgba(76, 89, 109, 0.5);
        background-color: white;

        &.active {
            color: #3d73f5;
            border: 1px solid #3d73f5;
        }
    }

    & > .picker-item:first-child {
        border-radius: 4px 0 0 4px;
    }

    & > .picker-item:last-child {
        border-right-width: 1px;
        border-radius: 0 4px 4px 0;
    }

    & > .length-sort:last-child {
        border-radius: unset;
    }

    ::v-deep .el-date-editor {
        &.el-range-editor {
            border-color: #bbc0ca;
            border-radius: 0 4px 4px 0;
            margin-top: unset;
        }

        &.is-active {
            border-color: #FF9831;
        }
    }
}
