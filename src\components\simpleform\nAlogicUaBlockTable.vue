<template>
    <div>
        <div class="radio-row">
            <el-radio-group v-model="objectModel.type">
                <el-radio :label="1">{{ $t("simpleForm.blockTable.allow") }}</el-radio>
                <el-radio :label="0">{{ $t("simpleForm.blockTable.block") }}</el-radio>
            </el-radio-group>
        </div>
        <div>
            <el-input
                v-model="objectModel.ua"
                class="textarea-content"
                type="textarea"
                :rows="7"
                :placeholder="placeholder"
            />
        </div>
    </div>
</template>

<script>
// import mixin from "../../editor.mixin";
export default {
    // mixins: [mixin],
    name: "n-alogic-ua-block-table",
    props: {
        uaData: Object,
    },
    data() {
        return {
            objectModel: {
                type: 1,
                ua: "",
            },
            maxNum: 400,
        };
    },
    computed: {
        placeholder() {
            return this.$t("domain.detail.tip41");
        },
        uaList() {
            // 需要过滤空表内容
            return this.objectModel.ua
                .split("\n")
                .map(i => i.trim())
                .filter(i => i);
        },
    },
    methods: {
        validateProcedure() {
            const result = {
                valid: true,
                msg: "",
                dom: "ua_type",
            };
            const { uaList, maxNum, objectModel } = this;

            if (objectModel.type === null && objectModel.ua) {
                result.valid = false;
                result.msg = this.$t("domain.detail.tip42");
            } else if (!objectModel.ua.trim()) {
                result.valid = false;
                result.msg = this.$t("domain.detail.tip49");
            } else if (uaList.length > maxNum) {
                result.valid = false;
                result.msg = this.$t("simpleForm.UaBlock.maxNum", { maxNum });
            }

            return Promise.resolve(result);
        },
    },
    watch: {
        objectModel: {
            handler(val) {
                if (val.ua === "" && val.type === null) {
                    this.model = "";
                } else {
                    this.model = {
                        type: val.type,
                        ua: this.uaList.join(","),
                    };
                    this.$emit("onChange", this.model);
                }
            },
            deep: true,
        },
    },
    mounted() {
        if (this.uaData) {
            this.objectModel = {
                type: this.uaData.type || 1,
                ua: this.uaData.ua.split(",").join("\n"),
            };
        }
    },
};
</script>

<style lang="scss" scoped>
.textarea-content {
    width: 70%;
}
</style>
