import { PROXY_PREFIX, SYS_ROUTE_PREFIX, NEW_PREFIX } from "./_PREFIX";
// 登出接口
export const LogoutUrl = SYS_ROUTE_PREFIX + `/ctyun/logout`;

// 下拉框基础数据
export const BasicUrl = {
    // 产品列表接口
    productList: PROXY_PREFIX + "/v1/basic/product",
    // 区域列表接口
    areaList: PROXY_PREFIX + "/v1/basic/ListArea",
    // 获取省份列表信息
    provinceList: PROXY_PREFIX + "/v1/basic/province",
    // 运营商列表接口
    ispList: PROXY_PREFIX + "/v1/basic/ListIsp",
    // 套餐校验
    isBilling: PROXY_PREFIX + "/v1/basic/isBilling",
    // 获取配置参数
    getConfig: PROXY_PREFIX + "/v1/basic/config",
    // 边缘函数套餐确认
    checkEdgeFunction: PROXY_PREFIX + "/v1/udf/edgeFunction/billings",
};
// 客户进制配置
export const ScaleUrl = PROXY_PREFIX + "/v1/billing/GetScale";
