.message-header {
    position: relative;

    .message-header-right {
        position: absolute;
        right: 0;
    }
}

.body-main {
    padding: 30px 10px;
}

::v-deep .el-tabs__header {
    margin-bottom: unset;
}

::v-deep .el-badge__content.is-dot {
    z-index: 1000;
    right: 0 !important;
}

.message-row {
    display: flex;
    flex-direction: row;

    .line {
        width: 16px;
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
        margin-right: 20px;
        padding-top: 5px;

        .circle {
            width: 12px;
            height: 12px;
            border-radius: 12px;
            background: rgb(228, 231, 237);
            display: inline-block;
            z-index: 10;
        }

        .lin-style {
            width: 2px;
            height: 100%;
            background: rgb(228, 231, 237);
            position: absolute;
        }
    }

    .message-content {
        flex: 1;

        .content-label {
            color: #333;
            font-size: 16px;
            line-height: 25px;
            margin-bottom: 10px;
        }

        .message-line {
            align-items: flex-start;
            justify-content: space-between;
            padding: 7px 0;
            margin-bottom: 10px;
            color: #0a1534;

            &.read {
                color: #788296;
            }

            &:hover {
                color: $theme-color;
            }

            &.active {
                color: $theme-color;
            }

            .content-title {
                line-height: 22px;
                padding: 0 32px;
                //color: rgb(102, 102, 102);
                font-size: 14px;
                cursor: pointer;
            }

            .content-time {
                //color: #999;
                line-height: 22px;
                font-size: 14px;
            }
        }
    }
}

.back-header {
    width: 100%;
    height: 60px;
    border-bottom: 1px solid #E4E7ED;
}

.detail-box {
    color: #333;
}

.detail-title {
    color: rgba(0, 0, 0, 0.85);
    font-size: 18px;
    margin-top: 20px;
}

.sub-title {
    color: #999;
    padding-top: 8px;
    font-size: 14px;
}

.detail-content {
    color: #999;
}
