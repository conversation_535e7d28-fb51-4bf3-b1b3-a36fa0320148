/**
 * 设置cookie
 * @param name
 * @param value
 */
export function setCookie(name, value, time, path = "/") {
    const arr = [name + "=" + escape(value)];
    if (time) {
        const exp = new Date();
        exp.setTime(exp.getTime() + time);
        arr.push("expires=" + exp.toGMTString());
    }
    arr.push("path=" + path);
    document.cookie = arr.join(";");
}

/**
 * 获取cookie
 * @param name
 * @returns {string|null}
 */
export function getCookie(name) {
    let arr = null;
    const reg = new RegExp("(^| )" + name + "=([^;]*)(;|$)");
    if ((arr = document.cookie.match(reg))) {
        return unescape(arr[2]);
    } else {
        return null;
    }
}

/**
 * 删除cookie
 * @param name
 */
export function delCookie(name, path = "/") {
    const exp = new Date();
    exp.setTime(exp.getTime() - 1);
    const cval = getCookie(name);
    if (cval !== null) {
        document.cookie = name + "=" + cval + ";expires=" + exp.toGMTString() + ";path=" + path;
    }
}

/**
 * 删除所有cookie
 */
export function clearAllCookie() {
    const date = new Date();
    date.setTime(date.getTime() - 10000);
    const keys = document.cookie.match(/[^ =;]+(?=\\=)/g);
    if (keys) {
        for (let i = keys.length; i--; )
            document.cookie = keys[i] + "=0; expire=" + date.toGMTString() + "; path=/";
    }
}
