.aocdn-cache-extensions-dialog {

    .aocdn-cache-extensions {
        margin-bottom: 5px;
        padding: 5px 0;
        border-bottom: 1px solid #ccc;
        line-height: 36px;

        .extension-title {
            // float: left;
            display: inline-block;
            width: 155px;
            vertical-align: center;

            ::v-deep .el-checkbox {
                display: grid;
                grid-template-columns: 8px auto;
                gap: 8px;
                align-items: end;
                margin-top: 10px;

                .el-checkbox__label {
                    white-space: pre-wrap;
                }

                .el-checkbox__input {
                    align-self: start;
                    padding-top: 2px;
                }
            }
        }

        .extension-list {
            overflow: hidden;
            display: inline-block;
            width: calc(100% - 155px);
            vertical-align: top;
            white-space: initial;

            ::v-deep .el-checkbox {
                width: 70px;
            }
        }
    }
}
