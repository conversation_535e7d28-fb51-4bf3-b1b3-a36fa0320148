{"common": {"usage": "用量分析", "rank": "热门分析", "user": "用户分析", "tab": ["带宽流量", "回源统计", "请求数", "命中率", "状态码", "回源状态码", "PV/UV", "地区运营商", "下载速度", "高性能网络"], "searchLabel1": "范围", "searchLabel2": "协议", "searchLabel3": "时间", "searchPlaceholder": ["请选择加速类型", "请选择域名", "请选择运营商", "请选择协议类型", "请选择协议版本", "请选择标签", "请选择状态码", "时间粒度", "请选择加速区域"], "domainSelectOption": "全部域名", "searchDownloadContent": "下载数据", "chart": {"loading": "正在拼命绘图中...", "toolBox": ["导出CSV", "区域缩放", "区域缩放还原", "还原"], "errMsg": ["域名参数为空，请确认后重试", "当前页面缺少 getData 方法", "无统计数据可用，请查询后重试", "当前页面缺少 tableToExcel 方法"]}, "table": {"loading": "正在查询中..."}, "searchBtn": "查询", "vchartTip1": "{type} 带宽峰值{num}", "vchartTip2": "{type} 95带宽峰值{num}", "vchartTip3": "{type} 总流量{num}", "vchartTip4": "{type} 下载速度峰值{num}", "timeGrain": ["5分钟", "1小时", "1天", "1分钟"], "abroadOptions": ["中国内地", "全球（不含中国内地）", "北美", "欧洲", "亚太1区", "亚太2区", "亚太3区", "中东&非洲", "南美"], "tableColumn": {"traffic": "流量({unit})", "peakBandwidth": "带宽峰值({unit})", "peakBandwidth2": "峰值带宽({unit})"}}, "dcdn": {"headerText": "全站加速-用量分析", "headerTip": "支持一年内、最长时间跨度为一个月的用量数据查询。", "bandwidthFlowWhole": {"radioBtn1": "带宽", "radioBtn2": "流量", "tableTip": "{type}峰值数据", "dailyType": "总", "chartType": "带宽", "vchartOptions": {"title1": "带宽", "title2": "流量", "yAxisName": "单位：{unit}"}, "tableColumn1": "日期", "tableColumn2": "{type}流量值{unit}", "tableColumn3": "{type}带宽峰值{unit}", "tableColumn4": "{type}峰值时间点", "tableToExcel": {"excelName": "{chartType}数据", "excelColumn1": "时间", "excelColumn2": "总带宽({mbps})", "excelColumn3": "流量({mb})", "excelColumn4": "{type} 带宽({mbps})", "excelColumn5": "{type} 流量({mb})", "excelColumn6": "汇总：", "excelColumn7": "{type} 汇总峰值带宽{num}{suffix}", "excelColumn8": "{type} 95峰值带宽{num}{suffix}", "excelColumn9": "{type} 总流量{num}{suffix}", "excelColumn10": "{type} 平均流量{num}{suffix}"}}, "missWhole": {"radioBtn1": "回源带宽", "radioBtn2": "回源流量", "tableTip": "回源峰值数据", "tableColumn1": "日期", "tableColumn2": "回源流量值({unit})", "tableColumn3": "回源带宽峰值({unit})", "tableColumn4": "回源峰值时间点", "vchartOptions": {"title1": "带宽", "title2": "流量", "yAxisName": "单位：{unit}"}, "tableToExcel": {"excelName": "{chartType}数据", "excelColumn1": "时间", "excelColumn2": "回源带宽({mbps})", "excelColumn3": "回源流量({mb})", "excelColumn4": "汇总：", "excelColumn5": "回源峰值带宽", "excelColumn6": "回源总流量", "excelColumn7": "平均回源流量"}}, "requestWhole": {"radioBtn1": "请求次数", "tableTip": "天粒度数据统计", "tableColumn1": "序号", "tableColumn2": "日期", "localRequestType": {"websocket": "websocket请求数"}, "vchartOptions": {"toolTips": "次", "yAxisName": "单位：次"}, "summariesName": "汇总请求数", "tableToExcel": {"excelName": "请求数数据", "excelColumn1": "汇总：", "excelColumn2": "时间"}}, "hit": {"radioBtn1": "流量命中率", "radioBtn2": "请求命中率", "vchartTip": "{showFlow}请求命中率", "totalTipPrefix1": "流量", "totalTipPrefix2": "请求", "totalTip": "命中率峰值", "vchartOptions": {"yAxisName": "单位：%"}, "seriesName1": "流量命中率", "seriesName2": "请求命中率", "tableToExcel": {"excelName1": "流量命中率数据", "excelName2": "请求命中率数据", "excelColumn1": "时间,命中流量(B),流量(B),命中流量占比(%)", "excelColumn2": "时间,命中次数,请求数,请求命中率(%)"}}, "statusCode": {"radioBtn": "所有状态码", "totalTip": "总状态码量：", "totalTipUnit": "次", "itemTip": "状态码量：", "ctTip1": "状态码占比表统计", "ctTip2": "状态码占比饼图统计", "tableColumn1": "状态码名称", "tableColumn2": "状态码次数(次)", "tableColumn3": "占比(%)", "vchartOptions": {"totalTip": "总状态码量", "itemTip": "状态码量", "seriesItemName1": "其他", "yAxisName": "单位：次", "seriesItemName2": "状态码"}, "tableToExcel": {"excelName": "状态码数据", "excelColumn1": "时间", "excelColumn2": "总状态码量（次）,{totalCode}", "excelColumn3": "状态码量（次）,{itemsTotal}", "tableName1": "所有状态码占比统计", "tableName2": "状态码占比统计"}}, "backToOriginStatusCode": {"radioBtn": "所有回源状态码", "totalTip": "总回源状态码量：", "totalTipUnit": "次", "itemTip": "回源状态码量：", "ctTip1": "回源状态码占比表统计", "ctTip2": "回源状态码占比饼图统计", "tableColumn1": "回源状态码名称", "tableColumn2": "回源状态码次数(次)", "tableColumn3": "占比(%)", "vchartOptions": {"totalTip": "总回源状态码量", "itemTip": "回源状态码量", "seriesItemName1": "其他", "yAxisName": "单位：次", "seriesItemName2": "回源状态码"}, "tableToExcel": {"excelName": "回源状态码数据", "excelColumn1": "时间", "excelColumn2": "总回源状态码量（次）,{totalCode}", "excelColumn3": "回源状态码量（次）,{itemsTotal}", "tableName1": "所有回源状态码占比统计", "tableName2": "回源状态码占比统计"}}, "PvUv": {"totalTip1New": "{type}峰值({tipText}统计)", "totalTipUnit": "{max}次", "totalTip2": "{type}总量", "totalTipUnit1": "天", "totalTipUnit2": "1小时", "ctTip": "天粒度数据统计", "tableColumn1": "序号", "tableColumn2": "日期", "tableColumn3": "PV(次)", "tableColumn4": "UV(次)", "searchTip1": "请选择域名后点击“查询”", "searchTip2": "没有查询到符合条件的记录，请换其他条件试试", "vchartOptions": {"yAxisName": "单位：次", "totalTipUnit": "{marker} {value} 次"}, "tableToExcel": {"excelName": "{title}数据", "excelColumn1": "时间,{chartType}(次)", "excelColumn2": "{chartType}峰值(1小时统计)", "excelColumn3": "{chartType}总量", "excelColumn4": "{chartType}峰值(天粒度数据统计)"}, "getSummariesItem": "当前汇总"}, "privateNetworkAcceleratorWhole": {"radioBtn1": "带宽", "radioBtn2": "流量", "tableTip": "{type}峰值数据", "dailyType": "总", "chartType": "带宽", "vchartOptions": {"title1": "带宽", "title2": "流量", "yAxisName": "单位：{unit}"}, "tableColumn1": "日期", "tableColumn2": "{type}流量值{unit}", "tableColumn3": "{type}带宽峰值{unit}", "tableColumn4": "{type}峰值时间点", "tableToExcel": {"excelName": "{chartType}数据", "excelColumn1": "时间", "excelColumn2": "总带宽({mbps})", "excelColumn3": "流量({mb})", "excelColumn4": "{type} 带宽({mbps})", "excelColumn5": "{type} 流量({mb})", "excelColumn6": "汇总：", "excelColumn7": "{type} 汇总峰值带宽{num}{suffix}", "excelColumn8": "{type} 95峰值带宽{num}{suffix}", "excelColumn9": "{type} 总流量{num}{suffix}", "excelColumn10": "{type} 平均流量{num}{suffix}"}}}, "usageQuery": {"headerText": "用量查询", "headerTip": "支持六个月内、最长时间跨度为一个月的用量数据查询。", "tip1": "日峰值月平均", "tip2": "95带宽峰值", "BandwidthFlow": {"ctTip": "峰值数据", "tableColumn1": "日期", "tableColumn2": "流量值{unit}", "tableColumn3": "带宽峰值{unit}", "tableColumn4": "峰值时间点", "tableColumn5": "回源带宽峰值{unit}", "tableColumn6": "回源峰值时间点", "chartType": "带宽", "chartOptions": {"title1": "带宽", "title2": "流量", "yAxisName": "单位：{unit}"}, "tableToExcel": {"excelName": "{chartType}数据", "excelColumn1": "时间", "excelColumn2": "带宽", "excelColumn3": "带宽({mbps})", "excelColumn4": "流量({mb})", "excelColumn5": "汇总：", "excelColumn6": "汇总峰值带宽{num}{suffix}", "excelColumn7": "95峰值带宽{num}{suffix}", "excelColumn8": "总流量{num}{suffix}", "excelColumn9": "平均流量{num}{suffix}"}}, "miss": {"radioBtn1": "回源带宽", "radioBtn2": "回源流量", "chartType": "回源带宽", "chartOptions": {"title1": "带宽", "title2": "流量", "yAxisName": "单位：{unit}"}, "tableToExcel": {"excelName": "{chartType}数据", "excelColumn1": "时间", "excelColumn2": "回源带宽({mbps})", "excelColumn3": "回源流量({mb})", "excelColumn4": "汇总：", "excelColumn5": "回源峰值带宽", "excelColumn6": "回源总流量", "excelColumn7": "平均回源流量"}}, "request": {"radioBtn1": "请求", "radioBtn2": "回源请求", "totalTip1": "总请求数", "totalTip2": "总回源请求数", "totalTip3": "请求数峰值", "totalTip4": "回源请求数峰值", "totalTip5": "请求数谷值", "totalTip6": "回源请求数谷值", "totalTipUnit": "{value}次", "ctTip": "天粒度数据统计", "tableColumn1": "序号", "tableColumn2": "日期", "tableColumn3": "请求数(次)", "tableColumn4": "回源请求数(次)", "chartType": "请求", "chartOptions": {"title1": "请求", "title2": "回源请求", "yAxisName": "单位：次"}, "getSummaries": "汇总请求数", "tableToExcel": {"excelName": "请求数数据", "excelName2": "回源请求数数据", "excelColumn1": "时间", "excelColumn2": "请求数(次)", "excelColumn3": "回源请求数(次)", "excelColumn4": "汇总：", "excelColumn5": "总请求数", "excelColumn6": "请求数峰值", "excelColumn7": "请求数谷值"}}, "downloadSpeed": {"tableTip": "{type}峰值数据", "dailyType": "", "chartType": "下载速度", "chartOptions": {"title1": "下载速度", "yAxisName": "单位：{unit}"}, "tableColumn1": "日期", "tableColumn2": "流量值 ({unit})", "tableColumn3": "{type}下载速度峰值{unit}", "tableColumn4": "峰值时间点", "tableToExcel": {"excelName": "{chartType}数据", "excelColumn1": "时间", "excelColumn2": "总下载速度({unit})", "excelColumn3": "流量({mb})", "excelColumn4": "{type} 下载速度({bps})", "excelColumn5": "{type} 流量({mb})", "excelColumn6": "汇总：", "excelColumn7": "{type} 汇总峰值下载速度{num}{suffix}", "excelColumn8": "{type} 95峰值下载速度{num}{suffix}"}}, "PrivateNetworkAccelerator": {"ctTip": "峰值数据", "tableColumn1": "日期", "tableColumn2": "流量值{unit}", "tableColumn3": "带宽峰值{unit}", "tableColumn4": "峰值时间点", "tableColumn5": "回源带宽峰值{unit}", "tableColumn6": "回源峰值时间点", "chartType": "带宽", "chartOptions": {"title1": "带宽", "title2": "流量", "yAxisName": "单位：{unit}"}, "tableToExcel": {"excelName": "{chartType}数据", "excelColumn1": "时间", "excelColumn2": "带宽", "excelColumn3": "带宽({mbps})", "excelColumn4": "流量({mb})", "excelColumn5": "汇总：", "excelColumn6": "汇总峰值带宽{num}{suffix}", "excelColumn7": "95峰值带宽{num}{suffix}", "excelColumn8": "总流量{num}{suffix}", "excelColumn9": "平均流量{num}{suffix}"}}}, "rank": {"headerText": "热门分析", "headerTip": "支持三个月内、最长时间跨度为一个月的热门数据查询。", "searchBar": {"areaSelectRadioText1": "流量优先", "areaSelectRadioText2": "访问次数优先"}, "common": {"tab": ["热门URL", "热门URL（回源）", "热门Referer", "域名排行", "TOP客户端IP"], "tableColumn1": "排行", "tableColumn2": "URL", "tableColumn3": "流量", "tableColumn4": "流量占比(%)", "tableColumn5": "访问次数", "tableColumn6": "访问占比(%)", "tableToExcel": {"excelName": "热门URL", "excelColumn1": "URL,流量,流量占比(%),访问次数(次),访问占比(%)", "excelColumn2": "汇总：", "excelColumn3": "总访问数：{num}", "excelColumn4": "总流量：{flow}"}}, "hotUrlMiss": {"tableToExcel": {"excelName": "热门URL（回源）"}}, "hotReferer": {"tableToExcel": {"excelName": "热门Referer", "excelColumn1": "Referer,流量,流量占比(%),访问次数(次),访问占比(%)"}}, "domainRank": {"tableColumn1": "排行", "tableColumn2": "域名", "tableColumn3": "流量", "tableColumn4": "流量占比(%)", "tableColumn5": "带宽峰值", "tableColumn6": "峰值时刻", "tableColumn7": "访问次数", "tableToExcel": {"excelName": "域名排行", "excelColumn1": "域名,流量,流量占比(%),带宽峰值,峰值时刻,访问次数(次)"}}, "timeSelect": "热门URL、热门URL（回源）、热门Referer、TOP客户端IP等数据按照小时统计，时间范围只支持按照整点选择"}, "provider": {"area": "地区", "flowPercent": "流量占总比(%)", "requestPercent": "请求数占总比(%)", "statisticsDomain": "统计域名", "all": "全部", "statisticsIsp": "统计运营商", "province": "省份", "bandwidth": "带宽", "traffic": "流量", "visitPercent": "访问次数占比", "providerData": "地区运营商数据", "mainland": "中国大陆", "overseas": "港澳台及海外地区", "overseas2": "港澳台及海外地区", "overseas3": "全球"}, "router": {"tip1": "站点统计", "tip2": "数据分析", "tip3": "CDN加速用量", "tip4": "全站加速用量", "tip5": "域名用量分析"}, "user": {"headerTip": "支持一年内、最长时间跨度为一个月的用户数据查询。", "tip1": "访问用户区域分布", "tip2": "用户所在区域", "tip3": "第{num}页", "tip4": "共{num}页", "tip5": ["高", "低"], "tip6": "用户区域数据", "tip7": "用户所属运营商", "tip8": "访问运营商分布", "tip9": "运营商", "tip10": "用户运营商数据", "tip11": "独立IP访问数", "tip12": "独立IP访问峰值", "tip13": "日活跃", "tip14": "1小时统计", "tip15": "日活跃IP总量：", "tip16": "时间", "tip17": "次", "tip18": "峰值", "tip19": "总量", "tip20": "IP数据"}, "eas": {"tip1": "IP应用加速用量", "tip2": "当前无实例", "tip3": "总带宽", "tip4": "总流量", "tip5": "请选择实例", "tip6": "全部实例", "tab": ["带宽", "流量", "连接数", "地区运营商", "域名排行", "TOP客户端IP"], "bandwidthList": ["总带宽", "上行带宽", "下行带宽"], "flowList": ["总流量", "上行流量", "下行流量"], "tip7": "IP应用加速用量", "tip8": "带宽峰值时刻", "tip9": "连接数占比(%)", "tip10": "并发连接数峰值", "tip11": "并发连接数峰值时刻", "excel": {"tip1": "总连接数：", "tip2": "总流量：", "tip3": "连接数峰值：", "tip4": "连接数谷值：", "tip5": "并发连接数峰值：", "tip6": "并发连接数谷值："}, "tip12": "Top客户端IP", "tip13": "天粒度统计", "tip14": "带宽数据", "tip15": "并发连接数", "tip16": "总连接数", "tip17": "连接数峰值", "tip18": "连接数谷值", "tip19": "并发连接数峰值", "tip20": "并发连接数谷值", "tip21": "连接数据", "tip22": "地区运营商列表", "tip23": "国内其他", "tip24": "连接数（个）", "tip25": "并发连接数峰值（个/秒）", "tip26": "流量值", "tip27": "导出时间", "tip28": "连接数优先", "tip29": "并发连接数据", "tip30": "流量数据", "tip31": "海外其他", "connections": {"tip1": "{value}个", "tip2": "{value}个/秒", "unit1": "单位：个", "unit2": "单位：个/秒"}}}