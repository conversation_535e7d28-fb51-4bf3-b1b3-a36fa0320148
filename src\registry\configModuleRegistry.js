// 可插拔配置-配置项注册中心
import { ConfigModulesModule } from '@/store/modules/configModules';

export const configModuleRegistry = {};

/**
 * 配置项注册接口
 * @param {string} moduleName - 模块名称，如果有功能锁，功能锁标识需要跟模块名称一致，因为 getApiData 中是使用 moduleName 来匹配功能锁
 * @param {object} config - 模块配置
 * @param {object} config.fields - 字段定义
 * @param {function} config.onModuleChange - 更新处理（包含联动逻辑）
 * @param {function} config.toApi - API转换
 * @param {object} config.anchor - 锚点配置
 */
export function registerConfigModule(moduleName, config) {
    // config传入以下配置对象
    // {
    //      fields: { ... },                            // 字段定义
    //      onModuleChange(form, val) { ... },                // 更新处理（包含联动逻辑）
    //      toApi(form, { isLocked }) { ... },          // API转换
    //      displayCondition(form) { ... },     // 定义展示条件额外的展示条件（其他配置影响当前配置的展示联动逻辑）
    //      anchor: { ... }                             // 锚点配置
    //      init: (thisData) => { ... }                      // 自定义的初始化处理方法，不传默认按照fields字段进行初始化
    //  }
    configModuleRegistry[moduleName] = config;
}

/**
 * 获取注册中心配置的工具函数
 * @param {string} moduleName - 模块名称，有传模块名称则获取该模块配置，没传则返回全部配置
 * @returns {object} 模块配置
 */
export function getRegistryConfig(moduleName) {
    // 如果总开关关闭，返回空配置
    if (!ConfigModulesModule.isMasterSwitchEnabled) {
        return {
            fields: {},
            onModuleChange: () => ({}),
            toApi: () => ({}),
            displayCondition: () => true, // 默认返回 true，表示默认显示
            anchor: null,
            init: null
        };
    }

    if (moduleName) {
        return configModuleRegistry[moduleName];
    }

    return configModuleRegistry;
}

/**
 * 配置项注册示例:
 *
registerConfigModule('ja4', {
    // 声明这个配置项有哪些字段
    fields: {
        ssl_ja4_enable: 'off',
        ja4_mode: 'standard',
        // 声明会被联动修改的字段
        related_port: 80,
        related_protocol: 'http'
    },
    // 这个 onModuleChange 是给 FormWrapper 用的，用于更新主表单数据
    onModuleChange(form, val) {
        // 1. 基础字段更新
        const updates = {
            ssl_ja4_enable: val.ssl_ja4_enable,
            ja4_mode: val.ja4_mode
        };

        // 2. 联动字段更新
        if (val.ssl_ja4_enable === 'on') {
            updates.related_port = 443;
            updates.related_protocol = 'https';
        } else {
            updates.related_port = 80;
            updates.related_protocol = 'http';
        }

        return updates;
    },
    // 提交时如何转换数据
    toApi(form, { isLocked }) {
        // 如果有功能锁，返回空对象，不做任何修改
        if (isLocked) {
            return {};
        }
        if (form.ssl_ja4_enable) {
            return {
                ssl_ja4_enable: form.ssl_ja4_enable,
                ja4_mode: form.ja4_mode
            };
        }
        return {};
    },
    // 添加展示条件配置
    displayCondition(form) {
        // 如：静态加速开关必须为开启状态
        return form.statics_ability === 'on';
    },
    // 锚点配置
    anchor: {
        prop: '#div6_2',
        label: 'domain.detail.ja4_fingerprint',
        position: {
            type: 'after',
            target: '#div6_1'  // 插入到 ja3 锚点之后
        },
        // 其他的特殊展示条件(如有)
        anchorCondition() {
            return this.isNewEcgw && !this.isLocked;
        },
        // 是否在展开的锚点菜单中，仅aocdn生效
        isInExpanded: boolean,
        // 是否在折叠的锚点菜单中，仅aocdn生效
        isInCollapsed: boolean,
    }
});
*/
