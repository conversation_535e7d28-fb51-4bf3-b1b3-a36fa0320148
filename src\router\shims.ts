/**
 * router 业务垫片，用于在 vue-router 的 push 方法中支持不同路径的正确跳转
 */
import Router from "vue-router";
import VueRouter from "vue-router";
import { prefix } from "@/router/routeData";

function push(this: VueRouter, ...args: any[]) {
    const target = args[0];
    let goHref = false;
    if (typeof target === "string") {
        // 非#开头，并且携带#号的, 例如 /abc/#/ABC, http://a.com/#/12123, abc/#/ABC, http://asasasa
        const anotherPageAddressPatter = /^(([^#]+#.*)|[^:\\/]+:\/\/.*)$/;
        if (anotherPageAddressPatter.test(target)) {
            window.location.href = target;
            goHref = true;
        } else {
            // 合法的本地hash，例如 #/abc, /abc, #abc, #/, #, abc都是合法值
            const localHashAddressPattern = /^(((#?\/)|#)[^#]*|[^#]+)$/;
            if (localHashAddressPattern.test(target)) {
                const standardLocalAddress = target.replace(/^#\//, "/");
                args[0] = prefix + standardLocalAddress;
            }
        }
    }

    if (!goHref) {
        (this as any).oldPush(...args);
    }
}
// TODO 未能寻找到补充 VueRouter 声明的实现方式
// 在 vue-router/types/router 中是 declare class VueRouter
(Router.prototype as any).oldPush = Router.prototype.push;
Router.prototype.push = push;
