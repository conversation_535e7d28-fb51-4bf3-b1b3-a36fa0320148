<template>
    <ct-box :tags="$t('home.product.title')">
        <el-table
            :empty-text="$t('common.table.empty')"
            :data="filteredProductList"
            class="ud-scrollbar"
            ref="table"
        >
            <el-table-column :label="$t('home.product.label1')" :formatter="formatterProduct" />
            <el-table-column
                :label="$t('home.product.label2')"
                :formatter="formatterBilling"
                align="center"
            />
        </el-table>
        <div class="btns" v-if="!isVip && !isCtclouds && orderEnable">
            <div>
                <el-button type="ct" @click="goBuy(cdnPacketOrderBtn, 'cdn')">
                    {{ $t("billingDetail.cdn.packageOderBtn") }}
                </el-button>
                <el-button type="ct" @click="goBuy(icdnPacketOrderBtn, 'icdn')">
                    {{ $t("billingDetail.icdn.packageOderBtn") }}
                </el-button>
            </div>
        </div>
    </ct-box>
</template>

<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import { nUserModule } from "@/store/modules/nuser";
import { BillingItem } from "@/types/home";
import { BasicUrl } from "@/config/url/basic";
import { checkCtiamButtonAuth } from "@/store/modules/menu";
import { GetCtiamButtonAction } from "@/store/config";
import { newBillingUrl } from "@/config/url";
import { productCodeToName } from "@/utils/product";

@Component
export default class Billing extends Vue {
    private cdnPacketOrderBtn = "";
    private icdnPacketOrderBtn = "";
    private productList: BillingItem[] = [];

    get isVip() {
        return nUserModule.isVip;
    }
    get isCtclouds() {
        return nUserModule.isCtclouds;
    }
    get lang() {
        return nUserModule.lang === "zh" ? "zh-cn" : "en-us";
    }
    // 判断是否为线上+个人、线上+政企、线下+个人
    get orderEnable() {
        return (
            (nUserModule.userInfo as any).saleChannel === 2 ||
            (nUserModule.userInfo as any).userType === "user"
        );
    }
    get filteredProductList() {
        return this.productList
            // 过滤待生效的产品
            .filter(item => item.billingType?.status === 1)
            .filter(item => {
                // 展示CDN加速主产品（含下载加速、静态加速、视频点播加速）、全站加速（含上传加速和websockt加速），不展示下载加速闲时
                return ["001", "003", "004", "006", "008"].includes(item.productCode);
            })
            .filter(item => {
                // 过滤非主产品
                return [
                    "ICDN_ABROAD",
                    "ICDN",
                    "ICDN_WEBSOCKET",
                    "ICDN_UPLOAD",
                    "VOD_ACCE",
                    "VOD_ACCE_ABROAD",
                    "CDN_ACCE",
                    "CDN_ACCE_ABROAD",
                    "DOWNLOAD_ACCE",
                    "DOWNLOAD_ACCE_ABROAD",
                    "STATIC_ACCE",
                    "STATIC_ACCE_ABROAD",
                ].includes(item.resourceType.replace(/_PT/, ""));
            });
    }
    mounted() {
        this.getConfig();
        this.getBillingData();
    }
    protected overseaRegionMap(region: string) {
        return region && `${region}` !== "100" ? `_${this.$t(`billingDetail.overseaRegionName['${region}']`)}` : ""
    }
    protected formatterProduct(row: BillingItem) {
        return `${productCodeToName(row.productCode)}-${this.$t(
            `billingDetail.productName.${row.resourceType.replace(/_PT/, "")}`
        )}${this.overseaRegionMap(row.overseaRegion)}`;
    }
    protected formatterBilling(row: BillingItem) {
        return row.billingType.billingType ? this.$t(`billingDetail.billingTypeName.${row.billingType.billingType}`) : "";
    }
    // 能看到按钮的用户必然有权限，没必要判断权限了
    protected async goBuy(url: string, product = "cdn") {
        await checkCtiamButtonAuth(
            GetCtiamButtonAction(product === "cdn" ? "cdnPackageSubscribe" : "icdnPackageSubscribe")
        );
        window.open(url);
    }

    async getConfig() {
        try {
            const basicConfig: any = await this.$ctFetch(BasicUrl.getConfig, { cache: true });
            this.cdnPacketOrderBtn = `${basicConfig.cdnPacketOrderBtn}&lang=${this.lang}`;
            this.icdnPacketOrderBtn = `${basicConfig.icdnPacketOrderBtn}&lang=${this.lang}`;
        } catch (e) {
            this.$errorHandler(e);
        }
    }
    async getBillingData() {
        const rst = await this.$ctFetch<BillingItem[]>(newBillingUrl.overviewList);
        this.productList = rst;
    }
}
</script>

<style lang="scss" scoped>
// 实现 table 自适应高度滚动
.ct-box {
    display: flex;
    flex-direction: column;
}

.el-table {
    flex: 1;
    margin: 0;

    &::before {
        content: none;
    }
}

.btns {
    margin: 8px 0;
    min-height: 0;
    text-align: center;
}

::v-deep {
    .el-table__body-wrapper {
        height: 100%;
        overflow-y: auto;
    }

    .el-table {
        .el-table__body {
            width: 100% !important;
        }

        .cell {
            // 使换行符\n生效
            white-space: pre-line;
        }
    }
}
</style>
