<template>
    <ct-section-wrap :headerText="headerTitle">
        <ct-box class="table-scroll-wrap">
            <div>
                <el-button type="text" class="back-btn" @click="back">
                    <i class="el-icon-arrow-left" />
                    {{ $t("certificate.back") }}
                </el-button>
                <span class="tips">{{ $t('certificate.certName') + ': ' + certName }}</span>
            </div>
            
            <el-table :empty-text="$t('common.table.empty')" :data="dataList" v-loading="loading">
                <el-table-column
                    min-width="120"
                    :label="$t('certificate.versionTable.label[0]')"
                    prop="certificate_version"
                    align="left"
                    show-overflow-tooltip
                />
                <el-table-column
                    :label="$t('certificate.versionTable.label[1]')"
                    prop="sans"
                    align="left"
                    min-width="120"
                    show-overflow-tooltip
                />
                <el-table-column
                    min-width="120"
                    :label="$t('certificate.versionTable.label[2]')"
                    prop="issuer"
                    align="left"
                    show-overflow-tooltip
                />
                <el-table-column :label="$t('certificate.versionTable.label[3]')" width="180">
                    <template slot-scope="{ row }">
                        {{ (row.issue_at * 1000) | timeFormat }}
                    </template>
                </el-table-column>
                <el-table-column :label="$t('certificate.versionTable.label[4]')" width="180">
                    <template slot-scope="{ row }">
                        {{ row.expires_at | timeFormat }}
                    </template>
                </el-table-column>
                <el-table-column :label="$t('certificate.versionTable.label[5]')" width="180">
                    <template slot-scope="{ row }">
                        {{ statusrMap[row.operate_type] }}
                    </template>
                </el-table-column>
                <el-table-column :label="$t('certificate.versionTable.label[6]')" width="180">
                    <template slot-scope="{ row }">
                        {{ (row.update_time * 1000) | timeFormat }}
                    </template>
                </el-table-column>
            </el-table>

            <div class="pager">
                <el-pagination
                    :small="isXs"
                    :layout="isXs ? 'prev, pager, next' : 'total, sizes, prev, pager, next,jumper'"
                    :total="totalRecord"
                    :current-page.sync="page"
                    :page-size="perPage"
                    :page-sizes="[10, 30, 50, 100]"
                    :hide-on-single-page="false"
                    @size-change="sizeChange"
                ></el-pagination>
            </div>
        </ct-box>
    </ct-section-wrap>
</template>

<script lang="ts">
import { Component, Vue, Watch } from "vue-property-decorator";
import { timeFormat } from "@/filters/index";
import { ScreenModule } from "@/store/modules/screen";
import { nUserModule } from "@/store/modules/nuser";
import { CertificateUrl } from "@/config/url/certificate";
import { VersionItem } from "@/types/certificate";
import { getLang } from "@/utils";

@Component({
    name: "versionHistory",
    filters: {
        timeFormat(data: string): string {
            return timeFormat(data) || data;
        },
    },
})
export default class Certificate extends Vue {
    private statusrMap = {
        1: this.$t("certificate.versionTable.types[0]"),
        2: this.$t("certificate.versionTable.types[1]"),
        3: this.$t("certificate.versionTable.types[2]"),
    };
    keyword = "";
    keywordType = "NAME";
    deploymentStatus = "";
    loading = false;
    dataList: VersionItem[] = []; // 最终展示的分页数据
    page = 1;
    perPage = 10;
    totalRecord = 0;

    mounted() {
        this.fetchList();
    }

    @Watch("page")
    onPageChange() {
        this.fetchList();
    }
    get isEn() {
        return getLang() === "en";
    }
    get isXs() {
        return this.screenWidth < 600;
    }
    get certId() {
        return this.$route.query?.id;
    }
    get certName() {
        return this.$route.query?.name;
    }
    get screenWidth() {
        return ScreenModule.width;
    }
    get email() {
        return nUserModule.userInfo.email;
    }
    get userName() {
        return nUserModule.userInfo.name;
    }
    get lang() {
        return nUserModule.lang;
    }
    get headerTitle() {
        return this.$t("certificate.title5")
    }
    async fetchList({
        id = this.certId,
        page_size = this.perPage,
        page = this.page,
    } = {}) {
        this.loading = true;
        const rst = await this.$ctFetch<{ items: VersionItem[]; total: number }>(
            CertificateUrl.certHistory,
            {
                encodeParams: true,
                data: {
                    id,
                    page_size,
                    page,
                },
            }
        );
        this.dataList = rst.items;
        this.totalRecord = rst.total;
    }
    reset() {
        this.keyword = "";
        this.deploymentStatus = "";

        // 重置查询条件后执行查询
        this.fetchList();
    }
    sizeChange(val: number) {
        // 恢复到第一页
        this.page = 1;
        this.perPage = val;
        this.fetchList();
    }
    back() {
        this.$router.push({
            name: "certificate.history"
        });  
    }
}
</script>

<style lang="scss" scoped>
.back-btn {
    color: #333333;
    margin-right: 20px;
    font-size: 14px;
    font-weight: 600;
    i {
        font-weight: 600;
    }
}
.tips {
    font-size: 12px;
    color: #333333;
    line-height: 18px;
}
.pager {
    text-align: right;
    margin-top: 8px;
}

.el-form-item {
    margin-bottom: 20px;
}

.alert {
    color: $g-color-yellow;
}

.search-bar-wrapper {
    display: flex;
    justify-content: space-between;
    @media (max-width: 1330px) {
        display: grid;
        grid-template: 1fr / 1fr;
        gap: 12px;
    }
}

.search-bar + .search-bar {
    margin-top: 20px;
}

.search-label {
    margin-right: 16px;
    font-size: 12px;
    color: #333 !important;
}
.ml-12 {
    margin-left: 12px;
}

.increse-width {
    min-width: 140px !important;
}
.danger {
    color: $color-danger;
}
.info {
    color: $color-info;
}
.success {
    color: $color-success;
}
</style>
