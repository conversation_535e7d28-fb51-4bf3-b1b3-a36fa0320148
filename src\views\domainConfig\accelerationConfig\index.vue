<template>
    <form-wrapper :key="renderKey" />
</template>

<script>
import FormWrapper from "./FormWrapper.vue";
import { SecurityAbilityModule } from "@/store/modules/securityAbility";

export default {
    components: {
        FormWrapper,
    },
    data() {
        return {};
    },
    computed: {
        renderKey() {
            return SecurityAbilityModule.securityRenderKey;
        },
    },
    mounted() {
        if (!window.__POWERED_BY_QIANKUN__) return; // fcdn 不与主应用交互
        window.custom.emit("handleCDNMonitor", [
            {
                event: "handleCDNConfigChange",
                type: "add",
            },
        ]);
    },
    beforeDestroy() {
        if (!window.__POWERED_BY_QIANKUN__) return; // fcdn 不与主应用交互
        window.custom.emit("handleCDNMonitor", [
            {
                event: "handleCDNConfigChange",
                type: "remove",
            },
        ]);
    },
};
</script>

<style lang="scss" scoped></style>
