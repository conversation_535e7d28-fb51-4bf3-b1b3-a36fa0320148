.ct-info-card {
    width: 100%;
    background: white;
    padding: 0 $padding-5x;

    .card-header {
        display: flex;
        padding: $padding-5x 0;
        justify-content: space-between;
        align-items: center;

        .card-title {
            position: relative;
            padding-left: 16px;
            justify-content: space-between;
            font-family: PingFangSC-Medium;
            font-size: 12px;
            color: #333333;
            letter-spacing: 0;
            line-height: 18px;
            font-weight: 500;

            &:before {
                content: "";
                width: 4px;
                height: 14px;
                background: $theme-color;
                position: absolute;
                left: 0;
                //top: 50%;
                //transform: translateY(-50%);
            }
        }

        .icon-tip {
            color: $theme-color;
            font-size: 18px;
            cursor: pointer;
            margin-left: 10px;
        }

        .info-card-tip {
            margin-top: 10px;
            font-size: 12px;
            font-weight: normal;
            color: $d-color-info;
        }
    }

    .header-tip {
        margin-left: 10px;
    }


    &.no-padding {
        padding: 0;
    }
}
