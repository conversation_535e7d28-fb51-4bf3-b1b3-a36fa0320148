<template>
    <el-table :data="fetchData" v-loading="loading" :element-loading-text="$t('statistics.common.table.loading')"
        :empty-text="$t('common.table.empty')">
        <el-table-column :label="$t('statistics.rank.common.tableColumn1')" prop="rank" width="80" />
        <el-table-column label="IP" prop="ip" min-width="180" />
        <el-table-column :label="$t('statistics.provider.traffic')" prop="flow" width="120" />
        <el-table-column :label="$t('statistics.rank.common.tableColumn5') + getTimesUnit()" prop="request" width="180" />
    </el-table>
</template>

<script lang="ts">
import { Component } from "vue-property-decorator";
import { mixins } from "vue-class-component";
import { StatisticsRankUrl } from "@/config/url/statistics";
import { SearchParams, ListTopIpFetchData, ListTopIpItem } from "@/types/statistics/rank";
import ChartMixin from "../rankMixin";
import { convertFlowB2P } from "@/utils";

@Component({
    name: "TopIp",
})
export default class TopIp extends mixins(ChartMixin) {
    fetchData: ListTopIpItem[] = [];
    protected downloadDataList: ListTopIpItem[] = [];
    private totalRequest = "";
    private totalFlow = "";

    // 接口获取数据
    protected async getData(params: SearchParams) {
        const rst = await this.fetchGenerator<ListTopIpFetchData>(StatisticsRankUrl.topIpList, {
            ...params,
            top: 100,
        }) || {
            list: [],
            totalRequest: 0,
            totalFlow: 0,
        };
        rst.list = rst.list || [];

        this.downloadDataList = rst.list
            .map(item => ({
                ...item,
                flow: `${((+item.flow) / Math.pow(this.scale, 2)).toFixed(2)}${this.MB}`,
            }))
            .sort((a, b) => +a.rank - +b.rank);

        this.fetchData = rst.list
            .map(item => ({
                ...item,
                flow: convertFlowB2P(item.flow, this.scale).result,
            }))
            .sort((a, b) => +a.rank - +b.rank);

        this.totalRequest = rst.totalRequest;
        this.totalFlow = rst.totalFlow;
    }
    // 重写 excel 数据拼接方法
    protected tableToExcel() {
        let str = "";

        str = `${this.$t("statistics.rank.common.tableColumn1")},IP,${this.$t("statistics.provider.traffic")},${this.$t("statistics.rank.common.tableColumn5")}${this.getTimesUnit()}\n`;

        this.downloadDataList.forEach(item => {
            str += `${item.rank},${item.ip},${item.flow},${item.request}\n`;
        });

        //增加峰值带宽和95峰值带宽
        str += `\n${this.$t("statistics.dcdn.missWhole.tableToExcel.excelColumn4")}\n${this.$t("statistics.rank.common.tableToExcel.excelColumn3", { num: this.totalRequest + this.$t("statistics.dcdn.backToOriginStatusCode.totalTipUnit") })}\n${this.$t("statistics.rank.common.tableToExcel.excelColumn4", { flow: `${((+this.totalFlow) / Math.pow(this.scale, 2)).toFixed(2)}${this.MB}` })}\n`;

        //表格CSV格式和内容
        this.downloadExcel({
            name: this.$t("statistics.rank.common.tab[4]") as string,
            str,
        });
    }
}
</script>

<style lang="scss" scoped></style>
