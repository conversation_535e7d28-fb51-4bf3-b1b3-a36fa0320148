<template>
    <div class="ct-edit-wrapper">
        <el-form
            label-width="140px"
            label-position="left"
            :model="ipBlackWhiteListForm"
            :rules="rules"
            ref="ipBlackWhiteListForm"
            :disabled="!isEdit || !isService"
        >
            <div v-if="!isPoweredByQiankun">
                <!-- IP黑白名单 开关 -->
                <el-form-item :label="$t('domain.create.ip')" prop="ip_switch">
                <el-switch
                    v-model="ipBlackWhiteListForm.ip_switch"
                    active-value="on"
                    inactive-value="off"
                    @change="ip_switch_change"
                ></el-switch>
                </el-form-item>
                <div v-if="ipBlackWhiteListForm.ip_switch === 'on'" class="switch-wrapper">
                    <!-- 类型 -->
                    <el-form-item :label="$t('domain.type')" prop="ip" :rules="rules.ip">
                        <div>
                            <div class="radio-row">
                                <el-radio-group v-model="ipBlackWhiteListForm.ipType" @change="ipTypeChange">
                                    <el-radio label="allow">{{ $t("domain.detail.trustlist") }}</el-radio>
                                    <el-radio label="block">{{ $t("domain.detail.blocklist") }}</el-radio>
                                </el-radio-group>
                            </div>
                            <div>
                                <el-input
                                    class="textarea-wrapper"
                                    v-model="ipBlackWhiteListForm.ip"
                                    type="textarea"
                                    :rows="3"
                                    :placeholder="ipPlaceholder"
                                    @change="onIpChange"
                                />
                            </div>
                        </div>
                    </el-form-item>
                </div>
            </div>

        </el-form>
    </div>
</template>

<script>
import componentMixin from "@/views/domainConfig/componentMixin";
import { SecurityAbilityModule } from "@/store/modules/securityAbility";

export default {
    name: "ipBlackWhiteList",
    components: {},
    mixins: [componentMixin],
    props: {
        datas: Object,
    },
    data() {
        return {
            ipBlackWhiteListForm: {
                ip_switch: "off",
                ipType: "allow",
                ip: "",
            },
            maxNum: 400,
            isLimitNum: false, // 是否限制黑白名单ip个数

            rules: {
              ip: [{ required: false, validator: this.valid_ip, trigger: "blur" }],
            },
        };
    },
    computed: {
      ipPlaceholder() {
          const { maxNum, isLimitNum } = this;
          if (isLimitNum) {
              return this.$t("domain.detail.tip39", { maxNum: maxNum });
          } else {
              return this.$t("domain.detail.tip40");
          }
      },
      ipList() {
        const { ipBlackWhiteListForm = {} } = this;
        const { ip = "" } = ipBlackWhiteListForm;
        return ip.split("\n");
      },
    },
    watch: {
        datas: {
            deep: true,
            handler(val) {
                this.init(val);
            },
            immediate: true,
        },
    },
    methods: {
      init(v) {
        if (!v) return;
        this.ipBlackWhiteListForm.ip_switch = v?.ip_switch;
        this.ipBlackWhiteListForm.ipType = v?.ipType;
        this.ipBlackWhiteListForm.ip = v?.ip;
      },
      // IP黑白名单开关关闭重新打开，需要将类型设置为白名单
      ip_switch_change(val) {
        const originalConf = SecurityAbilityModule.securityBasicConfigOriginForm;
        this.ipBlackWhiteListForm = {
            ip_switch: val,
            ipType: originalConf.ipType,
            ip: originalConf.ip,
        }

        this.$emit("onChange", this.ipBlackWhiteListForm);
      },
      // 类型 事件
      ipTypeChange() {
        this.$emit("onChange", this.ipBlackWhiteListForm);
      },
      // 输入框触发事件
      onIpChange(val) {
        this.$emit("onChange", this.ipBlackWhiteListForm);
      },
      valid_ip(rule, value, callback) {
            // 支持 ip 段
            const ipReg = /^((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)(\/(\d|[1-2]\d|3[0-2]))?$|^([\da-fA-F]{1,4}:){6}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)(\/([1-9]?\d|(1([0-1]\d|2[0-8]))))?$|^::([\da-fA-F]{1,4}:){0,4}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)(\/([1-9]?\d|(1([0-1]\d|2[0-8]))))?$|^([\da-fA-F]{1,4}:):([\da-fA-F]{1,4}:){0,3}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)(\/([1-9]?\d|(1([0-1]\d|2[0-8]))))?$|^([\da-fA-F]{1,4}:){2}:([\da-fA-F]{1,4}:){0,2}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)(\/([1-9]?\d|(1([0-1]\d|2[0-8]))))?$|^([\da-fA-F]{1,4}:){3}:([\da-fA-F]{1,4}:){0,1}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)(\/([1-9]?\d|(1([0-1]\d|2[0-8]))))?$|^([\da-fA-F]{1,4}:){4}:((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)(\/([1-9]?\d|(1([0-1]\d|2[0-8]))))?$|^([\da-fA-F]{1,4}:){7}[\da-fA-F]{1,4}(\/([1-9]?\d|(1([0-1]\d|2[0-8]))))?$|^:((:[\da-fA-F]{1,4}){1,6}|:)(\/([1-9]?\d|(1([0-1]\d|2[0-8]))))?$|^[\da-fA-F]{1,4}:((:[\da-fA-F]{1,4}){1,5}|:)(\/([1-9]?\d|(1([0-1]\d|2[0-8]))))?$|^([\da-fA-F]{1,4}:){2}((:[\da-fA-F]{1,4}){1,4}|:)(\/([1-9]?\d|(1([0-1]\d|2[0-8]))))?$|^([\da-fA-F]{1,4}:){3}((:[\da-fA-F]{1,4}){1,3}|:)(\/([1-9]?\d|(1([0-1]\d|2[0-8]))))?$|^([\da-fA-F]{1,4}:){4}((:[\da-fA-F]{1,4}){1,2}|:)(\/([1-9]?\d|(1([0-1]\d|2[0-8]))))?$|^([\da-fA-F]{1,4}:){5}:([\da-fA-F]{1,4})?(\/([1-9]?\d|(1([0-1]\d|2[0-8]))))?$|^([\da-fA-F]{1,4}:){6}:(\/([1-9]?\d|(1([0-1]\d|2[0-8]))))?$/;

            const { ipList, maxNum, ipBlackWhiteListForm, isLimitNum } = this;

            if (!ipBlackWhiteListForm?.ipType && ipBlackWhiteListForm?.ip) {
                return callback(new Error(this.$t("domain.detail.tip42")));
            } else if (!ipBlackWhiteListForm?.ip?.trim()) {
                return callback(new Error(this.$t("domain.detail.tip45")));
            } else if (ipList && ipList.length > maxNum && isLimitNum) {
                return callback(new Error(this.$t("domain.detail.tip46", { maxNum: maxNum })));
            } else {
                for (let i = 0; i < ipList?.length; i++) {
                    const item = ipList[i]?.trim();
                    // 输入的不是 ip
                    if (!ipReg.test(item)) {
                        return callback(new Error(this.$t("domain.detail.tip47")));
                    }
                }

                // 检查重复
                const hasRepeat = new Set(ipList).size !== ipList.length;
                if (hasRepeat) {
                    return callback(new Error(this.$t("domain.detail.tip48")));
                }
                return callback();
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.ct-edit-wrapper {
    ::v-deep {
        .el-form--label-left .el-form-item__label {
            text-align: right;
        }
    }
}
.textarea-wrapper {
    width: calc(100% - 120px);
}
</style>
