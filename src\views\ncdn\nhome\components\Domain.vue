<template>
    <ct-box :tags="$t('home.domain.title')">
        <template #tags-slot>
            <span>
                <el-tooltip placement="top" :content="$t('home.domain.tableLabel5')">
                    <ct-svg-icon icon-class="question-circle" class-name="ct-sort-drag-icon"></ct-svg-icon>
                </el-tooltip>
            </span>
        </template>
        <el-table
            :empty-text="$t('common.table.empty')"
            :data="filteredDomainList"
            class="ud-scrollbar"
            ref="table"
            v-loading="queryLoading"
        >
            <el-table-column :label="$t('home.domain.tableLabel1')" :key="'domainQuotaTotal' + domainQuotaTotal" min-width="150">
                <template slot="header">
                    <span class="cell-wrapper">
                        <span>{{ $t("home.domain.tableLabel1") + "：" }}</span>
                        <span class="domain-count-wrap">
                            <span class="domain-count">{{ domainQuotaTotal }}</span>
                            <span>{{ $t("home.domain.unit") }}</span>
                        </span>
                    </span>
                </template>
                <template slot-scope="scope">
                    <span class="cell-wrapper">
                        <span>{{  $t("home.domain.tableLabel4", { type: productFormatter(scope.row) }) + "：" }}</span>
                        <span class="domain-count-wrap">
                            <span class="domain-count">{{ scope.row.domainQuota }}</span>
                            <span>{{ $t("home.domain.unit") }}</span>
                        </span>
                    </span>
                </template>
            </el-table-column>
            <el-table-column
                :label="$t('home.domain.tableLabel2')"
                prop="domainQuantity"
                align="left"
                :key="'domainQuantityTotal' + domainQuantityTotal"
                min-width="80"
            >
                <template slot="header">
                    <span class="cell-wrapper">
                        <span>{{ $t("home.domain.tableLabel2") + "：" }}</span>
                        <span class="domain-count-wrap">
                            <span class="domain-count">{{ domainQuantityTotal }}</span>
                            <span>{{ $t("home.domain.unit") }}</span>
                        </span>
                    </span>
                </template>
                <template slot-scope="scope">
                    <span class="cell-wrapper">
                        <span>{{ $t("home.domain.tableLabel2") + "：" }}</span>
                        <span class="domain-count-wrap">
                            <span class="domain-count">{{ scope.row.domainQuantity }}</span>
                            <span>{{ $t("home.domain.unit") }}</span>
                        </span>
                    </span>
                </template>
            </el-table-column>
            <el-table-column
                :label="$t('home.domain.tableLabel3')"
                prop="domain"
                align="left"
                :key="'domainTotal' + domainTotal"
                min-width="90"
            >
                <template slot="header">
                    <span class="cell-wrapper">
                        <span>{{ $t("home.domain.tableLabel3") + "：" }}</span>
                        <span class="domain-count-wrap">
                            <span class="domain-count">{{ domainTotal }}</span>
                            <span>{{ $t("home.domain.unit") }}</span>
                        </span>
                    </span>
                </template>
                <template slot-scope="scope">
                    <span class="cell-wrapper">
                        <span>{{ $t("home.domain.tableLabel3") + "：" }}</span>
                        <span class="domain-count-wrap">
                            <span class="domain-count">{{ scope.row.domain }}</span>
                            <span>{{ $t("home.domain.unit") }}</span>
                        </span>
                    </span>
                </template>
            </el-table-column>
        </el-table>
        <div class="btns">
            <template v-if="lang === 'zh'">
                <el-button
                    type="primary"
                    plain
                    size="medium"
                    @click="go('ndomain.list')"
                >
                    {{ $t("home.domain.btn1") }}
                </el-button>
                <el-button
                    type="primary"
                    plain
                    size="medium"
                    @click="judgeBeforeNew()"
                >
                    {{ $t("home.domain.btn2") }}
                </el-button>
            </template>
            <el-button
                v-if="!isCtclouds"
                type="primary"
                plain
                size="medium"
                @click="go('refresh')"
            >
                {{ $t("home.domain.btn3") }}
            </el-button>
        </div>
    </ct-box>
</template>

<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import { ProductModule, getI18nLabel } from "@/store/modules/ncdn/nproduct";
import { nUserModule } from "@/store/modules/nuser";
import { nHomeUrl } from "@/config/url";
import { checkCtiamButtonAuth } from "@/store/modules/menu";
import { GetCtiamButtonAction } from "@/store/config";
import ctSvgIcon from "@/components/ctSvgIcon/index.vue";

@Component({
    components: {
        ctSvgIcon,
    },
})
export default class Domain extends Vue {
    count = 0;
    get workspaceId() {
        return this.$route.query.workspaceId;
    }

    private domainList: any = [];

    public queryLoading = false;

    get canUseList() {
        return ProductModule.canUseList;
    }
    get lang() {
        return nUserModule.lang;
    }
    get isCtclouds() {
        return nUserModule.isCtclouds;
    }
    // 返回产品名称
    private productFormatter(row: any) {
        return getI18nLabel(row.productCode);
    }
    protected async go(routeName: string) {
        await checkCtiamButtonAuth(
            GetCtiamButtonAction(routeName === "refresh" ? "overviewRefresh" : "overviewDomainManage")
        );
        this.$router.push({
            name: routeName,
        });
    }
    private async judgeBeforeNew() {
        await checkCtiamButtonAuth(GetCtiamButtonAction("overviewDomainAdd"));
        // 无产品时无法创建域名
        if (this.canUseList.length === 0) {
            await this.$prompt(
                this.$t("home.domain.noProduct") as string,
                this.$t("common.messageBox.title") as string,
                {
                    showInput: false,
                    showCancelButton: false,
                }
            );
        } else this.$ctUtil.redirect("#/ndomain/create", true);
    }

    async getDomainCount() {
        this.queryLoading = true;
        const rst = await this.$ctFetch<any[]>(nHomeUrl.domainCount, {
            encodeParams: true,
            data: {
                action: "c_domain", // DomainActionEnum[Domain]
                from: "scc",
            },
        });
        this.queryLoading = false;
        // this.count = rst.count;
        this.domainList = rst || [];
    }

    get filteredDomainList() {
        return this.domainList.filter((itm: any) => {
            return ProductModule.allProductOptions.some(product => product.value === itm.productCode);
        });
    }

    // 全部产品额度-总数
    get domainQuotaTotal() {
        return this.handleCalculate(this.filteredDomainList, "domainQuota");
    }

    // 已配域名-总数
    get domainQuantityTotal() {
        return this.handleCalculate(this.filteredDomainList, "domainQuantity");
    }

    // 已授权域名-总数
    get domainTotal() {
        return this.handleCalculate(this.filteredDomainList, "domain");
    }

    // 根据传入的形参计算对应的数量总和
    handleCalculate(array: any, param: any) {
        if (!(array && array.length > 0)) return;
        const count = array.reduce((accumulator: any, currentValue: any) => {
            return accumulator + currentValue[param];
        }, 0);

        return count;
    }

    mounted() {
        this.getDomainCount();
    }
}
</script>

<style lang="scss" scoped>
.ct-box {
    display: flex;
    flex-direction: column;
}
.el-table {
    flex: 1;
    margin: 0;

    &::before {
        content: none;
    }
}
// .info {
//     text-align: center;
//     clear: both;
//     .count {
//         margin-right: 4px;
//         font-size: 26px;
//         color: $color-master;
//     }
// }

.btns {
    margin-top: 8px;
    text-align: center;
}
.cell-wrapper {
    display: flex;
    gap: 4px;
    flex-wrap: wrap;

    .domain-count-wrap {
        word-break: break-word;
        .domain-count {
            margin-right: 4px;
            color: $color-master;
        }
    }
}
::v-deep {
    .el-table__body-wrapper {
        height: calc(100% - 50px);
        overflow-y: auto;
    }

    .el-table {
        // .el-table__body {
        //     width: 100% !important;
        // }

        .cell {
            white-space: pre-line;
        }
    }
}
.question-circle {
    font-size: $text-size-md;
    cursor: pointer;
}
.ct-sort-drag-icon {
    font-size: $text-size-md;
    vertical-align: middle;
    margin-left: -16px;
}
</style>
