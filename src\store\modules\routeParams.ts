// 路由信息store缓存表
// 因为根据产品要求，项目中不允许路由携带参数，所以常规路由携带参数改为store传参，这样做的意义在于页面刷新后，返回到最初始的状态
import { VuexModule, Module, Action, Mutation, getModule } from "vuex-module-decorators";

import store from "../index";

@Module({ dynamic: true, store, name: "routeParams" })
class RouteParams extends VuexModule {
    public routeParams: any = {};

    get routeParam() {
        return this.routeParams;
    }

    @Mutation
    private SET_ROUTE_PARAMS(info: any) {
        this.routeParams = info;
    }

    @Action
    public setParams(info: any) {
        this.SET_ROUTE_PARAMS(info);
    }
}

export const RouteParamsModule = getModule(RouteParams);
