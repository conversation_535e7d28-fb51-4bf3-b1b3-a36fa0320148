<template>
    <ct-box :tags="$t('statistics.user.tip8')" class="user-section">
        <template #tags-slot>
            <el-radio-group v-model="queryForm.currentType" size="small">
                <el-radio-button label="bandwidth">{{ $t('statistics.dcdn.bandwidthFlowWhole.radioBtn1')
                    }}</el-radio-button>
                <el-radio-button label="flow">{{ $t('statistics.dcdn.bandwidthFlowWhole.radioBtn2') }}</el-radio-button>
                <el-radio-button label="connections">{{ $t('statistics.eas.tip28') }}</el-radio-button>
            </el-radio-group>
            <el-tooltip effect="dark" :content="$t('statistics.common.searchDownloadContent')" placement="right">
                <i class="el-icon-download download-icon" @click="download"></i>
            </el-tooltip>
        </template>

        <el-table ref="tableBase" :data="showDataListBase" :element-loading-text="$t('statistics.common.table.loading')"
            v-loading="loading" max-height="280px" :key="queryForm.currentType">
            <el-table-column :label="$t('statistics.user.tip7')" prop="ispName" />
            <el-table-column :label="tableUnit" prop="currentData" />
            <el-table-column v-if="queryForm.currentType === 'flow'" :label="$t('statistics.rank.common.tableColumn4')"
                prop="flow_per" />
            <el-table-column v-if="queryForm.currentType === 'connections'" :label="$t('statistics.eas.tip9')"
                prop="connections_per" />
        </el-table>
    </ct-box>
</template>

<script>
import tableMixin from "../tableMixin";
import chartShim from "../chartShim";
import { StatisticsUserUrl } from "@/config/url";

export default {
    mixins: [tableMixin, chartShim],
    props: {
        instType: {
            type: Number,
            default: 1
        },
        instName: {
            type: String,
            default: ""
        },
    },
    data() {
        return {
            emitOnMounted: false,
            loading: false,
            fetchDataList: [], // 请求的列表数据
        };
    },
    computed: {},

    methods: {
        initData(reqParam) {
            this.getData(reqParam);
        },
        // 1、数据请求
        async getData(params) {
            this.loading = true;
            const rst = await this.$ctFetch(StatisticsUserUrl.ispDataList, {
                method: "POST",
                transferType: "json",
                body: { data: params },
            });
            this.loading = false;
            //*数据处理ispName
            if (rst.result.length) {
                const bst = await this.$ctFetch(StatisticsUserUrl.ispBaseList, {
                    method: "GET",
                    transferType: "json",
                });
                let index = -1;
                rst.result.forEach(item => {
                    index = bst.result.findIndex(val => Number(val.code) === item.isp);
                    item.ispName = index > -1 ? bst.result[index].cn_name : "";
                });
            }
            this.fetchDataList = rst.result || []; // 有就切到 1 ，没有就切到 0
        },
        download() {
            const { fetchDataList } = this;
            if (fetchDataList.length === 0) return this.$message(`${this.$t("statistics.common.chart.errMsg[2]")}`);

            const userTabMap = {
                bandwidth: this.$t("statistics.eas.tab[0]"),
                flow: this.$t("statistics.eas.tab[1]"),
                connections: this.$t("statistics.eas.tab[2]"),
            };

            let str = `${this.$t("statistics.user.tip9")},${userTabMap[this.queryForm.currentType]}${this.unitTabMap[this.queryForm.currentType]},${this.queryForm.currentType === "flow"
                ? `${this.$t('statistics.rank.common.tableColumn4')}\n`
                : this.queryForm.currentType === "connections"
                    ? `${this.$t('statistics.eas.tip9')}\n`
                    : "\n"
                }`;
            fetchDataList.forEach(item => {
                const currentData =
                    this.queryForm.currentType !== "connections"
                        ? (item[this.queryForm.currentType] / Math.pow(this.scale, 2)).toFixed(2)
                        : item[this.queryForm.currentType];

                str += item["ispName"] + ",";
                str += currentData + ",";
                str +=
                    this.queryForm.currentType === "flow"
                        ? item.flow_per
                        : this.queryForm.currentType === "connections"
                            ? item.connections_per
                            : "";
                str += "\n";
            });
            const instType = this.instType
            this.downloadExcelForUser({
                name: `${this.$t("statistics.user.tip10")}`,
                str,
                instType,
            });
        },
    },
};
</script>

<style scoped lang="scss"></style>
