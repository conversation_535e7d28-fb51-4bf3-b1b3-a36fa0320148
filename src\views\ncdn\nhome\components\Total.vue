<template>
    <ct-box v-loading="loading" :tags="$t('home.cert.title')">
        <div class="cert-wrap">
            <div class="cert-count total-count" @click="go">
                {{ $t("home.cert.label1") }}：<span>{{ certCount }}</span> {{ $t("home.cert.unit") }}
            </div>
            <div class="cert-count expire-count" @click="go">
                {{ $t("home.cert.label2") }}：<span>{{ willExpiredCount }}</span> {{ $t("home.cert.unit") }}
            </div>
        </div>
    </ct-box>
</template>

<script lang="ts">
import { nCertificateUrl } from "@/config/url";
import { Component, Vue } from "vue-property-decorator";
import { nUserModule } from "@/store/modules/nuser";
import { checkCtiamButtonAuth } from "@/store/modules/menu";
import { GetCtiamButtonAction } from "@/store/config";

@Component
export default class Total extends Vue {
    private loading = true;
    private certCount = 0;
    private willExpiredCount = 0;

    get isCtclouds() {
        return nUserModule.isCtclouds;
    }

    created() {
        //获取证书列表
        this.getCertList();
    }
    protected async getCertList() {
        //调用所有数据
        this.loading = true;
        const params = {
            limit: 9999,
            pageSize: 9999,
        };
        const res = await this.$ctFetch<any>(nCertificateUrl.certStatistics, {
            encodeParams: true,
            data: { ...params },
        });
        this.loading = false;
        //显示字段
        this.certCount = res.paging.total_record;
        this.willExpiredCount = res.willExpiredCount;
    }
    protected async go() {
        await checkCtiamButtonAuth(GetCtiamButtonAction("overviewCertCount"));
        this.$router.push("/certificate/list");
    }
}
</script>

<style lang="scss" scoped>
.cert-wrap {
    text-align: center;
    padding-top: 8px;
    .cert-count {
        display: inline-block;
        cursor: pointer;
        @include g-width(40%, 50%);
        span {
            font-size: 26px;
            color: $color-master;
            margin-right: 4px;
            &:hover {
                color: $color-master-hover;
            }
            &:active {
                color: $color-master-active;
            }
        }
    }
    .total-count {
        text-align: center;
    }
}
</style>
