@import '../adaptive/_index.scss';

// 文本溢出隐藏，需要设定 width ，建议设置元素 title 属性
.text-ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

// 文字换行
.break-all {
    word-break: break-all; // 连续英文会换行，但是连续的(((((((不会换行
    white-space: normal; // 默认文字间空白和换行
}

// 表单
$md-label-width: 140px;
$sm-label-width: 120px;

// .el-form {
//     .el-form-item {
//         margin-bottom: 18px;
//     }

//     // label
//     .el-form-item__label {
//         @include g-media-attr((attr: "float", sm: left, xs: none));
//         @include g-width(100%, $sm-label-width, $md-label-width);
//         @include g-media-attr((attr: "text-align", sm: right, xs: left));
//         // @include g-media-attr((attr: "line-height", sm: 32px, xs: 22px));
//     }

//     .el-form-item__content {
//         // 不应该使用 overflow：hidden 实现自适应，而应该是块级元素默认 100% 配合 margin实现
//         @include g-mg-lf(0, $sm-label-width, $md-label-width);

//         // 输入框不满行，提示语满行，好看些
//         >.el-input,
//         >.el-select,
//         >.el-textarea {
//             max-width: 700px;
//             @include g-width(100%, 80%, 60%);
//         }

//         .el-textarea {
//             margin: 4px 0;
//         }
//     }
// }

// 搜索条
.search-bar {
    // overflow: hidden;

    .search-btns {
        // float: right; // 不需要靠右了
        // @include g-display(block, inline-block);
        // @include g-media-attr((attr: 'text-align', sm: left, xs: right));
        margin-top: 20px;
    }

    >.el-input,
    >.el-select,
    .el-button,
    .el-range-editor {
        // margin-top: $common-space-2x;
        margin-right: $common-space-2x;
        // margin-bottom: $common-space-2x;
        vertical-align: middle;
    }

    .el-button+.el-button {
        margin-left: 0; // 上面增加了右边距，这里去掉默认的左边距
    }

    >.el-input,
    >.el-select {
        @include g-width(100%, 220px, 250px);
    }

    .el-date-editor--daterange {
        @include g-width(100%, 220px, 250px);
    }

    .el-range-separator {
        width: 30px;
    }

    .el-input-group__prepend {
        background-color: #fff;
        width: 50%;
    }
}

// 表格实现内容区滚动，此时需要搭配父容器作为 flex 容器
// 注意：使用该样式时，表格项不能设置 fixed 属性
.table-scroll-wrap {
    height: 100%;
    min-height: 450px;
    display: flex;
    flex-direction: column;
    border-radius: 3px;

    .el-table {
        flex: unset;
        // max-height: calc(100% - 110px);
        .el-table__header-wrapper {
            height: 40px;
        }

        .el-table__body-wrapper {
            height: calc(100% - 40px); // 需要去掉 table-header 高度
            overflow-y: auto;
        }
    }
}

// 多行文本最大行数
.multi-line-ellipsis {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
    max-height: 5em;
}

.el-pagination {
    text-align: right;
}

.gtm-w-full {
    width: 100%;
}

.el-tooltip__popper {
    max-width: 90%;
}

// 链接样式
.aocdn-ignore-link {
    cursor: pointer;
    color: $color-master;
}

.aocdn-ignore-anchor {
    color: $color-master;
    cursor: pointer;
}


@for $i from 0 through 70 {
    .mg#{$i} {
        margin: $i + px;
    }

    .mgt#{$i} {
        margin-top: $i + px;
    }

    .mgl#{$i} {
        margin-left: $i + px;
    }

    .mgb#{$i} {
        margin-bottom: $i + px;
    }

    .mgr#{$i} {
        margin-right: $i + px;
    }

    .mgy#{$i} {
        margin-top: $i + px;
        margin-bottom: $i + px;
    }

    .mgx#{$i} {
        margin-left: $i + px;
        margin-right: $i + px;
    }
}
