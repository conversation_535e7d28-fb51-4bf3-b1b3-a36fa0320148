import i18n from "@/i18n/index";
import { get } from "lodash-es";
import { configModuleRegistry } from '@/registry/configModuleRegistry';
import { ConfigModulesModule } from '@/store/modules/configModules';

export default {
    components: {},
    data() {
        return {
            activeNames: [""],
            domain_type: "",
        };
    },
    computed: {
        /**
         * 这个菜单只有 a1 -> 基础配置 入口才会使用
         */
        basicAnchorList() {
            const defaultList = [
                { label: i18n.t("domain.editPage.label1"), prop: "#div0" },
                { label: i18n.t("domain.detail.tab3"), prop: "#div1" },
                { label: i18n.t("domain.editPage.label12"), prop: "#div2" },
                { label: i18n.t("domain.detail.tab5"), prop: "#div3" },
                { label: i18n.t("domain.editPage.label14"), prop: "#div4" },
                { label: i18n.t("domain.detail.tab10"), prop: "#div5" },
                { label: i18n.t("domain.detail.label17"), prop: "#URLAuthentication" },
                { label: i18n.t("domain.editPage.label16"), prop: "#div6" },
                { label: i18n.t("domain.detail.ja3_fingerprint"), prop: "#div6_1" },
                { label: "websocket", prop: "#div7" },
                { label: i18n.t("domain.editPage.label30"), prop: "#htmlForbid" },
            ];

            // return defaultList;

            // 可插拔配置-基础锚点
            return this.isMasterSwitchEnabled ? this.mergePluginAnchors(defaultList) : defaultList;
        },
        /**
         * 折叠的锚点菜单
         * fcdn aocdn 需要隐藏的菜单在filter中进行处理
         */
        cdnAnchorListCollapse() {
            const isAocdn = this.isShowBasicConfigAndIsPoweredByQiankun.isPoweredByQiankun;
            const isFcdn = !isAocdn;

            const defaultList = [
                { label: i18n.t("domain.editPage.label1"), prop: "#div0" },
                // cdn 入口 小标题静态配置改为缓存配置
                { label: isAocdn ? i18n.t("domain.editPage.label27") : i18n.t("domain.detail.tab6"), prop: "#div8" },
                { label: i18n.t("domain.editPage.label28"), prop: "#div9" },
                { label: i18n.t("domain.editPage.label29"), prop: "#div10" },
                { label: i18n.t("domain.editPage.label31"), prop: "#video" },
            ];

            const cdnAnchorCollapse = defaultList.filter(itm => {
                if (isFcdn) {
                    // fcdn 旧框架不展示
                    if (
                        !this.isNewEcgw &&
                        [
                            i18n.t("domain.editPage.label31"),
                        ].includes(itm.label)
                    ) {
                        return false;
                    }

                    // 全站类产品需要特殊判断是否展示动态回源配置锚点
                    if (this.is_show_origin_type && itm.label === i18n.t("domain.editPage.label28")) {
                        return true;
                    }

                    // fcdn 不需要这些菜单
                    return ![
                        i18n.t("domain.editPage.label28"),
                        i18n.t("domain.editPage.label29"),
                    ].includes(itm.label);
                }

                if (isAocdn) {
                    // 静态加速开关关闭时，需要隐藏视频拖拉
                    if (
                        !this.isStaticsAbilityOn &&
                        [
                            i18n.t("domain.editPage.label31"),
                        ].includes(itm.label)
                    ) {
                        return false;
                    }
                }

                return true;
            });

            return this.isMasterSwitchEnabled ? this.mergePluginAnchors(cdnAnchorCollapse, true) : cdnAnchorCollapse;
        },
        /**
         * 展开的锚点菜单，包含 basicAnchorList
         * fcdn aocdn 需要隐藏的菜单在filter中进行处理
         */
        cdnAnchorListExpand() {
            const isAocdn = this.isShowBasicConfigAndIsPoweredByQiankun.isPoweredByQiankun;
            const isFcdn = !isAocdn;

            const defaultList = [
                { label: i18n.t("domain.editPage.label1"), prop: "#div0" },
                { label: i18n.t("domain.detail.tab3"), prop: "#div1" },
                { label: i18n.t("domain.editPage.label12"), prop: "#div2" },
                { label: i18n.t("domain.detail.tab5"), prop: "#div3" },
                { label: i18n.t("domain.editPage.label14"), prop: "#div4" },
                { label: i18n.t("domain.detail.tab10"), prop: "#div5" },
                { label: i18n.t("domain.detail.label17"), prop: "#URLAuthentication" },
                { label: i18n.t("domain.create.accessControl"), prop: "#deny" },
                { label: i18n.t("domain.editPage.label16"), prop: "#div6" },
                { label: i18n.t("domain.detail.ja3_fingerprint"), prop: "#div6_1" },
                { label: "websocket", prop: "#div7" },
                { label: i18n.t("domain.editPage.label30"), prop: "#htmlForbid" },
                // cdn 入口 小标题静态配置改为缓存配置
                { label: isAocdn ? i18n.t("domain.editPage.label27") : i18n.t("domain.detail.tab6"), prop: "#div8" },
                { label: i18n.t("domain.editPage.label28"), prop: "#div9" },
                { label: i18n.t("domain.editPage.label29"), prop: "#div10" },
                { label: i18n.t("domain.editPage.label31"), prop: "#video" },
                { label: i18n.t("domain.editPage.label34"), prop: "#advancedConf" },
                { label: "UDFScript", prop: "#UDFScript" },
            ];

            const  cdnAnchorExpand = defaultList.filter(itm => {
                // websocket 判断
                if (itm.label === "websocket") {
                    return this.isShowWebsocket;
                }

                // 访问控制 和 UDFScript 高级配置 单独判断
                if ([i18n.t("domain.create.accessControl"), "UDFScript"].includes(itm.label)) {
                    return this.showForStaticAndIcdn;
                }

                // fcdn 新框架 全站类/静态类 展示高级配置
                if ([i18n.t("domain.editPage.label34")].includes(itm.label)) {
                    return isFcdn && this.isNewEcgw && this.showForStaticAndIcdn;
                }

                if (isFcdn) {
                    // fcdn 旧框架不展示
                    if (
                        !this.isNewEcgw &&
                        [
                            i18n.t("domain.detail.label17"),
                            i18n.t("domain.editPage.label30"),
                            i18n.t("domain.editPage.label31"),
                            i18n.t("domain.detail.tab10"),
                        ].includes(itm.label)
                    ) {
                        return false;
                    }

                    // 全站类产品需要特殊判断是否展示动态回源配置锚点
                    if (this.is_show_origin_type && itm.label === i18n.t("domain.editPage.label28")) {
                        return true;
                    }

                    // fcdn 不需要这些菜单
                    return ![
                        i18n.t("domain.editPage.label28"),
                        i18n.t("domain.editPage.label16"),
                        i18n.t("domain.editPage.label29"),
                        i18n.t("domain.detail.ja3_fingerprint"),
                    ].includes(itm.label);
                }

                if (isAocdn) {
                    // 静态加速开关关闭时，需要隐藏视频拖拉
                    if (
                        !this.isStaticsAbilityOn &&
                        [
                            i18n.t("domain.editPage.label31"),
                        ].includes(itm.label)
                    ) {
                        return false;
                    }

                    // 访问控制是 fcdn 独有
                    return ![
                        i18n.t("domain.create.accessControl"),
                        i18n.t("UDFScript"),
                    ].includes(itm.label);
                }

                return true;
            });

            return this.isMasterSwitchEnabled ? this.mergePluginAnchors(cdnAnchorExpand) : cdnAnchorExpand;
        },
        /**
         * 获取当前配置和界面显示状态
         *
         * 此函数用于判断当前环境是否由Qiankun框架驱动，是否显示基础配置，以及是否使用新的Ecgw
         * 它通过检查全局变量和当前激活的界面名称来确定这些状态
         *
         * @returns {Object} 包含三个布尔值的对象：
         *          - isPoweredByQiankun: 当前环境是否由Qiankun框架驱动
         *          - isShowBasicConfig: 是否显示基础配置
         *          - isNewEcgw: 是否使用新的Ecgw
         */
        isShowBasicConfigAndIsPoweredByQiankun() {
            return {
                isPoweredByQiankun: window.__POWERED_BY_QIANKUN__,
                isShowBasicConfig: this.activeNames.includes("1"),
                isNewEcgw: this.defaultData?.use_ecgw === 1
            };
        },
        // 用于判断是否从基础配置菜单进来
        isUrlIncludeBasicConfig() {
            return window.location.hash.includes("basicConfig");
        },
        /**
         * 判断URL中是否包含特定的加速配置参数
         *
         * 此方法用于检查当前窗口位置的哈希值中是否包含名为"accelerationConfig"的参数
         * 它有助于确定URL中是否有与加速配置相关的参数
         *
         * @returns {boolean} 如果URL的哈希部分包含"accelerationConfig"，则返回true；否则返回false
         */
        isUrlIncludeAccelerationConfig() {
            return window.location.hash.includes("accelerationConfig");
        },
        /**
         * 判断是否显示WebSocket开关和输入框
         *
         * 乾坤入口 或者 域名类型等于全站加速-websocket时("105": "全站加速-websocket加速")， 才需要显示websocket开关和输入框
         * 这里的判断逻辑是基于当前环境是否由乾坤微前端框架提供，或者是特定的域名类型
         *
         * @returns {boolean} 如果条件满足，返回true，表示需要显示WebSocket开关和输入框；否则返回false
         */
        isShowWebsocket() {
            // 乾坤入口 或者 域名类型等于全站加速-websocket时("105": "全站加速-websocket加速")， 才需要显示websocket开关和输入框
            return window.__POWERED_BY_QIANKUN__ || this.defaultData?.domain_type === "105" && this.isNewEcgw;
        },
        isShowUploadSpeed() {
            return window.__POWERED_BY_QIANKUN__ || this.defaultData?.domain_type === "104" && this.isNewEcgw;
        },
        isMasterSwitchEnabled() {
            return ConfigModulesModule.isMasterSwitchEnabled;
        },
        /**
         * 检查当前模块是否已启用
         *
         * 该方法通过查询ConfigModulesModule模块来判断当前模块（通过moduleName指定）是否处于启用状态
         * 主要用于在执行其他操作前，快速验证模块的启用状态，以决定是否继续执行后续逻辑
         *
         * @returns {boolean} 返回当前模块是否已启用的状态
         */
        isEnabled() {
            return ConfigModulesModule.isModuleEnabled(this.moduleName);
        },
        // 可插拔配置-获取可插拔配置项的锚点
        pluginAnchors() {
            // 总开关关闭时不返回任何可插拔配置项的锚点
            if (!this.isMasterSwitchEnabled) {
                return [];
            }
            return Object.entries(configModuleRegistry)
                .filter(([module, config]) => {
                    if (!config.anchor) return false;
                    // 1. 模块必须启用
                    if (!this.isModuleEnabled(module)) return false;
                    // 2. 满足显示条件（如果有）
                    if (config.anchor.anchorCondition && !config.anchor.anchorCondition.call(this)) return false;
                    return true;
                })
                .map(([_, config]) => ({
                    label: this.$t(config.anchor.label),
                    prop: config.anchor.prop,
                    isInCollapsed: config.anchor.isInCollapsed,
                    isInExpanded: config.anchor.isInExpanded,
                }));
        },
    },
    methods: {
        /**
         * 计算锚点到顶部的距离
         */
        calculateContainerTop(doCalc) {
            if (!doCalc) return;
            const scroller = document.querySelector(".ct-config");
            this.containerToTop = get(scroller?.getBoundingClientRect(), "top", 0);
        },
        // 可插拔配置-合并可插拔配置项的锚点
        mergePluginAnchors(baseAnchors, isCollapsed = false) {
            // 如果总开关关闭，直接返回原始锚点
            if (!this.isMasterSwitchEnabled) {
                return baseAnchors;
            }

            const result = [...baseAnchors];

            this.pluginAnchors.filter(anchor => {
                if (anchor.isInCollapsed && isCollapsed) {
                    return true;
                }
                if (anchor.isInExpanded && !isCollapsed) {
                    return true;
                }

                return false
            }).forEach(anchor => {
                const config = Object.values(configModuleRegistry)
                    .find(c => c.anchor?.prop === anchor.prop);
                if (!config?.anchor?.position) {
                    // 没有指定位置，追加到末尾
                    result.push(anchor);
                    return;
                }

                const { position } = config.anchor;
                const targetIndex = result.findIndex(item => item.prop === position.target);
                if (targetIndex === -1) {
                    result.push(anchor);
                    return;
                }

                // 根据配置的位置插入
                const insertIndex = position.type === 'before' ? targetIndex : targetIndex + 1;
                result.splice(insertIndex, 0, anchor);
            });

            return result;
        }
    },
};
