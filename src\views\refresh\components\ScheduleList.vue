<template>
    <!-- 定时管理列表 -->
    <div>
        <div>
            <div class="search-bar-wrapper">
                <el-button class="el-icon-plus" type="primary" @click="showAdd">
                    {{ $t("refresh.createBtn") }}
                </el-button>
                <div class="search-bar">
                    <div class="search-bar-item">
                        <label>操作类型</label>
                        <el-tooltip placement="top" content="请选择操作类型">
                            <el-select v-model="refresh_type" class="refresh-form--selector" clearable>
                                <el-option value="url" :label="$t('refresh.common.tab[0]')" />
                                <el-option value="dir" :label="$t('refresh.common.tab[1]')" />
                                <el-option value="re" :label="$t('refresh.common.tab[2]')" />
                            </el-select>
                        </el-tooltip>
                    </div>
                    <div class="search-bar-item">
                        <label>{{ $t("refresh.search.label[0]") }}</label>
                        <el-date-picker
                            popper-class="aocdn-ignore-refresh-date-picker"
                            v-model="searchDate"
                            type="datetimerange"
                            range-separator="-"
                            :start-placeholder="$t('common.datePicker.start')"
                            :end-placeholder="$t('common.datePicker.end')"
                            :default-time="['00:00:00', '23:59:59']"
                            :picker-options="pickerOptions"
                        />
                    </div>
                    <div class="search-bar-item">
                        <label>{{ $t("refresh.search.label[1]") }}</label>
                        <el-tooltip placement="top" :content="$t('refresh.search.placeholder2')">
                            <el-input
                                class="custom-input"
                                v-model="domains"
                                clearable
                                :placeholder="$t('refresh.search.placeholder2')"
                            />
                        </el-tooltip>
                    </div>
                    <div class="search-bar-item">
                        <label>{{ $t("refresh.search.label[2]") }}</label>
                        <el-tooltip placement="top" :content="$t('refresh.search.placeholder3')">
                            <el-select
                                v-model="status"
                                clearable
                                :placeholder="$t('refresh.search.placeholder3')"
                                class="search-bar--status"
                            >
                                <el-option
                                    v-for="item in TaskStatusOptionsSchedule"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                />
                            </el-select>
                        </el-tooltip>
                    </div>
                    <div class="search-bar-item">
                        <el-button type="primary" plain @click="searchByBar">{{
                            $t("common.search.start")
                        }}</el-button>
                        <el-button @click="reset">{{ $t("common.search.reset") }}</el-button>
                    </div>
                </div>
            </div>
            <el-table :data="scheduleDataList" v-loading="loading">
                <el-table-column
                    :label="$t('refresh.table.label[0]')"
                    type="index"
                    width="60"
                    align="center"
                />
                <el-table-column label="操作类型" prop="refresh_type" width="80">
                    <template slot-scope="{ row }">
                        {{ refreshTypeMap[row.refresh_type] || row.refresh_type }}
                    </template>
                </el-table-column>

                <el-table-column label="任务类型" prop="task_type" width="70">
                    <template slot-scope="{ row }">
                        {{ row.task_type == 1 ? "预约" : "循环" }}
                    </template>
                </el-table-column>
                <el-table-column label="刷新时间" prop="plan_time" width="200">
                    <template slot-scope="{ row }">
                        {{ row | planFilter }}
                    </template>
                </el-table-column>
                <el-table-column label="刷新频率" show-overflow-tooltip min-width="130">
                    <template slot-scope="{ row }">
                        {{ row.plan_time | frequencyFilter }}
                    </template>
                </el-table-column>
                <el-table-column :label="$t('refresh.table.label[1]')" prop="urls" min-width="320">
                    <template slot-scope="{ row }">
                        <div class="urls-panel">
                            <div class="ellipsis-panel">
                                {{ (Array.isArray(row.urls) ? row.urls : []).join(",").slice(0, 1000) }}
                            </div>
                            <el-link @click="showUrls(row)" v-if="row.urls && row.urls.length">查看</el-link>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="操作时间" prop="update_time" width="140" />
                <el-table-column :label="$t('refresh.table.label[3]')" width="100">
                    <template slot-scope="{ row }">
                        <cute-state :type="statusMap.get(row.status) || 'info'">
                            {{ row.status | taskStateFilter }}
                        </cute-state>
                    </template>
                </el-table-column>
                <el-table-column :label="$t('common.table.operation')" width="140">
                    <template slot-scope="{ row }">
                        <el-button type="text" @click="onEdit(row)"> 编辑 </el-button>
                        <el-button v-if="row.status === 1" type="text" @click="onDisable(row)">
                            停用
                        </el-button>
                        <el-button v-if="row.status === 0" type="text" @click="onEnable(row)">
                            启用
                        </el-button>
                        <el-button type="text" @click="onDel(row)"> 删除 </el-button>
                    </template>
                </el-table-column>
            </el-table>
            <div class="pager">
                <el-pagination
                    :small="isXs"
                    :layout="isXs ? 'prev, pager, next' : 'total, sizes, prev, pager, next,jumper'"
                    :total="totalRecord"
                    :current-page.sync="page"
                    :page-size="page_size"
                    :page-sizes="[10, 30, 50, 100]"
                    :hide-on-single-page="false"
                    @size-change="sizeChange"
                ></el-pagination>
            </div>
        </div>

        <!-- 交行客户需求 支持预约刷新和循环刷新，为减少测试范围，跟其他 tab 的创建弹窗独立开 -->
        <create-dialog-new
            :addVisible="taskDialog.visible"
            :taskId="taskDialog.taskId"
            :dialogLoading="dialogLoading2"
            :addTask="addTask2"
            @cancel="cancel2"
            @submit="submitTask2"
        />

        <show-schduel-url-list
            :urls="showUrlsDialog.urls"
            :dialogVisible="showUrlsDialog.visible"
            @close="() => (showUrlsDialog.visible = false)"
        />
    </div>
</template>

<script lang="ts">
import { Component, Vue, Watch } from "vue-property-decorator";
import { ScreenModule } from "@/store/modules/screen";
import { refreshUrl } from "@/config/url";
import { ScheduleItem } from "@/types/refresh";
import { TaskSearchParams2, TaskStatusSchedule, RefreshType2 } from "@/types/refresh";
import {
    TaskStatusMapSchedule,
    RefreshTypeEnum2,
    TaskStatusOptionsSchedule,
    GetCtiamButtonAction,
} from "@/config/map";
import CreateDialogNew from "./CreateDialogNew.vue";
import ShowSchduelUrlList from "./showUrls.vue";
import { shortcuts_obj } from "@/utils/pickerOption";
import { CtiamCode } from "@/utils/ctFetch/errorConfig";
import { checkCtiamButtonAuth } from "@/store/modules/menu";
import { getDefaultSchedualTask, SchduelFrequencyEnum, SchduelTaskParam } from "../util";
import { ElDatePicker } from "@cutedesign/ui";

type taskParam = { refresh_type: RefreshType2; content: string };

@Component({
    filters: {
        taskStateFilter(input: TaskStatusSchedule): string {
            return TaskStatusMapSchedule[input];
        },
        planFilter(row: any) {
            if (row.task_type === 2) {
                // 循环任务
                return `${row.plan_time.eff_date}~${row.plan_time.exp_date} ${row.plan_time.plan_time}`;
            }
            return row.plan_time.plan_datetime; // 预约任务
        },
        frequencyFilter(plan_time: any) {
            if (plan_time.cron_everyday === 1) {
                return "每天";
            } else if (plan_time.cron_week) {
                const weekMap = ["日", "一", "二", "三", "四", "五", "六"];
                const cron_week = plan_time.cron_week.split(",").map(Number)
                if (cron_week.includes(0)) {
                    // 0 代表周日，放到最后
                    const zeroIndex = cron_week.indexOf(0);
                    cron_week.splice(zeroIndex, 1);
                    cron_week.push(0);
                }
                return `每周${cron_week
                    .map((num: any) => weekMap[num])
                    .join("、")}`;
            } else if (plan_time.cron_monday) {
                return `每月${plan_time.cron_monday.split(",").join("、")}日`;
            }
        },
    },
    components: { CreateDialogNew, ShowSchduelUrlList },
})
export default class Task extends Vue {
    private pickerOptions = {
        shortcuts: Object.keys(shortcuts_obj)
            .filter(key =>
                [
                    "recent_15_minutes",
                    "recent_1_hour",
                    "recent_6_hours",
                    "recent_24_hours",
                    "recent_1_week",
                ].includes(key)
            )
            .map(key => ({
                text: (shortcuts_obj as Record<string, { text: string }>)[key].text,
                onClick: (picker: ElDatePicker) => {
                    const end = new Date();
                    const start = new Date();
                    start.setTime(start.getTime() - this.getTimeOffset(key));
                    end.setHours(23, 59, 59);
                    picker.$emit("pick", [start, end]);
                },
            })),
    };

    private getTimeOffset(key: string): number {
        const timeMap: Record<string, number> = {
            recent_15_minutes: 15 * 60 * 1000,
            recent_1_hour: 3600 * 1000,
            recent_6_hours: 3600 * 1000 * 6,
            recent_24_hours: 3600 * 1000 * 24,
            recent_1_week: 3600 * 1000 * 24 * 7,
        };
        return timeMap[key] || 0;
    }
    taskDialog = {
        visible: false,
        taskId: 0,
    };
    showUrlsDialog = {
        visible: false,
        urls: [],
    };
    loading = false;
    dialogLoading2 = false;
    scheduleDataList: ScheduleItem[] = [];

    private domains = ""; // 搜索条件
    private status = ""; // 任务状态
    private searchDate: Date[] = [];
    protected refresh = false;
    totalRecord = 0;
    page = 1;
    page_size = 10;
    refresh_type = "";

    refreshTypeMap = {
        url: "URL 刷新",
        dir: "目录刷新",
        re: "正则刷新",
    };

    type = RefreshTypeEnum2.Schedule;
    addTask2: SchduelTaskParam = getDefaultSchedualTask();

    TaskStatusOptionsSchedule = TaskStatusOptionsSchedule;
    get isXs() {
        return this.screenWidth < 600;
    }

    get screenWidth() {
        return ScreenModule.width;
    }

    getTimeFromDate(d: Date): string {
        const y = d.getFullYear();
        const m = d.getMonth();
        const fullM = m >= 9 ? `${m + 1}` : `0${m + 1}`;
        const date = d.getDate() < 10 ? "0" + d.getDate() : d.getDate();
        const hours = d.getHours() < 10 ? "0" + d.getHours() : d.getHours();
        const minutes = d.getMinutes() < 10 ? "0" + d.getMinutes() : d.getMinutes();
        const seconds = d.getSeconds() < 10 ? "0" + d.getSeconds() : d.getSeconds();
        return `${y}-${fullM}-${date} ${hours}:${minutes}:${seconds}`;
    }

    // 重写 mixin 定义
    get searchParams() {
        const { domains, searchDate, status, refresh_type } = this;

        const params: TaskSearchParams2 = {
            start_time: searchDate?.[0] ? this.getTimeFromDate(searchDate[0]) : "",
            end_time: searchDate?.[1] ? this.getTimeFromDate(searchDate[1]) : "",
            url: domains,
            status,
            refresh_type,
        };
        return params;
    }

    // 根据status获取cute-state的type
    get statusMap() {
        return new Map([
            [0, "danger"],
            [1, "success"],
            [2, "info"],
        ]);
    }

    @Watch("page")
    onPageChange() {
        this.fetchList();
    }

    searchByBar() {
        this.fetchList();
    }

    reset() {
        this.page = 1;
        this.page_size = 10;
        this.domains = "";
        this.status = "";
        this.searchDate = [];
        this.refresh_type = "";
        this.fetchList();
    }

    mounted() {
        this.fetchList();
    }

    cancel2() {
        this.taskDialog.visible = false;
    }

    async showAdd() {
        await checkCtiamButtonAuth(GetCtiamButtonAction("refreshCreate"));
        this.taskDialog.visible = true;
        this.taskDialog.taskId = 0;
        this.addTask2 = getDefaultSchedualTask();
    }

    // 查询
    async fetchList() {
        this.loading = true;
        const params = {
            ...this.searchParams,
            page_size: this.page_size,
            page: this.page,
        };
        console.log("params:", params);
        try {
            const rst = await this.$ctFetch<{ list: ScheduleItem[]; total: number }>(
                refreshUrl.scheduleList,
                {
                    method: "POST",
                    body: {
                        data: params,
                    },
                }
            );
            this.scheduleDataList = rst.list;
            this.totalRecord = rst.total;
        } catch (err) {
            const isCtiamError = CtiamCode.includes((err as any)?.data?.code);
            if (isCtiamError) {
                // 权限报错时清空数据
                this.scheduleDataList = [];
            }
            this.$errorHandler(err);
        } finally {
            this.loading = false;
        }
    }

    sizeChange(val: number) {
        // 恢复到第一页
        this.page = 1;
        this.page_size = val;
        this.fetchList();
    }

    async submitTask2(task: SchduelTaskParam) {
        try {
            this.dialogLoading2 = true;
            await this.$ctFetch(
                this.taskDialog.taskId
                    ? refreshUrl.schduelRefreshTaskUpdate
                    : refreshUrl.schduelRefreshTaskCreate,
                {
                    method: "POST",
                    body: {
                        data: task,
                    },
                    clearEmptyParams: false,
                }
            );

            this.fetchList();
            this.$message.success(this.$t("refresh.create.msg6-1") as string);
            this.taskDialog.visible = false;
        } catch (error) {
            this.$errorHandler(error);
        } finally {
            this.dialogLoading2 = false;
        }
    }

    async onEdit(row: any) {
        this.taskDialog.taskId = row.id;
        this.taskDialog.visible = true;
        this.addTask2 = {
            refresh_type: row.refresh_type,
            task_type: row.task_type,
            plan_time: row.plan_time?.plan_time || "",
            plan_datetime: row.plan_time?.plan_datetime || "",
            eff_date: row.plan_time?.eff_date || "",
            exp_date: row.plan_time?.exp_date || "",
            loop_date: [row.plan_time?.eff_date || "", row.plan_time?.exp_date || ""],
            loop_frequency: row.plan_time?.cron_week
                ? SchduelFrequencyEnum.cron_week
                : row.plan_time?.cron_monday
                ? SchduelFrequencyEnum.cron_monday
                : SchduelFrequencyEnum.cron_everyday,
            cron_everyday: row.plan_time?.cron_everyday || 0,
            cron_week:
                row.plan_time?.cron_week
                    ?.split(",")
                    .filter((itm: string) => !!itm)
                    .map(Number) || [],
            cron_monday:
                row.plan_time?.cron_monday
                    ?.split(",")
                    .filter((itm: string) => !!itm)
                    .map(Number) || [],
            content: row.urls?.join("\n"),
        };
    }
    /**
     * 修改任务状态
     * @param id 任务id
     * @param modify -1:删除 0:停用 1:启用
     */
    async modifyTaskStatus(id: string, modify = -1 | 0 | 1) {
        const textMap: Record<string, string> = {
            "-1": "确认删除该任务吗？",
            "0": "确认停用该任务吗？",
            "1": "确认启用该任务吗？",
        };
        const titleMap: Record<string, string> = {
            "-1": "删除",
            "0": "停用",
            "1": "启用",
        };

        this.$confirm(textMap[`${modify}`], "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
            beforeClose: async (action, instance, done) => {
                if (action === "confirm") {
                    instance.confirmButtonLoading = true;
                    instance.confirmButtonDisabled = true;
                    try {
                        await this.$ctFetch(refreshUrl.schduelRefreshTaskStatusUpdate, {
                            method: "POST",
                            body: {
                                data: {
                                    id,
                                    modify,
                                },
                            },
                        });
                        this.fetchList();
                        this.$message.success(`任务${titleMap[`${modify}`]}成功`);
                        done();
                    } catch (err) {
                        this.$errorHandler(err);
                    }
                } else {
                    done();
                }

                instance.confirmButtonLoading = false;
                instance.confirmButtonDisabled = false;
            },
        });
    }
    async onEnable(row: any) {
        this.modifyTaskStatus(row.id, 1);
    }
    async onDisable(row: any) {
        this.modifyTaskStatus(row.id, 0);
    }

    async onDel(row: any) {
        this.modifyTaskStatus(row.id, -1);
    }

    showUrls(row: any) {
        this.showUrlsDialog.urls = row.urls;
        this.showUrlsDialog.visible = true;
    }
}
</script>
<style lang="scss" scoped>
.search-bar-wrapper {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 8px;
}
.search-bar {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
    flex-shrink: 0;
    &-item {
        display: flex;
        gap: 12px;
        align-items: center;
        label {
            flex-shrink: 0;
            font-size: 12px;
        }
    }
    .search-bar--status {
        width: 150px;
    }
    .custom-input {
        ::v-deep {
            .el-input__inner {
                padding-right: 12px;
            }
        }
    }
}

@media screen and (max-width: 1820px) {
    .search-bar {
        width: 100%;
        .search-bar {
            width: 100%;
        }
    }
}
// 状态颜色
.status-2 {
    color: $g-color-blue;
}

.status-3 {
    color: $g-color-green;
}

.status-4 {
    color: $g-color-red;
}

.status-1 {
    color: $g-color-gray;
}
.pager {
    margin-top: 8px;
    ::v-deep.el-pagination {
        text-align: right !important;
    }
}
.table-scroll-wrap {
    padding: 20px;
}
.urls-panel {
    display: flex;
    align-items: center;
    gap: 4px;
}
.ellipsis-panel {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: calc(100% - 40px);
}
</style>
<style lang="scss">
.aocdn-ignore-refresh-date-picker {
    > .el-picker-panel__footer .el-picker-panel__link-btn.el-button--text {
        display: none;
    }
}
</style>
