<template>
    <el-dialog
        :title="dialogTitle"
        :append-to-body="true"
        :close-on-click-modal="false"
        :modal-append-to-body="false"
        :visible="visible"
        custom-class="aocdn-cache-extensions-dialog"
        :before-close="() => $emit('cancel')"
    >
        <div
            class="aocdn-cache-extensions"
            v-for="(val, key) in extensionsOptions"
            :key="val.title"
        >
            <div class="extension-title">
                <el-checkbox
                    v-model="extensionAllSelected[key]"
                    @change="checkAllChange(key)"
                    :label="val.title"
                />
            </div>
            <div class="extension-list">
                <el-checkbox-group
                    v-model="extensionSelected[key]"
                    @change="checkSingleChange(key)"
                >
                    <el-checkbox
                        v-for="extension in val.list"
                        :label="extension"
                        :key="extension"
                    />
                </el-checkbox-group>
            </div>
        </div>
        <div class="aocdn-cache-extensions">
            <div class="extension-title">{{ $t("domain.detail.label40") }}</div>
            <div class="extension-list">
                <el-input
                    type="textarea"
                    :rows="2"
                    :placeholder="$t('domain.detail.placeholder1')"
                    v-model="extensionOther"
                />
            </div>
        </div>
        <div slot="footer" class="dialog-footer">
            <el-button @click="$emit('cancel')">{{
                $t("common.dialog.cancel")
            }}</el-button>
            <el-button type="primary" @click="setName">{{
                $t("common.dialog.submit")
            }}</el-button>
        </div>
    </el-dialog>
</template>

<script>
import { extensionsOptions } from "../simpleform/nAlogicCacheTable/config";

const extensionsMap = {};
Object.keys(extensionsOptions).forEach(key => {
    extensionsOptions[key].list.forEach(k => {
        extensionsMap[k] = key;
    });
});

export default {
    name: "fileSuffixDialog",
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        form: {
            type: Object,
            default: () => {
                return {};
            },
        },
        title: String,
    },
    data() {
        return {
            extensionAllSelected: {
                dynamic: false,
                image: false,
                style: false,
                av: false,
                download: false,
                page: false,
            },
            extensionSelected: {
                dynamic: [],
                image: [],
                style: [],
                av: [],
                download: [],
                page: [],
            },
            extensionOther: "",
            extensionsOptions,
        };
    },
    watch: {
        visible(val) {
            if (!val) return;
            this.showNameSet(this.form);
        },
    },
    methods: {
        showNameSet(cache) {
            // 清掉缓存
            Object.keys(this.extensionAllSelected).forEach(key => {
                this.extensionAllSelected[key] = false;
                this.extensionSelected[key] = [];
            });
            const other = [];
            // 过滤有复选框的后缀名
            (cache.content || "")
                .split(",")
                .filter(val => val)
                .forEach(item => {
                    if (extensionsMap[item]) {
                        this.extensionSelected[extensionsMap[item]].push(item);
                    } else {
                        other.push(item);
                    }
                });
            // 其他后缀名
            this.extensionOther = other.join(",");
            // 全选按钮是否选中
            Object.keys(this.extensionSelected).forEach(key => {
                this.extensionAllSelected[key] =
                    new Set(this.extensionSelected[key]).size ===
                    extensionsOptions[key].list.length;
            });
        },
        // 全选按钮逻辑
        checkAllChange(key) {
            const val = this.extensionAllSelected[key];
            this.extensionSelected[key] = val ? extensionsOptions[key].list : [];
        },
        // 选项是否触发全选
        checkSingleChange(key) {
            const val = this.extensionSelected[key];
            this.extensionAllSelected[key] =
                val.length === extensionsOptions[key].list.length;
        },
        setName() {
            const extentionReg = new RegExp("^\\w{1,9}(?:,\\w{1,9})*$|^$");
            if (!extentionReg.test(this.extensionOther)) {
                this.$message.error(this.$t("domain.detail.tip30"));
                return;
            }

            // 选中复选框的后缀
            const extensions = Object.keys(this.extensionSelected)
                .reduce((rst, key) => {
                    if (this.extensionSelected[key].length > 0) {
                        return rst + "," + this.extensionSelected[key].join(",");
                    }
                    return rst;
                }, "")
                .slice(1);

            // 没有更多，则直接用计算出的结果；如果有更多，则进行拼接
            this.$emit(
                "submit",
                !this.extensionOther
                    ? extensions
                    : `${extensions}${extensions ? "," : ""}${this.extensionOther}`
            );
        },
    },
    computed: {
        dialogTitle() {
            return this.title || this.$t('domain.detail.cacheModeMap.0')
        }
    }
};
</script>
