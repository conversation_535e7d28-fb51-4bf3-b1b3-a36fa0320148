import { RouteConfig } from "vue-router";

const indexRouter: RouteConfig[] = [
    {
        path: "/udfMicroApp/skeletonPage",
        name: "udfMicroApp.skeletonPage",
        component: () => import("./developerPlatform/index.vue"),
        meta: {
            isOnline: true,
        },
        beforeEnter(to: any, from: any, next: any) {
            // 通知主应用重置状态，避免同域名在加速配置和cdn配置互相跳转时导致的状态不正确
            // if (!MicroAppModule.showEdgeFunctionMenu) {
            //     next({
            //         name: "nhome",
            //     })
            // } else if (MicroAppModule.edgeFunctionEnable) {
            //     next({
            //         path: "/udfMicroApp/overview",
            //     })
            // }
            next();
        },
    },
];

export default indexRouter;
