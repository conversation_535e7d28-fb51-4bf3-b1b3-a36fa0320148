/*
 * @Description: 单位换算的过滤器
            注意：流量类的进制换算需要需要从后端获取 scale
 * @Author: wang y<PERSON><PERSON>
 */

import { TenThousandUnit, ThousandUnit } from "@/types/common";
import { TenThousandUnits, ThousandUnits } from "@/config/map";
import { nUserModule } from "@/store/modules/nuser";
import { timeFormat as _timeFormat } from "@cdnplus/common/filters";
import { BandwidthUnitB2P, BandwidthUnitM2P, BandwidthUnits, FlowUnitB2P, FlowUnitG2P, FlowUnitM2P, FlowUnits } from "@/types/statistics/unit";
import { getIndentation } from "@/utils";

// 万进制转换（只有整数）
const tenThousandIndentation = getIndentation<TenThousandUnit>(TenThousandUnits, {
    type: "number",
    digits: 2,
});
// 千进制转换（只有整数）
const thousandIndentation = getIndentation<ThousandUnit>(ThousandUnits, {
    type: "number",
    digits: 2,
});

export function convertTenThousand2Int(number: number | string, suffix = "") {
    const { num, unit } = nUserModule.lang === "en" ? thousandIndentation(number, 1000) : tenThousandIndentation(number, 10000);

    // 有单位则不处理（保留小数），无单位则处理为整数
    const _num = unit ? num : (+num * 1).toFixed(0);
    return `${_num} ${unit}${suffix}`;
}

export function timeFormat(value: string | number, replaceStr = "") {
    // 防止传入空字符串或者数字0
    if (!value) {
        return replaceStr;
    }
    return _timeFormat(value, replaceStr);
}

/**
 * 带宽/流量转换过滤器，由于使用到过滤器的仅总数据，一般这种数据都是要求 M 为最小单位，且只需要输出 数字+单位的格式
 * common包中的getIndentation方法没有对scale为1024的场景对单位进行处理，这里引用unit.ts中改写过的方法
 * @param data 统计数值
 * @param scale 缩进比例（正常由后端获取，默认 1000）
 */
const bandwidthM2PIndentation = getIndentation<BandwidthUnitM2P>(BandwidthUnits.M2P);
export function convertBandwidthM2P(data = 0, scale = 1000) {
    return bandwidthM2PIndentation(data, scale).result;
}

const flowM2PIndentation = getIndentation<FlowUnitM2P>(FlowUnits.M2P);
export function convertFlowM2P(data = 0, scale = 1000) {
    return flowM2PIndentation(data, scale).result;
}

const bandwidthB2PIndentation = getIndentation<BandwidthUnitB2P>(BandwidthUnits.B2P);
export function convertBandwidthB2P(data = 0, scale = 1000) {
    return bandwidthB2PIndentation(data, scale).result;
}

const flowB2PIndentation = getIndentation<FlowUnitB2P>(FlowUnits.B2P);
export function convertFlowB2P(data = 0, scale = 1000) {
    return flowB2PIndentation(data, scale).result;
}

export const flowG2PIndentation = getIndentation<FlowUnitG2P>(FlowUnits.G2P);

