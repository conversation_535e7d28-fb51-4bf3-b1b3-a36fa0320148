import { SYS_ROUTE_PREFIX, LOGIC_ROUTE_PREFIX, NEW_PREFIX, PROXY_PREFIX } from "./_PREFIX";

// Current接口
export const CurrentUrl = SYS_ROUTE_PREFIX + "/ctyun/current";
// bcp登录接口
export const BcpLoginUrl = `/cdn/ctyun/signin`;

// 登录接口
export const LoginUrl = SYS_ROUTE_PREFIX + `/sign/in?returnUrl=${encodeURIComponent(window.location.href)}`;
// 登出接口
export const LogoutUrl = SYS_ROUTE_PREFIX + `/ctyun/logout`;
// 菜单接口（有鉴权）
export const MenuUrl = SYS_ROUTE_PREFIX + "/ctyun/menu";
// ctiam 菜单接口
export const CtiamMenuUrl = SYS_ROUTE_PREFIX + "/v1/ctiam/menu/GetTree";
// ctiam 按钮权限接口
export const CtiamMenuAuthUrl = SYS_ROUTE_PREFIX + "/v1/ctiam/check/authList";
// ctiam 資源权限校验
export const CtiamResourceCheckUrl = SYS_ROUTE_PREFIX + "/v1/ctiam/check/resource";
// banner 接口（用于无鉴权的菜单配置）
export const BannerList = LOGIC_ROUTE_PREFIX + "/banner/GetDomain";
// IAM 个人中心
export const WorkspaceListUrl = SYS_ROUTE_PREFIX + "/ctyun/wlist";

// 获取域名列表（权限控制 do + 数据来源 from）
export const DomainList = LOGIC_ROUTE_PREFIX + "/product/instance/Selector";
// 获取产品列表
export const ProductList = LOGIC_ROUTE_PREFIX + "/product/package/List";

// 操作按钮权限（第一层）
export const OperationList = LOGIC_ROUTE_PREFIX + "/form/operation/List";

// 获取系统进制转换系数
export const ScaleUrl = LOGIC_ROUTE_PREFIX + "/billing/GetScale";

// 信息列表
export const MsgList = LOGIC_ROUTE_PREFIX + "/bulletin/List";
export const GetBBS = LOGIC_ROUTE_PREFIX + "/bulletin/GetBBS";
export const ListBBS = LOGIC_ROUTE_PREFIX + "/bulletin/ListBBS";

// 用户类型
export const QueryUserInfo = `${LOGIC_ROUTE_PREFIX}/billing/QueryUserInfo`;

// cdn 专有
// 获取系统进制转换系数
export const nScaleUrl = NEW_PREFIX + "/billing/GetScale";
// 获取产品列表
export const nProductList = NEW_PREFIX + "/basic/product";
// 判断是否开通套餐或者拥有有效资源包
export const basicPackage = NEW_PREFIX + "/basic/package";
// 用户类型
export const nQueryUserInfo = NEW_PREFIX + "/billing/QueryUserInfo";
// banner 接口（用于无鉴权的菜单配置）
export const nBannerList = NEW_PREFIX + "/banner/GetDomain";
export const nListBBS = NEW_PREFIX + "/bulletin/ListBBS";
export const nGetBBS = NEW_PREFIX + "/bulletin/GetBBS";
export const nMsgList = NEW_PREFIX + "/bulletin/List";

export const nBasicConfig = NEW_PREFIX + "/basic/config";
// 子账号
export const checkChildAccount = NEW_PREFIX + "/basic/checkChildAccount";

// 获取产品进制和系数
export const getRatioAndScale = NEW_PREFIX + "/billing/getRatioAndScale";

// 获取模块状态
export const getModulesStatus = NEW_PREFIX + "/basic/grayscale";