import "@/microApp/public-path";
import { render } from "@/microApp/lifeCycle";
export { bootstrap, mount, update, unmount } from "@/microApp/lifeCycle";

import Vue from "vue";
import { sync } from "vuex-router-sync";
import CuteComponent from "@cutedesign/ui";
// import AiOps from "@cutedesign/aiops-web";

import store from "./store";
import router, { utils as routerUtils } from "./router";
import utils, { redirectEntryUrl } from "./utils";
import { errorHandler } from "./utils/ctFetch/errorHandler";
import ctFetch from "@/utils/https.js";
import CtBreadcrumb from "@/components/CtBreadcrumb-mod.vue";
import { ScreenModule } from "@/store/modules/screen";
import { nUserModule } from "@/store/modules/nuser";
import dayjs from "dayjs";
import xss from "xss";
// import "@/assets/iconfont/iconfont.css";
import "@/icons";
import * as filters from "@/filters";

// 全局组件;
import "@cdnplus/common/components/index";
import "./components/index";
Vue.component("CtBreadcrumb", CtBreadcrumb);
// 静态资源
import "./assets/css/common.scss";
import "@cdnplus/common/assets/icon/iconfont.css";
import "@cutedesign/ui/lib/index.css";
import { loadCss, loadJs } from "./utils";

import * as echarts from "echarts";
import urlTransformer from "./utils/logic/url";
import docHelp from "./utils/logic/docHelp";

import "./qiankun/index.js";
import { MessageBoxConfirmWrapper } from "./utils/element/overwrite";

import "@/assets/fonts/iconfont.css";

Vue.prototype.$echarts = echarts;
// 增加bus通信
Vue.prototype.$bus = new Vue();
// 增加时间工具
Vue.prototype.$dayjs = dayjs;
// 放置xss攻击工具
Vue.prototype.xss = xss;
// 链接转换
Vue.prototype.$urlTransformer = urlTransformer;
// 打开文档悬浮框
Vue.prototype.$docHelp = docHelp;
Vue.prototype.$PoweredByQiankun = window.__POWERED_BY_QIANKUN__

class Main {
    constructor() {
        redirectEntryUrl();
        this.init();
    }

    init() {
        console.log("main--inint");
        //使用插件
        this.useVuePlugin();
        // 初始化配置
        this.initialCfg();
        // 注册 vue 插件之类的
        this.registerVue();
        //初始化Vue
        this.instanceVue();
        // 其他事件
        this.otherEvents();
    }

    //初始化配置
    initialCfg() {
        Vue.config.productionTip = false;

        // vuex-router同步
        sync(store, router);

        const plugins = {
            // 事件总线
            $ctBus: new Vue(),
            // 数据请求
            $ctFetch: ctFetch,
            // 数据异常处理
            $errorHandler: errorHandler,
            // 工具链
            $ctUtil: {
                ...utils,
                ...routerUtils,
            },
        };
        // 增加到原型链
        Object.assign(Vue.prototype, plugins);
    }

    //初始化Vue
    async instanceVue() {
        // 实例化vue-非qiankun
        if (!(window as any).__POWERED_BY_QIANKUN__) {
            const cssUrl = `/fcdn/ctyun/layoutStyle`;
            const jsUrl = `/fcdn/ctyun/layoutScript`;
            await Promise.all([loadCss(cssUrl), loadJs(jsUrl)]);
            render({});
            // ctyun环境下CtcloudLayout版本更新需要替换
            if (nUserModule.isCtyun && (window as any).CtcloudLayoutV2) {
                window.CtcloudLayout = (window as any).CtcloudLayoutV2;
            }
        }
        // 全局注册
        (window as any).$docHelp = docHelp;
    }

    // vue 相关注册
    registerVue() {
        Vue.use(CuteComponent);
        // Vue.use(AiOps, {
        //     serviceName: process.env.NODE_ENV === "production" ? "cdn-front" : "cdn-front-test", // 应用标识
        //     instrumentation: {
        //         router, // Vue Router路由实例
        //         xmlHttpRequest: true, // // 是否开启ajax请求监测
        //         routeLoad: true, // 是否开启路由切换追踪
        //         documentLoad: true, // 是否开启页面加载监测
        //         FIDLoad: true, // 是否开启FID监测
        //         FCPLoad: true, // 是否开启FCP监测
        //         LCPLoad: true, // 是否开启LCP监测
        //         FSPLoad: true, // 是否开启FSP监测
        //         CLSLoad: false, // 是否开启CLS监测。默认关闭，开启此项将产生较多性能数据上报，可按需开启
        //         TTSLoad: true, // 是否开启TTS(用户停留时间)监测
        //         BRLoad: true, // 是否开启BR(跳出率)上报，若开启建议传入Vue Router实例，以提高准确率
        //         errorHandler: true, // 是否开启异常捕获上报
        //         vueErrorSlience: false, // 是否隐藏浏览器控制台中的vue组件级别的异常捕获
        //         routerTitleKey: "meta.title", // 路由标题Key, 默认meta.title
        //     },
        // });

        // 注册全局过滤器
        Object.keys(filters).forEach(key => {
            Vue.filter(key, (filters as { [key: string]: Function })[key]);
        });

        // 覆盖element-ui的MessageBox.confirm方法
        Vue.prototype.$confirm = MessageBoxConfirmWrapper;
    }

    // 其他
    otherEvents() {
        const resizeFn = () => {
            const screenWidth = document.documentElement.clientWidth;
            ScreenModule.SET_SCREEN_SIZE(screenWidth);
        };
        resizeFn();
        window.addEventListener(
            "resize",
            utils.debounce(resizeFn, {
                delay: 100,
            })
        );
    }

    //使用插件
    useVuePlugin() {
        //统计日志插件
        if (window.CtcloudAnalysis) {
            window.CtcloudAnalysis.install(Vue, {
                router,
            });
        }
    }
}

new Main();
