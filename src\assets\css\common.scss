// 提供通用的 ele 主题和全局自定义样式
// @import '@cdnplus/common/assets/css/theme/element-var.scss';
// @import '@cdnplus/common/assets/css/theme/element-reset.scss';
@import './theme/element-var.scss';
@import './theme/element-reset.scss';
// @import '@cdnplus/common/assets/css/global/index.scss';
@import './global/index.scss';

// 根据子项目需求局部重写或补充（如果需要全部重写，则在当前项目 src/assets 下使用同名同路径文件覆盖引用）
@import './qiankun.shim.scss';

.el-form {
    .el-form-item {
        .el-form-item__error {
            position: relative;
        }
    }
}

// 消除微应用，el-dialog下的 form 也需要相对定位的报错提示
// 经过测试，微应用内部似乎存在head style 缓存，这里的样式并不会影响到主应用
.el-dialog {
    .el-form {
        .el-form-item {
            .el-form-item__error {
                position: relative;
            }
        }
    }
}

// 主应用的最大宽度90%，这里不会影响主应用
.el-tooltip__popper {
    max-width: 70%;
}
