import Index from "./index.vue";
import { RouteConfig } from "vue-router";
const certificateRouter: RouteConfig[] = [
    {
        path: "/certificate",
        name: "certificate",
        component: Index,
        meta: {
            breadcrumb: {
                title: '$t("certificate.title1")',
                route: ["certificate"],
            },
            perm: "certificate",
        },
        beforeEnter(to, from, next) {
            next();
        },
        children: [
            {
                path: "list",
                name: "certificate.list",
                component: () => import("./certificateList.vue"),
                meta: {
                    breadcrumb: {
                        title: '$t("certificate.title3")',
                        route: ["certificate", "certificate.list"],
                    },
                    perm: "certificate",
                },
            },
            {
                path: "history",
                name: "certificate.history",
                component: () => import("./deploymentHistory.vue"),
                meta: {
                    breadcrumb: {
                        title: '$t("certificate.title4")',
                        route: ["certificate", "certificate.history"],
                    },
                    perm: "certificate.history",
                },
            },
        ],
    },
    {
        path: "/certificate/history/versions",
        name: "certificate.versions",
        component: () => import("./versionHistory.vue"),
        meta: {
            breadcrumb: {
                title: '$t("certificate.title5")',
                route: ["certificate", "certificate.history", "certificate.versions"],
            },
            perm: "certificate",
        },
    },
    {
        path: "/certificate/list/batchBind",
        name: "certificate.batchBind",
        component: () => import("./batchBind/index.vue"),
        meta: {
            breadcrumb: {
                title: '$t("certificate.title2")',
                route: ["certificate", "certificate.list", "certificate.batchBind"],
            },
            perm: "certificate",
        },
    },

   
];
export default certificateRouter;
