<template>
    <ct-section-wrap :headerText="$t('order.title')" :headerTip="$t('order.slogan')">
        <ct-box class="table-scroll-wrap">
            <div class="search-panel-wrap">
                <div class="search-bar">
                    <el-select v-model="domain" filterable size="medium" :loading="domainListLoading" >
                        <el-option :label="$t('order.allDomainNames')" value="" />
                        <el-option
                            v-for="opt in domainOptions"
                            :key="opt.value"
                            :label="opt.label"
                            :value="opt.value"
                        />
                    </el-select>
                    <el-select v-model="action" size="medium">
                        <el-option :label="$t('order.allTypes')" value="" />
                        <el-option
                            v-for="opt in OrderActionOptions"
                            :key="opt.value"
                            :label="orderActionI18n(opt)"
                            :value="opt.value"
                        />
                    </el-select>
                    <el-select v-model="status" size="medium">
                        <el-option :label="$t('domain.list.status[0]')" value="" />
                        <el-option
                            v-for="opt in OrderStatusOptions"
                            :key="opt.value"
                            :label="orderStatusI18n(opt)"
                            :value="opt.value"
                        />
                    </el-select>
                    <el-date-picker
                        v-model="timeRange"
                        type="daterange"
                        :range-separator="$t('order.timeSperator')"
                        :start-placeholder="$t('order.startTime')"
                        :end-placeholder="$t('order.endTime')"
                        size="medium"
                        :default-time="['00:00:00', '23:59:59']"
                    />
                </div>
                <div class="search-btns">
                    <el-button type="primary" size="medium" @click="refresh = true"> {{ $t("common.search.start") }} </el-button>
                    <el-button size="medium" @click="reset"> {{ $t("common.search.reset") }} </el-button>
                </div>
            </div>

            <el-table
                :empty-text="$t('common.table.empty')"
                :data="dataList"
                v-loading="domainListLoading || loading"
            >
                <el-table-column :label="$t('order.ticketID')" minWidth="160" align="left" prop="orderNo" />
                <el-table-column prop="domain" :label="$t('order.domain')" minWidth="160" align="left" />
                <el-table-column
                    :label="$t('order.ticketType')"
                    minWidth="120"
                    align="left"
                    :formatter="getOrderActionType"
                />
                <el-table-column :label="$t('domain.list.tableLabel5')" minWidth="80" align="left">
                    <template #default="{ row }">
                        <cute-state :color="statusColorMap[row.status]">
                            {{ $t(`order.statusMap.itm${row.status}`) }}
                        </cute-state>
                    </template>
                </el-table-column>
                <el-table-column :label="$t('certificate.table.label[5]')" minWidth="160" align="left">
                    <template #default="{ row }">
                        {{ row.createTime | timeFormat }}
                    </template>
                </el-table-column>
                <!-- <el-table-column :label="$t('common.table.operation')" minWidth="120" align="left">
                    <template #default="{ row }">
                        <el-button type="text" size="medium" @click="onOperation(row, 'view')">
                            {{ $t("domain.view") }}
                        </el-button>
                        <el-button
                            v-if="
                                (row.action == 4 || row.action == 6) &&
                                row.status == 4 &&
                                row.bizFlag != 'createProductOverlay'
                            "
                            type="text"
                            size="medium"
                            @click="onOperation(row, 'reCreate')"
                        >
                            {{ $t("order.resubmit") }}
                        </el-button>
                    </template>
                </el-table-column> -->
            </el-table>
            <ct-pager :refresh.sync="refresh" :loadData="search" />
        </ct-box>
    </ct-section-wrap>
</template>

<script lang="ts">
import { Component, Watch, Mixins } from "vue-property-decorator";
import { DomainModule } from "@/store/modules/domain";
import { nDomainUrl } from "@/config/url";
import { nOrderUrl } from "@/config/url";
import {
    OrderActionMap,
    OrderActionOptions,
    OrderStatusMap,
    OrderStatusOptions,
    DomainActionEnum,
    OrderActionEnum,
    getDomainAction,
} from "@/config/map";
import { OrderItem } from "@/types/order";
import { nUserModule } from "@/store/modules/nuser";
import variables from "@cutedesign/ui/style/themes/default/index.scss";
import { timeFormat } from "@/filters/index";

import TableAndPagerActionMixin from "@/mixins/tableAndPagerAction";
import { CtiamCode } from "@/utils/ctFetch/errorConfig";
import { StatisticsModule } from "@/store/modules/statistics";

const statusColorMap = {
    1: variables.colorInfo,
    2: variables.colorMaster,
    3: variables.colorSuccess,
    4: variables.colorDanger,
    0: variables.colorDanger,
};
@Component({
    filters: {
        timeFormat(data: string): string {
            return timeFormat(data) || data;
        },
    },
})
export default class NOrderList extends Mixins(TableAndPagerActionMixin) {
    private domain = "";
    private action = "";
    private OrderActionMap = OrderActionMap;
    private OrderActionOptions: any[] = [];
    private status = "";
    private OrderStatusMap = OrderStatusMap;
    private OrderStatusOptions = OrderStatusOptions;
    private timeRange: Date[] = [];
    searchUrl = nOrderUrl.orderList;
    private statusColorMap = statusColorMap;
    private ctiamDomainModuleTimer: number | null = null;
    get isFcdnCtyunCtclouds() {
        return nUserModule.isFcdnCtyunCtclouds;
    }
    get domainAction() {
        return this.isFcdnCtyunCtclouds ? getDomainAction("DomainLog") : DomainActionEnum.Order;
    }
    // 全部的域名列表
    get domainList() {
        // fcdn-ctyun 体系（国内+国际）需要取ctiam权限管控下的域名数据
        return DomainModule[this.domainAction].list;
    }
    get domainListLoading() {
        // fcdn-ctyun 体系（国内+国际）需要取ctiam权限管控下的域名数据
        return DomainModule[this.domainAction].loading;

    }

    get domainOptions() {
        return this.domainList.map(domain => ({
            label: domain,
            value: domain,
        }));
    }
    get searchParams() {
        const domainList = this.domain ? [this.domain] : [...this.domainList];
        return {
            domainList,
            action: this.action,
            status: this.status,
            startTime: this.timeRange[0] ? Math.floor(+this.timeRange[0] / 1000) + "" : "",
            endTime: this.timeRange[1] ? Math.floor(+this.timeRange[1] / 1000) + "" : "",
        };
    }

    @Watch("timeRange")
    onTimeRangeChage(val: Date[] | null) {
        if (!val) this.timeRange = [];
    }
    @Watch("domainList")
    onDomainListChange(newArr: string[], oldArr: string[]) {
        // domain 数组长度变化，或者任何一个内容不同，则重新发起请求
        if (newArr.length !== oldArr.length || newArr.some((val, idx) => val !== oldArr[idx])) {
            this.refresh = true;
        }
    }

    created() {
        this.domain = (this.$route.query.d as string) || "";
        this.usePost = true;
    }

    beforeSearch() {
        // 有域名才继续请求
        return this.domainList.length !== 0;
    }
    reset() {
        this.domain = this.status = this.action = "";
        this.timeRange = [];

        // 重置查询条件后执行查询
        this.refresh = true;
    }
    // 重新发起
    async goCreateDomain(row: OrderItem) {
        if (Number(row.action) === 6) {
            await this.$confirm(
                this.$t("domain.detail.tip6") as string,
                this.$t("order.resubmit") as string,
                {
                    confirmButtonText: this.$t("common.dialog.submit") as string,
                    cancelButtonText: this.$t("common.dialog.cancel") as string,
                    type: "warning",
                }
            );
            await this.$ctFetch(nDomainUrl.updateAreaOrProduct, {
                method: "POST",
                data: { type: "product", domain: row.domain, newBusinessType: 8 },
                headers: {
                    "Content-Type": "application/json",
                },
            });
            this.$message.success(this.$t("domain.detail.tip7") as string);
            setTimeout(() => {
                this.refresh = true;
            }, 1000);
        } else {
            // 查询域名是否已存在且未删除
            const rst = await this.$ctFetch<{ exist: "true" | "false"; status: number }>(
                nOrderUrl.checkDomain,
                {
                    data: {
                        domain: row.domain,
                    },
                }
            );
            // exist 一定是存在的，但是 string 类型的，需要调整判断条件
            if (rst.exist === "true" && rst.status !== 8) {
                this.$message.error(this.$t("order.domainNameExists") as string);
                return;
            }
            await this.$confirm(
                this.$t("order.resubmitAddDomainTicketPrompt") as string,
                this.$t("order.resubmit") as string,
                {
                    confirmButtonText: this.$t("common.dialog.submit") as string,
                    cancelButtonText: this.$t("common.dialog.cancel") as string,
                    type: "warning",
                }
            );
            this.$router.push({
                name: "ndomain.create",
                query: {
                    orderId: row.orderId,
                    signature: row.signature,
                },
            });
        }
    }

    // 生成工单类型文案
    getOrderActionType(row: OrderItem) {
        return this.$t(`order.actionMap.itm${row.action}`) || this.$t("domain.unknown");
    }

    async onOperation(row: OrderItem, type: string) {
        if (type === "view") {
            this.$router.push({
                name: "norder.detail",
                query: {
                    orderId: row.orderId,
                    signature: row.signature,
                },
            });
        } else if (type === "reCreate") {
            this.goCreateDomain(row);
        }
    }
    // 覆盖mixin中的search方法
    protected async search({ page = 1, pageSize = 10 }) {
        // 必须有明确返回值，该值作为 total 使用
        if (!this.beforeSearch()) return 0;
        const params = Object.assign({ pageIndex: page, pageSize }, this.searchParams);

        this.loading = true;
        const getParams = { data: params };
        const postParams = {
            method: "POST",
            clearQsWithPost: false,
            body: {
                data: params,
            },
            headers: {
                "Content-Type": "application/json",
            },
        };
        try {
            const { total = 0, list = [], result = [] } = await this.$ctFetch<{
                total: number;
                list: OrderItem[];
                result: OrderItem[];
            }>(
                this.searchUrl || this.dynamicSearchUrl, // 静态 url 优先级高于动态 url
                this.usePost ? postParams : getParams
            );
            this.total = total;
            // 由于格式化数据需要使用 pageNum ，所以在数据清洗前先缓存
            this.pageNum = page;
            this.pageSize = pageSize;
            this.dataList = this.getResData ? this.dataListFormat(result) : this.dataListFormat(list);

            this.loading = false;
            this.afterSearch && this.afterSearch();
        } catch(err) {
            const isCtiamError = CtiamCode.includes((err as any)?.data?.code);
            if (isCtiamError) {
                // 权限报错时清空数据
                this.dataList = [];
                this.total = 0;
            }
            this.$errorHandler(err);
        } finally {
            this.loading = false;
        }

        return this.total;
    }
    // opt is a element of orderActionOptions
    orderActionI18n(opt: { label: string; value: typeof OrderActionEnum[keyof typeof OrderActionEnum] }) {
        return this.$t(`order.actionMap.itm${opt.value}`) as string;
    }
    orderStatusI18n(opt: { label: string; value: typeof OrderStatusMap[keyof typeof OrderStatusMap] }) {
        return this.$t(`order.statusMap.itm${opt.value}`) as string;
    }
    mounted() {
        const arr = ["6", "7"];
        for (const item of OrderActionOptions) {
            if (!arr.includes(item.value)) {
                this.OrderActionOptions.push(item);
            }
        }
        this.isFcdnCtyunCtclouds &&(this.ctiamDomainModuleTimer = window.setInterval(() => {
            DomainModule.GetDomainList({ action: this.domainAction, shouldCheckCache: true });
        }, (StatisticsModule.cacheTtl > 15 ? StatisticsModule.cacheTtl : 15) * 1000));
        this.refresh = true;
    }
    beforeDestroy() {
        this.ctiamDomainModuleTimer && clearInterval(this.ctiamDomainModuleTimer);
    }
}
</script>

<style lang="scss" scoped>

// 操作按钮的分割线样式
.el-button--text {
    position: relative;

    &:not(:first-child) {
        margin-left: 8px;

        &::before {
            content: "";
            position: absolute;
            height: 16px;
            border-left: 1px solid $border-color;
            top: 50%;
            left: -6px;
            transform: translateY(-50%);
        }
    }
}
.search-panel-wrap {
    display: flex;
    justify-content: space-between;

    .search-bar {
        display: flex;
        flex-wrap: wrap;
        gap: 12px;
        margin-bottom: 12px;
    }

    .search-btns {
        display: flex;
        gap: 4px;
        height: fit-content;
    }
}
</style>
