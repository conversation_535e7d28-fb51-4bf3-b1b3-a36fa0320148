<template>
    <div class="ct-edit-wrapper">
        <el-form
            label-width="140px"
            label-position="left"
            :model="form"
            :rules="rules"
            ref="defineRedirectForm"
            :disabled="!isEdit || !isService || isLockDefineRedirect"
        >
            <el-form-item
                prop="define_redirect"
                ref="defineRedirect"
                class="cache-table-style"
                :label="$t('domain.editPage.label36')"
            >
                <div class="ct-table-wrapper">
                    <el-table class="origin-table" :data="form.define_redirect" ref="defineRedirect">
                        <el-table-column prop="mode" :label="$t('domain.type')">
                            <template slot-scope="scope">{{ type_list[scope.row.mode] }}</template>
                        </el-table-column>
                        <el-table-column prop="content" :label="$t('domain.content')"> </el-table-column>
                        <el-table-column prop="pattern" :label="$t('domain.detail.label49')">
                        </el-table-column>
                        <el-table-column prop="replacement" :label="$t('domain.detail.label50')">
                        </el-table-column>
                        <el-table-column prop="code" :label="$t('domain.editPage.label35')">
                        </el-table-column>
                        <el-table-column prop="priority" :label="$t('domain.detail.label48')">
                        </el-table-column>
                        <el-table-column :label="$t('domain.operate')" width="120">
                            <template slot-scope="scope">
                                <el-button
                                    type="text"
                                    @click="handleOper(scope.row, 'edit', 'define_redirect', scope.$index)"
                                    >{{ $t("domain.modify") }}</el-button
                                >
                                <el-button
                                    type="text"
                                    @click="handleOper(scope.row, 'delete', 'define_redirect', scope.$index)"
                                    >{{ $t("domain.delete") }}</el-button
                                >
                            </template>
                        </el-table-column>
                    </el-table>
                    <div class="flex-row-style button-box">
                        <el-button
                            class="btn"
                            type="text"
                            @click="handleOper(null, 'create', 'define_redirect')"
                        >
                            + {{ addButtonText }}
                        </el-button>
                    </div>
                </div>
            </el-form-item>
        </el-form>
        <define-redirect-dialog
            :dialogVisible="defineRedirectDialogVisible"
            :defineRedirectForm="defineRedirectForm"
            :from="currentType"
            :define_redirect="form.define_redirect"
            @cancel="cancel"
            @submit="submitDefineRedirect"
        />
    </div>
</template>

<script>
import componentMixin from "@/views/domainConfig/componentMixin";
import validFieldMixin from "@/views/domainConfig/validFieldMixin";
import i18n from "@/i18n/index";
import { cloneDeep } from "lodash-es";
import defineRedirectDialog from "@/views/domainConfig/accelerationConfig/components/defineRedirectDialog.vue";
import { lowerFirst } from "@/utils";

const CacheTtlMap = {
    2: { name: i18n.t("simpleForm.alogicCacheMixin.CacheTtlMap.2"), factor: 60 * 60 * 24 },
    3: { name: i18n.t("simpleForm.alogicCacheMixin.CacheTtlMap.3"), factor: 60 * 60 },
    4: { name: i18n.t("simpleForm.alogicCacheMixin.CacheTtlMap.4"), factor: 60 },
    5: { name: i18n.t("simpleForm.alogicCacheMixin.CacheTtlMap.5"), factor: 1 },
};
// 缓存配置-类型：字典翻译
const CacheModeMap = {
    0: i18n.t("domain.detail.cacheModeMap[0]"),
    1: i18n.t("domain.detail.cacheModeMap[1]"),
    2: i18n.t("domain.detail.cacheModeMap[2]"),
    3: i18n.t("domain.detail.cacheModeMap[3]"),
    4: i18n.t("domain.detail.cacheModeMap[4]"),
    5: i18n.t("domain.detail.cacheModeMap[5]"),
};

export default {
    name: "defineRedirect",
    components: {
        defineRedirectDialog,
    },
    mixins: [componentMixin, validFieldMixin],
    props: {
        datas: Object,
        isLockDefineRedirect: Boolean,
    },
    data() {
        return {
            addButtonText: i18n.t("domain.editPage.label10"),
            form: {
                define_redirect: [], // 缓存参数
            },
            defineRedirectDialogVisible: false,
            currenIndex: "",
            defineRedirectForm: {
                id: null,
                mode: 0,
                pattern: "",
                replacement: "",
                code: "302",
                priority: 10,
                content: "",
            },
            currentType: "create",
            rules: {},
        };
    },
    computed: {},
    watch: {
        "datas.define_redirect": {
            deep: true,
            handler(val) {
                this.init(val);
            },
            immediate: true,
        },
    },
    methods: {
        init(v) {
            this.form.define_redirect = cloneDeep(v);
        },
        async handleOper(row, currentType, tabName, i) {
            this.currentType = currentType;

            this.currenIndex = i;
            const getTime = new Date().getTime();
            const defaultFormMap = {
                define_redirect: {
                    id: `define_redirect_${getTime}`,
                    mode: null,
                    pattern: "",
                    replacement: "",
                    code: "302",
                    priority: 10,
                    content: "",
                },
            };
            if (currentType === "create") {
                row = defaultFormMap[tabName];
            }
            if (currentType === "delete") {
                const msgMap = {
                    define_redirect: this.$t("domain.editPage.tip20"),
                };

                // 二次确认弹窗
                await this.$confirm(msgMap[tabName], this.$t("domain.delete"), {
                    type: "warning",
                });
                this.form[tabName].splice(i, 1);
                this.$emit("onChange", this.form.define_redirect);
            } else {
                this.defineRedirectDialogVisible = true;
                const rowData = {
                    ...defaultFormMap[tabName],
                    mode: "",
                    ...row,
                };
                this.$set(this, "defineRedirectForm", rowData);
            }
        },
        checkRepeatedName(cacheList) {
            if (cacheList.length === 0) return "";

            const nameLsit = cacheList
                .map(item => item.content)
                .join(",")
                .split(",");
            const nameMap = {};
            for (let idx = 0; idx < nameLsit.length; idx += 1) {
                if (nameMap[nameLsit[idx]]) {
                    return nameLsit[idx];
                } else {
                    nameMap[nameLsit[idx]] = true;
                }
            }

            return "";
        },
        // 弹窗点击确定事件
        async submitDefineRedirect() {
            // 进行重复性检查
            const { mode } = this.defineRedirectForm;
            const typeName = CacheModeMap[mode] || "";
            const typeList = this.form.define_redirect
                .filter((i, index) => index !== this.currenIndex) // 先把指定的过滤掉
                .filter(item => item.mode === mode); // 再把不符合的类型过滤掉

            typeList.push(this.defineRedirectForm); // 再把当前的追进去进行全比对

            const repeatedName = this.checkRepeatedName(typeList);
            if (repeatedName) {
                await Promise.reject(
                    this.$t("simpleForm.alogicCacheMixin.ValidationErrorMessages.DuplicateValidation", {
                        repeatedName,
                        type: lowerFirst(typeName),
                    })
                );
            }

            if (this.currentType === "create") {
                this.form.define_redirect.push({
                    ...this.defineRedirectForm,
                });
            } else {
                this.$set(this.form.define_redirect, this.currenIndex, this.defineRedirectForm);
            }
            this.$emit("onChange", this.form.define_redirect);
            this.defineRedirectDialogVisible = false;
        },
        cancel(dialogKey) {
            this[dialogKey] = false;
        },
    },
};
</script>

<style lang="scss" scoped>
@import "@/views/domainConfig/commonStyle.scss";
.ct-edit-wrapper {
    ::v-deep {
        .el-form--label-left .el-form-item__label {
            text-align: right;
        }
    }
}
</style>
