<template>
    <el-card>
        <template>
            <div class="total">
                <div class="tip">
                    {{ $t("statistics.common.vchartTip1", { type: "", num: "" }) }}{{ titleTipSuffix }}：
                    <span class="num">
                        {{ convertBandwidthB2P(fetchData.peakQueryBandwidth) }}
                    </span>
                    <span class="date">
                        {{ fetchData.peakQueryBandwidthTimestamp || "" }}
                    </span>
                </div>
                <div class="tip">
                    {{ $t("statistics.common.vchartTip2", { type: "", num: "" }) }}{{ titleTipSuffix }}：
                    <span class="num">
                        {{ convertBandwidthB2P(fetchData.peak95QueryBandwidth) }}
                    </span>
                </div>
            </div>
            <div class="total" v-if="compareConfig.useCompare">
                <div class="tip">
                    {{ $t("statistics.common.vchartTip1", { type: "", num: 2 }) }}：
                    <span class="num">
                        {{ convertBandwidthB2P(fetchData2.peakQueryBandwidth) }}
                    </span>
                    <span class="date">
                        {{ fetchData2.peakQueryBandwidthTimestamp || "" }}
                    </span>
                </div>
                <div class="tip">
                    {{ $t("statistics.common.vchartTip2", { type: "", num: 2 }) }}：
                    <span class="num">
                        {{ convertBandwidthB2P(fetchData2.peak95QueryBandwidth) }}
                    </span>
                </div>
            </div>
        </template>
        <v-chart class="statistic-chart-bw" v-loading="loading"
            :element-loading-text="$t('statistics.common.chart.loading')" autoresize theme="cdn" :options="options" @legendselectchanged="legendselectchanged" />

        <ct-tip>
            {{ $t("statistics.eas.tip13") }}
            <el-tooltip effect="dark" :content="$t('statistics.common.searchDownloadContent')" placement="right">
                <i class="el-icon-download ipa-download-icon usage-download-icon"
                    @click="() => $refs.bandwidthTable.downloadTable($t('statistics.eas.tab[0]'))"></i>
            </el-tooltip>
        </ct-tip>
        <usage-table ref="bandwidthTable"></usage-table>
    </el-card>
</template>

<script>
/* eslint-disable @typescript-eslint/camelcase */
import { statistics as StatisticsUrl } from "@/config/url/ipa/statistics";

import { convertBandwidthB2P } from "@/utils";
import mixChart from "../mixChart";
import chartShim from "../chartShim";
import { timeFormat } from "@/filters";
import usageTable from "./usageTable.vue";
import { cloneDeep } from "lodash-es";
import { getMaxFromList } from "@/utils/utils";
import { THEME_AREA_STYLE } from "@/config/echart/cdn-theme";
import { themeColorArr } from "@cdnplus/common/config/echart/theme";

export default {
    mixins: [mixChart, chartShim],
    components: { usageTable },
    data() {
        return {
            currentCmp: "bandwidthChart",
            reqParamsFromParentBackup: {},
            activeStatusFlow: "flow",
            fetchDataList: [],
            loading: false,
            downloadDataList: [],
            originFetchResult: {
                list1: [],
                list2: [], // 对比
            },
            titleTipSuffix: "",
            fetchData: {
                peakQueryBandwidth: 0,
                peak95QueryBandwidth: 0,
            },
            fetchData2: {
                peakQueryBandwidth: 0,
                peak95QueryBandwidth: 0,
            },
            selectedLegends: null, // 记录当前勾选legend
        };
    },
    props: {},
    computed: {
        bandwidthIndention() {
            const size = {
                bps: 1,
                Kbps: this.scale,
                Mbps: Math.pow(this.scale, 2),
                Gbps: Math.pow(this.scale, 3),
                Tbps: Math.pow(this.scale, 4),
                Pbps: Math.pow(this.scale, 5),
            };
            let peekBw = [];
            if (this.compareConfig.useCompare) {
                const max1 = (!this.selectedLegends || this.selectedLegends[this.currentLegends[0]]) ? getMaxFromList(this.originFetchResult.list1, "bandwidth") : 0;
                const max2 = (!this.selectedLegends || this.selectedLegends[this.currentLegends[1]]) ? getMaxFromList(this.originFetchResult.list2, "bandwidth") : 0;
                peekBw = Math.max(max1, max2)
            } else {
                peekBw = getMaxFromList(this.originFetchResult.list1, "bandwidth")
            }
            const rst = convertBandwidthB2P(peekBw, this.scale);
            return [size[rst._unit], rst.unit];
        },
        options() {
            let count = 0;
            const options = {
                tooltip: {
                    trigger: "axis",
                    // ***设置数据对比时的hover效果
                    formatter: params => {
                        const time = this.$dayjs(params[0].axisValue * 1).format("YYYY-MM-DD HH:mm:ss");
                        const value = params[0].value;
                        if (params[1]) {
                            // ***保存数据对比时的时间信息
                            const time1 = this.$dayjs(params[1].data.date * 1).format("YYYY-MM-DD HH:mm:ss");
                            const value1 = params[1].value;
                            return `
                                <div>${time}</div>
                                <div>
                                    <div style="display:inline-block;width:8px;height:8px;background-color:#6694D5;border-radius:50%;"></div>
                                    ${this.$t("statistics.dcdn.bandwidthFlowWhole.vchartOptions.title1")}：${value} ${this.bandwidthIndention[1]}
                                </div>
                                <div>${time1}</div>
                                <div>
                                    <div style="display:inline-block;width:8px;height:8px;background-color:#FA8334;border-radius:50%;"></div>
                                    ${this.$t("statistics.dcdn.bandwidthFlowWhole.vchartOptions.title1")}：${value1} ${this.bandwidthIndention[1]}
                                </div>
                                `;
                        } else {
                            return `
                                <div>${time}</div>
                                <div>
                                    <div style="display:inline-block;width:8px;height:8px;background-color:#6694D5;border-radius:50%;"></div>
                                    ${this.$t("statistics.dcdn.bandwidthFlowWhole.vchartOptions.title1")}：${value} ${this.bandwidthIndention[1]} </div>
                                `;
                        }
                    },
                },
                xAxis: {
                    // type: 'time',
                    type: "category",
                    boundaryGap: false,
                    data: [],
                    axisLabel: {
                        formatter: value => {
                            const time = this.$dayjs(value * 1).format("MM-DD HH:mm");
                            const arr = time.split(" ");
                            count += 1;
                            if (count % 2 === 1) return `${arr[0]}\n${arr[1]}`;
                        },
                    },
                },
                yAxis: {
                    name: `${this.$t("home.chart.unit")}: ${this.bandwidthIndention[1]}`,
                    type: "value"
                },
            };

            const chartData = this.getChartData(this.fetchDataList[0])
            options.series = [
                {
                    name: `${this.$t("statistics.dcdn.bandwidthFlowWhole.vchartOptions.title1")}1`,
                    type: "line",
                    data: chartData.chartData,
                    areaStyle: THEME_AREA_STYLE[themeColorArr[0]],
                }
            ]
            options.xAxis.data = chartData.xData

            if (this.compareConfig.useCompare) {
                const chartData2 = this.getChartData(this.fetchDataList[1])
                options.series.push({
                    name: `${this.$t("statistics.dcdn.bandwidthFlowWhole.vchartOptions.title1")}2`,
                    type: "line",
                    data: chartData2.chartData,
                    areaStyle: THEME_AREA_STYLE[themeColorArr[0]],
                })

                options.legend = {
                    data: this.currentLegends
                };
                options.legend.selected = this.selectedLegends
            }

            Object.assign(options, this.toolBox);

            return options;
        },
        currentLegends() {
            return [
                `${this.$t("statistics.dcdn.bandwidthFlowWhole.vchartOptions.title1")}1`,
                `${this.$t("statistics.dcdn.bandwidthFlowWhole.vchartOptions.title1")}2`,
            ]
        }
    },
    methods: {
        resetQuery() {
            this.queryForm.bandwidthType = 0;
        },
        // ***单位换算：bps自动适配
        convertBandwidthB2P(val = 0) {
            return convertBandwidthB2P(val, this.scale).result;
        },

        // 带宽初始化
        async initData(reqParam, isShow, array) {
            // 重置当前legends
            this.selectedLegends = null
            this.downloadDataList = [];
            this.loading = true;
            const fetchList = [];

            if (isShow) {
                this.titleTipSuffix = "1";
                for (let index = 0; index < array.length; index++) {
                    const element = array[index];
                    fetchList.push(this.getBandWithList({
                        ...reqParam,
                        start_time: Math.floor(element[0] / 1000),
                        end_time: Math.floor(element[1] / 1000),
                    }));
                }
            } else {
                this.titleTipSuffix = "";
                fetchList.push(this.getBandWithList(reqParam));
            }

            const fetchResultList = await Promise.all(fetchList);
            this.fetchDataList = fetchResultList.map(item => item.result);
            this.setPeakData(this.fetchDataList, isShow);
            this.setDownloadData(this.fetchDataList, isShow);
            this.loading = false;
        },
        getChartData(_fetchDataList) {
            const xData = []
            const chartData = []
            cloneDeep(_fetchDataList)
                ?.bandw
                .sort((a, b) => a.timestamp - b.timestamp)
                .map(item => {
                    xData.push(item.timestamp * 1000)
                    chartData.push({
                        value: (item.bandwidth / this.bandwidthIndention[0]).toFixed(2),
                        date: item.timestamp * 1000,
                    })
                });

            return {
                xData,
                chartData
            }
        },
        setDownloadData(fetchDataList, isShow) {
            this.originFetchResult.list1 = cloneDeep(fetchDataList[0].bandw);
            this.originFetchResult.list1.sort((a, b) => a.timestamp - b.timestamp);
            this.downloadDataList = this.downloadDataList.concat(this.getChartData(fetchDataList[0]).chartData);

            if (isShow) {
                this.originFetchResult.list2 = cloneDeep(fetchDataList[1].bandw);
                this.originFetchResult.list2.sort((a, b) => a.timestamp - b.timestamp);
                this.downloadDataList = this.downloadDataList.concat(this.getChartData(fetchDataList[1]).chartData);
            }
        },
        setPeakData(fetchDataList, isShow) {
            this.fetchData.peakQueryBandwidth = fetchDataList[0].peek.bandwidth;
            this.fetchData.peakQueryBandwidthTimestamp = this.$dayjs
                .unix(fetchDataList[0].peek.timestamp)
                .format("YYYY-MM-DD HH:mm:ss");
            this.fetchData.peak95QueryBandwidth = fetchDataList[0].peek_95.bandwidth;

            if (isShow) {
                this.fetchData2.peakQueryBandwidth = fetchDataList[1].peek.bandwidth;
                this.fetchData2.peakQueryBandwidthTimestamp = this.$dayjs
                    .unix(fetchDataList[1].peek.timestamp)
                    .format("YYYY-MM-DD HH:mm:ss");
                this.fetchData2.peak95QueryBandwidth = fetchDataList[1].peek_95.bandwidth;
            }
        },
        // 带宽列表查询
        getBandWithList(data) {
            if (!data) {
                data = { account_id: this.$store.state.user.userInfo.userId };
            }
            return this.$ctFetch(StatisticsUrl.bandWidthList, {
                method: "POST",
                transferType: "json",
                body: {
                    data,
                },
            });
        },
        // 监听图例选择变化
        legendselectchanged({ selected }) {
            // 更新勾选legend
            this.selectedLegends = selected;
        },

        /**
         * 导出处理函数
         */
        tableToExcel() {
            let str = `${this.$t("statistics.usageQuery.BandwidthFlow.tableToExcel.excelColumn1")},${this.$t("statistics.dcdn.bandwidthFlowWhole.vchartOptions.title1")}(${this.Mbps})\n`;

            const { titleTipSuffix, fetchData, fetchData2 } = this;
            const { peakQueryBandwidth, peak95QueryBandwidth } = fetchData;

            // 输出格式
            const downloadDataList = this.compareConfig.useCompare ? this.originFetchResult.list1.concat(this.originFetchResult.list2) : this.originFetchResult.list1;
            str += downloadDataList.reduce((str, item) => {
                const info = (item.bandwidth / Math.pow(this.scale, 2)).toFixed(2);
                str += `${timeFormat(item.timestamp)},`;
                str += info + "\n";
                return str;
            }, "");

            // 增加峰值带宽和95峰值带宽
            str += `\n${this.$t("statistics.rank.common.tableToExcel.excelColumn2")}\n`;

            str += `${this.$t('statistics.rank.domainRank.tableColumn5')}${titleTipSuffix},${(peakQueryBandwidth / Math.pow(this.scale, 2)).toFixed(2)} ${this.Mbps}\n ${this.$t("statistics.usageQuery.tip2")}${titleTipSuffix},${(peak95QueryBandwidth / Math.pow(this.scale, 2)).toFixed(2)} ${this.Mbps}\n`;

            if (this.compareConfig.useCompare) {
                str += `${this.$t('statistics.rank.domainRank.tableColumn5')}2,${(fetchData2.peakQueryBandwidth / Math.pow(this.scale, 2)).toFixed(2)} ${this.Mbps}\n ${this.$t("statistics.usageQuery.tip2")}2,${(fetchData2.peak95QueryBandwidth / Math.pow(this.scale, 2)).toFixed(2)} ${this.Mbps}\n`;
            }

            const timeRange = this.reqParamsFromParentBackup.timeRange;
            const startTime = timeRange[0]
                ? this.$dayjs(timeRange[0])
                    .startOf("day")
                    .format("YYYYMMDDHHmmss")
                : "";
            const endTime = timeRange[1]
                ? this.$dayjs(timeRange[1])
                    .endOf("day")
                    .format("YYYYMMDDHHmmss")
                : "";
            const string = startTime && endTime ? `${startTime}-${endTime}` : "";
            const name = `${this.$t("statistics.eas.tip14")}${string}`;
            this.downloadExcel({
                name: name,
                str,
            });
        },
        handleClickDataCompareBtn() {
            this.compareConfig.visible = !this.compareConfig.visible;
            if (!this.compareConfig.visible) {
                this.closeDataCompare();
            }
        },
        closeDataCompare() {
            this.compareConfig.visible = false;
            this.compareConfig.useCompare = false;
            this.updateAgency();
        },
    },
};
</script>
<style lang="scss" scoped>
.sub-query-wrapper {
    margin-bottom: 12px;
    display: flex;
    justify-content: space-between;
}

// 统计数字
.total {
    width: 100%;
    text-align: left;
    font-size: 14px;
    color: #333333;
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 8px;

    @media (max-width: 1350px) {
        grid-template-columns: 1fr;
    }

    .num {
        margin-left: 4px;
    }

    .date {
        color: #666666;
        font-size: 12px;
        margin-left: 4px;
    }
}

.compare-tip {
    display: grid;
    grid-template-columns: 1fr 60px 1fr;

    &-center {
        font-size: 14px;
        color: $color-info;
        font-weight: 600;
    }

    &-part {
        width: 100%;
        text-align: left;
        font-size: 14px;
        color: #333333;

        &-time {
            color: $color-info;
            font-size: 12px;
        }

        &-text {
            &-num {
                margin-left: 4px;
            }

            &-date {
                color: #666666;
                font-size: 12px;
                margin-left: 4px;
            }
        }
    }
}

// 内容区
.statistic-chart-bw {
    clear: both;
    width: 100%;
    height: 380px;
}

.ml-12 {
    margin-left: 12px;
}

.data-compare-btn {
    display: flex;
    align-items: center;
    writing-mode: vertical-lr;
    color: $color-master;
    background-color: $color-master-bg;
    margin-top: -56px;
    margin-right: -20px;
    width: 4px;
    padding: 12px 8px;
    border: 0;
    float: right;

    &:hover {
        background-color: $color-master-bg;
        color: $color-master;
    }
}
</style>
