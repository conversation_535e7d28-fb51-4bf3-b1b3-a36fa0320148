<template>
    <div v-if="showBreadcrumb" class="aocdn-ct-breadcrumb">
        <!--  面包屑数据-->
        <template v-if="!isPowerByQIANKUN">
            <el-breadcrumb separator="/">
                <el-breadcrumb-item v-for="(br, index) in breadcrumb" :key="index">
                    <el-tooltip v-if="br.type === 'toolTip'" placement="right" effect="dark">
                        <div slot="content">
                            <div class="tip-text" v-for="(tip, index) in br.tips" :key="index">
                                {{ tip | i18n }}
                            </div>
                        </div>
                        <span class="title-style">
                            {{ br.title | i18n }} <i :class="br.icon || 'el-icon-question'"></i>
                        </span>
                    </el-tooltip>
                    <span
                        v-else
                        @click="handleChangeRoute(br, index)"
                        :class="['title-style', index !== 0 && 'fcdn-current']"
                        >{{ br.title | i18n }}</span
                    >
                </el-breadcrumb-item>
            </el-breadcrumb>
        </template>
        <template v-else>
            <el-breadcrumb separator="/">
                <el-breadcrumb-item v-for="(br, index) in microAppBreadData" :key="index" :to="br.route">
                    <el-tooltip v-if="br.type === 'toolTip'" placement="right" effect="dark">
                        <div slot="content">
                            <div class="tip-text" v-for="(tip, index) in br.tips" :key="index">
                                {{ tip }}
                            </div>
                        </div>
                        <span
                            :class="[
                                'title-style',
                                index === microAppBreadData.length - 1 && 'current-header',
                                ![0, microAppBreadData.length - 1].includes(index) && 'font-color-light',
                            ]"
                        >
                            {{ br.title }} <i :class="br.icon || 'el-icon-question'"></i>
                        </span>
                    </el-tooltip>
                    <span
                        :class="[
                            'title-style',
                            index === microAppBreadData.length - 1 && 'current-header',
                            isLinkStyle(br) && 'is-link',
                        ]"
                        @click="handleChangeRoute(br)"
                    >
                        {{ br.name }}
                    </span>
                </el-breadcrumb-item>
            </el-breadcrumb>
        </template>

        <feedback />
    </div>
</template>

<script>
import { get } from "lodash-es";
import { AppModule } from "@/store/modules/app";
import i18n from "@/i18n";
import Feedback from "@/components/feedback/index.vue";

export default {
    components: { Feedback },
    props: {
        flattenRoutes: {
            type: Array,
            default: () => [],
        },
    },
    data() {
        return {
            breadcrumb: [],
            // microAppBreadData: [],
            drawerShow: false,
            isPowerByQIANKUN: window.__POWERED_BY_QIANKUN__,
        };
    },
    filters: {
        i18n(input) {
            const i18nStr = /^\$t\(["']([a-zA-Z.0-9]+)["']\)$/;
            if (i18nStr.test(input)) {
                return i18n.t(input.replace(i18nStr, "$1"));
            } else {
                return input;
            }
        },
    },
    computed: {
        microAppBreadData() {
            return AppModule.breadcrumbData;
        },
        isEasOverview() {
            return this.$route.name === "eas.overview";
        },
        showBreadcrumb() {
            // eas概览页不需要展示面包屑
            return (this.breadcrumb.length > 0 || this.microAppBreadData.length) && !this.isEasOverview;
        },
    },
    watch: {
        $route() {
            if (window.__POWERED_BY_QIANKUN__) {
                return;
            }

            this.generateBreadcrumb();
        },
    },
    created() {
        if (window.__POWERED_BY_QIANKUN__) {
            return;
        }

        this.generateBreadcrumb();
    },
    methods: {
        /**
         * 生成面包屑数据
         */
        generateBreadcrumb() {
            const { workspaceId } = this;
            const rt = this.$route;
            // 没有meta相关的，直接返回
            if (
                !rt.meta ||
                !rt.meta.breadcrumb ||
                !rt.meta.breadcrumb.route ||
                rt.meta.breadcrumb.route.length === 0
            ) {
                this.breadcrumb = [];
                return;
            }
            const { route: chain } = rt.meta.breadcrumb;

            const rs = [];
            // 把路由打平
            let routes = [...this.flattenRoutes];
            // 路由表中找出 匹配的路由配置
            routes = routes.filter(r => chain.includes(r.name));

            if (routes.length === 0) {
                this.breadcrumb = [];
                return;
            }

            // 生成 {"name1": 路由对象1， "name2": 路由对象2}
            const mapRoutes = routes.reduce((acc, r) => Object.assign(acc, { [r.name]: r }), {});
            // 生成面包屑数据
            chain.forEach(routeName => {
                const r = mapRoutes[routeName];
                const {
                    path,
                    name,
                    meta: { breadcrumb },
                } = r;
                // 路由表里的path可能为相对路径，只取path去跳转可能失败，故都取出来，优先使用name跳转
                const record = {
                    ...breadcrumb,
                    route: { path, name, query: { workspaceId } },
                };
                rs.push(record);
            });

            this.breadcrumb = rs;
        },
        isLinkStyle(item) {
            if (!item) {
                return;
            }

            return item.route || item.href;
        },
        /**
         * 处理切换路由
         */
        handleChangeRoute(item, i) {
            if (window.__POWERED_BY_QIANKUN__) {
                // 主应用的路由跳转
                const router = AppModule.baseAppRouter;
                if (!router) {
                    return;
                }

                if (item.hrefLocal) {
                    router.push(item.hrefLocal);
                    return;
                }

                const routeName = get(item, "route.name");
                if (routeName) {
                    router.push({ name: routeName });
                }
            } else {
                // fcdn 面包屑跳转
                // 第一个和最后一个点击都无跳转动作
                if (i !== 0 && i !== this.breadcrumb.length - 1) {
                    this.$router.push({ name: item.route.name });
                }
            }
        },
    },
};
</script>

<style scoped lang="scss">
.aocdn-ct-breadcrumb {
    padding: 0 $common-space-5x;
    background: #fff;
    box-shadow: 0 0 8px rgba(100, 110, 144, 0.1);
    border-left: 1px solid $color-neutral-2;

    display: flex;
    justify-content: space-between;
    align-items: center;

    .title-style {
        display: inline-block;
        font-size: 14px;
        margin: 16px 0;
        line-height: 1.5;
        // cursor: default;
        &:hover {
            font-weight: 400;
        }
        &.is-link {
            color: $text-color;
            &:hover {
                color: $color-master;
                cursor: pointer;
            }
        }
    }
    .tip-text {
        line-height: 20px;
        color: #fff;
    }
    .fcdn-current,
    .current-header {
        color: #333;
        cursor: pointer;
    }
    .fcdn-current:hover {
        color: #3d73f5;
    }

    .icon-notification {
        font-size: 20px;
        cursor: pointer;
    }

    .font-color-light {
        color: #999;
        cursor: text;
    }

    .is-home {
        font-weight: 400;
        color: #999;
        cursor: pointer;
    }

    .is-home:hover {
        color: #3d73f5;
        cursor: pointer;
    }

    .fcdn-home {
        color: #999;
        cursor: text;
    }
}
</style>
