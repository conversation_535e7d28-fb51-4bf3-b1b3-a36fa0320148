<template>
    <el-dialog
        :title="$t('ipManagement.planList.btns[0]')"
        :visible.sync="dialogVisible"
        :modal-append-to-body="false"
        :close-on-click-modal="false"
        :before-close="cancel"
        width="600px"
    >
        <el-form :rules="rules" :model="palnForm" ref="palnForm" label-width="130px" class="paln-form">
            <el-form-item :label="$t('ipManagement.planList.formTitle[0]')" prop="product">
                <el-select v-model="palnForm.product" filterable>
                    <el-option
                        v-for="opt in productOptions"
                        :key="opt.value"
                        :label="opt.label"
                        :value="opt.value"
                    />
                </el-select>
            </el-form-item>
            <el-form-item :label="$t('ipManagement.planList.formTitle[2]')" prop="name">
                <el-input :value="palnForm.name" disabled />
            </el-form-item>
            <el-form-item :label="$t('ipManagement.planList.formTitle[3]')" prop="domain">
                <domain-select
                    class="domain-select-wrapper"
                    v-model="palnForm.domain"
                    :domainOptions="domainOptions"
                    :multiple="true"
                    :loading="domainOptionsLoading"
                    key="multipleDomain"
                />
            </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
            <slot name="footer">
                <el-button v-if="!loading" @click="cancel">
                    {{ $t("ipManagement.planList.btns[7]") }}
                </el-button>
                <el-button type="primary" :loading="loading" @click="submit">
                    {{ $t("ipManagement.planList.btns[6]") }}
                </el-button>
            </slot>
        </div>
    </el-dialog>
</template>

<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";
import { DomainModule } from "@/store/modules/domain";
import { DomainActionEnum } from "@/config/map";
import DomainSelect from "@/components/domainsSelect/index.vue";
import { ipManagementUrl } from "@/config/url/ipManagement";

@Component({
    name: "CreatePlanDialog",
    components: {
        DomainSelect,
    },
})
export default class CreatePlanDialog extends Vue {
    @Prop({ default: true, type: Boolean }) private dialogVisible!: boolean;
    @Prop({ default: () => [] }) private disabledDomainList?: Array<string>;
    @Prop({ default: 20 }) private whiteListLimit!: number;
    private loading = false;
    private palnForm = {
        product: "020",
        name: "",
        domain: [],
    };
    private rules = {
        product: { required: true, message: this.$t("ipManagement.planList.validInfo[0]"), trigger: "blur" },
        name: { required: true, trigger: "blur" },
        domain: [
            { required: true, message: this.$t("ipManagement.planList.validInfo[1]"), trigger: "none", },
            {validator: this.validateDomain, trigger: "none",}
        ],
    };
    private configNameOptions: any = {
        "020": this.$t("ipManagement.sendList.configNameOption1"),
    };

    get productOptions() {
        return [{ label: this.$t("ipManagement.planList.productOption[0]"), value: "020" }];
    }
    get domainOptions() {
        return DomainModule[DomainActionEnum.Data].nativeList
            .filter(item => +item.status === 4)
            .filter(item => !this.disabledDomainList?.includes(item.domain))
            .map(item => ({
                label: item.label,
                value: item.domain,
            }));
    }
    get domainOptionsLoading() {
        return DomainModule[DomainActionEnum.Data].loading;
    }

    private mounted() {
        this.init();
    }

    /**
     * 初始化，生成默认回源方案名称
     */
    private init() {
        this.palnForm.name = this.configNameOptions[this.palnForm.product];
        this.palnForm.domain = [];
    }

    /**
     * 提交表单
     */
    private async submit() {
        (this.$refs?.palnForm as any).validate(async (valid: boolean) => {
            if (valid) {
                this.createPlan();
            }
        });
    }

    /**
     * 配置方案
     */
    private async createPlan() {
        this.loading = true;
        const { failed_list } = (await this.$ctFetch(ipManagementUrl.WhitelistCreate, {
            method: "POST",
            data: {
                product: this.palnForm.product,
                domains: this.palnForm.domain,
            },
            headers: {
                "Content-Type": "application/json",
            },
        })) as any;
        let status = "partialSuccess";
        if (failed_list.length === 0) {
            status = "success";
        } else if (failed_list.length === this.palnForm.domain.length) {
            status = "failed";
        }
        this.$emit("submit", { status, failedList: failed_list });
    }

    /**
     * 关闭当前弹窗
     */
    private cancel() {
        this.$emit("cancel");
    }

    private validateDomain(rule: any, value: Array<string>, callback: Function) {
        console.log(this.whiteListLimit)
        if (value.length && value.length > this.whiteListLimit) {
            callback(new Error(this.$t("ipManagement.planList.validInfo[2]", { count: this.whiteListLimit }) as string));
        } else {
            callback();
        }
    
    }
}
</script>

<style lang="scss" scoped>
.paln-form {
    .el-input,
    .el-select {
        width: 400px;
    }
    .domain-select-wrapper {
        ::v-deep {
            .domain-select__item {
                line-height: 1.5
            }
            .el-scrollbar {
                margin: 16px 0;
            }
        }
    }
    ::v-deep .el-form-item__label {
        word-break: normal;
    }
}
</style>
