<template>
    <batch-edit-checked v-model="checked">
        <el-form
            class="ct-edit-wrapper"
            label-width="140px"
            label-position="right"
            :model="form"
            :rules="rules"
            ref="formRef"
            :disabled="!checked"
            @validate="handleValidateEvent"
        >
            <el-form-item
                :label="$t('domain.detail.label42')"
                prop="error_code"
                ref="errorCode"
                class="ct-table-form-item code-style"
                ><span slot="label"
                    >{{ $t("domain.detail.label42") }}
                    <span>
                        <el-tooltip placement="top">
                            <div slot="content">
                                {{ $t("domain.detail.tip14") }}
                                <span>3xx,4xx,5xx</span><br />
                                {{ $t("domain.detail.tip105") }}<br />{{ $t("domain.detail.tip106") }}<br />{{
                                    $t("domain.detail.tip107")
                                }}
                            </div>
                            <ct-svg-icon
                                icon-class="question-circle"
                                class-name="ct-sort-drag-icon"
                            ></ct-svg-icon></el-tooltip></span
                ></span>
                <div class="ct-table-wrapper">
                    <el-table class="origin-table auto-table" :data="form.error_code">
                        <el-table-column prop="code" :label="$t('domain.detail.label41')">
                            <template #default="scope">
                                <el-form-item
                                    label=""
                                    :prop="`error_code.` + scope.$index + `.code`"
                                    :rules="rules.code"
                                    :class="{
                                        'table-form-item-wrapper': Object.values(
                                            ipValidateResultMap[scope.$index] || {}
                                        ).includes(false),
                                    }"
                                >
                                    <el-input
                                        v-model="scope.row.code"
                                        :placeholder="$t('domain.detail.placeholder3')"
                                        class="input-style"
                                        @change="handleChange"
                                    ></el-input>
                                </el-form-item>
                            </template>
                        </el-table-column>
                        <el-table-column prop="ttl" :label="$t('domain.detail.label33')">
                            <template #default="scope">
                                <el-form-item
                                    label=""
                                    :prop="`error_code.` + scope.$index"
                                    :rules="[
                                        {
                                            validator: validTtl,
                                            trigger: 'blur',
                                        },
                                    ]"
                                >
                                    <div class="flex-row-style combination-box">
                                        <el-input
                                            v-model.number="scope.row.ttl"
                                            maxlength="16"
                                            class="input-box"
                                            @change="handleChange"
                                            :placeholder="$t('domain.enter')"
                                        ></el-input>
                                        <el-select
                                            v-model="scope.row.timeType"
                                            @change="handleChange"
                                            class="select-box"
                                            :placeholder="$t('domain.editPage.placeholder8')"
                                        >
                                            <el-option
                                                :label="$t('simpleForm.alogicCacheMixin.CacheTtlMap.2')"
                                                value="2"
                                            />
                                            <el-option
                                                :label="$t('simpleForm.alogicCacheMixin.CacheTtlMap.3')"
                                                value="3"
                                            />
                                            <el-option
                                                :label="$t('simpleForm.alogicCacheMixin.CacheTtlMap.4')"
                                                value="4"
                                            />
                                            <el-option
                                                :label="$t('simpleForm.alogicCacheMixin.CacheTtlMap.5')"
                                                value="5"
                                            />
                                        </el-select>
                                    </div>
                                </el-form-item>
                            </template>
                        </el-table-column>
                        <el-table-column :label="$t('domain.operate')" width="80">
                            <template #default="scope">
                                <el-button
                                    type="text"
                                    @click="onOperator(scope.row, 'delete', 'error_code', scope.$index)"
                                    >{{ $t("domain.delete") }}</el-button
                                >
                            </template>
                        </el-table-column>
                    </el-table>
                    <div class="flex-row-style button-box">
                        <el-button class="btn" type="text" @click="onOperator(null, 'create', 'error_code')">
                            + {{ $t("domain.editPage.label10") }}
                        </el-button>
                    </div>
                </div>
            </el-form-item>
        </el-form>
    </batch-edit-checked>
</template>

<script lang="ts">
import { Component, Mixins } from "vue-property-decorator";
import ctSvgIcon from "@/components/ctSvgIcon/index.vue";
import i18n from "@/i18n/index";
import { cloneDeep } from "lodash-es";
import { cacheStatusCodeValidator, checkArrayRepeat } from "@/utils/validator.utils";
import BatchItemMixin from "../mixins/batch.mixin";
import { transferTimeType } from "@/utils/utils";

interface ErrorCodeItem {
    code: string;
    ttl: number;
    timeType: string;
}

@Component({
    name: "BatchEditErrorCode",
    components: {
        ctSvgIcon,
    },
})
export default class BatchEditErrorCode extends Mixins(BatchItemMixin) {
    currentType = "create";
    form: {
        error_code: ErrorCodeItem[];
    } = {
        error_code: [], // 状态码缓存
    };
    ipValidateResultMap: any = {};
    rules = {
        code: {
            validator: (rule: any, value: any, callback: any) => {
                let currentCode: string[] = [];
                let repeatCode = "";
                const code = value.replace(/,$/gi, "");
                //判断是编辑还是新增
                const bufferCode = cloneDeep(this.form.error_code);
                //获取当前的code码
                bufferCode.forEach((item: ErrorCodeItem) => {
                    currentCode = currentCode.concat(item.code.split(","));
                });
                repeatCode = checkArrayRepeat(currentCode);
                if (!value) {
                    return callback(i18n.t("domain.detail.placeholder8"));
                }
                //检查状态码重复性
                if (repeatCode.length > 0) {
                    callback(new Error(`${i18n.t("domain.detail.tip26")}${repeatCode}`));
                }
                //检查状态码规范性
                if (!cacheStatusCodeValidator(code.split(","))) {
                    callback(new Error(this.$t("domain.detail.tip27") as string));
                }
                const regex = /^(?:\d+,)*\d+$/;
                if (!regex.test(value)) {
                    callback(new Error(this.$t("domain.editPage.tip44") as string));
                }
                callback();
            },
            trigger: ["blur", "change"],
        },
    };

    handleChange() {
        this.$emit("onChange", this.form.error_code);
    }
    async onOperator(
        row: ErrorCodeItem | null,
        currentType: "create" | "delete",
        tabName: "error_code",
        i = 0
    ) {
        this.currentType = currentType;
        const getTime = new Date().getTime();
        if (currentType === "create") {
            const defaultFormMap = {
                error_code: { ttl: 80, code: "", timeType: "5" },
            };
            row = defaultFormMap[tabName];
        }
        if (currentType === "delete") {
            const msgMap = {
                error_code: this.$t("domain.editPage.tip20"),
            };

            // 二次确认弹窗
            await this.$confirm(msgMap[tabName] as string, this.$t("domain.delete") as string, {
                type: "warning",
            });
            this.form[tabName].splice(i, 1);
            this.$emit("onChange", this.form.error_code);
        } else {
            this.form[tabName].push(row as ErrorCodeItem);
        }
    }
    handleValidateEvent(...result: any[]) {
        const [prop, valid] = result;
        const index = prop.split(".")[1];
        const ipMap = this.ipValidateResultMap[index] || {};
        ipMap[prop] = valid;
        // 这里需要用$set是因为ipValidateResultMap的属性都是动态添加的，Vue没有办法监听到
        this.$set(this.ipValidateResultMap, index, ipMap);
    }
    validTtl(rule: any, value: any, callback: any) {
        if (!value?.ttl && value.ttl !== 0) {
            return callback(this.$t("domain.detail.placeholder29"));
        }
        const ttlReg = /^\d+$/;
        if (!ttlReg.test(value.ttl)) {
            return callback(new Error(this.$t("domain.editPage.ruleTip12") as string));
        } else if (value.timeType === "2" && Number(value.ttl) > 1095)
            callback(new Error(this.$t("domain.detail.ttlTip[3]") as string));
        else if (value.timeType === "3" && Number(value.ttl) > 26280)
            callback(new Error(this.$t("domain.detail.ttlTip[4]") as string));
        else if (value.timeType === "4" && Number(value.ttl) > 1576800)
            callback(new Error(this.$t("domain.detail.ttlTip[5]") as string));
        else if (value.timeType === "5" && Number(value.ttl) > 94608000)
            callback(new Error(this.$t("domain.detail.ttlTip[6]") as string));
        else callback();
    }
    get formData() {
        const { error_code }: { error_code: ErrorCodeItem[] } = cloneDeep(this.form);

        return {
            error_code: error_code.map(itm => ({
                ...itm,
                ttl: transferTimeType(itm.timeType, itm.ttl),
            })),
        };
    }
}
</script>

<style lang="scss" scoped>
@import "../commonStyle.scss";
.ct-edit-wrapper {
    width: 100%;
}
.combination-box {
    position: relative;

    .is-focus {
        z-index: 1000 !important;
    }

    .select-box {
        flex: 1.2 !important;
        min-width: 60px !important;
        ::v-deep {
            .el-input.el-input--medium .el-input__inner {
                border-top-left-radius: 0px !important;
                border-bottom-left-radius: 0px !important;
            }
        }
    }

    .input-box {
        flex: 2 !important;
        min-width: 70px !important;
        position: relative;
        left: 1px !important;
        ::v-deep {
            .el-input__inner {
                border-top-right-radius: 0px !important;
                border-bottom-right-radius: 0px !important;
            }
        }
    }
    ::v-deep {
        .el-input.el-input--medium .el-input__inner {
            // border-top-left-radius: 0px !important;
            // border-bottom-left-radius: 0px !important;
            // border-radius: 0 !important;
            border-color: #d9d9d9 !important;
        }
        .el-select .el-input.is-focus .el-input__inner {
            border-color: #d9d9d9 !important;
        }
    }
}
</style>
