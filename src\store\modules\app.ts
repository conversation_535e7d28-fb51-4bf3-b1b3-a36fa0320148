import { VuexModule, Module, Mutation, Action, getModule } from "vuex-module-decorators";
import store from "@/store";

@Module({ dynamic: true, store, name: "appLogic" })
class AppLogic extends VuexModule {
    public routerAppending = false; // 路由是否阻塞中,用于阻塞路由离开前，左侧菜单高亮展示
    public breadcrumbData: any = []; // 面包屑数据
    public baseAppRouter: any = null; // 基座（主应用路由实例，用于主应用层面的路由跳转）
    public currentMicroApp = ""; // 当前微应用name
    public microAppLoading = false;
    public contenProductLoading = false; // 产品内容加载状态，fcdn 加载状态
    public contentMenuLoading = false; // 菜单内容加载状态，fcdn 加载状态

    @Mutation
    SET_CONTENT_PRODUCT_LOADING(isLoading: boolean) {
        this.contenProductLoading = isLoading;
    }
    @Mutation
    SET_CONTENT_MENU_LOADING(isLoading: boolean) {
        this.contentMenuLoading = isLoading;
    }

    @Mutation
    SET_ROUTER_APPENDING(isRouterAppending: boolean) {
        this.routerAppending = isRouterAppending;
    }

    @Mutation
    SET_CURRENT_MICRO_APP(name: string) {
        this.currentMicroApp = name;
    }

    /**
     * 同步微应用加载状态
     * @param loading
     * @constructor
     */
    @Mutation
    SET_MICRO_APP_LOADING(loading: boolean) {
        this.microAppLoading = loading;
    }

    /**
     * 同步面包屑数据
     * @param arr
     * @constructor
     */
    @Mutation
    SET_BREADCRUMB_DATA(arr: any) {
        const data = arr.map((item: any) => {
            if (item?.name) {
                item.title = item.name;
            }

            if (item?.title) {
                item.name = item.title;
            }
            return {
                ...item,
            };
        });

        this.breadcrumbData = data;
    }

    /**
     * 同步主应用路由实例
     * @param routerInstance
     * @constructor
     */
    @Mutation
    SET_BASE_APP_ROUTER(routerInstance: any) {
        this.baseAppRouter = routerInstance;
    }
}

export const AppModule = getModule(AppLogic);
