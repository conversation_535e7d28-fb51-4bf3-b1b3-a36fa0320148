/*
 * @Description: 请求相关的工具函数
 * @Author: wang <PERSON><PERSON>
 */

// 延迟时间 1s
const TIMER = 1000;
// 相关请求延迟1s
export const delayedResponse = (fetch: Promise<unknown>, timer = TIMER) => {
    return new Promise((resolve, reject) => {
        fetch
            .then(rst => {
                setTimeout(() => {
                    // 接口正常延迟返回
                    resolve(rst);
                }, timer);
            })
            .catch(err => {
                // 接口报错立即响应
                reject(err);
            });
    });
};
