<template>
    <ct-box :tags="$t('home.product.title')">
        <el-table
            :empty-text="$t('common.table.empty')"
            :data="filteredProductData"
            class="ud-scrollbar"
            ref="table"
        >
            <el-table-column
                :label="$t('home.product.label1')"
                :formatter="formatterProduct"
            />
            <el-table-column
                :label="$t('home.product.label2')"
                :formatter="formatterBilling"
                align="center"
            />
        </el-table>
        <div class="btns" v-if="!isVip && !isCtclouds">
            <div>
                <el-button type="ct" @click="goBuy" v-if="flowpkgUrl">
                    {{ flowpkgTitle || $t("home.product.btn1") }}
                </el-button>
                <el-button type="primary" plain @click="$router.push('/billing')">
                    {{ $t("home.product.btn2") }}
                </el-button>
            </div>
        </div>
    </ct-box>
</template>

<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import { ProductModule } from "@/store/modules/ncdn/nproduct"; // cdn独有
import { nUserModule } from "@/store/modules/nuser";
import { billingData } from "@/types/home";
import { nBannerList } from "@/config/url";
import { BaseBannerItem } from "@/types/common";
import { domainLangAutoComple } from "@/utils";

@Component
export default class Billing extends Vue {
    private flowpkgUrl = "";
    private flowpkgTitle = "";
    get productList() {
        return ProductModule.canUseList;
    }
    get filteredProductData() {
        /**
         * 因为静态请求数的单位为万次，不涉及流量和日带宽峰值，需做隐藏处理，即不需要显示，包括中国内地和全球不含中国内地加速区域。
         * 200 CDN加速-静态HTTPS请求数-中国内地
         * 201 CDN加速-静态HTTPS请求数-全球不含中国内地
         * 202 CDN加速-静态QUIC请求数-中国内地
         * 203 CDN加速-静态QUIC请求数-全球不含中国内地
         */
        return this.productList
            .filter(item => !["200", "201", "202", "203"].includes(item.product_code))
            // 过滤内容审核
            .filter(item => !["136", "137", "138", "139", "140"].includes(item.product_code))
            // 过滤视频直播主产品
            .filter(item => !["005", "127"].includes(item.product_code))
            // 过滤边缘函数
            .filter(item => !["229", "230", "231", "232", "233", "234", "236", "237", "238", "239", "240", "241", "242", "243", "244"].includes(item.product_code))
            // 过滤视频直播增值服务
            .filter(item => !["141", "142", "143", "144", "145", "146", "147", "148", "245"].includes(item.product_code))
            // 过滤请求数
            .filter(item => !["204", "205", "206", "207", "208", "209", "210", "211", "212", "213", "214", "215", "216", "217"].includes(item.product_code));
    }
    get isVip() {
        return nUserModule.isVip;
    }
    get isCtclouds() {
        return nUserModule.isCtclouds;
    }
    mounted() {
        this.getShowBtn();
    }

    protected formatterProduct(row: billingData) {
        return row.product_cname;
    }
    protected formatterBilling(row: billingData) {
        return row.product_type === "flow_packet" ? this.$t("home.product.flowPkg") : row.billing_type_cname;
    }
    // 能看到按钮的用户必然有权限，没必要判断权限了
    protected goBuy() {
        window.open(this.flowpkgUrl);
    }

    async getShowBtn() {
        try {
            const rst = await this.$ctFetch<{ list: BaseBannerItem[] }>(nBannerList, {
                cache: true,
                data: {
                    domain: domainLangAutoComple("cdn.flowpkg.buy", nUserModule.lang),
                },
            });
            this.flowpkgUrl = rst.list[0] && rst.list[0].href;
            this.flowpkgTitle = (rst.list[0] && rst.list[0].title) || "";
        } catch (e) {
            // 操作按钮报错不做提示
        }
    }
}
</script>

<style lang="scss" scoped>
// 实现 table 自适应高度滚动
.ct-box {
    display: flex;
    flex-direction: column;
}

.el-table {
    flex: 1;
    margin: 0;
    &::before {
        content: none;
    }
}

.btns {
    margin: 8px 0;
    min-height: 0;
    text-align: center;
}

::v-deep {
    .el-table__body-wrapper {
        height: 100%;
        overflow-y: auto;
    }
    .el-table {
        .el-table__body {
            width: 100% !important;
        }
        .cell {
            // 使换行符\n生效
            white-space: pre-line;
        }
    }
}
</style>
