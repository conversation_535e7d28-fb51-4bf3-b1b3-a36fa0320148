<template>
    <div class="mp4-wrapper">
        <el-form
            label-width="140px"
            label-position="left"
            :model="form"
            :rules="rules"
            ref="mp4Form"
            :disabled="!isEdit || !isService || isLockMp4"
        >
            <div>
                <el-form-item :label="$t('domain.detail.label95')" prop="switch">
                    <el-switch
                        v-model="form.switch"
                        :active-value="1"
                        :inactive-value="0"
                        @change="switchChange"
                    ></el-switch>
                </el-form-item>

                <div v-if="form.switch" class="switch-wrapper">
                    <el-form-item
                        :label="$t('domain.type')"
                        :prop="isLockMp4 || !isStaticsAbilityOn ? null : `mode`"
                    >
                        <el-radio-group
                            v-model.number="form.mode"
                            @change="typeChange"
                            class="cache-form--selector"
                        >
                            <el-radio :label="0">{{ $t("domain.detail.cacheModeMap[0]") }}</el-radio>
                            <el-radio :label="1">{{ $t("domain.detail.cacheModeMap[1]") }}</el-radio>
                            <el-radio :label="2">{{ $t("domain.detail.cacheModeMap[2]") }}</el-radio>
                            <el-radio :label="3">{{ $t("domain.detail.cacheModeMap[3]") }}</el-radio>
                            <el-radio :label="4">{{ $t("domain.detail.cacheModeMap[4]") }}</el-radio>
                        </el-radio-group>
                    </el-form-item>

                    <el-form-item key="content" :label="$t('domain.content')" prop="content" class="is-required">
                        <el-input
                            v-model="form.content"
                            @change="handleChange"
                            :placeholder="fileTypePlaceholder()"
                            :disabled="form.mode === 2 || form.mode === 3"
                            clearable
                            class="input-style"
                            @focus="e => showNameSet(e, form)"
                        ></el-input>
                    </el-form-item>

                    <el-form-item :label="$t('domain.detail.label93')" prop="start_params">
                        <el-input
                            v-model="form.start_params"
                            @change="handleChange"
                            placeholder=""
                            clearable
                            class="input-style"
                        ></el-input>
                    </el-form-item>

                    <el-form-item :label="$t('domain.detail.label94')" prop="end_params">
                        <el-input
                            v-model="form.end_params"
                            @change="handleChange"
                            placeholder=""
                            clearable
                            class="input-style"
                        ></el-input>
                    </el-form-item>
                </div>
            </div>
        </el-form>

        <file-suffix-dialog
            :form="suffixDialogForm.form"
            :visible="suffixDialogForm.visible"
            @cancel="suffixDialogForm.visible = false"
            @submit="suffixDialogFormSubmit"
        />
    </div>
</template>

<script>
// import ctSvgIcon from "@/components/ctSvgIcon";
import componentMixin from "@/views/domainConfig/componentMixin";
import { allPathPattern } from '@/utils/validator.utils';
import fileSuffixDialog from "@/components/fileSuffixDialog/index.vue";
import { cloneDeep } from 'lodash-es';
import { SecurityAbilityModule } from "@/store/modules/securityAbility";

export default {
    name: "mp4",
    components: { fileSuffixDialog },
    mixins: [componentMixin],
    props: {
        datas: Object,
        isLockMp4: Boolean,
        isStaticsAbilityOn: Boolean,
    },
    data() {
        return {
            form: {
                switch: 0,
                mode: 0,
                content: "",
                start_params: "start",
                end_params: "end",
            },
            suffixDialogForm: {
                visible: false,
                form: null,
            },
            rules: {
                mode: [{ required: true, message: this.$t("domain.detail.tip42"), trigger: "change" }],
                content: [
                    {
                        validator: (rule, value, callback) => {
                            if (this.isLockMp4 || !this.isStaticsAbilityOn) callback();
                            const paths = value?.split(",");
                            paths?.forEach(path => {
                                if (
                                    parseInt(this.form.mode) === 4 &&
                                    (path.includes("?") || path.includes("？"))
                                )
                                    callback(this.$t("domain.detail.placeholder48"));
                                if (parseInt(this.form.mode) === 4 && path.length > 0 && path[0] !== "/")
                                    callback(this.$t("domain.detail.placeholder49"));
                            });
                            if (parseInt(this.form.mode) === 0 && !value)
                                callback(this.$t("domain.htmlForbid.forbid4"));
                            if (parseInt(this.form.mode) === 0 && !/^\w{1,9}(,\w{1,9})*$/.test(value))
                                callback(this.$t("domain.detail.placeholder50"));
                            if (parseInt(this.form.mode) === 1 && !value)
                                callback(this.$t("domain.detail.placeholder73"));
                            if (parseInt(this.form.mode) === 1 && !/^((\/[\w-.]+)+,)*(\/[\w-.]+)+$/.test(value))
                                callback(this.$t("domain.detail.placeholder51"));
                            if (parseInt(this.form.mode) === 2 && !value)
                                callback(this.$t("domain.detail.placeholder52"));
                            if (parseInt(this.form.mode) === 3 && !value)
                                callback(this.$t("domain.detail.placeholder53"));
                            if (parseInt(this.form.mode) === 4 && value.trim() === "")
                                callback(this.$t("domain.detail.placeholder74"));
                            if (parseInt(this.form.mode) === 4 && !allPathPattern.test(value))
                                callback(this.$t("domain.detail.placeholder14"));
                            else callback();

                        },
                        trigger: ["blur", "change"],
                    },
                    // { required: true, message: this.$t("domain.detail.placeholder54"), trigger: "blur" },
                ],
            },
        };
    },
    computed: {},
    watch: {
        "datas.mp4_conf_info": {
            deep: true,
            handler(val) {
                this.init(val);
            },
            immediate: true,
        },
    },
    methods: {
        init(v) {
            this.form.switch = v?.switch;
            this.form.mode = v?.mode;
            this.form.content = v?.content;
            this.form.start_params = v?.start_params;
            this.form.end_params = v?.end_params;
        },
        handleChange() {
            this.$emit("onChange", this.form, this.form.switch);
        },
        fileTypePlaceholder() {
            return parseInt(this.form.mode) === 0
                ? this.$t("simpleForm.alogicCacheMixin.FileSuffix.0")
                : parseInt(this.form.mode) === 1
                ? this.$t("simpleForm.alogicCacheMixin.FileSuffix.1")
                : this.$t("simpleForm.alogicCacheMixin.FileSuffix.4");
        },
        async typeChange(val) {
            if (val === 2 || val === 3) {
                this.form.content = "/";
            } else {
                this.form.content = "";
            }
            this.$refs.mp4Form.validateField("content");
            this.handleChange();
        },
        switchChange(val) {
            const originalConf = SecurityAbilityModule.securityOriginForm;
            console.log('originalConf ', originalConf.mp4_conf_info)
            this.form.mode = originalConf.mp4_conf_info.mode;
            this.form.content = originalConf.mp4_conf_info.content;
            this.form.start_params = originalConf.mp4_conf_info.start_params;
            this.form.end_params = originalConf.mp4_conf_info.end_params;

            this.$emit("onChange", this.form, val);
        },
        showNameSet(e, form) {
            // 内容为后缀名才弹出弹框
            if (form.mode !== 0) return;
            this.suffixDialogForm = {
                visible: true,
                form: cloneDeep(form)
            };
            e?.srcElement?.blur();
        },
        suffixDialogFormSubmit(content) {
            this.form.content = content;
            this.suffixDialogForm.visible = false;
            this.handleChange();
        }
    },
};
</script>

<style lang="scss" scoped>
.mp4-wrapper {
    ::v-deep {
        .el-form--label-left .el-form-item__label {
            text-align: right;
        }
    }
}
.input-style {
    width: 380px;
}
.question-circle {
    font-size: $text-size-md;
    cursor: pointer;
}
</style>
