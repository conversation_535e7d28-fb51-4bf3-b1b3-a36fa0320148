{"common": {"usage": "Utilization Statistics", "rank": "Hotspots Analysis", "user": "User Analysis", "tab": ["Bandwidth and Traffic", "Back-to-origin Statistics", "Number of Requests", "Hit Rate", "Status Codes", "Back-to-origin Status Code", "PV/UV", "Region & Carrier", "Download Speed", "Premium Network Service"], "searchLabel1": "<PERSON><PERSON>", "searchLabel2": "Agreement", "searchLabel3": "Time", "searchPlaceholder": ["Product Type", "Domain", "Carrier", "Protocol Type", "Protocol Version", "Tag", "Status Code", "Time Grain", "Accelerated region"], "domainSelectOption": "All Domain Names", "searchDownloadContent": "Download Data", "chart": {"loading": "Trying to Plot...", "toolBox": ["Export CSV", "Region Zoom", "Region Zoom Restore", "Rest<PERSON>"], "errMsg": ["Domain name parameter is empty, please confirm and retry", "The current page is missing the getData method", "No statistics available,  please check and try again", "The current page is missing the tableToExcel method"]}, "table": {"loading": "Searching..."}, "searchBtn": "Query", "vchartTip1": "{type} Peak Bandwidth{num}", "vchartTip2": "{type} 95th Percentile Peak Bandwidth{num}", "vchartTip3": "{type} Total Traffic{num}", "vchartTip4": "{type} Peak Download Rate{num}", "timeGrain": ["5 Minutes", "1 Hour", "1 Day", "1 Minutes"], "abroadOptions": ["Chinese mainland", "Global (Excluding the Chinese Mainland)", "North America", "Europe", "Asia I", "Asia II", "Asia III", "Middle East & Africa", "South America"], "tableColumn": {"traffic": "Traffic ({unit})", "peakBandwidth": "Peak Bandwidth ({unit})", "peakBandwidth2": "Peak Bandwidth ({unit})"}}, "dcdn": {"headerText": "ICDN - Utilization Statistics", "headerTip": "You can query the utilization data within the latest year and the supported maximum time span of each query is one month.", "bandwidthFlowWhole": {"radioBtn1": "Bandwidth", "radioBtn2": "Traffic", "tableTip": "Statistics by Day", "dailyType": "Total", "chartType": "Bandwidth", "vchartOptions": {"title1": "Bandwidth", "title2": "Traffic", "yAxisName": "Unit: {unit}"}, "tableColumn1": "Date", "tableColumn2": "{type} Traffic ({unit})", "tableColumn3": "Peak Bandwidth({unit})", "tableColumn4": "Peak Time", "tableToExcel": {"excelName": "{chartType} data", "excelColumn1": "Time", "excelColumn2": "Total Bandwidth({mbps})", "excelColumn3": "Traffic({mb})", "excelColumn4": "{type} Bandwidth({mbps})", "excelColumn5": "{type} Traffic({mb})", "excelColumn6": "Summary：", "excelColumn7": "{type} Aggregate Peak Bandwidth{num}{suffix}", "excelColumn8": "{type} 95th Percentile Peak Bandwidth{num}{suffix}", "excelColumn9": "{type} Total Traffic{num}{suffix}", "excelColumn10": "{type} Average Traffic{num}{suffix}"}}, "missWhole": {"radioBtn1": "Back-to-origin Bandwidth", "radioBtn2": "Back-to-origin Traffic", "tableTip": "Statistics by Day", "tableColumn1": "Date", "tableColumn2": "Back-to-origin Traffic ({unit})", "tableColumn3": "Back-to-origin Peak Bandwidth ({unit})", "tableColumn4": "Back-to-origin Peak Bandwidth Time", "vchartOptions": {"title1": "Bandwidth", "title2": "Traffic", "yAxisName": "Company:{unit}"}, "tableToExcel": {"excelName": "{chartType}data", "excelColumn1": "Time", "excelColumn2": "Back-to-origin Bandwidth ({mbps})", "excelColumn3": "Back-to-origin Traffic ({mb})", "excelColumn4": "Summary：", "excelColumn5": "Back-to-origin Peak Bandwidth", "excelColumn6": "Total Back-to-origin Traffic", "excelColumn7": "Average Back-to-origin Traffic"}}, "requestWhole": {"radioBtn1": "Number of Requests", "tableTip": "Statistics by Day", "tableColumn1": "Serial No", "tableColumn2": "Date", "localRequestType": {"websocket": "Number of Websocket Requests"}, "vchartOptions": {"toolTips": "", "yAxisName": "Unit: times"}, "summariesName": "Summary", "tableToExcel": {"excelName": "Number of Requests Data", "excelColumn1": "Summary：", "excelColumn2": "Time"}}, "hit": {"radioBtn1": "Traffic Hit Rate", "radioBtn2": "Request Hit Rate", "vchartTip": "{showFlow}Request Hit Rate", "totalTipPrefix1": "Traffic", "totalTipPrefix2": "Requests", "totalTip": "Peak {totalTipPrefix} Hit Rate", "vchartOptions": {"yAxisName": "Unit: %"}, "seriesName1": "Traffic Hit Rate", "seriesName2": "Request Hit Rate", "tableToExcel": {"excelName1": "Traffic Hit Ratio Data", "excelName2": "Request Hit Rate Data", "excelColumn1": "Time,Hit Traffic(B),Traffic(B),Percentage of Hit Traffic(%)", "excelColumn2": "Time,Hit Counts,Number of Requests,Request Hit Rate(%)"}}, "statusCode": {"radioBtn": "All Status Codes", "totalTip": "Total Status Codes:", "totalTipUnit": "", "itemTip": "Status Code Quantity:", "ctTip1": "Status Code Percentage by Table", "ctTip2": "Status Code Percentage by Pie Chart", "tableColumn1": "Status Code Name", "tableColumn2": "Number of Status Codes", "tableColumn3": "Proportion (%)", "vchartOptions": {"totalTip": "Total Status Codes", "itemTip": "Status Code Quantity", "seriesItemName1": "Other", "yAxisName": "Unit: times", "seriesItemName2": "Status Codes"}, "tableToExcel": {"excelName": "Status Codes Data", "excelColumn1": "Time", "excelColumn2": "Total Quantity,{totalCode}", "excelColumn3": "Status Code Quantity,{itemsTotal}", "tableName1": "All Status Code Proportion Statistics", "tableName2": "Status Code Proportion Statistics"}}, "backToOriginStatusCode": {"radioBtn": "All Back-to-origin Status Code", "totalTip": "Total Back-to-origin Status Code:", "totalTipUnit": "", "itemTip": "Back-to-origin Status Code Quantity:", "ctTip1": "Back-to-origin Status Code Percentage by Table", "ctTip2": "Back-to-origin Status Code Percentage by Pie Chart", "tableColumn1": "Back-to-origin Status Code Name", "tableColumn2": "Number of Back-to-origin Status Code", "tableColumn3": "Proportion (%)", "vchartOptions": {"totalTip": "Total Back-to-origin\n Status Code", "itemTip": "Back-to-origin\n Status Code Quantity", "seriesItemName1": "Other", "yAxisName": "Unit: times", "seriesItemName2": "Back-to-origin Status Code"}, "tableToExcel": {"excelName": "Back-to-origin Status Code Data", "excelColumn1": "Time", "excelColumn2": "Total Quantity,{totalCode}", "excelColumn3": "Back-to-origin Status Code Quantity,{itemsTotal}", "tableName1": "All Back-to-origin Status Code Proportion Statistics", "tableName2": "Back-to-origin Status Code Proportion Statistics"}}, "PvUv": {"totalTip1New": "Peak {type}:", "totalTipUnit": "{max} ", "totalTip2": "Total {type}:", "totalTipUnit1": "", "totalTipUnit2": "", "ctTip": "Statistics by Day", "tableColumn1": "Serial No", "tableColumn2": "Date", "tableColumn3": "PV", "tableColumn4": "UV", "searchTip1": "Please Select a Domain And Click 'Search'", "searchTip2": "No Records Match The Criteria, Please Try Other Criteria", "vchartOptions": {"yAxisName": "Unit: times", "totalTipUnit": "{marker} {value}"}, "tableToExcel": {"excelName": "{title} Data", "excelColumn1": "Time,{chartType}(second)", "excelColumn2": "{chartType}Peak Value (1 hour statistics)", "excelColumn3": "{chartType} Total", "excelColumn4": "{chartType}Peak Value(statistics by day)"}, "getSummariesItem": "Summary"}, "privateNetworkAcceleratorWhole": {"radioBtn1": "Bandwidth", "radioBtn2": "Traffic", "tableTip": "Statistics by Day", "dailyType": "Total", "chartType": "Bandwidth", "vchartOptions": {"title1": "Bandwidth", "title2": "Traffic", "yAxisName": "Unit: {unit}"}, "tableColumn1": "Date", "tableColumn2": "{type} Traffic ({unit})", "tableColumn3": "Peak Bandwidth({unit})", "tableColumn4": "Peak Time", "tableToExcel": {"excelName": "{chartType} data", "excelColumn1": "Time", "excelColumn2": "Total Bandwidth({mbps})", "excelColumn3": "Traffic({mb})", "excelColumn4": "{type} Bandwidth({mbps})", "excelColumn5": "{type} Traffic({mb})", "excelColumn6": "Summary：", "excelColumn7": "{type} Aggregate Peak Bandwidth{num}{suffix}", "excelColumn8": "{type} 95th Percentile Peak Bandwidth{num}{suffix}", "excelColumn9": "{type} Total Traffic{num}{suffix}", "excelColumn10": "{type} Average Traffic{num}{suffix}"}}}, "usageQuery": {"headerText": "Utilization Statistics", "headerTip": "You can query the usage data within six months and the longest time span is one month.", "tip1": "Monthly Average of Daily Peaks", "tip2": "95th Percentile Peak Bandwidth", "BandwidthFlow": {"ctTip": "Statistics by Day", "tableColumn1": "Date", "tableColumn2": "Traffic ({unit})", "tableColumn3": "Peak Bandwidth ({unit})", "tableColumn4": "Peak Time", "tableColumn5": "Back-to-origin Peak Bandwidth ({unit})", "tableColumn6": "Back-to-origin Peak Bandwidth Time", "chartType": "Bandwidth", "chartOptions": {"title1": "Bandwidth", "title2": "Traffic", "yAxisName": "Unit: {unit}"}, "tableToExcel": {"excelName": "{chartType} Data", "excelColumn1": "Time", "excelColumn2": "Bandwidth", "excelColumn3": "Bandwidth({mbps})", "excelColumn4": "Traffic({mb})", "excelColumn5": "Summary:", "excelColumn6": "Aggregate Peak Bandwidth{num}{suffix}", "excelColumn7": "95th Percentile Peak Bandwidth{num}{suffix}", "excelColumn8": "Total Traffic{num}{suffix}", "excelColumn9": "Average Traffic{num}{suffix}"}}, "downloadSpeed": {"tableTip": "Statistics by Day", "dailyType": "", "chartType": "Download Rate", "chartOptions": {"title1": "Download Rate", "yAxisName": "Unit: {unit}"}, "tableColumn1": "Date", "tableColumn2": "Traffic ({unit})", "tableColumn3": "{type}Peak Download Rate{unit}", "tableColumn4": "Peak Time", "tableToExcel": {"excelName": "{chartType} Data", "excelColumn1": "Time", "excelColumn2": "Total Download Rate({unit})", "excelColumn3": "Traffic({mb})", "excelColumn4": "{type} Download Rate({bps})", "excelColumn5": "{type} Traffic({mb})", "excelColumn6": "Summary：", "excelColumn7": "{type} Aggregated Peak Download Rate{num}{suffix}", "excelColumn8": "{type} 95 Percentile Peak Download Rate{num}{suffix}"}}, "miss": {"radioBtn1": "Back-to-origin Bandwidth", "radioBtn2": "Back-to-origin Traffic", "chartType": "Back-to-origin Bandwidth", "chartOptions": {"title1": "bandwidth", "title2": "traffic", "yAxisName": "Unit: {unit}"}, "tableToExcel": {"excelName": "{chartType}data", "excelColumn1": "Time", "excelColumn2": "Back-to-origin Bandwidth({mbps})", "excelColumn3": "Back-to-origin Traffic({mb})", "excelColumn4": "Summary:", "excelColumn5": "Back-to-origin peak bandwidth", "excelColumn6": "Total Back-to-origin traffic", "excelColumn7": "Average Back-to-origin Traffic"}}, "request": {"radioBtn1": "Request", "radioBtn2": "Back-to-origin Request", "totalTip1": "Total Number of Requests", "totalTip2": "Total Back-to-origin Requests", "totalTip3": "Peak Number of Requests", "totalTip4": "Peak Number of Back-to-origin Requests", "totalTip5": "Valley Number of Requests", "totalTip6": "Valley Number of Back-to-origin Requests", "totalTipUnit": "{value}", "ctTip": "Statistics by Day", "tableColumn1": "Serial No", "tableColumn2": "Date", "tableColumn3": "Number of Requests", "tableColumn4": "Number of Requests Back-to-origin", "chartType": "Request", "chartOptions": {"title1": "Request", "title2": "Back-to-origin Request", "yAxisName": "Unit: times"}, "getSummaries": "Summary", "tableToExcel": {"excelName": "Number of requests data", "excelName2": "Number of Requests Back-to-origin data", "excelColumn1": "Time", "excelColumn2": "Number of Requests", "excelColumn3": "Number of Requests Back-to-origin", "excelColumn4": "Summary:", "excelColumn5": "Total Number of Requests", "excelColumn6": "Peak Number of Requests", "excelColumn7": "Valley Number of Requests"}}, "PrivateNetworkAccelerator": {"ctTip": "Statistics by Day", "tableColumn1": "Date", "tableColumn2": "Traffic ({unit})", "tableColumn3": "Peak Bandwidth ({unit})", "tableColumn4": "Peak Time", "tableColumn5": "Back-to-origin Peak Bandwidth ({unit})", "tableColumn6": "Back-to-origin Peak Bandwidth Time", "chartType": "Bandwidth", "chartOptions": {"title1": "Bandwidth", "title2": "Traffic", "yAxisName": "Unit: {unit}"}, "tableToExcel": {"excelName": "{chartType} Data", "excelColumn1": "Time", "excelColumn2": "Bandwidth", "excelColumn3": "Bandwidth({mbps})", "excelColumn4": "Traffic({mb})", "excelColumn5": "Summary:", "excelColumn6": "Aggregate Peak Bandwidth{num}{suffix}", "excelColumn7": "95th Percentile Peak Bandwidth{num}{suffix}", "excelColumn8": "Total Traffic{num}{suffix}", "excelColumn9": "Average Traffic{num}{suffix}"}}}, "rank": {"headerText": "Hotspots Analysis", "headerTip": "You can query the hotspot data within the latest three months and the supported maximum time span of each query is one month.", "searchBar": {"areaSelectRadioText1": "Ranking Based on Traffic", "areaSelectRadioText2": "Ranking Based on The Number of Visits"}, "common": {"tab": ["Top URLs", "Top URLs (Back-to-origin)", "Popular Referer", "Domain Ranking", "TOP Client IP"], "tableColumn1": "Rank", "tableColumn2": "URL", "tableColumn3": "Traffic", "tableColumn4": "Traffic Share (%)", "tableColumn5": "Number of Visits", "tableColumn6": "Visit Share (%)", "tableToExcel": {"excelName": "Top URLs", "excelColumn1": "URL, Traffic, Traffic Share (%), Number of Visits, Visit Share (%)", "excelColumn2": "Summary:", "excelColumn3": "Total Number of Visits: {num}", "excelColumn4": "Total Traffic: {flow}"}}, "hotUrlMiss": {"tableToExcel": {"excelName": "Top URLs (Back-to-origin)"}}, "hotReferer": {"tableToExcel": {"excelName": "Popular Referer", "excelColumn1": "Referer, Traffic, Traffic Share (%), Number of Visits, Visit Share (%)"}}, "domainRank": {"tableColumn1": "Rank", "tableColumn2": "Domain", "tableColumn3": "Traffic", "tableColumn4": "Traffic Share (%)", "tableColumn5": "Peak Bandwidth", "tableColumn6": "Peak Bandwidth Moment", "tableColumn7": "Number of Visits", "tableToExcel": {"excelName": "Domain Ranking", "excelColumn1": "Domain Name, Traffic, Percentage of Traffic (%), Peak Bandwidth, Peak Bandwidth Moment, Number of Accesses"}}, "timeSelect": "Top URLs, Top URLs (Back-to-origin), Popular Referer, TOP Client IP, and others are calculated by hour. The time range selection only supports full-hour intervals."}, "provider": {"providerData": "Regional Carrier Data", "area": "Region", "flowPercent": "Traffic Proportion (%)", "requestPercent": "Request Proportion (%)", "statisticsDomain": "Statistic Domain Name", "province": "Province/State", "bandwidth": "Bandwidth", "traffic": "Traffic", "visitPercent": "Access Proportion", "all": "All", "statisticsIsp": "Statistic Carrier", "mainland": "Chinese mainland", "overseas": "HK/MO/TW&other regions", "overseas2": "HK_MO_TW&other regions", "overseas3": "Global"}, "router": {"tip1": "Site Statistics", "tip2": "Data Analysis", "tip3": "CDN Usage", "tip4": "ICDN Usage", "tip5": "Domain Name Usage Analysis"}, "user": {"headerTip": "You can query user data within one year. The maximum time span that you can specify for a query is one month.", "tip1": "Regional Distribution of Users", "tip2": "User Location", "tip3": "Page {num}", "tip4": "Total {num}", "tip5": ["High", "Low"], "tip6": "User Regions", "tip7": "User's ISP", "tip8": "ISP Distribution", "tip9": "ISP", "tip10": "ISP Data", "tip11": "Unique IP Access Count", "tip12": "Unique IP Access Peak", "tip13": "Daily Active", "tip14": "One-hour Statistics", "tip15": "Total Daily Active IPs:", "tip16": "Time", "tip17": "{prefix}times", "tip18": "Peak", "tip19": "Total Amount", "tip20": "IP Data"}, "eas": {"tip1": "IP Application Accelerator Usage", "tip2": "No instance", "tip3": "Total Bandwidth", "tip4": "Total Traffic", "tip5": "Please select the instance", "tip6": "All Instances", "tab": ["Bandwidth", "Traffic", "Number of Connections", "Region & Carrier", "Domain Ranking", "TOP Client IP"], "bandwidthList": ["Total Bandwidth", "Uplink Bandwidth", "Downlink Bandwidth"], "flowList": ["Total Traffic", "Uplink Traffic", "Downlink Traffic"], "tip7": "IP Application Accelerator Overview", "tip8": "Peak Bandwidth Time", "tip9": "Proportion of Connections (%)", "tip10": "Peak Concurrent Connections", "tip11": "Time of Peak Concurrent Connections", "excel": {"tip1": "Total Number of Connections:", "tip2": "Total Traffic:", "tip3": "Peak Connection:", "tip4": "Valley Connections:", "tip5": "Peak Concurrent Connections:", "tip6": "Valley Concurrent Connections:"}, "tip12": "Top Client IPs", "tip13": "Statistics by Day", "tip14": "Bandwidth Data", "tip15": "Number of Concurrent Connections", "tip16": "Total Number of Connections", "tip17": "Peak Connection", "tip18": "Valley Connections", "tip19": "Peak Concurrent Connections", "tip20": "Valley Concurrent Connections", "tip21": "Connection Data", "tip22": "ISP List", "tip23": "Other Regions in China", "tip24": "Number of connections (quantity)", "tip25": "Peak number of concurrent connections (quantity/s)", "tip26": "Traffic Value", "tip27": "Export time", "tip28": "Prioritize the Number of Connections", "tip29": "Concurrent Connection Data", "tip30": "Traffic Data", "tip31": "Other Region Outside Chinese Mainland", "connections": {"tip1": "{value}", "tip2": "{value} per second", "unit1": "Unit: quantity", "unit2": "Unit: quantity/s"}}}