<template>
    <ct-section-wrap
        headerText="全局task脚本"
        headerTip="全局task脚本用于定义后台周期性任务（非客户端请求触发），比如周期性同步远端配置数据到本地全局字典中。"
    >
        <template #headerBtn>
            <el-button @click="search" :disabled="loading">刷新</el-button>
            <el-button type="primary" @click="detailTask({}, 'add')">添加全局task脚本</el-button>
        </template>
        <ct-box :class="[{ 'fix-box': dataList.length > 11 }, 'table-scroll-wrap']">
            <ct-table
                :dataList="dataList"
                :columnConfig="columnConfig"
                :tableConfig="{ stripe: false }"
                :pagerData="{ pageNum, pageSize }"
                v-loading="loading"
            >
                <template slot="status-slot" slot-scope="{ scope }">
                    <span :class="'status-' + scope.row.deploy_status">
                        {{ statusFormatter(scope.row.deploy_status) }}
                    </span>
                </template>
                <template slot="operation-slot" slot-scope="{ scope }">
                    <el-button type="text" @click="detailTask(scope.row, 'detail')">查看</el-button>
                    <el-button
                        type="text"
                        v-if="showButton('edit', scope.row.deploy_status)"
                        @click="detailTask(scope.row, 'edit')"
                        >编辑</el-button
                    >
                    <el-button
                        type="text"
                        v-if="showButton('delete', scope.row.deploy_status)"
                        @click="handleClick('delete', scope.row)"
                        >删除</el-button
                    >
                    <el-button
                        type="text"
                        v-if="showButton('retry', scope.row.deploy_status)"
                        @click="handleClick('retry', scope.row)"
                        >失败重试</el-button
                    >
                </template>
            </ct-table>
            <ct-pager class="pager" :refresh.sync="refresh" :loadData="search"></ct-pager>
        </ct-box>
    </ct-section-wrap>
</template>

<script lang="ts">
import { Component, Mixins, Watch } from "vue-property-decorator";
import TableAndPagerActionMixin from "@/mixins/tableAndPagerAction";
import { ntaskUrl } from "@/config/url/ncdn/nscript";
import { deploymentStatusMap, GetCtiamButtonAction } from "@/config/map";
import { CtiamCode } from "@/utils/ctFetch/errorConfig";
import { checkCtiamButtonAuth } from "@/store/modules/menu";

@Component
export default class Task extends Mixins(TableAndPagerActionMixin) {
    protected prefetch = true;
    protected usePost = true;
    protected getResData = true;
    protected searchUrl = ntaskUrl.TaskList;
    private deploymentStatusMap = deploymentStatusMap;
    get columnConfig() {
        return [
            {
                prop: "task_name",
                label: "脚本名称",
                align: "left",
            },
            {
                type: "slot",
                name: "status-slot",
                baseConfig: {
                    label: "部署状态",
                    align: "left",
                },
            },
            {
                prop: "update_time",
                label: "修改时间",
                align: "left",
            },
            {
                type: "slot",
                name: "operation-slot",
                baseConfig: {
                    label: "操作",
                    minWidth: "80",
                    align: "left",
                },
            },
        ];
    }
    get getWorkspaceId() {
        return String(this.$route.query.workspaceId);
    }
    private async detailTask(row: any, type: string) {
        if (type === "add") {
            await checkCtiamButtonAuth(GetCtiamButtonAction("udfTaskAdd"));
        }
        if (type === "detail") {
            await checkCtiamButtonAuth(GetCtiamButtonAction("udfTaskView"));
        }
        if (type === "edit") {
            await checkCtiamButtonAuth(GetCtiamButtonAction("udfTaskEdit"));
        }

        if (type === "add" && this.total > 2) {
            return this.$message.warning("添加失败，当前客户可添加全局task脚本数量受限！");
        }
        this.$router.push({
            name: `nscript.task.${type}`,
            query: {
                taskId: row.id,
                operateType: type,
            },
        });
    }
    private async handleClick(type: string, row: any) {
        if (type === "delete") {
            await checkCtiamButtonAuth(GetCtiamButtonAction("udfTaskDelete"));
        }
        if (type === "retry") {
            await checkCtiamButtonAuth(GetCtiamButtonAction("udfTaskRetry"));
        }

        try {
            const operationType = type === "delete" ? "删除" : "失败重试";
            await this.$confirm(`此操作将对全局task脚本进行${operationType}操作，是否继续？`, `提示`, {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            });
            this.loading = true;
            const reqUrl = type === "delete" ? ntaskUrl.DeleteTask : ntaskUrl.RetryTask;
            await this.$ctFetch(reqUrl, {
                method: "POST",
                body: {
                    data: { taskId: row.id },
                },
                headers: {
                    "Content-Type": "application/json",
                },
            }).then(() => {
                this.$message.success("操作成功！");
                this.refresh = true;
            });
        } catch (error) {
            const isCtiamError = CtiamCode.includes((error as any)?.data?.code);
            if (isCtiamError) {
                this.$errorHandler(error);
            } else {
                const errData = error as any;
                if (errData.data && errData.data.reason) {
                    return this.$message.error(errData.data.reason);
                }
            }
        } finally {
            this.loading = false;
        }
    }
    private statusFormatter(status: number) {
        return this.deploymentStatusMap[status as keyof typeof deploymentStatusMap];
    }
    private showButton(type: string, status: number) {
        switch (type) {
            case "edit":
                return [-1, 3, 4, 7].includes(status);
            case "delete":
                return [-1, 3, 4].includes(status);
            case "retry":
                return [-1, 4, 7].includes(status);
            default:
                break;
        }
    }
    @Watch("$route", { immediate: false, deep: true })
    OnRouterChanage(newVal: any) {
        this.refresh = newVal.query.isRefresh === "true";
    }
}
</script>

<style lang="scss" scoped>
// 状态颜色
.status-3 {
    color: $g-color-green;
}
.status--1,
.status-4,
.status-7 {
    color: $g-color-red;
}
.status-0,
.status-1,
.status-2,
.status-8 {
    color: $g-color-gray;
}
</style>
