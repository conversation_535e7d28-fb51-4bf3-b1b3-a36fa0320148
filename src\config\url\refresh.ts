import { PROXY_PREFIX } from "./_PREFIX";

export const refreshUrl = {
    // 创建刷新任务
    createRefresh: `${PROXY_PREFIX}/v1/refresh/create`,
    // 刷新任务列表
    refreshList: `${PROXY_PREFIX}/v1/refresh/list`,
    // 创建预取任务
    createPreload: `${PROXY_PREFIX}/v1/preload/create`,
    // 预取任务列表
    preloadList: `${PROXY_PREFIX}/v1/preload/list`,
    // 刷新任务详情
    refreshGet: `${PROXY_PREFIX}/v1/refresh/Get`,
    // 检查是否有指定域名（非标）的权限
    checkDomainPrivilege: `${PROXY_PREFIX}/v1/domain/CheckPrivilege`,
    // 刷新定时任务
    schduelRefreshTaskCreate: `${PROXY_PREFIX}/v1/refresh/timed/create`,
    schduelRefreshTaskUpdate: `${PROXY_PREFIX}/v1/refresh/timed/update`,
    schduelRefreshTaskStatusUpdate: `${PROXY_PREFIX}/v1/refresh/timed/status`,
    // 定时任务列表
    scheduleList: `${PROXY_PREFIX}/v1/refresh/timed/list`,
};
