import { VuexModule } from "vuex-module-decorators";
import { DomainActionEnum } from "../../config";
import { SelectOptions } from "@/types/common";
import { DomainItem } from "@/types/domain";

// 域名列表单元
export interface DomainUnit {
    loading?: boolean;
    list: string[];
    options: SelectOptions[];
    nativeList: DomainItem[];
    requestTime?: number;
}

// Define your state interface
export interface BaseDomainState {
    [DomainActionEnum.Domain]: DomainUnit; // 域名管理
    [DomainActionEnum.Order]: DomainUnit; // 工单管理
    [DomainActionEnum.Data]: DomainUnit; // 统计分析、日志下载
    [DomainActionEnum.Refresh]: DomainUnit; // 刷新预取
    [DomainActionEnum.Review]: DomainUnit; // 内容审核

    [DomainActionEnum.Home]: DomainUnit;
    [DomainActionEnum.DomainLog]: DomainUnit;
    [DomainActionEnum.Label]: DomainUnit;
    [DomainActionEnum.Log]: DomainUnit;
    [DomainActionEnum.ContentAuditStatistics]: DomainUnit;
    [DomainActionEnum.ContentAuditViolations]: DomainUnit;
    [DomainActionEnum.ContentAuditLog]: DomainUnit;
}

/**
 * 创建一个新的域名单元实例
 */
const getDomainUnit = (): DomainUnit => ({
    list: [],
    options: [],
    nativeList: [],
    requestTime: 0,
    loading: false,
});

export class BaseDomain extends VuexModule implements BaseDomainState {
    // 未做权限管控前的action
    public [DomainActionEnum.Domain]: DomainUnit = getDomainUnit();
    public [DomainActionEnum.Order]: DomainUnit = getDomainUnit();
    public [DomainActionEnum.Data]: DomainUnit = getDomainUnit();
    public [DomainActionEnum.Refresh]: DomainUnit = getDomainUnit();
    public [DomainActionEnum.Review]: DomainUnit = getDomainUnit();
    // ctiam相关
    public [DomainActionEnum.Home]: DomainUnit = getDomainUnit();
    public [DomainActionEnum.DomainLog]: DomainUnit = getDomainUnit();
    public [DomainActionEnum.Label]: DomainUnit = getDomainUnit();
    public [DomainActionEnum.Log]: DomainUnit = getDomainUnit();
    public [DomainActionEnum.ContentAuditStatistics]: DomainUnit = getDomainUnit();
    public [DomainActionEnum.ContentAuditViolations]: DomainUnit = getDomainUnit();
    public [DomainActionEnum.ContentAuditLog]: DomainUnit = getDomainUnit();

    public [DomainActionEnum.BandwidthFlow]: DomainUnit = getDomainUnit();
    public [DomainActionEnum.BandwidthFlowWhole]: DomainUnit = getDomainUnit();
    public [DomainActionEnum.Miss]: DomainUnit = getDomainUnit();
    public [DomainActionEnum.MissWhole]: DomainUnit = getDomainUnit();
    public [DomainActionEnum.Request]: DomainUnit = getDomainUnit();
    public [DomainActionEnum.RequestWhole]: DomainUnit = getDomainUnit();
    public [DomainActionEnum.Hit]: DomainUnit = getDomainUnit();
    public [DomainActionEnum.HitWhole]: DomainUnit = getDomainUnit();
    public [DomainActionEnum.StatusCode]: DomainUnit = getDomainUnit();
    public [DomainActionEnum.StatusCodeWhole]: DomainUnit = getDomainUnit();
    public [DomainActionEnum.BackToOriginStatusCode]: DomainUnit = getDomainUnit();
    public [DomainActionEnum.BackToOriginStatusCodeWhole]: DomainUnit = getDomainUnit();
    public [DomainActionEnum.PvUv]: DomainUnit = getDomainUnit();
    public [DomainActionEnum.PvUvWhole]: DomainUnit = getDomainUnit();
    public [DomainActionEnum.Provider]: DomainUnit = getDomainUnit();
    public [DomainActionEnum.ProviderWhole]: DomainUnit = getDomainUnit();
    public [DomainActionEnum.DownloadSpeed]: DomainUnit = getDomainUnit();
    public [DomainActionEnum.PrivateNetworkAccelerator]: DomainUnit = getDomainUnit();
    public [DomainActionEnum.PrivateNetworkAcceleratorWhole]: DomainUnit = getDomainUnit();
    public [DomainActionEnum.HotUrl]: DomainUnit = getDomainUnit();
    public [DomainActionEnum.HotUrlWhole]: DomainUnit = getDomainUnit();
    public [DomainActionEnum.HotUrlMiss]: DomainUnit = getDomainUnit();
    public [DomainActionEnum.HotUrlMissWhole]: DomainUnit = getDomainUnit();
    public [DomainActionEnum.HotReferer]: DomainUnit = getDomainUnit();
    public [DomainActionEnum.HotRefererWhole]: DomainUnit = getDomainUnit();
    public [DomainActionEnum.DomainRank]: DomainUnit = getDomainUnit();
    public [DomainActionEnum.DomainRankWhole]: DomainUnit = getDomainUnit();
    public [DomainActionEnum.TopIp]: DomainUnit = getDomainUnit();
    public [DomainActionEnum.TopIpWhole]: DomainUnit = getDomainUnit();
    public [DomainActionEnum.User]: DomainUnit = getDomainUnit();
    public [DomainActionEnum.UserWhole]: DomainUnit = getDomainUnit();

    public [DomainActionEnum.Report]: DomainUnit = getDomainUnit();
}
