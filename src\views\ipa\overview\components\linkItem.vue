<template>
    <div class="link-item">
        <div class="idx-bg">
            <span class="idx">{{ idx }}</span>
        </div>
        <div>
            <div class="title" @click="openLink">{{ linkData.title }}</div>
        </div>
    </div>
</template>

<script lang="ts">
import { Component, Prop, Watch, Vue } from "vue-property-decorator";

type LinkItemType = {
    url: string;
    title: string;
    update_time: string;
};

@Component({
    name: "overview-link-item",
})
export default class LinkItem extends Vue {
    @Prop({ type: Object }) private linkData!: LinkItemType;
    @Prop({ type: Number, default: 0 }) private idx!: number;
    $docHelp: any

    private openLink() {
        // window.open(this.linkData.url);
        this.$docHelp(this.linkData.url)
    }
}
</script>

<style lang="scss" scoped>
.link-item {
    display: grid;
    grid-template-columns: 1fr 10fr;
    gap: 12px;
    margin-bottom: 12px;
    align-items: center;
    .idx-bg {
        height: 24px;
        width: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f7f8fa;
        border-radius: 50%;
        .idx {
            font-size: 14px;
            color: #333333;
            line-height: 18px;
            font-weight: 400;
        }
    }
    .title {
        cursor: pointer;
        font-size: 12px;
        color: #333333;
        letter-spacing: 0;
        line-height: 18px;
        font-weight: 400;
    }
    .title:hover {
        color: $color-master
    }
    .time {
        font-size: 12px;
        color: #7c818c;
        line-height: 18px;
        font-weight: 400;
    }
}
</style>
