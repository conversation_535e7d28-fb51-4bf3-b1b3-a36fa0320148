/* eslint-disable @typescript-eslint/no-var-requires */
const OptimizeCSSAssetsPlugin = require("optimize-css-assets-webpack-plugin");
const CustomUploadPlugin = require("statistic-loader/customUploadPlugin");

// TODO 该字段是后端服务路径的起始路径，按需修改成新控制台的代码
const product = "aocdn";
// 是否为 bs 控制台
const isBs = process.env.PLATFORM === "bs";
const localEnv = process.env.LOCAL_ENV || "";

const fs = require("fs");
const path = require("path");
const assetsDir = "./static";
const isProduction = process.env.NODE_ENV === "production";
const plugins = isProduction ? [] : [CustomUploadPlugin];

// 性能分析
const analysisPlugin = require("./scripts/analysis.js");
const resolve = _path => path.resolve(__dirname, _path);

// 环境地址映射
const SVR_URL_MAP = {
    dev: "https://www-test-dev.ctcdn.cn/",
    "vip-test": "https://www-test.ctcdn.cn/",
    "vip-pre": "https://console-pre.ctcdn.cn/",
    "ctyun-test": "https://cdn-test.ctyun.cn/",
    "ctyun-pre": "https://cdn-pre.ctyun.cn/",
    "intl-test": "https://cdn-test.esurfingcloud.com/",
    "intl-pre": "https://cdn-pre.esurfingcloud.com/",
    "bs-test": "https://bs-test.ctcdn.cn/",
    "bs-pre": "https://bs-pre.ctcdn.cn/",
};

// 后端服务地址
const SVR_URL = SVR_URL_MAP[localEnv] || SVR_URL_MAP["dev"];
const CURRENT_HOST =
    {
        dev: "local.ctcdn.cn",
        vip: "local.ctcdn.cn",
        ctyun: "local.ctyun.cn",
        intl: "local.esurfingcloud.com",
        bs: "local.ctcdn.cn",
    }[localEnv.split("-")[0]] || "local.ctcdn.cn";

console.log("CURRENT_HOST = ", CURRENT_HOST);
console.log("CURRENT ENV = ", localEnv);
console.log("SVR_URL = ", SVR_URL);

const proxyReq = (proxyReq, req) => {
    const { sockets } = proxyReq.agent;
    const keys = Object.keys(sockets);
    // eslint-disable-next-line no-console
    console.log(`当前请求代理到：${keys[0]}, ${sockets[keys[0]][0]._httpMessage.path}`);
};

const proxy = {
    "/iam": {
        // iam 相关服务，菜单、工作区列表等， /iam/gw
        target: SVR_URL,
    },
    "/gw": {
        // ctyun 相关服务
        target: SVR_URL,
    },
    "/sign": {
        // 登录 iam 登出还是用的 /sign/out
        target: SVR_URL,
    },
    "/aocdn": {
        // 业务，调整为区分不同 cdn 系统的标识后，该规则囊括了业务、登录、redirect
        target: SVR_URL,
        onProxyReq: proxyReq,
    },
    "/ipa": {
        target: SVR_URL,
    },
    "/ctaccessone": {
        target: SVR_URL,
        onProxyReq: proxyReq,
    },
    // 埋点资源代理，避免测试环境访问cta报错
    "/cta": {
        target: "http://www.ctyun.cn/",
    },
    fcdn: {
        target: SVR_URL,
        onProxyReq: proxyReq,
    },
    "/boson": {
        target: SVR_URL,
        changeOrigin: true,
        onProxyReq: proxyReq,
    },
};

//         delete require.cache[require.resolve(path)];
//         return require(path);
//     }
// };

// 目标 package 名称
module.exports = {
    devServer: {
        host: CURRENT_HOST,
        sockHost: CURRENT_HOST,
        port: 9991,
        proxy,
        https: true,
        // 本地证书生成方法见 /docs/环境配置.md
        key: !isProduction && fs.readFileSync("./local.ctcdn.cn-key.pem"),
        cert: !isProduction && fs.readFileSync("./local.ctcdn.cn.pem"),
        // key: !isProduction && fs.readFileSync("./local.ctyun.cn-key.pem"),
        // cert: !isProduction && fs.readFileSync("./local.ctyun.cn.pem"),
        disableHostCheck: true,
    },
    // publicPath: "/h5/aocdn",
    publicPath: `./`, // 部署应用包时的基本 URL
    assetsDir, // 相对于 outputDir 的目录，放置静态资源
    productionSourceMap: false,
    parallel: false,
    // 简单配置方式，最终会被 webpack-merge 合并
    configureWebpack: () => {
        const result = {
            resolve: {
                alias: {
                    "@": path.resolve(__dirname, "src"),
                },
            },
            module: {
                rules: [
                    {
                        test: /\.(xlsx|xls)$/i,
                        use: [
                            {
                                loader: "file-loader",
                                options: {
                                    name: "[name].[ext]",
                                    outputPath: "static/files",
                                    publicPath: isProduction ? "/h5/aocdn/static/files" : "/static/files",
                                    esModule: false,
                                },
                            },
                        ],
                    },
                ]
            }
        };
        if (isProduction) {
            result.output = {
                filename: path.posix.join(assetsDir, "js/[name].[chunkhash].js"),
                chunkFilename: path.posix.join(assetsDir, "js/[id].[chunkhash].js"),
            };
            result.optimization = {
                minimizer: [
                    // 压缩css
                    new OptimizeCSSAssetsPlugin({
                        assetNameRegExp: /\.css/g,
                    }),
                ],
                splitChunks: {
                    chunks: "all",
                    // 初始化并发数
                    maxInitialRequests: 10,
                    minSize: 30000, // 最小 Chunk 大小为 30KB
                    minChunks: 1, // 最小引用次数
                    cacheGroups: {
                        default: false,
                        vendors: {
                            name: "chunk-libs",
                            test: /[\\/]node_modules[\\/]/,
                            priority: 10,
                            chunks: "initial",
                        },
                        "vue-vendor": {
                            name: "chunk-vue",
                            priority: 20,
                            test: /[\\/]node_modules[\\/](vue|vue-router|vuex)[\\/]/,
                        },
                        "element-ui": {
                            name: "chunk-elementUI",
                            priority: 20,
                            test: /[\\/]node_modules[\\/](element-ui)[\\/]/,
                        },
                        "@cutedesign/ui": {
                            name: "chunk-cutedesign",
                            priority: 20,
                            test: /[\\/]node_modules[\\/]@cutedesign[\\/]/,
                        },
                        "monaco-editor": {
                            name: "chunk-monaco-editor",
                            priority: 20,
                            test: /[\\/]node_modules[\\/](monaco-editor)[\\/](esm)[\\/](vs)[\\/](editor)[\\/]/,
                        },
                        "monaco-language": {
                            name: "chunk-monaco-language",
                            priority: 20,
                            test: /[\\/]node_modules[\\/](monaco-editor)[\\/](esm)[\\/](vs)[\\/](language)[\\/]/,
                        },
                        "monaco": {
                            name: "chunk-monaco",
                            priority: 20,
                            test: /[\\/]node_modules[\\/](monaco-editor)[\\/](esm)[\\/](?!vs[\\/](editor|language)[\\/])/,
                        },
                        "echarts": {
                            name: "chunk-echarts",
                            priority: 20,
                            test: /[\\/]node_modules[\\/](echarts)[\\/]/,
                        }
                    },
                },
            };
        }
        result.devtool = isProduction ? false : "eval-source-map"; // 调试有时文件定位错误，所以开发环境强制输出sourceMap

        const microAppOutPut = {
            library: product, //库名，与主应用注册的微应用name保持一致
            libraryTarget: "umd", //UMD，一种javascript打包模式，让你打包好的代码模块能被主应用访问
            jsonpFunction: `webpackJsonp_${product}`,
        };

        result.output = Object.assign({}, result.output, microAppOutPut);
        result.plugins = plugins;

        return analysisPlugin.smp ? analysisPlugin.smp.wrap(result) : result;
    },
    // 链式配置方式，粒度更细
    // 文档：https://github.com/neutrinojs/webpack-chain
    chainWebpack: config => {
        analysisPlugin.BundleAnalyzerPlugin &&
            config.plugin("BundleAnalyzerPlugin").use(analysisPlugin.BundleAnalyzerPlugin);
        // 生产环境特别配置
        if (isProduction) {
            config.plugins.delete("preload");
            config.plugins.delete("prefetch");

            config
                .plugin("extract-css")
                .tap(args => {
                    args[0] = {
                        filename: path.posix.join(assetsDir, "css/[name].[contenthash:8].css"),
                        chunkFilename: path.posix.join(assetsDir, "css/[name].[contenthash:8].css"),
                    };
                    return args;
                })
                .end();
        }

        // 由于 dart-sass 编译时，会出现 icon 乱码的情况，引入自定义 loader 做一次转换
        config.module
            .rule("scss")
            .oneOf("normal")
            .use("unicode-loader")
            .loader(require.resolve("./babel/babel-loader-unicode"))
            .before("sass-loader")
            .end();

        // 根据入口改变 entry
        config
            .entry("app")
            .clear()
            .add(isBs ? "./src/main.bs.ts" : `./src/main.ts`);

        // 根据入口改变 template
        config.plugin("html").tap(args => {
            // TODO 按需配置模板信息
            Object.assign(args[0], {
                title: "CDN 控制台",
                template: `./public/index.html`,
                favicon: `./public/favicon.ico`,
                // TODO layout 静态资源在不同类型控制台是不同的，需要后端处理该路径
                // 说明：bs 控制台不需要加载 layout
                // layoutCSS: isBs ? "" : `/fcdn/ctyun/layoutStyle`, // fcdn才有layout
                // layoutJs: isBs ? "" : `/fcdn/ctyun/layoutScript`, // fcdn才有layout
            });
            return args;
        });

        // 声明全局通用变量，可在代码中访问
        config.plugin("define").tap(args => {
            Object.assign(args[0]["process.env"], {
                // TODO 该环境变量在代码中有使用，主要用于请求路径
                PKG_NAME: JSON.stringify(product),
                // TODO 菜单栏上面的控制台名称，默认使用 XXX 控制台，也可以用下面的参数自定义
                // PKG_TITLE: JSON.stringify("xxx"),
                // 如果是 bs 控制台，需要传递这个变量给 @cdnplus/common
                PLATFORM: JSON.stringify(isBs ? "bs" : ""),
                // NEW_API_PREFIX: JSON.stringify("ncdn"),
            });
            return args;
        });

        // 设置 svg-sprite-loader
        config.module
            .rule("svg")
            .exclude.add(resolve("src/icons"))
            .end();

        config.module
            .rule("icons")
            .test(/\.svg$/)
            .include.add(resolve("src/icons"))
            .end()
            .use("svg-sprite-loader")
            .loader("svg-sprite-loader")
            .options({
                symbolId: "icon-[name]",
            })
            .end();

        const publicPath = isProduction ? "/h5/aocdn/" : "./";

        const svgRule = config.module.rule("svg");
        svgRule.uses.clear();
        config.module
            .rule("svg")
            .test(/\.(svg)(\?.*)?$/)
            .exclude.add(path.resolve(__dirname, "src/icons"))
            .end()
            .use("file-loader")
            .loader("file-loader")
            .options({
                name: "static/img/[name].[hash:8].[ext]",
                publicPath,
            })
            .end();

        config.module
            .rule("vue")
            .use("vue-loader")
            .loader("vue-loader")
            .tap(options => {
                options.compilerOptions.directives = {
                    html(node, directiveMeta) {
                        (node.props || (node.props = [])).push({
                            name: "innerHTML",
                            value: `xss(_s(${directiveMeta.value}))`,
                        });
                    },
                };
                return options;
            });

        config.module.rule('fonts').use('url-loader').loader('url-loader').options({}).end();

        if (!isProduction) {
            config.module
                .rule("statistic")
                .include.add(resolve("src"))
                .end()
                .exclude.add(/src\/assets/)
                .end()
                .use("statistic-loader")
                .loader("statistic-loader")
                .end();
        }
    },
    css: {
        extract: isProduction,
        sourceMap: false,
        // https://cli.vuejs.org/zh/guide/css.html#向预处理器-loader-传递选项
        loaderOptions: {
            // 虽然 scss 也是 sass-loader 处理，但是因为语法差异，scss 要单独配置
            scss: {
                // prependData 配置后可以省略在每个文件都单独引用一次
                prependData: `
                @import "@/assets/css/_index.scss";
                @import "@/assets/css/variables.scss";
                @import "node_modules/@cutedesign/ui/style/themes/default/variables.scss";
                `,
            },
            postcss: {
                plugins: [
                    require("postcss-plugin-namespace")(".aocdn-micro-app", {
                        ignore: [
                            "html",
                            /body/,
                            "div",
                            /el-dialog/,
                            /el-tooltip__popper/,
                            /dialog/,
                            /aocdn-error-handler-wrapper/,
                            /aocdn-compare-wrapper/,
                            /aocdn-data-compare-wrapper/,
                            /aocdn-ct-breadcrumb/,
                            /label-select__dropdown/,
                            /label-select__dropdown-wrapper/,
                            /el-autocomplete-aocdn-width-adjust/,
                            /ecs-validation-error-modal/,
                            /:root/,
                            ":export",
                            /aocdn-ignore/,
                            /svg-icon/,
                            /el-popover/,
                            /el-drawer/,
                            /el-dropdown/
                        ],
                    }),
                ],
            },
        },
    },
    transpileDependencies: ["resize-detector", "alogic-base-web", "@cdnplus/common"],
};
