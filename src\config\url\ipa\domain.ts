/*
 * @Description: 域名相关
 */

import { IPA_PREFIX } from "../_PREFIX";

export const DomainUrl = {
    domainDetail: IPA_PREFIX + "/v1/domain/query",
    // 域名下拉框改为从工单返回的列表拿
    // domainList: IPA_PREFIX + "/v1/domain/query_list",
    domainListPage: IPA_PREFIX + "/v1/domain/query_list",
    domainList: IPA_PREFIX + "/v1/domain/name/list",
    addDomain: IPA_PREFIX + "/v1/domain",
    domainOperate: IPA_PREFIX + "/v1/domain/del_stop_start",
    updataDomain: IPA_PREFIX + "/v1/domain/update",
    domainListNew: IPA_PREFIX + "/v1/domain/name/list/scc",
    domainPortCheck: IPA_PREFIX + "/v1/domain/port_check",
    domainRepeatVerify: IPA_PREFIX + "/v1/domain/repeat_verify",
    domainZoneVerify: IPA_PREFIX + "/v1/domain/zone_verify",
    // 开启、关闭域名DDos防护开关
    domainDdos: IPA_PREFIX + "/v1/domain/ddos",
    // 域名列表-已启用/配置中状态数量统计
    queryListCount: IPA_PREFIX + "/v1/domain/query_list_count",
    // 实例名更新
    updataInstName: IPA_PREFIX + "/v1/domain/inst/update",
    // ipa 配置
    ipaConfig: IPA_PREFIX + "/v1/basic/config",
    // 区域访问控制
    regionList: IPA_PREFIX + "/v1/region/country",
    regionISP: IPA_PREFIX + "/v1/region/isp",
    regionProvince: IPA_PREFIX + "/v1/region/province",
    // 变更加速区域
    updateArea: IPA_PREFIX + "/v1/domain/update_area",
};
