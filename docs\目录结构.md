```sh
cdn-front
└───babel/** # babel相关配置 & 错误处理兜底
│
└───config
│    nginx.conf # docker构建镜像用的nginx配置
│
└───deploy/** # k8s配置文件
│
└───patch
│    alogic-base-web+2.0.8 # common包的补丁
│ 
└───public
│ 
└───scripts
│    │    analysis.js # 性能分析脚本
│
└───src
│    └────api # 主应用相关接口
│       └────assets # 静态资源 & 样式文件
│       └────components/** # 全局组件
│       └────config
│           └────echarts # echart 主题配置文件
│           └────map # 字典映射
│           └────pattern # 正则校验规则
│           └────sortConfig # 配置页面菜单顺序
│           └────url # 接口
│               └────ipa/ # 边缘接入相关接口
│               └────ncdn/ # fcdn 独有接口
│               │    _PREFIX # 接口前缀
│               │    其他文件 # aocdn & fcdn 相关接口directives
│       └────directives # vue指令
│       └────filters # 过滤器
│       └────i18n # 国际化语言文件
│       └────icons # svg 图标
│       └────microApp # qiankun 子应用
│       └────mixins # 公用mixin
│       └────router # vue-router 封装
│       └────store # vue-router 封装
│           └────config # 字典映射
│           └────modules # vuex模块
│                └───ipa # ipa专用
│                └───ncdn # fcdn专用
│                └───** # 共用
│           └────types # 类型定义
└───types # views/下各个模块的类型定义
└───utils # 公共方法
└───views
│    └────certificate 证书
│    └────domainConfig
│           └────accelerationConfig # aocdn -> CDN加速配置, fcdn -> 配置
│           └────basicConfig # aocdn -> 基础配置
│           └────components # 公用组件
│                └───lockTip.vue # 功能锁提示组件
│           └────mixins # 加速配置和基础配置的mixin，用于导出formData
│    └────errorPage # 404
│    └────forbidden # 禁止访问
│    └────index # index
│    └────interceptor # 工作区选择
│    └────ipa # 边缘接入相关页面
│       └────domain # 域名列表 & 配置
│       └────overview # 概览
│       └────statistics # 统计分析
│    └────ipBelong # ip归属地
│    └────label # 标签管理
│    └────log # 日志
│    └────nbilling # fcdn计费详情
│    └────ncdn # ncdn独有
│       └────ndomain # 域名列表 & 配置 & 批量修改
│       └────nhome # 首页
│       └────norder # 工单（已弃用）
│       └────nreview # 内容审核
│       └────nscript # udf脚本
│    └────refresh # 刷新预取
│    └────statistics # 统计分析
│        │   aocdn.vue # aocdn 统计分析入口文件
│        │   fcdn.vue # fcdn 统计分析-用量分析 入口文件
│        │   dcdn.vue # fcdn 统计分析-全站加速 入口文件
```
