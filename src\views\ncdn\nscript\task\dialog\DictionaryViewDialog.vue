<template>
    <el-dialog
        :append-to-body="true"
        :close-on-click-modal="false"
        :visible.sync="detailVisible"
        :before-close="cancel"
        @open="getDictionary"
        class="detail-dialog"
    >
        <h3 class="dictionaryTip">
            <i class="el-icon-info"></i> <span>全局字典支持全局task脚本进行读写，业务脚本只读</span>
        </h3>
        <el-table :data="dictList" v-loading="detailLoading" center>
            <el-table-column prop="dict_name" label="字典名称"></el-table-column>
            <el-table-column prop="dict_size" label="字典大小">
                <template slot-scope="scope">
                    {{ `${scope.row.dict_size}${scope.row.dict_type}` }}
                </template>
            </el-table-column>
        </el-table>
        <span slot="footer" class="dialog-footer">
            <el-button @click="cancel">返 回</el-button>
        </span>
    </el-dialog>
</template>

<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";
import { ndictionary } from "@/config/url/ncdn/nscript";
import { nUserModule } from "@/store/modules/nuser";
import { CtiamCode } from "@/utils/ctFetch/errorConfig";

@Component({})
export default class DictionaryDialog extends Vue {
    // 弹框显示标志位
    @Prop({ default: false, type: Boolean }) private detailVisible!: boolean;

    private detailLoading = false;
    private dictList = [];
    private cancel() {
        this.$emit("cancel");
    }
    get userId() {
        return String(nUserModule.userInfo.userId);
    }

    async getDictionary() {
        this.detailLoading = true;
        try {
            const res = await this.$ctFetch<{ result: { dicts: [] } }>(ndictionary.getInfos, {
                method: "POST",
                body: { data: { $user: this.userId, workspaceId: String(this.$route.query.workspaceId) } },
                headers: {
                    "Content-Type": "application/json",
                },
            });
            this.dictList = res.result?.dicts;
        } catch (error) {
            const isCtiamError = CtiamCode.includes((error as any)?.data?.code);
            if (isCtiamError) {
                this.$errorHandler(error);
                this.cancel();
            }
        } finally {
            this.detailLoading = false;
        }
    }
}
</script>

<style scoped lang="scss">
.detail-dialog {
    ::v-deep .el-dialog__header {
        display: none;
    }
    ::v-deep .el-dialog__footer {
        background: #fff;
    }
    ::v-deep .el-table th.el-table__cell {
        background: #ebeef5;
    }
    .dictionaryTip {
        margin-bottom: 10px;
    }
}
</style>
