/* eslint-disable @typescript-eslint/no-empty-function */
import { RouteConfig } from "vue-router";
import { SecurityAbilityModule } from "@/store/modules/securityAbility";
import { StatisticsModule } from "@/store/modules/statistics";

const router: [RouteConfig] = [
    {
        path: "/securityAbility/aocdnMicroApp",
        name: "securityAbility",
        component: () => import("./index.vue"),
        children: [
            {
                path: "basicConfig",
                name: "securityAbility.basicConfig",
                component: () => import("./basicConfig/index.vue"),
                meta: {
                    breadcrumb: {
                        title: "基础配置",
                        route: ["securityAbility", "securityAbility", "securityAbility.basicConfig"],
                    },
                    perm: "securityAbility.basicConfig",
                },
                beforeEnter(to, from, next) {
                    // 通知主应用重置状态，避免同域名在加速配置和cdn配置互相跳转时导致的状态不正确
                    window.custom.emit("handleCDNConfigChange", {
                        icChange: false,
                        fn: () => {},
                    });
                    next();
                }
            },
            {
                path: "accelerationConfig",
                name: "securityAbility.accelerationConfig",
                component: () => import("./accelerationConfig/index.vue"),
                meta: {
                    breadcrumb: {
                        title: "CDN加速配置",
                        route: ["securityAbility", "securityAbility", "securityAbility.accelerationConfig"],
                    },
                    perm: "securityAbility.accelerationConfig",
                },
                beforeEnter(to, from, next) {
                    // 通知主应用重置状态，避免同域名在加速配置和cdn配置互相跳转时导致的状态不正确
                    window.custom.emit("handleCDNConfigChange", {
                        icChange: false,
                        fn: () => {},
                    });
                    next();
                }
            },
            // {
            //     path: "ipaDomainEdit",
            //     name: "securityAbility.ipaDomainEdit",
            //     component: () => import("@/views/ipa/domain/edit/index.vue"),
            //     meta: {
            //         breadcrumb: {
            //             title: "IPA域名配置",
            //             route: ["securityAbility", "securityAbility", "securityAbility.ipaDomainEdit"],
            //         },
            //         perm: "securityAbility.accelerationConfig",
            //     },
            // },
        ],
        meta: {
            isSkipMicroApp: true,
            breadcrumb: {
                title: "我是测试基础配置",
                route: ["home", "domainConfig.basicConfig"],
            },
        },
        async beforeEnter(to, from, next) {
            // fcdn 编辑页是通过组件嵌套的，不走路由，这两个函数在fcdn下不会触发
            await SecurityAbilityModule.GetIsBilling();
            await StatisticsModule.GetBasicConfig();
            next();
        },
    },
];
export default router;
