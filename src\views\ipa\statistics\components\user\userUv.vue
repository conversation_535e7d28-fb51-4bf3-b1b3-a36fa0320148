<template>
    <ct-box :tags="$t('statistics.user.tip11')" class="user-section">
        <template #tags-slot>
            <el-tooltip effect="dark" :content="$t('statistics.common.searchDownloadContent')" placement="right">
                <i class="el-icon-download download-icon download-icon-wrapper" @click="download"></i>
            </el-tooltip>
        </template>

        <div class="total">
            <div class="ip-data">
                {{ $t('statistics.user.tip12') }}{{ `(${greater24Hours ? $t('statistics.user.tip13') :
                    $t('statistics.user.tip14')})` }}：
                <span>{{ (fetchData.maxUv || 0).toLocaleString() }}{{ $t('statistics.user.tip17', { prefix: " " })
                    }}</span>
            </div>
            <div class="ip-data" v-if="!greater24Hours">
                {{ $t('statistics.user.tip15') }}
                <span>{{ (fetchData.totalUv || 0).toLocaleString() }}{{ $t('statistics.user.tip17', { prefix: " " })
                    }}</span>
            </div>
        </div>

        <v-chart class="chart" v-loading="loading" :element-loading-text="$t('common.chart.loading')" autoresize
            theme="cdn" :options="options" />
    </ct-box>
</template>

<script>
import { StatisticsUserUrl } from "@/config/url/ipa/statistics";
import { timeFormat } from "@/filters/index";
import { THEME_AREA_STYLE } from "@/config/echart/cdn-theme";
import chartShim from "../chartShim";
import { getLang } from "@/utils";

export default {
    mixins: [chartShim],
    props: {
        instType: {
            type: Number,
            default: 1
        },
        instName: {
            type: String,
            default: ""
        },
    },
    data() {
        return {
            emitOnMounted: false,
            loading: false,
            // 请求的列表数据
            fetchData: {
                hours: [],
                daily: [],
                // maxPv: 0,
                // totalPv: 0,
                maxUv: 0,
                totalUv: 0,
            },
            greater24Hours: false, // 请求参数是否超过24h
        };
    },

    computed: {
        // 获取缩进
        uvUnit() {
            // 通过首项来判断是否缩进万位
            const uvList = this.greater24Hours ? this.fetchData.daily : this.fetchData.hours;

            if (uvList[0] && uvList[0].count >= 10000) {
                return {
                    unit: "W",
                    scale: 10000,
                };
            } else {
                return {
                    unit: "",
                    scale: 1,
                };
            }
        },
        // 2、数据处理
        options() {
            const { greater24Hours } = this;
            const { daily, hours } = this.fetchData;
            const { unit, scale } = this.uvUnit;

            const xAxisData = [];
            const seriesData = [];

            // 根据区间选择数据源
            (greater24Hours ? daily : hours)
                .sort((a, b) => a.timestamp - b.timestamp)
                .forEach(item => {
                    xAxisData.push(timeFormat(item.timestamp * 1000));
                    seriesData.push(item.count / scale || 0);
                });
            const times = getLang() === "en" ? "" : "次";
            const options = {
                title: {
                    subtext: `${this.$t('statistics.user.tip12')}${this.greater24Hours ? this.$t('statistics.user.tip13') : this.$t('statistics.user.tip14')}：${parseInt(
                        this.fetchData.maxUv || 0
                    ).toLocaleString()}${times}`,
                    left: "center",
                    top: "0",
                },
                tooltip: {
                    trigger: "axis",
                    formatter: a => {
                        const date = new Date(a[0].name.replace(/-/g, "/"));
                        let timePeriod = "";
                        const count = parseInt(a[0].value * scale + "").toLocaleString();
                        if (this.greater24Hours) {
                            // date.setHours(23);
                            // date.setMinutes(59);
                            timePeriod = a[0].name.slice(0, 10);
                        } else {
                            date.setMinutes(59);
                            let hour = date.getHours();
                            hour = hour < 10 ? `0${hour}` : hour;
                            timePeriod = `${a[0].name.slice(10, 16)}-${hour}:${date.getMinutes()}`;
                        }
                        return `${timePeriod}<br>${a[0].marker}${count}${times}`;
                    },
                },
                xAxis: {
                    type: "category", // 类目轴，从 data 中获取数据
                    axisLabel: {
                        formatter(val) {
                            // 小于24小时，横坐标显示小时数目
                            return greater24Hours ? val.slice(5, 10) : val.slice(10, 16);
                        },
                    },
                    axisTick: {
                        show: false,
                    },
                    data: xAxisData,
                },
                yAxis: {
                    name: `${this.$t("statistics.dcdn.requestWhole.vchartOptions.yAxisName")}`,
                    type: "value",
                    minInterval: 1,
                    axisLabel: {
                        formatter: `{value} ${unit}`,
                    },
                    axisTick: {
                        show: false,
                    },
                },
                series: [
                    {
                        type: "line",
                        color: "#0c59db",
                        data: seriesData,
                        areaStyle: THEME_AREA_STYLE["#358AE2"],
                    },
                ],
                grid: {
                    // 容器各个方向的留白
                    left: scale === 1 ? "10%" : "18%",
                    right: "8%",
                    top: "20%",
                    bottom: "10%",
                },
            };

            return options;
        },
    },

    methods: {
        initData(reqParam) {
            this.getData(reqParam);
        },
        // 1、数据请求
        async getData(params) {
            this.loading = true;
            this.greater24Hours = (params.end_time - params.start_time) / 3600 > 24;

            const rst = await this.$ctFetch(StatisticsUserUrl.uvDataList, {
                method: "POST",
                transferType: "json",
                body: {
                    data: {
                        ...params,
                        // 全部域名，新增一个ignore字段
                        ignore: params?.domain?.length > 1 ? "domain" : "",
                    },
                },
            });

            Object.assign(this.fetchData, rst);
            // ***数据处理
            this.fetchData.maxUv = rst.result.peek_hour;
            this.fetchData.totalUv = rst.result.ip_count_day;
            this.fetchData.hours = rst.result.list;
            this.fetchData.daily = rst.result.list;
        },

        download() {
            const { greater24Hours } = this;
            const { daily, hours, maxUv, totalUv } = this.fetchData;
            if (!totalUv) {
                this.$message(`${this.$t("statistics.common.chart.errMsg[2]")}`);
                return;
            }

            const uvList = greater24Hours ? daily : hours;

            if (uvList?.length === 0) return this.$message(`${this.$t("statistics.common.chart.errMsg[2]")}`);

            let str = `${this.$t("statistics.user.tip16")},IP(${this.$t("statistics.user.tip17", { prefix: "" })})\n`;

            uvList.forEach(item => {
                str += `${timeFormat(item["timestamp"] * 1000)},`;
                str += item.count + "\n";
            });

            str += `${this.$t("statistics.user.tip18")}(${greater24Hours ? this.$t("statistics.user.tip13") : this.$t("statistics.user.tip14")
                }),${maxUv}` + "\n";

            if (!greater24Hours) str += `${this.$t("statistics.user.tip19")}, ${totalUv} ` + "\n";
            const instType = this.instType

            this.downloadExcelForUser({
                name: `${this.$t("statistics.user.tip20")}`,
                str,
                instType,
            });
        },
    },
};
</script>

<style lang="scss" scoped>
.chart {
    width: 100%;
    height: 280px;
}

.total .ip-data {
    display: inline-block;

    >span {
        color: #666666;
    }

    &+.ip-data {
        margin-left: 16px;
    }
}

.user-section .download-icon.download-icon-wrapper {
    padding-left: 0;
}
</style>
