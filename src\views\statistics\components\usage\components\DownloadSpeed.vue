<template>
    <div class="chart-wrap">
        <div class="title-wrap">
            <template>
                <div class="total">
                    <div class="tip">
                        {{ $t("statistics.common.vchartTip4", { type: "", num: "" }) }}{{ titleTipSuffix }}：
                        <span class="num">
                            {{ convertBandwidthB2PForChart(fetchData.maxSpeed) }}
                        </span>
                        <span class="date">{{ fetchData.maxSpeedTimestamp | timeFormat }}</span>
                    </div>
                </div>
                <template v-if="useCompare">
                    <div class="total">
                        <div class="tip">
                            {{ $t("statistics.common.vchartTip4", { type: "", num: "2" }) }}：
                            <span class="num">
                                {{ convertBandwidthB2PForChart(fetchData2.maxSpeed) }}
                            </span>
                            <span class="date">{{ fetchData2.maxSpeedTimestamp | timeFormat }}</span>
                        </div>
                    </div>
                </template>
            </template>
        </div>

        <v-chart class="chart" v-loading="loading" :element-loading-text="$t('statistics.common.chart.loading')"
            autoresize theme="cdn" :options="options" @legendselectchanged="legendselectchanged" />
    </div>
</template>

<script lang="ts">
import { Component } from "vue-property-decorator";
import { mixins } from "vue-class-component";
import { StatisticsUsageUrl } from "@/config/url/statistics";
import { downloadCsv } from "@/utils";
import { timeFormat } from "@/filters/index";
import { SearchParams } from "@/types/statistics/usage";
import { QueryFetchSpeedData } from "@/types/statistics/usage";
import { THEME_AREA_STYLE } from "@/config/echart/cdn-theme";
import ChartMixin from "../chartMixin";
import { convertBandwidthB2P } from "@/utils";
import { nUserModule } from "@/store/modules/nuser";
import { cloneDeep } from "lodash-es";

type tooltipParam = { name: string; marker: string; value: string | number };

const defaultFetchData: QueryFetchSpeedData = {
    "5min": [],
    daily: [],
    maxSpeed: 0,
    maxSpeedTimestamp: 0,
};

@Component({
    name: "DownloadSpeed",
})
export default class DownloadSpeed extends mixins(ChartMixin) {
    chartType = this.$t("statistics.usageQuery.downloadSpeed.chartType");
    useCompare = false; // 是否使用了数据对比
    // 缓存请求参数
    private searchParams2?: SearchParams;
    // 接口数据（总1）
    fetchData: QueryFetchSpeedData = {
        ...defaultFetchData,
    };
    // 接口数据（总2，对比用）
    fetchData2: QueryFetchSpeedData = {
        ...defaultFetchData,
    };
    protected downloadDataList: QueryFetchSpeedData["5min"] = []; // 用于下载的 总数据
    private timeMinus?: number; // 时间差
    private legend1Selected = true; // 是否选择了图例1

    private localFetchGenerator<T>(params: SearchParams) {
        return this.fetchGenerator<T>(StatisticsUsageUrl.downloadSpeedList, {
            ...params,
        });
    }

    convertBandwidthB2PForChart(val = 0): any {
        const rst = convertBandwidthB2P(val, this.scale);
        return `${rst.num}${rst.unit.replace("bps", "B/s")}`;
    }

    // 当前展示是否为下载速度
    get showSpeed() {
        return true;
    }

    // 获取 seriesData 所使用的 key
    get seriesDataKey() {
        return this.showSpeed ? "speed" : "queryflow";
    }

    // 标题1的后缀（存在数据对比时需要）
    get titleTipSuffix() {
        return this.useCompare ? "1" : "";
    }

    // 根据最大值获取图表的单位规则 { 单位，缩进 }
    get chartUnitConfig() {
        // 单数据 or 多数据
        let max = 0;
        if (this.useCompare) {
            const max1 = this.getMaxFromList(this.fetchData["5min"], this.seriesDataKey);
            const max2 = this.getMaxFromList(this.fetchData2["5min"], this.seriesDataKey);

            max = Math.max(max1, max2);
        } else {
            max = this.getMaxFromList(this.fetchData["5min"], this.seriesDataKey);
        }

        return this.getSpeedUnitConfig(max, true);
    }

    protected dailyType(index: number) {
        return [`${this.$t("statistics.usageQuery.downloadSpeed.dailyType")}`, "http(s)", "websocket"][index];
    }

    // 1、数据请求
    protected async getData(params1: SearchParams, params2: SearchParams) {
        // 有参数2，说明开启了对齐
        this.useCompare = !!params2;

        // 缓存参数
        this.searchParams2 = params2;

        // 合并两组信息并去重
        function _dailyMerge(fetchData: QueryFetchSpeedData, fetchData2: QueryFetchSpeedData) {
            fetchData.daily = fetchData.daily
                ?.concat(fetchData2.daily)
                .sort((a, b) => Number(new Date(a.date)) - Number(new Date(b.date)));
            for (let i = 1; i < fetchData?.daily?.length; i++) {
                if (fetchData?.daily[i].date === fetchData?.daily[i - 1].date) {
                    fetchData?.daily?.splice(i, 1);
                }
            }
        }

        if (!this.useCompare) {
            //请求数据，趋势图和天粒度
            [this.fetchData] = await Promise.all([this.localFetchGenerator<QueryFetchSpeedData>(params1)]);
            if (!this.fetchData) this.fetchData = cloneDeep(defaultFetchData);
        } else {
            // 记录时间差，防止清空时间后tooltip报错
            this.timeMinus = (params1.startTime - params2.startTime) * 1000;
            [this.fetchData, this.fetchData2] = await Promise.all([
                this.localFetchGenerator<QueryFetchSpeedData>(params1),
                this.localFetchGenerator<QueryFetchSpeedData>(params2),
            ]);

            if (!this.fetchData) this.fetchData = cloneDeep(defaultFetchData);
            if (!this.fetchData2) this.fetchData2 = cloneDeep(defaultFetchData);

            // 合并两组信息并去重
            _dailyMerge(this.fetchData, this.fetchData2);
        }

        // 处理用于下载的数据
        this.downloadDataList = !this.useCompare
            ? this.fetchData["5min"]
            : this.fetchData2["5min"].concat(this.fetchData["5min"]);
    }

    // 2、数据处理
    get options() {
        const { seriesDataKey } = this;
        // 根据 switch 获取差异化数据
        const title = this.$t("statistics.usageQuery.downloadSpeed.chartOptions.title1");
        const { unit: _unit, scale } = this.chartUnitConfig;
        const unit = _unit.replace("bps", "B/s");
        const yAxisName = `${this.$t("statistics.usageQuery.downloadSpeed.chartOptions.yAxisName", {
            unit,
        })}`;

        const xAxisData: string[] = [];
        const seriesData: string[] = [];
        const fetchDataList = this.fetchData["5min"] || [];

        // 填充 chart 曲线数据
        fetchDataList
            .sort((a, b) => a.timestamp - b.timestamp)
            .forEach(item => {
                xAxisData.push(timeFormat(item.timestamp * 1000).replace(" ", "\n"));
                seriesData.push(((item[seriesDataKey] || 0) / scale).toFixed(2));
            });

        const space = nUserModule.lang === "en" ? " " : "";

        const options = {
            ...this.comOptions,
            tooltip: {
                trigger: "axis",
                formatter: (a: tooltipParam[]) =>
                    `${a[0].name}<br>${a[0].marker}${space}${title}: ${a[0].value}${unit}`,
            },
            xAxis: {
                type: "category", // 类目轴，从 data 中获取数据
                boundaryGap: false, // 坐标轴两边留白策略
                axisLabel: {
                    formatter: this.xAxisFormatterGenerator(),
                },
                data: xAxisData,
            },
            yAxis: {
                type: "value", // 数值轴
                name: yAxisName,
            },
            series: [
                {
                    type: "line",
                    data: seriesData,
                    areaStyle: THEME_AREA_STYLE["#358AE2"],
                },
            ],
        };

        // 没有数据对比，则直接返回配置
        if (!this.useCompare) return options;

        // 使用数据对比，则继续组装配置
        // const xAxisData2: string[] = [];
        const seriesData2: string[] = [];
        const fetchDataList2 = this.fetchData2["5min"] || [];
        fetchDataList2
            .sort((a, b) => a.timestamp - b.timestamp)
            .forEach(item => {
                // xAxisData2.push(timeFormat(item.timestamp * 1000).replace(" ", "\n"));
                seriesData2.push(((item[seriesDataKey] || 0) / scale).toFixed(2));
            });
        // 调整 series
        options.series = [
            {
                name: `${title}1`,
                type: "line",
                data: seriesData,
                areaStyle: THEME_AREA_STYLE["#358AE2"],
            },
            {
                name: `${title}2`,
                type: "line",
                data: seriesData2,
                areaStyle: THEME_AREA_STYLE["#FA8334"],
            },
        ];
        // 增加图例
        options.legend = {
            data: [`${title}1`, `${title}2`],
        };
        // 调整 tooltip ，动态控制 tooltip ，如果图例只选择一条时就只显示一组信息
        options.tooltip.formatter = (a: tooltipParam[]) => {
            // a[0].name带有换行符号，在firefox下new Date时会异常
            const name2 = timeFormat(
                Number(new Date(Number(new Date(a[0].name.replace("\n", " "))) - this.timeMinus!))
            );
            const name = this.legend1Selected ? a[0].name : name2;
            let tip = `${name}<br>${a[0].marker}${space}${title}: ${a[0].value}${unit}`;
            if (a[1]) {
                tip += `<br>${name2}<br>${a[1].marker}${space}${title}: ${a[1].value}${unit}`;
            }
            return tip;
        };
        return options;
    }

    // 监听图例选择变化
    legendselectchanged({ selected }: { selected: { [legend: string]: boolean } }) {
        const title = this.$t("statistics.usageQuery.downloadSpeed.chartOptions.title1");
        this.legend1Selected = selected[`${title}1`];
    }

    // 重写 excel 数据拼接方法
    protected tableToExcel() {
        const { unit, scale } = this.chartUnitConfig;
        // 表头
        let str = `${this.$t("statistics.usageQuery.downloadSpeed.tableToExcel.excelColumn1")},${`${this.$t(
            "statistics.usageQuery.downloadSpeed.tableToExcel.excelColumn2",
            {
                unit: unit.replace("bps", "B/s"),
            }
        )}`}`;

        str += "\n";

        // 输出格式
        str += this.downloadDataList.reduce((str, item) => {
            str += `${timeFormat(item["timestamp"] * 1000)},`;
            str += (item[this.seriesDataKey] / scale).toFixed(2) + "\n";
            return str;
        }, "");

        // 增加峰值下载速度
        str += `\n${this.$t("statistics.usageQuery.downloadSpeed.tableToExcel.excelColumn6")}\n`;

        const { titleTipSuffix, fetchData, fetchData2 } = this;

        // if (this.showSpeed) {
        const totalBpsUnit = this.bps.replace("bps", "B/s");
        const { maxSpeed } = fetchData;
        str += `${this.$t("statistics.usageQuery.downloadSpeed.tableToExcel.excelColumn7", {
            type: "",
            num: "",
            suffix: titleTipSuffix,
        })} ,${maxSpeed} ${totalBpsUnit} \n`;

        if (this.useCompare) {
            str += `${this.$t("statistics.usageQuery.downloadSpeed.tableToExcel.excelColumn7", {
                type: "",
                num: "2",
                suffix: "",
            })},${fetchData2.maxSpeed} ${totalBpsUnit} \n`;
        }
        // }

        this.downloadExcel({
            name: `${this.$t("statistics.usageQuery.downloadSpeed.tableToExcel.excelName", {
                chartType: this.chartType,
            })}`,
            str,
        });
    }
    // 重写下载方法
    protected downloadExcel({ name, str }: { name: string; str: string }) {
        // 此时要用缓存的参数2判断是否启用了数据对比，以保证参数和数据一致
        const useCompare = !!this.searchParams2;

        // 存在对比的时候，是从2个时间范围内选择时间点
        let { startTime, endTime } = this.searchParams;
        if (useCompare) {
            startTime = startTime < this.searchParams2!.startTime ? startTime : this.searchParams2!.startTime;

            endTime = endTime > this.searchParams2!.endTime ? endTime : this.searchParams2!.endTime;
        }

        const t1 = timeFormat(+startTime)
            .replace(/-|\s|:/g, "")
            .slice(0, 12);
        const t2 = timeFormat(+endTime)
            .replace(/-|\s|:/g, "")
            .slice(0, 12);

        downloadCsv(`${name}${t1}-${t2}`, str);
    }
}
</script>

<style lang="scss" scoped></style>
