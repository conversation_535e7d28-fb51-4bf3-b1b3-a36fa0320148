<template>
    <el-empty v-bind="$attrs">
        <slot></slot>
        <template slot="image">
            <slot name="image">
                <img :src="noDataSrc" :width="imageSize" :height="imageSize" />
            </slot>
        </template>
        <template slot="description">
            <slot name="description"></slot>
        </template>
    </el-empty>
</template>

<script>
import noDataImage from "./no-data.svg";

export default {
    name: "index",
    props: {
        imageSize: {
            type: Number,
            default: 200,
        },
    },
    data() {
        return {
            noDataSrc: noDataImage,
        };
    },
};
</script>

<style scoped></style>
