<template>
    <div class="label-select el-select" v-clickoutside="handleClose">
        <div class="label-select__tags">
            <el-tag v-if="hasCheckedLabel" size="mini" type="info">
                {{
                    valueCheckedArr.length === valuesTotal
                        ? `${$t("common.labelSelect.labelSlectTagsText1")}`
                        : `${$t("common.labelSelect.labelSlectTagsText2", {
                              num: valueCheckedArr.length,
                          })}`
                }}
            </el-tag>
        </div>
        <div
            class="label-select__input el-input"
            @click.stop.prevent="toggleDropdown"
            ref="reference"
            :class="{ 'is-focus': visible }"
        >
            <input
                ref="input"
                type="text"
                class="el-input__inner"
                :placeholder="hasCheckedLabel ? '' : placeholder"
                readonly="readonly"
                @focus="handleFocus"
                @keydown.esc.stop.prevent="visible = false"
                @keydown.tab="visible = false"
            />
            <i :class="['el-icon-arrow-up', { 'is-reverse': visible }]"></i>
        </div>
        <transition name="el-zoom-in-top">
            <div v-show="visible" class="label-select__dropdown el-popper" ref="popper">
                <div class="label-select__dropdown-wrapper">
                    <template v-if="labelList.length === 0">
                        <p class="el-select-dropdown__empty">
                            {{ $t("common.labelSelect.selectDropdownEmpty") }}
                        </p>
                    </template>
                    <template v-else>
                        <!-- 第一列 key  -->
                        <div class="dropdown-wrap" style="margin-right: 10px">
                            <!-- keys 全选框操作 -->
                            <el-checkbox
                                :indeterminate="keysIndeterminate"
                                v-model="keysAllChecked"
                                @change="handleCheckAllKeys"
                            >
                                {{ $t("common.labelSelect.checkboxText1") }}
                            </el-checkbox>
                            <!-- 左侧分组 key ，只选择不展开 -->
                            <div class="dropdown-list">
                                <el-checkbox
                                    v-for="{ label, value } in labelList"
                                    :label="value"
                                    :key="value"
                                    :indeterminate="valuesIndeterminate[value]"
                                    v-model="valuesAllChecked[value]"
                                    @change="handleCheckKey(value, $event)"
                                    :title="label"
                                >
                                    <!-- 点击内容区，只展开不选择 -->
                                    <div
                                        class="dropdown-item-label"
                                        @click.prevent="handleClickKeyContent(value)"
                                    >
                                        {{ label }}
                                    </div>
                                </el-checkbox>
                            </div>
                        </div>
                        <!-- 第二列 key：value -->
                        <div class="dropdown-wrap">
                            <!-- value 全选框操作 -->
                            <el-checkbox
                                :indeterminate="valuesIndeterminate[currentKey]"
                                v-model="valuesAllChecked[currentKey]"
                                @change="handleCheckCurrentValues(currentKey, $event)"
                            >
                                {{ $t("common.labelSelect.checkboxText2") }}
                            </el-checkbox>

                            <!-- value 单选 -->
                            <div class="dropdown-list">
                                <el-checkbox
                                    v-for="{ label, value } in valuesOptions[currentKey]"
                                    :label="value"
                                    :key="value"
                                    v-model="valueChecked[value]"
                                    @change="handleCheckValue(value, $event)"
                                    :title="label"
                                >
                                    {{ label }}
                                </el-checkbox>
                            </div>
                        </div>
                    </template>
                </div>
            </div>
        </transition>
    </div>
</template>
<script lang="ts">
import { Component, Prop, Watch } from "vue-property-decorator";
import { mixins } from "vue-class-component";
import i18n from "@/i18n";
// 引入element封装的popper工具
import Popper from "element-ui/lib/utils/vue-popper";
import Clickoutside from "element-ui/lib/utils/clickoutside";

// 树结构的叶结点
type Lable = {
    label: string; // 显示的文案
    value: string; // 后端生成的唯一 id
};
// 入参配置的树形结构，仅2层
type LabelItem = {
    label: string;
    value: string;
    children: Lable[];
};
// 状态映射关系
type StatusMap = {
    [key: string]: boolean;
};
// 存储映射 key 和 valueoptions 关系，用于判断展示右侧可选项
type LableOptionsMap = {
    [key: string]: Lable[];
};

/**
 * 说明：
 *  1、操作界面上左侧 Key 列只是做分组展示用，真正提交的数据还是右侧的 Value 列（提交的不是 value 内容而是后端为其生成的 id）
 *  2、创建 key value 时，后端会生成唯一 id ，用于对应具体的某个 value ，虽然页面上显示 value 可能存在重复，但是 id 并不会重复
 *  3、业务中使用时，显示的是 key value 的内容，筛选时使用的是 value 对应的 id
 */

// -1 表示全选
const __isAll__ = "-1";

// 标签选择
@Component({
    directives: { Clickoutside },
    model: {
        prop: "selectedLabel",
        event: "select",
    },
})
export default class LabelSelect extends mixins(Popper) {
    @Prop({ default: true }) private appendToBody!: boolean;
    @Prop({ default: () => [] }) private labelList!: LabelItem[];
    @Prop({ default: () => i18n.t("common.labelSelect.placeholder") }) private placeholder!: string;
    @Prop({ default: () => [] }) private selectedLabel!: typeof __isAll__ | string[]; // 选中的 value id 列表， -1 表示全选；由于业务限制（默认域名是没有标签的），不再默认全选，而是默认为空

    private referenceElm?: Element; // Popper 用
    private popperElm?: Element;

    private visible = false;
    private locked = false; // 本地锁，处理下拉框展示需求

    // 1、左侧 keys 全部已选
    keysAllChecked = false;
    // 2、左侧 keys 全选的不确定状态
    keysIndeterminate = false;
    // 3、左侧 keys options 直接用 labelList 即可
    // 4、左侧 key 选中状态，直接用 valuesAllChecked 中对应 values 是否被选中即可；
    // 1、右侧 currentKey 对应的 values 是否全选（key 为键）
    valuesAllChecked: StatusMap = {};
    // 2、右侧 currentKey 对应的 values 全选的不确定状态（key 为键）
    valuesIndeterminate: StatusMap = {};
    // 3、右侧 currentKey 对应的 values options ，多处理一层为了便于使用
    get valuesOptions() {
        const { labelList } = this;
        const optionsMap: LableOptionsMap = {};

        labelList.forEach(({ value, children }) => {
            optionsMap[value] = children;
        });

        return optionsMap;
    }
    // 3.1 先过滤掉 selectedLabel 中不存在与 labelList 中的数据，确保后续的计算可控
    get allChildrenValues() {
        return this.labelList
            .reduce<Lable[]>((all, item) => all.concat(item.children), [])
            .map(item => item.value);
    }
    // 4、右侧 currentKey 对应的 value 的选中状态（内部状态，value 为键）
    valueChecked: StatusMap = {};
    // 4.1 为了便于处理，已选 value 的 map 转为数组用于计算
    get valueCheckedArr() {
        const { valueChecked } = this;
        return Object.keys(valueChecked).filter(value => valueChecked[value]);
    }
    // 4.2 过滤后真正可用的已选内容
    get _selectedLabel() {
        const { selectedLabel, allChildrenValues } = this;
        // 只有入参支持 -1
        const _selectedLabel = selectedLabel === __isAll__ ? [...allChildrenValues] : selectedLabel;
        return _selectedLabel.filter(value => allChildrenValues.includes(value));
    }

    // 当没有选项且已全选时，也认为选中了 label
    get hasCheckedLabel() {
        return this.valueCheckedArr.length > 0;
    }

    // 当前右侧
    currentKey = ""; // 右侧 key:value 展示的是哪个 key 下，重要关联点
    // 右侧需要展示 key 的 name ，缓存一下更方便使用
    get currentKeyName() {
        const { currentKey } = this;
        if (!currentKey) return "";

        const { labelList } = this;

        return labelList.find(item => item.value === currentKey)?.label;
    }

    // values 总个数
    get valuesTotal() {
        return this.labelList.reduce((total, item) => total + (item.children?.length || 0), 0);
    }

    private calculateChecked() {
        const { labelList, selectedLabel } = this;
        labelList.forEach(({ value: key, children }) => {
            this.$set(this.valuesAllChecked, key, false); // 单组全选，用 key 存
            this.$set(this.valuesIndeterminate, key, false);
            children.forEach(({ value }) => {
                this.$set(this.valueChecked, value, false); // 单个选择，用 value 存
            });
        });

        if (selectedLabel === __isAll__) {
            this._checkAll();
        } else {
            const { _selectedLabel, allChildrenValues } = this;

            if (allChildrenValues.length === _selectedLabel.length && allChildrenValues.length) {
                // 等同全选
                this._checkAll();
            } else if (_selectedLabel.length === 0) {
                // 等同全删
                this._clearAll();
            } else {
                this.keysAllChecked = false; // 未全选
                this.keysIndeterminate = true; // 部分选中

                // 每个 key 判断
                this.labelList.forEach(({ value: key, children }) => {
                    const { _selectedLabel } = this;
                    // 初始化选择状态
                    children.forEach(({ value }) => {
                        // 存在则已选
                        if (_selectedLabel.includes(value)) {
                            this.valueChecked[value] = true;
                        } else {
                            this.valueChecked[value] = false;
                        }
                    });
                    this._checkOneKey(key);
                });
            }
        }
    }

    @Watch("labelList", { immediate: true })
    // 格式化初始数据
    onLabelListChange() {
        // 根据被选项和已选项，完成初始化
        const { labelList } = this;
        // 为几组 map 初始化处理
        this.calculateChecked();

        // 默认选中第一组展示
        if (labelList.length > 0) {
            this.currentKey = labelList[0].value;
        }

        this.emitData();
    }

    // 全选
    _checkAll() {
        this.keysAllChecked = true; // 整组全选
        this.keysIndeterminate = false;
        Object.keys(this.valuesAllChecked).forEach(key => (this.valuesAllChecked[key] = true));
        Object.keys(this.valuesIndeterminate).forEach(key => (this.valuesIndeterminate[key] = false));
        Object.keys(this.valueChecked).forEach(key => (this.valueChecked[key] = true));
    }
    // 全未选
    private _clearAll() {
        this.keysAllChecked = false; // 整组
        this.keysIndeterminate = false;
        Object.keys(this.valuesAllChecked).forEach(key => (this.valuesAllChecked[key] = false));
        Object.keys(this.valuesIndeterminate).forEach(key => (this.valuesIndeterminate[key] = false));
        Object.keys(this.valueChecked).forEach(key => (this.valueChecked[key] = false));
    }

    // （按需）遍历已选信息，更新到上层
    private emitData() {
        // 出参按实际情况返回，不再支持返回 -1 表示全部，上层必须严格按照已选标签过滤域名
        const data = [...this.valueCheckedArr];
        this.$emit("select", data);
    }

    // 判断某一组是否全选
    _checkOneKey(key: string) {
        // 查看单签 children 有几个已选
        const children = this.valuesOptions[key];

        const checkedTotal = children.reduce<number>(
            (count, { value }) => (this.valueChecked[value] ? ++count : count),
            0
        );

        if (checkedTotal === 0) {
            this.valuesAllChecked[key] = false;
            this.valuesIndeterminate[key] = false;
        } else if (checkedTotal === children.length) {
            this.valuesAllChecked[key] = true;
            this.valuesIndeterminate[key] = false;
        } else {
            this.valuesAllChecked[key] = false;
            this.valuesIndeterminate[key] = true;
        }
    }
    // 判断全部是否全选
    _checkAllKey() {
        // 每个 key 判断
        this.labelList.forEach(({ value: key }) => {
            this._checkOneKey(key);
        });

        const checkedTotal = Object.keys(this.valueChecked).reduce<number>(
            (count, value) => (this.valueChecked[value] ? ++count : count),
            0
        );

        if (checkedTotal === 0) {
            // 一个没选
            this.keysAllChecked = false;
            this.keysIndeterminate = false;
        } else if (checkedTotal === this.allChildrenValues.length) {
            // 全选
            this.keysAllChecked = true;
            this.keysIndeterminate = false;
        } else {
            // 部分已选
            this.keysAllChecked = false;
            this.keysIndeterminate = true;
        }
    }

    // keys 全选框，点击切换全局的全选状态
    handleCheckAllKeys(flag: boolean) {
        // 切换所有的选中状态
        flag ? this._checkAll() : this._clearAll();
        // 对外更新
        this.emitData();
    }

    // 点击左侧 key 的选择框，切换该 key 组的 value 选中状态
    handleCheckKey(key: string, flag: boolean) {
        this.currentKey = key;
        this.valuesAllChecked[key] = flag;
        // 变更当前组
        this.valuesOptions[key].forEach(({ value }) => {
            this.valueChecked[value] = flag;
        });
        // 检查全部
        this._checkAllKey();
        // 对外更新
        this.emitData();
    }

    // 点击左侧 key 的文案，只切换 current key，影响右侧 value 展示内容，不执行选择操作
    handleClickKeyContent(key: string) {
        this.currentKey = key;
    }

    // 点击右侧 key:value 全选按钮
    handleCheckCurrentValues(key: string, flag: boolean) {
        this.handleCheckKey(key, flag);
    }

    // 点击右侧 value
    handleCheckValue(value: string, flag: boolean) {
        // 变更当前
        this.valueChecked[value] = flag;
        // 检查全部
        this._checkAllKey();
        // 对外更新
        this.emitData();
    }

    // 为了 popper 提供基础配置
    private mounted() {
        this.referenceElm = (this.$refs.reference as Vue).$el;
        this.popperElm = (this.$refs.popper as Vue).$el;
        this.createPopper();
        this.$on("updatePopper", () => {
            if (this.visible) this.updatePopper();
        });
        this.$on("destroyPopper", this.destroyPopper);
    }

    // 监控输入变化
    @Watch("visible")
    private onVisibleChange(val: boolean) {
        if (val) {
            this.updatePopper();
        } else {
            (this.$refs.input as HTMLInputElement).blur(); // 关闭时清除边框
        }
    }

    @Watch("selectedLabel")
    private onSelectedLabelChange(val: string[] | string) {
        if (val?.length === 0) {
            this._clearAll();
        }
        this.calculateChecked();
    }
    // 交互
    private handleClose() {
        this.visible = false;
    }
    private handleFocus() {
        // 点击下拉按钮会先触发focus事件再触发click事件，所以要加个锁防止下拉框消失，同时要清除锁防止关闭不了
        if (!this.visible) {
            this.locked = true;
            this.visible = true;
            setTimeout(() => {
                this.locked = false;
            }, 300);
        }
    }
    private toggleDropdown() {
        if (this.locked) {
            this.locked = false;
        } else {
            this.visible = !this.visible;
        }
    }
}
</script>
<style lang="scss" scoped>
.label-select {
    // color: #c0c4cc;
    display: inline-block;
    position: relative;
    line-height: 32px;
}
.label-select__tags {
    position: absolute;
    z-index: 100;
    line-height: 32px;
    max-width: calc(100% - 60px);
    top: 50%;
    transform: translate(0, -50%);
    .el-tag {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        vertical-align: middle;
        width: 100%;
    }
}
.label-select__input {
    position: relative;
    width: 100%;
    input {
        outline: none;
        cursor: pointer;
        width: 100%;
        // padding: 0 30px 0 12px;
        // height: 32.1px;
        // box-sizing: border-box;
        // line-height: 32px;
        // border: 1px solid #dcdfe6;
        // &.is-focus {
        //     border: 1px solid $color-master;
        // }
        // &::placeholder {
        //     color: $color-neutral-7;
        //     font-size: 12px;
        //     line-height: 1.5;
        // }
    }
}
.el-icon-arrow-up {
    pointer-events: none;
    cursor: pointer;
    text-align: center;
    position: absolute;
    width: 25px;
    right: 5px;
    transform: rotateZ(180deg);
    transition: transform 0.3s;
    line-height: 32px;
    font-size: 14px;
    color: #999;
    &.is-reverse {
        transform: rotateZ(0deg);
    }
}
.label-select__dropdown {
    // position: absolute;
    position: fixed !important;
    z-index: 1001;
    min-width: 260px !important;
    border-radius: 3px;
    background-color: #ffffff;
    box-shadow: $shadow-2;
    box-sizing: border-box;
}
// 下拉框内容区
.label-select__dropdown-wrapper {
    padding: 16px !important;
    // box-shadow: $shadow-2;

    // 下拉框的两部分
    .dropdown-wrap {
        vertical-align: top;
        display: inline-block;
        min-width: 100px;
    }

    .dropdown-list {
        overflow: auto;
        margin-top: 8px;
        max-height: 200px;
        min-width: 130px;
        max-width: 300px;
    }

    // 单选框
    .el-checkbox {
        margin-right: 0;
        display: block;
        height: 24px;
        line-height: 1.5px;
    }

    // label
    .dropdown-item-label {
        line-height: 1.5px;
    }
}
.el-select-dropdown__empty {
    width: 100%;
}
.label {
    > label {
        vertical-align: middle;
    }
    .label-checkbox {
        margin-right: 4px;
        margin-top: 1px;
    }
    .label-category {
        display: inline-block;
        width: 75px;
        font-weight: bold;
    }
    & + .label {
        margin-top: 8px;
    }
}

::v-deep .el-checkbox__label {
    width: calc(100% - 16px);
}
</style>
