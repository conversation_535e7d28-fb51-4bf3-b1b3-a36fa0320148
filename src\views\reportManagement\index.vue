<template>
  <ct-section-wrap :headerText="headerText">
    <ct-box class="report-container table-scroll-wrap">
        <el-alert v-if="disableCreate" title="" type="warning" :closable="false" show-icon style="margin-bottom:16px">
            <template slot="title">
                <div>{{ $t("report.alertInfo") }}</div>
            </template>
        </el-alert>
        <div class="search-bar">
            <div class="search-bar__left">
                <el-button v-loading="createLoading" type="primary" :disabled="disableCreate" @click="handleCreate">{{ `+ ${$t("report.btns.create")}` }}</el-button>
            </div>
            <div class="search-bar__right">
                <el-tooltip :content="$t('report.search.placeholder[0]')" placement="top-start" :disabled="!isEn">
                    <el-select
                        v-model="searchParams.frequency"
                        :placeholder="$t('report.search.placeholder[0]')"
                        clearable
                    >
                        <el-option
                            v-for="(item, index) in frequencyOptions"
                            :key="index"
                            :label="item.label"
                            :value="item.value"
                        />
                    </el-select>
                </el-tooltip>
                <el-tooltip :content="$t('report.search.placeholder[1]')" placement="top-start" :disabled="!isEn">
                    <el-select
                        v-model="searchParams.status"
                        :placeholder="$t('report.search.placeholder[1]')"
                        clearable
                    >
                        <el-option
                            v-for="(item, index) in statusOptions"
                            :key="index"
                            :label="item.label"
                            :value="item.value"
                        />
                    </el-select>
                </el-tooltip>
                <el-input
                    v-model="searchParams.name"
                    :placeholder="$t('report.search.placeholder[2]')"
                    clearable
                />
                <el-button type="primary" plain @click="onSearch">{{ $t("report.btns.search") }}</el-button>
                <el-button @click="onReset">{{ $t("report.btns.reset") }}</el-button>
            </div>
        </div>
        <el-table
            v-loading="loading"
            :data="dataList"
        >
            <el-table-column :label="$t('report.table[0]')" prop="reportName" show-overflow-tooltip />
            <el-table-column :label="$t('report.table[1]')" prop="sendFrequency">
                <template slot-scope="{ row }">
                    {{ $t(`report.search.frequencyOptions[${+row.sendFrequency - 1}]`) }}
                </template>
            </el-table-column>
            <el-table-column :label="$t('report.table[2]')" prop="reportStatus">
                <template slot-scope="{ row }">
                    {{ $t(`report.search.statusOptions[${+row.reportStatus - 1}]`) }}
                </template>
            </el-table-column>
            <el-table-column :label="$t('report.table[3]')" prop="createTime">
                <template slot-scope="{ row }">
                    {{ row.createTime | dateFormat }}
                </template>
            </el-table-column>
            <el-table-column :label="$t('report.table[4]')" prop="sendNextTime">
                <template slot-scope="{ row }">
                    {{ row.sendNextTime | dateFormat }}
                </template>
            </el-table-column>
            <el-table-column :label="$t('report.table[5]')" width="180">
                <template slot-scope="{ row }">
                    <el-button type="text" @click="handleUpdate(row)">{{ $t('report.btns.edit') }}</el-button>
                    <el-button v-if="row.reportStatus === 2" type="text" @click="handleEnable(row, false)">{{ $t('report.btns.disable') }}</el-button>
                    <el-button v-else type="text" @click="handleEnable(row, true)">{{ $t('report.btns.enable') }}</el-button>
                    <el-button type="text" @click="handleDelete(row)">{{ $t('report.btns.delete') }}</el-button>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            :small="isXs"
            :layout="isXs ? 'prev, pager, next' : 'total, sizes, prev, pager, next,jumper'"
            :total="pager.total"
            :current-page.sync="pager.pageNum"
            :page-size="pager.pageSize"
            :page-sizes="[10, 30, 50, 100]"
            :hide-on-single-page="false"
            @size-change="handlePageSizeChange"
            @current-change="handleCurrentPageChange"
        />
    </ct-box>
  </ct-section-wrap>
</template>
<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import { ScreenModule } from "@/store/modules/screen";
import { reportUrl } from "@/config/url";
import { getLang } from "@/utils";
import { checkCtiamButtonAuth } from "@/store/modules/menu";
import { GetCtiamButtonAction } from "@/store/config";
import { CtiamCode } from "@/utils/ctFetch/errorConfig";

@Component({
    name: "reportManagement",
    filters: {
        dateFormat(date: string) {
            return date && date.replace(/T/, " "); // 返回时间过滤多余字符
        }
    }
})
export default class ReportManagement extends Vue {
    private headerText = this.$t("report.title");
    private loading = false;
    private createLoading = false
    private searchParams = {
        name: "",
        status: "",
        frequency: "", 
    }
    private statusOptions = [
        { label: this.$t("report.search.statusOptions[1]"), value: 2 },
        { label: this.$t("report.search.statusOptions[0]"), value: 1 },
    ]
    private frequencyOptions = [
        { label: this.$t("report.search.frequencyOptions[0]"), value: 1 },
        { label: this.$t("report.search.frequencyOptions[1]"), value: 2 },
        { label: this.$t("report.search.frequencyOptions[2]"), value: 3 },
        { label: this.$t("report.search.frequencyOptions[3]"), value: 4 },
        { label: this.$t("report.search.frequencyOptions[4]"), value: 5 },
        { label: this.$t("report.search.frequencyOptions[5]"), value: 6 },
        { label: this.$t("report.search.frequencyOptions[6]"), value: 7 },
    ]
    private dataList: any[] = []
    private pager = {
        pageNum: 1,
        pageSize: 10,
        total: 0
    }
    private reportCount = 0;

    get isXs() {
        return this.screenWidth < 600;
    }
    get screenWidth() {
        return ScreenModule.width;
    }
    
    get isEn() {
        return getLang() === "en";
    }

    get disableCreate() {
        return this.reportCount >= 10; // 订阅报表个数超过10个不允许新增
    }

    private async mounted() {
        await this.onSearch();
        this.reportCount = this.dataList.length;
    }

    private async onSearch() {
        try {
            this.loading = true;
            const data: any = await this.$ctFetch(reportUrl.getReportList, {
                method: "GET",
                transferType: "json",
                data: {
                    sendFrequency: this.searchParams.frequency,
                    reportStatus: this.searchParams.status,
                    reportName: window.encodeURIComponent(this.searchParams.name),
                    pageNum: this.pager.pageNum,
                    pageSize: this.pager.pageSize
                },
            });
            this.pager.total = data.total;
            this.dataList = data.items;
        } catch(err) {
            const isCtiamError = CtiamCode.includes((err as any)?.data?.code);
            if (isCtiamError) {
                // 权限报错时清空数据
                this.dataList = [];
            }
            this.$errorHandler(err);
        } finally {
            this.loading = false;
        }
    }

    private async onReset() {
        this.searchParams = {
            name: "",
            status: "",
            frequency: "", 
        };
        this.pager = {
            pageNum: 1,
            pageSize: 10,
            total: 0
        }
        await this.onSearch();
        this.reportCount = this.dataList.length;
    }

    private async handleCreate() {
        await checkCtiamButtonAuth(GetCtiamButtonAction("reportAdd"));
        this.$router.push({
            name: "reportManagement.create"
        })
    }

    private async handleUpdate(data: any) {
        await checkCtiamButtonAuth(GetCtiamButtonAction("reportEdit"));
        this.$router.push({
            name: "reportManagement.update",
            query: {
                id: data?.reportTemplateId
            }
        })
    }

    private async handleEnable(data: any, val: boolean) {
        await checkCtiamButtonAuth(GetCtiamButtonAction("reportManagementOperation"));

        const info = `${val ? this.$t("report.dialogInfo.enable") : this.$t("report.dialogInfo.disable")}`;
        this.$confirm(info, {
            title: `${this.$t("common.messageBox.title")}`,
            confirmButtonText: `${this.$t("common.dialog.submit")}`,
            cancelButtonText: `${this.$t("common.dialog.cancel")}`,
            type: "warning"
        }).then(async () => {
            try {
                await this.$ctFetch(val ? reportUrl.enableReport : reportUrl.disableReport, {
                   method: "POST",
                    body: {
                        data: {
                            reportTemplateId: data.reportTemplateId
                        },
                    },
                });
                this.onSearch();
            } catch(e) {
                this.$errorHandler(e);
            }
        }).catch(err => err)
    }

    private async handleDelete(data: any) {
        await checkCtiamButtonAuth(GetCtiamButtonAction("reportManagementDelete"));

        this.$confirm(`${this.$t("report.dialogInfo.delete")}`, {
            title: `${this.$t("common.messageBox.title")}`,
            confirmButtonText: `${this.$t("common.dialog.submit")}`,
            cancelButtonText: `${this.$t("common.dialog.cancel")}`,
            type: "warning"
        }).then(async () => {
            try {
                await this.$ctFetch(reportUrl.deleteReport, {
                   method: "POST",
                    body: {
                        data: {
                            reportTemplateId: data.reportTemplateId
                        },
                    },
                });
                this.onReset();
            } catch(e) {
                this.$errorHandler(e);
            }
        }).catch(err => err)
    }

    private handlePageSizeChange(pageSize: number) {
        this.pager.pageSize = pageSize;
        this.pager.pageNum = 1;
        this.onSearch();
    }
    private handleCurrentPageChange(page: number) {
        this.pager.pageNum = page;
        this.onSearch();
    }
}
</script>
<style lang="scss" scoped>
.report-container {
    display: flex;
    flex-direction: column;
    min-width: 920px;
    .search-bar {
        display:  flex;
        &__right {
            flex: 1;
            display: flex;
            justify-content: flex-end;
            & > *:not(:first-child) {
                margin-left: 12px;
                margin-right: 0;
            }
        }
        .el-select {
            width: 150px;
        }
        .el-input {
            width: 320px;
        }
    }
    .el-table {
        // flex: 1;
    }
}

.pager {
    margin-top: 8px;
    ::v-deep.el-pagination {
        text-align: right !important;
    }
}
</style>
