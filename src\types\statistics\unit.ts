/*
 * @Description: 单位相关
 * @Author: wang y<PERSON><PERSON>
 */

// 带宽单位类型
export type BandwidthUnitB2G = "bps" | "Kbps" | "Mbps" | "Gbps";
export type BandwidthUnitM2G = "Mbps" | "Gbps";
export type BandwidthUnitM2P = "Mbps" | "Gbps" | "Tbps" | "Pbps";
export type BandwidthUnitB2P = BandwidthUnitB2G | BandwidthUnitM2P;
export type BandwidthUnitB2Y = BandwidthUnitB2P | "Ebps" | "Zbps" | "Ybps";

// 带宽单位常量
export const BandwidthUnits: {
    B2G: BandwidthUnitB2G[];
    M2G: BandwidthUnitM2G[];
    M2P: BandwidthUnitM2P[];
    B2P: BandwidthUnitB2P[];
    B2Y: BandwidthUnitB2Y[];
} = {
    B2G: ["bps", "Kbps", "Mbps", "Gbps"],
    M2G: ["Mbps", "Gbps"],
    M2P: ["Mbps", "Gbps", "Tbps", "Pbps"],
    B2P: ["bps", "Kbps", "Mbps", "Gbps", "Tbps", "Pbps"],
    B2Y: ["bps", "Kbps", "Mbps", "Gbps", "Tbps", "Pbps", "Ebps", "Zbps", "Ybps"],
};

// 流量单位类型
export type FlowUnitB2G = "B" | "KB" | "MB" | "GB";
export type FlowUnitM2G = "MB" | "GB";
export type FlowUnitM2P = "MB" | "GB" | "TB" | "PB";
export type FlowUnitB2P = FlowUnitB2G | FlowUnitM2P;
export type FlowUnitB2Y = FlowUnitB2P | "EB" | "ZB" | "YB";
export type FlowUnitG2P = "GB" | "TB" | "PB";

// 流量单位常量
export const FlowUnits: {
    B2G: FlowUnitB2G[];
    M2G: FlowUnitM2G[];
    M2P: FlowUnitM2P[];
    B2P: FlowUnitB2P[];
    B2Y: FlowUnitB2Y[];
    G2P: FlowUnitG2P[];
} = {
    B2G: ["B", "KB", "MB", "GB"],
    M2G: ["MB", "GB"],
    M2P: ["MB", "GB", "TB", "PB"],
    B2P: ["B", "KB", "MB", "GB", "TB", "PB"],
    B2Y: ["B", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"],
    G2P: ["GB", "TB", "PB"],
};
