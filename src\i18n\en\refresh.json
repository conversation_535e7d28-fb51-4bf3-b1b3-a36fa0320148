{"headerText": "Refresh and Prefetch", "headerTip": "Creating refresh or prefetch tasks is suitable for updating and publishing origin resources, cleaning up unauthorized resources, and changing domain configurations, so as to reduce origin pressure and improve user experience.", "createBtn": "Create Task", "search": {"label": ["Select Time", "Search Criteria", "Task Status"], "placeholder2": "Please Enter the Full Domain Name Search", "placeholder3": "Please select the task status", "error1": "Time cannot be empty, please select a time", "tip1": "Only tasks within the last {timeLimit} days, spanning up to {timeSpanLimit} days, can be queried."}, "table": {"label": ["No.", "Content", "Submission Time", "State"]}, "status": ["Waiting", "On operation", "Operation succeed", "Failed", "Retrying"], "common": {"tab": ["URL Refresh", "Directory Refresh", "Regular Refresh", "URL Prefetch"]}, "create": {"title": "Create Refresh & Prefetching Task", "submitBtn": "Submit", "confirm": "OK", "cancel": "Cancel", "copy": "Copy", "continue": "Continue", "operationLabel": "Operation Type", "tip": ["1. The cached content of the CDN is not updated regularly. When the content of your origin is updated and you want users to get the latest content, you can submit a refresh task.\n2. A large batch of refresh may cause a high concurrent requests to be sent back-to-origin which will cause a high bandwidth. So, if the origin only has a small bandwidth, it is recommended that to refresh in small batches. \n3. One line per URL (carriage return) and supports up to {size} lines at a time, and pay attention to case sensitive URLs, refresh tasks generally take 5-10 minutes to take effect.", "1. The cached content of the CDN is not updated regularly. When the content of your origin is updated and you want users to get the latest content, you can submit a refresh task.\n2. A large batch of refresh may cause a high concurrent requests to be sent back-to-origin which will cause a high bandwidth. So, if the origin only has a small bandwidth, it is recommended that to refresh in small batches. \n3. One line per URL (carriage return) and supports up to {size} lines at a time, and note that the letters in the URL are case sensitive, the feature automatically pushes subdirectories, no need to fill in subdirectories, refresh task usually takes 5-10 minutes to take effect.", "1. Prefetching feature is used to preheat the specified content and cached to CDN in advance, so that the user can directly hit the cache on the first visit.\n2. A large batch of file prefetching may cause a high concurrent requests to be sent back-to-origin which will cause a high bandwidth. So, if the origin only has a small bandwidth, it is recommended that to prefetch in multiple small batches.\n3. One line per URL (carriage return)  and supports up to {size} lines at a time, and pay attention to the case distinction of the letters in the URL, prefetching tasks take generally 5 ~ 30 minutes to take effect.", "1. The cached content of the CDN is not updated regularly. When the content of your origin is updated and you want users to get the latest content, you can submit a refresh task.\n2. A large batch of refresh may cause a high concurrent requests to be sent back-to-origin which will cause a high bandwidth. So, if the origin only has a small bandwidth, it is recommended that to refresh in small batches. \n3. One line per URL (carriage return) and supports up to {size} lines at a time, and pay attention to case sensitive URLs, refresh tasks generally take 5-10 minutes to take effect."], "tipWarn": ["The domain names to be added must be under the same domain name and managed by the user name.", "The name of the directory to be added must be in the same domain name, and must be the domain name managed by the user.", "The domain names to be added must be under the same domain name and managed by the user name.", "The domain names to be added must be under the same domain name and managed by the user name."], "placeholder": ["Please enter the full URL you want to refresh, each URL should be entered as http:// or https://. Making http://{url}/images/test.jpg as an example, one line per URL (carriage return). Please be careful to distinguish the case of the letters in the URL, incorrect case will make the refresh invalid!", "Please enter the directory you want to refresh, the directory should be starting in http:// or https:// and end up with '/',making http://{url}/images/as an example.\none line per directory(carriage return for new line), please note the case distinction of the letters in the URL. wrong case will cause the refresh to be invalid!", "Please enter the full URL you want to prefetch, each URL should be entered as http:// or https://. Making http://{url}/images/test.jpg as an example, one line per URL (carriage return). Please be careful to distinguish the case of the letters in the URL, incorrect case will make the prefetch invalid!", "Please enter the regex URL you want to refresh. Each URL should be in the format of domain + regex path, and must start with http:// or https://. Example: http://www.ctyun.cn/[0-9][a-z].*.png. One URL per line (press Enter to add a new line). Please make sure to use the correct case in URLs — incorrect letter casing will cause the refresh to fail. Currently, only the following regex patterns are supported: [0-9], [a-z], [^/], .*."], "dialogTitle": "System Prompt", "dialogLabel": "Domain Details", "msg1": "Please enter data", "msg2": "Please enter up to {size} items of data", "msg3": "Please check that you have the authority to access to {url} and that the domain is available", "msg5": "Submitted out of quota, failed!", "msg6": "Your task has been submitted successfully, please verify in 5-10 minutes", "msg6-1": "Your task has been submitted successfully", "msg7": "Unrecognized", "msg8": "The following domains do not exist or not belong to this account. Please check whether you have permission to operate the listed domains and ensure the domain status is enabled. Click \"Continue\", only the URLs under valid domains will be submitted. Please click \"Cancel\" when all of the domains in the URLs are invalid and try to input the URLs again."}}