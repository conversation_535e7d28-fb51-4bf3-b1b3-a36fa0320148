<template>
    <ct-section-wrap class="overview-section-wrapper">
        <el-col class="overview-wrapper">
            <div class="overview-wrapper-left">
                <el-card>
                    <el-form class="search-form" :model="queryForm" label-width="0" inline>
                        <div class="search-form-wrapper">
                            <el-form-item>
                                <domain-select
                                    class="search-form-wrapper-domain"
                                    placeholder="全部域名"
                                    v-model="queryForm.domain"
                                    :domainOptions="domainOptions"
                                    :multiple="true"
                                    key="multipleDomain"
                                    @blur="customRenderWrapper()"
                                />
                            </el-form-item>
                            <el-form-item>
                                <ct-time-picker
                                    class="ct-time-picker-custom-wrapper"
                                    v-model="queryForm.timeRange"
                                    :periodOptions="['0', '1', '7', 'recentMonth', '30', '-1']"
                                    :maxDayBeforeNow="180"
                                    type="datetimerange"
                                    key="timePicker"
                                    size="medium"
                                />
                            </el-form-item>
                        </div>
                    </el-form>
                </el-card>
                <el-card>
                    <cute-titled-block title="数据概览">
                        <template #content>
                            <el-divider></el-divider>
                            <div class="data-overview-container" v-loading="loading">
                                <div
                                    class="overview-item-wrapper"
                                    v-for="(itm, idx) in dataOverviewCompound"
                                    :key="idx"
                                >
                                    <div class="overview-item-wrapper-icon">
                                        <ctSvgIcon :icon-class="itm.icon"></ctSvgIcon>
                                    </div>
                                    <div class="overview-item-wrapper-label">{{ itm.label }}</div>
                                    <div class="overview-item-wrapper-data">
                                        <span class="overview-item-wrapper-data-value"> {{ itm.value }}</span>
                                        <span class="overview-item-wrapper-data-unit"> {{ itm.unit }}</span>
                                    </div>
                                </div>
                            </div>
                        </template>
                        <template #extra>
                            <el-button type="text" @click="overviewMore">查看详情>></el-button>
                        </template>
                    </cute-titled-block>
                </el-card>
                <el-card class="usage-trend-wrapper">
                    <cute-titled-block title="用量趋势">
                        <template #content>
                            <el-divider></el-divider>
                            <div class="usage-trend-container">
                                <overview-chart
                                    ref="overviewChart"
                                    :activeType="activeChartType"
                                    :timeRange="queryForm.timeRange"
                                    :domain="queryForm.domain"
                                    :domainOptions="domainOptions"
                                />
                            </div>
                        </template>
                        <template #extra>
                            <el-tabs v-model="activeChartType">
                                <el-tab-pane label="流量趋势" name="flow"></el-tab-pane>
                                <el-tab-pane label="带宽趋势" name="bandwidth"></el-tab-pane>
                                <el-tab-pane label="连接数趋势" name="connections"></el-tab-pane>
                            </el-tabs>
                        </template>
                    </cute-titled-block>
                </el-card>
            </div>
            <div class="overview-wrapper-right">
                <el-card>
                    <cute-titled-block title="基础信息">
                        <template #content>
                            <el-divider></el-divider>
                            <el-table :data="basicInfoCompound">
                                <el-table-column label="加速区域" prop="area"></el-table-column>
                                <el-table-column label="域名统计" prop="domain">
                                    <template slot-scope="{ row }">
                                        <div>{{ row.domain || 0 }}个</div>
                                    </template>
                                </el-table-column>
                                <el-table-column label="端口统计" min-width="140px">
                                    <template slot-scope="{ row }">
                                        <div>TCP端口：{{ row.port.tcp }}个</div>
                                        <div>UDP端口：{{ row.port.udp }}个</div>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </template>
                    </cute-titled-block>
                </el-card>
                <el-card>
                    <cute-titled-block title="套餐信息">
                        <template #content>
                            <el-divider></el-divider>
                            <div class="package-info-wrapper">
                                <div
                                    class="package-info-item"
                                    v-for="(itm, idx) in packageInfoCompound"
                                    :key="idx"
                                >
                                    <div class="package-info-item-title">
                                        {{ itm.title }}
                                    </div>
                                    <div class="package-info-item-content">
                                        <template v-if="Array.isArray(itm.content)">
                                            <div v-for="(itm2, idx2) in itm.content" :key="idx2">
                                                {{ itm2 }}
                                            </div>
                                        </template>
                                        <template v-else>{{ itm.content }}</template>
                                    </div>
                                </div>
                            </div>
                        </template>
                        <template #extra>
                            <el-button type="text" @click="packageInfoCheckMore">查看更多</el-button>
                        </template>
                    </cute-titled-block>
                </el-card>
                <el-card class="qa-block-wrapper">
                    <cute-titled-block title="">
                        <template #title>
                            <el-tabs v-model="billingTypeQA">
                                <el-tab-pane label="常见问题" name="QA"> </el-tab-pane>
                                <el-tab-pane label="文档资源" name="DOC"></el-tab-pane>
                            </el-tabs>
                        </template>
                        <template #content>
                            <el-divider></el-divider>
                            <div>
                                <link-item
                                    v-for="(itm, idx) in getQaAndDocList[billingTypeQA] || []"
                                    :linkData="itm"
                                    :idx="idx + 1"
                                    :key="idx"
                                />
                            </div>
                        </template>
                        <template #extra>
                            <el-link
                                :underline="false"
                                class="aocdn-ignore-link"
                                @click="$docHelp(viewMoreLink)"
                                >查看更多</el-link
                            >
                        </template>
                    </cute-titled-block>
                </el-card>
            </div>
        </el-col>
    </ct-section-wrap>
</template>
<script>
/* eslint-disable @typescript-eslint/camelcase */
import { OverviewUrl } from "@/config/url";
import { DomainUrl } from "@/config/url/ipa/domain";
import { ScaleModule } from "@/store/modules/scale";
import { convertFlowB2P, convertBandwidthB2P } from "@/utils";
import { DomainModule } from "@/store/modules/domain";
import overviewChart from "./components/overview-chart.vue";
import LinkItem from "./components/linkItem.vue";
import DomainSelect from "@/components/domainsSelect";
import { AppModule } from "@/store/modules/app";
// svg icon
import ctSvgIcon from "@/components/ctSvgIcon";
import CtTimePicker from "@/components/ctTimePicker/index.vue";
import actions from "@/microApp/actions";
import { get } from "lodash-es";
import { IpaConfigModule } from "@/store/modules/ipa/config";
import urlTransformer from "@/utils/logic/url";

export default {
    components: { ctSvgIcon, CtTimePicker, overviewChart, LinkItem, DomainSelect },
    data() {
        return {
            queryForm: {
                domain: [],
                timeRange: null,
            },
            activeChartType: "flow",
            billingTypeQA: "QA",
            domainCount: {},
            portCount: {},
            totalFlow: {},
            peakBandWith: {},
            billingMethod: [],
            domainOrderStatus: 0,
            domainOptions: [],
            loading: false,
            statsData: {
                total_flow: "",
                peek_bandwidth: "",
                peek_connections: "",
                total_attack_event: "",
                peek_ddos_bandwidth: "",
            },
            loadingIndex: 1,
            packageInfo: {},
            productCodeMap: {
                "009": "应用加速(中国内地)",
                117: "应用加速频道(中国内地)",
                118: "应用加速端口(中国内地)",
                130: "应用加速(全球不含中国内地)",
                131: "应用加速频道(全球不含中国内地)",
                132: "应用加速端口(全球不含中国内地)",
            },
        };
    },

    async mounted() {
        this.getPackageInfo();
        await this.getDomainOrderCheck();
        await this.getDomainList();
        this.getOverviewStat();
        this.getDomainCount();
        this.getPortCount();
        this.getBillingMethod();
    },
    computed: {
        // ***单位换算：获取进制基数
        scale() {
            return ScaleModule.scale;
        },
        // 数据预览
        dataOverviewCompound() {
            const {
                total_flow,
                // total_attack_event,
                peek_bandwidth,
                peek_connections,
                // peek_ddos_bandwidth,
            } = this.statsData;
            const totalFlow = convertFlowB2P(total_flow, this.scale);
            const peakBandwidth = convertBandwidthB2P(peek_bandwidth, this.scale);
            // const attackEvent = {
            //     num: total_attack_event,
            //     unit: "次",
            // };
            const peakConnections = {
                num: peek_connections,
                unit: "个/秒",
            };
            // const peekDDosBw = convertBandwidthB2P(peek_ddos_bandwidth, this.scale);
            return [
                {
                    icon: "ipa-total-flow",
                    label: "总流量",
                    value: totalFlow.num || "",
                    unit: totalFlow.unit || "",
                },
                {
                    icon: "ipa-bandwidth",
                    label: "带宽峰值",
                    value: peakBandwidth.num || "",
                    unit: peakBandwidth.unit || "",
                },
                {
                    icon: "ipa-connection",
                    label: "并发连接数峰值",
                    value: peakConnections.num || "0",
                    unit: peakConnections.unit || "",
                },
                // {
                //     icon: "ipa-attack",
                //     label: "攻击事件数",
                //     value: attackEvent.num || "0",
                //     unit: attackEvent.unit || "",
                // },
                // {
                //     icon: "ipa-ddos",
                //     label: "DDos防护带宽峰值",
                //     value: peekDDosBw.num || "",
                //     unit: peekDDosBw.unit || "",
                // },
            ];
        },
        // 基础信息
        basicInfoCompound() {
            return [
                {
                    area: "中国内地",
                    domain: this.domainCount.mainland,
                    port: {
                        tcp: this.portCount.mainland_tcp,
                        udp: this.portCount.mainland_udp,
                    },
                },
                {
                    area: "全球(不含中国内地)",
                    domain: this.domainCount.overseas,
                    port: {
                        tcp: this.portCount.overseas_tcp,
                        udp: this.portCount.overseas_udp,
                    },
                },
                {
                    area: "全球",
                    domain: this.domainCount.global,
                    port: {
                        tcp: this.portCount.global_tcp,
                        udp: this.portCount.global_udp,
                    },
                },
            ];
        },
        // 套餐详情
        packageInfoCompound() {
            console.log("packageInfo = ", this.packageInfo);
            const { area, type, flow, flow_abroad, expire } = this.packageInfo;
            const versionMap = {
                basic: "基础版",
                custom: "定制版",
            };
            const areaMap = {
                0: "中国内地",
                1: "全球(不含中国内地)",
                2: "全球",
            };
            return [
                {
                    title: "套餐版本",
                    content: versionMap[type] || "-",
                },
                {
                    title: "加速区域",
                    content: areaMap[area] || "-",
                },
                {
                    title: "套餐流量",
                    content: (() => {
                        if (area && "01".includes(area.toString())) {
                            return [`${flow || 0}GB`];
                        }
                        if (area && "2".includes(area.toString())) {
                            return [`中国内地: ${flow || 0}GB`, `全球(不含中国内地): ${flow_abroad || 0}GB`];
                        }
                        return "-";
                    })(),
                },
                {
                    title: "到期时间",
                    content: expire || "-",
                },
            ];
        },
        // 查看更多
        viewMoreLink() {
            return urlTransformer(
                {
                    a1Ctyun: "https://www.ctyun.cn/document/10065985",
                    a1Ctcloud: "https://www.esurfingcloud.com/document/zh-cn/20689737",
                }
            )
        },
        qaLinkCtcloud() {
            return "https://www.esurfingcloud.com/document/zh-cn/20689737/20690046";
        },
        qaLink1() {
            return urlTransformer(
                {
                    a1Ctyun: "https://www.ctyun.cn/document/10065985/10280662",
                    a1Ctcloud: this.qaLinkCtcloud,
                }
            )
        },
        qaLink2() {
            return urlTransformer(
                {
                    a1Ctyun: "https://www.ctyun.cn/document/10065985/10280680",
                    a1Ctcloud: this.qaLinkCtcloud,
                }
            )
        },
        qaLink3() {
            return urlTransformer(
                {
                    a1Ctyun: "https://www.ctyun.cn/document/10065985/10280681",
                    a1Ctcloud: this.qaLinkCtcloud,
                }
            )
        },
        qaLink4() {
            return urlTransformer(
                {
                    a1Ctyun: "https://www.ctyun.cn/document/10065985/10280663",
                    a1Ctcloud: this.qaLinkCtcloud,
                }
            )
        },
        qaLink5() {
            return urlTransformer(
                {
                    a1Ctyun: "https://www.ctyun.cn/document/10065985/10280664",
                    a1Ctcloud: this.qaLinkCtcloud,
                }
            )
        },
        // 边缘接入服务版本介绍
        docLink1() {
            return urlTransformer(
                {
                    a1Ctyun: "https://www.ctyun.cn/document/10065985/10280547",
                    a1Ctcloud: "https://www.esurfingcloud.com/document/zh-cn/20689737/20689748",
                }
            )
        },
        // 边缘接入计费模式
        docLink2() {
            return urlTransformer(
                {
                    a1Ctyun: "https://www.ctyun.cn/document/10065985/10325808",
                    a1Ctcloud: "https://www.esurfingcloud.com/document/zh-cn/20689737/20689767",
                }
            )
        },
        // 零基础使用边缘接入服务
        docLink3() {
            return urlTransformer(
                {
                    a1Ctyun: "https://www.ctyun.cn/document/10065985/10280584",
                    a1Ctcloud: "https://www.esurfingcloud.com/document/zh-cn/20689737/20690288",
                }
            )
        },
        // IP应用加速
        docLink4() {
            return urlTransformer(
                {
                    a1Ctyun: "https://www.ctyun.cn/document/10065985/10198555",
                    a1Ctcloud: "https://www.esurfingcloud.com/document/zh-cn/20689737/IP%E5%BA%94%E7%94%A8%E5%8A%A0%E9%80%9F",
                }
            )
        },
        // 最佳实践
        docLink5() {
            return urlTransformer(
                {
                    a1Ctyun: "https://www.ctyun.cn/document/10065985/%E6%9C%80%E4%BD%B3%E5%AE%9E%E8%B7%B5",
                    a1Ctcloud: "https://www.esurfingcloud.com/document/zh-cn/20689737/%E6%9C%80%E4%BD%B3%E5%AE%9E%E8%B7%B5",
                }
            )
        },
        // 常见文件 & 文档资源
        getQaAndDocList() {
            const map = {
                QA: [
                    {
                        url: this.qaLink1,
                        title: "开通IP应用加速前，要先订购边缘接入服务吗",
                        update_time: "2023-09-25",
                    },
                    {
                        url: this.qaLink2,
                        title: "除了IP应用加速，边缘接入服务还提供哪些能力",
                        update_time: "2023-09-25",
                    },
                    {
                        url: this.qaLink3,
                        title: "IP应用加速能够解决什么问题",
                        update_time: "2023-09-25",
                    },
                    {
                        url: this.qaLink4,
                        title: "IP应用加速支持海外加速吗",
                        update_time: "2023-09-25",
                    },
                    {
                        url: this.qaLink5,
                        title: "如何一键接入IP应用加速",
                        update_time: "2023-09-25",
                    },
                ],
                DOC: [
                    {
                        url: this.docLink1,
                        title: "边缘接入服务版本介绍",
                        update_time: "2023-09-25",
                    },
                    {
                        url: this.docLink2,
                        title: "边缘接入计费概述",
                        update_time: "2023-09-25",
                    },
                    {
                        url: this.docLink3,
                        title: "零基础使用边缘接入服务",
                        update_time: "2023-09-25",
                    },
                    {
                        url: this.docLink4,
                        title: "边缘接入服务介绍",
                        update_time: "2023-09-25",
                    },
                    {
                        url: this.docLink5,
                        title: "最佳实践",
                        update_time: "2023-09-25",
                    },
                ],
            };
            return map;
        },
        // 域名限制数量
        domainCountLimit() {
            return IpaConfigModule.ipaDomainSelectorLimit;
        }
    },
    watch: {
        "queryForm.timeRange": {
            handler() {
                this.getOverviewStat();
            },
        },
    },
    methods: {
        overviewMore() {
            const mainRouter = actions.actions?.routerInstance;
            const path = get(this.$router.resolve({ name: "eas.statistics" }), "route.path");
            mainRouter &&
                mainRouter.push({
                    path: path,
                });
        },
        packageInfoCheckMore() {
            const router = AppModule.baseAppRouter;
            if (!router) {
                return;
            }

            router.push({
                name: "billingDetail",
            });
        },
        async getPackageInfo() {
            const { result } = await this.$ctFetch(OverviewUrl.chargeMethod, {
                method: "GET",
                transferType: "json",
                data: {
                    account_id: this.$store.state.user.userInfo.userId,
                },
            });
            this.packageInfo = result;
        },
        customRenderWrapper() {
            this.$refs.overviewChart.getChartData();
            this.getOverviewStat();
        },
        // 获取域名列表
        async getDomainList() {
            const { result } = await this.$ctFetch(DomainUrl.domainListNew, {
                method: "GET",
                transferType: "json",
                data: {
                    account_id: this.$store.state.user.userInfo.userId,
                },
                cache: true,
            });
            this.domainOptions = result.map(item => {
                return {
                    label: item.domain,
                    value: item.domain,
                };
            });
        },
        async getDomainOrderCheck() {
            const resp = await this.$ctFetch(OverviewUrl.domainOrderCheck, {
                method: "GET",
                transferType: "json",
            });
            this.domainOrderStatus = resp.result.status;
            DomainModule.setDomainOrderStatus(resp.result.status);
        },
        getBillingMethodText(code) {
            let text = "";
            switch (code) {
                case 1:
                    text = "流量";
                    break;
                case 2:
                    text = "日峰值带宽";
                    break;
                case 3:
                    text = "月95带宽峰值";
                    break;
                case 4:
                    text = "月均日峰值带宽";
                    break;
                case 5:
                    text = "月带宽峰值";
                    break;
                case 6:
                    text = "日95带宽值月平均";
                    break;
                case 40:
                    text = "日95带宽值月平均";
                    break;
                case 41:
                    text = "日95带宽值月平均";
                    break;
            }
            return text;
        },
        /**获取概览页五个指标的数据 */
        async getOverviewStat() {
            const queryParams = {
                account_id: this.$store.state.user.userInfo.userId,
                domain: this.queryForm.domain,
            };
            if (this.queryForm.domain.length === this.domainOptions.length) delete queryParams.domain;

            if (get(queryParams.domain, "length") > this.domainCountLimit) {
                this.$message.error(`当前域名/实例超过${this.domainCountLimit}个，请先选择${this.domainCountLimit}个以内域名/实例`);
                return;
            }

            const timeRange = this.queryForm.timeRange;
            if (!timeRange || !timeRange.length) return;
            queryParams.start_time = Math.floor(timeRange[0].getTime() / 1000);
            queryParams.end_time = Math.floor(timeRange[1].getTime() / 1000);
            this.loading = true;
            const loadingIndex = this.loadingIndex++;
            const rest = await this.$ctFetch(OverviewUrl.statisticsStats, {
                method: "POST",
                transferType: "json",
                body: {
                    data: queryParams,
                },
            });
            if (loadingIndex !== this.loadingIndex - 1)
                return console.warn("get different loading index! stop rendering");
            this.loading = false;
            this.statsData = rest.result;
        },
        async getDomainCount() {
            const rest = await this.$ctFetch(OverviewUrl.domainCount, {
                method: "GET",
                transferType: "json",
            });
            this.domainCount = rest.result;
        },
        async getPortCount() {
            const portCount = await this.$ctFetch(OverviewUrl.portTotalCount, {
                method: "GET",
                transferType: "json",
                data: {
                    account_id: this.$store.state.user.userInfo.userId,
                },
            });
            this.portCount = portCount.result;
        },
        async getBillingMethod() {
            const billingMethod = await this.$ctFetch(OverviewUrl.billingMethod, {
                method: "GET",
                transferType: "json",
                data: {
                    account_id: this.$store.state.user.userInfo.userId,
                },
            });
            this.billingMethod = billingMethod.result.data;
        },
    },
    name: "overview",
};
</script>
<style lang="scss" scoped>
@mixin shared-flex {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    flex-shrink: 0;
}

.overview-section-wrapper {
    ::v-deep .el-scrollbar {
        margin-bottom: 0 !important;
    }
}
.overview-wrapper {
    width: 100%;
    display: flex;
    flex-wrap: nowrap;
    gap: 16px;
    @media (max-width: 1300px) {
        flex-wrap: wrap;
        &-left,
        &-right {
            flex-shrink: 0;
            min-width: 100%;
        }
    }
    &-left {
        width: calc(68% - 8px);
        .el-card {
            margin-bottom: 16px !important;
        }
        .search-form {
            ::v-deep .el-form-item {
                margin-bottom: 0 !important;
            }
            &-wrapper {
                @include shared-flex;
                ::v-deep .container {
                    @include shared-flex;
                    display: flex !important;
                }
                &-domain {
                    ::v-deep {
                        .domain-select__item {
                            line-height: 1.5 !important;
                        }
                        .el-scrollbar {
                            margin: 16px 0 !important;
                        }
                    }
                }
            }
        }
    }
    &-right {
        width: calc(32% - 8px);
        .el-card {
            margin-bottom: 16px !important;
            .package-info-wrapper {
                display: grid;
                grid-template: 1fr 1fr / 1fr 1fr;
                text-align: center;
                line-height: 1.5;
                .package-info-item {
                    height: 62px;
                    &-title {
                        padding-bottom: 8px;
                        font-size: 12px !important;
                        font-weight: bold;
                    }
                    &-content {
                        color: $color-neutral-10;
                        font-size: 12px !important;
                    }
                }
            }
        }
    }
    .data-overview-container {
        display: grid;
        grid-auto-flow: column;
        .overview-item-wrapper {
            &-icon {
                display: flex;
                justify-content: center;
                ::v-deep .svg-icon {
                    width: 58px !important;
                    height: 58px !important;
                }
                margin-bottom: 8px;
            }
            &-label {
                text-align: center;
                font-size: 14px;
                color: #333333;
                line-height: 18px;
                font-weight: 400;
                margin-bottom: 4px;
            }
            &-data {
                text-align: center;
                color: #333333;
                line-height: 40px;
                font-weight: 500;
                &-value {
                    font-size: 24px;
                }
                &-unit {
                    font-size: 18px;
                }
            }
        }
    }
    ::v-deep .el-tabs {
        margin-bottom: -32px;
    }
    ::v-deep .el-tabs__header {
        margin-top: 6px;
    }
    ::v-deep .el-divider {
        margin: 16px 0 24px 0 !important;
    }
}
.overview-section-wrapper {
    ::v-deep.el-scrollbar__wrap {
        margin-bottom: 0 !important;
    }
}
.qa-block-wrapper {
    ::v-deep .el-card__body {
        padding-top: 12px;
    }
    ::v-deep .block__title::before {
        content: none !important;
    }
    ::v-deep .block__title__extra {
        margin-top: 20px;
    }
}
.ct-time-picker-custom-wrapper {
    ::v-deep {
        .el-radio-button__orig-radio:checked + .el-radio-button__inner {
            color: #fff !important;
            background: $color-master !important;
        }
    }
}
</style>
