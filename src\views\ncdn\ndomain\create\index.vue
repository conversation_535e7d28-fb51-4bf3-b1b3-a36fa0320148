<template>
    <ct-section-wrap class="domain-create" :headerText="desc.title" :headerTip="desc.tip">
        <!-- <template #header>
            <ct-section-header :title="desc.title" :tip="desc.tip" />
        </template> -->

        <template slot="headerBtn">
            <el-button type="text" icon="el-icon-d-arrow-left" @click="handleTargetToDomainList">
                {{ $t("domain.domainList") }}
            </el-button>
        </template>

        <form-wrapper />
    </ct-section-wrap>
</template>

<script>
import FormWrapper from "./FormWrapper";

export default {
    components: { FormWrapper },
    data() {
        return {
            desc: {
                title:
                    this.$route.query.type === "batch"
                        ? this.$t("domain.create.title2")
                        : this.$t("domain.create.title"),
                // tip: this.$t("domain.create.titleTip"),
            },
        };
    },
    methods: {
        handleTargetToDomainList() {
            this.$router.push({
                name: "ndomain.list",
            });
        },
    },
    mounted() {
        if (this.$route.query.orderId) {
            this.desc.title = this.$t("domain.create.reTitle");
            this.desc.tip = this.$t("domain.create.reTip");
        }
    },
};
</script>
<style scoped lang="scss">
.domain-create {
    ::v-deep {
        .box.form-box {
            margin-bottom: 8px;
            padding: 20px;
        }
    }
    >::v-deep.el-scrollbar {
        margin-bottom: 0 !important;
    }
}

::v-deep .el-scrollbar {
    margin-bottom: 0 !important;
}

.domain-warning {
    margin: -2px 0;
    font-size: 20px;
    color: $color-warning;
    vertical-align: middle;
}

.special-form {
    ::v-deep {
        .box.form-box {
            padding: 0;
            box-shadow: none;

            .head {
                display: none;
            }
        }
    }
}
.ct-section-wrap {
    ::v-deep {
        .ct-section-header {
            align-items: baseline !important;
        }
    }
}
</style>
