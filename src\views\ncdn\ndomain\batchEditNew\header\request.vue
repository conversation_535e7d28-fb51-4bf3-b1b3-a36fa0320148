<template>
    <batch-edit-checked v-model="checked">
        <el-form
            class="request-header-form"
            label-width="140px"
            label-position="right"
            :model="form"
            :rules="rules"
            ref="formRef"
            :disabled="!checked"
        >
            <div>
                <el-form-item
                    :label="$t('domain.detail.label26')"
                    prop="req_headers"
                    ref="reqHeaders"
                    class="ct-table-form-item table-form-item-style"
                >
                    <span slot="label" class="req-header-style">
                        {{ $t("domain.detail.label26") }}
                        <span class="question-style">
                            <el-tooltip placement="top" :content="$t('domain.editPage.tip7')">
                                <ct-svg-icon icon-class="question-circle" class-name="question-circle"
                            /></el-tooltip>
                        </span>
                    </span>
                    <div class="ct-table-wrapper">
                        <el-table class="origin-table auto-table" :data="form.req_headers">
                            <el-table-column prop="key" :label="$t('domain.editPage.label15')">
                                <template #header>
                                    <div class="flex-row-style">
                                        <span>{{ $t("domain.editPage.label15") }}</span>
                                        <el-tooltip
                                            class="item"
                                            style="margin-left: 4px"
                                            effect="dark"
                                            :content="$t('domain.editPage.tooltip1')"
                                            placement="top"
                                        >
                                            <i class="el-icon-question" />
                                        </el-tooltip>
                                    </div>
                                </template>
                                <template #default="scope">
                                    <el-form-item
                                        label=""
                                        :prop="`req_headers.` + scope.$index + `.key`"
                                        :rules="rules.req_headers_key"
                                    >
                                        <el-tooltip
                                            placement="top"
                                            :content="$t('domain.editPage.placeholder3')"
                                        >
                                            <el-input
                                                v-model="scope.row.key"
                                                :placeholder="$t('domain.editPage.placeholder3')"
                                                class="input-style"
                                                @change="handleChange"
                                            ></el-input>
                                        </el-tooltip>
                                    </el-form-item>
                                </template>
                            </el-table-column>
                            <el-table-column prop="value" :label="$t('domain.detail.label44')">
                                <template #default="scope">
                                    <el-form-item
                                        label=""
                                        :prop="`req_headers.` + scope.$index + `.value`"
                                        :rules="rules.req_headers_value"
                                    >
                                        <el-tooltip
                                            placement="top"
                                            :content="$t('domain.editPage.placeholder5')"
                                        >
                                            <el-input
                                                v-model="scope.row.value"
                                                class="input-style"
                                                @change="handleChange"
                                                :placeholder="$t('domain.editPage.placeholder5')"
                                            ></el-input>
                                        </el-tooltip>
                                    </el-form-item>
                                </template>
                            </el-table-column>
                            <el-table-column :label="$t('domain.operate')" width="80">
                                <template #default="scope">
                                    <el-button
                                        type="text"
                                        @click="onOperator(scope.row, 'delete', 'req_headers', scope.$index)"
                                        >{{ $t("domain.delete") }}</el-button
                                    >
                                </template>
                            </el-table-column>
                        </el-table>
                        <div class="flex-row-style button-box">
                            <el-button
                                class="btn"
                                type="text"
                                @click="onOperator(null, 'create', 'req_headers')"
                            >
                                + {{ $t("domain.editPage.label10") }}
                            </el-button>
                        </div>
                    </div>
                </el-form-item>
            </div>
        </el-form>
    </batch-edit-checked>
</template>

<script lang="ts">
import { Component, Mixins } from "vue-property-decorator";
import ctSvgIcon from "@/components/ctSvgIcon/index.vue";
import i18n from "@/i18n/index";
import { cloneDeep } from "lodash-es";
import BatchItemMixin from "../mixins/batch.mixin";

interface RequestHeaderItem {
    key: string;
    value: string;
}

@Component({
    components: {
        ctSvgIcon,
    },
})
export default class BatchEditRequestHeader extends Mixins(BatchItemMixin) {
    checked = false;
    currentType = "create";
    form: {
        req_headers: RequestHeaderItem[];
    } = {
        req_headers: [], // 回源HTTP请求头
    };
    rules = {
        req_headers_key: [
            { required: true, message: i18n.t("domain.detail.tip76"), trigger: "blur" },
            {
                max: 300,
                message: i18n.t("domain.editPage.ruleTip2"),
                trigger: ["blur", "change"],
            },
            {
                pattern: "^[\\w-]+$",
                message: i18n.t("domain.editPage.ruleTip3"),
                trigger: ["blur", "change"],
            },
            {
                validator: (rule: any, value: any, callback: any) => {
                    if (value && value.toLowerCase() === "host")
                        callback(new Error(this.$t("domain.detail.tip77") as string));
                    else callback();
                },
                trigger: ["blur", "change"],
            },
        ],
        req_headers_value: [
            {
                max: 300,
                message: i18n.t("domain.editPage.ruleTip2"),
                trigger: ["blur", "change"],
            },
            {
                pattern:
                    "^[^\\u4e00-\\u9fa5\\u3002\\uff1f\\uff01\\uff0c\\u3001\\uff1b\\uff1a\\u201c\\u201d\\u2018\\u2019\\uff08\\uff09\\u300a\\u300b\\u3008\\u3009\\u3010\\u3011\\u300e\\u300f\\u300c\\u300d\\ufe43\\ufe44\\u3014\\u3015\\u2026\\u2014\\uff5e\\ufe4f\\uffe5]+$",
                message: i18n.t("domain.detail.tip29"),
                trigger: ["blur", "change"],
            },
        ],
    };

    handleChange() {
        this.$emit("onChange", this.form.req_headers);
    }
    async onOperator(
        row: RequestHeaderItem | null,
        currentType: "create" | "delete",
        tabName: "req_headers",
        i = 0
    ) {
        this.currentType = currentType;

        if (currentType === "create") {
            const defaultFormMap = {
                req_headers: { key: "", value: "" },
            };
            row = defaultFormMap[tabName];
        }
        if (currentType === "delete") {
            const msgMap = {
                req_headers: this.$t("domain.editPage.tip20") as string,
            };

            // 二次确认弹窗
            await this.$confirm(msgMap[tabName], this.$t("domain.delete") as string, {
                type: "warning",
            });
            this.form[tabName].splice(i, 1);

            this.$emit("onChange", this.form.req_headers);
        } else {
            this.form[tabName].push(row as never);
        }
    }
    get formData() {
        return cloneDeep(this.form);
    }
}
</script>

<style lang="scss" scoped>
@import "../commonStyle.scss";

.request-header-form {
    width: 100%;
}
</style>
