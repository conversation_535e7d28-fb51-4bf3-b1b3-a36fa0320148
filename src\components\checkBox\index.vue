<!--这个组件为了适应v-model绑定为函数时报错，改用：value的形式；原生的elementUI必须用v-model，否则change事件触发的参数永远不会变化-->
<template>
    <el-checkbox-group v-model="valueInside" :disabled="disabled" @change="handleChange">
        <el-checkbox
            v-for="(checkItem, checkIndex) in arrShow"
            :key="checkIndex"
            :label="checkItem.value"
            :disabled="checkItem.disabled"
        >
            {{ checkItem.label }}
        </el-checkbox>
    </el-checkbox-group>
</template>

<script>
import { last } from "lodash-es";

export default {
    props: {
        value: {
            type: Array,
            required: false,
            default: () => [],
        },
        aryList: {
            type: Array,
            required: false,
            default: () => [],
        },
        alternative: {
            type: [String, Array],
            default: "",
        },
        disabled: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            valueInside: [],
        };
    },
    computed: {
        arrShow() {
            if (!this.alternative) {
                return this.aryList;
            }

            const current = last(this.valueInside);
            if (!current) {
                return this.aryList;
            }

            let flag = false;
            if (this.alternative instanceof Array) {
                flag = this.alternative.includes(current);
            } else {
                flag = current === this.alternative;
            }

            const ary = this.aryList.map(item => {
                if (flag) {
                    return {
                        ...item,
                        disabled:
                            this.alternative instanceof Array
                                ? !this.alternative.includes(item.value)
                                : !(this.alternative === item.value),
                    };
                }

                return {
                    ...item,
                    disabled:
                        this.alternative instanceof Array
                            ? this.alternative.includes(item.value)
                            : this.alternative === item.value,
                };
            });
            return ary;
        },
    },
    watch: {
        value: {
            handler(val) {
                this.valueInside = val;
            },
            immediate: true,
        },
    },
    methods: {
        handleChange(val) {
            this.$emit("change", val);
        },
    },
};
</script>

<style scoped></style>
