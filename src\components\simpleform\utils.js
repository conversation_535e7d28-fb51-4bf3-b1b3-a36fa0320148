// 去抖动方法
function debounce(func, wait) {
  let timerId = null;
  return function(...args) {
      window.clearTimeout(timerId);
      timerId = setTimeout(() => {
          func.apply(this, args);
      }, wait);
  };
}
function isObject(obj) {
  return Object.prototype.toString.call(obj).includes("Object");
}
function equalObject(obj1, obj2) {
  if (isObject(obj1) && isObject(obj2)) {
      const keys1 = Object.keys(obj1);
      const keys2 = Object.keys(obj2);
      return keys1.length === keys2.length && keys1.every(key => equalObject(obj1[key], obj2[key]));
  } else if (Array.isArray(obj1) && Array.isArray(obj2)) {
      return obj1.length === obj2.length && obj1.every((item, index) => equalObject(item, obj2[index]));
  }
  return obj1 === obj2;
}

export default {
  debounce,
  equalObject,
};
