<template>
    <div class="domain-select el-select" v-clickoutside="handleClose">
        <div class="domain-select__tags">
            <el-tag v-if="selectedCount" size="mini" type="info"> {{ $tc("common.domainSelect.labelSlectTagsText1", selectedCount) }} </el-tag>
            <el-tag
                v-if="!multiple && selectedDomainLocal"
                size="mini"
                type="info"
                closable
                @close="(selectedDomainLocal = ''), $emit('select', '')"
            >
                <span class="domain-select__tag-content" :title="selectedDomainLocal">
                    {{ selectedDomainLocal }}
                </span>
            </el-tag>
        </div>
        <div
            class="domain-select__input el-input"
            :class="{ 'is-focus': visible }"
            @click.stop="toggleDropdown"
            ref="reference"
        >
            <input
                ref="input"
                type="text"
                class="el-input__inner"
                :placeholder="selectedCount || (!multiple && selectedDomainLocal) ? '' : getPlaceholder"
                readonly="readonly"
                @focus="handleFocus"
                @keydown.esc.stop.prevent="visible = false"
                @keydown.tab="visible = false"
            />
            <i :class="['el-icon-arrow-up', { 'is-reverse': visible }]"></i>
        </div>
        <transition name="el-zoom-in-top">
            <div v-show="visible" class="domain-select__dropdown el-popper" ref="popper">
                <div class="domain-select__dropdown-wrapper">
                    <div class="domain-select__search-bar">
                        <el-checkbox v-if="multiple" v-model="isSelectAll" @change="toggleSelectAll" />
                        <el-input
                            :style="{ width: multiple ? '90%' : '100%' }"
                            suffix-icon="el-icon-search"
                            v-model="keywords"
                            size="small"
                        />
                    </div>
                    <el-scrollbar
                        wrap-class="el-select-dropdown__wrap"
                        view-class="el-select-dropdown__list"
                        class="domain-wrap"
                        v-show="filterDomainList.length > 0 && !loading"
                    >
                        <div
                            class="domain-select__item"
                            v-for="domain in filterDomainList"
                            :key="domain.value"
                        >
                            <el-checkbox
                                v-if="multiple"
                                :value="selectedDomainLocal[domain.value]"
                                @change="handleDomainChange(domain.value, $event)"
                                :label="domain.label"
                            />
                            <el-radio
                                v-else
                                v-model="selectedDomainLocal"
                                :label="domain.label"
                                @change="$emit('select', selectedDomainLocal)"
                            />
                        </div>
                    </el-scrollbar>
                    <!-- loading -->
                    <div
                        class="aocdn-ignore-loading-mask-wrapper"
                        v-show="loading"
                        v-loading="loading"
                        :element-loading-text="$t('common.loading')"
                    ></div>
                </div>
            </div>
        </transition>
    </div>
</template>
<script lang="ts">
import { Component, Prop, Watch } from "vue-property-decorator";
import { mixins } from "vue-class-component";
// 引入element封装的popper工具
import Popper from "element-ui/lib/utils/vue-popper";
import Clickoutside from "element-ui/lib/utils/clickoutside";

type DomainOption = {
    value: string;
    label: string;
};
type BooleanObject = {
    [key: string]: boolean;
};

// 域名选择
@Component({
    directives: { Clickoutside },
    model: {
        prop: "selectedDomain",
        event: "select",
    },
})
export default class DomainSelect extends mixins(Popper) {
    @Prop({ default: false }) private appendToBody!: boolean;
    @Prop({ default: () => [] }) private domainOptions!: DomainOption[];
    @Prop({ default: false }) private multiple!: boolean;
    @Prop({ default: "" }) private placeholder!: string;
    @Prop({ default: "domain" }) private domainType!: string;
    @Prop({
        // vue-property-decorator bug，指定this消除报错
        default(this: DomainSelect) {
            return this.multiple ? [] : "";
        },
    })
    private selectedDomain!: string | string[];
    @Prop({ default: false }) private loading!: boolean;

    private isSelectAll = false;
    private filterDomainList: any[] = [];
    private keywords = "";
    private selectedDomainLocal: BooleanObject | string = this.multiple ? {} : "";
    private visible = false;

    private locked = false;
    private referenceElm?: Element;
    private popperElm?: Element;

    get domainList() {
        const filterList: DomainOption[] = [];
        const existed: BooleanObject = {};
        // 域名去重
        this.domainOptions.forEach(domainObj => {
            if (!existed[domainObj.value]) {
                existed[domainObj.value] = true;
                filterList.push(domainObj);
            }
        });
        return filterList;
    }
    get selectedCount() {
        if (!this.multiple) return undefined;
        let count = 0;
        Object.keys(this.selectedDomainLocal).forEach(
            key => (this.selectedDomainLocal as BooleanObject)[key] && count++
        );
        return count;
    }

    get getPlaceholder() {
        return this.placeholder || this.$t("common.domainSelect.placeholder");
    }

    private mounted() {
        this.referenceElm = (this.$refs.reference as Vue).$el;
        this.popperElm = (this.$refs.popper as Vue).$el;
        if (this.multiple) {
            (this.selectedDomain as string[]).forEach(
                domain => ((this.selectedDomainLocal as BooleanObject)[domain] = true)
            );
        } else {
            this.selectedDomainLocal = this.selectedDomain as string;
        }
        this.filterDomainList.sort(this.sortMethod); // 初始化时也需要sort一次
        this.createPopper();
        this.$on("updatePopper", () => {
            if (this.visible) this.updatePopper();
        });
        this.$on("destroyPopper", this.destroyPopper);
    }
    private handleDomainChange(domain: string, val: boolean) {
        (this.selectedDomainLocal as BooleanObject)[domain] = val;
        this.isSelectAll = !this.filterDomainList.some(
            domainObj => !(this.selectedDomainLocal as BooleanObject)[domainObj.value]
        );
        const selectedDomain: string[] = [];
        Object.keys(this.selectedDomainLocal).forEach(domain => {
            if ((this.selectedDomainLocal as BooleanObject)[domain]) selectedDomain.push(domain);
        });
        this.$emit("select", selectedDomain);
    }
    private handleClose() {
        this.visible = false;
    }
    private handleFocus() {
        // 点击下拉按钮会先触发focus事件再触发click事件，所以要加个锁防止下拉框消失，同时要清除锁防止关闭不了
        if (!this.visible) {
            this.locked = true;
            this.visible = true;
            setTimeout(() => {
                this.locked = false;
            }, 300);
        }
    }
    private toggleDropdown() {
        if (this.locked) {
            this.locked = false;
        } else {
            this.visible = !this.visible;
        }
    }
    private toggleSelectAll(val: boolean) {
        if (val) {
            this.filterDomainList.forEach(domainObj => {
                (this.selectedDomainLocal as BooleanObject)[domainObj.value] = true;
            });
        } else {
            this.filterDomainList.forEach(domainObj => {
                (this.selectedDomainLocal as BooleanObject)[domainObj.value] = false;
            });
        }
        const selectedDomain: string[] = [];
        Object.keys(this.selectedDomainLocal).forEach(domain => {
            if ((this.selectedDomainLocal as BooleanObject)[domain]) selectedDomain.push(domain);
        });
        this.$emit("select", selectedDomain);
    }
    private sortMethod(a: DomainOption, b: DomainOption) {
        return this.multiple
            ? Number(!!(this.selectedDomainLocal as BooleanObject)[b.value]) -
                  Number(!!(this.selectedDomainLocal as BooleanObject)[a.value])
            : Number((this.selectedDomainLocal as string) === b.value) -
                  Number((this.selectedDomainLocal as string) === a.value);
    }
    @Watch("domainList", { immediate: true })
    private onDomainListChange(list: DomainOption[], oldVal: any) {
        if (this.multiple) {
            this.selectedDomainLocal = {};
            list.forEach(domain => {
                // 设置是否选择该域名的标志位，防止绑定不到
                this.$set(this.selectedDomainLocal as BooleanObject, domain.value, false);
            });
            if (oldVal) this.$emit("select", []); // 更新外部值为空，第一次有域名时不清空初始值
        } else {
            this.selectedDomainLocal = "";
            if (oldVal) this.$emit("select", ""); // 更新外部值为空，第一次有域名时不清空初始值
        }
        this.filterDomainList = (this.keywords
            ? this.domainList.filter(this.keywordsFilter)
            : this.domainList
        ).sort(this.sortMethod);
    }
    @Watch("filterDomainList")
    private onFilterDomainListChange(val: DomainOption[]) {
        this.isSelectAll =
            val.length > 0
                ? !val.some(domainObj => !(this.selectedDomainLocal as BooleanObject)[domainObj.value])
                : false;
    }
    @Watch("keywords")
    private onKeywordChange() {
        this.filterDomainList = this.domainList.filter(this.keywordsFilter).sort(this.sortMethod);
    }

    // 域名关键字过滤方法
    private keywordsFilter(domain: DomainOption) {
        const { keywords } = this;
        if (!keywords) return true;
        if (this.domainType !== "inst_name") {
            return keywords
                .replaceAll("，", ",")
                .split(",")
                .some(keyword => domain.value.includes(keyword));
        } else {
            return keywords
                .replaceAll("，", ",")
                .split(",")
                .some(keyword => domain.label.includes(keyword));
        }

        // return keywords
        //     .replaceAll("，", ",")
        //     .split(",")
        //     .some(keyword => domain.value.includes(keyword));
    }

    @Watch("visible")
    private onVisibleChange(val: boolean) {
        if (val) {
            // 异步更新popper位置，避免popper位置不准确，导致zoom动画不流畅
            setTimeout(() => {
                this.updatePopper();
            });
        } else {
            this.filterDomainList.sort(this.sortMethod);
            (this.$refs.input as HTMLInputElement).blur(); // 关闭时清除边框
            this.$emit("blur");
        }
    }
    @Watch("selectedDomain")
    private onSelectedDomainChange(val: string[] | string) {
        if (this.multiple) {
            Object.keys(this.selectedDomainLocal).forEach(domain => {
                (this.selectedDomainLocal as BooleanObject)[domain] = false;
            });
            (val as string[]).forEach(domain => {
                (this.selectedDomainLocal as BooleanObject)[domain] = true;
            });

            // 计算当前是否全选
            this.isSelectAll = this.domainList.every(
                domainObj => (this.selectedDomainLocal as BooleanObject)[domainObj.value]
            );
        } else {
            this.selectedDomainLocal = val as string;
        }
    }
}
</script>
<style lang="scss" scoped>
.domain-select {
    color: #999;
    display: inline-block;
    position: relative;
    width: 215px;
    cursor: pointer;
}
.domain-select__tags {
    width: auto; // 避免主应用样式覆盖
    height: auto; // 避免主应用样式覆盖
    position: absolute;
    z-index: 100;
    // line-height: 32px;
    max-width: calc(100% - 60px);
    top: 50%;
    transform: translate(0, -50%);
    .el-tag {
        width: 100%;
    }
    .domain-select__tag-content {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        vertical-align: middle;
        width: calc(100% - 18px);
        display: inline-block;
    }
}
.domain-select__input {
    position: relative;
    width: 100%;
    cursor: pointer;
    // input {
    //     outline: none;
    //     cursor: pointer;
    //     width: 100%;
    //     padding: 0 30px 0 15px;
    //     height: 32px;
    //     line-height: 32px;
    //     box-sizing: border-box;
    //     border: 1px solid #dcdfe6;
    //     &.is-focus {
    //         border: 1px solid #fa8334;
    //     }
    //     &::placeholder {
    //         color: #c0c4cc;
    //         font-size: 13px;
    //         line-height: 30px;
    //     }
    // }
}
.el-icon-arrow-up {
    pointer-events: none;
    cursor: pointer;
    text-align: center;
    position: absolute;
    width: 25px;
    right: 5px;
    transform: rotateZ(180deg);
    transition: transform 0.3s;
    line-height: 32px;
    &.is-reverse {
        transform: rotateZ(0deg);
    }
}
.domain-select__search-bar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    ::v-deep .el-input .el-input__validateIcon {
        display: none !important;
    }
}
.domain-select__dropdown {
    // position: absolute;
    position: fixed !important;
    z-index: 1001;
    border: solid 1px #e4e7ed;
    border-radius: 3px;
    background-color: #ffffff;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    box-sizing: border-box;
}
.domain-select__dropdown-wrapper {
    padding: 16px;
}
.domain-select__item {
    margin: 8px 0;
}
.domain-wrap {
    ::v-deep .el-scrollbar__wrap {
        .el-scrollbar__view {
            height: 100%;
            padding: 0 0 20px 0 !important;
        }
        overflow-x: hidden;
    }
}
</style>
<style lang="scss">
.aocdn-ignore-loading-mask-wrapper {
    height: 120px;
    .el-loading-mask > .el-loading-spinner {
        top: 30%;
        margin-top: 0px;

        > svg {
            max-height: 24px !important;
        }
    }
}
</style>
