<template>
    <div
        class="wrapper-show"
        :class="{
            'wrapper-show-no-full': data.tip && data.tipDirection === 'row' && data.inputStyle,
            'width-auto': data.widthAuto,
        }"
    >
        <!-- 普通input框-->
        <template v-if="data.type === 'input'">
            <el-input
                :value="get(form, data.prop)"
                :style="data.inputStyle"
                :disabled="isDisabled"
                :placeholder="placeholder || '请输入'"
                :maxlength="data.maxlength"
                @input="handleCommonChange($event, data)"
            />
        </template>
        <!-- 普通textarea框-->
        <el-input
            v-if="data.type === 'textarea'"
            :value="get(form, data.prop)"
            type="textarea"
            :placeholder="placeholder || '请输入'"
            :disabled="isDisabled"
            @input="handleCommonChange($event, data)"
        />
        <!-- 普通radio单选框-->
        <template v-if="data.type === 'radio'">
            <radio-group
                :value="get(form, data.prop)"
                :ary-list="listAry"
                :style="data.inputStyle"
                @change="handleCommonChange($event, data)"
            />
        </template>
        <!-- 普通复选框-->
        <!-- v-if条件多了一个判断，初始化的时候如果为空，checkbox会报错-->
        <template v-if="data.type === 'checkBox' && get(form, data.prop, [])">
            <check-box
                :value="get(form, data.prop)"
                :ary-list="listAry"
                :disabled="isDisabled"
                :alternative="get(data, 'alternative')"
                @change="handleCommonChange($event, data)"
            />
        </template>
        <!-- 普通单选checkbox框，值为true或false-->
        <ct-check-radio
            v-if="data.type === 'checkRadio'"
            v-model="form[data.prop]"
            :data="data"
            :label="data.radioLabel"
            :true-label="data.trueLabel"
            :false-label="data.falseLabel"
            :before-change="data.beforeChange"
            @handleChange="handleCommonChange($event, data)"
        />
        <!-- 普通选择框-->
        <el-select
            v-if="data.type === 'select'"
            :value="get(form, data.prop)"
            :style="data.inputStyle"
            :disabled="isDisabled"
            v-bind="data.config"
            placeholder="请选择"
            @change="handleCommonChange($event, data)"
        >
            <el-option
                v-for="optionItem in listAry"
                :key="optionItem.value"
                :label="optionItem.label"
                :value="optionItem.value"
                :disabled="optionItem.disabled"
            />
        </el-select>
        <!-- ct选择框-->
        <ct-select
            v-if="data.type === 'ctSelect'"
            :value="get(form, data.prop)"
            :disabled="isDisabled"
            :data="listAry"
            v-bind="data.config"
            @change="handleCommonChange($event, data)"
        />
        <!-- 普通switch框-->
        <el-switch
            v-if="data.type === 'switch'"
            :value="get(form, data.prop)"
            active-color="rgb(19, 206, 102)"
            inactive-color="#c7d2df"
            :active-value="data.activeValue || 'ON'"
            :inactive-value="data.inactiveValue || 'CLOSE'"
            v-bind="data.config"
            :disabled="isDisabled"
            class="ct-switch"
            @change="handleCommonChange($event, data)"
        />
        <!-- ctSwitch框-->
        <ct-switch
            v-if="data.type === 'ctSwitch'"
            v-model="form[data.prop]"
            :before-change-data="{ data: data, form: form }"
            :loading="data.loading"
            :before-change="data.beforeChange"
            :disabled="isDisabled"
            :active-value="data.activeValue"
            :inactive-value="data.inactiveValue"
            @handleChange="handleCommonChange($event, data)"
        />
        <!-- 时间计算器-->
        <time-calculate
            v-if="data.type === 'timeCalculate'"
            :value="get(form, data.prop)"
            @change="handleCommonChange($event, data)"
        />
        <!-- 普通日期控件-->
        <el-date-picker
            v-if="data.type === 'date'"
            v-model="form[data.prop]"
            :placeholder="placeholder || '请选择'"
            v-bind="data.config"
        />
        <!-- 提示语句-->
        <div v-if="data.type === 'tip'" class="tip-div" :class="data.tipClass">
            {{ data.tips }}
        </div>
        <!--  toolTip渲染-->
        <el-tooltip v-if="data.toolTips" effect="dark" placement="top" :content="data.toolTips">
            <i class="el-icon-warning" />
        </el-tooltip>
    </div>
</template>

<script>
import { mapGetters } from "vuex";
import { get, set, sortBy } from "lodash-es";
import timeCalculate from "@/components/timeCalculate/index.vue";
import checkBox from "@/components/checkBox/index.vue";
import radioGroup from "@/components/radio/index.vue";
import ctSelect from "@/components/ctSelect";
import ctSwitch from "@/components/ctSwitch";
import ctCheckRadio from "@/components/ctCheckRadio";
import Emitter from "@/utils/emitter";

export default {
    components: {
        timeCalculate,
        checkBox,
        radioGroup,
        ctSelect,
        ctSwitch,
        ctCheckRadio,
    },
    mixins: [Emitter],
    props: {
        form: {
            type: Object,
            required: false,
            default: () => {
                return {};
            },
        },
        data: {
            type: Object,
            required: false,
            default: () => {
                return {};
            },
        },
        prop: {
            type: String,
            required: false,
            default: "",
        },
    },
    computed: {
        ...mapGetters({
            optionsMap: "optionsMap",
        }),
        /**
         * 列表数据展示
         */
        listAry() {
            const item = this.data;
            let ary = null;
            if (item.list) {
                ary = item.list;
            } else if (item.listFilter && typeof item.listFilter === "function") {
                ary = item.listFilter(item);
            } else if (item.listProp) {
                ary = get(this.optionsMap, item.listProp, []);
            } else {
                ary = get(this.optionsMap, item.prop, []);
            }

            if (
                item.listFilterValue &&
                item.listFilterValue instanceof Array &&
                item.listFilterValue.length
            ) {
                const res = ary.map(filterItem => {
                    return {
                        label: filterItem.label,
                        value: filterItem.value,
                        disabled: !item.listFilterValue.includes(filterItem.value),
                    };
                });
                return sortBy(res, "disabled");
            }
            return ary;
        },
        /**
         * 是否禁用
         */
        isDisabled() {
            const item = this.data;
            // eslint-disable-next-line no-prototype-builtins
            if (item.hasOwnProperty("disabled") && typeof item.disabled === "function") {
                return item.disabled(this.form);
            }

            return item.disabled;
        },
        /**
         * 提示语
         */
        placeholder() {
            const placeholder = this.data.placeholder;
            if (placeholder && typeof placeholder === "function") {
                return placeholder(this.form);
            }

            return placeholder;
        },
    },
    methods: {
        /**
         * 处理变化
         */
        handleCommonChange(val, item) {
            // 因为部分表单设计多层嵌套，不能直接使用v-model形式
            const prop = item.prop;
            set(this.form, prop, val);
            // 因为这边并非双向绑定，存在一定的延迟，对于像radio/checkbox等组件；会存在validator中value延迟回调，导致验证不通过
            // 所以在赋值后进行手动调用一次通知更新
            this.dispatch("ElFormItem", "el.form.change", val);
            if (item.event && typeof item.event === "function") {
                item.event({
                    val: val,
                    item: item,
                    form: this.form,
                    prop: this.prop,
                });
            }
        },
        get,
    },
};
</script>

<style scoped lang="scss">
.wrapper-show {
    width: 100%;
    flex: 1;
    &.wrapper-show-no-full {
        flex: unset;
        width: unset;
    }
    &.width-auto {
        flex: unset;
        width: unset;
        display: inline-block;
    }
    .el-select {
        width: 100%;
        /*flex: 1;*/
    }
    .el-cascader {
        width: 100%;
    }

    .el-icon-warning {
        font-size: 16px;
        margin-left: 10px;
    }
}
</style>
