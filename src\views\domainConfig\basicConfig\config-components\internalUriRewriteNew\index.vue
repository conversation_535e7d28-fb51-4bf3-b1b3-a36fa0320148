<template>
    <div class="ct-edit-wrapper">
        <el-form
            label-width="140px"
            label-position="left"
            :model="form"
            :rules="rules"
            ref="internalUriRewriteNewForm"
            :disabled="!isEdit || !isService || isLockOriginUrlRewrite"
        >
            <div v-if="isShowUrlRewrite">
              <el-form-item
                :label="$t('domain.editPage.label8')"
                prop="internal_uri_rewrite_new"
                ref="internal_uri_rewrite_new"
                class="ct-table-form-item"
                ><span slot="label"
                    >{{ $t("domain.editPage.label8") }}
                    <span>
                        <el-tooltip placement="top">
                            <div slot="content">
                                {{ $t("domain.detail.tip65") }}
                                <a
                                    :underline="false"
                                    class="word-wrap aocdn-ignore-link"
                                    style="color:#3d73f5"
                                    @click="$docHelp(origin_uri_url)"
                                    >{{ $t("domain.detail.tip23") }}
                                </a>
                            </div>
                            <ct-svg-icon
                                icon-class="question-circle"
                                class-name="ct-sort-drag-icon"
                            ></ct-svg-icon></el-tooltip></span
                ></span>
                <div class="ct-table-wrapper">
                    <el-table
                        class="origin-table auto-table"
                        :data="form.internal_uri_rewrite_new"
                    >
                        <!-- 改写模式-->
                        <el-table-column prop="rewrite_mode">
                            <template slot="header">
                                <div class="flex-row-style">
                                    <span> {{ $t("domain.detail.rewriteMode") }}</span>
                                    <el-tooltip
                                        class="item"
                                        style="margin-left:4px"
                                        effect="dark"
                                        :content="$t('domain.detail.rewriteModeTip')"
                                        placement="top"
                                    >
                                        <i class="el-icon-question" />
                                    </el-tooltip>
                                </div>

                            </template>
                            <template slot-scope="{ row, $index }">
                                <el-form-item :prop="`internal_uri_rewrite_new.${$index}.rewrite_mode`">
                                    <el-select v-model="row.rewrite_mode" :placeholder="$t('domain.detail.rewriteModeList.2')" @change="handleChange">
                                        <el-option :label="$t('domain.detail.rewriteModeList.0')" value="decode" />
                                        <el-option :label="$t('domain.detail.rewriteModeList.1')" value="encode" />
                                    </el-select>
                                </el-form-item>
                            </template>
                        </el-table-column>
                        <!-- 待改写PATH-->
                        <el-table-column
                            prop="pattern"
                            :label="$t('domain.detail.label49')"
                            >
                            <template slot-scope="scope">
                                <el-form-item
                                    label=""
                                    :prop="
                                        `internal_uri_rewrite_new.` +
                                            scope.$index +
                                            `.pattern`
                                    "
                                    :rules="rules.pattern"
                                >
                                    <el-tooltip placement="top" :content="$t('domain.editPage.placeholder1')">
                                        <el-input
                                            v-model="scope.row.pattern"
                                            :placeholder="$t('domain.editPage.placeholder1')"
                                            class="input-style"
                                            @change="handleChange"
                                        ></el-input>
                                    </el-tooltip>
                                </el-form-item>
                            </template>
                        </el-table-column>
                        <el-table-column
                            prop="replace"
                            :label="$t('domain.editPage.label9')"
                        >
                            <template slot="header">
                                <div class="flex-row-style">
                                    <span>{{ $t("domain.editPage.label9") }}</span>
                                    <el-tooltip
                                        class="item"
                                        style="margin-left:4px"
                                        effect="dark"
                                        :content="$t('domain.detail.tip25')"
                                        placement="top"
                                    >
                                        <i class="el-icon-question" />
                                    </el-tooltip>
                                </div>
                            </template>
                            <template slot-scope="scope">
                                <el-form-item
                                    label=""
                                    :prop="
                                        `internal_uri_rewrite_new.` +
                                            scope.$index +
                                            `.replace`
                                    "
                                    :rules="rules.replace"
                                >
                                    <el-input
                                        v-model="scope.row.replace"
                                        class="input-style"
                                        @change="handleChange"
                                    ></el-input>
                                </el-form-item>
                            </template>
                        </el-table-column>
                        <el-table-column :label="$t('domain.operate')" width="80">
                            <template slot-scope="scope">
                                <el-button
                                    type="text"
                                    @click="
                                        onOperator(
                                            scope.row,
                                            'delete',
                                            'internal_uri_rewrite_new',
                                            scope.$index
                                        )
                                    "
                                    >{{ $t("domain.delete") }}</el-button
                                >
                            </template>
                        </el-table-column>
                    </el-table>
                    <div class="flex-row-style button-box">
                        <el-button
                            class="btn"
                            type="text"
                            @click="
                                onOperator(null, 'create', 'internal_uri_rewrite_new')
                            "
                        >
                            + {{ addButtonText }}
                        </el-button>
                    </div>
                </div>
              </el-form-item>
            </div>

        </el-form>
    </div>
</template>

<script>
import ctSvgIcon from "@/components/ctSvgIcon";
import componentMixin from "@/views/domainConfig/componentMixin";
import i18n from "@/i18n/index";
import { cloneDeep } from "lodash-es";
import { nUserModule } from '@/store/modules/nuser';
import urlTransformer from "@/utils/logic/url";

export default {
    name: "internalUriRewriteNew",
    components: {
        ctSvgIcon,
    },
    mixins: [componentMixin],
    props: {
        datas: Object,
        temp_domain_detail: Object,
        isLockOriginUrlRewrite: Boolean,
    },
    data() {
      return {
        addButtonText: i18n.t("domain.editPage.label10"),
        currentType: "create",
        form: {
            internal_uri_rewrite_new: [], // 回源HTTP请求头
        },
        temp_product_code_list: ["001", "003", "004", "005", "006", "008", "014"],
        rules: {
          pattern: [
            // { required: true, message: i18n.t("domain.detail.tip79-1"), trigger: "blur" },
            // {
            //     pattern: "^[\\S-]+$",
            //     message: i18n.t("domain.detail.tip78"),
            //     trigger: ["blur", "change"],
            // },
            {
                validator: (rule, value, callback) => {
                    if (this.isLockOriginUrlRewrite) callback();
                    const reg = /^[\S-]+$/;
                    if (value === "" || value === null || value === undefined)
                        callback(new Error(this.$t("domain.detail.tip79-1")));
                    else if (!reg.test(value))
                        callback(new Error(this.$t("domain.detail.tip78")));
                    else callback();

                },
                trigger: ["blur", "change"],
            },
          ],
          replace: [
            // { required: true, message: i18n.t("domain.detail.tip80-1"), trigger: "blur" },
            // {
            //     pattern: "^[\\S-]+$",
            //     message: i18n.t("domain.detail.tip78"),
            //     trigger: ["blur", "change"],
            // },
            {
                validator: (rule, value, callback) => {
                    if (this.isLockOriginUrlRewrite) callback();
                    const reg = /^[\S-]+$/;
                    if (value === "" || value === null || value === undefined)
                        callback(new Error(this.$t("domain.detail.tip80-1")));
                    else if (!reg.test(value))
                        callback(new Error(this.$t("domain.detail.tip78")));
                    else callback();

                },
                trigger: ["blur", "change"],
            },
          ],
        },
      };
    },
    computed: {
        origin_uri_url() {
            return urlTransformer(
                {
                    fcdnCtcloud: "https://www.esurfingcloud.com/document/zh-cn/10015932/20686451",
                    fcdnCtyun: "https://www.ctyun.cn/document/10015932/10638040",
                    a1Ctyun: "https://www.ctyun.cn/document/10065985/10192893",
                    a1Ctcloud: "https://www.esurfingcloud.com/document/zh-cn/20689737/20689825"
                }
            )
        },
        isShowUrlRewrite() {
            return (!window.__POWERED_BY_QIANKUN__ &&
                    this.temp_domain_detail.use_ecgw === 1 &&
                    this.temp_product_code_list.includes(this.temp_domain_detail.product_code)) ||
                    window.__POWERED_BY_QIANKUN__
        },
    },
    watch: {
        "datas.internal_uri_rewrite_new": {
            deep: true,
            handler(val) {
                this.init((Array.isArray(val) ? val : []).map(item => ({
                    ...item,
                    rewrite_mode: item.rewrite_mode || "decode",
                })));
            },
            immediate: true,
        },
    },
    methods: {
      init(v) {
          this.form.internal_uri_rewrite_new = cloneDeep(v);
      },
      handleChange() {
        this.$emit("onChange", this.form.internal_uri_rewrite_new);
      },
      async onOperator(row, currentType, tabName, i) {
        this.currentType = currentType;
        const getTime = new Date().getTime();

        if (currentType === "create") {
            const defaultFormMap = {
                internal_uri_rewrite_new: { pattern: "", replace: "", id: getTime + "", rewrite_mode: "decode" },
            };
            row = defaultFormMap[tabName];
        }
        if (currentType === "delete") {
            const msgMap = {
                internal_uri_rewrite_new: this.$t("domain.editPage.tip20"),
            };

            // 二次确认弹窗
            await this.$confirm(msgMap[tabName], this.$t("domain.delete"), {
                type: "warning",
            });
            this.form[tabName].splice(i, 1);

            this.$emit("onChange", this.form.internal_uri_rewrite_new);
        } else {
            this.form[tabName].push(row);
        }
      },
    },
};
</script>

<style lang="scss" scoped>
@import "@/views/domainConfig/commonStyle.scss";
.ct-edit-wrapper {
    ::v-deep {
        .el-form--label-left .el-form-item__label {
            text-align: right;
        }
    }
}
</style>
