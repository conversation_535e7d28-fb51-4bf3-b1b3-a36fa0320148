<template>
    <ct-section-wrap :headerText="$t('refresh.headerText')" :headerTip="$t('refresh.headerTip')">
        <ct-box class="table-scroll-wrap">
            <el-tabs v-model="type">
                <el-tab-pane :label="$t('refresh.common.tab[0]')" name="1" />
                <el-tab-pane :label="$t('refresh.common.tab[1]')" name="2" />
                <el-tab-pane :label="$t('refresh.common.tab[2]')" name="4" />
                <el-tab-pane :label="$t('refresh.common.tab[3]')" name="3" />
                <el-tab-pane v-if="refreshTimedEnable" label="定时管理" name="5" />
            </el-tabs>
            <!-- 定时管理列表 -->
            <schedule-list v-if="showScheduleList"></schedule-list>
            <div v-else>
                <div class="search-bar-wrapper">
                    <el-button class="el-icon-plus" type="primary" @click="showAdd">
                        {{ $t("refresh.createBtn") }}
                    </el-button>
                    <div class="search-bar">
                        <div class="search-bar-item">
                            <label>{{ $t("refresh.search.label[0]") }}</label>
                            <el-tooltip placement="top-start" :content="selectTimeTip">
                                <ct-svg-icon icon-class="question-circle" class-name="question-circle" />
                            </el-tooltip>
                            <el-date-picker
                                popper-class="aocdn-ignore-refresh-date-picker"
                                v-model="searchDate"
                                type="datetimerange"
                                range-separator="-"
                                :start-placeholder="$t('common.datePicker.start')"
                                :end-placeholder="$t('common.datePicker.end')"
                                :clearable="false"
                                :default-time="['00:00:00', '23:59:59']"
                                :picker-options="pickerOptions"
                                @change="onDateChange"
                        />
                        </div>
                        <div class="search-bar-item">
                            <label>{{ $t("refresh.search.label[1]") }}</label>
                            <el-tooltip placement="top" :content="$t('refresh.search.placeholder2')">
                                <el-input
                                    class="custom-input"
                                    v-model="domains"
                                    clearable
                                    :placeholder="$t('refresh.search.placeholder2')"
                                />
                            </el-tooltip>
                        </div>
                        <div class="search-bar-item">
                            <label>{{ $t("refresh.search.label[2]") }}</label>
                            <el-tooltip placement="top" :content="$t('refresh.search.placeholder3')">
                                <el-select
                                    v-model="status"
                                    clearable
                                    :placeholder="$t('refresh.search.placeholder3')"
                                    class="search-bar--status"
                                >
                                    <el-option
                                        v-for="item in TaskStatusOptions2"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    />
                                </el-select>
                            </el-tooltip>
                        </div>
                        <div class="search-bar-item">
                            <el-button type="primary" plain @click="searchByBar">{{
                                $t("common.search.start")
                            }}</el-button>
                            <el-button @click="reset">{{ $t('common.search.reset') }}</el-button>
                        </div>
                    </div>
                </div>
                <el-table :data="dataList" v-loading="loading">
                    <el-table-column
                        :label="$t('refresh.table.label[0]')"
                        type="index"
                        width="60"
                        align="center"
                    />
                    <el-table-column
                        :label="$t('refresh.table.label[1]')"
                        prop="url"
                        align="left"
                        show-overflow-tooltip
                    />
                    <el-table-column :label="$t('refresh.table.label[2]')" prop="createTime" align="left" />
                    <el-table-column :label="$t('refresh.table.label[3]')" align="left">
                        <template slot-scope="{ row }">
                            <cute-state :type="statusMap.get(row.status) || 'info'">{{
                                row.status | taskStateFilter
                            }}</cute-state>
                        </template>
                    </el-table-column>
                </el-table>
                <div class="pager">
                    <el-pagination
                        :small="isXs"
                        :layout="isXs ? 'prev, pager, next' : 'total, sizes, prev, pager, next,jumper'"
                        :total="totalRecord"
                        :current-page.sync="page"
                        :page-size="pageSize"
                        :page-sizes="[10, 30, 50, 100]"
                        :hide-on-single-page="false"
                        @size-change="sizeChange"
                    ></el-pagination>
                </div>
            </div>
            <create-dialog
                :addVisible="addTaskVisible"
                :dialogLoading="dialogLoading"
                :addTask="addTask"
                @cancel="cancel"
                @submit="submitTask"
            />
        </ct-box>
    </ct-section-wrap>
</template>

<script lang="ts">
import { Component, Vue, Watch } from "vue-property-decorator";
import { ScreenModule } from "@/store/modules/screen";
import { refreshUrl } from "@/config/url";
import { RefreshItem } from "@/types/refresh";
import { getAm0, timePeriodGenerator } from "@/utils/index";
import { TaskSearchParams, TaskStatus, RefreshType2 } from "@/types/refresh";
import {
    TaskStatusMap2,
    RefreshTypeEnum2,
    RefreshTypeMap2,
    TaskStatusOptions2,
    GetCtiamButtonAction,
} from "@/config/map";
import CreateDialog from "./components/CreateDialog.vue";
import ScheduleList from "./components/ScheduleList.vue";
import { shortcuts_obj } from "@/utils/pickerOption";
import { CtiamCode } from "@/utils/ctFetch/errorConfig";
import { checkCtiamButtonAuth } from "@/store/modules/menu";
import { StatisticsModule } from "@/store/modules/statistics";

import ctSvgIcon from "@/components/ctSvgIcon/index.vue";

type taskParam = { optype: RefreshType2; content: string };

@Component({
    filters: {
        taskStateFilter(input: TaskStatus): string {
            return TaskStatusMap2[input]
                ? (TaskStatusMap2[input] as string)
                : (this.$t("refresh.msg7") as string);
        },
    },
    components: { CreateDialog, ScheduleList, ctSvgIcon },
})
export default class Task extends Vue {
    usePost = true;
    addTaskVisible = false;
    loading = false;
    dialogLoading = false;
    dataList: RefreshItem[] = []; // 最终展示的分页数据

    private domains = ""; // 搜索条件
    private status = ""; // 任务状态
    private searchDate = timePeriodGenerator()(0);
    protected refresh = false;
    totalRecord = 0;
    page = 1;
    pageSize = 10;

    type = (this.$route.query.type || RefreshTypeEnum2.UrlRefresh) as RefreshType2;
    addTask: taskParam = {
        content: "",
        optype: RefreshTypeEnum2.UrlRefresh,
    };
    addTask2: taskParam = {
        content: "",
        optype: RefreshTypeEnum2.Schedule,
    };
    private minDate: null | number = null; // 时间选择器选择的最小时间
    TaskStatusOptions2 = TaskStatusOptions2;
    // 是否展示新的创建功能（任务管理tab）
    get refreshTimedEnable() {
        return StatisticsModule.refreshTimedEnable;
    }
    get isXs() {
        return this.screenWidth < 600;
    }

    get screenWidth() {
        return ScreenModule.width;
    }
    get dynamicSearchUrl() {
        return this.type === RefreshTypeEnum2.UrlPrefetch ? refreshUrl.preloadList : refreshUrl.refreshList;
    }
    get pickerOptions() {
        const options = {
            disabledDate: (time: Date) => {
                const gap = this.currentTimeLimit;
                // 获取今天0点数据
                const today0 = getAm0(new Date());
                // 禁用大于今天时间23：59：59
                if (+time > +new Date(+today0 + 24 * 60 * 60 * 1000 - 1)) return true;
                // 禁用小于设置的最早期限的，由于今天会算1天，需要倒推 x-1 天
                if (+time < +today0 - gap * 24 * 60 * 60 * 1000) return true;
                // if (this.type !== "3") return false;
                //当最小时间为null的时候则关闭禁用，点清空时会变为0，所以0也要判断
                if (this.minDate === null || this.minDate === 0) return false;
                // 如果是预取，还需要限制，选择的范围不能大于7天
                const range = this.currentTimeSpanLimit * 24 * 60 * 60 * 1000;
                return +time < +this.minDate - range || +time > +this.minDate + range + 24 * 60 * 60 * 1000 - 1000;
            },
            onPick: ({ minDate }: { minDate: Date }) => {
                this.minDate = +minDate;
            },
            shortcuts: [
                shortcuts_obj.recent_15_minutes,
                shortcuts_obj.recent_1_hour,
                shortcuts_obj.recent_6_hours,
                shortcuts_obj.recent_24_hours,
            ],
        };

        if (this.type === "3") options.shortcuts.push(shortcuts_obj.recent_1_week);

        return options;
    };
    getTimeFromDate(d: Date): string {
        const y = d.getFullYear();
        const m = d.getMonth();
        const fullM = m >= 9 ? `${m + 1}` : `0${m + 1}`;
        const date = d.getDate() < 10 ? "0" + d.getDate() : d.getDate();
        const hours = d.getHours() < 10 ? "0" + d.getHours() : d.getHours();
        const minutes = d.getMinutes() < 10 ? "0" + d.getMinutes() : d.getMinutes();
        const seconds = d.getSeconds() < 10 ? "0" + d.getSeconds() : d.getSeconds();
        return `${y}-${fullM}-${date} ${hours}:${minutes}:${seconds}`;
    }

    // 重写 mixin 定义
    get searchParams() {
        const { type, domains, searchDate, status } = this;

        const params: TaskSearchParams = {
            startTime: this.getTimeFromDate(searchDate[0]),
            endTime: this.getTimeFromDate(searchDate[1]),
            url: domains,
            domains, // 给后端验证 domain 权限用的
            status,
        };
        // 任务列表不走这里的查询，走schedule-list
        if (type !== RefreshTypeEnum2.Schedule && type !== RefreshTypeEnum2.UrlPrefetch) {
            params.type = RefreshTypeMap2[type];
        }
        return params;
    }

    // 根据status获取cute-state的type
    get statusMap() {
        return new Map([
            ["1", "info"],
            ["2", "master"],
            ["3", "success"],
            ["4", "danger"],
        ]);
    }

    get showScheduleList() {
        return this.refreshTimedEnable && this.type === RefreshTypeEnum2.Schedule;
    }

    /**
     * 选择时间-提示
     */
    get selectTimeTip() {
        const tip = this.$t("refresh.search.tip1", {
            timeLimit: this.timeLimit,
            timeSpanLimit: this.timeSpanLimit,
        });
        return tip;
    }

    /**
     * 时间限制-由于今天会算1天，需要倒推 x-1 天
     */
    get currentTimeLimit() {
        return this.timeLimit - 1;
    }

    /**
     * 时间限制-实际接口返回来的值
     */
    get timeLimit() {
        let data = this.urlRefreshTimeLimit;
        if (this.type === "3") {
            data = this.urlPrefetchTimeLimit;
        }
        return data;
    }

    /**
     * 时间跨度限制-由于今天会算1天，需要倒推 x-1 天
     */
    get currentTimeSpanLimit() {
        return this.timeSpanLimit - 1;
    }

    /**
     * 时间跨度限制
     */
    get timeSpanLimit() {
        let data = this.urlRefreshTimeSpanLimit;
        if (this.type === "3") {
            data = this.urlPrefetchTimeSpanLimit;
        }
        return data;
    }

    /**
     * URL刷新、目录刷新、正则刷新-时间限制
     */
    get urlRefreshTimeLimit() {
        return StatisticsModule.refreshConfig.urlRefreshTimeLimit;
    }

    /**
     * URL刷新、目录刷新、正则刷新-时间跨度限制
     */
    get urlRefreshTimeSpanLimit() {
        return StatisticsModule.refreshConfig.urlRefreshTimeSpanLimit;
    }

    /**
     * URL预取-时间限制
     */
    get urlPrefetchTimeLimit() {
        return StatisticsModule.refreshConfig.urlPrefetchTimeLimit;
    }

    /**
     * URL预取-时间跨度限制
     */
    get urlPrefetchTimeSpanLimit() {
        return StatisticsModule.refreshConfig.urlPrefetchTimeSpanLimit;
    }

    @Watch("type")
    handleTypeChange() {
        this.minDate = null;
        this.checkPrefetchTimeAndUpdate(this.searchDate);
        this.fetchList();
    }
    @Watch("page")
    onPageChange() {
        this.fetchList();
    }

    searchByBar() {
        if (this.searchDate === null) {
            this.$message.error(this.$t("refresh.search.error1") as string);
            return;
        }
        this.fetchList();
    }

    reset() {
        this.page = 1;
        this.pageSize = 10;
        this.domains = "";
        this.status = "";
        this.searchDate = timePeriodGenerator()(0);
        this.fetchList();
    }
    /**
     * 时间选择器选中后重新计算时间范围
     */
    onDateChange(time: [Date, Date]) {
        this.minDate = null;
        this.checkPrefetchTimeAndUpdate(time);
    }
    /**
     * 检查时间范围是否合法，不合法则重置
     */
    checkPrefetchTimeAndUpdate(time: Date[]) {
        const gap = this.currentTimeLimit;
        // 如果时间选择器为空，则不触发查询
        if (time === null) return;
        const [start, end] = time;
        const todayNow = new Date();
        const result: { [key: string]: Date } = {
            start,
            end,
        }
        // if (end.getTime() > todayNow.getTime()) {
        //     result.end = todayNow;
        // }
        // 如果开始时间小于当前时间-x天，则取当前时间-x天
        if (start.getTime() < todayNow.getTime() - gap * 24 * 60 * 60 * 1000) {
            result.start = new Date(todayNow.getTime() - gap * 24 * 60 * 60 * 1000);
        }
        // 如果结束时间小于开始时间，则取当前时间
        if (result.end.getTime() < result.start.getTime()) {
            const today24 = new Date();
            today24.setHours(23, 59, 59);
            result.end = today24;
        }
        this.searchDate = [result.start, result.end];
    }
    mounted() {
        this.fetchList();
    }

    cancel() {
        this.addTaskVisible = false;
    }

    async showAdd() {
        await checkCtiamButtonAuth(GetCtiamButtonAction("refreshCreate"));
        this.addTaskVisible = true;
        this.addTask = {
            content: "",
            optype: this.type,
        };
    }

    /**
     * 处理时间跨度
     * 如果是: URL刷新、目录刷新、正则刷新, 时间跨度不能超过5天，超过5天则报错
     * 如果是URL预取，时间跨度不能超过7天，超过7天则报错
     * true 代表超过, false 代表没超过
     */
    handleTimeSpan() {
        const { searchDate } = this;
        const startTime = searchDate ? searchDate[0] : 0;
        const endTime = searchDate ? searchDate[1] : 0;
        const startTs = Math.floor(+startTime / 1000);
        const endTs = Math.floor(+endTime / 1000);
        const difference = endTs - startTs;
        const limit = this.timeSpanLimit * 24 * 60 * 60;
        return difference > limit;
    }

    // 查询
    async fetchList() {
        if (this.type === RefreshTypeEnum2.Schedule) {
            return;
        }

        const isTimeOutOfLimit = this.handleTimeSpan();
        if (isTimeOutOfLimit) {
            const tip: any = this.$t("refresh.search.tip1", {
                timeLimit: this.timeLimit,
                timeSpanLimit: this.timeSpanLimit,
            });
            this.$message.error(tip);
            return;
        }

        this.loading = true;
        const params = {
            ...this.searchParams,
            pageSize: this.pageSize,
            pageIndex: this.page,
        };
        try {
            const rst = await this.$ctFetch<{ list: RefreshItem[]; total: number }>(this.dynamicSearchUrl, {
                // encodeParams: true,
                // data: ,
                method: "POST",
                body: {
                    data: params,
                },
            });
            this.dataList = rst.list;
            this.totalRecord = rst.total;
        } catch (err) {
            const isCtiamError = CtiamCode.includes((err as any)?.data?.code);
            if (isCtiamError) {
                // 权限报错时清空数据
                this.dataList = [];
            }
            this.$errorHandler(err);
        } finally {
            this.loading = false;
        }
    }

    sizeChange(val: number) {
        // 恢复到第一页
        this.page = 1;
        this.pageSize = val;
        this.fetchList();
    }

    async submitTask(domainList: any[]) {
        try {
            this.dialogLoading = true;
            const url =
                this.addTask.optype === RefreshTypeEnum2.UrlPrefetch
                    ? refreshUrl.createPreload
                    : refreshUrl.createRefresh;
            const data: { code?: number } = await this.$ctFetch(url, {
                method: "POST",
                body: {
                    data: {
                        type: RefreshTypeMap2[this.addTask.optype],
                        urls: domainList
                            .map(val => val.url)
                            .map(val => val.replace(/\s*/g, ""))
                            .filter(val => val), // 处理换行、所有空格、过滤空数据
                    },
                },
            });
            if (data.code && data.code === 100006) {
                this.$message.error(this.$t("refresh.create.msg5") as string);
                return;
            }
            this.fetchList();
            this.$message.success(this.$t("refresh.create.msg6") as string);
            this.addTaskVisible = false;
        } catch (error) {
            this.$errorHandler(error);
        } finally {
            this.dialogLoading = false;
        }
    }
}
</script>
<style lang="scss" scoped>
.search-bar-wrapper {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 8px;
}
.search-bar {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
    flex-shrink: 0;
    &-item {
        display: flex;
        gap: 12px;
        align-items: center;
        label {
            flex-shrink: 0;
            font-size: 12px;
        }
    }
    .search-bar--status {
        width: 150px;
    }
    .custom-input {
        ::v-deep {
            .el-input__inner {
                padding-right: 12px;
            }
        }
    }
}
// 状态颜色
.status-2 {
    color: $g-color-blue;
}

.status-3 {
    color: $g-color-green;
}

.status-4 {
    color: $g-color-red;
}

.status-1 {
    color: $g-color-gray;
}
.pager {
    margin-top: 8px;
    ::v-deep.el-pagination {
        text-align: right !important;
    }
}
.table-scroll-wrap {
    padding: 20px;
}
.question-circle {
    font-size: $text-size-md;
    cursor: pointer;
    margin-left: -8px;
}
</style>
<style lang="scss">
.aocdn-ignore-refresh-date-picker {
    > .el-picker-panel__footer .el-picker-panel__link-btn.el-button--text {
        display: none;
    }
}
</style>
