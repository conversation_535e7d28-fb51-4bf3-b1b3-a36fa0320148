{"acceArea": {"acceTipsCtclouds": "When you select Chinese Mainland or Global for the accelerated region:<br />1. If your account has not completed the real name verification, please complete the real name verification first according to the guideline document. <a href=\"{ctcloudLink}\" target=\"_blank\">Real Name Verification</a>, After completing the real name verification, please log in and try again. <br />2. If your domain has not completed the ICP Filing in Chinese Mainland, please complete it first.", "acceTipsNonCtclouds": "If the Chinese mainland is included, complete an ICP filing for the domain name in the Chinese mainland and then complete a Public Security Bureau (PSB) filing.", "isCtcCloudTip": {"tip1": "Currently, only CDN supports global acceleration. Please select ", "tip2": "the CDN type and ensure that ", "tip3": "CDN (Global, excluding Chinese Mainland)", "tip4": "has been enabled."}, "isCtcCloudTipOld": {"tip1": "Currently, only CDN supports global acceleration. Please ensure that ", "tip2": "CDN (Global, excluding Chinese Mainland)", "tip3": "has been enabled. You can change the service area after submitting a ticket to change the acceleration type to CDN."}, "billingNote": "Billing standards vary based on accelerated regions.", "productTip": {"008": {"tip1": "You have not enabled the CDN service for areas outside Chinese Mainland. Please enable {0} first.", "tip2": "CDN (Global, excluding Chinese Mainland) ", "tip3": "first.", "tip4": "You have not enabled the CDN acceleration service for areas outside Chinese Mainland. You can enable 'CDN (Global, excluding Chinese Mainland)' as required.{1}"}, "006": {"tip1": "ICDN supports global acceleration. Please enable {2} first", "tip2": "ICDN (Global, excluding Chinese Mainland) ", "tip3": "first.", "tip4": "You have not enabled the acceleration service for areas outside Chinese Mainland. You can enable 'ICDN (Global, excluding Chinese Mainland)' as required.{3}"}, "004": {"tip1": "Currently, only CDN supports global acceleration. Please select the CDN type and ensure that {4} has been enabled.", "tip2": "CDN (Global, excluding Chinese Mainland) ", "tip3": "has been enabled."}, "014": {"tip1": "Currently, only CDN supports global acceleration. Please select the CDN type and ensure that {5} has been enabled.", "tip2": "CDN (Global, excluding Chinese Mainland) ", "tip3": "has been enabled."}, "common": {"tip1": "Change Acceleration Type", "tip2": "If you need global acceleration, please contact the account manager first to enable the acceleration type ending with (Global, excluding Chinese Mainland)."}}, "confirmChangeAccelerationType": "Are you sure you want to change the acceleration type to CDN? Changing the acceleration type may affect billing. Please operate with caution."}, "mixin": {"Note": "Currently, only HTTP origin pull supports customizable ports. HTTPS uses port 443 for origin pull. If you select Follow client, requests are sent to port 80 or 443 of your origin server over the protocol specified by the requests.", "ttlTip": {"itm1": "Please set the correct cache time by entering a number."}, "follow": "Follow"}, "alogicCache": {"Type": "Type", "Content": "Content", "CacheTime": "TTL", "Priority": "Weight", "CacheURL": "URL Cache", "OK": "OK", "Cancel": "Cancel"}, "location": {"chinaMainland": "Chinese Mainland", "oversea": "Outside the Chinese Mainland", "global": "Global", "globalExcludingMainland": "Global(excluding Chinese Mainland)"}, "extention": {"dynamic": "Dynamic Files", "image": "Image Files", "style": "Style Files", "media": "Audio and Video Files", "download": "Download Files", "page": "Page Files"}, "alogicCacheMixin": {"CacheTtlMap": {"2": "Day(s)", "3": "Hour(s)", "4": "Minute(s)", "5": "Second(s)"}, "CacheTypeMap": {"1": "Do not cache", "2": "Origin first", "3": "Force cache"}, "timeTypeMap": {"2": "days", "3": "hours", "4": "minutes", "5": "seconds"}, "ValidationErrorMessages": {"RequiredCacheTime": "Please enter the expiration time.", "CacheTimeExceedsLimit": "Please set the correct cache time in {type}, with a maximum of {value}.", "RequiredPriority": "Enter a weight", "InvalidPriority": "Please set the correct weight as an integer between 1-100.", "DuplicateValidation": "Duplicate {type} is not allowed. Please check {repeatedName} {type}.", "selectCache": "Please select the {mode}."}, "FileSuffix": {"0": "jpg, png, css (separated by ',')", "1": "/test, /a/b/c (cannot end with '/')", "4": "/index.html, /test/a.jpg"}}, "domainInput": {"placeholder": "Enter a domain name, for example www.ctyun.cn", "orderLink": "Ticket Consultation", "verify": "Enter Verification Procedure", "ownershipVerify": "Authenticate Domain Ownership", "ownershipVerifyTip": "Domain ownership authentication required for [{model}]. You can use DNS resolution verification or file verification. If the operation fails, please ", "serviceTicket": "go to the customer service system", "ownershipVerifyTip2": "to submit a ticket.", "ownershipVerifyTip3": "1. Please add the following TXT records to your DNS service provider.", "verifyLink": "Verification Operation Guide", "descriptions": {"itm1": "Record Type", "itm2": "Host Record", "itm3": "Record Value"}, "dnsVerify": {"itm1": "1. Please add the following TXT records to your DNS service provider.", "itm2": "2. Wait for TXT parsing to generate.", "itm3": "3. Click the button below to verify.", "label": "DNS Resolution Authentication"}, "verifyText": "Verify", "verifyRst1": "Verification results:", "fileVerify": {"itm1": "1. Please create a file in the root directory of your {domainZone} source site. ", "itm2": "File name: {filename}, file content: {zoneRecord}", "itm3": "2. Access the file through http (s)://{domainZone}/{filename}.", "itm4": "3. Click the button below to verify.", "label": "Document Authentication"}, "verifyRst2": "Verification results:", "empty": "Enter a domain name", "invalid": "Incorrect domain name format.", "validating": "Domain name verification in progress.", "blacklist": "A violation history exists for this domain name. Access denied currently.", "topError": "Top-level domain name error.", "verifyFailed": "Ownership authentication required for the domain [{model}]. Click ", "repeat": "Failed to add this domain name because it exists under another account. If you have any questions, please see ", "existDomain": "Domain name is exists，If you have any questions, please see ", "orderExist": "An ongoing ticket exists for the domain name. Please wait for it to be processed and try again. If you have any questions, please see ", "repeatProduct": "Another product is being used by the domain name. If you have any questions, please see ", "validSuccess": "Verification successful.", "validFailed": "Verification failed. Reason:", "ownershipFailed": "Domain ownership authentication failed."}, "blockTable": {"allow": "Trustlist", "block": "Blocklist"}, "origin": {"slave": {"itm1": "Backup Layer-2", "itm2": "Backup Layer-3", "itm3": "Backup Layer-4", "itm4": "Backup Layer-5"}, "addOSS": "Add OSS Domain", "numberPlaceholder": "Please enter a number between 1-100.", "resourcePool": "Resource pool", "numberValidate": "Please enter an integer between 1-100.", "selectOSS": "Please select an OSS resource pool."}, "originPort": {"httpTip": "Please enter the correct HTTPS port number between 1-65535."}, "UaBlock": {"maxNum": "The number of input UA entries exceeds {maxNum}, please verify."}, "UriBlock": {"maxNum": "The number of input URL entries exceeds {maxNum}, please verify."}}