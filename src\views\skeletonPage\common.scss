.skeleton-page {
    width: 100%;
    height: 100%;
    padding: 20px;
    background: #f3f4f7;
    overflow: auto;
}

.framework-box {
    width: 100%;
    padding: 0 70px 30px 70px;
    display: flex;
    justify-content: center;
}

.guide-box {
    width: 100%;
    min-height: 440px;
    padding: 117px 120px;
    margin-bottom: 20px;

    .guide-title {
        font-family: PingFangSC-Semibold;
        font-size: 20px;
        color: #000000;
        letter-spacing: 0;
        line-height: 20px;
        font-weight: 600;
        margin-bottom: 32px;
    }

    .guide-tip {
        font-family: PingFangSC-Regular;
        font-size: 12px;
        color: #000000;
        letter-spacing: 0;
        text-align: justify;
        line-height: 24px;
        font-weight: 400;
        margin-bottom: 24px;
    }
}

.flex-style {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
}

.common-wrapper {
    border-radius: 4px;
    background: white;
    padding: 20px;
    margin-bottom: 20px;

    .common-item {
        margin-right: 60px;
        flex: 1;

        &:last-child {
            margin-right: unset;
        }
    }

    .title-style {
        font-family: PingFangSC-Semibold;
        font-size: 14px;
        color: #252626;
        letter-spacing: 0;
        font-weight: 600;
        margin-bottom: 30px;
    }

    .label-box {
        display: flex;
        font-family: PingFangSC-Semibold;
        font-size: 14px;
        color: #3e3f40;
        letter-spacing: 0;
        font-weight: 600;
        margin-bottom: 20px;
        white-space: nowrap;
        justify-content: center;
        line-height: 45px;

        .icon-style {
            width: 45px;
            height: 45px;
            margin-right: 15px;
            background-position: 0 0;
            background-repeat: no-repeat;
            background-size: contain;
        }
    }

    .content-style {
        font-family: PingFangSC-Regular;
        font-size: 14px;
        color: #3e3f40;
        letter-spacing: 0;
        font-weight: 400;
        white-space: pre-line;
    }

    &:last-child {
        margin-bottom: unset;
    }
}

.blue-btn-style {
    background: #3D73F5;
    border-color: #3D73F5;
}

.el-button {
    &.is-plain {
        border-color: #3D73F5;
        color: #3D73F5;
        &:focus {
            background: #3D73F5;
            color: white;
        }

        &:hover {
            background: #3D73F5;
            color: white;
        }
    }
}
