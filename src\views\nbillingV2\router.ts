import { RouteConfig } from "vue-router";

const billingDetailRouter: RouteConfig = {
    path: "/billingDetail",
    name: "billingDetail",
    component: () => import("./index.vue"),
    meta: {
        domain: "billingDetail",
        ucode: "billingDetail", // 配合 el-menu active
        breadcrumb: {
            // type: "toolTip",
            title: "$t('billingDetail.title')",
            // tips: [
            //     '$t("billing.tips.tip1")',
            //     '$t("billing.tips.tip2")',
            //     '$t("billing.tips.tip3")',
            //     '$t("billing.tips.tip4")',
            // ],
            route: ["billingDetail"],
        },
        perm: "billingDetail",
    },
    children: [
        {
            path: "cdn",
            name: "billingDetail.cdn",
            component: () => import("./cdn.vue"),
            meta: {
                breadcrumb: {
                    title: "$t('billingDetail.cdn.title')",
                    route: ["billingDetail", "billingDetail.cdn"],
                },
                perm: "billingDetail",
            },
        },
        {
            path: "icdn",
            name: "billingDetail.icdn",
            component: () => import("./icdn.vue"),
            meta: {
                breadcrumb: {
                    title: "$t('billingDetail.icdn.title')",
                    route: ["billingDetail", "billingDetail.icdn"],
                },
                perm: "billingDetail",
            },
        },
    ],
};

export default billingDetailRouter;
