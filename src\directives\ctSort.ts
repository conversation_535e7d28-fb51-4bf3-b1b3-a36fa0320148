import { getConfig } from "@/config/sortConfig";

const config: any = getConfig();
/**
 * 根据配置对DOM元素进行排序
 */
const sort = function(el: any, binding: any, vNode: any) {
    const type: any = binding.value;
    const titleSelector = vNode.data?.attrs && vNode.data.attrs["ct-dom-title"];
    const items: any[] = [];
    config[type]?.forEach((item: any) => {
        const itemDOM = el.querySelector(item.selector);
        if (itemDOM) {
            if (titleSelector) {
                // 控制标题
                const title = itemDOM.querySelector(titleSelector);
                if (title) {
                    item.newTitle && (title.innerHTML = item.newTitle);
                    if (item.hiddenTitle) {
                        title.remove();
                        itemDOM.style.border = "none";
                    }
                }
            }
            // 控制显示隐藏
            if (item.hidden) {
                itemDOM.remove();
            } else {
                items.push(itemDOM);
            }
        }
    });
    items.forEach(item => {
        el.appendChild(item);
    });
};

const sortDirective = {
    inserted: sort,
    componentUpdated: sort,
};

/**
 * 根据配置对DOM元素进行移动
 */
const move = function(el: any, binding: any, vNode: any) {
    const type = binding.value;
    const titleSelector = vNode.data?.attrs && vNode.data.attrs["ct-dom-title"];
    config[type]?.forEach((item: any) => {
        const sourceDOM = el.querySelector(item.sourceSelector);
        sourceDOM && (sourceDOM["data-key"] = new Date().getTime());
        const targetDOM = el.querySelector(item.targetSelector);
        if (sourceDOM && targetDOM) {
            if (titleSelector) {
                // 控制标题
                const title = sourceDOM.querySelector(titleSelector);
                if (title) {
                    item.newTitle && (title.innerHTML = item.newTitle);
                    if (item.hiddenTitle) {
                        title.remove();
                        sourceDOM.style.border = "none";
                    }
                }
            }

            if (targetDOM.nextElementSibling !== sourceDOM) {
                targetDOM.insertAdjacentElement("afterend", sourceDOM);
            }
        }
    });
};

const moveDirective = {
    inserted: move,
    componentUpdated: move,
};

export { sortDirective, moveDirective };
