<template>
    <el-card>
        <el-radio-group v-model="region">
            <el-radio-button :label="0">{{ $t('statistics.provider.mainland') }}</el-radio-button>
            <el-radio-button :label="1">{{ $t('statistics.provider.overseas') }}</el-radio-button>
        </el-radio-group>
        <el-table :data="dataList" v-loading="loading" :element-loading-text="$t('statistics.common.table.loading')"
            @sort-change="setSort" height="480px">
            <el-table-column :label="$t('statistics.provider.area')" prop="provinceName" align="center" />
            <!-- ***toFixed(2)方法会转数据类型number为string，所以在表格中限制小数位 -->
            <el-table-column :label="`${$t('statistics.rank.domainRank.tableColumn5')}(${Mbps})`" sortable
                prop="peek_bandwidth" align="center">
                <template slot-scope="scope">
                    {{ scope.row.peek_bandwidth.toFixed(2) }}
                </template>
            </el-table-column>
            <el-table-column :label="`${$t('statistics.provider.traffic')}(${GB})`" sortable prop="flow" align="center">
                <template slot-scope="scope">
                    {{ scope.row.flow.toFixed(2) }}
                </template>
            </el-table-column>
            <el-table-column :label="$t('statistics.rank.common.tableColumn4')" prop="flow_per" align="center" />
            <el-table-column :label="$t('statistics.eas.tip25')" sortable prop="peek_qps" align="center" />
            <el-table-column :label="$t('statistics.eas.tip24')" prop="connections" sortable align="center" />
            <el-table-column :label="$t('statistics.eas.tip9')" prop="connections_per" align="center" />
        </el-table>
    </el-card>
</template>

<script>
import { statistics as StatisticsUrl } from "@/config/url/ipa/statistics";
import rankMixin from "../rankMixin";
import chartShim from "../chartShim";
import { nUserModule } from "@/store/modules/nuser";
import { exportExcelFile } from "@/utils/logic/excel";

/* eslint-disable */
export default {
    name: "regionISP",
    mixins: [rankMixin, chartShim],
    data() {
        return {
            region: 0,
            // 搜索条
            useMultipleDomain: true,
            multipleDomain: [], //域名列表选中值
            domainOptions: [], //域名列表

            isp: [],
            ispList: [],

            area: [],

            currentPeriod: "0", // 当前 ct-time-picker 选择的时间段
            timeRange: [],

            // 表格
            loading: false,
            fetchData: [],

            // 表格排序
            sort: "", // 排序字段
            order: "", // 排序顺序 descending逆序 ascending正序
        };
    },
    mounted() {
        // this.getAreaList();

        this.$ctBus.$on("ipa:download:regionISP", this.downloadTable);
    },
    beforeDestroy() {
        this.$ctBus.$off("ipa:download:regionISP");
    },
    computed: {
        // ***单位换算：获取进制基数
        sscale() {
            return ScaleModule.scale;
        },
        // tab过滤后的数据
        dataList() {
            return this.fetchData.filter(item => item.overseas === this.region)
        },
        // 当前语言
        isEn() {
            return nUserModule.lang === "en";
        }
    },
    methods: {
        downloadTable() {
            if (!Array.isArray(this.downloadDataList) || !this.downloadDataList.length) {
                this.$message(`${this.$t("statistics.common.chart.errMsg[2]")}`);
                return true;
            }

            this.tableToExcel();
        },
        reqAreaList() {
            return this.$ctFetch(StatisticsUrl.areaList, {
                method: "GET",
                transferType: "json",
            });
        },
        // 获取表格数据
        async initData(reqParam) {
            this.loading = true;
            const { result } = await this.getData(reqParam);
            this.loading = false;
            // 返回结果没有海外则自动切换tab
            if (result.length && !result.some(item => item.overseas === 0)) {
                this.region = 1;
            }

            this.downloadDataList = result.map(item => ({
                ...item,
                provinceName: this.isEn ? item.province_en : item.province_cn,
                peek_bandwidth: item.peek_bandwidth / Math.pow(this.scale, 2),
                flow: item.flow / Math.pow(this.scale, 2)
            }));

            this.fetchData = result.map(item => ({
                ...item,
                // ***获取省份code与name对应信息3
                provinceName: this.isEn ? item.province_en : item.province_cn,
                peek_bandwidth: item.peek_bandwidth / Math.pow(this.scale, 2), // ***bps->Mbps
                flow: item.flow / Math.pow(this.scale, 3), // ***B->GB
            }));
        },
        getData(data) {
            return this.$ctFetch(StatisticsUrl.operatorMetricsList, {
                method: "POST",
                transferType: "json",
                body: {
                    data,
                },
            });
        },

        // 表格排序
        setSort(data) {
            this.sort = data.prop;
            this.order = data.order;
        },

        // 表格下载
        download() {
            this.tableToExcel();
        },
        // 重写 excel 数据拼接方法
        tableToExcel() {
            const sheethead = [`${this.$t('statistics.provider.area')}`, `${this.$t('statistics.rank.domainRank.tableColumn5')}(${this.Mbps})`, `${this.$t('statistics.provider.traffic')}(${this.MB})`, `${this.$t('statistics.rank.common.tableColumn4')}`, `${this.$t('statistics.eas.tip25')}`, `${this.$t('statistics.eas.tip24')}`, `${this.$t('statistics.eas.tip9')}`];

            const sheets = [[sheethead], [sheethead]];

            // 表格数据拼接
            this.downloadDataList.forEach(item => {
                const row = [`${item.provinceName}`, `${item.peek_bandwidth}`, `${item.flow}`, `${item.flow_per}`, `${item.peek_qps}`, `${item.connections}`, `${item.connections_per}`]
                if (item.overseas === 1) {
                    sheets[1].push(row);
                } else {
                    sheets[0].push(row);
                }
            });

            const timeRange = this.reqParamsFromParentBackup.timeRange;
            let t1 = this.$dayjs(timeRange[0]).format("YYYY-MM-DD HH:mm:ss");
            let t2 = this.$dayjs(timeRange[1]).format("YYYY-MM-DD HH:mm:ss");
            // 说明：由于下载时需要用到请求参数 searchParams 中的数据，所以需要在 getData 中主动缓存使用
            t1 = t1.replace(/-|\s|:/g, "").slice(0, 12);
            t2 = t2.replace(/-|\s|:/g, "").slice(0, 12);

            //表格CSV格式和内容
            exportExcelFile({
                [this.$t('statistics.provider.mainland')]: sheets[0],
                [this.$t('statistics.provider.overseas2')]: sheets[1],
            }, `${this.$t('statistics.eas.tip22')}${t1}-${t2}.xlsx`);
        },
    },
};
</script>
<style lang="scss" scoped></style>
