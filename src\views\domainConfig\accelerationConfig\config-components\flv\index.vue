<template>
    <div class="flv-wrapper">
        <el-form
            label-width="140px"
            label-position="left"
            :model="form"
            :rules="rules"
            ref="flvForm"
            :disabled="!isEdit || !isService || isLockFlv"
        >
            <div>
                <el-form-item :label="$t('domain.detail.label91')" prop="sub_flv_switch">
                    <el-switch v-model="sub_flv_switch" @change="onFlvSwitchChange"></el-switch>
                </el-form-item>

                <div v-if="sub_flv_switch" class="switch-wrapper">
                    <el-form-item
                        label=""
                        label-width="0"
                        :prop="isLockFlv ? null : `cus_flv_info`"
                        :rules="isLockFlv ? null : rules.cusFlvInfo"
                        key="cusFlvInfo"
                        ref="flvInfo"
                        class="cache-table-style"
                        v-show="isStaticsAbilityOn"
                    >
                        <div class="ct-table-wrapper">
                            <el-table class="origin-table" :data="form.cus_flv_info">
                                <el-table-column prop="mode" :label="$t('domain.type')" >
                                    <template slot-scope="scope">{{ type_list[scope.row.mode] }}</template>
                                </el-table-column>
                                <el-table-column prop="content" :label="$t('domain.content')" />

                                <el-table-column prop="flv_seek_type" :label="$t('domain.detail.label92')">
                                    <template slot-scope="scope">{{
                                        flv_seek_type_list[scope.row.flv_seek_type]
                                    }}</template>
                                </el-table-column>

                                <el-table-column
                                    prop="flv_seek_arg_start"
                                    :label="$t('domain.detail.label93')"
                                />

                                <el-table-column
                                    prop="flv_seek_arg_end"
                                    :label="$t('domain.detail.label94')"
                                />
                                <el-table-column prop="priority" :label="$t('domain.detail.label48')" />
                                <el-table-column :label="$t('domain.operate')" width="120">
                                    <template slot-scope="scope">
                                        <el-button
                                            type="text"
                                            :disabled="!isEdit || !isService || isLockFlv"
                                            @click="
                                                handleCacheOper(
                                                    scope.row,
                                                    'edit',
                                                    'cus_flv_info',
                                                    scope.$index
                                                )
                                            "
                                            >{{ $t("domain.modify") }}</el-button
                                        >
                                        <el-button
                                            type="text"
                                            :disabled="!isEdit || !isService || isLockFlv || form.cus_flv_info.length < 2"
                                            @click="
                                                handleCacheOper(
                                                    scope.row,
                                                    'delete',
                                                    'cus_flv_info',
                                                    scope.$index
                                                )
                                            "
                                            >{{ $t("domain.delete") }}</el-button
                                        >
                                    </template>
                                </el-table-column>
                            </el-table>
                            <div class="flex-row-style button-box">
                                <el-button
                                    class="btn"
                                    type="text"
                                    :disabled="!isEdit || !isService || isLockFlv"
                                    @click="handleCacheOper(null, 'create', 'cus_flv_info')"
                                >
                                    + {{ addButtonText }}
                                </el-button>
                            </div>
                        </div>
                    </el-form-item>
                </div>
            </div>
        </el-form>
        <flv-dialog
            :dialogVisible="flvDialogVisible"
            :flvForm="flvForm"
            :flvList="form.cus_flv_info"
            :from="currentType"
            @cancel="cancel"
            @submit="submitFlv"
        />
    </div>
</template>

<script>
// import ctSvgIcon from "@/components/ctSvgIcon";
import componentMixin from "@/views/domainConfig/componentMixin";
import validFieldMixin from "@/views/domainConfig/validFieldMixin";
import { cloneDeep } from "lodash-es";
import flvDialog from "@/views/domainConfig/accelerationConfig/config-components/flv/flvDialog.vue";
import i18n from "@/i18n";
import { lowerFirst } from '@/utils';
import { SecurityAbilityModule } from "@/store/modules/securityAbility";

const CacheModeMap = {
    0: i18n.t("domain.detail.cacheModeMap[0]"),
    1: i18n.t("domain.detail.cacheModeMap[1]"),
    2: i18n.t("domain.detail.cacheModeMap[2]"),
    3: i18n.t("domain.detail.cacheModeMap[3]"),
    4: i18n.t("domain.detail.cacheModeMap[4]"),
    5: i18n.t("domain.detail.cacheModeMap[5]"),
};

export default {
    name: "flv",
    components: {
        flvDialog,
    },
    mixins: [componentMixin, validFieldMixin],
    props: {
        datas: Object,
        isLockFlv: Boolean,
        isStaticsAbilityOn: Boolean,
        addButtonText: String,
        flv_info_switch: Boolean,
    },
    data() {
        return {
            form: {
                cus_flv_info: [],
            },
            flvForm: {
                mode: 0,
                content: "",
                flv_seek_type: "time",
                flv_seek_arg_start: "",
                flv_seek_arg_end: "",
                priority: 10,
            },
            sub_flv_switch: false,
            flvDialogVisible: false,
            currenIndex: "",
            currentType: "create",
            rules: {
                cusFlvInfo: [{ required: true, validator: this.validFlv, trigger: "blur, change" }],
            },
        };
    },
    computed: {},
    watch: {
        "datas.cus_flv_info": {
            deep: true,
            handler(val) {
                this.init(val);
            },
            immediate: true,
        },
        flv_info_switch: {
            deep: true,
            handler(val) {
                this.sub_flv_switch = val;
            },
            immediate: true,
        },
    },
    methods: {
        init(v) {
            this.form.cus_flv_info = cloneDeep(v);
        },
        onFlvSwitchChange(val) {
            if (val) {
                const originalConf = SecurityAbilityModule.securityOriginForm;
                this.form.cus_flv_info = cloneDeep(originalConf.cus_flv_info || []);

                this.$emit("onChange", this.form.cus_flv_info, true);
            } else {
                this.$emit("onChange", [], false);
            }
        },
        async handleCacheOper(row, currentType, tabName, i) {
            this.currentType = currentType;

            this.currenIndex = i;

            const getTime = new Date().getTime();
            if (currentType === "create") {
                const defaultFormMap = {
                    cus_flv_info: {
                        id: `cus_flv_info_${getTime}`,
                        mode: 0,
                        content: "",
                        flv_seek_type: "time",
                        flv_seek_arg_start: "begin",
                        flv_seek_arg_end: "stop",
                        priority: 10,
                    },
                };
                row = defaultFormMap[tabName];
            }
            if (currentType === "delete") {
                const msgMap = {
                    cus_flv_info: `${this.$t("domain.editPage.tip20")}`,
                };

                // 二次确认弹窗
                await this.$confirm(msgMap[tabName], this.$t("domain.delete"), {
                    type: "warning",
                });
                this.form[tabName].splice(i, 1);
                this.$emit("onChange", this.form.cus_flv_info, this.sub_flv_switch);
            } else {
                this.flvDialogVisible = true;
                const rowData = cloneDeep(row);
                this.$set(this, "flvForm", rowData);
            }
        },
        async submitFlv() {
            // 进行重复性检查
            const { mode } = this.flvForm;
            const typeName = CacheModeMap[mode] || "";
            const typeList = this.form.cus_flv_info
                .filter((i, index) => index !== this.currenIndex) // 先把指定的过滤掉
                .filter(item => item.mode === mode); // 再把不符合的类型过滤掉

            typeList.push(this.flvForm); // 再把当前的追进去进行全比对

            const repeatedName = this.checkRepeatedName(typeList);
            if (repeatedName) {
            await Promise.reject(this.$t("simpleForm.alogicCacheMixin.ValidationErrorMessages.DuplicateValidation", { repeatedName, type: lowerFirst(typeName) }));
            }

            if (this.currentType === "create") {
                this.form.cus_flv_info.push({
                    ...this.flvForm,
                });
            } else {
                this.$set(this.form.cus_flv_info, this.currenIndex, this.flvForm);
            }
            this.flvDialogVisible = false;
            this.$emit("onChange", this.form.cus_flv_info, this.sub_flv_switch);
        },
        cancel(dialogKey) {
            this[dialogKey] = false;
        },
        checkRepeatedName(cacheList) {
            if (cacheList.length === 0) return "";

            const nameLsit = cacheList
                .map(item => item.content)
                .join(",")
                .split(",");
            const nameMap = {};
            for (let idx = 0; idx < nameLsit.length; idx += 1) {
                if (nameMap[nameLsit[idx]]) {
                    return nameLsit[idx];
                } else {
                    nameMap[nameLsit[idx]] = true;
                }
            }

            return "";
        },
        fileTypePlaceholder() {
            return parseInt(this.form.mode) === 0
                ? this.$t("simpleForm.alogicCacheMixin.FileSuffix.0")
                : parseInt(this.form.mode) === 1
                ? this.$t("simpleForm.alogicCacheMixin.FileSuffix.1")
                : this.$t("simpleForm.alogicCacheMixin.FileSuffix.4");
        },
        async typeChange(val) {
            if (val === 2 || val === 3) {
                this.form.content = "/";
            } else {
                this.form.content = "";
            }
            this.$refs.flvForm.validateField("content");
        },
        validFlv(rule, value, callback) {
            if (this.isLockFlv || !this.isStaticsAbilityOn) callback();
            if (this.sub_flv_switch && this.form.cus_flv_info && this.form.cus_flv_info.length > 0) {
                callback();
            } else {
                callback(this.$t("domain.detail.placeholder78"));
            }
        },
    },
};
</script>

<style lang="scss" scoped>
@import "@/views/domainConfig/commonStyle.scss";

.flv-wrapper {
    ::v-deep {
        .el-form--label-left .el-form-item__label {
            text-align: right;
        }
    }
}
.input-style {
    width: 380px;
}
.question-circle {
    font-size: $text-size-md;
    cursor: pointer;
}
</style>
