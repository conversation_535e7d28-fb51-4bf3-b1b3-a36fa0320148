// 获取今天0点、本周一0点、本月1号0点
export const get0AM = (date: Date): number[] => {
    const now = new Date(date);
    const y = now.getFullYear();
    const m = now.getMonth();
    const d = now.getDate();
    const w = now.getDay();

    const d0am = +new Date(y, m, d, 0, 0, 0, 0);
    const w0am = +new Date(+d0am - (w - 1) * 24 * 60 * 60 * 1000);
    const m0am = +new Date(y, m, 1, 0, 0, 0, 0);

    return [Math.floor(d0am / 1000), Math.floor(w0am / 1000), Math.floor(m0am / 1000)];
};
/*
 * @Description: 时间相关的工具函数
 * @Author: wang yuegong
 */

// 1天的时长，毫秒
export const DAY_TIMESTAMP = 24 * 60 * 60 * 1000;

// 获取某天的0点
export const getAm0 = (date: Date): Date => {
    const newDate = new Date(date);
    newDate.setHours(0);
    newDate.setMinutes(0);
    newDate.setSeconds(0);
    newDate.setMilliseconds(0);

    return newDate;
};

// 生成获取指定时间段[起点，终点]的方法
export const timePeriodGenerator = () => {
    const today0 = +getAm0(new Date());

    // 默认终点是今天，常用于生成今天(0, ?0)、昨天(1, 1)、近x天(x, ?0)
    // 由于业务中更多的是向前获取，而不是向后，所以用正值表示向前，需要注意
    return (start: number, end = 0) => {
        if (start < end) throw Error("起点时间不能晚于终点时间");

        const startTime = new Date(today0 - start * DAY_TIMESTAMP);
        // 终点时间均为 23:59:59 ，所以输出时 -1 即可
        const endTime = new Date(today0 - end * DAY_TIMESTAMP + DAY_TIMESTAMP - 1000);

        return [startTime, endTime];
    };
};

/**
 * 校验时间范围是否在一个月内
 * <AUTHOR> <<EMAIL>>
 * @param {number | Date} startTime
 * @param {number | Date} endTime
 * @returns {boolean}
 */
export const isTimeInMonth = (startTime: number | Date, endTime: number | Date) => {
    return ((+endTime) - (+startTime)) <= (31 * 24 * 3600);
}
