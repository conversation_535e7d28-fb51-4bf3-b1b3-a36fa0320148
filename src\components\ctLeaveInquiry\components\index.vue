<template>
    <ct-dialog title="提示" :visible.sync="visible" width="400px" @close="visible = false">
        {{ title }}
        <template #footer>
            <el-button @click="handleCancel">取 消</el-button>
            <el-button plain @click="handleAbandon">放弃更改</el-button>
            <el-button type="primary" :loading="loading" @click="handleSubmit">提交保存</el-button>
        </template>
    </ct-dialog>
</template>

<script>
import ctDialog from "@/components/ctDialog";
import { SecurityAbilityModule } from "@/store/modules/securityAbility";

export default {
    name: "index",
    components: { ctDialog },
    data() {
        return {
            visible: false,
            loading: false,
            title: "当前域名有未保存的配置，是否保存配置后退出？",
            currentDomainInfo: null,
            submitFn: null,
        };
    },
    methods: {
        /**
         * 处理提交
         */
        async handleSubmit() {
            if (!this.submitFn || typeof this.submitFn !== "function") {
                this.handleSubmitCallback();
                return;
            }

            const fnRes = this.submitFn(this);
            if (!fnRes) {
                this.$emit("cancel");
                return;
            }

            if (fnRes === true) {
                this.handleSubmitCallback();
                return;
            }

            if (typeof fnRes.then === "function") {
                this.loading = true;
                try {
                    const result = await fnRes;
                    // 适配async-await中使用try-catch 并且return了值的情况
                    if (result === false) {
                        this.$message.error("提交失败，请检查");
                        this.$emit("cancel");
                        return;
                    }

                    this.handleSubmitCallback();
                } catch (e) {
                    this.$message.error("提交失败，请检查");
                }

                this.loading = false;
            }
        },
        /**
         * 提交回调
         */
        handleSubmitCallback() {
            this.visible = false;
            SecurityAbilityModule.SET_IS_EDIT(false);
            this.$emit("action", "confirm");
        },
        /**
         * 放弃更改
         */
        handleAbandon() {
            this.visible = false;
            SecurityAbilityModule.SET_IS_EDIT(false);
            this.$emit("action", "abandon");
            window.custom.emit("handleEditDomainConfig", false);
        },
        /**
         * 取消
         */
        handleCancel() {
            this.visible = false;
            this.$emit("close");
        },
    },
};
</script>

<style scoped></style>
