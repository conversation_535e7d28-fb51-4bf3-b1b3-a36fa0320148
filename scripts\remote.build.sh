#! /bin/sh

# web 远端构建脚本

current_branch=$(git symbolic-ref --short HEAD)
echo "== current branch is $current_branch =="

# drone 中设置环境变量，用于动态控制打包内容
PKG_NAME=${PKG_NAME}
echo "== current package is $PKG_NAME =="

# 错误处理：输出指定信息到错误通道2，并退出
function error_exit() {
    echo "$1" 1>&2
    exit 1
}

# 注意：monorepo 模式下，node_modules可以共享，但 dist 和 hash 要区分
# 检测并确保当前项目的主缓存目录存在
[ -d /tmp/cache/$PKG_NAME ] || mkdir -p /tmp/cache/$PKG_NAME
# 检测并确保通用的 node_modules 缓存目录存在
[ -d /tmp/cache/$PKG_NAME/node_modules ] || mkdir -p /tmp/cache/$PKG_NAME/node_modules

# 检测是否存在 last-commit-hash 文件（如果文件存在且为普通文件），如果有则读取其内容用于后续判断
test -f /tmp/cache/$PKG_NAME/last-commit-hash && last_commit_hash=$(cat /tmp/cache/$PKG_NAME/last-commit-hash)
if [ -z $last_commit_hash ]; then
    #  hash 长度为 0 ，则判定为有文件改变，需要执行构建操作
    echo "== /tmp/cache/$PKG_NAME/last-commit-hash not existed =="
    package_json_changed=0
    current_src_changed=0
else
    # 检查 commit 是否存在于当前分支中
    git branch -r --contains $last_commit_hash | grep -E $current_branch
    commit_usable=$?

    if [ $commit_usable == 0 ]; then
        # 查到 hash ，则和最后一次 commit 进行 diff 比较，判断是否需要执行构建操作
        echo "== last commit hash: $last_commit_hash =="
        git diff HEAD $last_commit_sha ./package.json | grep -q 'diff'
        package_json_changed=$?
        git diff HEAD $last_commit_hash ./src | grep -E 'diff'
        current_src_changed=$?
    else
        echo "last commit does not exist with the current branch";
        package_json_changed=0;
        common_src_changed=0
    fi

    # 输出各判断条件结果
    [ $package_json_changed == 0 ] && echo "** $PKG_NAME package.json changed **"
    [ $current_src_changed == 0 ] && echo "** $PKG_NAME src changed **"
fi

# 当 package.json 未改变时不安装依赖
if [ $package_json_changed == 1 ]; then
    echo "== package.json not changed, skip 'npm i' =="
else
    echo "== package.json changed, remove node_modules/* =="
    # 二次执行 npm i 会出现包安装不全的问题，尝试清空重装
    rm -rf ./node_modules/*
    echo "== npm i =="
    npm i --unsafe-perm || error_exit "** npm install failed **"
    # 保存一份 packge-lock 文件
    cp -f ./package-lock.json /tmp/cache/$PKG_NAME/package-lock.json
fi

# 无任何改变则跳过 build
if [ $current_src_changed == 1 ]; then
    echo "== skip build, copy last dist =="
    # 无需重新 build ，将上次保存的 dist 拷过来用于下一步的 docker 打包
    cp -rf /tmp/cache/$PKG_NAME/dist ./ || error_exit "** copy last dist failed **"
else
    # 建立 node_modules 软链，将挂载在磁盘上的文件链接到当前位置
    echo "== link node_modules =="
    ln -s /tmp/cache/node_modules/ ./

    echo "== start build =="
    # 执行指定打包
    npm run build || error_exit "** build failed **"

    # 保存一份 dist 目录，用于无需 build 时的 docker 构建
    cp -rf ./dist /tmp/cache/$PKG_NAME
fi

# 缓存当前次 git hash ，用于下次执行时检查代码是否发生指定变化
echo $(git rev-parse HEAD) >/tmp/cache/$PKG_NAME/last-commit-hash
