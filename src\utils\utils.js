import { Notification } from "element-ui";
import { errorHandler } from "@/utils";
import { appendQuery } from "@/utils/index";
import { get } from "lodash-es";
import router from "@/router";

export function notice(options) {
    Notification({
        duration: 2000,
        title: "错误",
        offset: 85,
        type: "error",
        ...options,
    });
}

/**
 * 该方法用于将有父子关系的数组转换成树形结构的数组
 * 接收一个具有父子关系的数组作为参数
 * 返回一个树形结构的数组
 */
export function translateDataToTree(data) {
    const map = {};
    const idMap = {};
    data.forEach(item => {
        if (!map[item.dicParentId]) {
            map[item.dicParentId] = [];
        }
        map[item.dicParentId].push(item);
        idMap[item.id] = item.dicParentId;
    });
    const topLevel = [];
    Object.keys(map).forEach(item => {
        if (!idMap[item]) {
            topLevel.push(item);
        }
    });
    //没有父节点的数据
    const parents = data.filter(
        value =>
            value.dicParentId === "undefined" ||
            value.dicParentId === null ||
            topLevel.includes(value.dicParentId.toString())
    );

    //有父节点的数据
    const children = data.filter(
        value =>
            value.dicParentId !== "undefined" &&
            value.dicParentId !== null &&
            !topLevel.includes(value.dicParentId.toString())
    );

    //定义转换方法的具体实现
    const translator = (parents, children) => {
        //遍历父节点数据
        parents.forEach(parent => {
            //遍历子节点数据
            children.forEach((current, index) => {
                //此时找到父节点对应的一个子节点
                if (current.dicParentId === parent.id) {
                    //对子节点数据进行深复制，这里只支持部分类型的数据深复制，对深复制不了解的童靴可以先去了解下深复制
                    const temp = JSON.parse(JSON.stringify(children));
                    //让当前子节点从temp中移除，temp作为新的子节点数据，这里是为了让递归时，子节点的遍历次数更少，如果父子关系的层级越多，越有利
                    temp.splice(index, 1);
                    //让当前子节点作为唯一的父节点，去递归查找其对应的子节点
                    translator([current], temp);
                    //把找到子节点放入父节点的children属性中
                    typeof parent.children !== "undefined"
                        ? parent.children.push(current)
                        : (parent.children = [current]);
                }
            });
        });
    };

    //调用转换方法
    translator(parents, children);

    //返回最终的结果
    return parents;
}

/**
 * 换算单位
 * @param str 换算字符串
 * @param unit 单位
 */
export function conversionUnit(str, unit, length = 3, isObj, endUnit, scale = 1000) {
    let arr = [];
    switch (unit) {
        // 带宽
        case "bps":
            arr = [
                {
                    unit: "Kbps",
                    number: 1000,
                },
                {
                    unit: "Mbps",
                    number: 1000,
                },
                {
                    unit: "Gbps",
                    number: 1000,
                },
                {
                    unit: "Tbps",
                    number: 1000,
                },
            ];
            if (scale === 1024) arr = arr.map(itm => ({
                unit: itm.unit.replace("bps", "ibps"),
                number: 1024
            }))
            break;
        // 流量
        case "byte":
            arr = [
                {
                    unit: "KB",
                    number: 1000,
                },
                {
                    unit: "MB",
                    number: 1000,
                },
                {
                    unit: "GB",
                    number: 1000,
                },
                {
                    unit: "TB",
                    number: 1000,
                },
            ];
            if (scale === 1024) arr = arr.map(itm => ({
                unit: itm.unit.replace("B", "iB"),
                number: 1024
            }))
            break;
        case "number":
            arr = [
                {
                    unit: "",
                    number: 1,
                },
                {
                    unit: "万",
                    number: 10000,
                },
                {
                    unit: "亿",
                    number: 10000,
                },
            ];
            break;
        case "m/s":
            arr = [
                {
                    unit: "Kb/s",
                    number: 1,
                },
                {
                    unit: "M/s",
                    number: 1024,
                },
            ];
            break;
    }
    let number = Number(str);
    let currentUnit = "";
    for (const item of arr) {
        currentUnit = item.unit;
        number = number / item.number;
        if (currentUnit === endUnit) {
            break;
        }
        if (!endUnit && parseInt(number).toString().length <= length) {
            break;
        }
    }
    if (isObj) {
        return {
            number: parseInt(number) === number ? number : parseFloat(number.toFixed(2)),
            unit: currentUnit,
        };
    }

    return parseInt(number) === number ? number + currentUnit : parseFloat(number.toFixed(2)) + currentUnit;
}

/**
 * 根据对象换算单位
 */
export function conversionUnitByObj({ str, unit, length = 6, isObj = true, endUnit }) {
    return conversionUnit(str, unit, length, isObj, endUnit);
}

/**
 * 根据数据动态换算（number）
 */
export function conversionUnitNumber(value) {
    return conversionUnit(value, "number", 4);
}

/**
 * 关键字匹配规则（从头开始匹配）
 * @param {匹配内容} val
 * @param {匹配关键字} keyword
 * @param {命中颜色} color
 * @returns
 */
export function keyRegExp(val = "", keyword, color = "#FA8B48") {
    val = `${val}`;
    if (keyword && val.indexOf(keyword) > -1) {
        return val.replace(keyword, `<font color="${color}">${keyword}</font>`);
    } else {
        return val;
    }
}

/**
 * 合并两个对象，并去除假值内容替换为真值数据
 * @param origin
 * @param newObj
 */
export function compactAssignObj(origin, newObj) {
    const obj = Object.assign({}, origin, newObj);
    Object.keys(obj).forEach(key => {
        if (!newObj[key]) {
            obj[key] = origin[key];
        }
    });
    return obj;
}

/**
 * 处理删除的回调
 * @param res
 */
export function handleDeleteCallback(res) {
    this.$message.success("删除成功！");
    if (
        this.total % this.queryParams.pageSize === 1 &&
        this.queryParams.pageIndex === parseInt(Math.ceil(this.total / this.queryParams.pageSize))
    ) {
        this.queryParams.pageIndex = this.queryParams.pageIndex - 1;
        this.getTableData();
    } else {
        this.getTableData();
    }
}

/**
 * 获取分销商管理的跳转目标地址
 */
export function getDistributorTargetURL(path) {
    const route = get(router, "currentRoute", {});
    const query = { workspaceId: get(route, "query.workspaceId") };
    // 正式环境
    if (process.env.NODE_ENV === "production") {
        const url = `/h5/ctaccessone/index.html#${path}`;
        return appendQuery(url, query);
    }

    const url = `/#${path}`;
    return appendQuery(url, query);
}

// 从数组中获取指定字段的最大值（用于生成合理的缩进规则）
export const getMaxFromList = (list, key) => {
    let max = 0;
    list?.map(item => +item[key]).forEach(num => (max = max > num ? max : num));
    return max;
}

/**
 * 处理condition赋值，空condition返回空对象，非空condition返回对应的condition
 * @param {*} list 包含mode和content的对象数组
 * @returns 处理后的condition
 */
export const conditionAssign = list => {
    const condition = {};

    list?.map(item => {
        if (item?.mode === "" ||
            item?.mode === null ||
            item?.mode === undefined ||
            item?.content === "" ||
            item?.content === null ||
            item?.content === undefined
        ) {
            return;
        } else {
            condition[item?.id] = [
                {
                    mode: item?.mode,
                    content: item?.content,
                }
            ]
        }
    })

    return condition
}

/**
 * 根据条件映射对列表项进行解构
 * 此函数旨在从condition_map中提取特定列表项的模式和内容，并将其应用到list数组的对应项上
 * @param {Object} condition_map - 包含列表项条件的映射
 * @param {Array} list - 需要进行解构操作的列表项数组
 * @returns {Array} - 返回经过解构操作后的列表项数组
 */
export const conditionDestruct = (condition_map, list) => {
    // 确保condition_map有值，如果为空则初始化为空对象
    condition_map = condition_map || {};

    // 检查condition_map是否有效且非空
    if (condition_map && Object.keys(condition_map).length) {
        // 遍历list数组中的每个项
        list.map(item => {
            // 检查当前项在condition_map中的条件是否存在，如果不存在则跳过当前项
            if (!condition_map[item?.id]) return;

            // 从condition_map中提取当前项的模式和内容，并应用到list数组的对应项上
            item.mode = condition_map[item?.id][0]?.mode;
            item.content = condition_map[item?.id][0]?.content;
        })
    }

    // 返回处理后的列表项数组
    return list
}

/**
 * 将表单prop转化为类名
 * @param prop 表单的prop，刚好表单的prop表示层级的关键字是.，这里用正则替换成-;以适配className的绑定
 */
export function transFormPropToClassName(prop) {
    return prop.replace(/\./g, "-");
}

/**
 * 将时间转为秒
 * @param {*} timeType
 * @param {*} time
 * @returns
 */
export const transferTimeType = (timeType, time) => {
    let ttl = time;

    if (timeType === "4") {
        ttl = ttl * 60;
    } else if (timeType === "3") {
        ttl = ttl * 60 * 60;
    } else if (timeType === "2") {
        ttl = ttl * 60 * 60 * 24;
    } else if (timeType === "5") {
        ttl = ttl * 1;
    }

    return ttl;
}

// 深度diff工具函数
export function deepDiff(obj1, obj2, path = '') {
    const differences = [];

    if (obj1 === obj2) {
        return differences;
    }

    if (obj1 === null || obj1 === undefined || obj2 === null || obj2 === undefined) {
        if (obj1 !== obj2) {
            differences.push({
                path: path || 'root',
                type: 'changed',
                oldValue: obj1,
                newValue: obj2
            });
        }
        return differences;
    }

    if (typeof obj1 !== typeof obj2) {
        differences.push({
            path: path || 'root',
            type: 'type_changed',
            oldValue: obj1,
            newValue: obj2
        });
        return differences;
    }

    if (typeof obj1 !== 'object') {
        if (obj1 !== obj2) {
            differences.push({
                path: path || 'root',
                type: 'changed',
                oldValue: obj1,
                newValue: obj2
            });
        }
        return differences;
    }

    if (Array.isArray(obj1) && Array.isArray(obj2)) {
        const maxLength = Math.max(obj1.length, obj2.length);
        for (let i = 0; i < maxLength; i++) {
            const currentPath = path ? `${path}[${i}]` : `[${i}]`;
            if (i >= obj1.length) {
                differences.push({
                    path: currentPath,
                    type: 'added',
                    newValue: obj2[i]
                });
            } else if (i >= obj2.length) {
                differences.push({
                    path: currentPath,
                    type: 'removed',
                    oldValue: obj1[i]
                });
            } else {
                differences.push(...deepDiff(obj1[i], obj2[i], currentPath));
            }
        }
        return differences;
    }

    if (Array.isArray(obj1) !== Array.isArray(obj2)) {
        differences.push({
            path: path || 'root',
            type: 'type_changed',
            oldValue: obj1,
            newValue: obj2
        });
        return differences;
    }

    const keys1 = Object.keys(obj1);
    const keys2 = Object.keys(obj2);
    const allKeys = new Set([...keys1, ...keys2]);

    for (const key of allKeys) {
        const currentPath = path ? `${path}.${key}` : key;
        if (!(key in obj1)) {
            differences.push({
                path: currentPath,
                type: 'added',
                newValue: obj2[key]
            });
        } else if (!(key in obj2)) {
            differences.push({
                path: currentPath,
                type: 'removed',
                oldValue: obj1[key]
            });
        } else {
            differences.push(...deepDiff(obj1[key], obj2[key], currentPath));
        }
    }

    return differences;
}
