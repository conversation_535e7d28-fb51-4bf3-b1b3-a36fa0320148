const _supportClassList = "classList" in document.body;

// 获取单个元素
const getEle = (selector: string, dom: Document | string | Element = document): Element | null => {
    let parent: Document | Element | null;
    if (typeof dom === "string") {
        parent = getEle(dom);
        if (!parent) return null;
    } else {
        parent = dom;
    }

    return (parent as HTMLElement).querySelector(selector);
};

// 获取多个元素
const getEles = (selector: string, dom: Document | string | Element = document) => {
    let parent: Document | Element | null;
    if (typeof dom === "string") {
        parent = getEle(dom);
        if (!parent) return null;
    } else {
        parent = dom;
    }

    return Array.from((parent as HTMLElement).querySelectorAll(selector));
};

// 是否存在某个类
const hasClass = (dom: Element, className: string) => {
    return _supportClassList ? dom.classList.contains(className) : dom.className.includes(className);
};

// 添加类
const addClass = (dom: Element, className: string) => {
    //已经存在，则直接返回
    if (hasClass(dom, className)) {
        return;
    }
    if (_supportClassList) {
        dom.classList.add(className);
    } else {
        let clazzName = dom.className;
        clazzName += ` ${className}`;
        // 去掉首尾多余空格，中间有多个空格的转为一个
        dom.className = clazzName.trim().replace(/\s+/g, " ");
    }
};

// 删除类
const removeClass = (dom: Element, className: string) => {
    const reg = new RegExp(`${className}\\s?`, "g");
    return _supportClassList
        ? dom.classList.remove(className)
        : (dom.className = dom.className.replace(reg, "").trim());
};

// 切换类
const toggleClass = (dom: Element, className: string) => {
    hasClass(dom, className) ? removeClass(dom, className) : addClass(dom, className);
};

// 事件监听，函数一次性执行
// 项目只支持到 ie 10，不需要做兼容处理
const on = (element: Element, event: any, handler: (this: Element, ev: any) => any) => {
    if (element && event && handler) {
        element.addEventListener(event, handler, false);
    }
};

// 是否是移动端环境
const isMobileEnv = () => {
    return window.document.documentElement.clientWidth < 768;
};

const toExportObj = {
    getEle,
    getEles,
    addClass,
    toggleClass,
    hasClass,
    removeClass,
    on,
    isMobileEnv,
};

export default toExportObj;
