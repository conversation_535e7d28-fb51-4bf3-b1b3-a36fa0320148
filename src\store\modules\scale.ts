import { VuexModule, Module, Action, Mutation, getModule } from "vuex-module-decorators";

import { ctFetch, getDomainProductCodeSet } from "../../utils";
import store from "../index";
import { ScaleUrl } from "@/config/url/basic";
import { statistics as IpaStatistics } from "@/config/url/ipa/statistics";
import { DomainModule } from "./domain";
import { getDomainAction } from "../config";
import { StatisticsModule } from "./statistics";

export interface ScaleState {
    scale: number;
}

@Module({ dynamic: true, store, name: "scale" })
class Scale extends VuexModule implements ScaleState {
    public scale = 1000;

    public productCodeScaleMap: { [key: string]: number } = {};

    @Mutation
    private SET_SCALE(scale: number) {
        this.scale = scale;
    }

    /**
     * 概览页获取进制
     */
    @Action
    public async GetScaleHome() {
        // 根据域名获取对应的产品类型
        const productList = DomainModule[getDomainAction("Home")].nativeList || [];
        const productType = getDomainProductCodeSet(productList) || "";

        // 如果已缓存了该产品类型的进制，则直接使用缓存值并返回
        if (this.productCodeScaleMap[productType]) {
            this.SET_SCALE(this.productCodeScaleMap[productType]);
            return;
        }

        // 如果没有productType，可以看成当前客户下没有域名，所以不需要获取进制
        if (!productType) {
            return;
        }

        const { scale = 0 } = await ctFetch<{
            scale: number;
        }>(ScaleUrl, {
            data: {
                productType,
            },
        });

        // 缓存该产品类型的进制
        this.productCodeScaleMap[productType] = scale;

        // 设置并应用进制
        this.SET_SCALE(scale);
    }

    /**
     * 统计分析获取进制
     */
    @Action
    public async GetScale(domainList: string[] = []) {
        let isAllDomain = false;
        const action = StatisticsModule.currentAction;
        if (!Array.isArray(domainList) || domainList.length === 0) {
            // 全部域名
            isAllDomain = true;
        }

        // 根据域名获取对应的产品类型
        const productList = DomainModule[action].nativeList?.filter(
            d => (domainList || []).includes(d.domain) || isAllDomain
        ) || [];

        const productType = getDomainProductCodeSet(productList) || "";

        if (this.productCodeScaleMap[productType]) {
            this.SET_SCALE(this.productCodeScaleMap[productType]);
            return;
        }

        // 如果没有productType，可以看成当前客户下没有域名，所以不需要获取进制
        if (!productType) {
            return;
        }

        const { scale = 0 } = await ctFetch<{
            scale: number;
        }>(ScaleUrl, {
            data: {
                productType,
            },
        });

        this.productCodeScaleMap[productType] = scale;

        this.SET_SCALE(scale);
    }

    // ipa 统计分析不需要获取进制，直接返回1000
    @Action
    public async GetIpaScale() {
        const key = "IPA_SCALE";
        if (this.productCodeScaleMap[key]) {
            this.SET_SCALE(this.productCodeScaleMap["IPA_SCALE"]);
            return;
        }

        const { result } = await ctFetch<{
            result: number;
        }>(IpaStatistics.getscale)

        this.SET_SCALE(result);
        this.productCodeScaleMap[key] = result;
    }
}

export const ScaleModule = getModule(Scale);
