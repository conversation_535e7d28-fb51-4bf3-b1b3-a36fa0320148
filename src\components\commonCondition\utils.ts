import i18n from "@/i18n";

/**
 * @description 获取condition输入框的placeholder
 */
export function getConditionContentPlaceholder(cacheModeOptions: { value: number }[], mode: number) {
    if (!cacheModeOptions.map(opt => opt.value).includes(mode)) return "";

    return mode === 0
        ? i18n.t("simpleForm.alogicCacheMixin.FileSuffix.0")
        : mode === 1
        ? i18n.t("simpleForm.alogicCacheMixin.FileSuffix.1")
        : i18n.t("simpleForm.alogicCacheMixin.FileSuffix.4");
}
