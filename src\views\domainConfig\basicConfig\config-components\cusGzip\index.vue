<template>
    <div class="ct-edit-wrapper">
        <el-form
            label-width="140px"
            label-position="left"
            :model="form"
            :rules="rules"
            ref="cusGzipForm"
            :disabled="!isEdit || !isService || isLockGzipConf"
        >
            <div>
              <el-form-item
                :label="$t('domain.detail.label62')"
                prop="cus_gzip"
                ref="cusGzip"
                class="ct-table-form-item"
            >
                <div class="ct-table-wrapper">
                    <el-table class="origin-table auto-table" :data="form.cus_gzip">
                        <el-table-column prop="type" :label="$t('domain.editPage.tip8')">
                            <template slot-scope="scope">
                                <el-form-item
                                    label=""
                                    :prop="`cus_gzip.` + scope.$index + `.type`"
                                    :rules="rules.type"
                                >
                                    <el-select
                                        v-model="scope.row.type"
                                        style="width:100%"
                                        @change="handleChange"
                                    >
                                        <el-option
                                            v-for="i in typeList"
                                            :key="i.value"
                                            :label="i.name"
                                            :value="i.value"
                                        ></el-option>
                                    </el-select>
                                </el-form-item>
                            </template>
                        </el-table-column>
                        <el-table-column prop="key" :label="$t('domain.editPage.tip9')">
                            <template slot="header">
                                <div class="flex-row-style">
                                    <span>{{ $t("domain.editPage.tip9") }}</span>
                                    <el-tooltip
                                        class="item"
                                        style="margin-left:4px"
                                        effect="dark"
                                        :content="$t('domain.editPage.placeholder6')"
                                        placement="top"
                                    >
                                        <i class="el-icon-question" />
                                    </el-tooltip>
                                </div>
                            </template>
                            <template slot-scope="scope">
                                <el-form-item
                                    label=""
                                    :prop="`cus_gzip.` + scope.$index + `.file_type`"
                                    :rules="rules.file_type"
                                >
                                    <el-tooltip placement="top" :content="$t('domain.editPage.placeholder7')">
                                        <el-input
                                            v-model.trim="scope.row.file_type"
                                            :placeholder="$t('domain.editPage.placeholder7')"
                                            class="input-style"
                                            @change="handleChange"
                                        ></el-input>
                                    </el-tooltip>
                                </el-form-item>
                            </template>
                        </el-table-column>
                        <el-table-column prop="key" :label="$t('domain.detail.label67')">
                            <template slot="header">
                                <div class="flex-row-style">
                                    <span>{{ $t("domain.detail.label67") }}</span>
                                    <el-tooltip
                                        class="item"
                                        style="margin-left:4px"
                                        effect="dark"
                                        :content="$t('domain.detail.tip56')"
                                        placement="top"
                                    >
                                        <i class="el-icon-question" />
                                    </el-tooltip>
                                </div>
                            </template>
                            <template slot-scope="scope">
                                <el-form-item
                                    label=""
                                    :prop="`cus_gzip.` + scope.$index + `.min_length`"
                                    :rules="rules.min_length"
                                >
                                    <el-input
                                        v-model="scope.row.min_length"
                                        :placeholder="$t('domain.detail.label67')"
                                        class="input-style"
                                        @change="handleChange"
                                    ></el-input>
                                </el-form-item>
                            </template>
                        </el-table-column>
                        <el-table-column :label="$t('domain.operate')" width="80">
                            <template slot-scope="scope">
                                <el-button
                                    type="text"
                                    @click="
                                        onOperator(
                                            scope.row,
                                            'delete',
                                            'cus_gzip',
                                            scope.$index
                                        )
                                    "
                                    >{{ $t("domain.delete") }}</el-button
                                >
                            </template>
                        </el-table-column>
                    </el-table>
                    <div class="flex-row-style button-box">
                        <el-button
                            class="btn"
                            type="text"
                            @click="onOperator(null, 'create', 'cus_gzip')"
                        >
                            + {{ addButtonText }}
                        </el-button>
                    </div>
                </div>
              </el-form-item>
            </div>

        </el-form>
    </div>
</template>

<script>
// import ctSvgIcon from "@/components/ctSvgIcon";
import componentMixin from "@/views/domainConfig/componentMixin";
import i18n from "@/i18n/index";
import { cloneDeep } from "lodash-es";

export default {
    name: "cusGzip",
    components: {
        // ctSvgIcon,
    },
    mixins: [componentMixin],
    props: {
        datas: Object,
        isLockGzipConf: Boolean,
    },
    data() {
      return {
        addButtonText: i18n.t("domain.editPage.label10"),
        currentType: "create",
        typeList: [
            { name: "gzip", value: 0 },
            { name: "brotli", value: 1 },
        ],
        form: {
            cus_gzip: [], // 文件压缩
        },
        rules: {
          type: [
            // { required: true, message: i18n.t("domain.detail.tip59"), trigger: "change" },
            {
                validator: (rule, value, callback) => {
                    if (this.isLockGzipConf) callback();
                    if (value === "" || value === null || value === undefined)
                        callback(new Error(this.$t("domain.detail.tip59")));
                    else callback();

                },
                trigger: ["blur", "change"],
            },
          ],
          file_type: [
            // { required: true, message: i18n.t("domain.detail.tip60"), trigger: "blur" },
            {
                validator: (rule, value, callback) => {
                    if (this.isLockGzipConf) callback();
                    if (value === "" || value === null || value === undefined)
                        callback(new Error(this.$t("domain.detail.tip60")));
                    else callback();

                },
                trigger: ["blur", "change"],
            },
          ],
          min_length: [
            // { required: true, message: i18n.t("domain.detail.tip57"), trigger: "blur" },
            {
                validator: (rule, value, callback) => {
                    const reg = /^((1000|10[01]\d|102[0-3])|[1-9]\d{0,2})[KBkb]$|^(?:[1-9]\d{0,4}|10[0-1]\d{3}|102[0-3]\d{2})[Mm]$/gm;
                    if (this.isLockGzipConf) callback();
                    if (value === "" || value === null || value === undefined) {
                        callback(new Error(i18n.t("domain.detail.tip57")));
                        return;
                    } else if (!reg.test(value)) {
                        callback(new Error(i18n.t("domain.detail.tip58")));
                        return;
                    } else {
                        callback();
                    }
                },
                trigger: ["blur", "change"],
            },
         ],
        },
      };
    },
    computed: {},
    watch: {
        "datas.cus_gzip": {
            deep: true,
            handler(val) {
                this.init(val);
            },
            immediate: true,
        },
    },
    methods: {
      init(v) {
          this.form.cus_gzip = cloneDeep(v);
      },
      handleChange() {
        this.$emit("onChange", this.form.cus_gzip);
      },
      async onOperator(row, currentType, tabName, i) {
        this.currentType = currentType;
        const getTime = new Date().getTime();

        if (currentType === "create") {
            const defaultFormMap = {
                cus_gzip: { type: 0, file_type: "", min_length: "1K", id: Date.now() + "" },
            };
            row = defaultFormMap[tabName];
        }
        if (currentType === "delete") {
            const msgMap = {
                cus_gzip: this.$t("domain.editPage.tip20"),
            };

            // 二次确认弹窗
            await this.$confirm(msgMap[tabName], this.$t("domain.delete"), {
                type: "warning",
            });
            this.form[tabName].splice(i, 1);

            this.$emit("onChange", this.form.cus_gzip);
        } else {
            this.form[tabName].push(row);
        }
      },
    },
};
</script>

<style lang="scss" scoped>
@import "@/views/domainConfig/commonStyle.scss";
.ct-edit-wrapper {
    ::v-deep {
        .el-form--label-left .el-form-item__label {
            text-align: right;
        }
    }
}
</style>
