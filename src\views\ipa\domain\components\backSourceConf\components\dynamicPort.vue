<template>
    <div>
        <div v-for="(item, key) in data" :key="getRenderKey(key, item)" class="port-row">
            <port-item
                :fields="fields"
                :form="item"
                :father-prop="`${fatherProp}.${key}`"
                :rules-map="rulesMap"
            />
            <!--  按钮区域-->
            <div>
                <el-button
                    icon="el-icon-plus"
                    type="primary"
                    plain
                    circle
                    style="padding:1px"
                    @click="handleAdd"
                />
                <el-button
                    v-if="data.length > 1"
                    icon="el-icon-minus"
                    type="primary"
                    plain
                    style="padding:1px"
                    circle
                    @click="handleDelete(key)"
                />
            </div>
        </div>
    </div>
</template>

<script>
import portItem from "./portItem.vue";
import { cloneDeep } from "lodash-es";

export  default {
    components: { portItem },
    props: {
        // 动态数组
        data: {
            type: Array,
            default: () => []
        },
        // 字段维护
        fields: {
            type: Array,
            default: () => []
        },
        // 用于校验element表单
        fatherProp: {
            type: String,
            default: ""
        },
        // 端口校验映射表
        rulesMap: {
            type: Object,
            default: () => {
                return {}
            }
        }
    },
    data() {
        return {
            templateData: {
                origin: "",
                req: ""
            }
        };
    },
    methods: {
        /**
         * 渲染key值，vue更新机制需要
         * @param key
         */
        getRenderKey(key, item ) {
            return key + (item.renderId || '').toString()
        },
        /**
         * 处理添加
         */
        handleAdd() {
            const info = cloneDeep(this.templateData);
            info.renderId = +new Date();
            this.data.push(info);
        },
        /**
         * 处理删除
         */
        handleDelete(index) {
            this.data.splice(index, 1);
        },
    },
}
</script>

<style lang="scss" scoped>
.port-row {
    display: flex;
    flex-direction: row;
}
</style>
