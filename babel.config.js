const asyncCatchClause = identifier => `
    // 忽略手动取消请求触发报错
    const checkAbort = err => {
        return err && err.raw && err.raw.name === "AbortError";
    };
    !checkAbort(${identifier}) && console.error(${identifier});
    if(this && !checkAbort(${identifier})) {
        // 各项目中 main.ts 和 store/index.ts 中关联预设方法
        if(typeof ${identifier} === 'string' && (this.$message || this.store?.$message)) {
            // 最简单的报错，通过 el-message 直接展示
            if(${identifier} !== 'cancel') {
                if(this.$message) {
                    this.$message.error(${identifier});
                } else if(this.store?.$message) {
                    this.store.$message.error(${identifier});
                }
            }
        } else if(this.$errorHandler) {
            // 复杂错误，如 请求报错、js执行报错
            this.$errorHandler(${identifier})
        } else if(this.store?.$errorHandler) {
            this.store?.$errorHandler(${identifier})
        } else {
            // 兜底的处理，如果没有预置的处理方式，则使用 $message.error 兜底
            var errMsg = ${identifier} && ${identifier}.data && ${identifier}.data.reason || ${identifier}.reason || ${identifier};
            if(${identifier} !== 'cancel' && typeof errMsg === 'string') {
                if(this.$message) {
                    this.$message.error(errMsg);
                } else if(this.store?.$message) {
                    this.store.$message.error(errMsg);
                }
            }
        }
    }
`;

module.exports = {
    env: {
        production: {
            presets: [
                [
                    "./babel/babel-preset-alogic",
                    {
                        asyncCatchClause,
                    },
                ],
            ],
        },
        development: {
            presets: [
                [
                    "./babel/babel-preset-alogic",
                    {
                        asyncCatchClause,
                    },
                ],
            ],
        },
        test: {
            presets: [
                "@babel/preset-env",
            ],
        },
    },
};
