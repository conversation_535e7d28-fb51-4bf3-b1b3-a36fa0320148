<template>
    <div class="ct-domain-panel" :class="{ fold: !isOpen }">
        <transition name="el-fade-in-linear">
            <div v-show="isOpen" class="domain-panel">
                <div class="flex-row-style space-between">
                    <span class="title-style">{{ "" }}</span>
                    <el-tooltip effect="dark" placement="top" content="侧边栏收起">
                        <ct-svg-icon
                            icon-class="indent"
                            class-name="indent-style"
                            @click="handleToggleFolder"
                        />
                    </el-tooltip>
                </div>
                <div class="flex-row-style space-between second-box">
                    <span>所有接入</span>
                    <el-button type="text" @click="handleAddDomain">
                        <ct-svg-icon icon-class="plus-circle" class-name="plus-circle" />
                        新增接入
                    </el-button>
                </div>
                <!-- 过滤面板-->
                <component
                    :is="currentFilterView"
                    :current-module="currentModule"
                    @form-change="handleFormChange"
                />
                <!-- 域名列表-->
                <div v-loading="domainLoading" class="domain-list-box">
                    <el-scrollbar
                        ref="scrollbar"
                        style="margin: 0;height: 100%;"
                        view-class="no-padding-wrap-scrollbar"
                    >
                        <template v-if="tableData.length && !domainLoading">
                            <div
                                v-for="(item, key) in tableData"
                                :key="key"
                                class="flex-row-style space-between domain-item"
                                :class="{ 'domain-item-active': currentDomain === item.domain }"
                                @click="handleChangeDomainConfig(item)"
                            >
                                <el-tooltip effect="dark" :content="item.domain" placement="left">
                                    <p class="domain">{{ item.domain }}</p>
                                </el-tooltip>
                                <el-tag :type="getStatusType(item)">
                                    {{ renderStatusText(item) }}
                                </el-tag>
                            </div>
                        </template>
                        <ct-empty v-else class="empty-column">
                            <div slot="description">
                                <div class="common-mb">暂无域名数据</div>
                                <el-button v-if="!domainListTotal" type="primary" @click="handleAddDomain">
                                    去新增
                                </el-button>
                            </div>
                        </ct-empty>
                    </el-scrollbar>
                </div>
            </div>
        </transition>
        <el-tooltip v-if="!isOpen" effect="dark" placement="top" :content="foldText">
            <ct-svg-icon
                icon-class="indent"
                class-name="indent-style folder-style"
                @click="handleToggleFolder"
            />
        </el-tooltip>
    </div>
</template>

<script>
import ipaComponent from "./components/ipaComponent";
import ctSvgIcon from "@/components/ctSvgIcon";
import ctEmpty from "@/components/ctEmpty";
import { SecurityAbilityModule } from "@/store/modules/securityAbility";
import { get } from "lodash-es";
import { cloneDeep } from "lodash-es";
import { handleLeaveModule } from "@/utils/common.js";

export default {
    name: "index",
    components: {
        ctSvgIcon,
        ctEmpty,
        ipaComponent,
    },
    props: {
        isOpen: {
            type: Boolean,
            default: true,
        },
        // 当前模块
        currentModule: {
            type: String,
            default: "",
        },
        domain: String,
    },
    data() {
        return {
            tableData: [],
            currentForm: {
                status: "",
                access_mode: 0,
                domain: "",
                inst_name: "",
            },
            statusLabel(status) {
                let label = "";
                switch (status) {
                    case 1:
                        label = "审核中";
                        break;
                    case 2:
                        label = "审核成功";
                        break;
                    case 3:
                        label = "配置中";
                        break;
                    case 4:
                        label = "已启用";
                        break;
                    case 5:
                        label = "停止中";
                        break;
                    case 6:
                        label = "已停止";
                        break;
                    case 7:
                        label = "删除中";
                        break;
                    case 8:
                        label = "已删除";
                        break;
                    case 9:
                        label = "审核失败";
                        break;
                    case 10:
                        label = "配置失败";
                        break;
                    case 11:
                        label = "停止失败";
                        break;
                    case 12:
                        label = "删除失败";
                        break;
                }
                return label;
            },
        };
    },
    computed: {
        currentFilterView() {
            return "ipaComponent";
        },
        domainLoading() {
            return SecurityAbilityModule.securityDomainLoading;
        },
        domainList() {
            return SecurityAbilityModule.securityDomainList;
        },
        currentDomain() {
            const info = SecurityAbilityModule.securityBasicDomainInfo;
            return get(info, "domain") || this.$route.query.domain;
        },
        // 收缩按钮提示语
        foldText() {
            const domain = SecurityAbilityModule.ipaSecurityDomain;
            return `侧边栏打开\n${domain || ""}`;
        },
        // 域名列表总数
        domainListTotal() {
            return SecurityAbilityModule.securityDomainTotal;
        },
        domainOrderStatus() {
            return SecurityAbilityModule.domainOrderStatus;
        },
    },
    watch: {
        domainList: {
            async handler(val) {
                if (val && val?.length > 0) {
                    this.tableData = cloneDeep(val);
                    this.initBasicData(val[0]);
                } else {
                    this.tableData = []
                }
            },
            immediate: true,
        },
        currentForm: {
            async handler(val) {
                if (!val) return;
                this.handleFilterTableData(val);
            },
            deep: true,
            immediate: true,
        },
    },
    mounted() {
        this.initBasicData();
        this.initDomainStatusData();
        this.initDomainListData();
    },
    methods: {
        /**
         * 初始化基础信息
         */
        async initBasicData(param) {
            const data = this.domainList?.find(itm => itm.domain === this.domain) || param;
            SecurityAbilityModule.SET_SECURITY_BASIC_DOMAIN_INFO({
                domain: data?.domain,
                status: data?.status,
                insert_date: data?.insert_date,
                cname: data?.cname,
                defenseCname: data?.defenseCname,
                area_scope: data?.area_scope,
                access_mode: data?.access_mode,
                inst_name: data?.inst_name,
                virtual_domain: data?.virtual_domain,
            });
        },
        async initDomainListData() {
            const data = {
                access_mode: 0,
            }
            await SecurityAbilityModule.getSecurityDomainList(data);
            this.scrollToHighLightElement();
        },
        async initDomainStatusData() {
            const params = {
                account_id: this.$store.state.user.userInfo.userId,
            };
            await SecurityAbilityModule.getDomainListPanelData(params);
        },
        /**
         * 滚动至高亮元素
         */
        scrollToHighLightElement() {
            this.$nextTick(() => {
                // 自动滚至当前高亮域名
                const highlightEle = document.querySelector(".domain-item-active");
                if (!highlightEle) {
                    return;
                }

                highlightEle.scrollIntoView({
                    behavior: "smooth", // 定义动画过渡效果
                    block: "start", // 定义垂直方向的对齐
                    inline: "nearest", // 定义水平方向的对齐
                });
            });
        },
        /**
         * 渲染统计数据
         */
        renderCountNumber(prop) {
            const module = get(this.moduleKeyMap, this.currentModule);
            if (!module) {
                return 0;
            }

            const joinProp = module + prop;
            return get(this.numberTotalData, joinProp, 0) || 0;
        },
        /**
         * 获取状态标签类型
         */
        getStatusType(item) {
            const map = {
                3: "info",
                4: "blue",
                6: "danger",
            };
            const status = item.status;
            return get(map, status, "info") || "info";
        },
        /**
         * 渲染状态文案
         */
        renderStatusText(item) {
            const status = item?.status;
            return this.statusLabel(status);
        },
        /**
         * 改变当前配置域名数据
         */
        async handleChangeDomainConfig(item) {
            // 切换域名时的事件
            if (SecurityAbilityModule.isIpaFormChange) {
                await handleLeaveModule();
            }

            this.handleSetDomainInfo(item);
        },
        /**
         * 设置域名信息
         * @param item
         */
        handleSetDomainInfo(item) {
            SecurityAbilityModule.SET_SECURITY_BASIC_DOMAIN_INFO(item);
        },
        /**
         * 处理改变折叠状态
         */
        handleToggleFolder() {
            this.$emit("update:isOpen", !this.isOpen);
        },
        /**
         * 处理添加域名
         */
        handleAddDomain() {
            if (!this.domainOrderStatus) {
                this.$alert("您尚未开通边缘接入产品，请完成订购后尝试新增域名。", "提示", {
                    confirmButtonText: "确定",
                });
                return;
            }
            // this.$emit("add-domain", true);
            this.$router.push({ name: "domain.create" });
        },
        /**
         * 处理面板参数变化
         */
        async handleFormChange(form) {
            this.currentForm = form;
            if (form?.domain === undefined) {
                delete form?.domain;
            }
            if (form?.inst_name === undefined) {
                delete form?.inst_name;
            }

            form.access_mode = form?.domain ? 0 : form?.inst_name ? 2 : 0

            if (!form?.status) {
                delete form?.status;
                await SecurityAbilityModule.getSecurityDomainList(form);

                this.handleFilterTableData(form);
            }
            // form.access_mode = form?.domain ? 0 : form?.inst_name ? 2 : 0
            // SecurityAbilityModule.getSecurityDomainList(form);
        },
        /**
         * 处理过滤表格数据
         * @param val 查询参数表单
         */
        handleFilterTableData(val) {
            let tableData = cloneDeep(this.domainList);
            // 接入方式
            if (val?.access_mode) {
                tableData = tableData.filter(item => item.access_mode === val.access_mode);
                const key = val.access_mode === 2 ? "inst_name" : "domain";
                if (val?.[key]) {
                    tableData = tableData.filter(item => item[key].includes(val[key]));
                }
            } else {
                if (val?.domain) {
                    tableData = tableData.filter(item => item.domain.includes(val.domain));
                }
            }

            if (val?.status) {
                tableData = this.domainList?.filter(item => {
                    return item?.status === val?.status;
                });
            }
            this.$set(this, "tableData", tableData);
        },
        /**
         * 处理过滤数据
         * @param item
         */
        handleChangeFilterData(item) {
            const value = item.value;
            if (!this.$refs.filterBox) {
                return;
            }

            const prop = get(this.moduleKeyMap, this.currentModule) + "Act";

            this.$refs.filterBox.currentType = prop;
            this.$refs.filterBox.formInside[prop] = value;
            this.$refs.filterBox.handleChange();
        },
    },
};
</script>

<style scoped lang="scss">
@import "./index.scss";
</style>
