<template>
    <batch-edit-checked v-model="checked" :disabled="isHttpProtocol">
        <el-form label-width="140px" label-position="right" :model="form" ref="formRef" :disabled="!checked">
            <el-form-item :label="$t('domain.create.originEncodeSelf')" prop="proxy_gmssl_mode">
                <el-select
                    v-model="form.proxy_gmssl_mode"
                    :placeholder="$t('domain.create.originEncodeSelfPlaceholder')"
                    style="width: 395px"
                    clearable
                    :disabled="isHttpProtocol"
                >
                    <el-option :label="$t('domain.create.proxyGmsslModeList.0')" value="on" />
                    <el-option :label="$t('domain.create.proxyGmsslModeList.1')" value="off" />
                    <el-option :label="$t('domain.create.proxyGmsslModeList.2')" value="follow" />
                </el-select>
            </el-form-item>
        </el-form>
    </batch-edit-checked>
</template>

<script lang="ts">
import { Component, Mixins, Watch } from "vue-property-decorator";
import BatchItemMixin from "../mixins/batch.mixin";

@Component({
    name: "BatchEditOriginProxyGmsslMode",
})
export default class BatchEditOriginProxyGmsslMode extends Mixins(BatchItemMixin) {
    checked = false;
    form = {
        proxy_gmssl_mode: "",
    };
    protocolData = {
        backorigin_protocol: "http",
        checked: false,
    };

    get isHttpProtocol() {
        return this.protocolData.backorigin_protocol === "http" && this.protocolData.checked;
    }

    @Watch("isHttpProtocol")
    onIsHttpProtocolChange(value: boolean) {
        if (!value) return;
        this.form.proxy_gmssl_mode = "";
        this.checked = false;
    }

    mounted() {
        this.$ctBus.$on(
            "batchEdit:backorigin_protocol",
            (data: { backorigin_protocol: string; checked: boolean }) => {
                this.protocolData = data;
                // 回源协议切换后清空加密算法以保持开关行为一致
                this.form.proxy_gmssl_mode = "";
            }
        );
    }

    beforeDestroy() {
        this.$ctBus.$off("batchEdit:backorigin_protocol");
    }

    get formData() {
        // 回源加密算法
        return {
            proxy_gmssl_mode: this.form.proxy_gmssl_mode,
        };
    }
}
</script>
