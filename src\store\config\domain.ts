/*
 * @Description: 用于域名选项列表（store/domain），区别于域名管理的 domain
 * @Author: wang yuegong
 */
export enum DomainActionEnum {
    "Domain" = "c_domain", // 域名管理
    "Order" = "c_order", // 工单管理
    "Data" = "c_data", // 统计分析、日志下载
    "Refresh" = "c_refresh", // 刷新预取
    "Review" = "cdn_review", // 内容审核

    // ctiam 使用的权限码-域名
    "Home" = "ctcdn:overview:viewList", // 概览
    "DomainLog" = "ctcdn:domainManage:logView", // 域名管理-操作日志
    "Label" = "ctcdn:domainManage:labelViewList", // 域名管理-标签管理
    "Log" = "ctcdn:logDownload:viewList", // 日志下载
    "ContentAuditStatistics" = "ctcdn:contentAudit:identificationViewList", // 内容审核-审核分析
    "ContentAuditViolations" = "ctcdn:contentAudit:blockViewList", // 内容审核-违规图片
    "ContentAuditLog" = "ctcdn:contentAudit:logViewList", // 内容审核-审核日志

    // ctiam 统计分析
    "BandwidthFlow" = "ctcdn:statisticAnalysis:cdnUsageBandwidthFlow", // 统计分析-带宽流量
    "BandwidthFlowWhole" = "ctcdn:statisticAnalysis:icdnUsageBandwidthFlow", // 统计分析-全站带宽流量
    "Miss" = "ctcdn:statisticAnalysis:cdnUsageMissBandwidthFlow", // 统计分析-缓存命中率
    "MissWhole" = "ctcdn:statisticAnalysis:icdnUsageMissBandwidthFlow", // 统计分析-全站缓存命中率
    "Request" = "ctcdn:statisticAnalysis:cdnUsageRequest", // 统计分析-请求量
    "RequestWhole" = "ctcdn:statisticAnalysis:icdnUsageRequest", // 统计分析-全站请求量
    "Hit" = "ctcdn:statisticAnalysis:cdnUsageHitRate", // 统计分析-命中率
    "HitWhole" = "ctcdn:statisticAnalysis:icdnUsageHitRate", // 统计分析-命中率
    "StatusCode" = "ctcdn:statisticAnalysis:cdnUsageStatusCode", // 统计分析-状态码
    "StatusCodeWhole" = "ctcdn:statisticAnalysis:icdnUsageStatusCode", // 统计分析-全站状态码
    "BackToOriginStatusCode" = "ctcdn:statisticAnalysis:cdnUsageMissStatusCode", // 统计分析-回源状态码
    "BackToOriginStatusCodeWhole" = "ctcdn:statisticAnalysis:icdnUsageMissStatusCode", // 统计分析-全站回源状态码
    "PvUv" = "ctcdn:statisticAnalysis:cdnUsagePvUv", // 统计分析-PV/UV
    "PvUvWhole" = "ctcdn:statisticAnalysis:icdnUsagePvUv", // 统计分析-全站PV/UV
    "Provider" = "ctcdn:statisticAnalysis:cdnUsageAreaIsp", // 统计分析-运营商
    "ProviderWhole" = "ctcdn:statisticAnalysis:icdnUsageAreaIsp", // 统计分析-全站运营商
    "DownloadSpeed" = "ctcdn:statisticAnalysis:cdnUsageDownloadSpeed", // 统计分析-下载速度
    "PrivateNetworkAccelerator" = "ctcdn:statisticAnalysis:cdnUsagePremiumNetwork", // 统计分析-高性能网络
    "PrivateNetworkAcceleratorWhole" = "ctcdn:statisticAnalysis:icdnUsagePremiumNetwork", // 统计分析-全站高性能网络全站
    "HotUrl" = "ctcdn:statisticAnalysis:cdnTopUrl", // 统计分析-热门URL
    "HotUrlWhole" = "ctcdn:statisticAnalysis:icdnTopUrl", // 统计分析-全站热门URL
    "HotUrlMiss" = "ctcdn:statisticAnalysis:cdnTopMissUrl", // 统计分析-热门URL缓存命中率
    "HotUrlMissWhole" = "ctcdn:statisticAnalysis:icdnTopMissUrl", // 统计分析-全站热门URL缓存命中率
    "HotReferer" = "ctcdn:statisticAnalysis:cdnTopReferer", // 统计分析-热门Referer
    "HotRefererWhole" = "ctcdn:statisticAnalysis:icdnTopReferer", // 统计分析-全站热门Referer
    "DomainRank" = "ctcdn:statisticAnalysis:cdnTopDomain", // 统计分析-域名排行
    "DomainRankWhole" = "ctcdn:statisticAnalysis:icdnTopDomain", // 统计分析-全站域名排行
    "TopIp" = "ctcdn:statisticAnalysis:cdnTopIp", // 统计分析-Top IP
    "TopIpWhole" = "ctcdn:statisticAnalysis:icdnTopIp", // 统计分析-全站Top IP
    "User" = "ctcdn:statisticAnalysis:cdnUser", // 统计分析-用户分析
    "UserWhole" = "ctcdn:statisticAnalysis:icdnUser", // 统计分析-全站用户分析

    // 报表
    "Report" = "ctcdn:reportManagement:reportViewList", // 报表

    // 后续如果需要定义Aone的权限码，参考下方的命名格式，取用时通过getDomainAction方法获取，不直接使用
    // "AoneHome" = "accessone:overview:viewList", // 概览
}

export enum CtiamActionEnum {
    // ctiam 使用的权限码-标签
    "Label" = "ctcdn:domainManage:labelViewList", // 域名管理-标签管理
    "DomainListLabel" = "ctcdn:domainManage:domainViewList", // 域名管理-域名列表
    "RefreshPreloadLabel" = "ctcdn:refreshPreload:create", // 刷新预取

    "AoneLabel" = "accessone:domainManage:labelViewList", // 域名管理-标签管理
    "AoneDomainListLabel" = "accessone:domainManage:domainViewList", // 域名管理-域名列表
    "AoneRefreshPreloadLabel" = "accessone:refreshPreload:create", // 刷新预取
}

export enum CtiamButtonEnum {
    overviewStatisticMore = "ctcdn:statisticAnalysis:cdnUsageBandwidthFlow",
    overviewCertCount = "ctcdn:certMange:certViewList",
    overviewDomainManage = "ctcdn:domainManage:domainViewList",
    overviewDomainAdd = "ctcdn:domainManage:domainSingleCreate",
    overviewRefresh = "ctcdn:refreshPreload:viewList",
    cdnPackageSubscribe = "ctcdn:overview:cdnPackageSubscribe",
    icdnPackageSubscribe = "ctcdn:overview:icdnPackageSubscribe",
    domainSingleCreate = "ctcdn:domainManage:domainSingleCreate",
    domainBatchCreate = "ctcdn:domainManage:domainBatchCreate",
    domainSingleModify = "ctcdn:domainManage:domainSingleModify",
    domainBatchModify = "ctcdn:domainManage:domainBatchModify",
    domainDetail = "ctcdn:domainManage:domainView",
    certCreate = "ctcdn:certMange:certCreate",
    certModify = "ctcdn:certMange:certModify",
    certBind = "ctcdn:certMange:certBind",
    logBatchDownload = "ctcdn:logDownload:BathDownload",
    logDownload = "ctcdn:logDownload:download",
    refreshCreate = "ctcdn:refreshPreload:create",
    reportAdd = "ctcdn:reportManagement:reportAdd",
    reportEdit = "ctcdn:reportManagement:reportEdit",
    labelView = "ctcdn:domainManage:labelView",
    udfDictView = "ctcdn:udf:dictView",
    cdnSubscribe = "ctcdn:billing:cdnSubscribe",
    cdnUnsubscribe = "ctcdn:billing:cdnUnsubscribe",
    cdnChangeBillingMethod = "ctcdn:billing:cdnChangeBillingMethod",
    cdnChangeAccelerationRegion = "ctcdn:billing:cdnChangeAccelerationRegion",
    cdnAddOrReduceContentAudit = "ctcdn:billing:cdnAddOrRemoveContentAudit",
    cdnAddOrReducePremiumNetworkService = "ctcdn:billing:cdnAddOrRemovePremiumNetworkService",
    billingCdnPackageSubscribe = "ctcdn:billing:cdnPackageSubscribe",
    cdnPackageUnsubscribe = "ctcdn:billing:cdnPackageUnsubscribe",
    icdnSubscribe = "ctcdn:billing:icdnSubscribe",
    icdnUnsubscribe = "ctcdn:billing:icdnUnsubscribe",
    icdnChangeBillingMethod = "ctcdn:billing:icdnChangeBillingMethod",
    icdnChangeAccelerationRegion = "ctcdn:billing:icdnChangeAccelerationRegion",
    icdnAddOrReducePremiumNetworkService = "ctcdn:billing:icdnAddOrRemovePremiumNetworkService",
    billingIcdnPackageSubscribe = "ctcdn:billing:icdnPackageSubscribe",
    icdnPackageUnsubscribe = "ctcdn:billing:icdnPackageUnsubscribe",
    billingCdnEdgeFunction = "ctcdn:billing:cdnAddOrRemoveBosonFaaS",
    billingIcdnEdgeFunction = "ctcdn:billing:icdnAddOrRemoveBosonFaaS",
    // 域名管理-域名列表
    domainListExport = "ctcdn:domainManage:domainViewList",
    domainListChangeAcceArea = "ctcdn:domainManage:domainSingleModify",
    domainListChangeAcceType = "ctcdn:domainManage:domainSingleModify",
    domainListIpv6 = "ctcdn:domainManage:domainSingleModify",
    domainListPrivateNetwork = "ctcdn:domainManage:domainSingleModify",
    domainListLabel = "ctcdn:domainManage:domainLabel",
    domainListOperation = "ctcdn:domainManage:domainSingleModify",
    domainListDisable = "ctcdn:domainManage:domainStop",
    domainListEnable = "ctcdn:domainManage:domainStart",
    domainListDelete = "ctcdn:domainManage:domainDelete",
    // 域名管理-标签管理
    labelListAdd = "ctcdn:domainManage:labelCreate",
    labelListEdit = "ctcdn:domainManage:labelModify",
    labelListDelete = "ctcdn:domainManage:labelOrGroupDelete",
    labelListBatchDelete = "ctcdn:domainManage:labelOrGroupBatchDelete",
    // 证书管理-证书列表
    certListDetail = "ctcdn:certMange:certView",
    certListDelete = "ctcdn:certMange:certDelete",
    certHistory = "ctcdn:certMange:deploymentHistory",
    // UDFScript-全局字典
    udfDictAdd = "ctcdn:udf:dictCreate",
    udfDictEdit = "ctcdn:udf:dictModify",
    udfDictDelete = "ctcdn:udf:dictDelete",
    udfDictRetry = "ctcdn:udf:dictRetry",
    // UDFScript-全局task脚本
    udfTaskAdd = "ctcdn:udf:taskCreate",
    udfTaskView = "ctcdn:udf:taskView",
    udfTaskEdit = "ctcdn:udf:taskModify",
    udfTaskDelete = "ctcdn:udf:taskDelete",
    udfTaskRetry = "ctcdn:udf:taskRetry",
    // UDFScript-业务脚本
    udfBussinessAdd = "ctcdn:udf:scriptCreate",
    udfBussinessView = "ctcdn:udf:scriptView",
    udfBussinessEdit = "ctcdn:udf:scriptModify",
    udfBussinessDelete = "ctcdn:udf:scriptDelete",
    udfBussinessRetry = "ctcdn:udf:scriptRetryFail",
    udfBussinessRollback = "ctcdn:udf:scriptStageRollback",
    udfBussinessDomainList = "ctcdn:domainManage:domainViewList",
    udfBussinessPublish = "ctcdn:udf:scriptStageToProduction",
    // 报表管理
    reportManagementOperation = "ctcdn:reportManagement:reportEnableOrDisable",
    reportManagementDelete = "ctcdn:reportManagement:reportDelete",
    // IP集管理
    ipSetListView = "ctcdn:ipSetManagement:ipSetListView",
    ipSetListEdit = "ctcdn:ipSetManagement:ipSetListEdit",
    ipSetListDetail = "ctcdn:ipSetManagement:ipSetListDetail",
    ipSetListDelete = "ctcdn:ipSetManagement:ipSetListDelete",
    ipSetListDownload = "ctcdn:ipSetManagement:ipSetListDownload",
    ipSetListBindDomain = "ctcdn:ipSetManagement:ipSetListBindDomain",
    ipSetListRetry = "ctcdn:ipSetManagement:ipSetListRetry",
    ipSetListAdd = "ctcdn:ipSetManagement:ipSetListAdd",
    ipSetUsageList = "ctcdn:ipSetManagement:ipSetUsageList",
    ipSetUsageDownload = "ctcdn:ipSetManagement:ipSetUsageDownload",
}

export type DomainActionKey = keyof typeof DomainActionEnum;
export type CtiamActionKey = keyof typeof CtiamActionEnum;
export type CtiamButtonActionKey = keyof typeof CtiamButtonEnum;

/**
 * 获取域名操作Action，根据当前环境返回对应的域名操作类型
 *
 * @param key 域名操作类型的键
 * @returns 对应的域名操作类型
 */
export function getDomainAction(key: DomainActionKey): DomainActionEnum {
    if (window.__POWERED_BY_QIANKUN__) return DomainActionEnum[`Aone${key}` as DomainActionKey];
    return DomainActionEnum[key];
}

/**
 * 获取非域名操作的Action
 *
 * @param key 域名操作类型的键
 * @returns 对应的域名操作类型
 */
export function getCtiamAction(key: CtiamActionKey): CtiamActionEnum {
    if (window.__POWERED_BY_QIANKUN__) return CtiamActionEnum[`Aone${key}` as CtiamActionKey];
    return CtiamActionEnum[key];
}

export function GetCtiamButtonAction(key: CtiamButtonActionKey): CtiamButtonEnum {
    if (window.__POWERED_BY_QIANKUN__) return CtiamButtonEnum[`Aone${key}` as CtiamButtonActionKey];
    return CtiamButtonEnum[key];
}
