<template>
    <brm-box
        :type="preLabelType"
        :pre-label="preLabel"
        :value="preLabelValueInside"
        :list="preLabelList"
        @change="handlePreLabelChange"
    >
        <el-select v-model="valueInside" v-bind="$attrs" v-on="$listeners" @change="handleChange">
            <slot></slot>
        </el-select>
    </brm-box>
</template>

<script>
import brmBox from "@/components/brmBox";

export default {
    components: {
        brmBox,
    },
    model: {
        prop: "value",
        event: "handle-change",
    },
    props: {
        value: {
            type: [String, Number, Array],
            required: true,
            default: "",
        },
        preLabel: {
            type: String,
            required: false,
            default: "",
        },
        preLabelType: {
            type: String,
            required: false,
            default: "",
        },
        preLabelValue: {
            type: [String, Number, Array],
            required: false,
            default: "",
        },
        preLabelList: {
            type: Array,
            required: false,
            default: () => [],
        },
        preLabelWidth: {
            type: String,
            required: false,
            default: "80px",
        },
    },
    data() {
        return {
            valueInside: "",
            preLabelValueInside: "",
        };
    },
    watch: {
        value: {
            handler(val) {
                this.valueInside = val;
            },
            immediate: true,
        },
        preLabelValue: {
            handler(val) {
                this.preLabelValueInside = val;
            },
            immediate: true,
        },
    },
    methods: {
        handleChange(val) {
            this.$emit("handle-change", val);
        },
        handlePreLabelChange(val) {
            this.$emit("update:preLabelValue", val);
        },
    },
};
</script>

<style scoped lang="scss"></style>
