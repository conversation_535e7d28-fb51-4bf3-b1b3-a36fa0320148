<template>
    <el-dialog
        title="变更加速区域"
        :visible.sync="dialogShow"
        :show-close="false"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
    >
        <div v-loading="loading" class="change-acce-area">
            <el-form :model="form" label-width="120px" label-position="left">
                <el-form-item label="加速区域：">
                    <el-radio-group v-model="form.area" class="area-radio" :disabled="!isAllowChange">
                        <el-radio v-for="item in areaList" :key="item.value" :label="item.value">
                            {{ item.label }}
                        </el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="提示：">
                    <div class="tip-box">
                        <!-- 允许变动的提示语-->
                        <template v-if="isAllowChange">
                            <div>
                                1、不同加速区域对应不同资费标准，加速区域包含中国内地时，域名请先完成在中国大陆的ICP备案，同时完成公安网的备案。
                            </div>
                            <div>
                                2、修改加速区域可能会引起回源IP变更，如果您的源站有回源IP白名单限制，请通过
                                <el-button type="text" @click="openNewPage(workOrderUrl)">
                                    【客户服务工单】
                                </el-button>
                                提交接收回源白名单邮箱（支持多个邮箱），收到天翼云CDN+团队发送的回源白名单邮件并在源站加白完成后，再到客户控制台变更加速区域。
                            </div>
                        </template>
                        <!-- 不允许变动的提示语-->
                        <template v-else-if="isDisabledChange">
                            <div>
                                <template v-if="currentDataProp === '1,1,0'">
                                    1、您当前未开通边缘接入全球（不含中国内地）套餐或全球套餐，不能变更加速区域。
                                    如有需要，可通过
                                    <el-button type="text" @click="openNewPage(changePackageGuide)">
                                        【变更套餐指引】
                                    </el-button>
                                    开通边缘接入全球（不含中国内地）套餐或全球套餐。
                                </template>
                                <template v-if="currentDataProp === '2,2,0'">
                                    1、您当前未开通边缘接入中国内地套餐和全球套餐，不能变更加速区域。
                                    如有需要，可通过
                                    <el-button type="text" @click="openNewPage(changePackageGuide)">
                                        【变更套餐指引】
                                    </el-button>
                                    开通边缘接入中国内地套餐或全球套餐。
                                </template>
                            </div>
                            <div>2、加速区域包含中国内地时，加速域名请先完成在中国大陆的ICP备案，同时完成公安网备案。</div>
                        </template>
                        <!-- 保底提示语，不在产品需求表中的情况-->
                        <template v-else>
                            <div>
                                域名套餐类型和原加速区域不符，请通过
                                <el-button type="text" @click="openNewPage(workOrderUrl)">
                                    【客户服务工单】
                                </el-button>
                                提交变更加速区域的需求。
                            </div>
                        </template>
                    </div>
                </el-form-item>
            </el-form>
        </div>
        <div slot="footer">
            <el-button @click="handleCancel">取消</el-button>
            <el-button
                type="primary"
                :loading="submitLoading"
                :disabled="isSubmitDisabled"
                @click="handleSubmit"
            >
                确定
            </el-button>
        </div>
    </el-dialog>
</template>

<script>
import { SecurityAbilityModule } from "@/store/modules/securityAbility";
import { nUserModule } from "@/store/modules/nuser";
import { DomainUrl } from "@/config/url/ipa/domain";
import { cloneDeep, get, isEqual } from "lodash-es";

export default  {
    data() {
        return {
            currentData: null,
            dialogShow: false,

            // 初始化加载
            loading: false,
            originForm: null,
            form: {
                area: "1",
            },
            originScope: "1", // 源站加速区域

            areaList: [
                { label: "中国内地", value: 1 },
                { label: "全球（不含中国内地）", value: 2 },
                { label: "全球", value: 3 },
            ],

            submitLoading: false, // 提交加载器
        }
    },
    computed: {
        // 当前用户套餐类型
        currentPackage() {
            const billingInfos = SecurityAbilityModule.billingInfos || {};
            const map = {
                1: "mainland",
                2: "overseas",
                3: "global"
            };

            const packageData = Object.entries(map).reduce((data, [key, prop]) => {
                if (get(billingInfos, prop)) {
                    data = key;
                }

                return data;
            }, "");

            return Number(packageData);
        },
        // 当前数据标识符
        currentDataProp() {
            return `${this.currentPackage},${this.form.area},${this.originScope}`;
        },
        // 是否允许变动
        isAllowChange() {
            // 套餐为全球，任意改动
            if (this.currentPackage === 3) return true;

            // 组合方式：套餐类型，原加速区域，源站
            // 域名原加速区域   字段：area_scope: 1,2,3           1-国内，2-全球（不含中国内地） 3-全球
            // 域名源站加速区域 字段：origin_ownership 0,1,2      0-国内，1-全球（不含中国内地） 2-全球

            const arr = ["2,1,1", "2,2,1", "2,3,1"];
            return arr.includes(this.currentDataProp);
        },
        // 禁用变动
        isDisabledChange() {
            // 组合方式：套餐类型，原加速区域，源站
            const arr = ["1,1,0", "2,2,0"];
            return arr.includes(this.currentDataProp);
        },
        // 提交按钮是否禁用
        isSubmitDisabled() {
            // 表单没有任何变动，不允许提交
            // 初始化加载过程中，不允许提交
            if (isEqual(this.originForm, this.form) || this.loading) return true;

            return false;
        },
        // 是否国际站
        isCtClouds() {
            return nUserModule.isCtclouds;
        },
        // 变更套餐指引
        changePackageGuide() {
            // 中国站
            const url = "https://www.ctyun.cn/document/10065985/10084842";
            // 国际站
            const urlClouds = "https://www.esurfingcloud.com/document/zh-cn/20689737/20689759"
            return this.isCtClouds ? urlClouds : url;
        },
        // 工单地址
        workOrderUrl() {
            // 中国站
            const url = "https://www.ctyun.cn/h5/wsc/worksheet/submit";
            // 国际站-中文
            const urlClouds = "https://www.esurfingcloud.com/contactus/zh-cn/fromIndex?lang=zh-cn";
            return this.isCtClouds ? urlClouds : url;
        }
    },
    methods: {
        /**
         * 调用弹窗
         * @param row
         */
        open(row) {
            this.dialogShow = true;
            this.currentData = row;
            this.form.area = get(row, "area_scope");
            // 记录下当前的初始值
            this.originForm = cloneDeep(this.form);
            this.getDomainDetail();
        },
        /**
         * 获取弹窗详情
         * @returns {Promise<void>}
         */
        async getDomainDetail() {
            this.loading = true;
            try {
                const res = await this.$ctFetch(DomainUrl.domainDetail, {
                    method: "GET",
                    transferType: "json",
                    data: {
                        account_id: this.$store.state.user.userInfo.userId,
                        domain: get(this.currentData, "domain"),
                    },
                });

                this.originScope = get(res, "result.[0].origin_ownership");
            } catch (e) {
                this.$errorHandler(e)
            }

            this.loading = false;
        },
        /**
         * 打开新页签
         */
        openNewPage(url) {
            window.open(url, "_blank");
        },
        /**
         * 点击取消
         */
        handleCancel() {
            this.dialogShow = false;
            this.currentData = null;
            this.originForm = null;
            this.form.area = "";
        },
        /**
         * 点击提交
         */
        handleSubmit() {
            const params = {
                domain: get(this.currentData, "domain"),
                new_area_scope: this.form.area
            }

            this.submitLoading = true;
            this.$ctFetch(DomainUrl.updateArea, {
                method: "POST",
                body: params
            }).then(res => {
                this.$message.success("提交变更加速区域工单成功！")
                this.$emit("success");
                this.handleCancel();
            }).catch(err => {
                this.$errorHandler(err);
            }).finally(() => {
                this.submitLoading = false;
            })
        }
    }
}
</script>

<style lang="scss" scoped>
.area-radio {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.tip-box {
    line-height: 18px;

    div {
        margin-bottom: 12px;

        &:last-child {
            margin-bottom: 0;
        }
    }
}

.change-acce-area {
    ::v-deep .el-form-item {
        margin-bottom: 20px;
        .el-form-item__label {
            line-height: 18px;
        }
    }
}

</style>
