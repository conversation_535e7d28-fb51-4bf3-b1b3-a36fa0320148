/*
 * @Description: 表单相关的工具函数
 * @Author: wang <PERSON><PERSON>
 */
import i18n from "@/i18n";
import { Form } from "element-ui/types";

// 将表单验证转换成 promise
export const formValidate2Promise = (form: Form) =>
    new Promise((resolve, reject) => {
        form.validate((valid, obj) => {
            const msg = !valid && Object.values(obj)[0][0]?.message;
            valid ? resolve(true) : reject(msg);
        });
    });
export const formItemValidate2Promise = (form: Form, itemKey: string, errMsg?: string) =>
    new Promise((resolve, reject) => {
        form.validateField(itemKey, valid => {
            valid ? reject(errMsg || valid) : resolve(true);
        });
    });
export const easyClone = (data: {} | []) => {
    return JSON.parse(JSON.stringify(data));
};
