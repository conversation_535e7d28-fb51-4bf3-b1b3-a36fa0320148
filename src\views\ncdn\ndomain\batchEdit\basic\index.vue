<template>
    <div>
        <el-form class="simple-form" name="basic" ref="basicForm" :model="basicForm" :rules="rules">
            <p class="label-name">{{ $t("domain.create.basicInfo") }}</p>
            <el-form-item
                :label="$t('domain.create.acceType')"
                prop="product_code"
                class="select-action-wrapper"
                label-width="124px"
                label-position="left"
            >
                <el-select
                    v-model="basicForm.product_code"
                    class="select"
                    ref="product_code"
                    popper-class="editor-select-popover-class"
                    :disabled="true"
                >
                    <el-option
                        v-for="item in list"
                        :key="item.value"
                        :label="item.displayValue"
                        :value="item.value"
                    />
                </el-select>
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
import minxin from "@/components/simpleform/minxin";
import { ProductCodeMap } from "@/store/config/product";
import { getI18nLabel } from "@/store/modules/ncdn/nproduct";
import { cloneDeep } from "lodash-es";

export default {
    name: "basic",
    components: {},
    mixins: [minxin],
    props: ["productCodeModel"],
    data() {
        return {
            text: "",
            basicForm: {
                product_code: "001",
            },
            list: [],
            rules: {},
        };
    },
    computed: {
        formData() {
            const returnData = {};
            const { product_code = "" } = this.basicForm;
            returnData.product_code = product_code;
            return returnData;
        },
    },
    mounted() {
        // this.queryProp();
    },
    watch: {
        productCodeModel: {
            deep: true,
            handler(val) {
                this.initProductCode(cloneDeep(val));
            },
            immediate: true,
        },
    },
    methods: {
        validateProcedure() {
            const { list } = this;
            const type = Object.prototype.toString.call(this.basicForm.product_code);
            let valid = true;
            let msg = "";
            // allow-create的不需校验
            if (this.basicForm.product_code) {
                if (type === "[object String]" || type === "[object Number]") {
                    const find = list.find(item => item.value === this.basicForm.product_code);
                    if (!find) {
                        valid = false;
                        msg = this.$t("domain.detail.tip63");
                    }
                } else if (type === "[object Array]") {
                    this.basicForm.product_code.forEach(s => {
                        const find = list.find(item => item.value === s);
                        if (!find) {
                            valid = false;
                            msg = this.$t("domain.detail.tip63-1");
                        }
                    });
                }
            }
            return Promise.resolve({
                valid,
                msg,
            });
        },
        initProductCode(val) {
            if (!val) return;
            this.$nextTick(() => {
                this.basicForm.product_code = val;
            });
        },
        // async queryProp() {
        //     let rst = {};
        //     const data = {
        //         prop: "product_type",
        //     };
        //     rst = await ctFetch(nBasicUrl.getProp, {
        //         data,
        //     });

        //     if (rst) {
        //         this.product_type_list = cloneDeep(rst);
        //     }
        // },
        getProductCodeList() {
            // 转化名称，并且存在域名的加速类型已退订等情况，导致返回的list不全，所以直接转化ProductCodeMap
            this.list = Object.keys(ProductCodeMap).map(key => {
                return {
                    disabled: false,
                    displayValue: getI18nLabel(key),
                    id: key,
                    value: key,
                };
            });
        },
    },
    created() {
        this.getProductCodeList();
    },
};
</script>

<style lang="scss" scoped>
@import "@/components/index.scss";
.simple-form {
    .tooltips {
        font-size: 12px;
        line-height: 12px;
        margin-top: 8px;
        color: $neutral-7;
    }
    .select-action-wrapper {
        .select.el-select {
            width: #{$wrapper-width}px;
            margin-right: 8px;
        }
    }
}
.label-name {
    font-weight: 700;
    margin: 20px 0 8px 0;
    width: 600px;
    overflow: hidden;
    font-size: 14px;
}
</style>
