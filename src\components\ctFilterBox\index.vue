<template>
    <div class="flex-row-style ct-filter-box">
        <el-select
            v-model="currentType"
            :style="selectStyle"
            class="drop-box"
            @change="handleCurrentTypeChange"
        >
            <el-option v-for="item in data" :label="item.label" :value="item.value" :key="item.value" />
        </el-select>
        <div class="view-box">
            <template v-if="currentComponent === 'input'">
                <el-input
                    v-model="formInside[currentType]"
                    clearable
                    v-bind="currentConfig"
                    @change="handleChange"
                />
            </template>
            <template v-if="currentComponent === 'select'">
                <el-select
                    v-model="formInside[currentType]"
                    clearable
                    v-bind="currentConfig"
                    @change="handleChange"
                >
                    <el-option
                        v-for="item in dataList"
                        :key="item.value"
                        :value="item.value"
                        :label="item.label"
                    />
                </el-select>
            </template>
        </div>
    </div>
</template>

<script>
import { mapGetters } from "vuex";
import { cloneDeep, get } from "lodash-es";

export default {
    name: "index",
    props: {
        data: {
            type: Array,
            default: () => [],
        },
        selectStyle: {
            type: Object,
            default: null,
        },
        form: {
            type: Object,
            default: () => {
                return {};
            },
        },
        defaultType: {
            type: [String, Number],
            default: "",
        },
    },
    data() {
        return {
            currentType: this.defaultType,
            originForm: null,
            formInside: null,
            dataList: [
                {
                    dictName: "配置中",
                    dictValue: 3,
                    ext: null,
                    label: "配置中",
                    value: 3,
                },
                {
                    dictName: "已启用",
                    dictValue: 4,
                    ext: null,
                    label: "已启用",
                    value: 4,
                },
                {
                    dictName: "已停用",
                    dictValue: 6,
                    ext: null,
                    label: "已停用",
                    value: 6,
                },
            ],
        };
    },
    computed: {
        ...mapGetters({
            optionsMap: "optionsMap",
        }),
        currentItem() {
            return this.data.find(item => item.value === this.currentType);
        },
        currentComponent() {
            return get(this.currentItem, "type");
        },
        currentConfig() {
            return get(this.currentItem, "config");
        },
        // dataList() {
        //     const list = get(this.currentItem, "list");
        //     if (typeof list === "function") {
        //         return list();
        //     }

        //     if (list instanceof Array) {
        //         return list;
        //     }

        //     const prop = get(this.currentItem, "value");
        //     const arr = get(this.optionsMap, prop, []) || [];
        //     if (arr) {
        //         return arr;
        //     }

        //     return list;
        // },
    },
    watch: {
        data() {
            this.resetCurrentType();
        },
    },
    created() {
        this.formInside = cloneDeep(this.form);
        // 记录初始值
        this.originForm = cloneDeep(this.form);
    },
    methods: {
        /**
         * 重置当前类型
         */
        resetCurrentType() {
            this.currentType = this.defaultType;
            this.handleCurrentTypeChange();
        },
        /**
         * 处理当前类型改变
         */
        handleCurrentTypeChange(isFromOut) {
            Object.keys(this.originForm).forEach(keyName => {
                this.formInside[keyName] = cloneDeep(this.originForm.keyName);
            });

            if (isFromOut !== "status") {
                return;
            }

            this.handleChange();
        },
        /**
         * 处理改变，仅下拉框选中、输入框失焦、回车触发
         * @param val
         */
        handleChange(val) {
            this.$emit("update:form", cloneDeep(this.formInside));
        },
    },
};
</script>

<style scoped lang="scss">
@import "./index.scss";
</style>
