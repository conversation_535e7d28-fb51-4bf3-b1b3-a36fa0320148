<template>
    <div>
        <el-radio-group v-model="model" class="radio-group" @input="onChange">
            <el-radio v-for="o in areaList" :key="o.value" :disabled="o.disabled" :label="o.value">
                <span v-html="o.displayValue" class="radio-display"></span>
            </el-radio>
        </el-radio-group>
        <div class="option-note" v-if="isEdit && isOld">
            <template v-if="!hasOverseaProduct">
                <div v-if="salesChannelLine === salesChannelLineEnum.onLine">
                    <!-- 国际站 isCtclouds -->
                    <p v-if="isCtclouds">
                        {{ $t("simpleForm.acceArea.isCtcCloudTip.tip1") }}
                        <el-button @click="changeType" class="aocdn-ignore-link" size="mini" type="text">
                            {{ $t("domain.create.tip3-1") }} </el-button
                        >{{ $t("simpleForm.acceArea.isCtcCloudTip.tip2")
                        }}<a target="_blank" :href="overseasCdnAcceleratesLink" class="aocdn-ignore-link">
                            {{ $t("simpleForm.acceArea.isCtcCloudTip.tip3") }}</a
                        >{{ $t("simpleForm.acceArea.isCtcCloudTip.tip4") }}
                        <a target="_blank" :href="learnLink" class="aocdn-ignore-link"> {{ $t("domain.detail.tip23") }}</a>
                    </p>
                    <!-- ctyun/vip -->
                    <p v-if="!isCtclouds && isOld2">
                        <span class="option-note">
                            {{ $t("simpleForm.acceArea.isCtcCloudTipOld.tip1") }}
                        </span>
                        <a target="_blank" :href="overseasCdnAcceleratesLink" class="aocdn-ignore-link">
                            {{ $t("simpleForm.acceArea.isCtcCloudTipOld.tip2") }}</a
                        >
                        <span class="option-note">{{ $t("simpleForm.acceArea.isCtcCloudTipOld.tip3") }}</span>
                        <a
                            class="aocdn-ignore-link"
                            @click="$docHelp('https://www.ctyun.cn/document/10015932/10129365')"
                        >
                            {{ $t("domain.detail.tip23") }}</a
                        >
                    </p>
                </div>
                <p v-else>
                    {{ $t("simpleForm.acceArea.productTip.common.tip2") }}
                </p>
            </template>
            <p v-else>{{ $t("simpleForm.acceArea.billingNote") }}</p>
        </div>
        <div v-else-if="!isDetail" class="option-note">
            <i18n :path="note">
                <a target="_blank" class="aocdn-ignore-link" :href="overseasCdnAcceleratesLink">{{ $t("simpleForm.acceArea.productTip.008.tip2") }}</a>
                <a class="aocdn-ignore-link" @click="$docHelp('https://www.ctyun.cn/document/10015932/10071342')">{{ $t("simpleForm.acceArea.productTip.common.tip1") }}</a>
                <a target="_blank" class="aocdn-ignore-link" :href="stationAcceleratesLink">{{ $t("simpleForm.acceArea.productTip.006.tip2") }}</a>
                <a class="aocdn-ignore-link" @click="$docHelp('https://www.ctyun.cn/document/10006847/10096615')">{{ $t("simpleForm.acceArea.productTip.common.tip1") }}</a>
                <a target="_blank" class="aocdn-ignore-link" :href="overseasCdnAcceleratesLink">{{ $t("simpleForm.acceArea.productTip.004.tip2") }}</a>
                <a target="_blank" class="aocdn-ignore-link" :href="overseasCdnAcceleratesLink">{{ $t("simpleForm.acceArea.productTip.014.tip2") }}</a>
            </i18n>
        </div>
        <p class="option-note" v-html="acceTips"></p>
    </div>
</template>

<script type="text/javascript">
import { nDomainUrl } from "@/config/url";
import { NEW_PREFIX } from "@/config/url/_PREFIX";
import { nUserModule } from "@/store/modules/nuser";
// import mixin from "../../editor.mixin";
import minxin from "@/components/simpleform/minxin";
import { DomainModule } from '@/store/modules/domain';
// 域名校验接口
const domainValidateUrl = NEW_PREFIX + "/domain/validate";
// 判断是否开通海外加速
const checkOverseaProductUrl = NEW_PREFIX + "/product/package/checkOverseaProduct";
// 产品线分类，1：线下，2：线上
const salesChannelLineEnum = {
    Offline: 1,
    onLine: 2,
};
/**
 * 最新的业务逻辑：任一产品
 *  - 仅开通内地时，只能选择“中国内地”
 *  - 仅开通海外时，只能选择“全球（不含内地）”
 *  - 同时开通海内海外时，三个选项都能选
 *
 * 特殊补充：直播加速05和全加加速06（2个子产品）时，是只能选国内，但是此时根本不展示当前组件，因此可以不用特殊处理这块的逻辑
 */

export default {
    name: "n-alogic-acce-area",
    mixins: [minxin],
    props: {
        productCode: {
            type: String,
            default: "",
        },
        domainType: {
            type: String,
            default: "",
        },
        areaScope: {
            type: String,
            default: "",
        },
        domain: {
            type: String,
            default: "",
        },
        from: {
            type: String,
            default: "",
        },
    },
    data() {
        return {
            model: "",
            hasMainLandProduct: true, // 是否开通内地
            hasOverseaProduct: false, // 是否开通海外
            // productCode: "", // 产品码
            feeLink: "https://vip.ctcdn.cn/help/#/10009761/10009950/common/10009951?workspaceId=0",
            // cdnAcceleratesLink: "",
            salesChannelLine: null,
            isRepeat: false, // 是否为叠加域名
            salesChannelLineEnum,
        };
    },
    computed: {
        endSentence() {
            return nUserModule.lang === "en" ? '.' : '';
        },
        // 泛静态产品
        isOld() {
            return /^(001|003|004|014)$/.test(this.productCode);
        },
        // 泛静态产品去掉014
        isOld2() {
            return /^(001|003|004)$/.test(this.productCode);
        },
        areaList() {
            return [
                {
                    value: "1",
                    displayValue: this.$t("simpleForm.location.chinaMainland"),
                    // 只要开通内地，就能选择内地（不再限制产品）
                    disabled: !this.hasMainLandProduct,
                },
                {
                    value: "2",
                    displayValue: this.$t("simpleForm.location.globalExcludingMainland"),
                    // 只要开通海外，就能选择海外（不再限制产品）
                    // 增加条件：重叠域名暂时不支持海外
                    disabled: !this.hasOverseaProduct || this.isRepeat,
                },
                {
                    value: "3",
                    displayValue: this.$t("simpleForm.location.global"),
                    // 当同时开通内地、海外，才允许选全球（不再限制产品）
                    disabled: !(this.hasOverseaProduct && this.hasMainLandProduct),
                },
            ];
        },
        note() {
            const { productCode, hasOverseaProduct, salesChannelLine } = this;
            return hasOverseaProduct
                ? this.$t("simpleForm.acceArea.billingNote")
                : this.getNoHasOverseaProductTip(productCode, salesChannelLine);
        },
        isCtclouds() {
            return nUserModule.isCtclouds;
        },
        cdnAcceleratesLink() {
            return this.isCtclouds
                ? nUserModule.lang === "zh"
                    ? "https://www.esurfingcloud.com/products/zh-cn/10015926"
                    : "https://www.esurfingcloud.com/products/10015926"
                : "https://www.ctyun.cn/products/cdnjs";
        },
        overseasCdnAcceleratesLink() {
            return this.isCtclouds
                ? nUserModule.lang === "zh"
                    ? " https://www.esurfingcloud.com/bac/product/ops?custId=0&siteId=ctyun&productId=10015926&operationId=1003017103&lang=zh-cn"
                    : "https://www.esurfingcloud.com/bac/product/ops?custId=0&siteId=ctyun&productId=10015926&operationId=1003017103&lang=en-us"
                : "https://cdn.ctyun.cn/h5/norder/?product=008&action=0&lang=zh-cn";
        },
        stationAcceleratesLink() {
            return this.isCtclouds
                ? nUserModule.lang === "zh"
                    ? "https://www.esurfingcloud.com/bac/product/ops?custId=0&siteId=ctyun&productId=10006828&operationId=1000682801&lang=zh-cn"
                    : "https://www.esurfingcloud.com/bac/product/ops?custId=0&siteId=ctyun&productId=10006828&operationId=1000682801&lang=en-us"
                : "https://www.ctyun.cn/h5/bac/product/ops?custId=0&siteId=ctyun&productId=10006828&operationId=1000682801";
        },
        learnLink() {
            return nUserModule.lang === "zh"
                ? "https://www.esurfingcloud.com/document/zh-cn/10015932/20688150"
                : "https://www.esurfingcloud.com/document/10015932/20688150";
        },
        acceTips() {
            const ctcloudLink =
                nUserModule.lang === "zh"
                    ? "https://www.esurfingcloud.com/console/info/realname?lang=zh-cn"
                    : "https://www.esurfingcloud.com/console/info/realname?lang=en-us";
            return this.isCtclouds
                ? this.$t("simpleForm.acceArea.acceTipsCtclouds", { ctcloudLink })
                : this.$t("simpleForm.acceArea.acceTipsNonCtclouds");
        },
    },
    watch: {
        productCode() {
            // 变更产品类型时，需要判断是否开通海外加速
            this.checkOverseaProduct();
            // 新增时，每次选择加速类型后，重置加速区域为空
            if (this.isCreate) {
                this.model = "";
                this.checkRepeat();
            }
        },
        domainType() {
            // 变更域名类型时，需要判断是否开通海外加速
            this.checkOverseaProduct();
        },
        areaScope(val) {
            if (val) {
                this.model = val;
            }
        },
    },
    methods: {
        onChange() {
            this.$emit("acceArea", this.model);
        },
        // getNoHasOverseaProductTip(type, salesChannelLine) {
        //     const { overseasCdnAcceleratesLink, stationAcceleratesLink } = this;
        //     switch (type) {
        //         case "008":
        //             return `您当前尚未开通CDN加速境外服务，请先前往开通<a target="_blank"  class="aocdn-ignore-link" href="${overseasCdnAcceleratesLink}">CDN加速-全球（不含中国内地）</a>服务。`;
        //         case "006":
        //             return `全站加速支持全球范围加速，先前往开通<a target="_blank" class="aocdn-ignore-link"href="${stationAcceleratesLink}">全站加速-全球（不含中国内地）</a>服务。`;
        //         case "001":
        //         case "003":
        //         case "004":
        //         case "014":
        //             return salesChannelLine === salesChannelLineEnum.onLine
        //                 ? `当前仅CDN加速支持开通全球加速服务，请选择CDN加速类型，并确保已开通<a target="_blank" class="aocdn-ignore-link" href="${overseasCdnAcceleratesLink}">CDN加速-全球（不含中国内地）</a>服务。`
        //                 : `如需支持全球加速服务，请先联系客户经理，前往开通对应产品的全球不含中国内地加速区域。`;
        //     }
        // },
        getNoHasOverseaProductTip(type, salesChannelLine) {
            const { overseasCdnAcceleratesLink, stationAcceleratesLink } = this;
            switch (type) {
                case "008":
                    return this.isCtclouds
                        ? "simpleForm.acceArea.productTip.008.tip1"
                        : salesChannelLine !== salesChannelLineEnum.onLine
                        ? "simpleForm.acceArea.productTip.008.tip4"
                        : "simpleForm.acceArea.productTip.common.tip2";
                case "006":
                    return this.isCtclouds
                        ? "simpleForm.acceArea.productTip.006.tip1"
                        : salesChannelLine === salesChannelLineEnum.onLine
                        ? "simpleForm.acceArea.productTip.006.tip4"
                        : "simpleForm.acceArea.productTip.common.tip2";
                case "001":
                case "003":
                case "004":
                    return salesChannelLine === salesChannelLineEnum.onLine
                        ? "simpleForm.acceArea.productTip.004.tip1"
                        : "simpleForm.acceArea.productTip.common.tip2";
                case "014":
                    return !this.isCtclouds
                        ? ""
                        : salesChannelLine === salesChannelLineEnum.onLine
                        ? "simpleForm.acceArea.productTip.014.tip1"
                        : "simpleForm.acceArea.productTip.common.tip2";
                default:
                    return ""
            }
        },
        // handleRelative(relativeFormValue, relativeIndex, relativeFormSubmitValue, valid, candidateList) {
        //     // 新增时，每次选择加速类型后，重置加速区域为空
        //     if (this.isCreate && !this.$route.query.orderId) {
        //         this.model = "";
        //     }
        //     const { pId, pValue } = relativeFormSubmitValue;
        //     // 依赖产品类型
        //     if (pId === "product_type") {
        //         this.productList = candidateList;
        //         this.productCode = pValue.split("=")[1];
        //     } else if (pId === "domain") {
        //         // 检查域名是否为重叠域名
        //         if (valid) {
        //             this.$ctFetch(domainValidateUrl, {
        //                 data: {
        //                     domain: pValue,
        //                     operationType: "update",
        //                 },
        //             })
        //                 .then(({ isRepeat }) => (this.isRepeat = isRepeat === "true"))
        //                 .catch(() => {
        //                     // console.log(err);
        //                 });
        //         }
        //     }
        // },
        // 判断是否开通海外加速
        async checkOverseaProduct() {
            // scc 分了一大堆的二级产品，前端不需要关注二级产品，只需要关注后端返回的这两个字段
            try {
                const { hasOverseaProduct, hasMainLandProduct, salesChannelLine } = await this.$ctFetch(
                    checkOverseaProductUrl,
                    {
                        data: {
                            productCode: this.productCode,
                        },
                    }
                );
                this.hasOverseaProduct = hasOverseaProduct;
                this.hasMainLandProduct = hasMainLandProduct;
                this.salesChannelLine = salesChannelLine;
                this.$emit("hasOverseaProduct", this.hasOverseaProduct);
            } catch (e) {
                // console.error(e);
            }
        },
        async init() {
            this.model = "";
            // 重新发起
            if (this.$route.query.orderId) {
                this.model = this.areaScope;
            }
            if (DomainModule.createDomainStage.isFromDetail && this.areaScope) {
                this.model = this.areaScope;
            }
            await this.renderDomainTypeOptions();
            this.productList = this.codeMap;

            this.checkRepeat();
        },
        async checkRepeat() {
            if (!this.domain && !this.$route.query.domainId) return;
            // 检查域名是否为重叠域名
            this.$ctFetch(domainValidateUrl, {
                method: "POST",
                data: {
                    domain: this.domain || this.$route.query.domainId,
                    operationType: "update",
                },
                headers: {
                    "Content-Type": "application/json",
                },
            })
                .then(({ isRepeat }) => (this.isRepeat = isRepeat))
                .catch(() => {
                    // console.log(err);
                });
        },
        // 移动到加速类型去了
        async changeType() {
            // 008 是 CDN 加速，新的产品
            if ((this.productList || []).find(item => item.value === "008")) {
                await this.$confirm(
                    this.$t("simpleForm.acceArea.confirmChangeAccelerationType"),
                    this.$t("domain.create.tip3-1"),
                    {
                        confirmButtonText: this.$t("common.dialog.submit"),
                        cancelButtonText: this.$t("common.dialog.cancel"),
                        type: "warning",
                    }
                );
                await this.$ctFetch(nDomainUrl.updateAreaOrProduct, {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                    },
                    data: { type: "product", domain: this.$route.query.domainId, newBusinessType: 8 },
                });
                this.$message.success(this.$t("domain.detail.tip7"));
                setTimeout(() => {
                    this.$router.push({
                        name: "ndomain.list",
                    });
                }, 1000);
            } else {
                await this.$prompt(this.$t("domain.detail.tip8", { link: this.cdnAcceleratesLink }),
                    this.$t("common.messageBox.title"),
                    {
                        showInput: false,
                        showCancelButton: false,
                        dangerouslyUseHTMLString: true,
                    }
                );
            }
        },
    },
    mounted() {
        this.init();
        this.checkOverseaProduct();
    },
};
</script>

<style lang="scss" scoped>
@import "../variable.scss";

.option-note {
    font-size: #{$tooltips-font-size}px;
    line-height: #{$tooltips-font-size + 6}px;
    margin-top: 8px;
    color: $neutral-7;
}
.radio-group {
    display: inline-block !important;
    ::v-deep {
        .el-radio.is-bordered + .el-radio.is-bordered {
            margin-left: 0;
            margin-bottom: 10px;
        }
    }
}
.option-note {
    ::v-deep .link {
        // color: #0004ed;
    }
}
.radio-display {
    line-height: 24px;
}
</style>
