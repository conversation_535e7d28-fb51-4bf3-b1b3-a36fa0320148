import { RouteConfig } from "vue-router";

const isAocdn = window.__POWERED_BY_QIANKUN__;

const statisticsRouter: RouteConfig = {
    path: "/statistics",
    name: "statistics",
    component: () => isAocdn ? import("./aocdn.vue") : import("./index.vue"),
    meta: {
        breadcrumb: {
            title: isAocdn ? "$t('statistics.router.tip1')" : "$t('statistics.router.tip2')",
            route: ["home", "statistics"],
        },
        perm: "statistics",
    },
    async beforeEnter(to, from, next) {
        // 迁移阻塞页面加载的方法到 ./entryMixin.ts 的 created 钩子中, 以解决子应用切换时的页面加载阻塞问题
        next();
    },
    children: [
        {
            path: "cdn",
            name: "statistics.cdn",
            component: () => import("./fcdn.vue"),
            meta: {
                breadcrumb: {
                    title: "$t('statistics.router.tip3')",
                    route: ["statistics", "statistics.cdn"],
                },
                perm: "statistics.cdn",
            },
        },
        {
            path: "dcdn",
            name: "statistics.dcdn",
            component: () => import("./dcdn.vue"),
            meta: {
                breadcrumb: {
                    title: "$t('statistics.router.tip4')",
                    route: ["statistics", "statistics.dcdn"],
                },
                perm: "statistics.dcdn",
            },
        },
    ],
};
export default statisticsRouter;
