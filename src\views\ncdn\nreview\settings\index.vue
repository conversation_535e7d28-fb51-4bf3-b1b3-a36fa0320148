<template>
    <ct-section-wrap
        headerText="设置"
        headerTip="CDN内容审核是CDN加速的一项增值服务，开通CDN内容审核功能后，系统会自动只能监测通过CDN加速的图片是否涉黄涉暴涉政恐，鉴定为违规图片的URL将会被记录下来供客户导出，同时支持对违规内容做自动封禁，实现“净网分发”。目前内容审核主要针对图片业务。"
    >
        <ct-box>
            <el-tabs v-model="currentComponent">
                <el-tab-pane label="服务设置" name="Service" />
                <el-tab-pane label="任务列表" name="Task" />
            </el-tabs>
            <component :is="currentComponent" />
        </ct-box>
    </ct-section-wrap>
</template>
<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import Service from "./Service.vue";
import Task from "./Task.vue";

@Component({
    components: { Service, Task },
})
export default class Settings extends Vue {
    currentComponent = "Service";
    private mounted() {
        sessionStorage.settingTabName = "服务设置";
    }
}
</script>

<style lang="scss" scoped></style>
