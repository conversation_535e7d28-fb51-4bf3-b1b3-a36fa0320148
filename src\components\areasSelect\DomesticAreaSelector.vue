<template>
    <div class="area-select el-select" v-clickoutside="handleClose">
        <div class="area-select__tags">
            <el-tag size="mini" type="info">
                {{ selectedAreaLocal[-1] || selectedCount === 0 ? $t("common.areaSelect.allArea") :
                    $tc("common.areaSelect.selectedCount", selectedCount) }}
            </el-tag>
        </div>
        <div class="area-select__input el-input" @click.stop.prevent="toggleDropdown" ref="reference"
            :class="{ 'is-focus': visible }">
            <input ref="input" type="text" class="el-input__inner" :placeholder="selectedCount ? '' : placeholder"
                readonly="readonly" @focus="handleFocus" @keydown.esc.stop.prevent="visible = false"
                @keydown.tab="visible = false" />
            <i :class="['el-icon-arrow-up', { 'is-reverse': visible }]"></i>
        </div>
        <transition name="el-zoom-in-top">
            <div v-show="visible" class="aocdn-ignore-area-select__dropdown el-popper" ref="popper">
                <div class="aocdn-ignore-area-select__dropdown-wrapper">
                    <el-checkbox class="aocdn-ignore-region-checkbox" v-model="isAllSelected"
                        @change="handleCheckAllChange"><label class="aocdn-ignore-region-all">{{
                            $t("common.areaSelect.selectAll") }}</label></el-checkbox>
                    <div class="aocdn-ignore-region" v-for="region in areaList" :key="region.value">
                        <div class="aocdn-ignore-region-header">
                            <el-checkbox class="aocdn-ignore-region-checkbox" v-if="
                                (region.value !== '-1' && region.value !== '0') ||
                                (region.value === '0' && oversea)
                            " :value="regionAllSelected[region.value]" @change="handleRegionAllSelect(region.value)" />
                            <label v-if="
                                (region.value !== '-1' && region.value !== '0') ||
                                (region.value === '0' && oversea)
                            " class="aocdn-ignore-region-category">
                                {{ region.label }}
                            </label>
                        </div>
                        <!-- 添加判断，若为海外地区，只显示全选的海外选项，海外的children(只有一个元素，海外)不予显示 -->
                        <template v-if="region.value !== '0' && region.value !== '-1'">
                            <el-checkbox class="aocdn-ignore-region-checkbox-area" v-for="province in region.children"
                                :key="province.value" :label="province.label"
                                v-model="selectedAreaLocal[province.value]"
                                @change="handleAreaChange(province.value, $event, region.value)" />
                        </template>
                    </div>
                </div>
            </div>
        </transition>
    </div>
</template>
<script lang="ts">
import { Component, Prop, Watch } from "vue-property-decorator";
import { mixins } from "vue-class-component";
// 引入element封装的popper工具
import Popper from "element-ui/lib/utils/vue-popper";
import Clickoutside from "element-ui/lib/utils/clickoutside";

type Province = {
    value: string;
    label: string;
};
type BooleanObject = {
    [key: string]: boolean;
};
type Region = { label: string; children: Province[]; value: string };

// 地区选择
@Component({
    directives: { Clickoutside },
    model: {
        prop: "selectedArea",
        event: "select",
    },
})
export default class AreaSelect extends mixins(Popper) {
    @Prop({ default: true }) private appendToBody!: boolean;
    @Prop({ default: () => [] }) private areaList!: Region[];
    @Prop({ default: false }) private multiple!: boolean;
    @Prop({ default: "" }) private placeholder!: string;
    // @Prop({ default: () => [] }) private selectedArea!: string[];
    @Prop({
        default: () => {
            return {
                province: [],
                continent: [],
                continentRegion: [],
            };
        },
    })
    private selectedArea!: any;
    @Prop({ default: false }) private oversea!: boolean;
    @Prop({ default: true }) private popperAppendToBody!: boolean;
    @Prop({ default: false }) private disabled!: boolean;

    private selectedAreaLocal: BooleanObject = {};
    private visible = false;

    private locked = false;
    private referenceElm?: Element;
    private popperElm?: Element;
    private isAllSelected = true;

    get provinceDisabled() {
        const selectedProvinceNumber = Object.keys(this.selectedAreaLocal).filter(province => {
            return this.selectedAreaLocal[province] === true;
        }).length;
        if (selectedProvinceNumber === 1) {
            return true;
        }
        return false;
    }

    get selectedCount() {
        let count = 0;
        Object.keys(this.selectedAreaLocal).forEach(key => this.selectedAreaLocal[key] && count++);
        return count;
    }

    get regionAllSelected() {
        const obj: { [key: string]: boolean } = {};
        for (const region of this.areaList) {
            if (region.value !== "-1") {
                obj[region.value] = !region.children.some(
                    province => !this.selectedAreaLocal[province.value]
                );
            }
        }

        return obj;
    }

    private mounted() {
        this.referenceElm = (this.$refs.reference as Vue).$el;
        this.popperElm = (this.$refs.popper as Vue).$el;
        this.createPopper();
        this.$on("updatePopper", () => {
            if (this.visible) this.updatePopper();
        });
        this.$on("destroyPopper", this.destroyPopper);
    }

    private handleAreaChange(province: Province["value"], val: boolean) {
        if (!this.multiple && val === true) {
            for (const key in this.selectedAreaLocal) {
                this.selectedAreaLocal[key] = false;
            }
            this.selectedAreaLocal[province] = true;
        }
        const proviceList: string[] = [];
        if (province !== "-1") {
            !val && (this.selectedAreaLocal[-1] = false);
            // 除全部地区选项外，所有区域是否全部勾选
            this.isAllSelected = !Object.keys(this.selectedAreaLocal).find(area => {
                return area !== "-1" && this.selectedAreaLocal[area] === false;
            });
            if (this.isAllSelected) {
                // 除全部地区选项外，若所有地区全部勾选，则自动勾选上全部地区
                this.selectedAreaLocal[-1] = true;
            } else {
                // 除全部地区选项外，若没有勾选全部地区，则取消全部地区的勾选
                this.selectedAreaLocal[-1] = false;
            }
            Object.keys(this.selectedAreaLocal).forEach(area => {
                if (this.selectedAreaLocal[area]) {
                    proviceList.push(area);
                }
            });
        }
        this.$emit("select", {
            province: proviceList,
            continent: [],
            continentRegion: [],
        });
    }
    private handleRegionAllSelect(region: Region["value"]) {
        this.regionAllSelected["-1"] = true;
        const selected = !this.regionAllSelected[region];
        // 区域全选处理
        this.areaList
            .find(item => item.value === region)!
            .children.forEach(province => {
                this.selectedAreaLocal[province.value] = selected;
            });
        this.emitData();
    }
    private handleClose() {
        this.visible = false;
    }
    private handleFocus() {
        // 点击下拉按钮会先触发focus事件再触发click事件，所以要加个锁防止下拉框消失，同时要清除锁防止关闭不了
        if (!this.visible) {
            this.locked = true;
            this.visible = true;
            setTimeout(() => {
                this.locked = false;
            }, 300);
        }
    }
    private toggleDropdown() {
        if (this.locked) this.locked = false;
        else this.visible = !this.visible;
    }
    private emitData() {
        const proviceList: string[] = [];
        // 除全部地区选项外，所有区域是否全部勾选
        this.isAllSelected = !Object.keys(this.selectedAreaLocal).find(area => {
            return area !== "-1" && this.selectedAreaLocal[area] === false;
        });
        if (this.isAllSelected) {
            // 除全部地区选项外，若所有地区全部勾选，则自动勾选上全部地区
            this.selectedAreaLocal[-1] = true;
            if (!proviceList.includes("-1")) {
                proviceList.push("-1");
            }
        } else {
            // 除全部地区选项外，若没有勾选全部地区，则取消全部地区的勾选
            this.selectedAreaLocal[-1] = false;
        }
        Object.keys(this.selectedAreaLocal).forEach(area => {
            if (this.selectedAreaLocal[area]) {
                proviceList.push(area);
            }
        });
        this.$emit("select", {
            province: proviceList,
            continent: [],
            continentRegion: [],
        });
    }

    private handleCheckAllChange(val: boolean) {
        // 全选
        if (val) {
            Object.keys(this.selectedAreaLocal).forEach(area => {
                this.selectedAreaLocal[area] = true;
            });
        }

        // 取消全选
        if (!val) {
            Object.keys(this.selectedAreaLocal).forEach(area => {
                this.selectedAreaLocal[area] = false;
            });
        }
        this.emitData();
    }

    @Watch("areaList", { immediate: true })
    onAreaListChange(val: Region[]) {
        this.selectedAreaLocal = {};
        val.forEach(region => {
            region.children.forEach(province => {
                this.$set(this.selectedAreaLocal, province.value, false);
            });
        });
        this.selectedAreaLocal[-1] = true;
        this.emitData();
    }
    @Watch("visible")
    private onVisibleChange(val: boolean) {
        if (val) {
            if (this.selectedCount === 0) {
                this.isAllSelected = true;
                Object.keys(this.selectedAreaLocal).forEach(area => {
                    this.selectedAreaLocal[area] = true;
                });
            }
            this.updatePopper();
        } else {
            if (this.selectedCount === 0) {
                this.isAllSelected = true;
                Object.keys(this.selectedAreaLocal).forEach(area => {
                    this.selectedAreaLocal[area] = true;
                });
            }
            (this.$refs.input as HTMLInputElement).blur(); // 关闭时清除边框
            this.$emit("blur");
        }
    }
    @Watch("selectedArea.province", { immediate: true })
    private onSelectedAreaChange(val: string[]) {
        Object.keys(this.selectedAreaLocal).forEach(area => {
            this.selectedAreaLocal[area] = false;
        });
        val?.forEach(area => {
            this.selectedAreaLocal[area] = true;
        });
    }
}
</script>
<style lang="scss" scoped>
.area-select {
    color: #c0c4cc;
    display: inline-block;
    position: relative;
    width: 215px;
    line-height: 32px;
}

.area-select__tags {
    position: absolute;
    z-index: 100;
    line-height: 32px;
    max-width: calc(100% - 60px);
    top: 50%;
    transform: translate(0, -50%);

    .el-tag {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        vertical-align: middle;
        width: 100%;
    }
}

.area-select__input {
    position: relative;
    width: 100%;

    input {
        outline: none;
        cursor: pointer;
        width: 100%;
        height: 32.1px;
        box-sizing: border-box;
        line-height: 32px;
        border: 1px solid #dcdfe6;

        &.is-focus {
            border: 1px solid #fa8334;
        }

        &::placeholder {
            color: #c0c4cc;
            font-size: 13px;
            line-height: 30px;
        }
    }
}

.el-icon-arrow-up {
    pointer-events: none;
    cursor: pointer;
    text-align: center;
    position: absolute;
    width: 25px;
    right: 5px;
    transform: rotateZ(180deg);
    transition: transform 0.3s;
    line-height: 32px;

    &.is-reverse {
        transform: rotateZ(0deg);
    }
}
</style>

<style lang="scss">
.aocdn-ignore-area-select__dropdown {
    position: absolute;
    z-index: 1001;
    border: solid 1px #e4e7ed;
    width: 910px;
    border-radius: 2px;
    background-color: #ffffff;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    box-sizing: border-box;
}

.aocdn-ignore-area-select__dropdown-wrapper {
    max-height: 60vh;
    overflow: auto;
    padding: 15px;
}

.aocdn-ignore-region {
    display: grid;
    gap: 8px;
    grid-template-columns: repeat(7, 120px);

    .aocdn-ignore-region-header {
        display: flex;

        .el-checkbox {
            margin-right: 4px !important;
        }
    }

    .aocdn-ignore-region-checkbox-area {
        margin-right: 12px !important;

        ::v-deep {
            .el-checkbox__input {
                vertical-align: top;
                margin-top: 2px;
            }

            .el-checkbox__label {
                line-height: 1 !important;
                white-space: pre-wrap;
                overflow-wrap: anywhere;

            }
        }
    }

    .aocdn-ignore-region-category {
        display: inline-block;
        font-weight: bold;
        font-size: 14px;

        ::v-deep {
            .el-checkbox {
                display: flex;
                align-items: flex-start;

                .el-checkbox__label {
                    white-space: normal;
                }
            }
        }
    }

    &+.aocdn-ignore-region {
        margin-top: 8px;
    }
}

.aocdn-ignore-region-all {
    display: inline-block;
    width: 120px;
    margin-left: -4px;
    font-weight: bold;
    color: #000000;
    font-size: 14px;
}
</style>
