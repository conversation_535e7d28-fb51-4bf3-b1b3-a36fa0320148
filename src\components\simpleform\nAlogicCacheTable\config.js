import i18n from '../../../i18n'
// 后缀名选项
export const extensionsOptions = {
    dynamic: {
        title: i18n.t("simpleForm.extention.dynamic"),
        list: ["asp", "php", "jsp", "ashx", "aspx", "do"],
    },
    image: {
        title: i18n.t("simpleForm.extention.image"),
        list: ["jpg", "jpeg", "png", "gif", "webp", "bmp", "ico", "tiff"],
    },
    style: {
        title: i18n.t("simpleForm.extention.style"),
        list: ["js", "css", "xml", "json"],
    },
    av: {
        title: i18n.t("simpleForm.extention.media"),
        list: [
            "wmv",
            "mp3",
            "wma",
            "ogg",
            "flv",
            "mp4",
            "avi",
            "mpg",
            "mpeg",
            "f4v",
            "hlv",
            "rmvb",
            "rm",
            "3gp",
            "img",
            "m3u8",
            "ts",
            "swf",
        ],
    },
    download: {
        title: i18n.t("simpleForm.extention.download"),
        list: ["bin", "zip", "rar", "ipa", "apk", "jar", "sis", "xap", "msi", "exe", "cab", "7z"],
    },
    page: {
        title: i18n.t("simpleForm.extention.page"),
        list: ["txt", "htm", "shtml", "html"],
    },
};

// 默认缓存设置1：点播加速、静态加速、下载加速、CDN加速
export const defaultCacheList1 = [
    {
        mode: 0,
        file_type: "php,ashx,aspx,asp,jsp,do",
        ttl: 0,
        time: "0", // 0
        timeType: "5",
        cache_type: 3,
        cache_with_args: 0,
        priority: 10,
        split: 0,
    },
    {
        mode: 0,
        file_type: "js,css,xml,htm,html",
        ttl: 1800,
        time: "30", // 30 分
        timeType: "4",
        cache_type: 3,
        cache_with_args: 0,
        priority: 10,
        split: 0,
    },
    {
        mode: 0,
        file_type: "swf,jpg,jpeg,webp,gif,png,bmp,ico,ts",
        ttl: 86400,
        time: "1", // 1 日
        timeType: "2",
        cache_type: 3,
        cache_with_args: 0,
        priority: 10,
        split: 0,
    },
    {
        mode: 0,
        file_type:
            "wmv,mp3,wma,ogg,flv,mp4,avi,mpg,mpeg,f4v,hlv,rmvb,rm,3gp,img,bin,zip,rar,ipa,apk,jar,sis,xap,msi,exe,cab,7z,pdf,doc,docx,xls,xlsx,ppt,pptx,txt",
        ttl: 31536000,
        time: "365", // 365 日
        timeType: "2",
        cache_type: 3,
        cache_with_args: 0,
        priority: 10,
        split: 0,
    },
];

// 默认缓存设置2：全站加速
export const defaultCacheList2 = [
    {
        mode: 0,
        file_type: "js,css,xml,htm,html",
        ttl: 1800,
        time: "30", // 30 分
        timeType: "4",
        cache_type: 3,
        cache_with_args: 0,
        priority: 10,
        split: 0,
    },
    {
        mode: 0,
        file_type: "swf,jpg,jpeg,webp,gif,png,bmp,ico,ts",
        ttl: 86400,
        time: "1", // 1 日
        timeType: "2",
        cache_type: 3,
        cache_with_args: 0,
        priority: 10,
        split: 0,
    },
    {
        mode: 0,
        file_type:
            "wmv,mp3,wma,ogg,flv,mp4,avi,mpg,mpeg,f4v,hlv,rmvb,rm,3gp,img,bin,zip,rar,ipa,apk,jar,sis,xap,msi,exe,cab,7z,txt,pdf,doc,docx,xls,xlsx,ppt,pptx",
        ttl: 31536000,
        time: "365", // 365 日
        timeType: "2",
        cache_type: 3,
        cache_with_args: 0,
        priority: 10,
        split: 0,
    },
];
