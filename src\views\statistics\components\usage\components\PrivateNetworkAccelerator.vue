<template>
    <div class="chart-wrap">
        <div class="title-wrap">
            <div class="operate-btn">
                <el-radio-group v-model="chartType" size="mini">
                    <el-radio-button
                        :label="$t('statistics.dcdn.privateNetworkAcceleratorWhole.radioBtn1')"></el-radio-button>
                    <el-radio-button
                        :label="$t('statistics.dcdn.privateNetworkAcceleratorWhole.radioBtn2')"></el-radio-button>
                </el-radio-group>
            </div>
            <template v-if="showBandwidth">
                <div class="total">
                    <div class="tip">
                        {{ $t("statistics.common.vchartTip1", { type: "", num: "" }) }}{{ titleTipSuffix }}：
                        <span class="num">
                            {{ fetchData.topBandwidth | convertBandwidthB2P(scale) }}
                        </span>
                        <span class="date">{{ fetchData.topBandwidthTime || "" }}</span>
                    </div>
                    <div class="tip">
                        {{ $t("statistics.common.vchartTip2", { type: "", num: "" }) }}{{ titleTipSuffix }}：
                        <span class="num">
                            {{ fetchData.top95Bandwidth | convertBandwidthB2P(scale) }}
                        </span>
                    </div>
                </div>
                <div class="total" v-if="useCompare">
                    <div class="tip">
                        {{ $t("statistics.common.vchartTip1", { type: "", num: "2" }) }}：
                        <span class="num">
                            {{ fetchData2.topBandwidth | convertBandwidthB2P(scale) }}
                        </span>
                        <span class="date">{{ fetchData2.topBandwidthTime || "" }}</span>
                    </div>
                    <div class="tip">
                        {{ $t("statistics.common.vchartTip2", { type: "", num: "2" }) }}：
                        <span class="num">
                            {{ fetchData2.top95Bandwidth | convertBandwidthB2P(scale) }}
                        </span>
                    </div>
                </div>
            </template>
            <template v-else>
                <div class="total">
                    <div class="tip">
                        {{ $t("statistics.common.vchartTip3", { type: "", num: "" }) }}{{ titleTipSuffix }}：
                        <span class="num">{{ fetchData.totalFlow | convertFlowB2P(scale) }}</span>
                    </div>
                    <div class="tip" v-if="useCompare">
                        {{ $t("statistics.common.vchartTip3", { type: "", num: "2" }) }}：
                        <span class="num">{{ fetchData2.totalFlow | convertFlowB2P(scale) }}</span>
                    </div>
                </div>
            </template>
        </div>

        <v-chart class="chart" v-loading="loading" :element-loading-text="$t('statistics.common.chart.loading')"
            autoresize theme="cdn" :options="options" @legendselectchanged="legendselectchanged" />

        <ct-tip>
            {{ $t("statistics.usageQuery.PrivateNetworkAccelerator.ctTip") }}
            <el-tooltip effect="dark" :content="$t('statistics.common.searchDownloadContent')" placement="right">
                <i class="el-icon-download usage-download-icon" @click="downloadTable"></i>
            </el-tooltip>
        </ct-tip>

        <el-table :data="fetchData.daily" v-loading="loading"
            :element-loading-text="$t('statistics.common.table.loading')" :empty-text="$t('common.table.empty')">
            <el-table-column :label="$t('statistics.usageQuery.PrivateNetworkAccelerator.tableColumn1')" prop="date"
                :sortable="true"></el-table-column>
            <el-table-column :label="$t('statistics.usageQuery.PrivateNetworkAccelerator.tableColumn2', {
                unit: wrapWithBrackets(indentFlowConfig.unit),
            })
                ">
                <template slot-scope="{ row }">
                    {{ (row.flow / indentFlowConfig.scale).toFixed(2) }}
                </template>
            </el-table-column>
            <el-table-column :label="$t('statistics.usageQuery.PrivateNetworkAccelerator.tableColumn3', {
                unit: wrapWithBrackets(indentQueryBandwidthConfig.unit),
            })
                ">
                <template slot-scope="{ row }">
                    {{ (row.topBandwidth / indentQueryBandwidthConfig.scale).toFixed(2) }}
                </template>
            </el-table-column>
            <el-table-column :label="$t('statistics.usageQuery.PrivateNetworkAccelerator.tableColumn4')">
                <template slot-scope="{ row }">
                    {{ row.topBandwidthTime | timeFormat }}
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<script lang="ts">
import { Component } from "vue-property-decorator";
import { mixins } from "vue-class-component";
import { StatisticsUsageUrl } from "@/config/url/statistics";
import { downloadCsv } from "@/utils";
import { timeFormat } from "@/filters/index";
import { Query5Min, SearchParams } from "@/types/statistics/usage";
import { QueryFetchData } from "@/types/statistics/usage";
import { THEME_AREA_STYLE } from "@/config/echart/cdn-theme";
import ChartMixin from "../chartMixin";
import { nUserModule } from "@/store/modules/nuser";
import { get } from "lodash-es";
import { cloneDeep } from "lodash-es";

type tooltipParam = { name: string; marker: string; value: string | number };

const defaultFetchData: QueryFetchData = {
    "5min": [],
    daily: [],
    topBandwidth: 0,
    top95Bandwidth: 0,
    totalFlow: 0,
    avgQueryFlow: 0,
    dailyPeakMonthlyAverage: 0,
    topBandwidthTime: 0,
};

@Component({
    name: "PrivateNetworkAccelerator",
})
export default class PrivateNetworkAccelerator extends mixins(ChartMixin) {
    chartType = `${this.$t("statistics.usageQuery.PrivateNetworkAccelerator.chartType")}`; //
    useCompare = false; // 是否使用了数据对比
    // 缓存请求参数
    private searchParams2?: SearchParams;
    // 接口数据
    fetchData: QueryFetchData = cloneDeep(defaultFetchData);
    fetchData2: QueryFetchData = cloneDeep(defaultFetchData);
    protected downloadDataList: QueryFetchData["5min"] = []; // 用于下载的数据

    private timeMinus?: number; // 时间差
    private legend1Selected = true; // 是否选择了图例1

    // 请求生成，type： flow-流量带宽数据 request-请求数 status-回源数据
    private async localFetchGenerator<T>(params: SearchParams) {
        const rst = await this.fetchGenerator<T>(StatisticsUsageUrl.dedicatedLineList, {
            ...params,
        });

        return rst;
    }

    // 当前展示是否为带宽（流量）
    get showBandwidth() {
        return this.chartType === `${this.$t("statistics.usageQuery.PrivateNetworkAccelerator.chartType")}`;
    }

    // 获取 seriesData 所使用的 key
    get seriesDataKey() {
        return this.showBandwidth ? "bandwidth" : "flow";
    }

    // 标题1的后缀（存在数据对比时需要）
    get titleTipSuffix() {
        return this.useCompare ? "1" : "";
    }

    // 根据最大值获取图表的单位规则 { 单位，缩进 }
    get chartUnitConfig() {
        // 单数据 or 多数据
        let max = 0;
        if (this.useCompare) {
            const max1 = this.getMaxFromList(this.fetchData["5min"], this.seriesDataKey);
            const max2 = this.getMaxFromList(this.fetchData2["5min"], this.seriesDataKey);

            max = Math.max(max1, max2);
        } else {
            max = this.getMaxFromList(this.fetchData["5min"], this.seriesDataKey);
        }

        return this.showBandwidth ? this.getBandwidthUnitConfig(max) : this.getFlowUnitConfig(max);
    }

    // 1、数据请求
    protected async getData(params1: SearchParams, params2: SearchParams) {
        // 有参数2，说明开启了对齐
        this.useCompare = !!params2;

        // 缓存参数
        this.searchParams2 = params2;

        if (!this.useCompare) {
            [this.fetchData] = await Promise.all([
                this.localFetchGenerator<QueryFetchData>(params1),
            ]);

            if (!this.fetchData) this.fetchData = cloneDeep(defaultFetchData);
        } else {
            // 记录时间差，防止清空时间后tooltip报错
            this.timeMinus = (params1.startTime - params2.startTime) * 1000;
            [
                this.fetchData,
                this.fetchData2,
            ] = await Promise.all([
                this.localFetchGenerator<QueryFetchData>(params1),
                this.localFetchGenerator<QueryFetchData>(params2),
            ]);

            if (!this.fetchData) this.fetchData = cloneDeep(defaultFetchData);
            if (!this.fetchData2) this.fetchData2 = cloneDeep(defaultFetchData);

            // 合并两组信息并去重
            if (this.fetchData.daily?.length && this.fetchData2?.daily?.length) {
                this.fetchData.daily = this.fetchData.daily
                    ?.concat(this.fetchData2.daily)
                    .sort((a, b) => Number(new Date(a.date)) - Number(new Date(b.date)));

                for (let i = 1; i < this.fetchData.daily?.length; i++) {
                    if (this.fetchData.daily[i].date === this.fetchData.daily[i - 1].date) {
                        this.fetchData.daily.splice(i, 1);
                    }
                }
            }
        }

        // 处理用于下载的数据
        this.downloadDataList = !this.useCompare
            ? get(this.fetchData, "5min", [])
            : get(this.fetchData2, "5min", []).concat(get(this.fetchData, "5min", []));
    }

    // 2、数据处理
    get options() {
        const { seriesDataKey } = this;
        // 根据 switch 获取差异化数据
        const title = this.showBandwidth
            ? `${this.$t("statistics.usageQuery.PrivateNetworkAccelerator.chartOptions.title1")}`
            : `${this.$t("statistics.usageQuery.PrivateNetworkAccelerator.chartOptions.title2")}`;
        const { unit, scale } = this.chartUnitConfig;
        const yAxisName = `${this.$t(
            "statistics.usageQuery.PrivateNetworkAccelerator.chartOptions.yAxisName",
            {
                unit: unit,
            }
        )}`;

        const xAxisData: string[] = [];
        const seriesData: string[] = [];
        const fetchDataList = get(this.fetchData, "5min", []) as Query5Min[];
        fetchDataList
            .sort((a, b) => a.timestamp - b.timestamp)
            .forEach(item => {
                xAxisData.push(timeFormat(item.timestamp * 1000).replace(" ", "\n"));
                seriesData.push(((item[seriesDataKey] || 0) / scale).toFixed(2));
            });

        const space = nUserModule.lang === "en" ? " " : "";

        const options = {
            ...this.comOptions,
            tooltip: {
                trigger: "axis",
                formatter: (a: tooltipParam[]) =>
                    `${a[0].name}<br>${a[0].marker}${space}${title}: ${a[0].value}${unit}`,
            },
            xAxis: {
                type: "category", // 类目轴，从 data 中获取数据
                boundaryGap: false, // 坐标轴两边留白策略
                axisLabel: {
                    formatter: this.xAxisFormatterGenerator(),
                },
                data: xAxisData,
            },
            yAxis: {
                type: "value", // 数值轴
                name: yAxisName,
            },
            series: [
                {
                    type: "line",
                    data: seriesData,
                    areaStyle: THEME_AREA_STYLE["#358AE2"],
                },
            ],
        };

        // 没有数据对比，则直接返回配置
        if (!this.useCompare) return options;

        // 使用数据对比，则继续组装配置
        // const xAxisData2: string[] = [];
        const seriesData2: string[] = [];
        const fetchDataList2 = get(this.fetchData2, "5min", []) as Query5Min[];
        fetchDataList2
            .sort((a, b) => a.timestamp - b.timestamp)
            .forEach(item => {
                // xAxisData2.push(timeFormat(item.timestamp * 1000).replace(" ", "\n"));
                seriesData2.push(((item[seriesDataKey] || 0) / scale).toFixed(2));
            });
        // 调整 series
        options.series = [
            {
                name: `${title}1`,
                type: "line",
                data: seriesData,
                areaStyle: THEME_AREA_STYLE["#358AE2"],
            },
            {
                name: `${title}2`,
                type: "line",
                data: seriesData2,
                areaStyle: THEME_AREA_STYLE["#FA8334"],
            },
        ];
        // 增加图例
        options.legend = {
            data: [`${title}1`, `${title}2`],
        };
        // 调整 tooltip ，动态控制 tooltip ，如果图例只选择一条时就只显示一组信息
        options.tooltip.formatter = (a: tooltipParam[]) => {
            // a[0].name带有换行符号，在firefox下new Date时会异常
            const name2 = timeFormat(
                Number(new Date(Number(new Date(a[0].name.replace("\n", " "))) - this.timeMinus!))
            );
            const name = this.legend1Selected ? a[0].name : name2;
            let tip = `${name}<br>${a[0].marker}${space}${title}: ${a[0].value}${unit}`;
            if (a[1]) {
                tip += `<br>${name2}<br>${a[1].marker}${space}${title}: ${a[1].value}${unit}`;
            }
            return tip;
        };
        return options;
    }

    // 监听图例选择变化
    legendselectchanged({ selected }: { selected: { [legend: string]: boolean } }) {
        const title = this.showBandwidth
            ? `${this.$t("statistics.usageQuery.PrivateNetworkAccelerator.chartOptions.title1")}`
            : `${this.$t("statistics.usageQuery.PrivateNetworkAccelerator.chartOptions.title2")}`;
        this.legend1Selected = selected[`${title}1`];
    }

    // 获取各种缩进计算的配置（根据最大值获取）
    get indentQueryBandwidthConfig() {
        const max = this.getMaxFromList(get(this.fetchData, "daily", []), "topBandwidth");
        return this.getBandwidthUnitConfig(max, true);
    }
    get indentFlowConfig() {
        const max = this.getMaxFromList(get(this.fetchData, "daily", []), "flow");
        return this.getFlowUnitConfig(max, true);
    }

    // 重写 excel 数据拼接方法
    protected tableToExcel() {
        let str = `${this.$t("statistics.usageQuery.PrivateNetworkAccelerator.tableToExcel.excelColumn1")},${this.chartType ===
            `${this.$t("statistics.usageQuery.PrivateNetworkAccelerator.tableToExcel.excelColumn2")}`
            ? `${this.$t("statistics.usageQuery.PrivateNetworkAccelerator.tableToExcel.excelColumn3", {
                mbps: this.Mbps,
            })}`
            : `${this.$t("statistics.usageQuery.PrivateNetworkAccelerator.tableToExcel.excelColumn4", {
                mb: this.MB,
            })}`
            }\n`;

        // 输出格式
        str += this.downloadDataList.reduce((str, item) => {
            str += `${timeFormat(item["timestamp"] * 1000)},`;
            str += (item[this.seriesDataKey] / Math.pow(this.scale, 2)).toFixed(2) + "\n";
            return str;
        }, "");

        // 增加峰值带宽和95峰值带宽
        str += `\n${this.$t("statistics.usageQuery.PrivateNetworkAccelerator.tableToExcel.excelColumn5")}\n`;

        const { titleTipSuffix, fetchData: _fetchData, fetchData2: _fetchData2 } = this;
        const fetchData = this.processQueryFetchData(_fetchData);
        const fetchData2 = this.processQueryFetchData(_fetchData2);
        const { topBandwidth, top95Bandwidth, totalFlow, avgQueryFlow } = fetchData;

        if (this.showBandwidth) {
            str += `${this.$t("statistics.usageQuery.PrivateNetworkAccelerator.tableToExcel.excelColumn6", {
                num: "",
                suffix: titleTipSuffix,
            })},${topBandwidth} ${this.Mbps} \n ${this.$t(
                "statistics.usageQuery.PrivateNetworkAccelerator.tableToExcel.excelColumn7",
                {
                    num: "",
                    suffix: titleTipSuffix,
                }
            )},${top95Bandwidth} ${this.Mbps} \n`;

            if (this.useCompare) {
                str += `${this.$t(
                    "statistics.usageQuery.PrivateNetworkAccelerator.tableToExcel.excelColumn6",
                    {
                        num: "2",
                        suffix: "",
                    }
                )},${fetchData2.topBandwidth} ${this.Mbps} \n ${this.$t(
                    "statistics.usageQuery.PrivateNetworkAccelerator.tableToExcel.excelColumn7",
                    {
                        num: "2",
                        suffix: "",
                    }
                )},${fetchData2.top95Bandwidth} ${this.Mbps} \n`;
            }
        } else {
            str += `${this.$t("statistics.usageQuery.PrivateNetworkAccelerator.tableToExcel.excelColumn8", {
                num: "",
                suffix: titleTipSuffix,
            })},${totalFlow} ${this.MB} \n ${this.$t(
                "statistics.usageQuery.PrivateNetworkAccelerator.tableToExcel.excelColumn9",
                {
                    num: "",
                    suffix: titleTipSuffix,
                }
            )},${avgQueryFlow} ${this.MB} \n`;

            if (this.useCompare) {
                str += `${this.$t(
                    "statistics.usageQuery.PrivateNetworkAccelerator.tableToExcel.excelColumn8",
                    {
                        num: "2",
                        suffix: "",
                    }
                )},${fetchData2.totalFlow} ${this.MB} \n ${this.$t(
                    "statistics.usageQuery.PrivateNetworkAccelerator.tableToExcel.excelColumn9",
                    {
                        num: "2",
                        suffix: "",
                    }
                )},${fetchData2.avgQueryFlow} ${this.MB} \n`;
            }
        }

        this.downloadExcel({
            name: `${this.$t("statistics.usageQuery.PrivateNetworkAccelerator.tableToExcel.excelName", {
                chartType: this.chartType,
            })}`,
            str,
        });
    }
    // 重写下载方法
    protected downloadExcel({ name, str }: { name: string; str: string }) {
        // 此时要用缓存的参数2判断是否启用了数据对比，以保证参数和数据一致
        const useCompare = !!this.searchParams2;

        // 存在对比的时候，是从2个时间范围内选择时间点
        let { startTime, endTime } = this.searchParams;
        if (useCompare) {
            startTime = startTime < this.searchParams2!.startTime ? startTime : this.searchParams2!.startTime;

            endTime = endTime > this.searchParams2!.endTime ? endTime : this.searchParams2!.endTime;
        }

        const t1 = timeFormat(+startTime)
            .replace(/-|\s|:/g, "")
            .slice(0, 12);
        const t2 = timeFormat(+endTime)
            .replace(/-|\s|:/g, "")
            .slice(0, 12);

        downloadCsv(`${name}${t1}-${t2}`, str);
    }

    protected downloadTable() {
        if (this.checkListIsEmpty(this.fetchData?.daily)) return;

        let str = `${this.$t("statistics.usageQuery.BandwidthFlow.tableColumn1")},${this.$t('statistics.usageQuery.PrivateNetworkAccelerator.tableColumn2', {
            unit: this.wrapWithBrackets(this.MB),
        })},${this.$t('statistics.usageQuery.PrivateNetworkAccelerator.tableColumn3', {
            unit: this.wrapWithBrackets(this.Mbps),
        })},${this.$t('statistics.usageQuery.PrivateNetworkAccelerator.tableColumn4')}\n`;

        this.fetchData.daily.forEach((item => {
            str += item.date + ",";
            str += (+item.flow / Math.pow(this.scale, 2)).toFixed(2) + ",";
            str += (+item.topBandwidth / Math.pow(this.scale, 2)).toFixed(2) + ",";
            str += timeFormat(item.topBandwidthTime) + ",\n";
        }))

        this.downloadExcel({
            name: `${this.$t("statistics.common.tab[9]")}-${this.$t("statistics.usageQuery.PrivateNetworkAccelerator.ctTip")}`,
            str
        })
    }
}
</script>

<style lang="scss" scoped></style>
