/**
 * @Description: cache table 在新增和编辑时公用的逻辑
 * @Author: wang <PERSON><PERSON>
 */
 import i18n from "../../../i18n";

 // 缓存设置的各项配置
 // 类型：映射及选项生成（如果放开其他类型，需要调整页面逻辑）
 const CacheModeMap = {
     0: i18n.t("domain.detail.cacheModeMap[0]"),
     1: i18n.t("domain.detail.cacheModeMap[1]"),
     2: i18n.t("domain.detail.cacheModeMap[2]"),
     3: i18n.t("domain.detail.cacheModeMap[3]"),
     4: i18n.t("domain.detail.cacheModeMap[4]"),
     // 5: "正则",
 };
export const cacheModeOptions = Object.keys(CacheModeMap).map(mode => ({
     label: CacheModeMap[mode],
     value: mode * 1, // 需要是 number 类型
 }));

 // 缓存时间：映射及选项生成（最终换算单位 s）
 const CacheTtlMap = {
     // 0: { name: "年", factor: 60 * 60 * 24 * 365 },
     // 1: { name: "月", factor: 60 * 60 * 24 * 31 },
     2: { name: i18n.t("simpleForm.alogicCacheMixin.CacheTtlMap.2"), factor: 60 * 60 * 24 },
     3: { name: i18n.t("simpleForm.alogicCacheMixin.CacheTtlMap.3"), factor: 60 * 60 },
     4: { name: i18n.t("simpleForm.alogicCacheMixin.CacheTtlMap.4"), factor: 60 },
     5: { name: i18n.t("simpleForm.alogicCacheMixin.CacheTtlMap.5"), factor: 1 },
 };
 const cacheTtlMapOptions = Object.keys(CacheTtlMap).map(mode => ({
     label: CacheTtlMap[mode].name,
     value: mode, // string 类型
 }));

 // 缓存类型：映射及选项生成
 const cacheTypeMap = {
     1: i18n.t("simpleForm.alogicCacheMixin.CacheTypeMap.1"),
     2: i18n.t("simpleForm.alogicCacheMixin.CacheTypeMap.2"),
     3: i18n.t("simpleForm.alogicCacheMixin.CacheTypeMap.3"),
     // 4: "custom定制缓存", 配置系统支持custom定制缓存，该缓存规则，控制台只支持展示，不支持变更
 };
 const cacheTypeMapOptions = Object.keys(cacheTypeMap).map(mode => ({
     label: cacheTypeMap[mode],
     value: mode * 1, // number 类型
 }));

 // 初始数据
 const defaultCacheData = {
     mode: 0,
     file_type: "",
     ttl: "80", // 上传的时间
     time: "80", // 显示的时间，配合 timeType
     timeType: "5", // 5 秒
     cache_type: 3,
     cache_with_args: 0,
     priority: 10,
     split: 0,
 };

 // 首字母小写
 function lowerFirst(str) {
     return str.charAt(0).toLowerCase() + str.slice(1);
 }

 export default {
     data() {
         return {
             cacheList: [], // 组件内本地配置
             defaultCacheData,
             CacheModeMap,
             CacheTtlMap,
             cacheTtlMapOptions,
             cacheTypeMapOptions,

             dialogVisible: false,

             rules: {
                 file_type: [
                     { required: true, message: i18n.t("domain.htmlForbid.forbid4"), trigger: "blur" },
                     {
                         // 为空是为了满足创建时，自定义后缀名允许为空；而编辑时必填是由上一条 required:true 限制
                         pattern: "^\\w{1,9}(?:,\\w{1,9})*$|^$",
                         message: i18n.t("domain.detail.placeholder10"),
                     },
                 ],
                 path: [
                     { required: true, message: i18n.t("domain.detail.placeholder73"), trigger: "blur" },
                     {
                         pattern: "^(?:/[\\w-.]+)+(?:,(?:/[\\w-.]+)+)*$",
                         message: i18n.t("domain.detail.placeholder12"),
                     },
                 ],
                 allPath: [
                     { required: true, message: i18n.t("domain.detail.placeholder74"), trigger: "blur" },
                     {
                         pattern:
                             // "^(?:(?:/[\\w-*]+)+\\.?[A-Za-z0-9*]+)+(?:,(?:/[\\w-*]+)+\\.?[A-Za-z0-9*]+)*$",
                             "^([A-Za-z0-9]|[!#$&'()*+,-./:;=?@_~])*$",
                         message: i18n.t("domain.detail.placeholder14"),
                     },
                 ],
                 time: [
                     { required: true, message: i18n.t("simpleForm.alogicCacheMixin.ValidationErrorMessages.RequiredCacheTime"), trigger: "blur" },
                     { pattern: "^\\d*$", message: i18n.t("domain.detail.ttlTip.1") },
                 ],
                 priority: [
                     { required: true, message: i18n.t("domain.detail.placeholder20"), trigger: "blur" },
                     { pattern: "^([1-9]\\d?|100)$", message: i18n.t("simpleForm.alogicCacheMixin.ValidationErrorMessages.InvalidPriority") },
                 ],
             },
         };
     },

     computed: {
         // 动态计算选项
         cacheModeOptions() {
             return cacheModeOptions.map(option => {
                 let disabled = false;

                 // mode 2、3 只支持选一个
                 if (option.value === 2 || option.value === 3) {
                     disabled = this.cacheList.some(item => item.mode === option.value);
                 }

                 return {
                     disabled,
                     ...option,
                 };
             });
         },
     },

     filters: {
         inputPlaceholder(mode) {
             return {
                 0: i18n.t("simpleForm.alogicCacheMixin.FileSuffix.0"),
                 1: i18n.t("simpleForm.alogicCacheMixin.FileSuffix.1"),
                 4: i18n.t("simpleForm.alogicCacheMixin.FileSuffix.4"),
             }[mode];
         },
     },

     // watch: {
     //     cacheList: {
     //         handler(val) {
     //             this.model = val;
     //         },
     //         deep: true,
     //     },
     // },

     methods: {
         // 设置时间
         setTll(cache) {
             const factor = CacheTtlMap[cache.timeType]?.factor;
             cache.ttl = cache.time * factor;
         },

         // “重新发起”时
         init(input) {
             if (input) {
                 // 提交数据未编码，因此不再需要解码操作，直接使用接口数据即可
                 const cacheList = JSON.parse(JSON.stringify(input));
                 const timeStepArr = Object.keys(CacheTtlMap).map(mode => CacheTtlMap[mode].factor);
                 const timeStepMap = Object.keys(CacheTtlMap);
                 this.cacheList = cacheList.map(i => {
                     const stepFindIndex = timeStepArr.findIndex(s => {
                         return parseInt(i.ttl, 10) % s === 0;
                     });
                     // 读取初始数据
                     return {
                         mode: i.mode,
                         file_type: i.file_type,
                         ttl: i.ttl,
                         cache_with_args: i.cache_with_args,
                         priority: i.priority || 10, // 权重默认10
                         timeType: timeStepMap[stepFindIndex],
                         // 根据 ttl 计算出页面展示的数据 time + timeType
                         time: parseInt(i.ttl, 10) / timeStepArr[stepFindIndex],
                         cache_type: i.cache_type, // 修改时需要用到底层返回的原数据
                     };
                 });
             }
         },
         // 自定义校验
         validateProcedure() {
             const result = {
                 valid: true,
                 msg: "",
                 dom: "cache",
             };
             for (let index = 0; index < this.cacheList.length; index += 1) {
                 const element = this.cacheList[index];
                 const { mode, file_type, time, ttl, priority, timeType } = element;
                 const pathPattern = new RegExp(this.rules.path[1].pattern);
                 const allPathPattern = new RegExp(this.rules.allPath[1].pattern);
                 // const timePattern = new RegExp(this.rules.time[1].pattern);
                 const priorityPattern = new RegExp(this.rules.priority[1].pattern);
                 // 没有拓展名内容不行
                 if (!file_type) {
                     result.valid = false;
                     result.msg = i18n.t("simpleForm.alogicCacheMixin.ValidationErrorMessages.selectCache", { mode: CacheModeMap[mode] });
                 } else if (mode === 1 && !pathPattern.test(file_type)) {
                     result.valid = false;
                     result.msg = this.rules.path[1].message;
                 } else if (mode === 4 && !allPathPattern.test(file_type)) {
                     result.valid = false;
                     result.msg = this.rules.allPath[1].message;
                 } else if (time === "") {
                     // time 未输入，
                     result.valid = false;
                     result.msg = i18n.t("domain.detail.ttlTip.0");
                 } else if (isNaN(ttl)) {
                     // !/^\d+$/.test(ttl) 这里不使用数字正则的原因，是因为这里采用计算后的结果，有可能是bigInt，会有越界的情况
                     // 越界后，其实是数字，但是值中包含科学计数法e等符号，会被判定为非数字
                     result.valid = false;
                     result.msg = i18n.t("simpleForm.mixin.ttlTip.itm1");
                 } else if (timeType === "2" && Number(time) > 1095) {
                     result.valid = false;
                     result.msg = i18n.t("simpleForm.alogicCacheMixin.ValidationErrorMessages.CacheTimeExceedsLimit", { value: 1095, type: i18n.t("simpleForm.alogicCacheMixin.timeTypeMap.2") });
                 } else if (timeType === "3" && Number(time) > 26280) {
                     result.valid = false;
                     result.msg = i18n.t("simpleForm.alogicCacheMixin.ValidationErrorMessages.CacheTimeExceedsLimit", { value: 26280, type: i18n.t("simpleForm.alogicCacheMixin.timeTypeMap.3") });
                 } else if (timeType === "4" && Number(time) > 1576800) {
                     result.valid = false;
                     result.msg = i18n.t("simpleForm.alogicCacheMixin.ValidationErrorMessages.CacheTimeExceedsLimit", { value: 1576800, type: i18n.t("simpleForm.alogicCacheMixin.timeTypeMap.4") });
                 } else if (timeType === "5" && Number(time) > 94608000) {
                     result.valid = false;
                     result.msg = i18n.t("simpleForm.alogicCacheMixin.ValidationErrorMessages.CacheTimeExceedsLimit", { value: 94608000, type: i18n.t("simpleForm.alogicCacheMixin.timeTypeMap.5") });
                 } else if (!priority) {
                     result.valid = false;
                     result.msg = i18n.t("simpleForm.alogicCacheMixin.ValidationErrorMessages.RequiredPriority");
                 } else if (!priorityPattern.test(priority)) {
                     result.valid = false;
                     result.msg = i18n.t("simpleForm.alogicCacheMixin.ValidationErrorMessages.InvalidPriority");
                 }

                 if (!result.valid) {
                     return Promise.resolve(result);
                 }
             }
             // 重复性校验
             // 后缀名列表
             let cacheList = this.cacheList.filter(item => item.mode === 0);
             let repeatedName = this.checkRepeatedName(cacheList);
             if (repeatedName) {
                 result.valid = false;
                 result.msg = i18n.t("simpleForm.alogicCacheMixin.ValidationErrorMessages.DuplicateValidation", { repeatedName, type: lowerFirst(this.$t("domain.detail.cacheModeMap.0")) });
                 return Promise.resolve(result);
             }
             // 目录列表
             cacheList = this.cacheList.filter(item => item.mode === 1);
             repeatedName = this.checkRepeatedName(cacheList);
             if (repeatedName) {
                 result.valid = false;
                 result.msg = i18n.t("simpleForm.alogicCacheMixin.ValidationErrorMessages.DuplicateValidation", { repeatedName, type: lowerFirst(this.$t("domain.detail.cacheModeMap.1")) });
                 return Promise.resolve(result);
             }
             // 全路径文件列表
             cacheList = this.cacheList.filter(item => item.mode === 4);
             repeatedName = this.checkRepeatedName(cacheList);
             if (repeatedName) {
                 result.valid = false;
                 result.msg = i18n.t("simpleForm.alogicCacheMixin.ValidationErrorMessages.DuplicateValidation", { repeatedName, type: lowerFirst(this.$t("domain.detail.cacheModeMap.4")) });
                 return Promise.resolve(result);
             }
             return Promise.resolve(result);
         },

         // 从 cache 数组的 name 字段中检查出重复内容
         checkRepeatedName(cacheList) {
             if (cacheList.length === 0) return "";

             const nameLsit = cacheList
                 .map(item => item.file_type)
                 .join(",")
                 .split(",");
             const nameMap = {};
             for (let idx = 0; idx < nameLsit.length; idx += 1) {
                 if (nameMap[nameLsit[idx]]) {
                     return nameLsit[idx];
                 } else {
                     nameMap[nameLsit[idx]] = true;
                 }
             }

             return "";
         },
     },
 };
