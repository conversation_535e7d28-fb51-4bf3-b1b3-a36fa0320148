import basic from "./basic";
import origin from "./origin";
import cache from "./cache";
import deny from "./deny";
import { DomainModule } from "@/store/modules/domain";

export default {
    components: {
        basic,
        cache,
        origin,
        deny,
    },
    data() {
        return {
            configData: {},
            ecgwPorpsData: {},
        };
    },
    computed: {
        // basicData() {},
        // originData() {},
        // cacheData() {},
        // denyData() {},
    },
    methods: {
        formValidate2Promise(form) {
            return new Promise((resolve, reject) => {
                form.validate(valid => {
                    if (valid) {
                        resolve();
                    } else {
                        this.gatewayTabs.forEach(item => {
                            if (item.prop === form.$attrs.name) {
                                item.error = true;
                                return;
                            }
                            if (item.children) {
                                item.children.map(child => {
                                    if (child.prop === form.$attrs.name) {
                                        if (["sourceStation", "httpReqHeader"].includes(form.$attrs.name)) {
                                            DomainModule.nGetDomainEditIconShow("origin");
                                        }
                                        if (
                                            ["filetypeTtl", "errorCode", "respHeaders"].includes(
                                                form.$attrs.name
                                            )
                                        ) {
                                            DomainModule.nGetDomainEditIconShow("cache");
                                        }
                                        if (["refererChain", "ipBlackWhiteList"].includes(form.$attrs.name)) {
                                            DomainModule.nGetDomainEditIconShow("deny");
                                        }
                                        DomainModule.nGetDomainEditIconShow(form.$attrs.name);
                                        child.error = true;
                                        item.error = true;
                                    }
                                });
                            }
                        });
                        reject(this.$t("domain.detail.tip101"));
                    }
                });
            });
        },
    },
};
