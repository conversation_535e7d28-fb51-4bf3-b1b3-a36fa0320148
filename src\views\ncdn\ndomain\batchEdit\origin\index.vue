<template>
    <div style="height: 100%">
        <el-tabs
            v-if="showCurrentTab"
            v-model="subActiveTab"
            :tab-position="'top'"
            class="domain-edit-menu">
            <el-tab-pane
                v-for="(item, key) in subTabChildren"
                :key="item.prop + key"
                :label="item.label"
                :name="item.prop"
                :ref="item.prop"
                :disabled="item.disabled"
            >
                <span slot="label">
                    <i class="el-icon-warning" v-show="domain_batch_icon_show[item.prop]" />
                    {{ item.name }}
                </span>
            </el-tab-pane>
        </el-tabs>
        <el-form
            name="sourceStation"
            ref="sourceStationForm"
            :model="sourceStationForm"
            class="wrap-content"
            :rules="rules"
            label-width="124px"
            v-show="subActiveTab === 'sourceStation'"
            :disabled="false"
            label-position="left"
        >
            <p class="label-name">{{ $t("domain.detail.label23") }}</p>
            <el-form-item
                :label="$t('domain.create.originServer')"
                prop="origin"
                :rules="rules.originAll"
                class="is-required"
            >
                <div class="origin-add">
                    <el-button
                        :disabled="sourceStationForm.origin.length >= 60"
                        type="primary"
                        @click="onOperator(null, 'create', 'origin')"
                    >
                        {{ $t("domain.add2") }}
                    </el-button>
                    <span class="info-text">{{ $t("domain.batch.tip4") }}</span>
                </div>
                <el-table
                    :empty-text="$t('common.table.empty')"
                    class="origin-table"
                    :data="sourceStationForm.origin"
                    stripe
                    border
                    header-row-class-name="origin-table__header"
                >
                    <el-table-column
                        :label="$t('domain.create.number')"
                        type="index"
                        width="60"
                    ></el-table-column>
                    <el-table-column prop="is_xos" :label="$t('domain.create.originType')">
                        <template slot-scope="scope">
                            {{ xosMap[scope.row.is_xos] }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="origin" :label="$t('domain.create.originServer')"> </el-table-column>
                    <el-table-column prop="role" :label="$t('domain.create.level')">
                        <template slot-scope="scope">
                            {{ roleMap[scope.row.role] }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="weight" :label="$t('domain.create.weight')"> </el-table-column>
                    <el-table-column :label="$t('domain.operate')">
                        <template slot-scope="scope">
                            <el-button
                                type="text"
                                @click="onOperator(scope.row, 'edit', 'origin', scope.$index)"
                                >{{ $t("domain.modify") }}</el-button
                            >
                            <el-button
                                type="text"
                                @click="onOperator(scope.row, 'delete', 'origin', scope.$index)"
                                >{{ $t("domain.delete") }}</el-button
                            >
                        </template>
                    </el-table-column>
                </el-table>

                <!-- <el-dialog
                    :title="`新增源站`"
                    :append-to-body="true"
                    :close-on-click-modal="false"
                    :visible.sync="originDialogVisible"
                    :before-close="cancel"
                    class="origin-dialog"
                    width="480px"
                >
                    <el-form :rules="rules" :model="originForm" ref="originForm" label-width="60px">
                        <el-form-item
                            v-if="originDialogVisible"
                            :label="$t('domain.create.originServer')"
                            prop="origin"
                            :rules="rules.dialog_origin"
                        >
                            <el-input v-model="originForm.origin"></el-input>
                            <div
                                slot="error"
                                slot-scope="{ error }"
                                class="el-form-item__error error-wrapper-mt-6"
                            >
                                {{ error }}
                            </div>
                        </el-form-item>
                        <el-form-item :label="$t('domain.create.level')" prop="role" :rules="rules.role">
                            <el-select v-model="originForm.role">
                                <el-option value="master" :label="$t('domain.create.primary')" />
                                <el-option value="slave" :label="$t('domain.create.secondary')" />
                            </el-select>
                            <div
                                slot="error"
                                slot-scope="{ error }"
                                class="el-form-item__error error-wrapper-mt-6"
                            >
                                {{ error }}
                            </div>
                        </el-form-item>
                    </el-form>
                    <div slot="footer">
                        <el-button class="dialog-btn" @click="cancel">取消 </el-button>
                        <el-button type="primary" class="dialog-btn" @click="submitOrigin"> 确定 </el-button>
                    </div>
                </el-dialog> -->
            </el-form-item>
            <p class="label-name">{{ $t("domain.create.originPolicy") }}</p>
            <el-form-item
                :label="$t('domain.create.originPolicy')"
                prop="backorigin_protocol"
                :rules="rules.backorigin_protocol"
                class="form-item-div"
            >
                <div class="autoform-item">
                    <el-radio-group
                        v-model="sourceStationForm.backorigin_protocol"
                        fill="#3d73f5"
                        class="radio-group"
                    >
                        <template>
                            <el-radio
                                v-for="o in originProtocolOption"
                                :key="o.value"
                                :label="o.value"
                                :disabled="o.disabled"
                            >
                                <span v-html="o.displayValue" class="radio-button-display"></span>
                            </el-radio>
                        </template>
                    </el-radio-group>
                    <div class="tooltips">
                        {{ $t("domain.create.tip6") }}
                    </div>
                </div>
            </el-form-item>
            <!-- <el-form-item :label="$t('domain.create.originPort')" prop="http_origin_port" :rules="rules.http_origin_port">
                <div class="wrap">
                    <el-row :gutter="15">
                        <el-col :span="4">
                            <div class="http-div">
                                <span class="http-span">http</span>
                                <el-input v-model="sourceStationForm.http_origin_port"></el-input>
                            </div>
                        </el-col>
                        <el-col :span="4">
                            <div class="https-div">
                                <span class="https-span">https</span>
                                <el-input
                                    :readonly="true"
                                    v-model="sourceStationForm.https_origin_port"
                                ></el-input>
                            </div>
                        </el-col>
                    </el-row>
                </div>
            </el-form-item> -->
            <el-form-item :label="$t('domain.create.originPort')" class="is-required">
                <div class="wrap">
                    <el-row :gutter="15">
                        <el-col :span="4">
                            <div class="http-div">
                                <el-form-item
                                    label="http"
                                    prop="http_origin_port"
                                    :rules="rules.http_origin_port"
                                    label-width="35px"
                                >
                                    <el-input v-model="sourceStationForm.http_origin_port"></el-input>
                                </el-form-item>
                            </div>
                        </el-col>
                        <el-col :span="4">
                            <div class="https-div">
                                <el-form-item
                                    label="https"
                                    prop="https_origin_port"
                                    :rules="rules.https_origin_port"
                                    label-width="38px"
                                >
                                    <el-input v-model="sourceStationForm.https_origin_port"></el-input>
                                </el-form-item>
                            </div>
                        </el-col>
                    </el-row>
                </div>
            </el-form-item>
            <el-form-item :label="$t('domain.create.originHost2')" prop="req_host">
                <el-input
                    v-model="sourceStationForm.req_host"
                    :placeholder="$t('domain.enter')"
                    style="width: 375px"
                ></el-input>
                <div class="tooltips">
                    {{ $t("domain.create.tip8") }}
                </div>
            </el-form-item>
        </el-form>
        <el-form
            name="httpReqHeader"
            ref="httpReqHeaderForm"
            :model="httpReqHeaderForm"
            class="wrap-content"
            :rules="rules"
            label-width="124px"
            v-show="subActiveTab === 'httpReqHeader'"
            :disabled="false"
        >
            <p class="label-name">{{ $t("domain.detail.label26") }}</p>
            <div class="form-box">
                <el-button @click="showHttpReqHeaderDialog()" type="primary"> {{ $t("domain.add2") }} </el-button>

                <span class="note">
                    {{ $t("domain.detail.tip97") }}
                </span>

                <el-table :empty-text="$t('common.table.empty')" :data="httpReqHeaderForm.req_headers">
                    <el-table-column :label="$t('domain.detail.label43')" prop="key"></el-table-column>
                    <el-table-column :label="$t('domain.detail.label44')" prop="value"></el-table-column>
                    <el-table-column :label="$t('domain.operate')">
                        <template slot-scope="{ row }">
                            <el-button type="text" @click="showHttpReqHeaderDialog(row)">
                                {{ $t("domain.modify") }}
                            </el-button>
                            <el-button type="text" @click="deleteHeader(row)">
                                {{ $t("domain.delete") }}
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <el-dialog
                    :modal-append-to-body="false"
                    :title="$t('domain.batch.label1')"
                    :visible.sync="dialogVisible"
                    :close-on-click-modal="false"
                    width="800px"
                >
                    <el-form :rules="rules" :model="headerForm" ref="headerForm" label-width="85px">
                        <el-form-item :label="$t('domain.detail.label43')" prop="key">
                            <el-input
                                :placeholder="$t('domain.detail.tip76')"
                                v-model="headerForm.key"
                                maxlength="150"
                            />
                            <div
                                slot="error"
                                slot-scope="{ error }"
                                class="el-form-item__error error-wrapper"
                            >
                                {{ error }}
                            </div>
                            <div class="note">
                                <div>{{ $t("domain.detail.tip16") }}</div>
                                <div>
                                    {{ $t("domain.detail.tip77") }}
                                </div>
                            </div>
                        </el-form-item>
                        <el-form-item :label="$t('domain.detail.label44')" prop="value">
                            <el-input
                                :placeholder="$t('domain.detail.placeholder5')"
                                v-model="headerForm.value"
                                maxlength="300"
                            />
                            <div
                                slot="error"
                                slot-scope="{ error }"
                                class="el-form-item__error error-wrapper"
                            >
                                {{ error }}
                            </div>
                            <div class="note">
                                <div>{{ $t("domain.detail.tip98") }}</div>
                                <div>{{ $t("domain.detail.tip99") }}</div>
                                <div>{{ $t("domain.detail.tip100") }}</div>
                            </div>
                        </el-form-item>
                    </el-form>
                    <template #footer>
                        <el-button @click="dialogVisible = false">
                            {{ $t("common.dialog.cancel") }}
                        </el-button>
                        <el-button type="primary" @click="submitHeader">
                            {{ $t("common.dialog.submit") }}
                        </el-button>
                    </template>
                </el-dialog>
            </div>
        </el-form>
        <origin-dialog
            :dialogVisible="originDialogVisible"
            :originForm="originForm"
            :from="currentType"
            :originList="sourceStationForm.origin"
            :currenIndex="String(currenIndex)"
            :useEcgw="1"
            :isBatch="true"
            @cancel="cancel"
            @submit="submitOrigin"
            @bucketHandle="onBucketHandle"
        />
    </div>
</template>

<script>
import minxin from "@/components/simpleform/minxin";
import gatewayMapMix from "../gatewayMapMinxin";
import { ip, domain } from "@cdnplus/common/config/pattern";
import { DomainModule } from "@/store/modules/domain";
import OriginDialog from "@/views/ncdn/ndomain/batchEdit/OriginDialog.vue";
import { portRegex } from '@/utils/validator.utils';
import { checkOriginHost } from '@/utils/pattern';
import { reservedIp } from "@/config/npattern";

const urlReg = new RegExp(domain);
const ipReg = new RegExp(ip);
const reservedIpReg = new RegExp(reservedIp); // ipv4 中的保留 ip 地址

export default {
    name: "origin",
    components: {
        OriginDialog,
    },
    mixins: [minxin, gatewayMapMix],
    props: ["isTabValid", "showCurrentTab"],
    data() {
        return {
            xosMap: {
                0: this.$t("domain.create.xos0"),
                1: this.$t("domain.create.xos1"),
            },
            domain_batch_icon_show: DomainModule.domain_batch_icon_show,
            gatewayTabs: [],
            subActiveTab: "sourceStation",
            currentType: "create",
            currenIndex: "",
            type: "1",
            text: "",
            // 源站配置
            roleMap: {
                master: this.$t("domain.create.primary"),
                slave: this.$t("domain.create.secondary"),
            },
            sourceStationForm: {
                backorigin_protocol: "http",
                req_host: "",
                origin: [],
                http_origin_port: 80,
                https_origin_port: 443,
            },
            originForm: { origin: "", role: "master", weight: "10", is_xos: 0 },
            originDialogVisible: false,
            // 回源HTTP请求头
            httpReqHeaderForm: {
                req_headers: [],
            },
            editRowKey: "", // 记录被修改的item的key值
            editRowLevel: "", // 记录被修改的item的level值
            curIndex: null,
            dialogVisible: false,
            headerForm: {
                key: "",
                value: "",
            },
            temp_bucket: "",
            rules: {
                originAll: [{ required: true, validator: this.validateOriginAll, trigger: "blur, change" }],
                dialog_origin: [
                    { required: true, message: this.$t("domain.detail.tip73"), trigger: "blur" },
                    {
                        validator: this.validateOrigin,
                        trigger: "blur",
                    },
                ],
                role: [{ required: true, message: this.$t("domain.detail.tip74"), trigger: "blur" }],
                backorigin_protocol: [
                    { required: true, message: this.$t("domain.detail.tip75"), trigger: "change" },
                ],
                http_origin_port: [
                    // { required: true, message: this.$t("domain.detail.tip90"), trigger: "blur" },
                    { validator: this.valid_http_origin_port, trigger: "blur" },
                ],
                https_origin_port: [{ validator: this.https_origin_port_valid, trigger: "blur" }],
                req_host: [{ required: false, validator: this.checkGenericDomain, trigger: "blur" }],
                // HTTP请求头
                key: [
                    {
                        required: true,
                        message: this.$t("domain.detail.tip76"),
                        trigger: "blur",
                    },
                    {
                        pattern: "^[\\w-]+$",
                        message: this.$t("domain.detail.tip16"),
                        trigger: ["blur", "change"],
                    },
                    {
                        validator: (rule, value, callback) => {
                            if (
                                this.curIndex !== null &&
                                this.httpReqHeaderForm.req_headers[this.curIndex].key === value
                            )
                                callback();
                            else if (this.httpReqHeaderForm.req_headers.some(header => header.key === value))
                                callback(new Error(this.$t("domain.detail.tip28")));
                            else if (value && value.toLowerCase() === "host")
                                callback(this.$t("domain.detail.tip77"));
                            else callback();
                        },
                        trigger: ["blur", "change"],
                    },
                ],
                value: [
                    {
                        pattern:
                            "^[^\\u4e00-\\u9fa5\\u3002\\uff1f\\uff01\\uff0c\\u3001\\uff1b\\uff1a\\u201c\\u201d\\u2018\\u2019\\uff08\\uff09\\u300a\\u300b\\u3008\\u3009\\u3010\\u3011\\u300e\\u300f\\u300c\\u300d\\ufe43\\ufe44\\u3014\\u3015\\u2026\\u2014\\uff5e\\ufe4f\\uffe5]+$",
                        message: this.$t("domain.detail.tip29"),
                        trigger: ["blur", "change"],
                    },
                    {
                        validator: (rule, value, callback) => {
                            if (/^\s+$/gi.test(value)) callback(new Error(this.$t("domain.detail.tip78")));
                            else callback();
                        },
                        trigger: ["blur", "change"],
                    },
                    // {
                    //     pattern: "^[\\S-]+$",
                    //     message: this.$t("domain.detail.tip78"),
                    //     trigger: ["blur", "change"],
                    // },
                ],
            },
        };
    },
    computed: {
        subTabChildren() {
            return this.gatewayTabs.find(item => item.prop === "origin").children;
        },
        formData() {
            const returnData = {
                basic_conf: {},
            };
            const {
                origin = [],
                backorigin_protocol,
                req_host,
                http_origin_port,
                https_origin_port,
            } = this.sourceStationForm;
            const { req_headers = [] } = this.httpReqHeaderForm;
            returnData.origin = origin;
            // 如果origin有值，需要传xos_origin_is = 0 给后端, 同时关闭zos
            if (returnData.origin && returnData.origin.length > 0) {
                returnData.xos_origin_is = 0;
                returnData.zos_origin = { switch: 0 };
            }

            // 去掉is_xos
            if (returnData.origin && returnData.origin.length > 0) {
                returnData.origin.map(item => {
                    delete item?.is_xos;
                });
            }

            returnData.basic_conf.http_origin_port = http_origin_port * 1;
            returnData.basic_conf.https_origin_port = https_origin_port * 1;
            returnData.backorigin_protocol = backorigin_protocol;
            returnData.req_host = req_host;
            returnData.req_headers = req_headers;
            // 需要去掉前后空格，保留中间的空格
            if (returnData.req_headers && returnData.req_headers.length > 0) {
                for (let i = 0; i < returnData.req_headers.length; i++) {
                    returnData.req_headers[i].value = returnData.req_headers[i].value.trim();
                }
            }
            return returnData;
        },
    },
    watch: {
        isTabValid: {
            deep: true,
            handler() {
                this.gatewayTabs.forEach(item => {
                    if (item.label === this.$t("domain.detail.tab3")) {
                        item.children.forEach(e => {
                            if (this.domain_batch_icon_show[e.prop]) {
                                this.$nextTick(() => {
                                    this.subActiveTab = e.prop;
                                });
                            }
                        });
                    }
                });
            },
            immediate: true,
        },
    },
    methods: {
        validateOriginAll(rule, value, callback) {
            let count = 0;
            const temp_origin = this.sourceStationForm.origin.map(item => item.origin);
            const origin_set = new Set(temp_origin);
            let element = {};
            for (let i = 0; i < this.sourceStationForm.origin.length; i++) {
                element = this.sourceStationForm.origin[i];
                if (this.sourceStationForm.origin[i].role === "master") {
                    count++;
                }
            }
            const { origin } = element;
            if (origin_set.size !== temp_origin.length) {
                callback(this.$t("domain.create.tip14"));
            } else if (count < 1) {
                callback(this.$t("domain.create.tip18"));
            } else if (!ipReg.test(origin) && !urlReg.test(origin)) {
                callback(this.$t("domain.detail.tip89"));
            } else if (reservedIpReg.test(origin)) {
                callback(this.$t("domain.create.tip16"));
            } else {
                callback();
            }
            callback();
        },
        // 源站配置 页面用到的所有方法
        async submitOrigin() {
            if (this.currentType === "create") {
                this.sourceStationForm.origin.push({
                    ...this.originForm,
                });
            } else {
                // 修改
                this.$set(this.sourceStationForm.origin, this.currenIndex, this.originForm);
            }
            this.originDialogVisible = false;
        },
        cancel() {
            this.originDialogVisible = false;
        },
        onBucketHandle(val) {
            this.temp_bucket = val;
        },
        async onOperator(row, currentType, tabName, i) {
            if (
                currentType === "create" &&
                tabName === "origin" &&
                this.sourceStationForm.origin &&
                this.sourceStationForm.origin.length >= 60
            ) {
                this.$message({
                    showClose: true,
                    message: this.$t("domain.detail.tip96"),
                    type: "error",
                });
            }
            this.currentType = currentType;
            this.currenIndex = i;
            if (currentType === "create") {
                const defaultFormMap = {
                    origin: { origin: "", role: "master", weight: "10", is_xos: 0 },
                };
                row = defaultFormMap[tabName];
            }
            if (currentType === "delete") {
                const msgMap = {
                    origin: `${this.$t("domain.create.tip34")}${row.origin}吗？`,
                };

                // 二次确认弹窗
                await this.$confirm(msgMap[tabName], this.$t("domain.delete"), {
                    type: "warning",
                });
                this.sourceStationForm[tabName].splice(i, 1);
            } else {
                this.originDialogVisible = true;
                row.weight = row.weight ? row.weight : "10";
                this.originForm = { ...row };
            }
        },
        async valid_http_origin_port(rule, value, callback) {
            const pattern = portRegex;
            if (
                this.sourceStationForm.http_origin_port === "" ||
                this.sourceStationForm.http_origin_port === null ||
                this.sourceStationForm.http_origin_port === undefined
            ) {
                return callback(new Error(this.$t("domain.detail.tip90")));
            }
            if (
                !pattern.test(this.sourceStationForm.http_origin_port) ||
                Number(this.sourceStationForm.http_origin_port) === 443
            ) {
                return callback(new Error(this.$t("domain.detail.tip91")));
            }
            return callback();
        },
        async https_origin_port_valid(rule, value, callback) {
            const pattern = portRegex;
            if (
                this.sourceStationForm.https_origin_port === "" ||
                this.sourceStationForm.https_origin_port === null ||
                this.sourceStationForm.https_origin_port === undefined
            ) {
                return callback(new Error(this.$t("domain.detail.tip92")));
            }
            if (!pattern.test(this.sourceStationForm.https_origin_port)) {
                return callback(new Error(this.$t("domain.detail.tip93")));
            }
            return callback();
        },
        checkGenericDomain(rule, value, callback) {
            if (
                this.sourceStationForm.req_host &&
                !checkOriginHost(this.sourceStationForm.req_host)
            ) {
                callback(new Error(this.$t("domain.create.tip19")));
            }
            callback();
        },
        validateOrigin(rule, value, callback) {
            const { origin } = this.originForm;
            if (!ipReg.test(origin) && !urlReg.test(origin)) {
                callback(this.$t("domain.detail.tip89"));
            } else if (reservedIpReg.test(origin)) {
                callback(this.$t("domain.create.tip16"));
            }
            callback();
        },
        // 回源HTTP请求头 页面用到的方法
        submitHeader() {
            this.$refs.headerForm.validate(valid => {
                if (valid) {
                    if (this.curIndex !== null) {
                        this.httpReqHeaderForm.req_headers.splice(this.curIndex, 1, {
                            ...this.headerForm,
                        });
                        if (this.headerForm.key === this.editRowKey) {
                            // key值未改变，视为修改，将level原样返回给后端
                            Object.assign(this.httpReqHeaderForm.req_headers[this.curIndex], {
                                level: this.editRowLevel,
                            });
                        } else {
                            // key值改变，视为新增，默认下发level:"1,2,3"
                            // Object.assign(this.httpReqHeaderForm.req_headers[this.curIndex], {
                            //     level: "1,2,3",
                            // });
                            delete this.httpReqHeaderForm.req_headers[this.curIndex].level;
                        }
                    } else {
                        this.httpReqHeaderForm.req_headers.push(this.headerForm);
                    }
                    this.dialogVisible = false;
                }
            });
        },
        async deleteHeader(header) {
            await this.$confirm(this.$t("domain.detail.tip34"), this.$t("domain.delete"), {
                confirmButtonText: this.$t("common.dialog.submit"),
                cancelButtonText: this.$t("common.dialog.cancel"),
                type: "warning",
            });
            this.httpReqHeaderForm.req_headers = this.httpReqHeaderForm.req_headers.filter(
                item => item.key !== header.key
            );
        },
        showHttpReqHeaderDialog(header) {
            this.dialogVisible = true;
            if (header) {
                this.editRowLevel = header.level;
                this.editRowKey = header.key;
                this.curIndex = this.httpReqHeaderForm.req_headers.findIndex(item => item.key === header.key);
                this.headerForm = { ...header };
            } else {
                this.curIndex = null;
                // 新增的时候，向后端默认下发level:"1,2,3"，level不在页面进行展示
                this.headerForm = { key: "", value: "" };
            }
        },
    },
    created() {
        this.gatewayTabs = [...this.defaultTabs];
    },
};
</script>

<style lang="scss" scoped>
@import "@/components/index.scss";
.el-icon-warning {
    color: $g-color-red;
}
.wrap-content {
    height: 100%;
    overflow-x: hidden;
}
::v-deep {
    .el-tabs__header {
        margin: 0;
    }
}
.label-name {
    font-size: 14px;
    font-weight: bold;
    margin: 20px 0 8px 0;
    width: 600px;
    overflow: hidden;
}
::v-deep {
    .el-form .el-form-item {
        margin-bottom: 20px;
    }
}
.origin-add {
    margin: 0 0 6px 0;
}
.tooltips {
    font-size: 12px;
    line-height: 12px;
    margin-top: 8px;
    color: $neutral-7;
}
.tooltips-text {
    font-size: 12px;
    line-height: 12px;
    margin: 8px 0 0 8px;
    color: $neutral-7;
}
.info-text {
    margin-left: 8px;
    font-size: 12px;
    color: $neutral-7;
}
// .word-wrap {
//     color: #6d7dc5;
// }
.form-box {
    // margin: 0 0 0 20px;
}
.note {
    color: $neutral-7;
    font-size: 12px;
    div:nth-child(n + 2) {
        margin-top: -8px;
    }
}
.wrap {
    .http-div {
        min-width: 280px;
    }
    .https-div {
        margin-left: 20px;
        min-width: 280px;
    }
    .http-span,
    .https-span {
        margin-right: 8px;
        font-size: 12px;
    }
    .el-input {
        min-width: 70px;
        max-width: 80px;
    }
}
.tab-info {
    font-size: 12px;
    color: $color-danger;
}
.error-wrapper {
    margin-top: -10px;
}
.error-wrapper-mt-6 {
    margin-top: -6px;
}
.radio-group {
    display: inline-block !important;
}
</style>
