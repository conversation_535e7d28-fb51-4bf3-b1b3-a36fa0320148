/*
 * @Description: area 和 isp 公用的逻辑
 * @Author: wang <PERSON><PERSON>gong
 */

import i18n from "@/i18n";
import { ScaleModule } from "@/store/modules/scale";
import { conversionUnit } from "@/utils/utils";

// 输出映射
const userTabMap = {
  bandwidth: i18n.t("statistics.eas.tab[0]"),
  flow: i18n.t("statistics.eas.tab[1]"),
  connections: i18n.t("statistics.eas.tab[2]"),
};

export default {
  data() {
    return {
      fetchDataList: [], // 请求的列表数据
      currentTypeMap: [
        {
          label: this.$t("statistics.eas.tab[0]"),
          value: "bandwidth",
        },
        {
          label: this.$t("statistics.eas.tab[1]"),
          value: "flow",
        },
        {
          label: this.$t("statistics.eas.tab[2]"),
          value: "connections",
        },
      ],
    };
  },

  computed: {
    unitTabMap() {
      return {
        bandwidth: this.scale === 1024 ? "Mibps" : "Mbps",
        flow: this.scale === 1024 ? "MiB" : "MB",
        connections: ""
      }
    },
    tabName() {
      return `${userTabMap[this.queryForm.currentType]}`;
    },
    // 获取进制基数
    scale() {
      return ScaleModule.scale;
    },

    // 表格中显示单位
    tableUnit() {
      return `${userTabMap[this.queryForm.currentType]}`;
    },

    // 处理展示数据（通用处理）
    showDataListBase() {
      const type = this.queryForm.currentType === "flow" ? "byte" : "bps";
      return this.fetchDataList
        .map(item => {
          const currentData =
            this.queryForm.currentType !== "connections"
              ? conversionUnit(+item[this.queryForm.currentType], type, 4, false, false, this.scale)
              : item[this.queryForm.currentType];
          // *数据处理
          // eslint-disable-next-line
          item.flow_per = parseFloat(item.flow_per).toFixed(2);
          // eslint-disable-next-line
          item.connections_per = parseFloat(item.connections_per).toFixed(2);

          return {
            ...item,
            currentData,
          };
        })
        .sort((a, b) => b[this.queryForm.currentType] - a[this.queryForm.currentType]); // 倒叙排列展示
    },
  },
};
