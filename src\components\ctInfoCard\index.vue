<!-- 图标类信息卡片 -->
<template>
    <div
        class="ct-info-card"
        :class="{ 'is-mini': size === 'mini', 'no-padding': !isPadding, 'header-small': headerSmall }"
    >
        <div class="flex-row-style card-header" :style="headerStyle">
            <div>
                <div class="flex-row-style card-title">
                    <slot name="title">{{ title }}</slot>
                    <slot name="headerTip">
                        <div v-if="headerTip" class="tip-common-style header-tip">
                            {{ headerTip }}
                        </div>
                    </slot>
                    <el-tooltip v-if="titleTip" effect="dark" :content="titleTip" placement="top">
                        <i class="el-icon-warning icon-tip" />
                    </el-tooltip>
                </div>
                <div v-if="tipText" class="info-card-tip">{{ tipText }}</div>
            </div>
            <slot name="right"></slot>
        </div>
        <slot />
    </div>
</template>

<script>
export default {
    name: "index",
    props: {
        title: {
            type: String,
            default: "",
        },
        titleHeight: {
            type: String,
            default: "",
        },
        titleTip: {
            type: String,
            default: "",
        },
        tipText: {
            type: String,
            default: "",
        },
        size: {
            type: String,
            default: "",
        },
        isPadding: {
            type: Boolean,
            default: true,
        },
        headerTip: {
            type: String,
            default: "",
        },
        headerSmall: {
            type: Boolean,
            default: false,
        },
    },
    computed: {
        headerStyle() {
            if (!this.titleHeight) {
                return {};
            }

            return {
                height: this.titleHeight,
            };
        },
    },
};
</script>

<style scoped lang="scss">
@import "./index.scss";
</style>
