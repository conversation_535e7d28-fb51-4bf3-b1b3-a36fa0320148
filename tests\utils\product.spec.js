import { describe, test, expect } from '@jest/globals';
import { isStaticProduct, getGatewayType, domainEndsWithSpecialSuffix } from '@/utils/product';

describe('isStaticProduct', () => {
    test('should return true for product "001"', () => {
        expect(isStaticProduct("001")).toBeTruthy();
    });

    test('should return true for product "003"', () => {
        expect(isStaticProduct("003")).toBeTruthy();
    });

    test('should return true for product "008"', () => {
        expect(isStaticProduct("008")).toBeTruthy();
    });

    test('should return true for product "014"', () => {
        expect(isStaticProduct("014")).toBeTruthy();
    });

    test('should return true for product "004"', () => {
        expect(isStaticProduct("004")).toBeTruthy();
    });

    test('should return false for product "002"', () => {
        expect(isStaticProduct("002")).toBeFalsy();
    });

    test('should return false for product "005"', () => {
        expect(isStaticProduct("005")).toBeFalsy();
    });
});


describe('getGatewayType', () => {
    test('should return the provided gateway type if it is valid', () => {
        const product = "006";
        const gatewayType = 6;
        expect(getGatewayType(product, gatewayType)).toBe(gatewayType);
    });

    test('should return default gateway type 6 if product type is 006 and gateway type is undefined', () => {
        const product = "006";
        const gatewayType = undefined;
        expect(getGatewayType(product, gatewayType)).toBe(6);
    });

    test('should return default gateway type 1 if product type is not 006 and gateway type is undefined', () => {
        const product = "001";
        const gatewayType = undefined;
        expect(getGatewayType(product, gatewayType)).toBe(1);
    });

    test('should return default gateway type 6 if product type is 006 and provided gateway type is not valid', () => {
        const product = "006";
        const gatewayType = 99;
        expect(getGatewayType(product, gatewayType)).toBe(6);
    });

    test('should return provided gateway type if product type is not 006 and provided gateway type is valid', () => {
        const product = "001";
        const gatewayType = 1;
        expect(getGatewayType(product, gatewayType)).toBe(gatewayType);
    });
});


describe('domainEndsWithSpecialSuffix', () => {
    test('should return false if domain does not end with any suffix in the list', () => {
        const domain = 'example.com';
        const suffixList = ['net', 'org'];
        expect(domainEndsWithSpecialSuffix(domain, suffixList)).toBe(false);
    });

    test('should return true if domain ends with at least one suffix in the list', () => {
        const domain = 'example.com';
        const suffixList = ['com', 'org'];
        expect(domainEndsWithSpecialSuffix(domain, suffixList)).toBe(true);
    });

    test('should handle empty or null suffixes in the list', () => {
        const domain = 'example.com';
        const suffixList = ['', null, 'com'];
        expect(domainEndsWithSpecialSuffix(domain, suffixList)).toBe(true);
    });

    test('should return false if suffixList is empty', () => {
        const domain = 'example.com';
        const suffixList = [];
        expect(domainEndsWithSpecialSuffix(domain, suffixList)).toBe(false);
    });

    test('should handle undefined or null values in suffixList', () => {
        const domain = 'example.com';
        const suffixList = [undefined, null, 'com'];
        expect(domainEndsWithSpecialSuffix(domain, suffixList)).toBe(true);
    });

    test('should return false if all suffixes are empty or null', () => {
        const domain = 'example.com';
        const suffixList = [undefined, null, ''];
        expect(domainEndsWithSpecialSuffix(domain, suffixList)).toBe(false);
    });
});
