<template>
    <el-table :data="fetchData" v-loading="loading" :element-loading-text="$t('statistics.common.table.loading')"
        :empty-text="$t('common.table.empty')">
        <el-table-column :label="$t('statistics.rank.common.tableColumn1')" prop="rank" width="80" />
        <el-table-column label="Referer" prop="referer" min-width="120" />
        <el-table-column :label="$t('statistics.rank.common.tableColumn3')" prop="flow" width="120" />
        <el-table-column :label="$t('statistics.rank.common.tableColumn4')" prop="flowPer" width="180" />
        <el-table-column :label="$t('statistics.rank.common.tableColumn5') + getTimesUnit()" prop="request" width="180" />
        <el-table-column :label="$t('statistics.rank.common.tableColumn6')" prop="requestPer" width="180" />
    </el-table>
</template>

<script lang="ts">
import { Component } from "vue-property-decorator";
import { mixins } from "vue-class-component";
import { StatisticsRankUrl } from "@/config/url/statistics";
import { SearchParams, ListTopRefererFetchData, ListTopRefererItem } from "@/types/statistics/rank";
import ChartMixin from "../rankMixin";
import { convertFlowB2P } from "@/utils";

@Component({
    name: "HotReferer",
})
export default class HotReferer extends mixins(ChartMixin) {
    fetchData: ListTopRefererItem[] = [];
    protected downloadDataList: ListTopRefererItem[] = [];
    private totalRequest = "";
    private totalFlow = "";

    // 接口获取数据
    protected async getData(params: SearchParams) {
        const rst = await this.fetchGenerator<ListTopRefererFetchData>(StatisticsRankUrl.topRefererList, {
            ...params,
            top: 100,
        }) || {
            list: [],
            totalRequest: 0,
            totalFlow: 0,
        };
        rst.list = rst.list || [];

        this.downloadDataList = rst.list
            .map(item => ({
                ...item,
                flow: `${((+item.flow) / Math.pow(this.scale, 2)).toFixed(2)}${this.MB}`,
            }))
            .sort((a, b) => +a.rank - +b.rank);

        this.fetchData = rst.list
            .map(item => ({
                ...item,
                flow: convertFlowB2P(item.flow, this.scale).result,
            }))
            .sort((a, b) => +a.rank - +b.rank);

        this.totalRequest = rst.totalRequest;
        this.totalFlow = rst.totalFlow;
    }
    // 重写 excel 数据拼接方法
    protected tableToExcel() {
        let str = "";

        str = `${this.$t("statistics.rank.hotReferer.tableToExcel.excelColumn1", { flowUnit: this.MB })}\n`;

        this.downloadDataList.forEach(item => {
            str += `${item.referer},${item.flow},${item.flowPer},${item.request},${item.requestPer}\n`;
        });

        //增加峰值带宽和95峰值带宽
        str += `\n${this.$t("statistics.rank.common.tableToExcel.excelColumn2")}\n${this.$t(
            "statistics.rank.common.tableToExcel.excelColumn3",
            {
                num: this.totalRequest + this.$t("statistics.dcdn.backToOriginStatusCode.totalTipUnit"),
            }
        )}\n${this.$t("statistics.rank.common.tableToExcel.excelColumn4", {
            flow: `${((+this.totalFlow) / Math.pow(this.scale, 2)).toFixed(2)}${this.MB}`,
        })}\n`;

        //表格CSV格式和内容
        this.downloadExcel({
            name: `${this.$t("statistics.rank.hotReferer.tableToExcel.excelName")}`,
            str,
        });
    }
}
</script>

<style lang="scss" scoped></style>
