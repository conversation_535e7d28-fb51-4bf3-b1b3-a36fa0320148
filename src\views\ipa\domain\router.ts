import { RouteConfig } from "vue-router";
import { SecurityAbilityModule } from "@/store/modules/securityAbility";

const domainRouter: RouteConfig = {
    path: "/eas/domain",
    name: "domain",
    component: () => import("./index.vue"),
    redirect: {
        name: "domain.list",
    },
    meta: {
        breadcrumb: {
            title: "域名管理",
            route: ["domain"],
        },
    },
    beforeEnter(to, from, next) {
        SecurityAbilityModule.getDomainOrderCheck();
        next();
    },
    children: [
        {
            path: "list",
            name: "domain.list",
            component: () => import("./list/index.vue"),
            meta: {
                breadcrumb: {
                    title: "IP应用加速接入",
                    route: ["domain", "domain.list"],
                },
                keepAlive: true,
                perm: "eas.domainList",
            },
        },
        {
            path: "edit",
            name: "domain.edit",
            component: () => import("./edit/index.vue"),
            meta: {
                breadcrumb: {
                    title: "IP应用加速配置",
                    route: ["domain", "domain.list", "domain.edit"],
                },
                perm: "eas.domainEdit",
            },
            beforeEnter(to, from, next) {
                SecurityAbilityModule.SET_IS_IPA_SECURITY_CHANGE(false);
                next();
            }
        },
        {
            path: "create",
            name: "domain.create",
            component: () => import("./create/index.vue"),
            meta: {
                breadcrumb: {
                    title: "新增域名",
                    route: ["domain", "domain.list", "domain.create"],
                },
                perm: "eas.domainList",
            },
        },
        {
            path: "detail",
            name: "domain.detail",
            component: () => import("./detail/index.vue"),
            meta: {
                breadcrumb: {
                    title: "IP应用加速配置",
                    route: ["domain", "domain.list", "domain.detail"],
                },
                perm: "eas.domainList",
            },
        },
    ],
};

export default domainRouter;
