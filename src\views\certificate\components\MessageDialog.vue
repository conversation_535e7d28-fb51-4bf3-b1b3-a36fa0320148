<template>
    <el-dialog
        :title="$t('certificate.message.title')"
        :close-on-click-modal="false"
        :visible.sync="isConfirm"
        :before-close="cancel"
        append-to-body
        class="detail-dialog"
    >
        <div v-loading="detailLoading">
            <div v-html="$t('certificate.message.tip1', { certname: certificate.name })"></div>
            <div class="detail-list">
                <label style="vertical-align: top">{{ $t("certificate.message.label") }}：</label>
                <div style="display: inline-block">
                    <p v-for="item in domainData" :key="item.domain">
                        <span>{{ item.domain }}</span>
                    </p>
                </div>
                <span v-if="domainData.length === 0">无</span>
            </div>
        </div>
        <div slot="footer">
            <el-button class="dialog-btn" size="small" @click="cancel">
                {{ $t("common.dialog.cancel") }}
            </el-button>
            <el-button type="primary" class="dialog-btn" @click="confirm" size="small">
                {{ $t("common.dialog.submit") }}
            </el-button>
        </div>
    </el-dialog>
</template>

<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";

type domainParam = { domain: string };
@Component({})
export default class MessageDialog extends Vue {
    // 弹框显示标志位
    @Prop({ default: false, type: Boolean }) private isConfirm!: boolean;
    // 加载状态标志位
    @Prop({ default: false, type: Boolean }) private detailLoading!: boolean;
    // 证书详情
    @Prop({ default: {}, type: Object }) private certificate!: {};
    // 关联域名
    @Prop({
        default: () => {
            return [];
        },
        type: Array,
    })
    private domainData!: domainParam[];

    private confirm() {
        this.$emit("confirm");
    }
    private cancel() {
        this.$emit("cancel");
    }
}
</script>

<style lang="scss" scoped>
.detail-dialog {
    ::v-deep .el-dialog {
        @include g-width(90%, 50%, 30%);
    }
    ::v-deep a {
        color: #3d73f5;
    }

    .detail-list {
        margin-top: 10px;
        div {
            margin-bottom: 16px;
        }

        label {
            display: inline-block;
            width: 150px;
            text-align: left;
        }

        span {
            margin-left: 10px;
            color: #292b32;
        }
    }
}
</style>
