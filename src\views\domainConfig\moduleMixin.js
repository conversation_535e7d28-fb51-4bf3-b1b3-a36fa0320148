// 模块化mixin公共函数
import { SecurityAbilityModule } from "@/store/modules/securityAbility";

export default {
    // 放created好像有点问题，不知道key变化后的强刷机制是怎样的
    mounted() {
        SecurityAbilityModule.SET_CURRENT_MODULE(this);
    },
    beforeDestroy() {
        SecurityAbilityModule.SET_CURRENT_MODULE(null);

        // 组件销毁前，去除当前模块初始表单数据
        SecurityAbilityModule.SET_SECURITY_ORIGIN_FORM(null);
        SecurityAbilityModule.SET_SECURITY_CURRENT_FORM(null);
    },
};
