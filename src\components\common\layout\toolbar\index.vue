<template>
    <div class="xs-footer">
        <!-- 一级菜单+分类 -->
        <div class="tabs">
            <div
                v-for="(item, index) in menuList"
                :key="index"
                :class="['tab', activeIdentifier.replace(/-.*/, '') === '' + index ? 'active' : '']"
                :style="{ width: `${calcWidth}%` }"
                @click.stop="beforeGo(item, index)"
            >
                <div class="item-wrapper">
                    <div class="item">
                        <i :class="item.icon"></i>
                        <span>{{ item.name }}</span>
                    </div>
                </div>

                <!-- 二级菜单 start-->
                <section v-if="item.items" :style="{ width: `${calcWidth}%` }" class="nav-menu-wrapper">
                    <div :ref="`nav-menu-${index}`" class="nav-menu">
                        <div
                            v-for="(it, idx) in item.items"
                            :key="idx"
                            class="nav-menu-item"
                            @click.stop="beforeGo(it, index)"
                        >
                            {{ it.name }}
                        </div>
                    </div>
                </section>

                <!-- 二级菜单 end-->
            </div>
        </div>
    </div>
</template>
<script lang="ts">
import { Component, Mixins, Watch } from "vue-property-decorator";
import { Route } from "vue-router";
import { Dom } from "../../../../utils";
import MenuMixin from "../sidebar/menuMixin";
import { MenuListItem } from "../../../../types/store";

@Component({
    name: "toolbar-menu",
})
export default class ToolbarMenu extends Mixins(MenuMixin) {
    private get calcWidth() {
        // 最窄的按照5个的比例展示
        return Math.floor(100 / (this.menuList.length > 5 ? 5 : this.menuList.length));
    }

    @Watch("$route", { immediate: true })
    private onRouteChange(to: Route) {
        // 无需请求菜单的情况:  在拦截器路由中, 环境不匹配,匹配到了当前菜单
        const noInvoke = to.name === "interceptor" || !Dom.isMobileEnv();
        if (noInvoke) {
            return;
        }
        this.$ctBus.$emit("menulist:update");
    }

    private mounted() {
        const contentEle = Dom.getEle(".el-scrollbar__view > .wrapper > .right-content") as Element;
        // 点击右侧区域，二级菜单消失
        Dom.on(contentEle, "click", this.toggleActive);
    }

    // 跳转
    private beforeGo(menu: MenuListItem, index: number) {
        // 处理样式
        this.processNavMenuStyle(index);

        this.go(menu);
    }

    // 处理样式
    private processNavMenuStyle(index: number) {
        // 当前的curMenuName名字
        const curMenuName = `nav-menu-${index}`;
        // 去掉其他nav-menu的acitve态
        this.toggleActive(curMenuName);
    }

    private toggleActive(curMenuName: string) {
        Object.keys(this.$refs).forEach(key => {
            const refEle = (this.$refs[key] as HTMLElement[])[0];
            if (key === curMenuName) {
                Dom.toggleClass(refEle, "active");
                // 若处于active态，要计算父元素的left
                if (Dom.hasClass(refEle, "active")) {
                    // 获取父元素
                    const parentEle = refEle.parentNode as HTMLElement;
                    const tabEle = parentEle.parentNode as HTMLElement;
                    const { left } = tabEle.getBoundingClientRect();
                    // 更改父元素的left
                    parentEle.style.left = `${left}px`;
                }
                return;
            }
            Dom.removeClass(refEle, "active");
        });
    }
}
</script>
<style lang="scss" scoped>
$footer-height: 60px;
.xs-footer {
    display: none;
}
@include g-media-xs {
    .xs-footer {
        @include g-set-position($position: fixed, $left: 0, $bottom: 0);
        display: block;
        width: 100%;
        height: $footer-height;
        z-index: 999;
        background-color: $g-color-white;
        box-shadow: 0 0 10px 0 rgba(154, 141, 141, 0.6);
    }

    .tabs {
        display: inline-block;
        width: 100%;
        height: 100%;
        white-space: nowrap;
        overflow-x: auto;
        overflow-y: hidden;
    }
    .tab {
        // @include g-set-position($position: relative);
        position: static;
        display: inline-block;
        width: calc((100% - 50px) / 5);
        height: 100%;
        @include g-set-font($font-size: $g-fs-small);
        text-align: center;
        &.active {
            color: $g-color-yellow;
        }
        .item-wrapper {
            @include g-set-position($position: relative);
            width: 100%;
            height: 100%;
        }

        .item {
            @include g-set-position($position: absolute, $left: 50%, $top: 50%);
            width: 100%;
            transform: translate(-50%, -50%);
        }
        i {
            width: 100%;
        }
        span {
            display: block;
            width: 100%;
        }
    }
    // 1 祖先元素要出现滚动条，使用了overflow
    // 2 子孙元素要绝对定位，使得元素在祖先元素之外
    // 1不会让 2出去
    // 解决办法：
    // 1. 去掉相对于一级菜单的定位，把一级菜单的position改为static
    //      2. 对于要定位的二级菜单加一层包裹元素，绝对定位
    //      3. 真实要出去的元素，相对上面的包裹元素定位

    // https://css-tricks.com/popping-hidden-overflow/
    .nav-menu-wrapper {
        @include g-set-position($position: absolute, $bottom: 0);
        background-color: $g-color-white;
        height: 0;
    }
    .nav-menu {
        @include g-set-position($position: absolute, $left: 0, $bottom: $footer-height);
        width: 100%;
        color: $g-color-black;
        background-color: $g-color-white;
        box-shadow: 0 0 10px 0 rgba(154, 141, 141, 0.6);
        transform: translateY(calc(100% + 60px));
        opacity: 0;
        transition: transform 0.2s ease-in;
        &.active {
            transform: translateY(0);
            opacity: 1;
        }
        .nav-menu-item {
            @include g-set-position($position: relative);
            height: 50px;
            line-height: 50px;
            font-size: $g-fs-small;
            &.active {
                color: $g-color-yellow;
            }
            &:not(:last-child) {
                &::after {
                    content: "";
                    @include g-set-position($position: absolute, $left: 10%, $bottom: 0);
                    display: inline-block;
                    width: 80%;
                    height: 1px;
                    background-color: rgba(0, 0, 0, 0.3);
                }
            }
        }
    }
}
</style>
