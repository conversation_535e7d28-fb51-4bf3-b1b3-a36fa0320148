<template>
    <div :class="{ 'hsts-wrapper': !isFcdnCreate }">
        <el-form
            :label-width="labelWidth"
            :label-position="labelPosition"
            :model="form"
            :rules="rules"
            ref="hstsForm"
            :disabled="disabled"
        >
            <div v-if="requestProtocolIncludesHttps">
                <el-form-item label="HSTS" prop="switch" :label-width="hstsSwitchLabelWidth">
                    <el-switch v-model="form.switch" :active-value="1" :inactive-value="0" @change="switchChange"></el-switch>
                </el-form-item>

                <!-- <div class="hsts-bg" :style="{ 'margin-left': labelWidth, 'padding-left': isFcdnCreate ? '8px' : '0' }" v-show="form.switch === 1"> -->
                <div v-show="form.switch === 1" :class="{ 'switch-wrapper': !isFcdnCreate }">
                    <el-form-item :label="$t('domain.detail.label33')" prop="max_age" :rules="rules.max_age" key="max_age">
                        <el-input class="input-style" v-model.number="form.max_age" maxlength="16" @change="handleChange">
                            <template slot="append">{{
                                $t("simpleForm.alogicCacheMixin.CacheTtlMap.5")
                            }}</template></el-input
                        >
                    </el-form-item>

                    <el-form-item
                        :label="$t('domain.editPage.label13')"
                        prop="include_sub_domains"
                        class="sub-domain-wrapper"
                    >
                        <span slot="label"
                            >{{ $t("domain.editPage.label13") }}
                            <span>
                                <el-tooltip placement="top" :content="include_sub_domains_content">
                                    <ct-svg-icon
                                        icon-class="question-circle"
                                        class-name="question-circle"/></el-tooltip></span
                        ></span>
                        <el-select
                            v-model="form.include_sub_domains"
                            @change="handleChange"
                            :placeholder="$t('domain.editPage.placeholder13')"
                            clearable
                            class="input-style"
                        >
                            <el-option
                                v-for="times_item in include_sub_domains_list"
                                :key="times_item.value"
                                :label="times_item.label"
                                :value="times_item.value"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                </div>
            </div>
        </el-form>
    </div>
</template>

<script>
import ctSvgIcon from "@/components/ctSvgIcon";
import componentMixin from "@/views/domainConfig/componentMixin";
import { getLang } from '@/utils';
import { SecurityAbilityModule } from "@/store/modules/securityAbility";

export default {
    components: { ctSvgIcon },
    mixins: [componentMixin],
    props: {
        datas: Object,
        requestProtocolIncludesHttps: Boolean,
        isFcdnCreate: Boolean,
        isLockRequestProtocol: Boolean,
    },
    data() {
        return {
            form: {
                switch: 0,
                max_age: 1200,
                include_sub_domains: null,
            },
            include_sub_domains_list: [
                { label: this.$t("domain.editPage.label24"), value: "on" },
                { label: this.$t("domain.editPage.label25"), value: "off" },
            ],
            rules: {
                max_age: [
                    { required: true, validator: this.validMaxAge, trigger: "blur" },
                ],
            },
        };
    },
    computed: {
        include_sub_domains_content() {
            return this.$t("domain.editPage.tip4");
        },
        fcdnCreateShim() {
            return {
                labelWidth: getLang() === "en" ? "190px" : "140px",
                labelPosition: "right",
            }
        },
        labelPosition() {
            if (this.isFcdnCreate) return this.fcdnCreateShim.labelPosition;
            return "right";
        },
        labelWidth() {
            if (this.isFcdnCreate) return this.fcdnCreateShim.labelWidth;
            return "150px";
        },
        hstsSwitchLabelWidth() {
            if (this.isFcdnCreate) return this.fcdnCreateShim.labelWidth;
            return "140px";
        },
        disabled() {
            if (this.isFcdnCreate) return false;
            return !this.isEdit || !this.isService || this.isLockRequestProtocol;
        },
    },
    watch: {
        "datas.hsts": {
            deep: true,
            handler(val) {
                this.init(val);
            },
            immediate: true,
        },
    },
    methods: {
        init(v) {
            this.form.switch = v?.switch;
            this.form.max_age = v?.max_age;
            this.form.include_sub_domains = v?.include_sub_domains;
        },
        switchChange(val) {
            const originalConf = SecurityAbilityModule.securityBasicConfigOriginForm;

            if (this.isFcdnCreate) {
                this.form.max_age = 1200;
                this.form.include_sub_domains = null;
            } else {
                this.form.max_age = originalConf.hsts.max_age;
                this.form.include_sub_domains = originalConf.hsts.include_sub_domains;
            }

            this.handleChange();
        },
        handleChange() {
            this.$emit("onChange", this.form);
        },
        async validMaxAge(rule, value, callback) {
            const num = /^[0-9]*$/;
            if (!this.requestProtocolIncludesHttps) return callback();
            if (value === "" || value === null || value === undefined) {
                return callback(new Error(this.$t("domain.detail.placeholder29")));
            } else if (!num.test(value)) {
                return callback(new Error(this.$t("domain.detail.tip95")));
            } else if (value < 0 || value > 31536000) {
                return callback(new Error(this.$t("domain.detail.placeholder30")));
            } else {
                return callback();
            }
        },
        validateProcedure() {
            return new Promise(resolve => {
                this.$refs?.hstsForm?.validate((valid, validationInfo) => {
                    const { max_age } = validationInfo;
                    resolve({
                        valid,
                        msg: Array.isArray(max_age) && max_age.length ? max_age[0].message : "",
                        dom: "hsts"
                    });
                });
            })
        }
    },
};
</script>

<style lang="scss" scoped>
.hsts-wrapper {
    ::v-deep {
        .el-form--label-left .el-form-item__label {
            text-align: right;
        }
    }
}
.hsts-bg {
    display: flex;
    flex-direction: column;
    justify-content: center;
    width: 700px;
    height: 108px;
    background: $color-neutral-1;
    ::v-deep {
        .el-form-item.el-form-item--medium {
            margin-bottom: 0px;
        }
    }
}
.sub-domain-wrapper {
    margin-top: 20px;
}
.input-style {
    width: 380px;
}
.question-circle {
    font-size: $text-size-md;
    cursor: pointer;
}
</style>
