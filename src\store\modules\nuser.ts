// export * from "@cdnplus/common/store/modules/user";
import { VuexModule, Module, Action, Mutation, getModule } from "vuex-module-decorators";
import store from "../index";
import { ctFetch, getLang, setLang, switchLang } from "../../utils";
import { CurrentUrl, LoginUrl, nDomainUrl } from "../../config/url";
import i18n from "../../i18n";
import ctFetchI18n from "../../utils/ctFetch/i18n";
import { checkNewUser } from "@/config/url";

const origin = window.location.origin;

interface UserInfo {
    name: string;
    userId: string;
    email: string;
    saleChannel?: string;
    channel?: string;
    ctyunUserId?: string;
    ctyunAcctId?: string;
}
interface CurrentPromise {
    isLoggedIn: boolean;
    property: UserInfo;
}

export interface UserState {
    isVip: boolean;
    isCtclouds: boolean;
    isLoggedIn: boolean;
    userInfo: UserInfo;
    workspaceId: string;
    lang: string;
    isCtyun: boolean;
}

@Module({ dynamic: true, store, name: "user" })
class User extends VuexModule implements UserState {
    // public isVip = !process.env.PLATFORM?.includes("bs") && !domain.includes("ctyun.cn"); // 以域名为标准，控制全局展示
    public isVip = origin.includes("ctcdn"); // 以域名为标准，控制全局展示
    public isCtclouds = origin.includes("cloud"); // 以域名为标准，控制全局展示
    public isBcp = origin.includes("bcp"); // 以域名为标准，控制全局展示
    public isCtyun = origin.includes("ctyun"); // 以域名为标准，控制全局展示
    // fcdn 的国内站、国际站
    public isFcdnCtyunCtclouds =
        !window.__POWERED_BY_QIANKUN__ && (origin.includes("ctyun") || origin.includes("cloud"));
    // 是否为 eas, 该选项判断不准确，已弃用
    public isEas = location.hash.includes("/aocdnMicroApp/eas") || location.hash.includes("/overview/easOverview");
    public isLoggedIn = false;
    public userInfo: UserInfo = {
        // 预设一下空值
        name: "",
        userId: "",
        email: "",
        channel: "",
        saleChannel: "",
    };
    public isNewUser = true;

    public workspaceId = "";
    public lang = getLang();

    @Mutation
    public SET_LANG(lang: string) {
        lang = switchLang(lang);
        this.lang = lang;
        setLang(lang);
        i18n.locale = lang;
        ctFetchI18n.locale = lang;
        process.env?.PLATFORM !== "bs" && !window.__POWERED_BY_QIANKUN__ && (document.title = `CDN ${lang === "en" ? "Console" : "控制台"}`);
    }

    @Mutation
    public SET_USERINFO(userInfo: UserInfo) {
        this.userInfo = userInfo;
    }

    @Mutation
    public SET_LOGGEDIN(loggedIn: boolean) {
        this.isLoggedIn = loggedIn;

        if (loggedIn === false) {
            // 未登录
            window.location.href = LoginUrl;
        }
    }

    @Mutation
    public SET_WORKSPACEID(workspaceId: string) {
        if (workspaceId === this.workspaceId) return;

        this.workspaceId = workspaceId;

        ctFetch.config!({
            mergeDataStrategy: "merge",
            data: { workspaceId },
            wrapperDataWithPost: true, // 将 post 请求中的 workspaceId 移到 body.data 下
        });
    }

    @Mutation
    public SET_ISNEWUSER(isNewUser: boolean) {
        this.isNewUser = isNewUser;
    }

    @Action
    public async GetUserInfo(payload: { cache: boolean } = { cache: true }) {
        if (payload.cache && this.isLoggedIn) {
            return this.userInfo;
        }

        // 根据不同环境使用 layout 提供的登录数据，用直接请求兜底 1
        let fetchPromise: Promise<CurrentPromise>;
        if (window.AlogicLayout && window.AlogicLayout.authCurrentPromise) {
            fetchPromise = window.AlogicLayout.authCurrentPromise as Promise<CurrentPromise>;
        } else if (window.CtcloudLayout && window.CtcloudLayout.authCurrentPromise) {
            fetchPromise = window.CtcloudLayout.authCurrentPromise as Promise<CurrentPromise>;
        } else {
            // bs使用通用current即可
            fetchPromise = ctFetch(CurrentUrl);
        }

        const {
            isLoggedIn,
            property: userInfo,
        }: {
            isLoggedIn: boolean;
            property: UserInfo;
        } = await fetchPromise;

        this.SET_USERINFO(userInfo);
        this.SET_LOGGEDIN(isLoggedIn);

        // 普通用户，使用用户id作为工作区id
        if (isLoggedIn && !this.isVip) {
            this.SET_WORKSPACEID(userInfo.userId);
        }

        return userInfo;
    }

    @Action
    public async checkNewUser() {

        if (!this.isCtyun) {
            this.SET_ISNEWUSER(false);
            return;
        }

        const fetchPromise: Promise<boolean | {
            data: boolean
        }> = ctFetch(checkNewUser);

        const isNewUser = await fetchPromise;
        this.SET_ISNEWUSER(typeof isNewUser === "boolean" ? isNewUser : isNewUser.data);
    }

    public supportHttps = {
        https: false,
        link: "",
    }
    @Mutation
    public SET_SUPPORT_HTTPS(supportHttps: {
        https: boolean,
        link: string,
    }) {
        this.supportHttps = supportHttps;
    }
    @Action async checkSupportHttps() {
        const fetchPromise: Promise<{
            https: boolean,
            link: string,
        }> = ctFetch(nDomainUrl.checkSupportHttps);

        const res = await fetchPromise;
        this.SET_SUPPORT_HTTPS(res);
    }
}

export const nUserModule = getModule(User);
