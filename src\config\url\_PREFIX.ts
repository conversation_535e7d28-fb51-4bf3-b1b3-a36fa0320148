/*
 * @Description: 通用路径前缀和业务路径前缀，默认从 common 导出，可以根据实际情况调整
 * @Author: wang yuegong
 */

// 默认的是 `/${process.env.PKG_NAME}` 和 `/v1/${process.env.PKG_NAME}`
// 特定系统的虚拟路径
const MOCK_ROUTE_PREFIX = "https://yapi.ctcdn.cn/mock/737/aocdn";
const isBs = process.env.PLATFORM === "bs";
const ROUTE_PREFI = isBs ? "" : "/v1";

// 根据是否主应用进入，来判断前缀, 主应用进入前缀默认是 /aocdn, cdn进入前缀默认是 /fcdn
const ENV_API_PREFIX = window.__POWERED_BY_QIANKUN__ ? "aocdn" : "fcdn";
export const SYS_ROUTE_PREFIX = `${process.env.SYS_ROUTE || "/" + ENV_API_PREFIX}`;
// 业务的虚拟路径
export const LOGIC_ROUTE_PREFIX = `${process.env.LOGIC_ROUTE || "/v1/" + ENV_API_PREFIX}`;

// cdn 专用接口前缀
const NEW_PREFIX = `${process.env.LOGIC_ROUTE || "/" + ENV_API_PREFIX + "/v1"}`;
// aocdn 接口前缀
const PROXY_PREFIX = `/${ENV_API_PREFIX}`;
// ipa 接口前缀
const IPA_PREFIX = `/${ENV_API_PREFIX}/eas`;

export { MOCK_ROUTE_PREFIX, ROUTE_PREFI, PROXY_PREFIX, IPA_PREFIX, NEW_PREFIX };
