import { RouteConfig } from "vue-router";

const indexRouter: RouteConfig = {
    path: "/forbidden",
    name: "forbidden",
    meta: {
        ucode: "forbidden",
        breadcrumb: {
            title: '$t("common.forbidden.breadcrumbTitle")',
            route: ["home", "forbidden"],
        },
        withoutAuth: true,
    },
    component: () => import("./index.vue"),
};

export default indexRouter;
