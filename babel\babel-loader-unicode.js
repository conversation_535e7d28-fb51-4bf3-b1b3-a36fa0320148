/**
 * @Description: transform icon content to unicode ，当使用 dart-sass 时，会出现 el-icon 乱码的情况
 * @Author: wang y<PERSON>
 * @Date: 2021-01-15 11:08:53
 * @LastEditTime: 2021-01-15 12:37:10
 */

function convertCharStr2CSS(ch) {
    let code = ch.charCodeAt(0).toString(16);
    while (code.length < 4) {
        code = `0${code}`;
    }
    return `\\${code}`;
}

module.exports = source => {
    return source.replace(/content:\s*(?:'|")([\u0080-\uffff])(?:'|")/g, (str, $1) => {
        return `content: "${convertCharStr2CSS($1)}"`;
    });
};
