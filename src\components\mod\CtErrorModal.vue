<template>
    <el-dialog
        :title="$t('title')"
        :visible.sync="visible"
        :close-on-click-modal="false"
        :before-close="beforeClose"
        :custom-class="isEn ? 'aocdn-error-handler-wrapper-en' : 'aocdn-error-handler-wrapper'"
    >
        <h3 class="err-header">
            <i class="el-icon-warning" />
            <template v-if="currentErrItem.reasonUseHtml">
                <span v-html="decodeURIComponentSafely(currentErrItem.reason || reasonText)" />
            </template>
            <template v-else>
                {{ decodeURIComponentSafely(currentErrItem.reason || reasonText) }}
            </template>
        </h3>
        <div class="err-more">
            <div class="err-more-title" @click="toggleMore">
                {{ $t("detail") }}：<i class="el-icon-arrow-down" :class="showMore ? 'is-reverse' : ''" />
            </div>
            <ul v-show="showMore">
                <li class="err-more-msg">
                    <span class="err-msg-key">CODE</span>
                    <span class="err-msg-value">{{ currentErrItem.code || "" }}</span>
                </li>
                <li class="err-more-msg" v-if="currentErrItem.serial">
                    <span class="err-msg-key">SERIAL</span>
                    <span class="err-msg-value">{{ currentErrItem.serial }}</span>
                </li>
                <li class="err-more-msg" v-if="currentErrItem.url">
                    <span class="err-msg-key">URL</span>
                    <span class="err-msg-value">{{ currentErrItem.url }}</span>
                </li>
                <li class="err-more-msg" v-if="currentErrItem.params">
                    <span class="err-msg-key">PARAMS</span>
                    <span class="err-msg-value">{{ currentErrItem.params }}</span>
                </li>
                <li class="err-more-msg" v-if="currentErrItem.stack">
                    <span class="err-msg-key">STACK</span>
                    <span class="err-msg-value">{{ currentErrItem.stack }}</span>
                </li>
                <li class="err-more-msg" v-if="isFcdnCtyunCtclouds && currentErrItem.isCtiamCode">
                    <span class="err-msg-key">AUTH</span>
                    <span class="err-msg-value">
                        {{ currentErrItem.data.action }}<br />
                        {{ currentErrItem.data.des }}
                    </span>
                </li>
                <li class="err-more-msg" v-if="isFcdnCtyunCtclouds && currentErrItem.isCtiamCode && currentErrItem.data.domains">
                    <span class="err-msg-key">DOMAINS</span>
                    <span class="err-msg-value">
                        <span v-if="currentErrItem.data.domains.length > 50">
                            <span v-if="!showFullDomains">
                                {{ currentErrItem.data.domains.slice(0, 50) }}{{ showFullDomains ? "" : "..." }}
                            </span>

                            <a
                                v-if="!showFullDomains"
                                @click="showFullDomains = !showFullDomains"
                                class="pointer"
                            >
                                <i class="el-icon-arrow-down" :class="showFullDomains ? 'is-reverse' : ''" />
                            </a>
                            <span v-if="showFullDomains"
                                >{{ currentErrItem.data.domains }}
                                <a
                                    v-if="showFullDomains"
                                    @click="showFullDomains = !showFullDomains"
                                    class="pointer"
                                >
                                    <i
                                        class="el-icon-arrow-down"
                                        :class="showFullDomains ? 'is-reverse' : ''"
                                    />
                                </a>
                            </span>
                        </span>
                        <span v-else>
                            {{ currentErrItem.data.domains }}
                        </span>
                    </span>
                </li>
                <li class="err-more-msg" v-if="isFcdnCtyunCtclouds && currentErrItem.isCtiamCode">
                    <span class="err-msg-key">RESOURCE</span>
                    <span class="err-msg-value">
                        ctrn:ctcdn:*:account-id:resourcename <br />
                        <span class="resource-wrap">
                            {{ currentErrItem.data.resource }}
                        </span>
                    </span>
                </li>
            </ul>
        </div>
        <div class="err-pagination" v-if="errList.length > 1">
            <button type="button" class="btn-prev" :disabled="currentIdx === 0" @click="prev">
                <i class="el-icon-arrow-left" />
            </button>
            <span class="current-idx">{{ currentIdx + 1 }}</span> / {{ errList.length }}
            <button
                type="button"
                class="btn-next"
                :disabled="currentIdx === errList.length - 1"
                @click="next"
            >
                <i class="el-icon-arrow-right" />
            </button>
        </div>
        <div slot="footer" class="dialog-footer">
            <el-button v-if="currentErrItem.btnConfig" type="primary" @click="ensure">
                {{ renderBtnConfigTitle(currentErrItem.btnConfig) || $t("confirm") }}
            </el-button>
            <el-button :type="currentErrItem.btnConfig ? '' : 'primary'" @click="close">
                <!-- 一个按钮时，文案为确定，两个按钮时，文案为取消 -->
                {{ currentErrItem.btnConfig ? $t("cancle") : $t("confirm") }}
            </el-button>
        </div>
    </el-dialog>
</template>
<script lang="ts">
import { Vue, Component } from "vue-property-decorator";
import { ErrorMsg, ErrorModalBtn } from "@/utils/ctFetch/errorTypes";
import { nUserModule } from "@/store/modules/nuser";
import { getLang } from "@/utils";

@Component({})
export default class CtErrorModal extends Vue {
    visible = false; // 整个弹窗是否展示
    showMore = false; // 错误详情是否展示
    errList: ErrorMsg[] = [
        {
            code: "",
            reason: "",
        },
    ]; // 错误信息列表
    currentIdx = 0; // 当前展示的错误信息 idx
    showFullDomains = false; // 是否展示全部的domains

    // 当前展示的错误信息
    get currentErrItem() {
        return this.errList[this.currentIdx] || {};
    }
    get reasonText() {
        return this.$t("defaultReason");
    }
    get isFcdnCtyunCtclouds() {
        return nUserModule.isFcdnCtyunCtclouds;
    }
    get isEn() {
        return getLang() === "en";
    }

    open(errList: ErrorMsg[]) {
        if (errList.length === 0) return;

        this.errList = errList
            .filter(err => err.code)
            .map(err => {
                if (err.url) {
                    const [url, params] = err.url.split("?");

                    err.url = url;
                    err.params = params;
                }
                return err;
            });

        if (this.errList.length > 0) {
            this.visible = true;
        }
    }

    // 关闭时的处理
    beforeClose(done?: Function) {
        this.showFullDomains = false; // 默认不展示全部的domains
        this.showMore = false; // 默认不展示详情
        this.currentIdx = 0; // 默认恢复到第一条
        this.$emit("err.close"); // 向外广播关闭事件
        done && done();
    }

    close() {
        this.visible = false;
        this.beforeClose();
    }

    // 用户指定的确定按钮
    ensure() {
        const { href, cb } = this.currentErrItem.btnConfig as ErrorModalBtn;

        if (cb) {
            cb();
        } else if (href) {
            window.location.href = href;
        }

        // 触发完执行关闭操作
        this.close();
    }

    toggleMore() {
        this.showMore = !this.showMore;
    }

    prev() {
        this.currentIdx -= 1;
    }
    next() {
        this.currentIdx += 1;
    }

    decodeURIComponentSafely(content: string) {
        try {
            return decodeURIComponent(content);
        } catch (err) {
            return content;
        }
    }

    /**
     * 渲染按钮名称
     */
    renderBtnConfigTitle(config: { title: string }) {
        const title = config.title;
        if (!title) return "";

        return this.$t(title) ? this.$t(title) : title;
    }
}
</script>

<style lang="scss" scoped>
::v-deep {
    .aocdn-error-handler-wrapper {
        &.el-dialog {
            max-width: 560px;
        }
        .el-dialog__footer {
            text-align: right;
        }
    }
    .aocdn-error-handler-wrapper-en {
        &.el-dialog {
            max-width: 620px;
        }
        .el-dialog__footer {
            text-align: right;
        }
    }
}
.aocdn-error-handler-wrapper, .aocdn-error-handler-wrapper-en {
    .err-header {
        margin-bottom: 12px;
        font-size: 14px;
        // line-height: 1.5px;
        font-weight: bold;
        word-break: break-word;

        .el-icon-warning {
            margin-right: 4px;
            color: $color-warning;
            font-size: 14px;
            vertical-align: middle;
        }
    }

    .err-more {
        font-size: 12px;
    }

    // 更多信息的标题
    .err-more-title {
        margin-left: 5%;
        color: $color-info;
        cursor: pointer;

        &:hover {
            color: $color-info-hover;
        }
    }
    .err-more-msg {
        display: flex;
        margin-top: 8px;
        margin-left: 8%;
        padding: 4px 0;
        color: $color-info;
        border-bottom: 1px solid $border-color;

        .err-msg-key {
            width: 95px;
            color: $color-info;
        }

        .err-msg-value {
            text-align: right;
            flex: 1;
            overflow-wrap: anywhere;

            .resource-wrap {
                word-break: normal;
            }
        }
    }

    // 更多信息箭头
    .el-icon-arrow-down {
        transition: transform 0.3s;
        transform: rotate(0deg);

        &.is-reverse {
            transform: rotate(180deg);
        }
    }

    // 切换错误信息按钮
    .err-pagination {
        margin-top: 4px;
        margin-left: 5%;

        .current-idx {
            color: $color-master;
        }

        .btn-prev,
        .btn-next {
            border: none;
            padding: 6px;
            background-color: #fff;
            cursor: pointer;

            &:disabled {
                color: $color-info-disabled;
                cursor: not-allowed;
            }

            &:focus {
                outline: none;
            }
        }
    }
}
.pointer {
    cursor: pointer;
    color: #999;
}
</style>
