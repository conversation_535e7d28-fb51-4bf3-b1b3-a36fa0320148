<template>
    <el-dialog
        :title="$t('domain.list.note')"
        :append-to-body="true"
        :close-on-click-modal="false"
        :visible.sync="dialogVisible"
        :before-close="cancel"
        class="work-order-tip-dialog"
        width="500px"
        :show-close="true"
    >
        <el-alert title="" type="info" :closable="false" show-icon class="ct-alert">
            <template slot="title">
                <div>
                    {{ $t("domain.detail.placeholder33") }}
                </div>
            </template>
        </el-alert>
        <div slot="footer" class="btns">
            <el-button @click="cancel">{{ $t("domain.cancel") }}</el-button>
            <el-button type="primary" :loading="loading" @click="submit">{{ $t("domain.submit") }}</el-button>
        </div>
    </el-dialog>
</template>

<script lang="ts">
import { Component, Prop, Vue, Watch } from "vue-property-decorator";

@Component({})
export default class UpdateDialog extends Vue {
    private loading = false;
    @Prop({ default: false, type: Boolean }) private dialogVisible!: boolean;
    @Prop({ default: false, type: Boolean }) private existOrderLoading!: boolean;

    private async submit() {
        this.$emit("submit");
    }
    private cancel() {
        this.loading = false;
        this.$emit("cancel", "workOrderVisible");
    }
    @Watch("existOrderLoading", { immediate: true })
    onExistOrderLoadingChange(val: boolean) {
        this.loading = val;
    }
}
</script>

<style lang="scss" scoped>
.work-order-tip-dialog {
    .ct-alert {
        ::v-deep {
            .el-alert {
                align-items: center;
                padding: 12px;
            }
        }
    }
}
</style>
