<template>
    <div class="flow-wrap" v-loading="loading">
        <template v-if="hasProduct">
            <template v-if="canOperate">
                <div class="pro-wrap" v-for="(item, i) in filteredProductData" :key="i">
                    <div class="pro-label">{{ item.product_cname || "" }}</div>
                    <div class="pro-btn">
                        <el-radio-group v-model="item.billing_type">
                            <el-radio :label="1" :disabled="isDisabledFlow(item)">{{
                                $t("billing.billingMethod.type1")
                            }}</el-radio>
                            <el-radio :label="2" :disabled="isDisabledPeak(item)">{{
                                $t("billing.billingMethod.type2")
                            }}</el-radio>
                        </el-radio-group>
                        <el-button
                            size="small"
                            type="primary"
                            class="margin-left-20"
                            :disabled="isDisabledChangeProduct(item.product_code)"
                            @click="changeProduct(item)"
                        >
                            {{ $t("billing.btn.btn1") }}
                        </el-button>
                    </div>
                </div>
                <!-- <div class="change-tip">
                    <h3>计费变更须知：</h3>
                    1、流量：按照每日的实际流量计费；<br />
                    2、日带宽峰值：按照每日带宽每5分钟统计一个带宽峰值，每日得到288个值，取其中的最大值；<br />
                    3、计费方式由“日带宽峰值计费” → “流量计费”时，切换次日00:00生效；<br />
                    4、计费方式由“流量计费” →
                    “日带宽峰值计费”时，切换次日00:00生效，此时如已经订购流量包，则流量包余量冻结，冻结期间流量包有效期不会延长，恢复流量计费方式后，流量包再继续抵扣；<br />
                    5、变更生效前，允许多次进行变更操作，按照最终变更修改为准。<br />
                </div> -->
                <el-alert title="" :closable="false" show-icon content-area-tip style="margin-bottom: 16px">
                    <template slot="title">
                        <div class="change-tip">
                            <h3>{{ $t("billing.billingNote.title") }}</h3>
                            {{ $t("billing.billingNote.note1") }}<br />
                            <!-- {{ $t("billing.billingNote.note5") }}<br /> -->
                            {{ $t("billing.billingNote.note2") }}<br />
                            {{ $t("billing.billingNote.note3") }}<br />
                            {{ $t("billing.billingNote.note4") }}<br />
                        </div>
                    </template>
                </el-alert>
            </template>

            <el-table v-else :data="filteredProductData" :empty-text="$t('common.table.empty')">
                <el-table-column
                    :label="$t('billing.productTable.column1')"
                    prop="product_cname"
                    minWidth="100"
                />
                <el-table-column
                    :label="$t('billing.productTable.column2')"
                    prop="billing_type_cname"
                    minWidth="100"
                />
                <el-table-column :label="$t('billing.productTable.column3')" prop="eff_date" minWidth="100" />
                <el-table-column :label="$t('billing.productTable.column4')" prop="exp_date" minWidth="100" />
            </el-table>
        </template>

        <div class="nodata-tip" v-else>
            {{ $t("billing.noProductTip.part1")
            }}<span @click="toBuy" class="link-txt"> {{ $t("billing.noProductTip.part2") }} </span>
        </div>
    </div>
</template>

<script lang="ts">
import { nCdnBillingUrl, nQueryUserInfo } from "@/config/url";
import { BasicUrl } from "@/config/url/basic";
import { nUserModule } from "@/store/modules/nuser"; // 已合并
import { FlowProductData, UserChargingType } from "@/types/billing";
import { Component, Vue } from "vue-property-decorator";
import { UserChargingTypeEnum } from "@/config/map/billing";
const billingTypeOptions = [
    {
        label: "流量",
        value: 1,
    },
    {
        label: "日带宽峰值",
        value: 2,
    },
];

@Component({
    name: "PackageDetail",
})
export default class PackageDetail extends Vue {
    private billingTypeOptions = billingTypeOptions;
    private productData: FlowProductData[] = [];
    private hasProduct = true;
    private loading = true;
    private isOpenConfirm = false;
    private hideProducts = [
        "136", "137", "138", "139", "140", "189", "190", "200", "201", "202", "203",
        "229", "230", "231", "232", "233", "234", "236", "237", "238", "239", "240", "241", "242", "243", "244",
        "141", "142", "143", "144", "145", "146", "147", "148", "245",
        "005", "127",
        "204", "205", "206", "207", "208", "209", "210", "211", "212", "213", "214", "215", "216", "217"

    ];
    // 用户类型，默认后付费
    userType: UserChargingType = UserChargingTypeEnum.PostPay;

    get userInfo() {
        return nUserModule.userInfo;
    }
    get filteredProductData() {
        /**
         * 增值服务产品不展示
         *  136 CDN加速内容审核
         *  137下载加速内容审核
         *  138静态加速内容审核
         *  139视频点播加速内容审核
         *  140下载加速(闲时)内容审核
         *  CDN加速-虚拟专线-全球不含中国内地 product_code = 189
         *  全站加速-虚拟专线-全球不含中国内地 product_code = 190
         *
         * 因为静态请求数的单位为万次，不涉及流量和日带宽峰值，需做隐藏处理，即不需要显示，包括中国内地和全球不含中国内地加速区域。
         * 200 CDN加速-静态HTTPS请求数-中国内地
         * 201 CDN加速-静态HTTPS请求数-全球不含中国内地
         * 202 CDN加速-静态QUIC请求数-中国内地
         * 203 CDN加速-静态QUIC请求数-全球不含中国内地
         * ---0704需求变更，因底层不支持，以下逻辑先不上
         *  因 006 全站加速(中国内地)、104 全站加速-上传加速、105 全站加速-websocket加速 这三个服务的计费变更，要求保持三种计费方式一致，故隐藏以下类型。取出 resources_id 传给后端
         * 104 全站加速-上传加速
         * 105 全站加速-websocket加速产品
         */
        return this.productData.filter(item => !this.hideProducts.includes(item.product_code));
    }
    // 是否可以操作，目前条件官网且预付费的线上用户可操作，即 channel === 2 && saleChannel === 2 && userType === 1
    // 云公司+线上渠道+预付费，才可以操作
    // 另，这两个参数只出现在天翼云 current 接口中，iam current 接口中没有对应字段，那么 vip 的地址进入页面永远都是不可操作的，因为接口没对应字段
    get canOperate() {
        // 由于环境问题，无法直接用普通cdn测试，没办法登录，只能用vip-cdn创建一个工作区来进行测试
        if ("changeProduct" in this.$route.query) {
            return true;
        }
        return (
            this.userInfo &&
            this.userInfo.channel === "2" &&
            this.userInfo.saleChannel === "2" &&
            this.userType === UserChargingTypeEnum.PrePay
        );
    }
    get isCtclouds() {
        return nUserModule.isCtclouds;
    }
    get resourceId006() {
        // 变更 全站加速全球（不含中国内地） 时需要带上 全站加速（中国内地）的 resourceId 传给后端
        return this.productData.filter(item => ["006"].includes(item.product_code));
    }
    private toBuy() {
        const link = this.isCtclouds
            ? nUserModule.lang === "zh"
                ? "https://www.esurfingcloud.com/bac/product/ops?custId=0&siteId=ctyun&productId=10015926&operationId=1003017103&lang=zh-cn"
                : "https://www.esurfingcloud.com/bac/product/ops?custId=0&siteId=ctyun&productId=10015926&operationId=1003017103&lang=en-us"
            : "https://www.ctyun.cn/h5/bcc/product";
        window.open(link, "_blank");
    }
    private openConfirm(tip: string, url: string) {
        this.isOpenConfirm = true;
        this.$confirm(tip, this.$t("billing.confirmTip.title") as string, {
            confirmButtonText: this.$t("billing.confirmTip.btn1") as string,
            cancelButtonText: this.$t("billing.confirmTip.btn2") as string,
        })
            .then(() => {
                window.open(url, "_blank");
            })
            .catch(() => {
                this.isOpenConfirm = false;
            });
    }
    // 获取产品列表
    private async getFlowProduct() {
        this.loading = true;
        const data = await this.$ctFetch<{ list?: FlowProductData[] }>(BasicUrl.productList);
        const list = data.list || [];
        this.productData = list.filter((d: FlowProductData) => d.product_type === "bss_product");
        this.hasProduct = this.productData.length > 0;
        this.loading = false;
        if (!this.hasProduct && !this.isOpenConfirm) {
            const link = this.isCtclouds
                ? nUserModule.lang === "zh"
                    ? "https://www.esurfingcloud.com/bac/product/ops?custId=0&siteId=ctyun&productId=10015926&operationId=1003017103&lang=zh-cn"
                    : "https://www.esurfingcloud.com/bac/product/ops?custId=0&siteId=ctyun&productId=10015926&operationId=1003017103&lang=en-us"
                : "https://www.ctyun.cn/h5/bcc/product";
            this.openConfirm(this.$t("billing.confirmTip.tip1") as string, link);
        }
    }
    // 变更产品
    private async changeProduct(item: FlowProductData) {
        let resource_ids;
        if (item.product_code === "123") {
            resource_ids = `${item.resource_id},${this.resourceId006[0].resource_id}`;
        }
        await this.$confirm(
            this.$t("billing.changeProduct.tip") as string,
            this.$t("billing.common.itm3") as string,
            {
                confirmButtonText: this.$t("billing.common.itm1") as string,
                cancelButtonText: this.$t("billing.common.itm2") as string,
                dangerouslyUseHTMLString: true,
                type: "warning",
            }
        );

        await this.$ctFetch(nCdnBillingUrl.changeProduct, {
            data: {
                resourceId: item.product_code === "123" ? resource_ids : item.resource_id,
                resourceType: item.product_code,
                billingType: item.billing_type,
            },
        });

        this.$message.success(this.$t("billing.changeProduct.msg") as string);
    }
    // 获取用户类型
    async getUserType() {
        const result = await this.$ctFetch<{ chargingType: UserChargingType }>(nQueryUserInfo);

        this.userType = result.chargingType;
    }
    // 变更按钮禁用
    private isDisabledChangeProduct(v: string) {
        // CDN加速 全球不含中国内地 & 下载加速闲时 中国内地
        if (["120", "014"].includes(v)) return true;
        return false;
    }
    // CDN加速 全球不含中国内地 禁用日带宽峰值选项
    private isDisabledPeak({ product_code, billing_type }: FlowProductData) {
        if (product_code === "014") {
            // 闲时 如果选择的也不是日带宽峰值，那么就禁用
            return billing_type !== 2;
        }
        if (["120"].includes(product_code)) return true;
        return false;
    }
    private isDisabledFlow({ product_code }: FlowProductData) {
        if (["014"].includes(product_code)) return true;
        return false;
    }

    mounted() {
        this.getFlowProduct();
        this.getUserType();
    }
}
</script>

<style lang="scss" scoped>
.flow-wrap {
    min-height: 250px;
}
.pro-wrap {
    margin: 25px 0;
    .pro-label {
        display: inline-block;
        width: 280px;
        font-size: 14px;
        color: #666;
        line-height: 20px;
    }
    .pro-btn {
        display: inline-block;
    }
    .btn-width {
        width: 120px;
    }
}
.margin-left-20 {
    margin-left: 20px;
}
.change-tip {
    // color: #7c818c;
    font-size: 12px;
    line-height: 20px;

    h3 {
        font-size: 14px;
        color: #7c818c;
    }
}

.nodata-tip {
    font-size: 14px;
    color: #666;
    text-align: center;
    margin: 30px 0;
    .link-txt {
        color: #ff9832;
        cursor: pointer;
    }
}
</style>
