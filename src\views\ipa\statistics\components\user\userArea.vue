<template>
    <ct-box :tags="$t('statistics.user.tip1')" class="user-area">
        <template #tags-slot>
            <el-radio-group v-model="queryForm.currentType" size="small" @change="handleChangeType">
                <el-radio-button label="bandwidth">{{
                    $t("statistics.dcdn.bandwidthFlowWhole.radioBtn1")
                }}</el-radio-button>
                <el-radio-button label="flow">{{
                    $t("statistics.dcdn.bandwidthFlowWhole.radioBtn2")
                }}</el-radio-button>
                <el-radio-button label="connections">{{ $t("statistics.eas.tip28") }}</el-radio-button>
            </el-radio-group>
            <el-tooltip
                effect="dark"
                :content="$t('statistics.common.searchDownloadContent')"
                placement="right"
            >
                <i class="el-icon-download download-icon" @click="download"></i>
            </el-tooltip>
        </template>

        <v-chart
            class="user-section"
            v-loading="loading"
            :element-loading-text="$t('common.chart.loading')"
            autoresize
            theme="cdn"
            :options="options"
        />

        <div class="user-section">
            <el-table
                v-if="['connections', 'flow'].includes(queryForm.currentType)"
                ref="table"
                v-loading="loading"
                :data="showDataList"
                :element-loading-text="$t('statistics.common.table.loading')"
                border
                max-height="425px"
                key="table1"
            >
                <el-table-column :label="$t('statistics.user.tip2')" prop="provinceName" align="right" />
                <el-table-column :label="tableUnit" prop="currentData" />
                <el-table-column>
                    <template #header>
                        {{
                            queryForm.currentType === "flow" ? $t("statistics.rank.common.tableColumn4") : ""
                        }}
                        {{ queryForm.currentType === "connections" ? $t("statistics.eas.tip9") : "" }}
                    </template>
                    <template slot-scope="{ row }">
                        {{ queryForm.currentType === "flow" ? row.flow_per : row.connections_per }}
                    </template>
                </el-table-column>
            </el-table>
            <el-table
                v-else
                ref="table"
                v-loading="loading"
                :data="showDataList"
                :element-loading-text="$t('statistics.common.table.loading')"
                border
                max-height="425px"
                key="table2"
            >
                <el-table-column :label="$t('statistics.user.tip2')" prop="provinceName" align="right" />
                <el-table-column :label="tableUnit" prop="currentData" />
            </el-table>
            <div class="pager-wrapper">
                <div class="pager-number">
                    {{ $t("statistics.user.tip3", { num: pagerNumber }) }}/{{
                        $t("statistics.user.tip4", {
                            num: pagerTotal,
                        })
                    }}
                </div>
                <div class="pager-btn">
                    <el-button
                        size="mini"
                        plain
                        :disabled="pagerNumber < 2 || pagerTotal === 0"
                        icon="el-icon-caret-left"
                        @click="pagerDec"
                    >
                    </el-button>
                    <el-button
                        size="mini"
                        plain
                        :disabled="pagerNumber === pagerTotal || pagerTotal === 0"
                        icon="el-icon-caret-right"
                        @click="pagerAdd"
                    >
                    </el-button>
                </div>
            </div>
        </div>
    </ct-box>
</template>

<script>
import tableMixin from "../tableMixin";
import "@cdnplus/common/config/echart/china";
import { StatisticsUserUrl } from "@/config/url/ipa/statistics";
import { getIndexByField, getLang } from "@/utils";
import { conversionUnit } from "@/utils/utils";
import { get } from "lodash-es";
import chartShim from "../chartShim";
import provinceValue from "@/views/statistics/components/user/province";
import provinceEn from "@/i18n/en/province.json";
import provinceZh from "@/i18n/zh/province.json";

export default {
    mixins: [tableMixin, chartShim],
    props: {
        instType: {
            type: Number,
            default: 1,
        },
        instName: {
            type: String,
            default: "",
        },
    },

    data() {
        return {
            loading: false,
            fetchDataList: [], // 请求的列表数据
            pagerNumber: 0,
            pagerSize: 7,
            areaList: [],
            countryList: [],
            onLoadingAreaList: false,
        };
    },
    computed: {
        proMap() {
            const areaMap = new Map();
            this.areaList.map(item => {
                item.children.map(citem => {
                    areaMap.set(citem.value, citem.label);
                    if (!citem.children) return;
                    citem.children.map(sitem => {
                        areaMap.set(`${sitem.value}`, sitem.label);
                    });
                });
            });

            const countryMap = new Map();
            const lang = getLang();
            this.countryList.map(item => {
                countryMap.set(`${item.countryNum}`, lang === "zh" ? item.countryCn : item.countryEn);
            });
            return { areaMap, countryMap };
        },
        pagerTotal() {
            return Math.ceil(this.fetchDataList.length / this.pagerSize);
        },
        // 获取地图的计算最大值配置
        visualMapConfig() {
            // 计算最大值的数据
            const { fetchDataList } = this;
            let visualMapMax = 1000;
            const visualMapMin = 0;

            if (fetchDataList.length > 0) {
                // ***遍历：取所有数据中最大值，而非首个数据
                let temp = fetchDataList[0][this.queryForm.currentType];
                this.fetchDataList.map(item => {
                    if (temp < item[this.queryForm.currentType]) {
                        temp = item[this.queryForm.currentType];
                    }
                });
                if (this.queryForm.currentType !== "connections") {
                    visualMapMax = +temp;
                } else {
                    // visualMapMax = Number(fetchDataList[0][cureentType]);
                    visualMapMax = Number(temp);
                }
            }
            // 当没数据时，颜色一致
            if (visualMapMax === 0) visualMapMax = 1000;

            return {
                max: visualMapMax,
                min: visualMapMin,
            };
        },

        // 2、数据处理
        options() {
            const { fetchDataList, visualMapConfig, tabName } = this;

            // ***深拷贝：防止原始数据被修改
            const seriesData = JSON.parse(JSON.stringify(provinceValue));
            const type = this.queryForm.currentType === "flow" ? "byte" : "bps";

            //进行表格数据倒叙排序
            fetchDataList
                .sort((a, b) => +b[this.queryForm.currentType] - +a[this.queryForm.currentType])
                .forEach(item => {
                    //获取接口中的每一项对应地图初始话数据的索引值
                    const index = getIndexByField(seriesData, "name", item.provinceName);
                    //根据索引值将数据填充到地图上面
                    if (index !== -1) {
                        seriesData[index].value =
                            this.queryForm.currentType !== "connections"
                                ? +item[this.queryForm.currentType]
                                : +item[this.queryForm.currentType];
                    }
                });

            const options = {
                tooltip: {
                    formatter: params => {
                        // *数据处理
                        const name = get(params, "data.name");
                        const value = get(params, "data.value");
                        if (this.queryForm.currentType === "connections") {
                            return `${tabName}<br />${name}: ${value}`;
                        }

                        const res = conversionUnit(value, type, 4, false, false, this.scale);
                        return `${tabName}<br />${name}: ${res}`;
                    },
                },
                series: [
                    {
                        name: this.$t("statistics.dcdn.bandwidthFlowWhole.radioBtn1"),
                        type: "map",
                        geoIndex: 0,
                        data: seriesData,
                    },
                ],
                // 地图最大值
                visualMap: {
                    ...visualMapConfig,
                    left: "left",
                    top: "bottom",
                    text: [this.$t("statistics.user.tip5[0]"), this.$t("statistics.user.tip5[1]")],
                    inRange: {
                        color: ["#bad6ff", "#699bff"],
                    },
                    formatter: value => {
                        if (this.queryForm.currentType === "connections") {
                            return value?.toFixed(0);
                        }
                        return conversionUnit(value, type, 4, false, false, this.scale); // 范围标签显示内容。
                    },
                    show: true,
                },
                geo: {
                    map: "china",
                    roam: false,
                    zoom: 1.23,
                    label: {
                        normal: {
                            show: true,
                            fontSize: "10",
                            color: "#333",
                        },
                    },
                    itemStyle: {
                        normal: {
                            borderColor: "rgba(0, 0, 0, 0.2)",
                        },
                        emphasis: {
                            areaColor: "#3d73f5",
                            shadowOffsetX: 0,
                            shadowOffsetY: 0,
                            shadowBlur: 20,
                            borderWidth: 0,
                            shadowColor: "rgba(0, 0, 0, 0.1)",
                        },
                    },
                },
            };

            const isEn = getLang() === "en";
            if (isEn) {
                options.geo.nameMap = Object.keys(provinceEn).reduce((obj, key) => {
                    const mapKey = isEn ? provinceZh[key] : provinceEn[key];
                    const val = isEn ? provinceEn[key] : provinceZh[key];
                    obj[mapKey] = val;
                    return obj;
                }, {});
            }

            return options;
        },
        // 计算需要展示的数据
        showDataList() {
            const { pagerNumber, pagerSize } = this;
            return this.showDataListBase.slice((pagerNumber - 1) * pagerSize, pagerNumber * pagerSize);
        },
    },

    methods: {
        async getAreaList() {
            if (this.onLoadingAreaList) return;
            this.onLoadingAreaList = true;
            const [bst, cst] = await Promise.all([
                this.$ctFetch(StatisticsUserUrl.areaBaseList, {
                    method: "GET",
                    transferType: "json",
                    cache: true,
                }),
                this.$ctFetch(StatisticsUserUrl.countryBaseList, {
                    method: "GET",
                    transferType: "json",
                    cache: true,
                }),
            ])
                .catch(() => {
                    console.error("获取地区列表失败");
                })
                .finally(() => {
                    this.onLoadingAreaList = false;
                });

            this.areaList = bst?.result?.children || [];
            this.countryList = cst?.result || [];
        },
        //表格前端分页
        pagerAdd() {
            this.pagerNumber += 1;
        },
        pagerDec() {
            this.pagerNumber -= 1;
        },
        initData(reqParam) {
            this.getData(reqParam);
        },
        // 1、数据请求
        async getData(params) {
            this.loading = true;

            if (!this.areaList?.length || this.countryList?.length) {
                await this.getAreaList();
            }

            const rst = await this.$ctFetch(StatisticsUserUrl.areaDataList, {
                method: "POST",
                transferType: "json",
                body: { data: params },
            });
            // ***数据处理provinceName
            if (rst.result.length) {
                rst.result.forEach(item => {
                    item.provinceName = this.getAreaName(item.province.toString());
                });
            }
            this.fetchDataList = rst.result || [];
            // 有就切到 1 ，没有就切到 0
            this.pagerNumber = Number(!!get(rst, "result.length", 0));
        },
        getAreaName(code) {
            /**
             * 先使用/eas/v1/area/list返回的地区列表做第一次映射
             * 第一次映射没有匹配到值，使用后端提供的新接口/v1/basic/listCountry做第二次映射
             * 第二次映射若仍未匹配到值，则显示为“国内其他”
             */
            const area1 = this.proMap.areaMap.get(`${code}`);
            if (area1) return area1;

            const area2 = this.proMap.countryMap.get(`${code}`);
            if (area2) return area2;

            if (["1000277", "1"].includes(code)) return `${this.$t("statistics.eas.tip31")}`;

            if (["0"].includes(code)) return `${this.$t("statistics.eas.tip23")}`;

            return `${this.$t("statistics.dcdn.backToOriginStatusCode.vchartOptions.seriesItemName1")}`;
        },
        cellStyle(column) {
            if (column.columnIndex === 0) {
                return {
                    "background-color": "rgb(247, 248, 250) !important",
                };
            } else {
                return {
                    "background-color": "#fff !important",
                };
            }
        },
        download() {
            const { fetchDataList } = this;
            if (fetchDataList.length === 0)
                return this.$message(`${this.$t("statistics.common.chart.errMsg[2]")}`);

            const userTabMap = {
                bandwidth: this.$t("statistics.dcdn.bandwidthFlowWhole.radioBtn1"),
                flow: this.$t("statistics.dcdn.bandwidthFlowWhole.radioBtn2"),
                connections: this.$t("statistics.eas.tip28"),
            };

            let str = `${this.$t("statistics.provider.province")},${userTabMap[this.queryForm.currentType]}${
                this.unitTabMap[this.queryForm.currentType]
            },${
                this.queryForm.currentType === "flow"
                    ? `${this.$t("statistics.rank.common.tableColumn4")}\n`
                    : this.queryForm.currentType === "connections"
                    ? `${this.$t("statistics.eas.tip9")}\n`
                    : "\n"
            }`;
            fetchDataList.forEach(item => {
                const currentData =
                    this.queryForm.currentType !== "connections"
                        ? (item[this.queryForm.currentType] / Math.pow(this.scale, 2)).toFixed(2)
                        : item[this.queryForm.currentType];

                str += item["provinceName"] + ",";
                str += currentData + ",";
                str +=
                    this.queryForm.currentType === "flow"
                        ? item.flow_per
                        : this.queryForm.currentType === "connections"
                        ? item.connections_per
                        : "";
                str += "\n";
            });
            const instType = this.instType;
            this.downloadExcelForUser({
                name: this.$t("statistics.user.tip6"),
                str,
                instType,
            });
        },
        handleChangeType() {
            if (this.fetchDataList.length === 0) return;
            // 切换类型时分页置1
            this.pagerNumber = 1;
        },
    },
};
</script>

<style lang="scss" scoped>
.user-section {
    height: 350px;
}

.pager-wrapper {
    display: flex;
    flex-direction: row;

    .pager-number {
        color: #606266;
        padding: 10px;
        font-size: 13px;
    }

    .pager-btn {
        margin-top: 5px;

        .el-button + .el-button {
            margin: 0;
        }
    }
}
</style>
