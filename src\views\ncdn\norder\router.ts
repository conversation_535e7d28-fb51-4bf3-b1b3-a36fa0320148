import { RouteConfig } from "vue-router";
import { DomainModule } from "@/store/modules/domain"; // 已合并
import { DomainActionEnum, getDomainAction } from "@/config/map";
import { nUserModule } from "@/store/modules/nuser";

const orderRouter: RouteConfig = {
    path: "/norder",
    name: "norder",
    component: () => import("./index.vue"),
    redirect: {
        name: "norder.list",
    },
    children: [
        {
            path: "list",
            name: "norder.list",
            component: () => import("./list/index.vue"),
            beforeEnter(to, from, next) {
                // 不阻塞页面加载
                const isFcdnCtyunCtclouds = nUserModule.isFcdnCtyunCtclouds;
                isFcdnCtyunCtclouds
                    ? DomainModule.GetDomainList({ action: getDomainAction("DomainLog") })
                    : DomainModule.GetDomainList({ action: DomainActionEnum.Order });
                next();
            },
            meta: {
                breadcrumb: {
                    title: "$t('order.title')",
                    route: ["ndomain", "norder.list"],
                },
                perm: "norder.list",
            },
        },
    ],
};
export default orderRouter;
