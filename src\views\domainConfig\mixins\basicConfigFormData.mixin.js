import { CdnConfigModule } from "@/store/modules/cdnConfig";
import { conditionAssign } from "@/utils/utils";
import { cloneDeep } from "lodash-es";

export default {
    computed: {
        formData() {
            return window.__POWERED_BY_QIANKUN__ ? this.aocdnFormData : this.fcdnFormData;
        },
        /**
         * fcdn & aocdn 共用数据处理逻辑
         */
        commonFormData() {
            const returnData = {
                basic_conf: {},
                outlink_replace_filter: {},
                website_ipv6_access_mark: {},
                websocket_speed: 0,
                ssl: "",
                origin_host_http: {},
                origin_host_type: 0,
                ignore_backorigin_args: 0,
                backorigin_arg_rewrite_other: [],
                split_set: {},
                url_auth: {},
                limit_speed_const: [],
                entry_limits: [],
                xos_origin: {},
                xos_origin_is: 0,
                hsts: {
                    switch: 0,
                    max_age: 1200,
                    include_sub_domains: null,
                },
                special_port_change: false, // 特殊端口功能用
                white_referer: {},
                black_referer: {},
                ip_white_list: "",
                ip_black_list: "",
                user_agent: {
                    type: 1,
                    ua: "",
                    ignore_case: "on",
                    mode: 1,
                },
                uri_deny: {
                    type: 1,
                    uri: "",
                },
                html_forbid_op: [],
                html_forbid_op_condition: {},
                follow_request_backport: 0,
                backorigin_sni: {},
                version: '',
                ssl_ciphers: "", // 加密套件
                custom_ssl_ciphers: "",
            };
            const {
                origin = [],
                backorigin_protocol,
                proxy_gmssl_mode,
                basic_conf = {},
                req_host,
                internal_uri_rewrite_new = [],
                cert_name = "",
                cert_name_gm = "",
                force_jump = {},
                resp_headers = [],
                req_headers = [],
                ssl_stapling,
                ssl = [],
                cus_gzip = [],
                follow_302 = 0,
                follow302_is_valid = [],
                follow302_times = 1,
                backorigin_arg_rewrite_other = [],
                split_set = {},
                url_auth = {},
                xos_origin = {
                    use_ori_uri: "",
                },
                html_forbid_op = [],
                version,
                ssl_ciphers,
                custom_ssl_ciphers = [],
                quic,
            } = this.form;

            returnData.version = version;

            const { http_origin_port = 80, https_origin_port = 443, use_http2 = 0 } = basic_conf;

            returnData.signature = this.signature;
            returnData.product_code = this.form.product_code;
            // 回源配置
            returnData.origin = this.isLockOrigin || this.isLockXosOrigin || this.isLockZosOrigin ? null : cloneDeep(origin);
            returnData.backorigin_protocol = cloneDeep(backorigin_protocol);

            // 回源加密算法
            if (proxy_gmssl_mode && backorigin_protocol !== "http") {
                returnData.proxy_gmssl_mode = proxy_gmssl_mode;
            } else {
                returnData.proxy_gmssl_mode = "";
            }

            // 回源端口
            returnData.basic_conf.http_origin_port = cloneDeep(http_origin_port);
            returnData.basic_conf.https_origin_port = cloneDeep(https_origin_port);
            // 服务端口
            // 特殊端口功能用
            if (
                this.isServerPortChange
            ) {
                returnData.special_port_change = true;
            } else {
                returnData.special_port_change = false;
            }
            returnData.basic_conf.http_server_port = this?.form?.http_server_port;
            returnData.basic_conf.https_server_port = this?.form?.https_server_port;
            returnData.follow_request_backport = this?.form?.follow_request_backport || 0;

            // 分片回源
            if (!this.isLockSplitSet && !!this?.form?.split_white && !!this?.form?.split_set && this.isNewEcgw) {
                returnData.split_set = split_set.split_set;
                if (returnData.split_set !== "off") {
                    if (
                        this?.form?.split_set?.mode === "" ||
                        this?.form?.split_set?.mode === null ||
                        this?.form?.split_set?.mode === undefined ||
                        this?.form?.split_set?.content === "" ||
                        this?.form?.split_set?.content === null ||
                        this?.form?.split_set?.content === undefined
                    ) {
                        returnData.split_set_condition = {}
                    } else {
                        returnData.split_set_condition = {
                            split_set: [
                                {
                                    mode: this?.form?.split_set?.mode,
                                    content: this?.form?.split_set?.content,
                                }
                            ]
                        }
                    }
                }
            } else {
                delete returnData?.split_set;
                delete returnData?.split_set_condition;
            }

            // 回源sni
            returnData.backorigin_sni = cloneDeep(this?.form?.backorigin_sni);

            // 回源HOST
            returnData.req_host = req_host;
            // 回源URI改写
            if (!this.isLockOriginUrlRewrite) {
                returnData.internal_uri_rewrite_new = cloneDeep(internal_uri_rewrite_new);
            } else {
               delete returnData?.internal_uri_rewrite_new;
            }

            // 指定源站回源HOST：origin_host有值，origin_host_type传1，没值传0
            const origin_host_http = {};
            if (!this.isLockOrigin && !this.isLockXosOrigin && !this.isLockZosOrigin) {
                let count = 0;
                (origin || []).map(item => {
                    if (
                        item.origin_host === "" ||
                        item.origin_host === null ||
                        item.origin_host === undefined
                    ) {
                        this.$set(returnData, "origin_host_http", {});
                    } else {
                        count++;
                        origin_host_http[item?.origin] = item?.origin_host;
                    }
                });
                if (count > 0) {
                    returnData.origin_host_type = 1;
                } else {
                    returnData.origin_host_type = 0;
                }
                this.$set(returnData, "origin_host_http", origin_host_http);
            } else {
                delete returnData?.origin_host_http;
                delete returnData?.origin_host_type;
                delete returnData?.xos_origin_is;
            }
            // 忽略回源参数开关
            if (!this.isLockUrlParams) {
                returnData.ignore_backorigin_args = cloneDeep(this.form?.ignore_backorigin_args);
            } else {
                delete returnData?.ignore_backorigin_args;
            }
            // 回源参数规则
            if (!this.isLockParamsRewrite) {
                // 匹配编辑后的 回源参数改写 和页面初始化时读取到的 回源参数改写，如果 保持参数顺序 接口回传是关闭的话，且当前也是关闭的话，将是否对参数编码的值设置为接口回传的值
                this.temp_backorigin_arg_rewrite_other?.forEach(item => {
                    const exist = backorigin_arg_rewrite_other?.find(i => i.id === item.id);
                    if (!exist) return;
                    if (item.keep_args_order === "off" && exist.keep_args_order === "off") {
                        exist.need_encode_args = item.need_encode_args;
                    }
                })
                returnData.backorigin_arg_rewrite_other = cloneDeep(backorigin_arg_rewrite_other);
                // argsList 转成 args
                returnData.backorigin_arg_rewrite_other?.forEach(item => {
                    const args = item.argsList?.reduce((result, item) => {
                        result[item.name] = item.value;
                        return result;
                    }, {});
                    item.args = args;
                    delete item.argsList;
                });
            } else {
                delete returnData?.backorigin_arg_rewrite_other;
            }

            // 回源超时时间设置
            if (!this.isLockBackOriginTime) {
                returnData.backup_origin_timeout = this.form.backorigin_switch
                    ? this.form.backup_origin_timeout
                    : ""; // 开关关闭时，传“”
                returnData.backup_origin_resptimeout = this.form.backorigin_switch
                    ? this.form.backup_origin_resptimeout
                    : ""; // 开关关闭时，传“”
            } else {
                delete returnData?.backup_origin_timeout;
                delete returnData?.backup_origin_resptimeout;
            }


            // xos源站
            if (!this.isLockXosOrigin) {
                returnData.xos_origin = cloneDeep(xos_origin);
                (origin || []).map(item => {
                    if (item.is_xos === 1) {
                        returnData.xos_origin.origin = item.origin;
                    }
                });
                if (origin?.some(item => item.is_xos === 1) && !this.isLockOrigin) {
                    returnData.xos_origin_is = 1;
                }
                if (this.temp_bucket) {
                    returnData.xos_origin.bucket = this.temp_bucket;
                }

                // 私有Bucket回源
                if (this.form.bucket_status) {
                    returnData.xos_origin.ak = this.form.ak;
                    returnData.xos_origin.sk = this.form.sk;
                } else {
                    returnData.xos_origin.ak = "";
                    returnData.xos_origin.sk = "";
                }
                if (this.form.use_ori_uri === "" || this.form.use_ori_uri === null || this.form.use_ori_uri) {
                    returnData.xos_origin.use_ori_uri = this.form.use_ori_uri;
                } else {
                    delete returnData?.xos_origin?.use_ori_uri;
                }

                // 如果是ct备份源站，不需要传xos_origin
                if (CdnConfigModule.isCtBackupSourceSelected) {
                    delete returnData?.xos_origin.ak;
                    delete returnData?.xos_origin.sk;
                    delete returnData?.xos_origin.bucket;
                    returnData.xos_origin.ct_bucket = true;
                }
            } else {
                delete returnData?.xos_origin;
                delete returnData?.xos_origin_is;
            }

            if (!this.isLockZosOrigin) {
                // zos源站
                const isZosOrigin = this.form.origin?.find(itm => itm.is_xos === 2);

                if (isZosOrigin) {
                    returnData.zos_origin = {
                        private_bucket: this.form.bucket_status ? 1 : 0,
                        switch: 1,
                        origin: this.form.origin?.find(itm => itm.is_xos === 2).origin
                    };

                    if (returnData.zos_origin.private_bucket) {
                        returnData.zos_origin.ak = this.form.ak;
                        returnData.zos_origin.sk = this.form.sk;
                    } else {
                        returnData.zos_origin.ak = "";
                        returnData.zos_origin.sk = "";
                    }
                } else {
                    returnData.zos_origin = { switch: 0 };
                }
            } else {
                delete returnData?.zos_origin;
            }

            // 请求协议
            if (this.requestProtocolIncludesHttps) {
                returnData.https_status = "on";
                returnData.ssl_stapling = ssl_stapling;
                if (ssl && ssl.length > 0) {
                    returnData.ssl = ssl.join(",");
                } else {
                    returnData.ssl = "";
                }

                // HSTS
                returnData.hsts.switch = this.form?.hsts?.switch;
                if (this.form?.hsts?.switch === 1) {
                    returnData.hsts.max_age = this.form?.hsts?.max_age;
                    if (["on", "off"].includes(this.form?.hsts?.include_sub_domains)) {
                        returnData.hsts.include_sub_domains = this.form?.hsts?.include_sub_domains;
                    } else {
                        returnData.hsts.include_sub_domains = null;
                    }
                } else {
                    delete returnData?.hsts?.max_age;
                    delete returnData?.hsts?.include_sub_domains;
                }
                // 加密套件
                returnData.ssl_ciphers = ssl_ciphers;
                returnData.custom_ssl_ciphers = custom_ssl_ciphers && custom_ssl_ciphers?.join(":");

                // 是否开启quic
                if (this.getQuicParams.quicEnable) {
                    returnData.quic = quic?.switch ? quic : { switch: 0 };
                    if (this.getQuicParams.isNewQuic) returnData.newQuic = true;
                }
            } else {
                returnData.https_status = "off";
                returnData.ssl = "";

                if (this.getQuicParams.quicEnable) {
                    returnData.quic = { switch: 0 };
                }

                delete returnData?.hsts;
                delete returnData?.ssl_ciphers;
                delete returnData?.custom_ssl_ciphers;
            }
            if (this.requestProtocolIncludesHttp) {
                returnData.http_status = "on";
            } else {
                returnData.http_status = "off";
            }

            // 强制跳转
            returnData.force_jump = force_jump;

            // HTTPS配置
            if (!this.isLockRequestProtocol) {
                returnData.cert_name = cert_name;
                returnData.cert_name_gm = cert_name_gm;
                returnData.basic_conf.use_http2 = cloneDeep(use_http2);

                if (!returnData.cert_name && !returnData.cert_name_gm) {
                    returnData.cert_name = "";
                    delete returnData?.basic_conf?.use_http2;
                    delete returnData?.ssl_stapling;
                    delete returnData?.ssl;
                }
            } else {
                delete returnData?.https_status
                delete returnData?.basic_conf.https_server_port
                delete returnData?.ssl_stapling
                delete returnData?.ssl
                delete returnData?.hsts
            }

            if (this.isLockSslCiphers) {
                delete returnData?.ssl_ciphers;
                delete returnData?.custom_ssl_ciphers;
                delete returnData?.cert_name;
                delete returnData?.cert_name_gm;
            }

            // 头部修改
            const temp_resp_headers = cloneDeep(resp_headers)
            temp_resp_headers?.map(item => {
                if (item?.disabled) {
                    return delete item?.disabled
                }
            })
            if (!this.isLockRespHeader) returnData.resp_headers = cloneDeep(temp_resp_headers);
            if (!this.isLockReqHeader) returnData.req_headers = cloneDeep(req_headers);

            // 域名
            // returnData.domain = SecurityAbilityModule.securityDomain // 换成detail返回的domain数据
            returnData.domain = this.form.domain;

            // 文件压缩
            if (!this.isLockGzipConf) {
                returnData.cus_gzip = cloneDeep(cus_gzip);
            } else {
                delete returnData?.cus_gzip;
            }
            // URL鉴权
            if (!this.isLockUrlAuth) {
                returnData.url_auth = cloneDeep(url_auth);
            } else {
                delete returnData?.url_auth;
            }

            // 回源302/301跟随
            returnData.basic_conf.follow_302 = cloneDeep(follow_302);
            if (follow_302 === 1) {
                if (follow302_is_valid.includes(1)) {
                    returnData.follow302_is_valid = 1;
                } else {
                    returnData.follow302_is_valid = 0;
                }
                returnData.follow302_times = follow302_times ? follow302_times * 1 : null;
            } else if (follow_302 === 0) {
                returnData.follow302_is_valid = 0;
                returnData.follow302_times = null;
            }

            // websocket
            // 乾坤入口 或者 域名类型等于全站加速-websocket时("105": "全站加速-websocket加速")， 才需要传参给后端接口
            if (this.isShowWebsocket) {
                if (!this.isLockWebSocket) {
                    returnData.websocket_speed = this.form.websocket_speed;
                    if (returnData.websocket_speed) {
                        returnData.websocket_time = {
                            backup_origin_timeout: this.form.websocket_time.backup_origin_timeout || "",
                            backup_origin_resptimeout: this.form.websocket_time.backup_origin_resptimeout,
                            backup_origin_sendtimeout: this.form.websocket_time.backup_origin_sendtimeout,
                        };
                    }
                } else {
                    delete returnData?.websocket_speed;
                    delete returnData?.websocket_time?.backup_origin_timeout;
                    delete returnData?.websocket_time?.backup_origin_resptimeout;
                    delete returnData?.websocket_time?.backup_origin_sendtimeout;
                }
            }

            // html禁止操作
            if (!this.isLockHtmlForbid) {
                if (html_forbid_op && html_forbid_op.length > 0) {
                    returnData.html_forbid_op = html_forbid_op
                } else {
                    returnData.html_forbid_op = []
                }
                let html_forbid_op_condition = {};
                if (html_forbid_op && html_forbid_op.length > 0) {
                    html_forbid_op.map(item => {
                        if (item?.mode === "" ||
                            item?.mode === null ||
                            item?.mode === undefined ||
                            item?.content === "" ||
                            item?.content === null ||
                            item?.content === undefined
                        ) {
                            html_forbid_op_condition = {};
                        } else {
                            html_forbid_op_condition[item?.id] = [
                                {
                                    mode: item?.mode,
                                    content: item?.content,
                                }
                            ]
                        }
                    })
                } else {
                    html_forbid_op_condition = {};
                }
                returnData.html_forbid_op_condition = html_forbid_op_condition;

            } else {
                delete returnData?.html_forbid_op;
                delete returnData?.html_forbid_op_condition;
            }

            return returnData;
        },
        /**
         * fcdn 独有数据处理逻辑
         */
        fcdnFormData() {
            const returnData = cloneDeep(this.commonFormData);

            // IPv6外链改造
            delete returnData?.outlink_replace_filter;
            delete returnData?.website_ipv6_access_mark;

            // 仅对 fcdn 静态、全站 开放的配置
            if (this.showForStaticAndIcdn) {
                // 访问控制
                // Referer防盗链
                if (this.form.referer === "on") {
                    if (this.form.refererType === "allow") {
                        returnData.white_referer.allow_empty = this.form?.allow_empty;
                        // 只有新框架，才需要传 referer_empty_protocol 给后端
                        if (this.temp_domain_detail.use_ecgw === 1) {
                            returnData.white_referer.referer_empty_protocol = this.form?.referer_empty_protocol;
                            returnData.white_referer.match_all_ports = this.form?.match_all_ports;
                            returnData.white_referer.ignore_case = this.form?.ignore_case;
                            returnData.white_referer.is_append = this.form?.is_append;
                        }
                        if (this.domainList && this.domainList.length > 0) {
                            returnData.white_referer.allow_list = this.domainList;
                        } else {
                            returnData.white_referer.allow_list = [];
                        }
                        delete returnData?.black_referer;
                    }
                    if (this.form.refererType === "block") {
                        returnData.black_referer.allow_empty = this.form?.allow_empty;
                        // 只有新框架，才需要传 referer_empty_protocol 给后端
                        if (this.temp_domain_detail.use_ecgw === 1) {
                            returnData.black_referer.referer_empty_protocol = this.form?.referer_empty_protocol;
                            returnData.black_referer.match_all_ports = this.form?.match_all_ports;
                            returnData.black_referer.ignore_case = this.form?.ignore_case;
                            returnData.black_referer.is_append = this.form?.is_append;
                        }
                        if (this.domainList && this.domainList.length > 0) {
                            returnData.black_referer.allow_list = this.domainList;
                        } else {
                            returnData.black_referer.allow_list = [];
                        }
                        delete returnData?.white_referer;
                    }
                }

                if (this.isLockReferer) {
                    delete returnData.white_referer;
                    delete returnData.black_referer;
                }

                // ipset黑白名单
                if (this.form.ip_set_forbid?.switch !== null) {
                    returnData.ip_set_forbid = { ...this.form.ip_set_forbid };
                }

                // IP黑白名单
                if (this.form.ip_switch === "on") {
                    if (this.form.ipType === "allow") {
                        if (
                            this.form.ip !== "" &&
                            this.form.ip !== null &&
                            this.form.ip !== undefined
                        ) {
                            returnData.ip_white_list = this.form.ip
                                ?.split("\n")
                                ?.map(i => i.trim())
                                ?.join(",");
                            delete returnData?.ip_black_list;
                        }
                    }
                    if (this.form.ipType === "block") {
                        if (
                            this.form.ip !== "" &&
                            this.form.ip !== null &&
                            this.form.ip !== undefined
                        ) {
                            returnData.ip_black_list = this.form.ip
                                ?.split("\n")
                                ?.map(i => i.trim())
                                ?.join(",");
                            delete returnData?.ip_white_list;
                        }
                    }
                }

                // ua黑白名单 新框架展示
                if (!this.isLockUa) {
                    if (this.temp_domain_detail.use_ecgw === 1 && this.form.ua_switch === "on") {
                        returnData.user_agent.type = this.form?.user_agent?.type;
                        returnData.user_agent.ignore_case = this.form?.user_agent?.ignore_case;
                        returnData.user_agent.mode = this.form?.user_agent?.mode;
                        if (this.uaList && this.uaList.length > 0) {
                            returnData.user_agent.ua = this.uaList;
                        } else {
                            returnData.user_agent.ua = [];
                        }
                    } else {
                        returnData.user_agent = {};
                    }
                } else {
                    delete returnData?.user_agent;
                }
                // url黑白名单 新框架展示
                if (this.temp_domain_detail.use_ecgw === 1 && this.form.uri_switch === "on") {
                    returnData.uri_deny.type = this.form?.uri_deny?.type;
                    if (this.uriList && this.uriList.length > 0) {
                        returnData.uri_deny.uri = this.uriList;
                    } else {
                        returnData.uri_deny.uri = [];
                    }
                } else {
                    returnData.uri_deny = {};
                }


                // 单请求限速
                if (!this.isLockLimitSpeed) {
                    const { limit_speed_const } = this.form;
                    if (limit_speed_const?.length > 0) {
                        returnData.limit_speed_const = cloneDeep(limit_speed_const);
                    } else {
                        returnData.limit_speed_const = [];
                    }

                    returnData.limit_speed_const_condition = conditionAssign(returnData.limit_speed_const);
                } else {
                    delete returnData?.limit_speed_const;
                    delete returnData?.limit_speed_const_condition;
                }

                // 访问限频
                if (!this.isLockEntryLimit) {
                    const { entry_limits } = this.form;
                    if (entry_limits?.length > 0) {
                        returnData.entry_limits = cloneDeep(entry_limits);
                    } else {
                        returnData.entry_limits = [];
                    }

                    returnData.entry_limits_condition = conditionAssign(returnData.entry_limits);
                } else {
                    delete returnData?.entry_limits;
                    delete returnData?.entry_limits_condition;
                }
            } else {
                // 访问控制
                delete returnData?.white_referer;
                delete returnData?.black_referer;
                delete returnData?.ip_set_forbid;
                delete returnData?.ip_white_list;
                delete returnData?.ip_black_list;
                delete returnData?.user_agent;
                delete returnData?.uri_deny;
                delete returnData?.limit_speed_const;
                delete returnData?.entry_limits;
            }

            // CDN控制台旧框架的情况，根据CDN控制台逻辑，不需要传参给后端接口
            if (this.temp_domain_detail.use_ecgw === 0) {
                // 回源超时时间
                delete returnData?.backup_origin_timeout;
                delete returnData?.backup_origin_resptimeout;
                // 回源302/301跟随
                delete returnData?.follow302_is_valid;
                delete returnData?.follow302_times;
                delete returnData?.basic_conf?.follow_302;
                // https -> ssl
                delete returnData?.ssl;
                // https -> hsts
                delete returnData?.hsts;
                // 忽略回源参数开关
                delete returnData?.ignore_backorigin_args;
                // 回源参数规则
                delete returnData?.backorigin_arg_rewrite_other;
                // url 鉴权
                delete returnData?.url_auth;
                // 页面优化
                delete returnData?.html_forbid_op;
                delete returnData?.html_forbid_op_condition;
                // 访问控制 ua
                delete returnData?.user_agent;
                // 访问控制 url黑白名单
                delete returnData?.uri_deny;
                // 访问控制 单请求限速
                delete returnData?.limit_speed_const;
                // 文件处理 -> 文件压缩
                delete returnData?.cus_gzip;
                // 回源 sni
                delete returnData?.backorigin_sni;
                // 分片回源
                delete returnData?.split_set;
                delete returnData?.split_set_condition;
            }

            return returnData;
        },
        /**
         * aocdn独有数据处理逻辑
         */
        aocdnFormData() {
            const returnData = cloneDeep(this.commonFormData);

            // IPv6外链改造
            returnData.outlink_replace_filter = cloneDeep(this.form.outlink_replace_filter);
            if (this.form.outlink_replace_filter.switch === 1) {
                returnData.outlink_replace_filter.switch = 1;
                returnData.outlink_replace_filter.times = this.form.outlink_replace_filter.times;
            } else {
                returnData.outlink_replace_filter.switch = 0;
                delete returnData.outlink_replace_filter.times;
            }
            if (this.form?.outlink_replace_filter?.blacklist) {
                returnData.outlink_replace_filter.blacklist = cloneDeep(
                    this?.form.outlink_replace_filter?.blacklist?.split(",")
                );
            } else {
                returnData.outlink_replace_filter.blacklist = [];
            }
            // 网站首页IPv6标识
            returnData.website_ipv6_access_mark = cloneDeep(this.form.website_ipv6_access_mark);
            if (this.form.website_ipv6_access_mark.switch === 1) {
                returnData.website_ipv6_access_mark.switch = 1;
                returnData.website_ipv6_access_mark.show_content = this.form.website_ipv6_access_mark.show_content;
                returnData.website_ipv6_access_mark.show_times = this.form.website_ipv6_access_mark.show_times;
            } else {
                returnData.website_ipv6_access_mark.switch = 0;
            }

            // JA3指纹配置
            if (this.form.ssl_ja3_enable) {
                returnData.ssl_ja3_enable = this.form.ssl_ja3_enable;
            }

            // 如果是乾坤入口，不需要传这几个参数
            delete returnData?.black_referer;
            delete returnData?.white_referer;

            delete returnData?.ip_white_list;
            delete returnData?.ip_black_list;

            delete returnData?.user_agent;
            delete returnData?.uri_deny;
            delete returnData?.limit_speed_const;
            delete returnData?.entry_limits;
            return returnData;
        }
    }
}
