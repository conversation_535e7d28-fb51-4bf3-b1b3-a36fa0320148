<template>
    <span ref="feedbackRef"></span>
</template>

<script setup>
import { nUserModule } from "@/store/modules/nuser";
import { getLang } from "@/utils";
import { ref, onMounted, onUnmounted, getCurrentInstance, computed } from "vue";

const feedbackRef = ref("");
const instance = getCurrentInstance();
const { proxy } = instance || {};

const onError = err => {
    proxy.$message({
        type: "error",
        message: err.msg,
    });
};
const onSuccess = data => {
    proxy.$message({
        type: "success",
        message: data.msg,
    });
};

const isFeedbackEnabled = computed(() => {
    return !window.__POWERED_BY_QIANKUN__ && nUserModule.isCtyun && getLang() === "zh";
});

onMounted(async () => {
    if (!isFeedbackEnabled.value) {
        return;
    }

    window.CtcloudLayoutV2.Feedback.init({
        baseNode: feedbackRef.value,
        scope: "console2",
        label: "CDN加速的控制台管理页面",
        urlType: "18",
        bookId: "10015932",
        productName: "CDN加速",
        onError,
        onSuccess,
    });
});

onUnmounted(() => {
    if (isFeedbackEnabled.value && window.CtcloudLayoutV2.Feedback) {
        window.CtcloudLayoutV2.Feedback.close();
    }
});
</script>

<style lang="scss" scoped></style>
