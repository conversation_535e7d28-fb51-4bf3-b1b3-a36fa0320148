<template>
    <div class="container">
        <el-radio-group class="radio-group" v-model="currentPeriodLocal" :size="size">
            <el-radio-button
                v-for="period in periodOptions"
                :key="period"
                :label="period"
                v-show="period !== '-1' || currentPeriodLocal !== '-1'"
            >
                <!-- 自定义的 radio 表现比较特殊 -->
                <span :class="{ 'period-wrap': period === '-1' }">
                    <i class="el-icon-date" v-if="period === '-1'" />
                    {{
                        periodLabelMap[period]
                            ? periodLabelMap[period]
                            : $t("common.ctTimePicker.label3", { number: period })
                    }}
                </span>
            </el-radio-button>
        </el-radio-group>
        <el-date-picker
            v-if="currentPeriodLocal === '-1'"
            :size="size"
            v-model="timeRangeLocal"
            :type="type"
            ref="datePicker"
            :default-time="['00:00:00', '23:59:59']"
            :start-placeholder="$t('common.datePicker.start')"
            :end-placeholder="$t('common.datePicker.end')"
            :picker-options="pickerOptions"
            :popper-class="isHourly ? 'aocdn-ignore-time-picker-hour' : ''"
            :format="'yyyy-MM-dd HH:mm:ss'"
            @focus="handlePickerFocus"
            @blur="handlePickerBlur"
        />
    </div>
</template>

<script lang="ts">
import { Component, Prop, Watch, Vue, Ref } from "vue-property-decorator";

import { DatePickerOptions, ElDatePicker } from "element-ui/types/date-picker";

import { getAm0 as get0 } from "../../utils";
import dayjs from "dayjs";
import { inRange } from "lodash-es";
import { cloneDeep } from "lodash-es";

interface MyDatePickerOptions extends DatePickerOptions {
    reset(): void;
}

/**
 * 2020-11-27
 * 需求变更：结束时间都以该天 23:59:59 为准，不再出现到当前时间点的情况
 * 影响范围：1、近 x 天  2、可选范围  3、根据 timeRange 反推近 x 天描述
 */

@Component({
    name: "CtTimePicker",
    model: {
        prop: "timeRange",
        event: "select",
    },
})
export default class CtTimePicker extends Vue {
    /**
     * 时间段的快捷选项：不允许大于1的数值存在
     * -1：自定义，0：今天，1：昨天，X：近X天
     */
    @Prop({ default: () => ["0", "1", "7", "30", "-1"], type: Array }) private periodOptions!: string[];
    // 默认选择的时间段，如果不在 periodOptions 范围内则无效
    @Prop({ default: "0", type: String }) private currentPeriod!: string;
    @Prop({ default: 365, type: Number }) private maxDayBeforeNow!: number;
    @Prop({ default: 31 * 24 * 60 * 60 * 1000, type: Number }) private maxTimeRangeLength!: number;
    @Prop({ default: "small", type: String }) private size!: string;
    @Prop({ default: () => [], type: Array }) private timeRange!: [Date?, Date?];
    @Prop({ default: "datetimerange", type: String }) private type!: string;
    @Ref("datePicker") readonly datePickerRef!: ElDatePicker;
    @Prop({ default: false, type: Boolean }) private isHourly!: boolean; // 是否是小时粒度

    // 预设的 label ，除此之外均为“近 x 天”
    private periodLabelMap: { [key: string]: string } = {
        0: `${this.$t("common.ctTimePicker.label1")}`,
        1: `${this.$t("common.ctTimePicker.label2")}`,
        "-1": `${this.$t("common.ctTimePicker.label4")}`,
        recentMonth: `${this.$t("home.chart.instant")}`,
    };
    private repeatInterval: number | null = null;
    private currentPeriodLocal = ""; // 当前选中的快捷选项\
    private originalPickerChangeMethod: any = null; // 保存原始的 picker change 方法
    private minDate: null | number = null;
    private maxDate: null | number = null;
    private timeRangeLocal?: null | [Date?, Date?] = null;
    private pickerOptions: MyDatePickerOptions = {
        // 设置禁用时间
        disabledDate: (time: Date) => {
            // 获取今天0点数据
            const today0 = get0(new Date());
            // 禁用大于今天时间23：59：59
            if (+time > +new Date(+today0 + 24 * 60 * 60 * 1000 - 1)) return true;

            // 禁用小于设置的最早期限的，由于今天会算1天，需要倒推 x-1 天
            if (+time < +today0 - (this.maxDayBeforeNow - 1) * 24 * 60 * 60 * 1000) return true;

            // 当只有minDate有值的时候，需要执行下面的判断逻辑
            if (this.minDate && !this.maxDate) {
                //当最小时间为null的时候则关闭禁用，点清空时会变为0，所以0也要判断
                if (this.minDate === null || this.minDate === 0) return false;

                // 由于现在选择时间后，默认会调整到当日23:59:59，跨度30天即为31天，需要减掉 1s
                const oneMonth = this.maxTimeRangeLength - 1000;
                // 超出当前选择时间前后31天的不可选择，time为选择框中出现的日期

                return +time > this.minDate + oneMonth || +time < this.minDate - oneMonth;
            }

            return false;
        },
        onPick: ({ minDate, maxDate }) => {
            this.minDate = +minDate;
            this.maxDate = +maxDate;
        },
        reset: () => {
            if (!this.timeRange || this.timeRange.length === 0) this.minDate = null;
        },
    };

    private created() {
        // 处理默认选中，如果 入参 存在在 options 则使用，否则取默认
        if (this.currentPeriod && this.periodOptions.includes(this.currentPeriod)) {
            this.currentPeriodLocal = this.currentPeriod;
        } else {
            this.currentPeriodLocal = this.periodOptions[0];
        }
    }

    @Watch("isHourly")
    private onIsHourlyChange(isHourly: boolean) {
        this.$nextTick(() => {
            if (!this.datePickerRef) return;
            (this.datePickerRef as any).mountPicker();

            const val = cloneDeep(this.timeRangeLocal);
            if (isHourly) {
                // 分钟和秒数都是 0
                val[0] = new Date(val[0].getFullYear(), val[0].getMonth(), val[0].getDate(), val[0].getHours(), 0, 0);
                val[1] = new Date(val[1].getFullYear(), val[1].getMonth(), val[1].getDate(), val[1].getHours(), 59, 59);
            } else {
                if (val[1] > new Date()) {
                    val[1] = new Date();
                }
            }
            this.timeRangeLocal = val;
        })
    }

    @Watch("currentPeriod")
    private onCurrentPeriodChange(period: string) {
        if (period && period !== this.currentPeriodLocal && this.periodOptions.includes(period)) {
            this.currentPeriodLocal = period;
        }
    }

    @Watch("currentPeriodLocal")
    private onCurrentPeriodLocalChange(period: string) {
        // 特定情况下需要获取当前的快捷选项，外面使用 .sync 更新
        if (this.currentPeriod !== period) this.$emit("update:currentPeriod", period);

        if (period === "-1") {
            return; // 自定义不作任何处理，保留快捷的选择结果
        }

        const today0 = get0(new Date());
        const today24 = new Date(+today0 + 24 * 60 * 60 * 1000 - 1000);
        let start, end;
        switch (period) {
            case "0": // 今天
                start = today0;
                end = today24;
                break;
            case "1": // 昨天
                start = new Date(+today0 - 24 * 60 * 60 * 1000);
                end = new Date(+today0 - 1000);
                break;
            case "recentMonth": // 本月
                start = new Date(
                    dayjs()
                        .startOf("month")
                        .format("YYYY-MM-DD HH:mm:ss")
                );
                end = today24;
                break;
            default: {
                // 近 x 天，由于今天会算1天，需要倒推 x-1 天
                start = new Date(+today0 - (+period - 1) * 24 * 60 * 60 * 1000);
                end = new Date(+today0 + 24 * 60 * 60 * 1000 - 1000);
            }
        }

        this.timeRangeLocal = [start, end];
    }
    // @Watch("timeRange", { immediate: true })
    // private onTimeRangeChange(val?: [Date?, Date?]) {
    //     this.timeRangeLocal = val;
    //     if (!val || val.length === 0) return;

    //     const [start, end] = val as [Date, Date];

    //     // 判断是否为已存在快捷方式
    //     let currentPeriod = "-1"; // 默认自定义
    //     // 获取几个关键节点
    //     const today = get0(new Date());
    //     const today24 = new Date(+today + 24 * 60 * 60 * 1000 - 1000);
    //     const todayNow = new Date();
    //     const yesterday = +new Date(+today - 24 * 60 * 60 * 1000);
    //     const yesterday24 = new Date(+today - 1000);
    //     // 允许1秒的误差
    //     const endTimeIsInRange = inRange(+end, +todayNow - 1000 * 1, todayNow);

    //     if (+start === +yesterday && +end === +yesterday24) {
    //         // 情况一：昨天
    //         currentPeriod = "1";
    //     } else if (+start === +today && endTimeIsInRange && !this.isSelectCurrentMonth) {
    //         // 情况二：今天，now 的计算有偏差，允许存在
    //         currentPeriod = "0";
    //     } else if (
    //         +start ===
    //         +new Date(
    //             dayjs()
    //                 .startOf("month")
    //                 .format("YYYY-MM-DD HH:mm:ss")
    //         ) &&
    //         +today24 === +end &&
    //         this.isSelectCurrentMonth
    //     ) {
    //         currentPeriod = "recentMonth";
    //     } else if (endTimeIsInRange && !this.isSelectCurrentMonth) {
    //         // 情况三：近多少天，由于今天算1天，所以总数+1
    //         currentPeriod = (+today - +start) / (24 * 60 * 60 * 1000) + 1 + "";
    //     } else {
    //         // 情况四：自定义
    //         currentPeriod = "-1";
    //     }

    //     // 如果存在预设，则使用
    //     if (this.periodOptions.includes(currentPeriod)) {
    //         this.currentPeriodLocal = currentPeriod;
    //     } else {
    //         this.currentPeriodLocal = "-1";
    //     }
    // }
    @Watch("timeRangeLocal")
    private onTimeRangeLocalChange(val: Date[]) {
        const todayNow = new Date();

        if (val?.[0] > todayNow) {
            val[0] = get0(new Date());
        }
        if (val?.[1] > todayNow) {
            val[1] = todayNow;
        }

        if (this.isHourly) {
            // 分钟和秒数都是 0
            val[0] = new Date(val[0].getFullYear(), val[0].getMonth(), val[0].getDate(), val[0].getHours(), 0, 0);
            val[1] = new Date(val[1].getFullYear(), val[1].getMonth(), val[1].getDate(), val[1].getHours(), 59, 59);
        }

        this.$emit("select", val);
    }

    /**
     * 按照五分钟一次的频率更新最新时间，因为打点也是按照五分钟一个点来打
     * 避免用户在选择时间范围后，过了很久再点击查询，但是结束时间还是一开始选中时设置的结束时间
     */
    private repeatUpdateTimeRange() {
        this.repeatInterval = window.setInterval(() => {
            const endTimeIsInRange = inRange(this.timeRange[1], +new Date() - 1000 * 60 * 6, new Date());

            if (endTimeIsInRange && this.currentPeriod !== "-1")
                this.$emit("select", [this.timeRange[0], new Date()]);
        }, 1000 * 60 * 5);
    }

    /**
     * 覆盖el-date-picker的方法以便实现自定义结束时间
     */
    private handlePickerFocus(instance: any) {
        // 覆盖el-date-picker的方法以便实现自定义结束时间
        this.$nextTick(() => {
            const picker = instance.picker;
            const originalMethod = (this.originalPickerChangeMethod = picker.handleRangePick);
            picker.handleRangePick = (...args: any[]) => {
                const maxDate = args[0].maxDate;
                if (+get0(new Date()) === +get0(maxDate)) {
                    picker.defaultTime = [
                        picker.defaultTime[0],
                        this.isHourly
                            ? `${new Date().getHours()}:59:59`
                            : dayjs(new Date()).format("HH:mm:ss"),
                    ];
                } else {
                    picker.defaultTime = ["00:00:00", "23:59:59"];
                }
                originalMethod(...args);
            };
        });
    }

    /**
     * 恢复el-date-picker的原生方法
     */
    private handlePickerBlur(instance: any) {
        // 覆盖el-date-picker的方法以便实现自定义结束时间
        this.$nextTick(() => {
            const picker = instance.picker;
            picker.handleRangePick = this.originalPickerChangeMethod;
        });

        this.pickerOptions.reset();
    }

    mounted() {
        this.repeatUpdateTimeRange();
    }

    beforeDestroy() {
        clearInterval(this.repeatInterval as number);
    }
}
</script>

<style lang="scss" scoped>
.container {
    display: inline-block;
}

.el-date-editor {
    margin-left: 8px;
    vertical-align: middle;
}

.search-bar .el-range-editor {
    margin: 0 10px 0 0;
}

.period-wrap {
    color: $theme-color !important;
    height: 18px !important;
    line-height: 18px !important;
}
</style>

<style lang="scss">
.aocdn-ignore-time-picker-hour {
    .el-time-spinner__wrapper {
        width: 100% !important;
    }

    .el-scrollbar:nth-of-type(2) {
        display: none !important;
    }
}
</style>
