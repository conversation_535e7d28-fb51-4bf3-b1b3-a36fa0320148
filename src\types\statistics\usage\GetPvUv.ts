/*
 * @Description: 统计分析 GetPvUv 接口类型注解，涉及页面：统计分析-pv/uv
 * @Author: wang yue<PERSON>
 */

// GetPvUv 接口中按天/小时的统计数据（用于PVUV tab）
export interface PvUvItem {
    pvCount: number;
    uvCount: number;
    timestamp: number;
}

// GetPvUv 接口数据（用于PVUV tab）
export interface PvUvFetchData {
    hours: PvUvItem[];
    daily: PvUvItem[];
    maxPv: number;
    totalPv: number;
    maxUv: number;
    totalUv: number;
}
