/**
 * 搜索当前目录下符合命名的 vue 组件，注册为全局Vue组件
 * 		以 .mod 结尾的为通用性功能组件
 * 		以 .logic 结尾的为通用性业务组件
 */
import Vue from "vue";
// 注册全局组件
class RigsterGlobalComponent {
    constructor() {
        this.register();
    }

    register() {
        // 增加 vue-eachrts 的注册
        const r = require.context("./", true, /\.?(mod|logic|ECharts)\.vue$/);
        r.keys &&
            typeof r.keys === "function" &&
            r.keys().forEach(key => {
                // key是路径
                let component = r(key).default;
                // 特殊处理：ts 中默认导出的是 VueComponet ，并不是一个 options 对象，本地开发时无碍，打包后会导致组件注册失败
                if (typeof component === "function") {
                    component = component.options;
                }

                // TODO 组件必须显示声明 name ，不然发布时会出现注册失败的情况，原因尚未确定
                const name = component.name;

                Vue.component(name, component);
            });
        // 增加重构后的新组件注册
        const nr = require.context("./simpleform", true, /\.vue$/);
        nr.keys().forEach(key => {
            let component = nr(key).default;
            if (typeof component === "function") {
                component = component.options;
            }

            // TODO 组件必须显示声明 name ，不然发布时会出现注册失败的情况，原因尚未确定
            const name = component.name;
            Vue.component(name, component);
        });
    }
}
new RigsterGlobalComponent();
