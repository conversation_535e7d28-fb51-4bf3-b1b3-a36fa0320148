<!--
* CDN是否支持开启HTTPS提示，用法请全局搜索 cdn-https-support-tip
* 如果是不相关的提示，不要引入新的判断条件，建议拷贝一份，修改后使用，避免污染公共组件
-->
<template>
    <el-tooltip
        v-if="!isSupportHttps && !alreadyOpen"
        effect="dark"
        placement="top"
        popper-class="aocdn-ignore-lock-tip-wrapper"
    >
        <div slot="content" class="lock-tip">
            <i18n path="domain.httpsSupport.tip1">
                <a class="aocdn-ignore-link" :href="link" target="_blank">{{
                    $t("domain.httpsSupport.tip2")
                }}</a>
            </i18n>
        </div>
        <slot :https-disabled="true"></slot>
    </el-tooltip>
    <div v-else>
        <slot :https-disabled="false"></slot>
    </div>
</template>

<script lang="ts">
import { nUserModule } from "@/store/modules/nuser";
import { Component, Vue, Prop, Watch } from "vue-property-decorator";

@Component({
    name: "CdnHttpsSupportTip",
})
export default class CdnHttpsSupportTip extends Vue {
    // 存量域名已开启的不需要提示，关闭后重新开启则需要提示
    @Prop({ type: Boolean, default: false })
    alreadyOpen!: boolean;
    // 加速类型是否为【CDN加速】
    @Prop({ type: Boolean, default: false })
    isCDN!: boolean;

    get isSupportHttps() {
        if (window.__POWERED_BY_QIANKUN__ || !this.isCDN) return true;

        // 线上or个人用户 才需要相关判断是否支持https
        if (
            (nUserModule.userInfo as any).saleChannel === "2" ||
            (nUserModule.userInfo as any).userType === "user"
        ) {
            return nUserModule.supportHttps.https;
        }

        return true;
    }
    get link() {
        return nUserModule.supportHttps.link;
    }

    // 添加 watch 来监听 https-disabled 状态变化
    @Watch("isSupportHttps", { immediate: true })
    onHttpsSupportChange() {
        this.$emit("update:support", this.isSupportHttps);
    }
}
</script>
