import { RouteConfig } from "vue-router";
import { nScriptModule } from "@/store/modules/nscript";

const scriptRouter: RouteConfig = {
    path: "/nscript",
    name: "nscript",
    component: () => import("./index.vue"),
    redirect: {
        name: "nscript.dictionary",
    },
    meta: {
        breadcrumb: {
            title: "UDFScript",
            route: ["nscript"],
        },
    },
    children: [
        {
            path: "dictionary",
            name: "nscript.dictionary",
            component: () => import("./dictionary/index.vue"),
            meta: {
                breadcrumb: {
                    title: "全局字典",
                    route: ["nscript", "nscript.dictionary"],
                },
                perm: "script.dictionary",
            },
        },
        {
            path: "dictionary/add",
            name: "nscript.dictionary.add",
            component: () => import("./dictionary/add/DictionaryAdd.vue"),
            meta: {
                breadcrumb: {
                    title: "添加全局字典",
                    route: ["nscript", "nscript.dictionary", "nscript.dictionary.add"],
                    isdisabledTo: true,
                },
            },
        },
        {
            path: "dictionary/detail",
            name: "nscript.dictionary.detail",
            component: () => import("./dictionary/add/DictionaryAdd.vue"),
            meta: {
                breadcrumb: {
                    title: "查看全局字典",
                    route: ["nscript", "nscript.dictionary", "nscript.dictionary.detail"],
                    isdisabledTo: true,
                },
            },
        },
        {
            path: "dictionary/edit",
            name: "nscript.dictionary.edit",
            component: () => import("./dictionary/add/DictionaryAdd.vue"),
            meta: {
                breadcrumb: {
                    title: "编辑全局字典",
                    route: ["nscript", "nscript.dictionary", "nscript.dictionary.edit"],
                    isdisabledTo: true,
                },
            },
        },
        {
            path: "task",
            name: "nscript.task",
            component: () => import("./task/index.vue"),
            meta: {
                breadcrumb: {
                    title: "全局task脚本",
                    route: ["nscript", "nscript.task"],
                },
                perm: "script.task",
            },
        },
        {
            path: "task/add",
            name: "nscript.task.add",
            component: () => import("./task/add/TaskAdd.vue"),
            meta: {
                breadcrumb: {
                    title: "添加全局task脚本",
                    route: ["nscript", "nscript.task", "nscript.task.add"],
                    isdisabledTo: true,
                },
                perm: "script.task",
            },
        },
        {
            path: "task/detail",
            name: "nscript.task.detail",
            component: () => import("./task/add/TaskAdd.vue"),
            meta: {
                breadcrumb: {
                    title: "查看全局task脚本",
                    route: ["nscript", "nscript.task", "nscript.task.detail"],
                    isdisabledTo: true,
                },
            },
        },
        {
            path: "task/edit",
            name: "nscript.task.edit",
            component: () => import("./task/add/TaskAdd.vue"),
            meta: {
                breadcrumb: {
                    title: "编辑全局task脚本",
                    route: ["nscript", "nscript.task", "nscript.task.edit"],
                    isdisabledTo: true,
                },
            },
        },
        {
            path: "business",
            name: "nscript.business",
            component: () => import("./business/index.vue"),
            meta: {
                breadcrumb: {
                    title: "业务脚本",
                    route: ["nscript", "nscript.business"],
                },
                perm: "script.business",
            },
        },
        {
            path: "business/add",
            name: "nscript.business.add",
            component: () => import("./business/add/BusinessAdd.vue"),
            meta: {
                breadcrumb: {
                    title: "添加业务脚本",
                    route: ["nscript", "nscript.business", "nscript.business.add"],
                    isdisabledTo: true,
                },
            },
            beforeEnter(to, from, next) {
                nScriptModule.nGetScriptDomainList(true);
                next();
            },
        },
        {
            path: "business/edit",
            name: "nscript.business.edit",
            component: () => import("./business/add/BusinessAdd.vue"),
            meta: {
                breadcrumb: {
                    title: "编辑业务脚本",
                    route: ["nscript", "nscript.business", "nscript.business.edit"],
                    isdisabledTo: true,
                },
            },
        },
        {
            path: "business/detail",
            name: "nscript.business.detail",
            component: () => import("./business/add/BusinessAdd.vue"),
            meta: {
                breadcrumb: {
                    title: "查看业务脚本",
                    route: ["nscript", "nscript.business", "nscript.business.detail"],
                    isdisabledTo: true,
                },
            },
        },
    ],
};

export default scriptRouter;
