<template>
    <el-card>
        <div class="total-wrapper">
            <div class="total">
                <div class="tip">
                    {{ $tc(`statistics.eas.flowList[${listIndex}]`) }}{{ titleTipSuffix }}：
                    <span class="num">
                        {{ convertFlowB2P(fetchData.totalFlow) }}
                    </span>
                </div>
            </div>
            <div class="total" v-if="compareConfig.useCompare">
                <div class="tip">
                    {{ $tc(`statistics.eas.flowList[${listIndex}]`) }}2：
                    <span class="num">
                        {{ convertFlowB2P(fetchData2.totalFlow) }}
                    </span>
                </div>
            </div>
        </div>

        <v-chart class="statistic-chart-flow" v-loading="loading"
            :element-loading-text="$t('statistics.common.chart.loading')" autoresize theme="cdn" :options="options" @legendselectchanged="legendselectchanged" />

        <ct-tip>
            {{ $t("statistics.eas.tip13") }}
            <el-tooltip effect="dark" :content="$t('statistics.common.searchDownloadContent')" placement="right">
                <i class="el-icon-download ipa-download-icon usage-download-icon"
                    @click="() => $refs.flowTable.downloadTable($t('statistics.eas.tab[1]'))"></i>
            </el-tooltip>
        </ct-tip>
        <usage-table ref="flowTable"></usage-table>
    </el-card>
</template>

<script>
/* eslint-disable @typescript-eslint/camelcase */
import { statistics as StatisticsUrl } from "@/config/url/ipa/statistics";

// **单位换算：ms->"YYYY-MM-DD HH:mm:ss"
import { ScaleModule } from "@/store/modules/scale";
import { convertFlowB2P } from "@/utils";
import { timeFormat } from "@/filters";
import mixChart from "../mixChart";
import chartShim from "../chartShim";
import usageTable from "./usageTable.vue";
import { cloneDeep } from "lodash-es";
import { getMaxFromList } from "@/utils/utils";
import { THEME_AREA_STYLE } from "@/config/echart/cdn-theme";
import { themeColorArr } from "@cdnplus/common/config/echart/theme";

export default {
    mixins: [mixChart, chartShim],
    components: { usageTable },
    data() {
        return {
            currentCmp: "flowChart",
            activeStatusFlow: "flow",
            downloadDataList: [],
            loading: false,
            reqParamsFromParentBackup: {},
            originFetchResult: {
                list1: [],
                list2: [], // 对比
            },

            titleTipSuffix: "",
            fetchData: {
                totalFlow: 0,
                avgQueryflow: 0,
            },
            fetchData2: {
                totalFlow: 0,
                avgQueryflow: 0,
            },
            fetchDataList: [],
            selectedLegends: null, // 记录当前勾选legend
        };
    },
    computed: {
        listIndex() {
            return this.queryForm?.flowType || 0;
        },
        // 流量类型
        flowTypeMap() {
            const map = new Map([
                [0, `${this.$t("statistics.eas.flowList[0]")}`],
                [1, `${this.$t("statistics.eas.flowList[1]")}`],
                [2, `${this.$t("statistics.eas.flowList[2]")}`],
            ]);
            return map.get(this.queryForm.flowType) || this.queryForm.flowType;
        },
        flowIndention() {
            const size = {
                B: 1,
                KB: this.scale,
                MB: Math.pow(this.scale, 2),
                GB: Math.pow(this.scale, 3),
                TB: Math.pow(this.scale, 4),
                PB: Math.pow(this.scale, 5),
            };
            let peekFlow = [];
            if (this.compareConfig.useCompare) {
                const max1 = (!this.selectedLegends || this.selectedLegends[this.currentLegends[0]]) ? getMaxFromList(this.originFetchResult.list1, "flow") : 0;
                const max2 = (!this.selectedLegends || this.selectedLegends[this.currentLegends[1]]) ? getMaxFromList(this.originFetchResult.list2, "flow") : 0;
                peekFlow = Math.max(max1, max2)
            } else {
                peekFlow = getMaxFromList(this.originFetchResult.list1, "flow")
            }
            // const peekFlow = this.compareConfig.useCompare
            //     ? Math.max(getMaxFromList(this.originFetchResult.list1, "flow"), getMaxFromList(this.originFetchResult.list2, "flow"))
            //     : getMaxFromList(this.originFetchResult.list1, "flow");
            const rst = convertFlowB2P(peekFlow, this.scale);
            return [size[rst._unit], rst.unit];
        },
        options() {
            let count = 0;
            const options = {
                tooltip: {
                    trigger: "axis",
                    // ***设置数据对比时的hover效果
                    formatter: params => {
                        const time = this.$dayjs(params[0].axisValue * 1).format("YYYY-MM-DD HH:mm:ss");
                        const value = params[0].value;
                        if (params[1]) {
                            // ***保存数据对比时的时间信息
                            const time1 = this.$dayjs(params[1].data.date).format("YYYY-MM-DD HH:mm:ss");
                            const value1 = params[1].value;
                            return `
                                <div>${time}</div>
                                <div>
                                    <div style="display:inline-block;width:8px;height:8px;background-color:#6694D5;border-radius:50%;"></div>
                                    ${this.$t("statistics.provider.traffic")}：${value} ${this.flowIndention[1]}
                                </div>
                                <div>${time1}</div>
                                <div>
                                    <div style="display:inline-block;width:8px;height:8px;background-color:#FA8334;border-radius:50%;"></div>
                                    ${this.$t("statistics.provider.traffic")}：${value1} ${this.flowIndention[1]}
                                </div>
                                `;
                        } else {
                            return `
                                <div>
                                    <div style="display:inline-block;width:8px;height:8px;background-color:#6694D5;border-radius:50%;"></div>
                                    ${time}</div>
                                <div>${this.$t("statistics.provider.traffic")}：${value} ${this.flowIndention[1]}</div>
                                `;
                        }
                    },
                },
                xAxis: {
                    // type: 'time',
                    type: "category",
                    boundaryGap: false,
                    data: [],
                    axisLabel: {
                        formatter: value => {
                            const time = this.$dayjs(value * 1).format("MM-DD HH:mm");
                            const arr = time.split(" ");
                            count += 1;
                            if (count % 2 === 1) return `${arr[0]}\n${arr[1]}`;
                        },
                    },
                },
                yAxis: {
                    name: `${this.$t("home.chart.unit")}: ${this.flowIndention[1]}`,
                    type: "value",
                },
                series: [],
            };
            Object.assign(options, this.toolBox);


            const chartData = this.getChartData(this.fetchDataList[0])
            options.series = [
                {
                    name: `${this.$t("statistics.provider.traffic")}1`,
                    type: "line",
                    data: chartData.chartData,
                    areaStyle: THEME_AREA_STYLE[themeColorArr[0]],
                }
            ]
            options.xAxis.data = chartData.xData

            // 增加图例
            if (this.compareConfig.useCompare) {
                const chartData2 = this.getChartData(this.fetchDataList[1])
                options.series.push({
                    name: `${this.$t("statistics.provider.traffic")}2`,
                    type: "line",
                    data: chartData2.chartData,
                    areaStyle: THEME_AREA_STYLE[themeColorArr[0]],
                })

                options.legend = {
                    data: this.currentLegends
                };
                options.legend.selected = this.selectedLegends
            }

            return options;
        },
        // 记录当前全量legend，方便对应数据
        currentLegends() {
            return [
                `${this.$t("statistics.provider.traffic")}1`,
                `${this.$t("statistics.provider.traffic")}2`
            ];
        }
    },
    methods: {
        resetQuery() {
            this.queryForm.flowType = 0;
        },
        // ***单位换算：B自动适配
        convertFlowB2P(val = 0) {
            return convertFlowB2P(val, this.scale).result;
        },

        async initData(reqParam, isShow, array) {
            // 重置当前legends
            this.selectedLegends = null
            // 当前流量类型
            this.queryForm.flowType = reqParam.type || 0;
            this.downloadDataList = [];
            this.loading = true;
            const fetchList = [];

            if (isShow) {
                this.titleTipSuffix = "1";
                for (let index = 0; index < array.length; index++) {
                    const element = array[index];
                    fetchList.push(this.getFlowList({
                        ...reqParam,
                        start_time: Math.floor(element[0] / 1000),
                        end_time: Math.floor(element[1] / 1000),
                    }));
                }
            } else {
                this.titleTipSuffix = "";
                fetchList.push(this.getFlowList(reqParam));
            }

            const fetchResultList = await Promise.all(fetchList);
            this.fetchDataList = fetchResultList.map(item => item.result);

            this.fetchData.avgQueryflow = this.getAvgFlow(this.fetchDataList[0].list);
            this.fetchData.totalFlow = this.fetchDataList[0].total;
            if (isShow) {
                this.fetchData2.avgQueryflow = this.getAvgFlow(this.fetchDataList[1].list);
                this.fetchData2.totalFlow = this.fetchDataList[1].total;
            }
            this.setDownloadData(this.fetchDataList, isShow);
            this.loading = false;
        },
        getChartData(_fetchDataList) {
            const xData = []
            const chartData = []
            cloneDeep(_fetchDataList)
                ?.list
                .sort((a, b) => a.timestamp - b.timestamp)
                .map(item => {
                    xData.push(item.timestamp * 1000)
                    chartData.push({
                        value: (item.flow / this.flowIndention[0]).toFixed(2),
                        date: item.timestamp * 1000,
                    })
                });

            return {
                xData,
                chartData
            }
        },
        setDownloadData(fetchDataList, isShow) {
            this.originFetchResult.list1 = cloneDeep(fetchDataList[0].list);
            this.originFetchResult.list1.sort((a, b) => a.timestamp - b.timestamp);
            this.downloadDataList = this.downloadDataList.concat(this.getChartData(fetchDataList[0]).chartData);

            if (isShow) {
                this.originFetchResult.list2 = cloneDeep(fetchDataList[1].list);
                this.originFetchResult.list2.sort((a, b) => a.timestamp - b.timestamp);
                this.downloadDataList = this.downloadDataList.concat(this.getChartData(fetchDataList[1]).chartData);
            }
        },
        getAvgFlow(flowList = []) {
            return flowList
                .filter(itm => itm.flow)
                .reduce((prev, cur) => prev + cur.flow, 0) / (flowList.length || 1);
        },
        getFlowList(data) {
            if (!data) {
                data = { account_id: this.$store.state.user.userInfo.userId };
            }
            return this.$ctFetch(StatisticsUrl.flowList, {
                method: "POST",
                transferType: "json",
                body: {
                    data,
                },
            });
        },

        // 监听图例选择变化
        legendselectchanged({ selected }) {
            // 更新勾选legend
            this.selectedLegends = selected;
        },

        /**
         * 导出处理函数
         */
        tableToExcel() {
            let str = `${this.$t("statistics.usageQuery.BandwidthFlow.tableToExcel.excelColumn1")},${this.$t("statistics.provider.traffic")}(${this.MB})\n`;

            const { titleTipSuffix, fetchData, fetchData2 } = this;
            const { totalFlow } = fetchData;

            // 输出格式
            const downloadDataList = this.compareConfig.useCompare ? this.originFetchResult.list1.concat(this.originFetchResult.list2) : this.originFetchResult.list1;
            str += downloadDataList.reduce((str, item) => {
                const info = (item.flow / Math.pow(this.scale, 2)).toFixed(2);;
                str += `${timeFormat(item.timestamp)},`;
                str += info + "\n";
                return str;
            }, "");

            str += `${this.flowTypeMap}${titleTipSuffix},${(totalFlow / Math.pow(this.scale, 2)).toFixed(2)} ${this.MB}\n `;

            if (this.compareConfig.useCompare) {
                str += `${this.flowTypeMap}2,${(fetchData2.totalFlow / Math.pow(this.scale, 2)).toFixed(2)} ${this.MB}\n `;
            }

            const timeRange = this.reqParamsFromParentBackup.timeRange;
            const startTime = timeRange[0]
                ? this.$dayjs(timeRange[0])
                    .startOf("day")
                    .format("YYYYMMDDHHmmss")
                : "";
            const endTime = timeRange[1]
                ? this.$dayjs(timeRange[1])
                    .endOf("day")
                    .format("YYYYMMDDHHmmss")
                : "";
            const string = startTime && endTime ? `${startTime}-${endTime}` : "";
            const name = `${this.$t("statistics.eas.tip30")}_${string}`;
            this.downloadExcel({
                name: name,
                str,
            });
        },
        handleClickDataCompareBtn() {
            this.compareConfig.visible = !this.compareConfig.visible;
            if (!this.compareConfig.visible) {
                this.closeDataCompare();
            }
        },
        closeDataCompare() {
            this.compareConfig.visible = false;
            this.compareConfig.useCompare = false;
            this.updateAgency();
        },
    },
};
</script>
<style lang="scss" scoped>
.sub-query-wrapper {
    margin-bottom: 12px;
    display: flex;
    justify-content: space-between;
}

.total-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

// 统计数字
.total {
    width: 100%;
    text-align: left;
    font-size: 14px;
    color: #333333;
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 8px;

    @media (max-width: 1350px) {
        grid-template-columns: 1fr;
    }

    .num {
        margin-left: 4px;
    }

    .date {
        color: #666666;
        font-size: 12px;
        margin-left: 4px;
    }
}

.compare-tip {
    display: grid;
    grid-template-columns: 1fr 60px 1fr;

    &-center {
        font-size: 14px;
        color: $color-info;
        font-weight: 600;
    }

    &-part {
        width: 100%;
        text-align: left;
        font-size: 14px;
        color: #333333;

        &-time {
            color: $color-info;
            font-size: 12px;
        }

        &-text {
            &-num {
                margin-left: 4px;
            }

            &-date {
                color: #666666;
                font-size: 12px;
                margin-left: 4px;
            }
        }
    }
}

// 内容区
.statistic-chart-flow {
    clear: both;
    width: 100%;
    height: 380px;
}

.ml-12 {
    margin-left: 12px;
}

.data-compare-btn {
    display: flex;
    align-items: center;
    writing-mode: vertical-lr;
    color: $color-master;
    background-color: $color-master-bg;
    margin-top: -56px;
    margin-left: -20px;
    width: 4px;
    padding: 12px 8px;
    border: 0;
    float: left;

    &:hover {
        background-color: $color-master-bg;
        color: $color-master;
    }
}
</style>
