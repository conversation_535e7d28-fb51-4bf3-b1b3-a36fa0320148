import { ipv4, ip, domain, reservedIp } from "@cdnplus/common/config/pattern";
// import actions from "@/microApp/actions";
// import { get } from "lodash-es";
import { AppModule } from "@/store/modules/app";
import i18n from "@/i18n";
import { portRegex } from '@/utils/validator.utils';
import { productCodeToName } from "@/utils/product";

const ipReg = new RegExp(ip);
export default {
    data() {
        return {
            // 外链改造层数-数组
            timesList: [
                { label: "0", value: 0 },
                { label: "1", value: 1 },
                { label: "2", value: 2 },
                { label: "3", value: 3 },
                { label: "4", value: 4 },
                { label: "5", value: 5 },
            ],
            // 缓存配置-类型-映射
            type_list: {
                0: i18n.t("domain.detail.cacheModeMap[0]"),
                1: i18n.t("domain.detail.cacheModeMap[1]"),
                2: i18n.t("domain.detail.cacheModeMap[2]"),
                3: i18n.t("domain.detail.cacheModeMap[3]"),
                4: i18n.t("domain.detail.cacheModeMap[4]"),
                5: i18n.t("domain.detail.cacheModeMap[5]"),
            },
            // 缓存配置-缓存规则-映射
            cache_type_list: {
                1: i18n.t("domain.detail.label36"),
                2: i18n.t("domain.detail.label37"),
                3: i18n.t("domain.detail.label38"),
                4: i18n.t("domain.detail.label35"),
                5: i18n.t("domain.detail.label123"),
            },
            // 缓存配置-去问号缓存-映射
            cache_with_args_list: {
                0: i18n.t("domain.detail.label96"),
                1: i18n.t("domain.detail.label97"),
            },
            rewriteModeMap: {
                add: i18n.t("domain.detail.label80"), // 追加
                cover: i18n.t("domain.detail.label81"), // 覆盖
            },
            areaScopeLabel(area_scope) {
                let label = "";
                switch (area_scope) {
                    case 1:
                        label = i18n.t("domain.areaScope[0]");
                        break;
                    case 2:
                        label = i18n.t("domain.areaScope[1]");
                        break;
                    case 3:
                        label = i18n.t("domain.areaScope[2]");
                        break;
                }
                return label;
            },
            productCodeLabel(productCode) {
                return productCodeToName(productCode);
            },
            // flv拖拉-拖拉模式
            flv_seek_type_list: {
                time: i18n.t("domain.detail.placeholder70"),
                byte: i18n.t("domain.detail.placeholder71"),
            },
            rules: {},
        };
    },

    computed: {},

    filters: {},

    methods: {
        // 升级到高级版
        toHighVersion() {
            const billingInfos = sessionStorage.getItem("accessOnePackageInfos")
                ? JSON.parse(sessionStorage.getItem("accessOnePackageInfos"))
                : [];

            const router = AppModule.baseAppRouter;

            if (!router) {
                return;
            }

            const info = billingInfos.find(item => item.resourceType === "EDGE_SEC_ACCE");
            if (!info) {
                return;
            }

            router.push({
                path: "/packageManagement/purchase",
                query: {
                    resourceId: info.resourceId,
                },
            });
        },
        async valid_http_origin_port(rule, value, callback) {
            const pattern = portRegex;
            if (value === "" || value === null || value === undefined) {
                return callback(new Error(i18n.t("domain.detail.tip90")));
            }
            if (!pattern.test(value) || Number(value) === 443) {
                return callback(new Error(i18n.t("domain.detail.tip91")));
            }
            return callback();
        },
        async https_origin_port_valid(rule, value, callback) {
            const pattern = portRegex;
            if (value === "" || value === null || value === undefined) {
                // return callback(new Error("请填写HTTPS端口"));
                return callback(new Error(i18n.t("domain.detail.tip92")));
            }
            if (!pattern.test(value)) {
                // return callback(new Error("请输入正确的端口号，1-65535"));
                return callback(new Error(i18n.t("domain.detail.tip93")));
            }
            return callback();
        },
        // 已挪到该文件下：/utils/validator.utils
        // checkArrayRepeat(array) {
        //     array.sort();
        //     const reEle = [];
        //     for (let i = 0; i < array.length; i++) {
        //         if (i + 1 === array.length) {
        //             continue;
        //         }
        //         //判断相邻的元素是否相同
        //         if (array[i] === array[i + 1]) {
        //             reEle.push(array[i]);
        //         }
        //     }
        //     // 去重
        //     return Array.from(new Set(reEle)).join(",");
        // },
        // 缓存参数-忽略参数 字典翻译
        ignoreParamsFormatter({ ignore_params }) {
            if (ignore_params === 1) {
                return i18n.t("domain.detail.label51")
            } else if (ignore_params === 2) {
                return i18n.t("domain.detail.label52")
            } else if (ignore_params === 3) {
                return i18n.t("domain.detail.label53")
            } else if (ignore_params === 4) {
                return i18n.t("domain.detail.label54")
            } else {
                return "";
            }
        },
    },
};
