<template>
    <el-dialog
        :title="from === 'create' ? $t('domain.create.originServer-1') : $t('domain.create.originServer-2')"
        :append-to-body="true"
        :close-on-click-modal="false"
        :visible.sync="dialogVisible"
        :before-close="cancel"
        class="origin-dialog"
    >
        <el-form
            :rules="addRules"
            :model="originForm"
            ref="originForm"
            class="origin-form"
            label-width="160px"
        >
            <el-form-item>
                <el-radio-group v-model="originForm.is_xos">
                    <el-radio :label="0">{{ $t("domain.create.xos0") }}</el-radio>
                    <el-radio :label="1" v-if="showXosOrigin" :disabled="isXosZosSelected">{{
                        $t("domain.create.xos1")
                    }}</el-radio>
                    <el-radio :label="2" v-if="showZosOrigin" :disabled="isXosZosSelected">{{
                        $t("domain.create.zos.tip1")
                    }}</el-radio>
                </el-radio-group>
                <div class="note" v-if="originForm.is_xos === 1 && showXosOrigin">
                    {{ $t("domain.create.tip42") }}
                    <a v-if="!isCtclouds" @click="$docHelp(xosOriginLink)" class="aocdn-ignore-link">
                        {{ $t("domain.create.tip43") }}
                    </a>
                    <br />
                    {{ $t("domain.create.tip44-1") }}
                    <br />
                    {{
                        $t("domain.create.tip44", {
                            endPointStr: endPointStr
                                ? endPointStr
                                : `ctyunxs.cn${$t("domain.or")}xstore.ctyun.cn`,
                        })
                    }}
                    <br />
                    {{ $t("domain.create.tip45") }}
                    <br />
                    {{ $t("domain.editPage.tip19-5") }}
                    <br />
                </div>
                <div class="note" v-if="originForm.is_xos === 2 && showZosOrigin">
                    {{ $t("domain.create.zos.tip6") }}
                    <a v-if="!isCtclouds" @click="$docHelp(zosOriginLink)" class="aocdn-ignore-link">
                        {{ $t("domain.create.zos.tip7") }}
                    </a>
                    <br />
                    {{ $t("domain.create.tip44-1") }}
                    <br />
                    {{
                        $t("domain.create.zos.tip8", {
                            endPointStr:
                                (zosOriginSuffix || []).join(` ${$t("domain.or")} `) || ".zos.ctyun.cn",
                        })
                    }}
                    <br />
                    {{ $t("domain.create.zos.tip9") }}
                    <br />
                    {{ $t("domain.editPage.tip19-5") }}
                    <br />
                </div>
            </el-form-item>
            <el-form-item
                v-if="dialogVisible && originForm.is_xos === 0"
                :label="$t('domain.create.originServer')"
                prop="origin"
                class="test-origin-wrapper"
            >
                <el-input v-model="originForm.origin" placeholder="" class="text-content"></el-input>
            </el-form-item>
            <el-form-item
                v-if="dialogVisible && originForm.is_xos === 1"
                :label="$t('domain.create.originServer')"
                prop="origin"
            >
                <el-autocomplete
                    popper-class="my-autocomplete"
                    v-model.trim="originForm.origin"
                    :fetch-suggestions="querySearch"
                    :placeholder="$t('domain.create.placeholder5')"
                    @select="handleSelect"
                    class="text-content"
                    :popper-append-to-body="false"
                >
                    <template slot-scope="{ item }">
                        <el-tooltip
                            effect="dark"
                            :content="item.bucketName + '.' + item.endPoint"
                            placement="right-end"
                        >
                            <div class="name">{{ item.bucketName + "." + item.endPoint }}</div>
                        </el-tooltip>
                    </template>
                </el-autocomplete>
            </el-form-item>
            <el-form-item
                v-if="dialogVisible && originForm.is_xos === 2"
                :label="$t('domain.create.originServer')"
                prop="origin"
            >
                <el-input
                    v-model="originForm.origin"
                    :placeholder="$t('domain.create.zos.tip2')"
                    class="text-content"
                ></el-input>
            </el-form-item>
            <el-form-item v-if="dialogVisible" :label="$t('domain.create.level')" prop="role">
                <el-select
                    v-model="originForm.role"
                    :placeholder="$t('domain.detail.tip74')"
                    class="text-content"
                >
                    <el-option value="master" :label="$t('domain.create.primary')" />
                    <el-option value="slave" :label="$t('domain.create.secondary')" />
                </el-select>
            </el-form-item>
            <host-select-mod
                v-model.trim="originForm.origin_host"
                v-if="dialogVisible && !isBatch && useEcgw === 1"
                :label="$t('domain.create.originHost')"
                host-type="originHost"
                :accelerate-domains="accelerateDomains"
                :origin-domains="getOriginDomains"
                label-width="160px"
                style="width: 560px;"
                :rules="addRules.origin_host"
                prop="origin_host"
            ></host-select-mod>
            <el-form-item v-if="dialogVisible" :label="$t('domain.create.weight')" prop="weight">
                <el-input
                    v-model.trim="originForm.weight"
                    :placeholder="$t('domain.detail.placeholder20')"
                    class="text-content"
                ></el-input>
            </el-form-item>
        </el-form>
        <template #footer>
            <el-button @click="cancel">{{ $t("common.dialog.cancel") }}</el-button>
            <el-button type="primary" @click="submit">{{ $t("common.dialog.submit") }}</el-button>
        </template>
    </el-dialog>
</template>

<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";
import pattern from "@/config/pattern";
import { Form } from "element-ui";
import { domain, ip } from "@cdnplus/common/config/pattern";
import { NEW_PREFIX } from "@/config/url/_PREFIX";
import { nUserModule } from "@/store/modules/nuser";
import { formValidate2Promise } from "@/utils";
import { isStaticProduct } from "@/utils/product";
import { StatisticsModule } from "@/store/modules/statistics";
import { reservedIp } from "@/config/npattern";
import hostSelectMod from "@/components/hostSelect/hostSelect.mod.vue";

type originParam = { role: string; origin: string; weight: string; is_xos: number; origin_host: string };
const urlReg = new RegExp(domain);
const ipReg = new RegExp(ip);
const reservedIpReg = new RegExp(reservedIp);
const xosList = NEW_PREFIX + "/domain/xstore-xos/list";
const xosSuffix = NEW_PREFIX + "/domain/xstore-xos/suffix";

const weightPattern = "^([1-9][0-9]?|100)$";
const weightReg = new RegExp(weightPattern);

@Component({
    components: { hostSelectMod },
})
export default class UpdateDialog extends Vue {
    private pattern = pattern;
    private xosOriginList = [];
    private endPointList = [];
    private endPointStr = "";
    private temp_bucket = "";
    private wholeStationProductCode = ["006", "104", "105"]; // 全站加速的 product_code
    @Prop({ default: "create", type: String }) private from!: string;
    @Prop({ default: "", type: String }) private currenIndex!: string;
    // 弹框显示标志位
    @Prop({ default: false, type: Boolean }) private dialogVisible!: boolean;
    @Prop({ default: { role: "master", origin: "", weight: "", is_xos: 0, origin_host: "" } })
    private originForm!: originParam;
    @Prop({ default: () => [], type: Array }) private originList!: originParam[];
    @Prop({ default: "", type: String }) private productCode!: string;
    @Prop({ default: 0, type: Number }) private useEcgw!: number;
    @Prop({ default: 0, type: Number }) private backoriginMode!: number;
    @Prop({ default: false, type: Boolean }) private isBatch!: boolean;
    @Prop({ default: () => [], type: Array }) public accelerateDomains!: string[];

    private get isCtclouds() {
        return nUserModule.isCtclouds;
    }
    private get xosOriginLink() {
        return "https://www.ctyun.cn/document/10306929";
    }
    private get zosOriginLink() {
        return "https://www.ctyun.cn/document/10026735";
    }
    private get zosOriginSuffix() {
        return StatisticsModule.zosOriginSuffix;
    }
    private get isXosZosSelected() {
        const idx = this.originList.findIndex(item => item.is_xos === 1 || item.is_xos === 2);
        return idx > -1 && `${idx}` !== `${this.currenIndex}`;
    }
    // 校验规则
    get addRules() {
        return {
            origin: [
                { required: true, message: this.$t("domain.detail.tip73"), trigger: "blur" },
                {
                    validator: (rule: any, value: string, callback: Function) => {
                        const { origin } = this.originForm;
                        const list = JSON.parse(JSON.stringify(this.originList));
                        if (this.from === "create") {
                            list.push(this.originForm);
                        }
                        if (this.from === "edit") {
                            list.splice(this.currenIndex, 1);
                            list.splice(this.currenIndex, 0, this.originForm);
                        }
                        const endPointList = JSON.parse(JSON.stringify(this.endPointList));
                        const selsctArr = [...list].map(item => item);
                        let suffixNum = 0;
                        let count = 0;
                        const originData: any = JSON.parse(JSON.stringify(this.originForm.origin));
                        endPointList.forEach((item: string | any[]) => {
                            if (item) {
                                selsctArr.forEach(ori => {
                                    if (ori.origin.endsWith(item) && ori.is_xos === 1) {
                                        suffixNum++;
                                    }
                                });
                                // 当前弹窗如果选中的是 媒体存储源站，输入的域名后缀，至少包含一个后端接口返回的后缀中的一个，否则需要校验报错
                                if (originData.endsWith(item) && this.originForm.is_xos === 1) {
                                    count++;
                                }
                            }
                        });

                        // zos
                        if (
                            this.originForm.is_xos === 2 &&
                            !this.zosOriginSuffix?.some(zos => this.originForm.origin?.endsWith(zos))
                        ) {
                            callback(
                                new Error(
                                    `${this.$t("domain.create.zos.tip3", {
                                        endPointStr:
                                            this.zosOriginSuffix?.join(` ${this.$t("domain.or")} `) ||
                                            ".zos.ctyun.cn",
                                    })}`
                                )
                            );
                        }

                        const specialOriList = list.filter((ori: originParam) => ori.is_xos > 0);
                        const xosList = specialOriList.filter((ori: originParam) => ori.is_xos === 1);
                        const zosList = specialOriList.filter((ori: originParam) => ori.is_xos === 2);

                        if (xosList.length && zosList.length) {
                            callback(new Error(`${this.$t("domain.create.zos.tip4")}`));
                        }

                        // xos || zos 源站只能配置一个
                        if (this.showZosOrigin) {
                            if (xosList.length > 1 || zosList.length > 1) {
                                callback(new Error(`${this.$t("domain.create.zos.tip5")}`));
                            }
                        }

                        if (!ipReg.test(origin) && !urlReg.test(origin)) {
                            callback(this.$t("domain.detail.tip89"));
                        } else if (reservedIpReg.test(origin)) {
                            callback(this.$t("domain.create.tip16"));
                        } else if (suffixNum === 0 && this.originForm.is_xos === 1) {
                            callback(this.$t("domain.detail.tip102"));
                        } else if (suffixNum > 1 && this.originForm.is_xos === 1) {
                            callback(this.$t("domain.detail.tip103"));
                        } else if (count === 0 && this.originForm.is_xos === 1) {
                            callback(this.$t("domain.detail.tip102"));
                        } else callback();
                    },
                    trigger: ["blur", "change"],
                },
            ],
            role: [{ required: true, message: this.$t("domain.detail.tip74"), trigger: "change" }],
            weight: [
                { required: true, message: this.$t("domain.detail.placeholder20"), trigger: "blur" },
                {
                    validator: (rule: any, value: string, callback: Function) => {
                        if (value === "" || value === null || value === undefined) {
                            callback(this.$t("domain.detail.placeholder20"));
                        } else if (!weightReg.test(value)) {
                            callback(this.$t("domain.create.tip17"));
                        } else callback();
                    },
                    trigger: ["blur", "change"],
                },
            ],
        };
    }
    // 媒体存储源站显示条件
    get showXosOrigin() {
        return (
            (!window.__POWERED_BY_QIANKUN__ &&
                this.useEcgw === 1 &&
                this.backoriginMode === 0 &&
                !this.isBatch &&
                !this.wholeStationProductCode.includes(this.productCode)) ||
            this.wholeStationProductCode.includes(this.productCode) ||
            window.__POWERED_BY_QIANKUN__
        );
    }
    // 对象存储源站展示条件(仅静态类产品支持)
    get showZosOrigin() {
        if (!this.zosOriginSuffix) return false;
        return (isStaticProduct(this.productCode) || this.productCode === "006") && !this.isBatch;
    }
    get getOriginDomains() {
        if (this.from === "create")
            return [...this.originList.map(itm => itm.origin), this.originForm.origin].filter(itm => !!itm);
        else {
            const originList = this.originList.map(itm => itm.origin);
            originList[+this.currenIndex] = this.originForm.origin;
            return originList.filter(itm => !!itm);
        }
    }
    handleBucket() {
        const endPointList = JSON.parse(JSON.stringify(this.endPointList));
        const originList = JSON.parse(JSON.stringify(this.originList));
        const list = [...originList].map(item => item);
        this.temp_bucket = "";
        endPointList.forEach((item: string | any[]) => {
            if (item) {
                list.forEach(ori => {
                    if (ori.origin.endsWith(item) && ori.is_xos === 1) {
                        this.temp_bucket = ori.origin.substring(0, ori.origin.indexOf("."));
                    }
                    this.$emit("bucketHandle", this.temp_bucket);
                });
            }
        });
    }
    private async submit() {
        await formValidate2Promise(this.$refs.originForm as Form);
        this.$emit("submit");
        this.handleBucket();
    }
    private cancel() {
        this.$emit("cancel", "originDialogVisible");
    }
    mounted() {
        this.getXosList();
        this.getXosSuffix();
    }
    handleSelect(item: { bucketName: string; endPoint: string }) {
        this.originForm.origin = `${item.bucketName + "." + item.endPoint}`;
    }
    private async getXosList() {
        let res: any = [];
        res = await this.$ctFetch(xosList);
        const getData = res;
        if (getData) {
            const datas: any = [];
            for (let i = 0; i < getData.length; i++) {
                if (getData[i].bucketName) {
                    datas.push({
                        bucketName: getData[i].bucketName,
                        endPoint: getData[i].endPoint,
                    });
                }
            }
            this.xosOriginList = datas;
        }
    }
    // 获取xos源站后缀，用来校验
    private async getXosSuffix() {
        let res: any = [];
        res = await this.$ctFetch(xosSuffix);
        const getData = res;
        if (getData) {
            const endPointDatas: any = [];
            let endPointStr = "";

            for (let i = 0; i < getData.length; i++) {
                endPointDatas.push(getData[i].suffix);
                // 动态配置后缀
                if (getData[i].suffix) {
                    endPointStr +=
                        getData[i].suffix?.substring(1, getData[i].suffix?.length) +
                        `${i === getData.length - 1 ? "" : this.$t("domain.or")}`;
                    this.endPointStr = endPointStr;
                }
            }
            this.endPointList = endPointDatas;
        }
    }
    querySearch(queryString: any, cb: (arg0: never[]) => void) {
        const restaurants = this.xosOriginList;
        const results = queryString ? restaurants.filter(this.createFilter(queryString)) : restaurants;
        // 调用 callback 返回建议列表的数据
        cb(results);
    }
    createFilter(queryString: string) {
        return (restaurant: { bucketName: string }) => {
            return (
                !!restaurant.bucketName &&
                restaurant.bucketName.toLowerCase().indexOf(queryString.toLowerCase()) === 0
            );
        };
    }
}
</script>

<style lang="scss" scoped>
// .origin-dialog {
// }
::v-deep {
    .el-dialog {
        width: 60%;
    }
}
::v-deep {
    .el-dialog__header {
        border-bottom: 0;
    }
}
::v-deep {
    .my-autocomplete {
        width: 500px !important;
    }
}
.note {
    line-height: 1.5;
    color: $neutral-7;
    font-size: 12px;
    div:nth-child(n + 2) {
        margin-top: -8px;
    }
}
.btn-bg {
    color: $color-master;
    cursor: pointer;
}
.text-content {
    width: 400px !important;
}
.test-origin-wrapper {
    ::v-deep {
        .el-form-item__error {
            line-height: 0.8;
        }
    }
}
</style>
