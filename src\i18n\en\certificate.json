{"title1": "Certificates Management", "title2": "Bind Domain", "title3": "Certificate List", "title4": "History", "title5": "History Versions", "tip": "HTTPS secures content delivery for CDN. It allows clients to browse content on websites more securely and effectively while accessing the content at a high speed. When you bind domain names, only regular certificate configurations are supported. For mutual authentication, please contact after-sales technical support.", "tipA1": "HTTPS secures content delivery. It allows clients to browse content on websites more securely and effectively while accessing the content at a high speed. When you bind domain names, only regular certificate configurations are supported. For mutual authentication, please contact after-sales technical support.", "tip2": "Displays the certificate deployment history, including certificate modification and binding operations. You can view the domain deployment list associated with the certificate.", "addBtn": "Add Self-managed Certificate", "updateBtn": "Update Self-managed Certificate", "certName": "Certificate Alias", "back": "Back", "search": {"label1": "Keyword", "label2": "Created At", "label3": "Deployment Status"}, "placeholder": ["Please select the deployment status", "Enter keywords to search"], "status": {"opt": ["Enabled", "Deploying", "Not Enabled"]}, "keyword": {"opt": ["Certificate Alias", "General Name", "Certificate Brand"]}, "del": {"title": "Certificate Deletion", "tip1": "The current certificate still has the associated domain {domain}, so you cannot delete the certificate. Please delete the certificate after your certificate is unassociated with the domain.", "tip2": "Check whether to continue deleting the certificate"}, "table": {"label": ["Certificate Alias", "General Name", "Certificate Brand", "Issued At", "Expire On", "Created At", "Updated At", "Deployment Status", "Actions", "Certificate Status"], "label5Tip1": "The certificate file has expired, please renew it as soon as possible", "label5Tip2": "The expiration date is within 30 days, please renew it as soon as possible", "operation": ["Details", "Delete", "Update", "Bind Domain"]}, "versionTable": {"label": ["Version", "Authorized Domain", "Issuing Authority", "Issued At", "Expire On", "Operation Type", "Updated At"], "types": ["Add", "Modify", "Delete"]}, "detail": {"title": "Certificate Details", "label": ["Certificate Alias", "General Name", "Created At", "Certificate Brand", "Validity Period", "Bound Domain Names", "New Domain Name", "Removed Domain Name", "Expire In"], "tip1": "The certificate content is not modified. Please verify.", "tip2": "Bound domains include Zero Trust service, AccessOne, CDN acceleration, and other products."}, "message": {"title": "Note", "tip1": "You are updating the <span style='color:#3d73f5'>{certname}</span> certificate. You can check the update progress by choosing Homepage > Information Center > Domain Message Notification. Please check whether to continue", "label": "Associate the Domain"}, "update": {"tip1": "Please upload the certificate (Note: Please pay attention to the validity period of the certificate).", "tip2": "At present, only PEM format is supported, other formats can be converted by yourself or <a target='_blank' href='{orderLink}' style='color:#3d73f5'>submit a ticket</a> to the eSurfing Cloud technical support team to consult the recommended conversion site.", "tip2-1": "At present, only PEM format is supported, other formats can be converted by yourself or ", "tip2-2": "submit a ticket ", "tip2-3": "to the eSurfing Cloud technical support team to consult the recommended conversion site.", "label1": "Certificate Alias", "label1Tip1": "Enter a certificate alias", "label1Tip2": "The specified certificate alias is invalid. The certificate alias can only contain letters, digits, and the following special characters: _-*.()", "label1Tip3": "Cannot exceed 255 characters in length.", "label1Tip4": "The specified certificate alias already exists.", "label2": "Certificate Public Key (PEM)", "label2Tip1": "Enter the public key of the certificate", "label2Tip2": "Cannot exceed 65,535 characters in length.", "label3": "Certificate Private Key (PEM)", "label3Tip1": "Enter the private key of the certificate", "label3Tip2": "The value contains a maximum of 65535 characters"}, "chain": {"title": "System Prompt", "detail": "Details", "reason": "The certificate chain of your added certificate is incomplete, which incurs security risks. If you still need to submit it, please select Continue, and if you want to cancel it, select Cancel.", "label1": "Certificate Information", "label2": "Certificate Type", "label3": "Validation Status", "label4": "Validation Prompt", "ensure": "Continue", "status": ["Verification passed", "Verification failed", "Unknown"], "certTypes": ["Accelerated domain certificate", "Intermediate certificate", "Root certificate"]}, "bindDomain": {"tipAOne": "Only security and acceleration domains can be queried or bound.", "tip": "Bind up to {count} domain name at a time. The filtering range only includes enabled domain names without ongoing tickets.|Bind up to {count} domain names at a time. The filtering range only includes enabled domain names without ongoing tickets.", "canBind": "Domain Names Available for Binding", "selected": "Selected ({selected}/{maxNum})", "bind": "Bind", "bindStatus": "Domain Binding Status", "onGoing": "Domain binding in progress", "result": "Result", "failed": "Binding failed.", "success": "Distributed successfully.", "batchBindTip": "Note: The domain name takes effect after the domain name status becomes enabled.", "selectBind": "Please select the domain to be bound.", "confirmTip": "This operation will turn on the HTTPS switch for the selected domain and bind this certificate. Are you sure you want to continue?", "confirmTip-1": "This action will enable the IP Blocklist/Trustlist for the selected domain name and bind it to this IP set. Continue?", "failedReason": "Reason for Failure", "optionList": "Display All Boundable Domain Names", "noDomainBindList": "Display Unconfigured Certificate Domain Names Only", "noDomainBindList-1": "Show only domains without an IP set", "otherDomainBindList": "Display Configured Certificate Domain Names Only", "otherDomainBindList-1": "Show only domains with an IP set", "selectAll": "Select All", "clearAll": "Clear", "fuzzySearch": "Fuzzy query supported", "selectDomainNum1": "A maximum of {count} domain name can be bound each time.|A maximum of {count} domain names can be bound each time.", "selectDomainNum2": " Selected {count} domain name currently.| Selected {count} domain names currently.", "buttonTip": "Batch bind for security and acceleration domains only."}, "edit": {"title": "Modify Certificate", "parseCert": "Decode Certificate"}, "deleteCert": "Deleted", "text1": "Please enter a certificate alias for easy locating", "text2": "Please enter the Public Key, and the file suffix is .crt (the Public Key file) or .pem (the Certificate file)", "text3": "Please enter the Private Key, and the file suffix is .key (the Private Key file)", "text4": "Before adding a self-managed certificate, prepare the certificate file according to the requirements. Currently, only PEM format is supported. You can convert other formats to the PEM format by yourself, or <a target='_blank' href='{orderLink}' style='color:#3d73f5'>submit a ticket</a> to consult the eSurfing Cloud customer service for recommended conversion sites.", "text5": "Please pay attention to the validity period of the uploaded certificate and ensure that the certificate has a complete certificate chain to avoid security risks.", "text6": "It takes 1 to 2 minutes to add a certificate, please wait patiently.", "incompleteTip": "Certificate has an incomplete chain and may pose a security risk.", "证书类型": "Certificate Type", "国际标准证书": "International Standard Certificate", "国密证书": "SM2 Certificate", "请输入PEM格式的证书公钥内容": "Please enter the PEM-formatted public key", "请输入PEM格式的证书私钥内容": "Please enter the PEM-formatted private key", "签名证书公钥": "Signature Certificate Public Key", "签名证书私钥": "Signature Certificate Private Key", "请输入PEM格式的签名证书公钥内容": "Please enter the PEM-formatted signature public key", "请输入PEM格式的签名证书私钥内容": "Please enter the PEM-formatted signature private key", "加密证书公钥": "Encryption Certificate Public Key", "加密证书私钥": "Encryption Certificate Private Key", "请输入PEM格式的加密证书公钥内容": "Please enter the PEM-formatted encryption public key", "请输入PEM格式的加密证书私钥内容": "Please enter the PEM-formatted encryption private key", "国际标准证书和国密证书至少要有一种有配置": "At least one of International or SM2 Certificate must be configured", "国密证书和quic不能同时开启": "SM2 certificate and QUIC cannot be enabled at the same time.", "国密证书不支持强加密套件": "SM2 certificates do not support strong encryption suites.", "PEM编码参考样例": "PEM编码参考样例"}