<template>
  <el-dialog
    :title="$t('report.create.dialog.title')"
    :close-on-click-modal="false"
    :visible="dialogVisible"
    class="add-dialog"
    append-to-body
    width="600px"
    @close="cancel"
  >
    <el-form
        ref="addForm"
        :model="form"
        label-width="130px"
        :rules="rules"
    >
        <el-form-item :label="$t('report.create.dialog.form[0]')" prop="product">
            <el-select v-model="form.product" @change="acceTypeChange" clearable multiple>
                <el-option
                    v-for="(item, index) in productOptions"
                    :key="index"
                    :label="$t(item.label)"
                    :value="item"
                />
            </el-select>
        </el-form-item>
        <el-form-item :label="$t('report.create.dialog.form[1]')" prop="domain" :rules="isChildAccount ? rules.domain : []">
            <domain-select
                v-model="form.domain"
                class="aocdn-micro-app"
                :domainOptions="domainOptions"
                :multiple="true"
                :loading="domainOptionsLoading"
                @select="domainChange"
            />
        </el-form-item>
        <el-form-item v-if="type === 2" :label="$t('report.create.dialog.form[2]')" prop="region">
            <el-select v-model="form.region">
                <el-option
                    v-for="(item, index) in regionOptions"
                    :key="index"
                    :label="item.label"
                    :value="item.value"
                />
            </el-select>
        </el-form-item>
        <el-form-item v-if="type === 1" :label="$t('report.create.dialog.form[3]')" prop="chart">
            <el-select v-model="form.chart" multiple>
                <el-option
                    v-for="(item, index) in reportDic.chartOptions"
                    :key="index"
                    :label="item.label"
                    :value="item.code"
                />
            </el-select>
        </el-form-item>
        <el-form-item v-else :label="$t('report.create.dialog.form[4]')" prop="data">
            <el-select v-model="form.data" multiple>
                <el-option
                    v-for="(item, index) in dataOptions"
                    :key="index"
                    :label="item.label"
                    :value="item.code"
                />
            </el-select>
        </el-form-item>
    </el-form>
    <div v-loading="loading" slot="footer">
        <el-button @click="cancel">
            {{ $t("common.dialog.cancel") }}
        </el-button>
        <el-button type="primary" @click="confirm">
            {{ $t("common.dialog.submit") }}
        </el-button>
    </div>
  </el-dialog>
</template>

<script lang="ts">
import { Component, Vue, Watch, Prop, Inject } from "vue-property-decorator";
import deepclone from "lodash-es/cloneDeep";
import { Form } from "element-ui";
import { DomainModule } from "@/store/modules/domain";
import { ProductModule } from "@/store/modules/ncdn/nproduct";
import { StatisticsModule } from "@/store/modules/statistics";
import DomainSelect from "@/components/domainsSelect/index.vue";
import { DomainActionEnum, getDomainAction } from "@/config/map";
import { nUserModule } from "@/store/modules/nuser";

@Component({
    name: "addChartOrDataDialog",
    components: { DomainSelect }
})
export default class AddChartOrDataDialog extends Vue {
    @Inject() private reportDic: any;
    @Prop({ default: 0 }) private type: number|undefined
    @Prop({ default: 10 }) private remainLength: number|undefined
    private loading = false
    private dialogVisible = false
    private form: any = {
        product: [],
        domain: [],
        region: 0,
        chart: [],
        data: []
    }
    private rules = {
        product: [{ required: true, message: this.$t("report.create.dialog.validInfo[0]"), trigger: "blur" }],
        chart: [{ required: true, message: this.$t("report.create.dialog.validInfo[1]"), trigger: "blur" }],
        data: [{ required: true, message: this.$t("report.create.dialog.validInfo[2]"), trigger: "blur" }],
        domain: [{ required: true, message: this.$t("common.searchBar.errMsg2"), trigger: "blur" }]
    }
    private regionOptions = [
        { label: this.$t("report.create.dialog.region[0]"), value: 0 },
        { label: this.$t("report.create.dialog.region[1]"), value: 1 },
        { label: this.$t("report.create.dialog.region[2]"), value: 2 }
    ]

    // 过滤后的可选数据列表
    get dataOptions() {
        if (this.form.domain.length) {
            return this.reportDic.dataOptions.filter((option: any) => {
                return option.code !== 2; // 若有选择域名，则隐藏”账单用量“选项
            })
        } else {
            return this.reportDic.dataOptions
        }
    }

    get productOptions() {
        let options: any[] = []
        options = ProductModule.allProductOptions;
        if (window.__POWERED_BY_QIANKUN__) {
            options = StatisticsModule.allProductOptions.map(item => {
                return {
                    label: item.product_cname,
                    value: item.product_code
                }
            })
        }
        return (options as any).filter((option: any) => {
            if(window.__POWERED_BY_QIANKUN__) {
                // 仅支持安全与加速（020）
                return ["020"].includes(option.value)
            } else {
                // 仅支持静态加速（001）、下载加速（003）、视频点播加速（004）、CDN加速（008）、全站加速（006）
                return ["001", "003", "004", "006", "008"].includes(option.value)
            }
        })
    }

    get domainAction() {
        if (nUserModule.isFcdnCtyunCtclouds) {
            return getDomainAction("Report");
        }
        return DomainActionEnum.Data;
    }

    get domainOptionsLoading() {
        return DomainModule[this.domainAction].loading;
    }

    // 全部的域名列表
    get domainList() {
        this.productOptions.forEach((product: any) => {
            product.domains = [];
        });
        return DomainModule[this.domainAction].nativeList.filter(domain => {
            return this.productOptions.some((product: any) => {
                if (product.value === domain.productCode) {
                    product.domains.push(domain.domain);
                    return true;
                }
                return false
            });
        });
    }

    // 根据加速类型筛选展示域名列表
    get domainOptions() {
        return (
            this.domainList
                // 过滤逻辑：如果产品未选择，则默认所有域名；若产品有选择，则根据产品进行过滤
                .filter(item => {
                    return this.form.product.length ? (this.form.product.some((acceTypeObj: any) => acceTypeObj.value === item.productCode)) : true;
                })
                .map(item => ({
                    // 从接口原生数据中获取 options
                    label: item.label,
                    value: item.domain
                }))
        );
    }

    /**
     * 是否是子账号
     */
    get isChildAccount() {
        return StatisticsModule.childAccount;
    }

    @Watch("dialogVisible")
    private dialogVisibleChange(val: boolean) {
        if (val) {
            this.form = {
                product: [],
                domain: [],
                region: 0,
                chart: [],
                data: []
            };
            (this.$refs.addForm as Form)?.clearValidate()
            nUserModule.isFcdnCtyunCtclouds && DomainModule.GetDomainList({ action: this.domainAction, shouldCheckCache: true });
        }
    }

    private acceTypeChange() {
        this.form.domain = []
    }

    private domainChange(domains: string[]) {
        if (domains.length) {
            // 若有选择域名，则去除”账单用量“选项
            this.form.data = this.form.data.filter((item: any) => item !== 2)
        }
    }

    private cancel() {
        this.dialogVisible = false
    }

    private confirm() {
        (this.$refs.addForm as Form)?.validate((valid: boolean) => {
            if (valid) {
                const data: any[] = [];
                this.form.product.forEach((product: any) => {
                    const domians = this.form.domain.filter((domain: string) => product.domains.includes(domain));
                    // pdf
                    if (this.type === 1) {
                        if (!domians.length || domians.length === product.domains.length) {
                            data.push({
                                productCode: product.value,
                                domain: this.isChildAccount ? deepclone(domians) : [],
                                region: this.form.region,
                                chart: deepclone(this.form.chart),
                                data: deepclone(this.form.data)
                            })
                        } else {
                            domians.forEach((domain: any) => {
                               data.push({
                                    productCode: product.value,
                                    domain: [domain],
                                    region: this.form.region,
                                    chart: deepclone(this.form.chart),
                                    data: deepclone(this.form.data)
                                })
                            })
                        }
                    }
                    // excel
                    if (this.type === 2) {
                        data.push({
                            productCode: product.value,
                            domain: domians.length === product.domains.length && !this.isChildAccount ? [] : deepclone(domians),
                            region: this.form.region,
                            chart: deepclone(this.form.chart),
                            data: deepclone(this.form.data)
                        })
                    }
                });
                if (data.length > (this.remainLength || 10)) {
                    this.$message.warning(this.$t("report.create.dialog.warning") as string)
                } else {
                    this.$emit("add", data)
                    this.dialogVisible = false
                }
            }
        });
    }
}
</script>

<style lang="scss" scoped>
.add-dialog {
    ::v-deep .el-form-item__content {
        & > .el-input,
        & > .el-select {
            width: 400px;
        }
    }
}
</style>
