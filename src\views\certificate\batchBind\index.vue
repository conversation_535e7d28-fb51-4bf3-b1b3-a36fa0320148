<template>
    <!-- 绑定域名，证书、IP集两个模块复用 -->
    <ct-section-wrap>
        <template #header>
            <ct-section-header :title="getName"/>
        </template>
        <ct-box class="table-scroll-wrap">
            <el-alert
                type="info"
                class="mgb20"
                :closable="false"
            >
                <template v-slot:title>{{ $t("billing.confirmTip.title") }}</template>
                <template>
                    <div v-if="isAOne">{{ $t("certificate.bindDomain.tipAOne")}}</div>
                    <div>{{ $tc('certificate.bindDomain.tip', maxBindDomainNum) }}</div>
                </template>
            </el-alert>
            <div class="filter-bar">
                <div class="filter-bar-title">{{ `${$t("log.tip2")}` }}</div>
                <div class="filter-bar-radio-wrapper">
                    <el-radio-group v-model="currentShowType" class="filter-bar-radio-group">
                        <el-radio label="optionList">{{ $t("certificate.bindDomain.optionList") }}</el-radio>
                        <el-radio label="noDomainBindList">
                            {{
                                isCertificate
                                    ? $t("certificate.bindDomain.noDomainBindList")
                                    : $t("certificate.bindDomain.noDomainBindList-1")
                            }}
                        </el-radio>
                        <el-radio label="otherDomainBindList">
                            {{
                                isCertificate
                                    ? $t("certificate.bindDomain.otherDomainBindList")
                                    : $t("certificate.bindDomain.otherDomainBindList-1")
                            }}
                        </el-radio>
                    </el-radio-group>
                </div>
            </div>
            <div class="bind-domain-box">
                <div class="left-table">
                    <el-table
                        :empty-text="$t('common.table.empty')"
                        :data="leftTableData"
                        v-loading="loading"
                        @select="handleSelectionChange"
                        @select-all="handleSelectAllCurrentPage"
                        row-key="domain"
                        ref="leftTable"
                        :span-method="data => spanMethod(2, data)"
                    >
                        <el-table-column
                            type="selection"
                            :reserve-selection="true"
                            width="55"
                            :selectable="selectHandle"
                        ></el-table-column>
                        <el-table-column
                            :label="$t('common.table.index')"
                            type="index"
                            :index="indexMethod"
                            width="100"
                        ></el-table-column>
                        <el-table-column
                            :label="$t('certificate.bindDomain.canBind')"
                            prop="domain"
                            :min-width="isEn ? '180' : '100'"
                        ></el-table-column>
                        <el-table-column min-width="200">
                            <template slot="header">
                                <div style="display: flex; gap: 8px">
                                    <div class="el-input el-input--mini el-input--suffix">
                                        <input
                                            class="el-input__inner"
                                            v-model="tableFilterLeft"
                                            size="mini"
                                            :placeholder="$t('certificate.bindDomain.fuzzySearch')"
                                            autocomplete="off"
                                            autocorrect="off"
                                        />
                                    </div>
                                    <el-button type="text" size="mini" @click="selectAllDomainInOptionList">
                                        {{ $t("certificate.bindDomain.selectAll") }}
                                    </el-button>
                                </div>
                            </template>
                        </el-table-column>
                    </el-table>
                    <div class="pager">
                        <el-pagination
                            small
                            layout="total, sizes, prev, pager, next, jumper"
                            :total="totalRecord"
                            :current-page.sync="currentPage"
                            :page-size="pagesize"
                            :page-sizes="[10, 30, 50, 100]"
                            :hide-on-single-page="false"
                            @current-change="pageIndexChange"
                            @size-change="pageSizeChange"
                        ></el-pagination>
                    </div>
                </div>
                <div class="arrow">
                    <i class="el-icon-right"></i>
                </div>
                <div class="right-table">
                    <el-table
                        :empty-text="$t('common.table.empty')"
                        :data="getMultipleSelection"
                        :span-method="data => spanMethod(1, data)"
                    >
                        <el-table-column
                            :label="$t('common.table.index')"
                            type="index"
                            width="100"
                        ></el-table-column>
                        <el-table-column
                            :label="
                                $t('certificate.bindDomain.selected', {
                                    selected: multipleSelection.length,
                                    maxNum: maxBindDomainNum,
                                })
                            "
                            prop="domain"
                            min-width="120"
                        >
                            <template slot-scope="{ row }">
                                <div class="right-table-domain">
                                    <span>{{ row.domain }}</span>
                                    <el-button
                                        icon="close"
                                        @click="handleUnselect(row)"
                                        :border="false"
                                        circle
                                        size="mini"
                                    ></el-button>
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column min-width="200">
                            <template slot="header">
                                <div style="display: flex; gap: 8px">
                                    <div class="el-input el-input--mini el-input--suffix">
                                        <input
                                            class="el-input__inner"
                                            v-model="tableFilterRight"
                                            size="mini"
                                            :placeholder="$t('certificate.bindDomain.fuzzySearch')"
                                            autocomplete="off"
                                            autocorrect="off"
                                        />
                                    </div>
                                    <el-button type="text" size="mini" @click="handleClearSelection">{{
                                        $t("certificate.bindDomain.clearAll")
                                    }}</el-button>
                                </div>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
            </div>
            <div class="bind-domain-footer">
                <el-button size="medium" @click="goBack">{{ $t("domain.back") }}</el-button>
                <el-button type="primary" size="medium" @click="batchBindSubmit">{{
                    $t("certificate.bindDomain.bind")
                }}</el-button>
            </div>

            <el-dialog
                :title="$t('certificate.bindDomain.bindStatus')"
                :visible.sync="batchBindDialogVisible"
                :modal-append-to-body="false"
                :close-on-click-modal="false"
                width="1000px"
                @close="onClose"
            >
                <el-table
                    :empty-text="$t('common.table.empty')"
                    :data="batchAssociateList"
                    v-loading="batchBindDialogLoading"
                    :element-loading-text="$t('certificate.bindDomain.onGoing')"
                    element-loading-spinner="el-icon-loading"
                >
                    <el-table-column
                        :label="$t('certificate.title2')"
                        prop="domain"
                        width="200"
                    ></el-table-column>
                    <el-table-column :label="$t('certificate.bindDomain.result')" prop="result" width="180">
                        <template #default="{ row }">
                            <cute-state :type="row.reason ? 'danger' : 'success'">
                                {{
                                    row.reason
                                        ? $t("certificate.bindDomain.failed")
                                        : $t("certificate.bindDomain.success")
                                }}
                            </cute-state>
                        </template>
                    </el-table-column>
                    <el-table-column :label="$t('certificate.bindDomain.failedReason')" prop="reason">
                        <template #default="{ row }">
                            <cute-state v-show="row.reason" type="danger" class="cute-state-icon-wrapper">
                                {{ row.reason }}
                            </cute-state>
                        </template>
                    </el-table-column>
                </el-table>
                <p class="batchBindTip">
                    {{ $t("certificate.bindDomain.batchBindTip") }}
                </p>
                <template #footer>
                    <el-button type="primary" @click="batchBindDialogVisible = false">{{
                        $t("common.dialog.submit")
                    }}</el-button>
                </template>
            </el-dialog>
        </ct-box>
    </ct-section-wrap>
</template>

<script lang="ts">
import { Component, Prop, Ref, Vue, Watch } from "vue-property-decorator";
import { CertificateUrl } from "@/config/url/certificate";
import { IpSetUrl } from "@/config/url/ipSet";
import { ElTable } from "element-ui/types/table";
import { getLang } from "@/utils";
import { CtiamCode } from "@/utils/ctFetch/errorConfig";

type SourceType = "certificate" | "ipset";

interface ApiFieldMap {
    optionList: string;
    noDomainBindList: string;
    otherDomainBindList: string;
    batchLinkDomainNum: string;
}

const API_FIELD_MAP: Record<SourceType, ApiFieldMap> = {
    certificate: {
        optionList: "optionList",
        noDomainBindList: "noCertDomainList",
        otherDomainBindList: "linkCertDomainList",
        batchLinkDomainNum: "batchLinkCertDomainNum",
    },
    ipset: {
        optionList: "domainList",
        noDomainBindList: "noBindDomainList",
        otherDomainBindList: "bindDomainList",
        batchLinkDomainNum: "batchLinkIpSetDomainNum",
    },
};

type failedDomainItem = {
    reason: string;
    domain: string;
};
type rowItem = {
    domain: string;
};

@Component
export default class CertificateBatchBind extends Vue {
    // protected searchUrl = this.isCertificate ? CertificateUrl.listOption : IpSetUrl.listOption;
    private multipleSelection: rowItem[] = [];
    private loading = false;
    private batchBindDialogVisible = false;
    private batchBindDialogLoading = false;
    private optionList: rowItem[] = [];
    private noDomainBindList: rowItem[] = []; // 未绑定域名的证书、IP集
    private otherDomainBindList: rowItem[] = []; // 绑定了其他证书、IP集的域名
    private batchLinkCertDomainNum = 100; // 最多可绑定的域名数量
    private batchLinkIpSetDomainNum = 100; // IP集最多可绑定的域名数量
    private batchAssociateList: rowItem[] = [];
    // private dataList: object[] = [];
    currentPage = 1;
    pagesize = 10;
    private currentShowType: "noDomainBindList" | "otherDomainBindList" | "optionList" = "optionList";
    private tableFilterLeft = "";
    private tableFilterRight = "";
    private leftTableData: rowItem[] = [];

    @Ref("leftTable") readonly leftTableRef!: ElTable;

    // AOne环境
    get isAOne() {
        return window.__POWERED_BY_QIANKUN__;
    }

    get isEn() {
        return getLang() === "en";
    }

    get getName() {
        return this.$route.query.name;
    }

    get forbid_type() {
        return this.$route.query.forbid_type;
    }

    get sourceId() {
        return this.$route.query.id;
    }

    get sourceType(): SourceType {
        return (this.$route.query.sourceType as SourceType) || "certificate";
    }

    // 判断是否为证书类型
    get isCertificate() {
        return this.sourceType === "certificate";
    }

    // 获取最大可绑定域名数量
    get maxBindDomainNum() {
        return this.isCertificate ? this.batchLinkCertDomainNum : this.batchLinkIpSetDomainNum;
    }

    // 获取当前来源的 API 配置
    get apiConfig() {
        return this.isCertificate
            ? {
                  listOption: CertificateUrl.listOption,
                  batchBind: CertificateUrl.batchAssociate,
              }
            : {
                  listOption: IpSetUrl.listOption,
                  batchBind: IpSetUrl.batchAssociate,
              };
    }
    get filteredList() {
        // 返回模糊匹配后的列表数据
        return this[this.currentShowType].filter((item: rowItem) => {
            return item.domain.toLowerCase().indexOf(this.tableFilterLeft.toLowerCase()) !== -1;
        });
    }
    get getDataList() {
        return this.filteredList.slice(
            (this.currentPage - 1) * this.pagesize,
            this.currentPage * this.pagesize
        );
    }
    get getMultipleSelection() {
        return this.multipleSelection.filter((item: rowItem) => {
            return item.domain.toLowerCase().indexOf(this.tableFilterRight.toLowerCase()) !== -1;
        });
    }
    get totalRecord() {
        return this.filteredList.length;
    }

    @Watch("getDataList")
    onListChange() {
        this.leftTableData = [...this.getDataList];
    }

    created() {
        this.init();
    }
    private handleSelectionChange(_val: [], row: rowItem) {
        const idx = this.multipleSelection.findIndex((item: rowItem) => item.domain === row.domain);
        idx === -1 ? this.multipleSelection.push(row) : this.multipleSelection.splice(idx, 1);
    }
    private handleSelectAllCurrentPage(data: rowItem[]) {
        // data是当前页的所有数据+已选中的数据，但是还未存到multipleSelection中
        // multipleSelection是右侧表格中已选中的数据
        // 通过当前的页码得出当前页的数据
        const currentPageData = this.filteredList.slice(
            (this.currentPage - 1) * this.pagesize,
            this.currentPage * this.pagesize
        );
        // 如果data中不包含当前页的数据，说明是取消全选，需要从multipleSelection中剔除
        if (currentPageData.every(row => !data.map(itm => itm.domain).includes(row.domain))) {
            this.multipleSelection = this.multipleSelection.filter(
                (item: rowItem) => !currentPageData.map(itm => itm.domain).includes(item.domain)
            );
            // 将本页的选中状态都取消
            currentPageData.forEach((item: rowItem) => {
                this.leftTableRef.toggleRowSelection(item, false);
            });
            return;
        }
        // 先获取这一页中还未选中的数据
        const unSelectedData = currentPageData.filter((item: rowItem) => {
            return this.multipleSelection.findIndex((item2: rowItem) => item.domain === item2.domain) === -1;
        });

        // 计算当前剩余可选数量
        const unSelectedCount = this.maxBindDomainNum - this.multipleSelection.length;

        // 从未选的数据中选项选择域名
        // 选中的数量不能超过剩余可选数量，取剩余可选数和未选数据的最小值
        const addedCount = Math.min(unSelectedCount, unSelectedData.length);
        unSelectedData.slice(0, addedCount).forEach(row => {
            this.multipleSelection.push(row);
        });

        // 判断是否需要提示
        if (unSelectedCount < unSelectedData.length) {
            this.$message.warning(
                (this.$tc("certificate.bindDomain.selectDomainNum1", this.maxBindDomainNum) as string) +
                    (this.$tc("certificate.bindDomain.selectDomainNum2", addedCount) as string)
            );
        }

        // 表格选中状态同步
        // 先将本页的选中状态都取消
        currentPageData.forEach((item: rowItem) => {
            this.leftTableRef.toggleRowSelection(item, false);
        });
        // 手动反转选中状态
        currentPageData.forEach(row => {
            if (!this.multipleSelection.map((item: rowItem) => item.domain).includes(row.domain)) {
                return;
            }
            this.leftTableRef.toggleRowSelection(row, true);
        });
    }
    private selectHandle(row: rowItem) {
        const multipleDomainList = this.multipleSelection.map((item: rowItem) => item.domain);
        // 不可选的几种情况：1、绑定个数已达上线，且当前选择的域名不是选中的域名
        // 2、当前选择的域名不是optionList中的域名
        if (
            multipleDomainList.length > this.maxBindDomainNum - 1 &&
            !multipleDomainList.includes(row.domain)
        ) {
            return false;
        }
        if (!this.optionList.map((item: rowItem) => item.domain).includes(row.domain)) {
            return false;
        }
        return true;
    }
    pageIndexChange(val: number) {
        this.currentPage = val;
    }
    pageSizeChange(val: number) {
        this.pagesize = val;
    }
    // 全选当前filltedList中的域名
    selectAllDomainInOptionList() {
        if (this.maxBindDomainNum < 1) return;
        // 可选择的域名：optionList与filltedList的交集
        const canSelectList = this.optionList.filter((item: rowItem) => {
            return this.filteredList.map((item2: rowItem) => item2.domain).includes(item.domain);
        });
        // notInCanSelectList就是本次点击全选可选中的域名
        const notInCanSelectList = canSelectList.filter((item: rowItem) => {
            return !this.multipleSelection.map((item2: rowItem) => item2.domain).includes(item.domain);
        });
        const previousSelectedCount = this.multipleSelection.length;
        this.multipleSelection = [
            ...this.multipleSelection,
            ...notInCanSelectList.slice(0, this.maxBindDomainNum - this.multipleSelection.length),
        ];
        if (this.maxBindDomainNum - previousSelectedCount <= notInCanSelectList.length - 1) {
            this.$message.warning(
                (this.$tc("certificate.bindDomain.selectDomainNum1", this.maxBindDomainNum) as string) +
                    (this.$tc(
                        "certificate.bindDomain.selectDomainNum2",
                        this.maxBindDomainNum - previousSelectedCount
                    ) as string)
            );
        }
        this.$nextTick(() => {
            this.manualSelectRow(this.multipleSelection);
        });
    }
    // 清空选中的域名
    handleClearSelection() {
        this.multipleSelection = [];
        this.leftTableRef.clearSelection();
    }
    // 合并列
    spanMethod(lastIndex: number, { columnIndex }: { columnIndex: number }) {
        if (columnIndex < lastIndex) {
            return {
                rowspan: 1,
                colspan: 1,
            };
        } else if (columnIndex === lastIndex) {
            return {
                rowspan: 1,
                colspan: 2,
            };
        } else {
            return {
                rowspan: 0,
                colspan: 0,
            };
        }
    }
    manualSelectRow(rows: rowItem[]) {
        rows.forEach(row => this.leftTableRef.toggleRowSelection(row, true));
        // 手动触发 el-table 中的 setData
        // 使用 computed 的 getDataList 作为表格的数据 会导致在不同过滤条件点击表格右侧全选时，选中状态不会更新
        // https://github.com/ElemeFE/element/issues/20343
        this.leftTableData = [...this.leftTableData];
    }
    indexMethod(index: number) {
        // 返回每页的序号
        return this.currentPage * this.pagesize - (this.pagesize - index - 1);
    }
    async init() {
        if (this.isCertificate && !this.getName) return;

        this.loading = true;
        try {
            interface ApiResponse {
                [key: string]: string | number | undefined;
                optionList?: string;
                noCertDomainList?: string;
                linkCertDomainList?: string;
                domainList?: string;
                noBindDomainList?: string;
                bindDomainList?: string;
                batchLinkCertDomainNum?: number;
                batchLinkIpSetDomainNum?: number;
            }

            const params: any = {};

            if (this.isCertificate) {
                params.certName = this.getName;
            }

            const res = await this.$ctFetch<ApiResponse>(this.apiConfig.listOption, {
                data: params,
            });

            const fieldMap = API_FIELD_MAP[this.sourceType];
            const destructDomainStr = (arrStr: string | undefined) =>
                arrStr?.split(",")?.map(item => ({ domain: item })) || [];

            const optionListValue = res[fieldMap.optionList];
            const noDomainBindListValue = res[fieldMap.noDomainBindList];
            const otherDomainBindListValue = res[fieldMap.otherDomainBindList];

            if (typeof optionListValue === "string" && optionListValue.length > 0) {
                this.optionList = destructDomainStr(optionListValue);
            }
            if (typeof noDomainBindListValue === "string" && noDomainBindListValue.length > 0) {
                this.noDomainBindList = destructDomainStr(noDomainBindListValue);
            }
            if (typeof otherDomainBindListValue === "string" && otherDomainBindListValue.length > 0) {
                this.otherDomainBindList = destructDomainStr(otherDomainBindListValue);
            }

            // 根据sourceType设置不同的最大绑定数量
            if (this.sourceType === "certificate") {
                this.batchLinkCertDomainNum =
                    typeof res.batchLinkCertDomainNum === "number" ? res.batchLinkCertDomainNum : 100;
            } else {
                this.batchLinkIpSetDomainNum =
                    typeof res.batchLinkIpSetDomainNum === "number" ? res.batchLinkIpSetDomainNum : 100;
            }
        } catch (err) {
            this.$errorHandler(err);
        } finally {
            this.loading = false;
        }
    }
    async batchBindSubmit() {
        const { multipleSelection } = this;
        if (!multipleSelection.length) {
            return this.$message.warning(this.$t("certificate.bindDomain.selectBind") as string);
        }
        await this.$confirm(
            `<p style='text-align:left;'>${this.$t(
                this.isCertificate
                    ? "certificate.bindDomain.confirmTip"
                    : "certificate.bindDomain.confirmTip-1"
            )}</p>`,
            this.$t("certificate.message.title") as string,
            {
                confirmButtonText: this.$t("common.dialog.submit") as string,
                cancelButtonText: this.$t("common.dialog.cancel") as string,
                dangerouslyUseHTMLString: true,
                center: true,
                type: "warning",
            }
        );
        try {
            this.batchBindDialogVisible = true;
            this.batchBindDialogLoading = true;
            const domainList = multipleSelection.map((item: any) => item.domain);

            // 根据不同的sourceType构造不同的请求参数
            const requestData = this.isCertificate
                ? {
                      certName: this.getName,
                      domainList,
                  }
                : {
                      list: domainList,
                      ip_set_forbid: {
                          alias_name: this.getName,
                          switch: 1,
                          forbid_type: this.forbid_type,
                      },
                  };

            const res = await this.$ctFetch<{ failedDomainList: failedDomainItem[] }>(
                this.apiConfig.batchBind,
                {
                    method: "POST",
                    data: requestData,
                    headers: {
                        "Content-Type": "application/json",
                    },
                }
            );

            const successDomainList = multipleSelection.filter((item: any) => {
                if (
                    res.failedDomainList.every(
                        (failedItem: failedDomainItem) => item.domain !== failedItem.domain
                    )
                ) {
                    return item;
                }
            });
            this.batchAssociateList = res.failedDomainList.concat(
                ...(successDomainList as failedDomainItem[])
            );
        } catch (err) {
            const isCtiamError = CtiamCode.includes((err as any)?.data?.code);
            if (isCtiamError) {
                this.$errorHandler(err);
            }
            this.batchAssociateList = this.multipleSelection.map(item =>
                Object.assign({}, item, {
                    reason: isCtiamError ? (err as any).data.reason : (err as any).reason,
                })
            );
        } finally {
            this.batchBindDialogLoading = false;
        }
    }
    private handleUnselect(row: rowItem) {
        this.handleSelectionChange([], row);
        this.leftTableRef.toggleRowSelection(row, false);
    }
    private onClose() {
        this.$router.push({
            name: this.isCertificate ? "certificate.list" : "ipset.list",
        });
    }
    private goBack() {
        this.$router.go(-1);
    }
}
</script>

<style lang="scss" scoped>
.table-scroll-wrap {
    height: 100%;
    overflow: hidden;

    .is-alert-tip, .filter-bar, .bind-domain-footer,{
        flex-shrink: 0;
    }

    .bind-domain-box {
        flex: 1;
        overflow: hidden;
    }
}
.bind-domain-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    //height: calc(100% - 95px);
    .left-table,
    .right-table {
        width: 48%;
        height: 90%;
        ::v-deep .el-table {
            height: calc(100% - 40px);
            margin: 0;
        }
        &-domain {
            display: flex;
            justify-content: space-between;
        }
        ::v-deep .el-table {
            border: 0 !important;
        }
        ::v-deep .el-table th.el-table__cell.is-leaf {
            border-right: 0 !important;
        }
        ::v-deep .el-table td.el-table__cell,
        .aocdn-micro-app .el-table th.el-table__cell.is-leaf {
            border-right: none;
        }
        ::v-deep .el-table--border-full::after {
            width: 0 !important;
        }
    }
    .right-table {
        ::v-deep .el-table__header-wrapper .el-checkbox {
            display: none;
        }
    }
}
.filter-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 24px;
    &-title {
        align-self: flex-start;
        flex-grow: 0;
    }
    &-radio-wrapper {
        flex-grow: 1;
        justify-self: flex-start;
    }
    &-radio-group {
        display: grid;
        justify-self: start;
        grid-template: repeat(3, 1fr) / 1fr;
        gap: 12px;
    }
}
.batchBindTip {
    margin-top: 8px;
    color: #7c818c;
}
.pager {
    margin-top: 12px;
    text-align: right;
}
.arrow {
    color: #666;
    font-size: 24px;
}
.bind-domain-footer {
    display: flex;
    justify-content: center;
    align-items: center;
}
::v-deep .el-dialog .el-dialog__body {
    padding-bottom: 8px;
}

.cute-state-icon-wrapper {
    ::v-deep {
        .dot {
            flex-shrink: 0;
        }
    }
}
</style>
