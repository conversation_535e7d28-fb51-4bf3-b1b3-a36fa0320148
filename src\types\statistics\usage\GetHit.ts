/*
 * @Description: 统计分析 GetHit 接口类型注解，涉及页面：统计分析-命中率
 * @Author: wang yuegong
 */

// GetHit 接口数据中按时间的统计数据（用于命中率 tab）
export interface HitCnt {
    timestamp: number;
    reqCnt: number; // 请求数
    hitReqCnt: number; // 请求命中数
    hitPer: number; // 请求命中率
    flow: number; // 流量
    hitFlow: number; // 流量命中数
    hitFlowPer: number; // 流量命中率
}

// GetHit 接口数据（用于命中率 tab）
export interface HitFetchData {
    hitcnt: HitCnt[];
    topFlowHit: number; // 流量命中率峰值
    topRequestHit: number; // 请求命中率峰值
    flowTime: number; // 流量命中率峰值时间（时间戳秒）
    requestTime: number; // 请求命中率峰值时间（时间戳秒）
}
