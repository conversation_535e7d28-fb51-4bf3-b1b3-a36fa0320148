import { Vue, Component, Ref } from "vue-property-decorator";
import ctAnchor from "@/components/ctAnchor/index.vue";

@Component({
    name: "AnchorMixin",
    components: {
        ctAnchor,
    },
})
export default class AnchorMixin extends Vue {
    @Ref("anchor") anchorRef!: Vue;
    containerToTop = 0;
    anchorChildKey: string | null = null;
    basicAnchorList: { label: string; prop: string }[] = [];
    anchorSelector = {
        contentArea: "",
        viewArea: "",
        emptyBlock: "",
    }

    updated() {
        // 更新 anchorHelperBlock 的高度
        this.updateAnchorHeight();
    }

    updateAnchorChild(val: string) {
        this.anchorChildKey = val;
    }

    updateAnchorHeight() {
        this.$nextTick(() => {
            const lastLabelDom = document.querySelector(
                this.basicAnchorList[this.basicAnchorList.length - 1]?.prop
            );
            const contentAreaHeight = (document.querySelector(this.anchorSelector.contentArea) as HTMLElement)?.offsetHeight;
            const viewAreaHeight = (document.querySelector(this.anchorSelector.viewArea) as HTMLElement)?.offsetHeight;
            const emptyBlock = document.querySelector(this.anchorSelector.emptyBlock);

            const height =
                viewAreaHeight -
                (contentAreaHeight - (lastLabelDom as HTMLElement)?.offsetTop) +
                (emptyBlock as HTMLElement)?.offsetHeight;
            // 浏览器前进后退导致emptyBlock未渲染时js报错
            if (emptyBlock) {
                (emptyBlock as HTMLElement).style.height = `${height < 0 ? 0 : height}px`;
            }
        });
    }
    handleScroller(e: any) {
        const ele = e.target;
        const scrollTop = ele.scrollTop; // 容器滚动的距离
        const anchor = this.anchorRef.$el as HTMLElement;
        // const positionInfo = anchor.getBoundingClientRect();
        // 当前锚点距离窗口的距离
        // const left = get(positionInfo, "left", 0);
        // 锚点动态样式
        if (scrollTop >= this.containerToTop && this.containerToTop > 0) {
            anchor.style.position = "fixed";
            // anchor.style.top = this.containerToTop + "px";
            anchor.style.top = window.__POWERED_BY_QIANKUN__
                ? `${this.containerToTop + 5}px`
                : `${this.containerToTop + 60}px`;
            // anchor.style.left = left + "px";
            anchor.style.right = "30px";
            return;
        }

        anchor.style.position = "relative";
        anchor.style.top = "0px";
        anchor.style.right = "5px";
    }
}
