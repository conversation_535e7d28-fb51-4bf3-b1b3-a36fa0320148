<template>
    <section>
        <div class="search-bar">
            <el-select
                v-model="packageType"
                multiple
                collapse-tags
                size="medium"
                :placeholder="$t('billing.productTable.column1')"
                @change="handlePackageChange"
            >
                <el-option
                    :label="$t('billing.resourcePackage.optionLabel')"
                    value="packageTypeAll"
                ></el-option>
                <el-option
                    v-for="opt in productOptions"
                    :key="opt.value"
                    :label="$t(`${opt.label}`)"
                    :value="opt.value"
                />
            </el-select>
            <el-select
                v-model="status"
                multiple
                collapse-tags
                size="medium"
                :placeholder="$t('billing.common.itm4')"
                @change="handleStatusChange"
            >
                <el-option :label="$t('billing.resourcePackage.allStatus')" value="statusAll"></el-option>
                <el-option
                    v-for="opt in statusOptions"
                    :key="opt.value"
                    :label="opt.label"
                    :value="opt.value"
                />
            </el-select>

            <el-button size="medium" @click="beforeRefresh"> {{ $t("billing.common.itm5") }} </el-button>

            <div class="search-btns" v-if="!isBcp">
                <el-button v-for="o in operationList" :key="o.title" @click="open(o)" type="ct" size="medium">
                    {{ o.title }}
                </el-button>
            </div>
        </div>

        <el-table
            :data="dataList"
            v-loading="loading"
            @sort-change="sortTable"
            :empty-text="$t('common.table.empty')"
            class="resource-package"
        >
            <el-table-column prop="index" :label="$t('billing.common.itm6')" width="80" />
            <el-table-column
                :label="$t('billing.resourcePackage.package')"
                min-width="120"
                prop="resource_packet_name"
            />
            <el-table-column :label="$t('billing.resourcePackage.usage')" min-width="200">
                <template slot-scope="{ row }">
                    <div>
                        {{ $t("billing.resourcePackage.already")
                        }}{{ $tc(PacketUnitMap[row.resource_type], row.used_num) }}({{
                            $t("billing.resourcePackage.total")
                        }}{{ $tc(PacketUnitMap[row.resource_type], row.packet_size) }})
                    </div>
                    <el-slider
                        :format-tooltip="formatTooltip"
                        :class="[sliderStatus(row), 'packet-slider']"
                        :disabled="true"
                        :value="packagePercent(row)"
                    />
                </template>
            </el-table-column>
            <el-table-column
                prop="start_time"
                min-width="150"
                :label="$t('billing.productTable.column3')"
                sortable="custom"
            />
            <el-table-column
                prop="end_time"
                min-width="150"
                :label="$t('billing.productTable.column4')"
                sortable="custom"
            />
            <el-table-column
                :label="$t('billing.common.itm4')"
                min-width="100"
                :formatter="formatterStatus"
            />
        </el-table>

        <ct-pager class="pager" :refresh.sync="refresh" :loadData="search" />
    </section>
</template>

<script lang="ts">
import { Component, Mixins } from "vue-property-decorator";
import { ProductModule } from "@/store/modules/ncdn/nproduct"; // cdn独有
import ResourcePackageMixin from "./index";
import { ProductCodeEnum, PackageStatusMap } from "@/config/map";
import { nUserModule } from "@/store/modules/nuser"; // 已合并

@Component({
    name: "ResourcePackage",
})
export default class ResourcePackage extends Mixins(ResourcePackageMixin) {
    private packageType: string[] = [];
    private status: string[] = ["2"];
    private isPackageTypeAllChange = false;
    private isStatusAllChange = false;

    get productOptions() {
        //二级产品过滤掉
        const productCodeArr: string[] = Object.values(ProductCodeEnum);
        const list = ProductModule.productOptions.filter(item => {
            return (
                item.value !== ProductCodeEnum.Socket &&
                item.value !== ProductCodeEnum.Upload &&
                item.value !== ProductCodeEnum.DownloadIdlesse &&
                productCodeArr.includes(item.value)
            );
        });
        return list;
    }
    get isBcp() {
        return nUserModule.isBcp;
    }
    get statusOptions() {
        const statusArr: string[] = Object.values(PackageStatusMap).map(
            (_, idx) => this.$t(`billing.packageStatusOptions.itm${idx + 1}`) as string
        );
        const statusList = statusArr.map((item, index) => ({
            label:
                item === this.$t("billing.resourcePackage.activate")
                    ? this.$t("billing.resourcePackage.used")
                    : item,
            value: String(index + 1),
        }));
        return statusList;
    }

    get searchParams() {
        return {
            //必传字段 拉去全部 本地删选
            productCode:
                !this.packageType.length || this.packageType.includes("packageTypeAll")
                    ? ""
                    : this.packageType.join(","),
            status: !this.status.length
                ? this.statusOptions.map(item => item.value).join(",")
                : this.status.filter(item => item !== "statusAll").join(","),
            // 增加生效时间、到期时间排序字段
            ...this.sortFlag,
        };
    }

    mounted() {
        this.getOperation("cdn.package.buy");
    }

    handlePackageChange(value: string[]) {
        if (this.isPackageTypeAllChange) {
            this.isPackageTypeAllChange = false;
            this.packageType = value.includes("packageTypeAll")
                ? value.filter(item => item !== "packageTypeAll")
                : [];
        } else {
            const alloption = ["packageTypeAll", ...this.productOptions.map(item => item.value)];
            this.packageType = value.includes("packageTypeAll")
                ? alloption
                : value.length === this.productOptions.length
                ? alloption
                : value;
            this.isPackageTypeAllChange =
                value.includes("packageTypeAll") || value.length === this.productOptions.length;
        }
        this.refresh = true;
    }
    handleStatusChange(value: string[]) {
        if (this.isStatusAllChange) {
            this.isStatusAllChange = false;
            this.status = value.includes("statusAll") ? value.filter(item => item !== "statusAll") : [];
        } else {
            const statusAllOptions = ["statusAll", ...this.statusOptions.map(item => item.value)];
            this.status = value.includes("statusAll")
                ? statusAllOptions
                : value.length === this.statusOptions.length
                ? statusAllOptions
                : value;
            this.isStatusAllChange =
                value.includes("statusAll") || value.length === this.statusOptions.length;
        }
        this.refresh = true;
    }

    beforeRefresh() {
        this.packageType = [];
        this.status = [];
        this.refresh = true;
    }
}
</script>

<style lang="scss" scoped>
@import "./index.scss";
.search-bar {
    .search-btns {
        float: right;
        margin-right: -10px;
        margin-top: 0;
    }
    ::v-deep .el-select__tags > span > span:first-child {
        width: 100px;
        text-align: center;
    }
}
</style>
