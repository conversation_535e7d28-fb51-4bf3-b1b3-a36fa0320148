{"title": "Operation Log", "slogan": "Display the tickets generated when you add, update, disable, enable, and delete a domain name.", "allDomainNames": "All Domain Names", "allTypes": "All Types", "update": "Update", "inProgress": "In progress", "successful": "Successful", "failed": "Failed", "startTime": "Start Time", "endTime": "End Time", "completeTime": "Completed On", "timeSperator": "to", "ticketID": "Ticket ID", "ticketType": "Ticket Type", "resubmit": "Resubmit", "domainNameExists": "The domain name already exists.", "resubmitAddDomainTicketPrompt": "If you choose to resubmit the ticket that adds a new domain name, you will be redirected to the Add Domain Name page populated with the current configuration. Continue?", "ticketDetails": "Ticket Details", "updateDomainConfigSuccess": "The domain configuration is updated.", "domainConfigUpdateCompleted": "The domain configuration update is completed, and will take effect in 5 to 15 minutes.", "submitted": "Submitted", "domain": "Domain", "actionMap": {"itm0": "Update", "itm1": "Disable", "itm2": "Enable", "itm3": "Delete", "itm4": "Add", "itm5": "Update", "itm6": "Update", "itm7": "Update"}, "statusMap": {"itm1": "Unprocessed", "itm2": "In progress", "itm3": "Successful", "itm4": "Failed"}, "domainConf": "Domain configuration", "domainInProgress": "The service ticket is in progress.", "domainCompletedPart1": "The domain {action} is completed.", "domainCompletedPart2": "It is expected to take effect in 5-15 minutes.", "reason": "Cause: {reason}", "domainActionFailed": "Domain {domain} failed", "domainFailed": "Domain {action} failed", "updateAccelerationTypeInProgress": "Acceleration type is updating.", "updateAccelerationTypeCompleted": "Acceleration type is updated.", "updatedToCDNProduct": "Updated to CDN acceleration service.", "updateAccelerationTypeFailed": "Failed to update the acceleration type.", "updateFailed": "Failed to update.", "ticketNotProcessed": "Service ticket is unprocessed.", "modifyAccelerationAreaInProgress": "Accelerated region is being modified.", "modifyAccelerationAreaCompleted": "Accelerated region is modified.", "domainConfigCompleted": "Domain configuration is completed.", "modifyAccelerationAreaFailed": "Failed to modify the accelerated region.", "domainConfigFailed": "Domain configuration failed.", "orderProgress": {"finish": "Finish", "success": "Successful", "fail": "Failed"}}