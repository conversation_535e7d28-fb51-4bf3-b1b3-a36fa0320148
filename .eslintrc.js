module.exports = {
    root: true,
    env: {
        browser: true,
        commonjs: true, // 支持commonjs语法
        node: true, // 支持node
        es6: true, // 支持es6语法
    },
    extends: [
        "plugin:vue/essential",
        "eslint:recommended",
        "@vue/typescript/recommended",
        "@vue/prettier",
        "@vue/prettier/@typescript-eslint",
    ],
    parserOptions: {
        parser: "@typescript-eslint/parser",
        ecmaVersion: 2020,
        sourceType: "module",
    },
    rules: {
        // 使用了transform-remove-console插件，在插件里定义打包时候该保留/删除的 日志级别，eslint 只做警示，是否真的需要保留交给开发者
        "no-console": "warn",
        camelcase: "off",
        "@typescript-eslint/camelcase": 0,
        "no-debugger": process.env.NODE_ENV === "production" ? "error" : "off",
        // 文件以单一的换行符结束
        "eol-last": 0,
        // 必须使用全等
        eqeqeq: 2,
        // 关闭禁止混用tab和空格
        "no-mixed-spaces-and-tabs": [0],
        // 'import/extensions': ['off', 'never'],
        // 箭头函数的参数使用圆括号
        "arrow-parens": [2, "as-needed"],
        // 函数圆括号之前有一个空格，关闭 eslint 规则，交由 prettier 自行处理
        "space-before-function-paren": 0,
        // 暂时允许 any
        "@typescript-eslint/no-explicit-any": [0, { ignoreRestArgs: true }],
        // 允许使用非空断言 obj!.a
        "@typescript-eslint/no-non-null-assertion": 0,
        // 关闭声明未使用变量规则
        "@typescript-eslint/no-unused-vars": "off",
        // 启用 prettier 格式化检查
        "prettier/prettier": "warn",
    },
};
