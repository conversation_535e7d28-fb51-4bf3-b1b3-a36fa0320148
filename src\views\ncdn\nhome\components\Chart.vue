<template>
    <ct-box :tags="$t('home.chart.title2')">
        <template #tags-slot>
            <el-radio-group v-model="method" size="mini" class="time-radio">
                <el-radio-button class="time-radio-button" label="flow">{{
                    $t("home.chart.flow")
                }}</el-radio-button>
                <el-radio-button class="time-radio-button" label="bandwidth">{{
                    $t("home.chart.bandwidth")
                }}</el-radio-button>
            </el-radio-group>
        </template>
        <v-chart
            class="chart chart-box"
            v-loading="loading"
            :element-loading-text="$t('common.chart.loading')"
            autoresize
            theme="cdn"
            :options="options"
        />
    </ct-box>
</template>
<script lang="ts">
import { Component, Prop, Vue, Watch } from "vue-property-decorator";
import { StatisticsUsageUrl } from "@/config/url/statistics";
import { DomainModule } from "@/store/modules/domain";
import { ScaleModule } from "@/store/modules/scale";
import { THEME_AREA_STYLE } from "@/config/echart/cdn-theme";
import { Query5Min } from "@/types/statistics/usage";
import { convertBandwidthM2P, convertFlowM2P } from "@/utils";
import { divideScale } from "@/utils/unit";
import { DomainActionEnum, getDomainAction } from "@/config/map";
import { StatisticsModule } from "@/store/modules/statistics";
import { nUserModule } from "@/store/modules/nuser";
import { getAm0 } from "@/utils";

type tooltipParam = { name: string; marker: string; value: string[] | number[]; axisValueLabel: string };
@Component
export default class Chart extends Vue {
    @Prop({ default: true }) onInit!: boolean;

    public method = "flow";
    public loading = true;
    private avgFlow = 0;
    private peekBandwidth = 0;
    private seriesData: any[][] = [];
    private searchParams: {
        type: string;
        domainList: string[]; // 根据业务要求，域名是必要参数
        startTime: number; // 根据业务要求，时间是必要参数
        endTime: number;
        productType: string[];
    } = {
        type: "flow",
        domainList: [],
        startTime: 0,
        endTime: 0,
        productType: [],
    };
    // 是否子账号
    get childAccount() {
        return StatisticsModule.childAccount;
    }
    get domainCountLimit() {
        return StatisticsModule.domainCountLimit;
    }
    get isFcdnCtyunCtclouds() {
        return nUserModule.isFcdnCtyunCtclouds;
    }
    // 域名列表
    get domainList() {
        // fcdn-ctyun 体系（国内+国际）需要取ctiam权限管控下的域名数据
        return this.isFcdnCtyunCtclouds
            ? DomainModule[getDomainAction("Home")].list
            : DomainModule[DomainActionEnum.Data].list;
    }
    //进制转换
    get scale() {
        return ScaleModule.scale;
    }
    //通过平均流量*300,通过此指标来执行五分钟流量缩进,返回[缩进倍数，单位]
    get flowIndention() {
        const size = {
            MB: 1,
            GB: this.scale,
            TB: Math.pow(this.scale, 2),
            PB: Math.pow(this.scale, 3),
        };
        const rst = convertFlowM2P(this.avgFlow, this.scale);
        return [size[rst._unit], rst.unit];
    }
    //以峰值带宽为指标,采用自缩进方式，返回[缩进倍数，单位]
    get bandwidthIndention() {
        const size = {
            Mbps: 1,
            Gbps: this.scale,
            Tbps: Math.pow(this.scale, 2),
            Pbps: Math.pow(this.scale, 3),
        };
        const rst = convertBandwidthM2P(this.peekBandwidth, this.scale);
        return [size[rst._unit], rst.unit];
    }

    // 获取 月-日 周
    private formatData(val: string) {
        // 兼容后端接口数据中单位为 s 的时间戳
        const d = new Date(+val);
        const weekConfig = [
            this.$t("home.chart.sun"),
            this.$t("home.chart.mon"),
            this.$t("home.chart.tue"),
            this.$t("home.chart.wed"),
            this.$t("home.chart.thu"),
            this.$t("home.chart.fri"),
            this.$t("home.chart.sat"),
        ];
        return `${d.getMonth() + 1}-${d.getDate()} ${weekConfig[d.getDay()]}`;
    }

    // 趋势图数据处理
    get options() {
        const data = this.seriesData;
        // 无数据时显示全0曲线
        this.$nextTick(() => {
            let cur = +((getAm0(new Date()).getTime() - 6 * 24 * 3600 * 1000) / 100000).toFixed()
            if (data.length === 0) {
                const end = +((new Date().getTime()) / 100000).toFixed();
                while (cur < end) {
                    data.push([cur * 100000, "0.00"])
                    cur += 3
                }
            }
        })
        return {
            tooltip: {
                trigger: "axis",
                formatter: (a: tooltipParam[]) =>
                    a[0].axisValueLabel +
                    "<br>" +
                    a[0].marker +
                    `${this.$t(this.method === "flow" ? "home.chart.flow" : "home.chart.bandwidth")}：` +
                    a[0].value[1] +
                    `${this.method === "flow" ? this.flowIndention[1] : this.bandwidthIndention[1]}`,
            },
            xAxis: {
                type: "time", // 类目轴，从 data 中获取数据
                axisLabel: {
                    formatter: this.formatData,
                },
                maxInterval: 24 * 60 * 60 * 1000,
            },
            yAxis: {
                type: "value", // 数值轴
                name: `${this.$t("home.chart.unit")}：${
                    this.method === "flow" ? this.flowIndention[1] : this.bandwidthIndention[1]
                }  `,
                axisLine: {
                    show: false,
                },
                axisTick: {
                    show: false,
                },
                splitLine: {
                    lineStyle: {
                        color: "#F8F8F8",
                    },
                },
            },
            grid: {
                // 容器各个方向的留白
                left: "10%",
                right: "5%",
                top: "15%",
                bottom: "20%",
            },
            series: [
                {
                    type: "line",
                    smooth: true, // 平滑曲线
                    data: data,
                    areaStyle: THEME_AREA_STYLE["#358AE2"],
                    showSymbol: false, // 2个属性配合，常态不显示拐点，hover时显示
                    hoverAnimation: false,
                },
            ],
        };
    }
    paramsInit() {
        const now = new Date();
        const y = now.getFullYear();
        const m = now.getMonth();
        const d = now.getDate();
        const d0am = new Date(y, m, d, 0, 0, 0, 0); // 当日0点
        const d7ago = new Date(+d0am - 6 * 24 * 3600 * 1000); // 7天前
        this.searchParams = {
            type: "flow",
            // domainList: this.domainList,
            // 域名数量大于等于100时，不再传递域名参数
            domainList: this.childAccount ? this.domainList : [],
            startTime: Math.floor(+d7ago / 1000),
            endTime: Math.floor(+now / 1000),
            // 不传递域名参数domainList时，需要传递productType
            productType: this.childAccount ? [] : ["008", "005", "001", "003", "004", "006", "104", "105", "014"],
        };
    }
    //切换tab选项
    @Watch("method")
    onMethodChange() {
        this.getData();
    }

    @Watch("onInit")
    onInitChange(val: boolean) {
        if (val) return;
        this.getData();
    }

    // 获取流量 flow 、带宽 bandwidth
    protected async getData() {
        if (this.onInit) return;
        this.paramsInit();
        // 无可选域名时，拦截请求
        // if (this.searchParams.domainList.length === 0) return;
        if (this.domainList.length === 0) {
            return;
        }
        // 子账号，域名数量 > 100个，不调用接口
        if (this.childAccount && this.domainList.length > this.domainCountLimit) {
            this.$message({
                type: "error",
                showClose: true,
                duration: 3000,
                message: `${this.$t("home.domain.tip", { count: this.domainCountLimit })}`,
            });
            return;
        }

        this.loading = true;

        const rst = await this.$ctFetch<any>(StatisticsUsageUrl.queryOverviewList, {
            method: "POST",
            body: {
                data: this.searchParams,
            },
            headers: {
                "Content-Type": "application/json",
            },
        });
        //缩进判断规则用
        this.avgFlow = Number(divideScale(rst.avgQueryFlow)) * 300;
        this.peekBandwidth = Number(divideScale(rst.topBandwidth));
        this.setOptionData(this.method, rst["5min"]);
    }

    // 设置 chart
    protected setOptionData(type: string, dataList: Query5Min[] | null) {
        this.seriesData = [];
        if (dataList === null) {
            const d0am = new Date();
            for (let i = 0; i < 7; i++) {
                const dayNum = new Date(+d0am - i * 24 * 3600 * 1000);
                this.seriesData.push([+dayNum, "0.00"]);
            }
        } else {
            const factor = Math.pow(this.scale, 2);
            dataList.forEach(list => {
                this.seriesData.push([
                    +list.flowTime * 1000,
                    (
                        (type === "flow"
                            ? list.flow / +this.flowIndention[0]
                            : list.bandwidth / +this.bandwidthIndention[0]) / factor
                    ).toFixed(2),
                ]);
            });
        }
    }
}
</script>

<style lang="scss" scoped>
.ct-box {
    @include g-height(330px, 260px);
    overflow: hidden;
}

.chart-box {
    height: 90%;
    width: 100%;
}

.time-radio {
    text-align: center;
    ::v-deep {
        .time-radio-button .el-radio-button__inner {
            display: inline-block;
            width: auto;
        }
    }
}
</style>
