{"searchBar": {"errMsg1": "Domain filtering feature supports all or up to {count} domains, please reselect domains and retry.", "errMsg2": "Please select a domain name", "errMsg3": "Tag filtering feature supports up to {count} domains, please reselect domains and retry.", "errMsg4": "Please select a time", "errMsg5": "The current domains exceeds {count},Please limit the number of domains you choose less than {count}.", "errMsg6": "The interval between the start and end times cannot exceed 3 hours. Please select a new time."}, "requestType": ["Total Requests", "Static Http Requests", "Dynamic Http Requests", "Static Https Requests", "Dynamic Https Requests", "Dynamic Optimization Requests"], "qpsType": ["Total QPS", "Dynamic Https QPS", "Static Https QPS", "Dynamic Http QPS", "Static Http QPS", "Dynamic Optimization QPS"], "breadcrumb": {"statistic": "Statistical Analysis", "dcdn": "ICDN", "usage": "Utilization Statistics", "rank": "Hotspots Analysis", "referer": "Refresh and Prefetch", "refererTip1": "The cache of the CDN node is not updated in real time. After the content of your origin site is updated, you want the user to obtain", "refererTip2": "the latest resources, you can force the CDN node cache to expire through the cache refresh feature.", "create": "Create Task", "views": "View Tasks"}, "forbidden": {"breadcrumbTitle": "No Entry", "service": "Sorry, you have not activated CDN/ICDN, please click <a href='{cdnlink}' target='_blank'>&nbsp;CDN</a> or <a href='{icdnlink}' target='_blank'>ICDN</a> to activate the service and then try again.", "auth": "Sorry, you lack relevant permission, please contact the administrator of the workspace.", "default": "Access restricted", "offline": "Sorry, you have not activated CDN/ICDN, please contact with the key account manager to help you to activate the service first and then try again. "}, "datePicker": {"start": "Start Date", "end": "End Date"}, "search": {"start": "Search", "reset": "Reset"}, "table": {"index": "No.", "operation": "Actions", "empty": "No Data"}, "message": {"success": "Successfully", "fail": "Operation failure"}, "dialog": {"submit": "OK", "cancel": "Cancel", "close": "Close", "previous": "Previous"}, "messageBox": {"title": "Hint"}, "chart": {"loading": "Drawing furiously..."}, "ctCodeSample": {"buttonText": "example"}, "ctTable": {"numberColumnLabel": "number", "operationColumnLabel": "operation"}, "ctTimePicker": {"label1": "Today", "label2": "Yesterday", "label3": "Last {number} Days", "label4": "Customize"}, "ctTimeCompare": {"buttonText": "Data Comparison", "spanText": "comparison"}, "ctHourPicker": {"label1": "Last 1 Hour", "label2": "Last 2 Hours", "label3": "Last 3 Hours", "label4": "Customize"}, "labelSelect": {"labelSlectTagsText1": "All Labels", "labelSlectTagsText2": "{num} Item Selected", "selectDropdownEmpty": "No Data", "placeholder": "Label", "checkboxText1": "Label Group", "checkboxText2": "Label"}, "domainSelect": {"labelSlectTagsText1": "{count} Item Selected|{count} Items Selected", "placeholder": "Domain Name"}, "domainStatus": ["Confirming", "Confirmed", "Configurating", "Enabled", "Disabling", "Disabled", "Deleting", "Deleted", "Unconfirmed", "Failed to configuration", "Failed to stop", "Failed to delete", "Customer Migration", "Product Migration"], "taskStatus": ["Waiting", "On operation", "Operation succeed", "Failed"], "productName": ["Web Acceleration", "Dynamic Acceleration", "Download", "VOD", "Livestreaming", "Integrated CDN", "Secure Acceleration", "CDN", "ICDN - Upload Acceleration", "ICDN - WebSocket Acceleration", "Download (idle time)", "Unknown"], "allProductName": {"001": "Web Acceleration", "002": "Dynamic Acceleration", "003": "Download", "004": "VOD", "005": "Livestreaming", "006": "ICDN", "007": "Secure Acceleration", "008": "CDN", "009": "App Acceleration", "010": "Web Application Firewall", "011": "DDoS", "014": "Download (idle time)", "015": "RTC", "020": "AccessOne", "021": "ZeroTrust", "024": "EdgeWan", "026": "ZeroTrust"}, "patternMsg": ["Please enter the correct domain name", "Please enter the correct ipv4/ipv6 address", "Please enter the correct port number", "Please enter the correct referer", "Please enter the correct URL address...", "Please enter the correct directory address..."], "errModal": {"title": "SYS Msg", "defaultReason": "Unknown Error", "detail": "<PERSON><PERSON><PERSON>"}, "date": {"days": "days", "day": "day", "minute": "minute", "minutes": "minutes", "hour": "hour", "hours": "hours", "second": "second", "seconds": "seconds"}, "form": {"validateFailed": "Input error in the table, please verify."}, "areaSelect": {"allArea": "All Regions", "selectedCount": "Selected {count} item | Selected {count} items", "selectAll": "Select All"}, "pickerOptions": {"option1": "Last 15 Minutes", "option2": "Last 1 hours", "option3": "Last 6 hours", "option4": "Last 24 hours", "option5": "Last Week", "option6": "Last Month", "option7": "Last 3 Months"}, "sessionError": "The current session is not logged in or has timed out. You need to log in again", "oldVersion": "Old Console", "oldVersionTip": "Switching the platform version will discard edited content. Are you sure to continue?", "alarmTip": {"tip1": "[Risk Alert] Recently, eSurfing Cloud CDN has received reports of unprotected domains being attacked or abused for data transmission, resulting in high bills. Please review your domain configuration and learn about the risks and countermeasures. For details, please refer to: {0}.", "tip2": "High Bill Risk Warning", "tip3": "To avoid attacks or abuses for data transmission on your domain, which could lead to high bills, we recommend that you set up protection and alert features as needed to reduce the risks of sudden spikes in bandwidth usage. For details, please refer to: {0}."}, "ctiam": {"auth": "You don't have permission to operate, please contact account administrator for authorization based on AUTH and RESOURCE information", "resource": "Specific resources (please adjust account-id and resourcename parameter as needed)  "}, "loading": "Data is loading, please wait...", "提交": "Submit"}