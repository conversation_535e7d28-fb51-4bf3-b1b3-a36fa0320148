import urlTransformer from "@/utils/logic/url";

export default {
  methods: {
    handleAreaScopeChange() {
      const h = this.$createElement;
      const openLink = orderLink;
      this.$msgbox({
        title: "提示",
        closeOnClickModal: false,
        closeOnPressEscape: false,
        message: h("p", null, [
          h(
            "span",
            null,
            "修改加速区域可能会引起回源ip变更，如果您的源站有回源IP白名单限制，请通过"
          ),
          h(
            "el-link",
            {
              props: {
                type: "primary",
              },
              on: {
                click: () => window.open(openLink),
              },
              style: {
                display: "inline-block"
              }
            },
            "【客户服务工单】"
          ),
          h(
            "span",
            null,
            "提交接收回源白名单邮箱（支持多个邮箱），收到天翼云CDN+团队发送的回源白名单邮件并在源站加白完成后，再到客户控制台变更加速区域。"
          ),
        ]),
        showClose: false,
      });
    },
  },
  computed: {
    orderLink() {
      return urlTransformer(
          {
              a1Ctyun: "https://www.ctyun.cn/h5/wsc/worksheet/submit",
              a1Ctcloud: "https://www.esurfingcloud.com/contactus/zh-cn/fromIndex?lang=zh-cn"
          }
      )
    },
  },
};
