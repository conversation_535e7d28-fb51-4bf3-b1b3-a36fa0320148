/**
 * @Description: 部分组件需要调用 api ，请求地址前缀应该进行统一管理
 * @Author: wang yuegong
 */

// 业务的虚拟路径前缀
import { LOGIC_ROUTE_PREFIX } from "@cdnplus/common/config/url/_PREFIX";

// 获取表单项配置
export const GetProperty = LOGIC_ROUTE_PREFIX + "/form/operation/GetProperty";

// AlogicCertSelect 组件需要的地址
export const nAlogicCertSelect = {
    // 添加证书（定制接口，会先执行域名和证书的匹配判断）
    createCert: LOGIC_ROUTE_PREFIX + "/cert/domain/Create",
};

// AlogicPhone 组件需要的请求地址
export const AlogicPhone = {
    SendSms: LOGIC_ROUTE_PREFIX + "/form/util/sms/Send",
    VerifySms: LOGIC_ROUTE_PREFIX + "/form/util/sms/Verify",
    GetImage: LOGIC_ROUTE_PREFIX + "/form/util/image/Get",
    VerifyImage: LOGIC_ROUTE_PREFIX + "/form/util/image/Verify",
};

// AlogicIfshowIpv6Switch 组件需要的地址
export const AlogicIfshowIpv6Switch = {
    UserInfo: LOGIC_ROUTE_PREFIX + "/billing/QueryUserInfo",
};
