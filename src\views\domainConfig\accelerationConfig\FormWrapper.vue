<template>
    <section class="ct-section-wrap" v-loading="detailLoading">
        <el-scrollbar wrap-class="ct-config" ref="scrollbar">
            <section class="content-wrap">
                <template v-if="!isService">
                    <el-alert
                        class="header-tip-style"
                        type="info"
                        show-icon
                        :title="currentAbnormalText"
                        :closable="false"
                    />
                </template>
                <div id="section_div0">
                    <p
                        id="div0"
                        class="label-name base-info-style"
                        :class="{ 'mt-0': !isPoweredByQiankun && isService }"
                    >
                        {{ $t("domain.editPage.label1") }}
                    </p>
                    <basic-info class="basic-style" :baseInfo="baseInfo" :defaultData="defaultData" />
                </div>
                <div class="line-style" style="margin-bottom:20px"></div>
                <section
                    v-move-directive="'moveDetail'"
                    ct-dom-title=".label-name"
                    :data-key="childKey"
                    class="domain-edit"
                >
                    <div class="content-wrap_left" ref="outer">
                        <el-collapse
                            v-if="isPoweredByQiankun"
                            v-model="activeNames"
                            ref="collapse"
                            class="collapse-style"
                        >
                            <el-collapse-item :title="collapseTitle" name="1">
                                <div class="anchor-item">
                                    <basic-config
                                        ref="basicConfig"
                                        :defaultData="defaultData"
                                        :fromAccelerationConfig="fromAccelerationConfig"
                                        :showForStaticAndIcdn="showForStaticAndIcdn"
                                        @update-child="updateChild"
                                    />
                                    <div class="line-style" style="width: 100%"></div>
                                </div>
                            </el-collapse-item>
                        </el-collapse>
                        <div v-else>
                            <basic-config
                                ref="basicConfig"
                                :defaultData="defaultData"
                                :fromAccelerationConfig="fromAccelerationConfig"
                                :showForStaticAndIcdn="showForStaticAndIcdn"
                                isFlatten
                            />
                        </div>
                        <!-- @validate="handleValidateEvent" 状态码缓存用，已移到状态码缓存子组件 -->
                        <el-form
                            class="simple-create-form"
                            name="form"
                            :model="form"
                            ref="form"
                            label-width="141px"
                            label-position="left"
                            :disabled="!isEdit || !isService"
                        >
                            <div class="anchor-item">
                                <div id="section_div8" class="form-section">
                                    <p id="div8" class="label-name">
                                        <!-- cdn 入口 小标题静态配置改为缓存配置，对应锚点也修改 -->
                                        {{
                                            isPoweredByQiankun
                                                ? $t("domain.editPage.label27")
                                                : $t("domain.detail.tab6")
                                        }}
                                    </p>
                                    <!-- 静态加速 开关 -->
                                    <el-form-item prop="statics_ability" v-if="isPoweredByQiankun">
                                        <span slot="label"
                                            >{{ $t("common.productName[0]") }}
                                            <span>
                                                <el-tooltip placement="top">
                                                    <div slot="content">
                                                        {{ $t("domain.detail.tip108") }}
                                                    </div>
                                                    <ct-svg-icon
                                                        icon-class="question-circle"
                                                        class-name="ct-sort-drag-icon"
                                                    ></ct-svg-icon></el-tooltip></span
                                        ></span>
                                        <el-switch
                                            v-model="form.statics_ability"
                                            active-value="on"
                                            inactive-value="off"
                                            @change="onStaticsAbilityChange"
                                        ></el-switch>
                                    </el-form-item>
                                    <div
                                        v-show="isStaticsAbilityOn"
                                        :class="{ 'switch-wrapper': isPoweredByQiankun }"
                                    >
                                        <!-- 缓存配置 子组件 -->
                                        <lock-tip :lock="isLockCacheTime" :addon="isStaticsAbilityOn">
                                            <filetype-ttl
                                                ref="filetypeTtl"
                                                :datas="form"
                                                :isLockCacheTime="isLockCacheTime"
                                                :isStaticsAbilityOn="isStaticsAbilityOn"
                                                @onChange="onFiletypeTtlChange"
                                            ></filetype-ttl>
                                        </lock-tip>

                                        <!-- 状态码缓存 子组件 -->
                                        <div>
                                            <error-code
                                                ref="errorCode"
                                                :datas="form"
                                                :isStaticsAbilityOn="isStaticsAbilityOn"
                                                @onChange="onErrorCodeChange"
                                            ></error-code>
                                        </div>

                                        <!-- 缓存参数 子组件 -->
                                        <lock-tip :lock="isLockCacheKeyArgs" :addon="isStaticsAbilityOn">
                                            <cacheKey-args
                                                ref="cacheKeyArgs"
                                                :datas="form"
                                                :isLockCacheKeyArgs="isLockCacheKeyArgs"
                                                :isNewEcgw="isNewEcgw"
                                                @onChange="onCacheKeyArgsChange"
                                                @onCacheKeyDeleteIdList="onCacheKeyDeleteIdList"
                                                @onCacheKeyIdList="onCacheKeyIdList"
                                            ></cacheKey-args>
                                        </lock-tip>

                                        <!-- 缓存uri 子组件 -->
                                        <lock-tip :lock="isLockCacheKeyUri" :addon="isStaticsAbilityOn">
                                            <cacheKey-uri
                                                ref="cacheKeyUri"
                                                :datas="form"
                                                :isLockCacheKeyUri="isLockCacheKeyUri"
                                                :isStaticsAbilityOn="isStaticsAbilityOn"
                                                :isNewEcgw="isNewEcgw"
                                                @onChange="onCacheKeyUriChange"
                                            ></cacheKey-uri>
                                        </lock-tip>

                                        <!-- 可插拔配置-共享缓存 -->
                                        <shared-host
                                            ref="shared_host"
                                            :isLockSharedHost="isLockSharedHost"
                                            :formDatas="form"
                                            :product_code="temp_domain_detail.product_code"
                                            :domain_type="temp_domain_detail.domain_type"
                                            @onModuleChange="onModuleChange('shared_host', $event)"
                                            @onSharedHostCanChooseList="
                                                list => (sharedHostCanChooseList = list)
                                            "
                                        />

                                        <!-- 后续在静态加速开关中添加字段，要同步在 onStaticsAbilityChange 中添加重新开启的逻辑 -->
                                    </div>
                                </div>
                                <div
                                    v-if="isPoweredByQiankun || is_show_origin_type"
                                    id="section_div9"
                                    class="form-section"
                                >
                                    <!-- 动态配置 -->
                                    <p id="div9" class="label-name">
                                        {{ $t("domain.editPage.label28") }}
                                    </p>
                                    <div
                                        class="common-wrapper"
                                        v-if="!dynamicAccelerateEnable && isPoweredByQiankun"
                                    >
                                        <div class="title-box">{{ $t("domain.editPage.label28") }}</div>
                                        <div class="content-box">
                                            <!-- 提供动态请求加速服务，AceesOne通过实时检测全球节点延迟，通过智能探测选路、自研协议及内核优化技术，提升动态请求的访问速度。 -->
                                            {{ $t("domain.editPage.dynamicTip") }}
                                        </div>
                                        <el-button
                                            class="btn-style"
                                            type="primary"
                                            @click="toHighVersion"
                                            :disabled="false"
                                            >{{ $t("domain.editPage.btn1") }}</el-button
                                        >
                                    </div>
                                    <div
                                        class="dynamic-wrap"
                                        v-if="dynamicAccelerateEnable && isPoweredByQiankun"
                                    >
                                        <!-- 动态加速 开关 -->
                                        <el-form-item
                                            :label="$t('common.productName[1]')"
                                            prop="dynamic_ability"
                                            class="dynamic-box"
                                        >
                                            <el-switch
                                                style="margin-top:-2px;"
                                                v-model="form.dynamic_ability"
                                                active-value="on"
                                                inactive-value="off"
                                                @change="dynamic_ability_change"
                                            ></el-switch>
                                            <span>
                                                <!-- <i class="el-icon-warning-outline icon-tip"></i> -->
                                                <ct-svg-icon
                                                    icon-class="info-circle"
                                                    class-name="icon-column-label"
                                                />
                                            </span>
                                            <span class="tips">{{ $t("domain.editPage.tooltip15") }}</span
                                            ><a
                                                :underline="false"
                                                class="word-wrap aocdn-ignore-link"
                                                style="color:#3d73f5"
                                                @click="$docHelp(charge_link)"
                                                >{{ $t("domain.editPage.tooltip16") }}</a
                                            >
                                        </el-form-item>
                                        <div v-show="form.dynamic_ability === 'on'" class="switch-wrapper">
                                            <!-- 选路方式 -->
                                            <el-form-item
                                                :label="$t('domain.create.routeSelectionMethod')"
                                                prop="dynamic_config.route_type"
                                                class="label-header is-required"
                                            >
                                                <el-radio-group
                                                    class="radio-style"
                                                    v-model="form.dynamic_config.route_type"
                                                    :disabled="form.dynamic_ability === 'off'"
                                                >
                                                    <el-radio :label="0">{{
                                                        $t("domain.detail.routeType[0]")
                                                    }}</el-radio>
                                                    <el-radio :label="1">{{
                                                        $t("domain.detail.routeType[1]")
                                                    }}</el-radio>
                                                    <el-radio :label="2">{{
                                                        $t("domain.detail.routeType[2]")
                                                    }}</el-radio>
                                                </el-radio-group>
                                            </el-form-item>
                                            <!-- 动态回源策略 aocdn入口显示 -->
                                            <el-form-item
                                                :label="$t('domain.create.dynamicPolicy')"
                                                prop="origin_policy.http_config.origin_type"
                                                :rules="rules.origin_type"
                                                class="is-required"
                                            >
                                                <span slot="label" class="label-style"
                                                    >{{ $t("domain.create.dynamicPolicy") }}
                                                    <span class="question-style">
                                                        <el-tooltip placement="top" :content="dynamicContent">
                                                            <ct-svg-icon
                                                                icon-class="question-circle"
                                                                class-name="ct-sort-drag-icon"
                                                            ></ct-svg-icon></el-tooltip></span
                                                ></span>

                                                <el-radio-group
                                                    v-model="form.origin_policy.http_config.origin_type"
                                                    :disabled="form.dynamic_ability === 'off'"
                                                >
                                                    <el-radio label="fastest_simple">{{
                                                        $t("domain.detail.originType[0]")
                                                    }}</el-radio>
                                                    <el-radio label="poll">{{
                                                        $t("domain.detail.originType[1]")
                                                    }}</el-radio>
                                                    <el-radio label="keep_hash">{{
                                                        $t("domain.detail.originType[2]")
                                                    }}</el-radio>
                                                </el-radio-group>
                                            </el-form-item>
                                        </div>
                                    </div>
                                    <!-- 动态回源策略 CDN入口显示 -->
                                    <el-form-item
                                        :label="$t('domain.create.dynamicPolicy')"
                                        prop="origin_policy.http_config.origin_type"
                                        :rules="rules.origin_type"
                                        v-if="is_show_origin_type"
                                    >
                                        <span slot="label" class="label-style"
                                            >{{ $t("domain.create.dynamicPolicy") }}
                                            <span class="question-style">
                                                <el-tooltip placement="top" :content="dynamicContent">
                                                    <ct-svg-icon
                                                        icon-class="question-circle"
                                                        class-name="ct-sort-drag-icon"
                                                    ></ct-svg-icon></el-tooltip></span
                                        ></span>

                                        <el-radio-group v-model="form.origin_policy.http_config.origin_type">
                                            <el-radio label="fastest_simple">{{
                                                $t("domain.detail.originType[0]")
                                            }}</el-radio>
                                            <el-radio label="poll">{{
                                                $t("domain.detail.originType[1]")
                                            }}</el-radio>
                                            <el-radio label="keep_hash">{{
                                                $t("domain.detail.originType[2]")
                                            }}</el-radio>
                                        </el-radio-group>
                                    </el-form-item>
                                </div>
                                <div id="section_div10" class="form-section">
                                    <!-- 上传配置 -->
                                    <p id="div10" class="label-name" v-if="isPoweredByQiankun">
                                        {{ $t("domain.editPage.label29") }}
                                    </p>
                                    <el-form-item
                                        :label="$t('domain.detail.label120')"
                                        prop="upload_speed"
                                        :rules="rules.upload_speed"
                                        v-if="isPoweredByQiankun"
                                    >
                                        <span slot="label"
                                            >{{ $t("domain.detail.label120") }}
                                            <span>
                                                <el-tooltip placement="top">
                                                    <div slot="content">
                                                        {{ $t("domain.detail.tip109") }}
                                                    </div>
                                                    <ct-svg-icon
                                                        icon-class="question-circle"
                                                        class-name="ct-sort-drag-icon"
                                                    ></ct-svg-icon></el-tooltip></span
                                        ></span>
                                        <el-switch
                                            v-model="form.upload_speed"
                                            :active-value="1"
                                            :inactive-value="0"
                                            @change="upload_speed_change"
                                        ></el-switch>
                                    </el-form-item>
                                </div>
                                <div
                                    v-if="isNewEcgw && isStaticsAbilityOn"
                                    id="section_video"
                                    class="form-section"
                                >
                                    <p id="video" class="label-name">
                                        {{ $t("domain.editPage.label31") }}
                                    </p>
                                    <!-- mp4拖拉 -->
                                    <lock-tip :lock="isLockMp4" :addon="isStaticsAbilityOn">
                                        <mp4
                                            :datas="form"
                                            @onChange="onMp4Change"
                                            :isLockMp4="isLockMp4"
                                            :isStaticsAbilityOn="isStaticsAbilityOn"
                                            ref="mp4"
                                        ></mp4>
                                    </lock-tip>

                                    <!-- flv拖拉 -->
                                    <lock-tip :lock="isLockFlv" :addon="isStaticsAbilityOn">
                                        <flv
                                            :datas="form"
                                            :isStaticsAbilityOn="isStaticsAbilityOn"
                                            :addButtonText="addButtonText"
                                            :flv_info_switch="flv_info_switch"
                                            :isLockFlv="isLockFlv"
                                            @onChange="onFlvChange"
                                            ref="flv"
                                        ></flv>
                                    </lock-tip>
                                </div>
                                <!-- 高级配置 -->
                                <div v-if="isNewEcgw && showForStaticAndIcdn" id="section_advancedConf">
                                    <p id="advancedConf" class="label-name">
                                        {{ $t("domain.editPage.label34") }}
                                    </p>
                                    <!-- 访问URL重定向 -->
                                    <lock-tip :lock="isLockDefineRedirect" :addon="showForStaticAndIcdn">
                                        <define-redirect
                                            ref="defineRedirect"
                                            :datas="form"
                                            :temp_define_redirect="temp_define_redirect"
                                            :isLockDefineRedirect="isLockDefineRedirect"
                                            @onChange="onDefineRedirectChange"
                                        ></define-redirect>
                                    </lock-tip>
                                    <!-- 错误页面重定向 -->
                                    <lock-tip :lock="isLockErrorPage" :addon="showForStaticAndIcdn">
                                        <error-page
                                            ref="errorPage"
                                            :datas="form"
                                            :isLockErrorPage="isLockErrorPage"
                                            :showForStaticAndIcdn="showForStaticAndIcdn"
                                            @onChange="onErrorPageChange"
                                        ></error-page>
                                    </lock-tip>
                                </div>
                                <!-- UDFScript -->
                                <div v-if="showForStaticAndIcdn" id="section_UDFScript" class="form-section">
                                    <p id="UDFScript" class="label-name">
                                        UDFScript
                                    </p>
                                    <div>
                                        <udf-script
                                            :datas="form"
                                            :isPoweredByQiankun="isPoweredByQiankun"
                                            ref="udfScript"
                                        ></udf-script>
                                    </div>
                                </div>
                            </div>
                        </el-form>
                    </div>
                    <ct-anchor
                        v-sort-directive="'sortDetailAnchor'"
                        v-move-directive="'moveDetailAnchor'"
                        ref="anchor"
                        :data-key="anchorChildKey"
                        class="anchor-position anchor-style"
                        :data="getActivities"
                        scroll-dom=".ct-config"
                        @update-child="updateAnchorChild"
                    />
                </section>
                <div class="anchor-helper-block-2"></div>
                <cute-fixed-footer class="submit">
                    <div class="footer-content">
                        <!-- 不可编辑：域名状态：非 已启用，或者：isBillingSuccess 为 false -->
                        <el-tooltip
                            v-if="(!isService || !isBillingSuccess) && !isFromCopyDomain"
                            class="item"
                            effect="dark"
                            :content="editConfigContent"
                            placement="top"
                        >
                            <el-button
                                type="primary"
                                @click="editConfig"
                                size="medium"
                                :disabled="!isService || !isBillingSuccess"
                            >
                                {{ $t("domain.editPage.btn2") }}
                            </el-button>
                        </el-tooltip>

                        <!-- 可编辑：域名状态：已启用，并且：isBillingSuccess 为 true -->
                        <!-- 直播域名不需要tooltips提示，不可编辑 -->
                        <el-button
                            type="primary"
                            @click="editConfig"
                            :disabled="is_VOD"
                            size="medium"
                            v-if="!isEdit && isService && isBillingSuccess && !isFromCopyDomain"
                        >
                            {{ $t("domain.editPage.btn2") }}
                        </el-button>
                        <el-button
                            key="cancel"
                            plain
                            @click="handleCancel"
                            :loading="submitLoading"
                            size="medium"
                            v-if="isEdit"
                        >
                            {{ $t("domain.editPage.btn3") }}
                        </el-button>
                        <el-button
                            key="submit"
                            :disabled="isSecurityFormSame || isWaitingFormValidate"
                            type="primary"
                            @click="handleUpdateDomain(null, null)"
                            :loading="submitLoading"
                            size="medium"
                            v-if="isEdit"
                        >
                            {{ $t("domain.editPage.btn4") }}
                        </el-button>
                        <el-button v-if="isFromCopyDomain" @click="goBackToCreateDomain">
                            {{ $t("certificate.back") }}
                        </el-button>
                    </div>
                </cute-fixed-footer>

                <work-order-tip-dialog
                    :dialogVisible="workOrderVisible"
                    :existOrderLoading="existOrderLoading"
                    @cancel="cancel"
                    @submit="submitWorkOrder"
                />
                <server-port-tip-dialog
                    :dialogVisible="serverPortTipVisible"
                    @submit="submitServerPortTip"
                    :errorCode="serverPortErrorCode"
                />
            </section>
        </el-scrollbar>
    </section>
</template>

<script>
import BasicConfig from "@/views/domainConfig/basicConfig";
import { formValidate2Promise } from "@/utils";
import validFieldMixin from "@/views/domainConfig/validFieldMixin";
import { siteUrl } from "@/config/url";
import { SecurityAbilityModule } from "@/store/modules/securityAbility";
import basicInfo from "@/views/domainConfig/basicInfo";
import ctSvgIcon from "@/components/ctSvgIcon";
import moduleMixin from "@/views/domainConfig/moduleMixin";
import { get, cloneDeep } from "lodash-es";
import ctAnchor from "@/components/ctAnchor";
import workOrderTipDialog from "@/views/domainConfig/basicConfig/components/workOrderTipDialog.vue";
import refreshMixin from "@/views/domainConfig/refreshMixin";
import gatewayDataMixin from "@/views/domainConfig/gatewayDataMixin";
import serverPortTipDialog from "@/views/domainConfig/basicConfig/components/serverPortTipDialog.vue";
import i18n from "@/i18n/index";
import handleAnchorMixin from "@/views/domainConfig/handleAnchorMixin";
import formDataMixin from "../mixins/acceConfigFormData.mixin";
import lockTip from "../components/lockTIp.vue";
import { nDomainUrl } from "@/config/url";
import { moveDirective, sortDirective } from "@/directives/ctSort";
import urlTransformer from "@/utils/logic/url";
import { CdnConfigModule } from "@/store/modules/cdnConfig";
import { CODE_SERVER_PORT_ERROR_2364, CODE_SERVER_PORT_ERROR_2367 } from "@/utils/ctFetch/errorConfig";
import configModuleMixin from "@/mixins/configModuleMixin"; // 可插拔配置，引入 mixin
import { ConfigModulesModule } from "@/store/modules/configModules"; // 可插拔配置，引入 MODULE

const CacheTtlMap = {
    2: { name: i18n.t("simpleForm.alogicCacheMixin.CacheTtlMap.2"), factor: 60 * 60 * 24 },
    3: { name: i18n.t("simpleForm.alogicCacheMixin.CacheTtlMap.3"), factor: 60 * 60 },
    4: { name: i18n.t("simpleForm.alogicCacheMixin.CacheTtlMap.4"), factor: 60 },
    5: { name: i18n.t("simpleForm.alogicCacheMixin.CacheTtlMap.5"), factor: 1 },
};

export default {
    directives: {
        moveDirective,
        sortDirective,
    },
    components: {
        BasicConfig,
        basicInfo,
        ctSvgIcon,
        ctAnchor,
        workOrderTipDialog,
        serverPortTipDialog,
        lockTip,
    },
    mixins: [
        validFieldMixin,
        moduleMixin,
        refreshMixin,
        gatewayDataMixin,
        handleAnchorMixin,
        formDataMixin,
        configModuleMixin,
    ],
    props: {},
    data() {
        return {
            childKey: null,
            anchorChildKey: null,
            defaultData: {},
            isVivo: false,
            temp_domain_detail: {},
            flv_info_switch: false,
            fromAccelerationConfig: true,
            workOrderVisible: false,
            serverPortTipVisible: false,
            isQueryDomainDetail: false,
            is_exist: null, // 是否有在途工单：true表示有在途工单；false表示没有在途工单，即工单已完结
            existOrderLoading: false,
            isDomainChange: false,
            addButtonText: i18n.t("domain.editPage.label10"),
            activeIndex: 0,
            activeNames: [""],
            funcName: [],
            cachekey_args_condition: {},
            keepUrlToAnchor: false,
            anchorInterval: null,
            sharedHostCanChooseList: [],
            getDomainDetailAbortController: null,
            form: {
                filetype_ttl: [], // 缓存配置
                cachekey_args: [], // 缓存参数
                cachekey_args_condition: {},
                error_code: [], // 状态码缓存
                cachekey_uri: [], // 缓存URI
                statics_ability: "on", // 静态加速开关
                dynamic_ability: "off", // 动态加速-开关
                upload_speed: 0, // 上传加速开关
                dynamic_config: {
                    route_type: 2, // 选路方式
                    probe_url: "",
                },
                origin_policy: {
                    http_config: {
                        origin_type: "poll",
                    },
                },
                domain: "",
                mp4_conf_info: {
                    switch: 0,
                    mode: 0,
                    content: "",
                    start_params: "",
                    end_params: "",
                },
                cus_flv_info: [],
                area_scope: "", // 加速区域
                domain_type: "", // 域名类型
                // udfScript
                script: [
                    {
                        id: "",
                        name: "",
                    },
                ],
                define_redirect: [],
                error_page: [], // 错误页面重定向
            },
            temp_define_redirect: [],
            cus_flv_info_condition: {}, // flv的condition
            mp4_conf_info_condition: {}, // mp4的condition
            define_redirect_condition: {}, // 访问URL重定向的condition
            charge_url: "",
            submitLoading: false,

            // 状态码缓存
            cacheKeyIdList: [],
            cacheKeyDeleteIdList: [], // 缓存参数，点击删除时，存储该条数据的id

            loading: false,
            detailLoading: false,
            timeType: "5",
            currentType: "create",
            currenIndex: "",
            containerToTop: 0,
            baseInfo: [
                { label: i18n.t("domain.create.domainName"), value: "" },
                { label: "CNAME", value: "" },
                {
                    label: i18n.t(
                        window.__POWERED_BY_QIANKUN__ ? "domain.editPage.label2" : "domain.create.acceType"
                    ),
                    value: "",
                },
                {
                    label: i18n.t(
                        window.__POWERED_BY_QIANKUN__
                            ? "domain.create.serviceArea"
                            : "domain.list.tableLabel4"
                    ),
                    value: "",
                },
                { label: i18n.t("domain.list.tableLabel6"), value: "" },
            ],
            rules: {
                cachekey_uri_pattern: [
                    { required: true, validator: this.validCacheKeyUriPattern, trigger: "blur" },
                ],
                upload_speed: {
                    required: false,
                    validator: this.validateuploadSpeedORwebsocketSpeed,
                    trigger: ["blur", "change"],
                },
                origin_type: [{ required: true, message: i18n.t("domain.detail.tip72"), trigger: "change" }],
            },
            validateForm: [],
            validateComponents: ["filetypeTtl", "cacheKeyUri", "cacheKeyArgs"],
            serverPortErrorCode: CODE_SERVER_PORT_ERROR_2364,
        };
    },
    computed: {
        isWaitingFormValidate() {
            return CdnConfigModule.isWaitingFormValidate;
        },
        isFromCopyDomain() {
            return this.$route.query.from === "copy";
        },
        charge_link() {
            return urlTransformer({
                a1Ctyun: this.charge_url,
                a1Ctcloud: "https://www.esurfingcloud.com/document/zh-cn/20689737/20689765",
            });
        },
        // 通过prop传递给basicConfig，只在最外层维护一份，不额外维护
        // 由于这段逻辑只有fcdn会用到，因此在a1->基础配置入口时，showForStaticAndIcdn默认为false
        showForStaticAndIcdn() {
            // fcdn，静态、全站产品, 不区分新旧框架
            // 静态类：下载加速、静态加速、cdn加速、下载闲时、点播
            // 全站类： 全站加速-上传加速"、全站加速、全站加速-websocket加速
            // 这里使用defaultData是因为访问控制放在basicConfig里面，temp_domain_detail读取不到
            return (
                !window.__POWERED_BY_QIANKUN__ &&
                ["001", "003", "008", "014", "004", "006"].includes(this.temp_domain_detail?.product_code)
            );
        },
        // 是否为新框架
        isNewEcgw() {
            return this.temp_domain_detail.use_ecgw === 1;
        },
        getActivities() {
            // fcdn 没有基础配置的概念，所以不需要进行菜单折叠，默认返回展开的菜单
            if (!this.isPoweredByQiankun) {
                return this.cdnAnchorListExpand;
            }
            return this.activeNames.includes("1") ? this.cdnAnchorListExpand : this.cdnAnchorListCollapse;
        },
        dynamicAccelerateEnable() {
            return SecurityAbilityModule.dynamicAccelerateEnable;
        },
        securityDomain() {
            return SecurityAbilityModule.securityDomain;
        },
        securityDomainStatus() {
            return SecurityAbilityModule.securityDomainStatus;
        },
        dynamicContent() {
            return this.$t("domain.create.tip7");
        },
        isEdit() {
            return SecurityAbilityModule.isEdit;
        },
        collapseTitle() {
            return this.activeNames.includes("1")
                ? this.$t("domain.editPage.tip23")
                : this.$t("domain.editPage.tip24");
        },
        isSecurityFormSame() {
            return SecurityAbilityModule.isSecurityFormSame;
        },
        // 域名是否启用中
        isService() {
            const status = get(SecurityAbilityModule.securityDomainInfo, "domainStatus");
            return status === "NORMAL";
        },
        // 是否域名新增中
        isAddingDomain() {
            return get(SecurityAbilityModule.securityDomainInfo, "workStatus") === 2;
        },
        // 非常规提示语
        currentAbnormalText() {
            if (this.isAddingDomain) {
                return this.$t("domain.editPage.tip14");
            }

            return this.$t("domain.editPage.tip1");
        },
        // 缓存参数功能锁
        isLockCacheKeyArgs() {
            return this.funcName?.includes("cache_key_args");
        },
        // 缓存uri功能锁：存在cache_key_uri字段则不允许修改配置
        isLockCacheKeyUri() {
            return this.funcName?.includes("cache_key_uri");
        },
        // 缓存host配置功能锁: 前端判断，如果选择的不是同类型&已启用&更新中的域名，则锁住配置
        isLockSharedHost() {
            if (this.sharedHostCanChooseList?.length && this.temp_domain_detail.shared_host) {
                return !this.sharedHostCanChooseList.find(itm => {
                    return itm.domain === this.temp_domain_detail.shared_host;
                });
            }

            if (!this.temp_domain_detail.shared_host) {
                return false;
            }

            return true;
        },
        // mp4拖拉 功能锁
        isLockMp4() {
            return this.funcName?.includes("mp4time_seek");
        },
        // flv拖拉 功能锁
        isLockFlv() {
            return this.funcName?.includes("flv_info");
        },
        // 缓存过期时间 / 缓存配置: 功能锁
        isLockCacheTime() {
            return this.funcName?.includes("cache_time");
        },
        // URL重定向自助: 功能锁
        isLockDefineRedirect() {
            return this.funcName?.includes("define_redirect");
        },
        // 错误页面重定向: 功能锁 errorPage
        isLockErrorPage() {
            return this.funcName?.includes("error_page");
        },

        isStaticsAbilityOn() {
            return this.form.statics_ability === "on";
        },
        isDomainCorrect() {
            return this.securityDomain === this.form.domain;
        },
        editConfigContent() {
            // 如果 isBillingSuccess 为false，并且是已启用，就要用提示：获取业务中心能力开关失败，请重新刷新页面。
            if (!this.isBillingSuccess && this.isService) {
                return this.$t("domain.editPage.tip15");
            }
            return this.$t("domain.editPage.tip16");
        },
        // 是否从乾坤入口进：true：是；false：否
        isPoweredByQiankun() {
            return window.__POWERED_BY_QIANKUN__;
        },
        temp_domain_type() {
            return SecurityAbilityModule.domain_type;
        },
        // isBillingSuccess 为 true，表示该域名有：websocket 动态 ipv6 等套餐的能力，如果为 false，编辑配置按钮就需要置灰不可编辑，并且提示：获取业务中心能力开关失败，请重新刷新页面。
        isBillingSuccess() {
            return this.isPoweredByQiankun ? SecurityAbilityModule.isBillingSuccess : true;
        },
        is_show_origin_type() {
            return !this.isPoweredByQiankun && this.temp_domain_detail.product_code === "006" && !this.isVivo;
        },
        is_VOD() {
            return this.temp_domain_detail.product_code === "005";
        },
    },
    watch: {
        securityDomain: {
            async handler(val) {
                this.isDomainChange = true;
                if (!val) {
                    this.form = this.$options?.data()?.form;
                    SecurityAbilityModule.SET_SECURITY_ORIGIN_FORM(cloneDeep(this.form));
                    SecurityAbilityModule.SET_SECURITY_CURRENT_FORM(cloneDeep(this.form));
                    const baseInfo = [
                        { label: this.$t("domain.create.domainName"), value: "-" },
                        { label: "CNAME", value: "-" },
                        {
                            label: this.$t(
                                window.__POWERED_BY_QIANKUN__
                                    ? "domain.editPage.label2"
                                    : "domain.create.acceType"
                            ),
                            value: "-",
                        },
                        {
                            label: this.$t(
                                window.__POWERED_BY_QIANKUN__
                                    ? "domain.create.serviceArea"
                                    : "domain.list.tableLabel4"
                            ),
                            value: "-",
                        },
                        { label: this.$t("domain.list.tableLabel6"), value: "-" },
                    ];
                    this.baseInfo = baseInfo;
                    return;
                }
                // 可插拔配置-加载配置项状态，需要在获取域名之后触发
                this.loadConfigModules(val); // 可插拔配置
                this.getDomainDetail();
                const isEditFromCdn = this.$route?.query?.opType === "edit";
                if (!isEditFromCdn) {
                    SecurityAbilityModule.SET_IS_EDIT(false);
                }
            },
            immediate: true,
        },
        securityDomainStatus: {
            deep: true,
            async handler(newVal, oldVal) {
                if (this.isEdit || this.isDomainChange) {
                    this.isDomainChange = false;
                    return;
                }
                if (newVal !== oldVal && oldVal !== undefined) {
                    this.getDomainDetail();
                }
            },
            immediate: true,
        },
        form: {
            deep: true,
            handler(val) {
                // if (this.isAddingDomain) {
                //     return;
                // }
                SecurityAbilityModule.SET_SECURITY_CURRENT_FORM(val);
            },
            immediate: true,
        },
        // 通知主应用
        isSecurityFormSame() {
            const data = {
                icChange: !this.isSecurityFormSame,
                fn: this.handleSubmit,
            };
            window.custom.emit("handleCDNConfigChange", data);
            SecurityAbilityModule.SET_IS_CDN_FIX_SECURITY_CHANGE(!this.isSecurityFormSame);
            SecurityAbilityModule.SET_SECURITY_MODULE({ handleSubmit: data?.fn });
        },
        workOrderVisible: {
            deep: true,
            handler(val) {
                if (!val) {
                    this.existOrderLoading = false;
                    this.handleClearTimer();
                    this.handleClearOrderTimer();

                    if (this.isQueryDomainDetail) {
                        setTimeout(() => {
                            this.getDomainDetail();
                        }, 3100);
                    }

                    return;
                }
            },
            immediate: true,
        },
        // 点击保存 (handleUpdateDomain) 后，弹窗，点击确定，调用域名更新接口 (handleUpdate)，
        // 这时候需要调用查询在途工单接口 (handleExistOrder) ，如果返回false，需要每隔1秒调用一次：查询在途工单接口，
        // 如果查在途工单接口一直返回false，10秒后自动关闭弹窗，不再调用接口
        // 如果查在途工单接口返回true，自动关闭窗口，不再调用接口
        // 查在途工单接口：true：代表有在途工单；false：代表无在途工单
        is_exist: {
            deep: true,
            handler(val) {
                if (val === null) return;
                this.handleRefresh(val);
            },
            immediate: true,
        },
        temp_domain_type: {
            deep: true,
            async handler(val) {
                this.$set(this.form, "domain_type", val);
            },
            immediate: true,
        },
    },
    mounted() {
        if (!this.isPoweredByQiankun) {
            this.activeNames = ["1"]; // fcdn入口 基础配置默认展开。
            this.getDomainIsVivo();
        }

        this.initEditConfBtn();
        this.keepUrlToAnchor = true; // 设置状态，保持url跳转锚点
        this.fromUrlToAnchor();

        this.calculateContainerTop(true);
        const scroller = document.querySelector(".ct-config");
        if (!scroller) {
            return;
        }

        scroller.addEventListener("scroll", this.handleScroller);
    },
    updated() {
        // 更新 anchorHelperBlock 的高度
        this.updateAnchorHeight();
        this.fromUrlToAnchor();
    },
    beforeDestroy() {
        this.getDomainDetailAbortController?.abort("cancel by user");

        const scroller = document.querySelector(".ct-config");
        if (!scroller) {
            return;
        }

        scroller.removeEventListener("scroll", this.handleScroller);
        scroller.removeEventListener("scroll", this.setKeepUrlToAnchor);
        this.handleClearTimer();
    },
    methods: {
        goBackToCreateDomain() {
            this.$router.push({ name: "ndomain.create" });
        },
        updateChild(val) {
            this.childKey = val;
        },
        updateAnchorChild(val) {
            this.anchorChildKey = val;
        },
        updateAnchorHeight() {
            this.$nextTick(() => {
                const lastLabelDom = document.querySelector(
                    this.getActivities[this.getActivities.length - 1]?.prop
                );
                if (!lastLabelDom) return;
                const contentAreaHeight = document.querySelector(".content-wrap").offsetHeight;
                const viewAreaHeight = document.querySelector(".ct-config").offsetHeight;
                const emptyBlock = document.querySelector(".anchor-helper-block-2");

                const height =
                    viewAreaHeight - (contentAreaHeight - lastLabelDom.offsetTop) + emptyBlock.offsetHeight;
                emptyBlock.style.height = `${height < 0 ? 0 : height}px`;
                // const emptyBlock = document.querySelector(".anchor-helper-block-2");
                // if (!window.__POWERED_BY_QIANKUN__) {
                //     emptyBlock.style.height = 0;
                //     return;
                // }
                // emptyBlock.style.height = "338px";
            });
        },
        setKeepUrlToAnchor() {
            !this.detailLoading && (this.keepUrlToAnchor = false); // 解除url跳转锚点
        },
        handleScroller(e) {
            this.$refs.scrollbar.update();
            const ele = e.target;
            const scrollTop = ele.scrollTop; // 容器滚动的距离
            const anchor = this.$refs.anchor.$el;
            // const positionInfo = anchor.getBoundingClientRect();
            // 当前锚点距离窗口的距离
            // const left = get(positionInfo, "left", 0);
            // 锚点动态样式
            if (scrollTop >= this.containerToTop && this.containerToTop > 0) {
                anchor.style.position = "fixed";
                anchor.style.top = this.isPoweredByQiankun
                    ? `${this.containerToTop + 5}px`
                    : `${this.containerToTop + 60}px`;
                // anchor.style.left = left + "px";
                anchor.style.right = this.isPoweredByQiankun ? "30px" : "45px";

                return;
            }

            anchor.style.position = "relative";
            anchor.style.top = 0;
            // anchor.style.left = 0;
            anchor.style.right = "5px";
        },
        /**
         * 根据url跳转到对应的锚点
         * 1、由于每次渲染页面的布局都有可能发生变化，会影响跳转的准确性，在每次updated后调用该功能
         * 2、用户手动scroll也会触发updated，使用keepUrlToAnchor来判断是否是用户行为
         * 3、锚点自动跳转时，也会触发setKeepUrlToAnchor，需要跳转前后进行卸载安装
         *  */
        fromUrlToAnchor() {
            if (!this.keepUrlToAnchor) return;
            const anchor = window.location.hash?.split("anchor=")[1]?.split("&")[0];
            this.anchorInterval && clearInterval(this.anchorInterval);
            if (anchor) {
                const scroller = document.querySelector(".ct-config");
                scroller.removeEventListener("scroll", this.setKeepUrlToAnchor);
                if (this.$refs.anchor) {
                    this.$refs.anchor.handleScrollToTarget({ prop: `#${anchor}` });
                    clearInterval(this.anchorInterval);
                }
                this.anchorInterval = setTimeout(() => {
                    scroller.addEventListener("scroll", this.setKeepUrlToAnchor);
                }, 1000);
            }
        },
        editConfig() {
            if (!this.isDomainCorrect) {
                this.$message.warning(this.$t("domain.editPage.domainFailTip"));
                return;
            }

            // fcdn 详情页点击编辑
            if (!this.isPoweredByQiankun && this.$route?.name === "ndomain.detail") {
                const { creating, status } = this.$route?.query;
                this.$router.push({
                    name: "ndomain.edit",
                    query: { domain: this.form.domain, opType: "edit", creating, status },
                });
                SecurityAbilityModule.SET_IS_EDIT(true);
                return;
            }

            const isEdit = true;
            SecurityAbilityModule.SET_IS_EDIT(isEdit);
        },
        initEditConfBtn() {
            let isEdit = false;
            if (this.$route?.query?.opType === "edit") {
                isEdit = true;
            }
            SecurityAbilityModule.SET_IS_EDIT(isEdit);
        },
        async validCacheKeyUriPattern(rule, value, callback) {
            if ((value === "" || value === null || value === undefined) && this.isStaticsAbilityOn) {
                return callback(new Error(this.$t("domain.detail.placeholder6")));
            }
            return callback();
        },

        // 处理基础信息
        handleBaseInfo(data) {
            // 处理加速区域
            const baseInfo = [
                { label: this.$t("domain.create.domainName"), value: data?.domain || "-" },
                { label: "CNAME", value: data?.cname || "-" },
                {
                    label: this.$t(
                        window.__POWERED_BY_QIANKUN__ ? "domain.editPage.label2" : "domain.create.acceType"
                    ),
                    value: this.productCodeLabel(data?.product_type || data?.product_code) || "-",
                },
                {
                    label: this.$t(
                        window.__POWERED_BY_QIANKUN__
                            ? "domain.create.serviceArea"
                            : "domain.list.tableLabel4"
                    ),
                    value: this.areaScopeLabel(data?.area_scope) || "-",
                },

                { label: this.$t("domain.list.tableLabel6"), value: data?.create_time || "-" },
                // { label: this.$t("domain.detail.label5"), isShow: true },
            ];
            if (
                !window.__POWERED_BY_QIANKUN__ &&
                !baseInfo.some(item => {
                    item.label === "域名类型" || item.label === "Domain Type";
                }) &&
                ["006", "104", "105"].includes(this.form.domain_type)
            ) {
                const obj = { label: this.$t("domain.detail.label5"), isShow: true };
                baseInfo.splice(1, 0, obj);
            }
            this.baseInfo = cloneDeep(baseInfo);
        },
        init(val) {
            if (!val) return;
            if (this.securityDomain !== val?.domain) return;
            this.$nextTick(() => {
                const thisData = cloneDeep(val);
                this.temp_domain_detail = cloneDeep(val);

                // 可插拔配置-在原有初始化之前,先合并可配置模块的字段
                this.mergeConfigModuleFields();

                // 域名详情接口如果没有返回 backorigin_mode 字段，前端需要将 backorigin_mode 设置为默认值：0
                if (
                    this.temp_domain_detail?.backorigin_mode === "" ||
                    this.temp_domain_detail?.backorigin_mode === null ||
                    this.temp_domain_detail?.backorigin_mode === undefined
                ) {
                    this.$set(this.temp_domain_detail, "backorigin_mode", 0);
                }
                Object.assign(this.$data, { ...thisData });
                // 域名类型
                const domain_type = thisData.domain_type || "";
                this.form.domain_type = domain_type;
                SecurityAbilityModule.SET_DOMAIN_TYPE(domain_type);

                this.handleBaseInfo(cloneDeep(thisData));
                this.form.domain = thisData.domain;
                // 静态加速开关
                let statics_ability = "on";
                if (this.isPoweredByQiankun) {
                    statics_ability = ["on", "off"].includes(thisData?.statics_ability)
                        ? thisData?.statics_ability
                        : "on";
                }
                this.$set(this.form, "statics_ability", statics_ability);
                // 缓存配置
                if (thisData.filetype_ttl && thisData.filetype_ttl.length > 0) {
                    this.initTimeFormat(thisData.filetype_ttl, "filetype_ttl");
                } else {
                    this.$set(this.form, "filetype_ttl", []);
                }
                // 状态码缓存
                if (thisData.error_code && thisData.error_code.length > 0) {
                    this.initTimeFormat(thisData.error_code, "error_code");
                } else {
                    this.$set(this.form, "error_code", []);
                }
                // 缓存URI
                if (thisData.cachekey_uri && thisData.cachekey_uri.length > 0) {
                    this.$set(this.form, "cachekey_uri", thisData.cachekey_uri);
                } else {
                    this.$set(this.form, "cachekey_uri", []);
                }

                this.charge_url = thisData?.charge_url || "https://www.ctyun.cn/document/10065985/10198142";

                // 访问URL重定向
                let define_redirect = [];
                if (thisData.define_redirect && thisData.define_redirect.length > 0) {
                    define_redirect = cloneDeep(thisData.define_redirect);
                } else {
                    define_redirect = [];
                }

                const temp_define_redirect = cloneDeep(define_redirect);
                if (
                    thisData?.define_redirect_condition &&
                    Object.keys(thisData?.define_redirect_condition).length > 0
                ) {
                    temp_define_redirect.map(item => {
                        if (thisData?.define_redirect_condition[item?.id]) {
                            item.mode = thisData?.define_redirect_condition[item.id][0]?.mode;
                            item.content = thisData?.define_redirect_condition[item.id][0]?.content;
                        }
                    });
                }
                this.form.define_redirect = cloneDeep(temp_define_redirect);
                this.temp_define_redirect = cloneDeep(temp_define_redirect);

                // 错误页面重定向
                if (thisData.error_page && thisData.error_page.length > 0) {
                    this.$set(this.form, "error_page", thisData.error_page);
                } else {
                    this.$set(this.form, "error_page", []);
                }

                // 乾坤入口进来，才需要回填数据
                let origin_type = "fastest_simple"; // CDN入口：域名详情接口有返回值就展示对应的值，没返回就默认"fastest_simple"
                if (this.isPoweredByQiankun) {
                    // 动态配置
                    this.form.dynamic_ability = thisData?.dynamic_ability;
                    // 选路方式
                    this.form.dynamic_config.route_type = thisData?.dynamic_config?.route_type;
                    this.form.dynamic_config.probe_url = thisData?.dynamic_config?.probe_url;
                    // 动态回源策略
                    origin_type = "poll"; // AOCDN入口：域名详情接口有返回值就展示对应的值，没返回就默认"fastest_simple"
                }
                if (this.isShowUploadSpeed) {
                    // 上传加速
                    this.form.upload_speed = thisData?.upload_speed || 0;
                }
                // 动态回源策略
                if (thisData?.origin_policy) {
                    // 因为历史数据包含一些旧内容(retry_codes之类的字段)，这里全部捞过来赋值，保存时回传
                    this.form.origin_policy = { ...thisData?.origin_policy };
                }
                this.form.origin_policy.http_config.origin_type =
                    thisData?.origin_policy?.http_config?.origin_type || origin_type;
                // 处理：缓存key-缓存参数
                const temp_cachekey_args = cloneDeep(thisData?.cachekey_args) || [];
                // 详情接口返回的cachekey_args_condition，如果id和cachekey_args数组的id匹配，将cachekey_args_condition里面的mode和content赋值给cachekey_args里面的mode和content，否则置为null
                if (
                    thisData?.cachekey_args_condition &&
                    Object.keys(thisData?.cachekey_args_condition).length > 0
                ) {
                    temp_cachekey_args.map(item => {
                        if (thisData?.cachekey_args_condition[item?.id]) {
                            item.content = thisData?.cachekey_args_condition[item.id][0]?.content;
                            item.typeMode = thisData?.cachekey_args_condition[item.id][0]?.mode;
                        } else if (!thisData?.cachekey_args_condition[item?.id]) {
                            item.content = null;
                            item.typeMode = null;
                        }
                    });
                }
                let cachekey_args_arr = [];
                cachekey_args_arr = temp_cachekey_args.map(item => {
                    if (item?.ignore === 0 && item?.is_with_args === 0) {
                        return {
                            ignore_params: 1,
                            ...item,
                            mode: item?.typeMode,
                        };
                    } else if (
                        item?.ignore === 1 &&
                        (item?.ignore_args === "" || item?.ignore_args === null)
                    ) {
                        return {
                            ignore_params: 2,
                            ...item,
                            mode: item?.typeMode,
                        };
                    } else if (item?.ignore === 0 && item?.mode === 0 && item?.args) {
                        return {
                            ignore_params: 3,
                            ...item,
                            args: item?.args?.substring(1),
                            mode: item?.typeMode,
                        };
                    } else if (item?.ignore === 1 && item?.ignore_args) {
                        return {
                            ignore_params: 4,
                            ...item,
                            args: item?.ignore_args,
                            mode: item?.typeMode,
                        };
                    } else {
                        return {
                            ignore_params: null,
                            ...item,
                            args: item?.ignore_args,
                            mode: item?.typeMode,
                        };
                    }
                });
                this.form.cachekey_args = cloneDeep(cachekey_args_arr);
                this.cachekey_args_condition = cloneDeep(thisData?.cachekey_args_condition) || {};
                // mp4
                let mp4_conf_info = {};
                if (thisData.mp4_conf_info && Object.keys(thisData.mp4_conf_info).length > 0) {
                    mp4_conf_info = thisData.mp4_conf_info;
                    if (
                        thisData.mp4_conf_info_condition &&
                        Object.keys(thisData.mp4_conf_info_condition).length > 0
                    ) {
                        mp4_conf_info.mode = thisData?.mp4_conf_info_condition["mp4_time_delay"][0]?.mode;
                        mp4_conf_info.content =
                            thisData?.mp4_conf_info_condition["mp4_time_delay"][0]?.content;
                    } else {
                        mp4_conf_info.mode = null;
                        mp4_conf_info.content = null;
                    }
                } else {
                    mp4_conf_info = {
                        switch: 0,
                        mode: 0,
                        content: "",
                        start_params: "",
                        end_params: "",
                    };
                }
                this.form.mp4_conf_info = cloneDeep(mp4_conf_info);
                // flv
                let cus_flv_info = [];
                if (thisData.cus_flv_info && thisData.cus_flv_info.length > 0) {
                    this.flv_info_switch = true;
                    cus_flv_info = thisData.cus_flv_info;
                } else {
                    this.flv_info_switch = false;
                    cus_flv_info = [];
                }

                const temp_cus_flv_info = cloneDeep(cus_flv_info);
                if (
                    thisData?.cus_flv_info_condition &&
                    Object.keys(thisData?.cus_flv_info_condition).length > 0
                ) {
                    temp_cus_flv_info.map(item => {
                        if (thisData?.cus_flv_info_condition[item?.id]) {
                            item.mode = thisData?.cus_flv_info_condition[item.id][0]?.mode;
                            item.content = thisData?.cus_flv_info_condition[item.id][0]?.content;
                        }
                    });
                }
                this.form.cus_flv_info = cloneDeep(temp_cus_flv_info);

                // 用于自助功能锁
                this.funcName = thisData?.funcName || [];
                // 可插拔配置-同步功能锁数据到 store
                ConfigModulesModule.SET_FUNC_LOCKS(this.funcName);

                // 加速区域 透传
                this.form.area_scope = thisData?.area_scope;

                // udfScript
                if (!this.isPoweredByQiankun) {
                    // udfScript
                    const { script = [] } = thisData;
                    this.form.script = cloneDeep(script);
                }

                // 可插拔配置-初始化可配置模块的数据
                this.initConfigModules(thisData);

                SecurityAbilityModule.SET_SECURITY_ORIGIN_FORM(cloneDeep(this.form));
                SecurityAbilityModule.SET_SECURITY_CURRENT_FORM(cloneDeep(this.form));
            });
        },
        initTimeFormat(input, param) {
            if (input) {
                const cacheList = cloneDeep(input);
                const timeStepArr = Object.keys(CacheTtlMap).map(mode => CacheTtlMap[mode].factor);
                const timeStepMap = Object.keys(CacheTtlMap);
                if (param === "filetype_ttl") {
                    this.form.filetype_ttl = cacheList.map(i => {
                        const stepFindIndex = timeStepArr.findIndex(s => {
                            return parseInt(i.ttl, 10) % s === 0;
                        });
                        // 读取初始数据
                        return {
                            mode: i.mode,
                            file_type: i.file_type,
                            ttl: parseInt(i.ttl, 10) / timeStepArr[stepFindIndex],
                            cache_with_args: i.cache_with_args,
                            priority: i.priority || 10,
                            timeType: timeStepMap[stepFindIndex],
                            time: parseInt(i.ttl, 10) / timeStepArr[stepFindIndex],
                            cache_type: i.cache_type,
                            split: 0,
                        };
                    });
                }
                if (param === "error_code") {
                    this.form.error_code = cacheList.map(i => {
                        const stepFindIndex = timeStepArr.findIndex(s => {
                            return parseInt(i.ttl, 10) % s === 0;
                        });
                        // 读取初始数据
                        return {
                            code: i.code,
                            ttl: parseInt(i.ttl, 10) / timeStepArr[stepFindIndex],
                            timeType: timeStepMap[stepFindIndex],
                        };
                    });
                }
            }
        },
        clearValidation() {
            try {
                this.$refs.basicConfig.$children[0].$refs.form.clearValidate();
                this.$refs.form.clearValidate();
            } catch (e) {
                console.log(e);
            }
        },
        async handleCancel() {
            if (this.isSecurityFormSame) {
                SecurityAbilityModule.SET_IS_EDIT(false);
                this.clearValidation();
            } else {
                await this.$confirm(this.$t("domain.editPage.tip13"), this.$t("common.messageBox.title"), {
                    confirmButtonText: this.$t("common.dialog.submit"),
                    cancelButtonText: this.$t("common.dialog.cancel"),
                    type: "warning",
                }).then(() => {
                    if (!this.isSecurityFormSame) {
                        SecurityAbilityModule.SET_IS_EDIT(false);
                        this.getDomainDetail();
                        this.clearValidation();
                    } else {
                        this.clearValidation();
                    }
                });
            }
        },
        renderFormValidate() {
            this.validateForm = [];
            // 获取基础配置的表单校验
            this.validateForm.push(this.$refs.basicConfig?.$children[0]?.$refs?.form);
            // 获取CDN加速配置父组件的表单校验
            this.validateForm.push(this.$refs.form);
            // 获取基础配置子组件的表单校验
            this.$refs.basicConfig?.$children[0]?.validateComponents?.forEach(itm => {
                this.validateForm.push(
                    this.$refs.basicConfig?.$children[0]?.$refs?.[`${itm}`]?.$refs[`${itm}Form`]
                );
                this.validateForm.push(...this.$refs.basicConfig?.$children[0]?.renderConfigModuleValidate());
            });

            // 获取子组件表单校验
            if (this.isStaticsAbilityOn) {
                // 获取mp4表单校验
                if (this.isNewEcgw && this.validateComponents.indexOf("mp4") < 0) {
                    this.validateComponents.push("mp4");
                }
                // 获取flv表单校验
                if (this.isNewEcgw && this.validateComponents.indexOf("flv") < 0) {
                    this.validateComponents.push("flv");
                }
                // 获取状态码缓存表单校验
                if (this.validateComponents.indexOf("errorCode") < 0) {
                    this.validateComponents.push("errorCode");
                }
                // 获取缓存URI表单校验
                if (this.validateComponents.indexOf("cacheKeyUri") < 0) {
                    this.validateComponents.push("cacheKeyUri");
                }
            } else {
                this.validateComponents = [];
            }

            if (this.showForStaticAndIcdn) {
                if (this.isNewEcgw && this.validateComponents.indexOf("errorPage") < 0) {
                    this.validateComponents.push("errorPage");
                }
            } else {
                this.validateComponents = this.validateComponents?.filter(item => item !== "errorPage");
            }

            // 抽离成子组件后，需要将子组件中的表单校验加入到父组件的表单校验中
            this.validateComponents.forEach(name => {
                this.validateForm.push(this.$refs[name]?.$refs[`${name}Form`]);
            });

            // 可插拔配置-添加可配置模块的表单校验
            this.validateForm.push(...this.renderConfigModuleValidate());
        },
        restructureData(data) {
            const params = cloneDeep(data);
            if (params.statics_ability === "off") {
                params.filetype_ttl = [];
                delete params?.error_code;
                delete params?.cachekey_args;
                delete params?.cachekey_args_condition;
                delete params?.cachekey_uri;

                delete params?.mp4_conf_info;
                delete params?.mp4_conf_info_condition;

                delete params?.cus_flv_info;
                delete params?.cus_flv_info_condition;
            }
            return params;
        },
        // 点击弹窗的 提交保存 按钮时，触发事件
        async handleSubmit(resolve, reject) {
            // 调用基础配置的renderFormValidate方法，主要用于获取基础配置的子组件中有条件判断的表单校验，比如：websocket 子组件表单校验
            this.$refs.basicConfig?.$children[0]?.renderFormValidate();
            this.renderFormValidate();
            try {
                await Promise.all(this.validateForm.map(itm => formValidate2Promise(itm)));
            } catch (err) {
                this.$message({
                    showClose: true,
                    message: err.message || err,
                    type: "error",
                });
                reject && reject(false);
                return false;
            }
            this.$message.warning(this.$t("domain.detail.placeholder33"));
            let updateForm = {};
            updateForm = cloneDeep(this.formData);
            try {
                this.detailLoading = true;
                this.submitLoading = true;
                Object.assign(updateForm, this.$refs.basicConfig?.$children[0]?.formData);
                // 静态加速开关关闭时，需要清空缓存配置
                const param = this.restructureData(updateForm);

                await this.$ctFetch(siteUrl.domainUpdate, {
                    method: "POST",
                    transferType: "json",
                    clearEmptyParams: false,
                    body: {
                        ...param,
                        ...this.$refs.basicConfig?.$children[0]?.getApiData(),
                        ...this.getApiData(), // 可插拔配置-合并可配置模块的数据
                    },
                });
                this.$message.success(this.$t("domain.create.tip27"));
                const isEdit = false;
                SecurityAbilityModule.SET_IS_EDIT(isEdit);
                SecurityAbilityModule.SET_SECURITY_RENDER_KEY();
                resolve && resolve(true);
            } catch (e) {
                this.submitLoading = false;
                this.detailLoading = false;
                reject && reject(false);

                const code = e?.data?.code || "";
                if ([CODE_SERVER_PORT_ERROR_2364, CODE_SERVER_PORT_ERROR_2367].includes(code)) {
                    this.serverPortTipVisible = true;
                    this.serverPortErrorCode = code;
                } else {
                    this.$errorHandler(e);
                }
            } finally {
                this.submitLoading = false;
                this.detailLoading = false;
            }
        },

        submitWorkOrder() {
            this.handleUpdate();
        },
        // 点击页面右下角：保存 按钮时，触发事件
        async handleUpdateDomain(resolve, reject) {
            // 调用基础配置的renderFormValidate方法，主要用于获取基础配置的子组件中有条件判断的表单校验，比如：websocket 子组件表单校验
            this.$refs.basicConfig?.$children[0]?.renderFormValidate();
            this.renderFormValidate();

            try {
                await Promise.all(this.validateForm.map(itm => formValidate2Promise(itm)));
            } catch (err) {
                console.log("err", err);
                this.$message({
                    showClose: true,
                    message: err.message || err,
                    type: "error",
                });
                reject && reject(false);
                return false;
            }
            this.existOrderLoading = false;
            this.workOrderVisible = true;
            this.isQueryDomainDetail = false;
        },
        async handleUpdate(resolve, reject) {
            let updateForm = {};
            updateForm = cloneDeep(this.formData);
            try {
                this.detailLoading = true;
                this.submitLoading = true;
                this.existOrderLoading = true;
                // 重置is_exist，确保每次请求弹窗会在10s内关闭
                this.is_exist = null;
                // const updateForm = cloneDeep(this.form);
                Object.assign(updateForm, this.$refs.basicConfig?.$children[0]?.formData);
                // 静态加速开关关闭时，需要清空缓存配置
                const param = this.restructureData(updateForm);
                await this.$ctFetch(siteUrl.domainUpdate, {
                    method: "POST",
                    transferType: "json",
                    clearEmptyParams: false,
                    body: {
                        ...param,
                        ...this.$refs.basicConfig?.$children[0]?.getApiData(),
                        ...this.getApiData(), // 可插拔配置-合并可配置模块的数据
                    },
                });
                this.handleExistOrder();
                this.handleOrderRefresh();
                this.$message.success(this.$t("domain.create.tip27"));
                const isEdit = false;
                SecurityAbilityModule.SET_IS_EDIT(isEdit);
                resolve && resolve(true);

                // fcdn保存成功后需要跳转到域名列表
                if (!window.__POWERED_BY_QIANKUN__) {
                    setTimeout(() => {
                        this.$router.push({
                            name: "ndomain.list",
                        });
                    }, 1000);
                }
            } catch (e) {
                this.submitLoading = false;
                this.detailLoading = false;
                this.workOrderVisible = false; // 域名更新接口如果返回失败，需要关闭弹窗
                // this.isQueryDomainDetail = true;

                // 如果返回的code是这三种：clnt.e2364 clnt.e2367，不需要调用errorHandler，只需要展示另一个弹窗，即：serverPortTipVisible = true
                const code = e?.data?.code || "";
                if ([CODE_SERVER_PORT_ERROR_2364, CODE_SERVER_PORT_ERROR_2367].includes(code)) {
                    this.serverPortTipVisible = true;
                    this.serverPortErrorCode = code;
                } else {
                    this.$errorHandler(e);
                }
                reject && reject(false);
            } finally {
                this.submitLoading = false;
                this.detailLoading = false;
            }
        },
        async handleExistOrder() {
            try {
                const res = await this.$ctFetch(siteUrl.isExistOrder, {
                    method: "GET",
                    transferType: "json",
                    data: {
                        domain: this.securityDomain,
                    },
                });
                if (res) {
                    // 是否有在途工单：true表示有在途工单；false表示没有在途工单，即工单已完结
                    this.is_exist = res?.is_exist;
                    // this.handleRefresh(res?.is_exist);
                    if (res?.is_exist === true) {
                        this.workOrderVisible = false;
                        this.isQueryDomainDetail = true;
                        // SecurityAbilityModule.SET_SECURITY_RENDER_KEY(); // 该行代码作用：生命周期会重新走一遍，然后重新调用获取数据的接口
                    }
                }
            } catch (error) {
                this.$errorHandler(error);
            }
        },
        submitServerPortTip() {
            this.serverPortTipVisible = false;
        },
        cancel(dialogKey) {
            this[dialogKey] = false;
        },
        async getDomainDetail() {
            if (!this.securityDomain) return;
            try {
                this.getDomainDetailAbortController?.abort("cancel by user");
                this.getDomainDetailAbortController = new AbortController();
                this.detailLoading = true;
                SecurityAbilityModule.SET_IS_DOMAIN_DETAIL_LOADING(true);
                const res = await this.$ctFetch(siteUrl.domainDetail, {
                    method: "GET",
                    transferType: "json",
                    data: {
                        domain: this.securityDomain,
                    },
                    signal: this.getDomainDetailAbortController.signal,
                });
                // 回填数据
                if (res) {
                    this.defaultData = cloneDeep(res);
                    this.init(res);

                    const isEditFromCdn = this.$route?.query?.opType === "edit";
                    if (!isEditFromCdn) {
                        SecurityAbilityModule.SET_IS_EDIT(false);
                    }

                    this.$ctBus.$emit("accelerationConfig:domain:status", `${res?.status}`);
                }
            } catch (error) {
                if (error.name === "AbortError" || error.raw === "cancel by user") {
                    return;
                }

                SecurityAbilityModule.SET_IS_DOMAIN_DETAIL_LOADING(false);
                await this.handleDefaultValue();
                SecurityAbilityModule.SET_SECURITY_ORIGIN_FORM(cloneDeep(this.form));
                SecurityAbilityModule.SET_SECURITY_CURRENT_FORM(cloneDeep(this.form));

                this.$errorHandler(error);
            }

            this.detailLoading = false;
            SecurityAbilityModule.SET_IS_DOMAIN_DETAIL_LOADING(false);
            // detail接口调用完成后计算，避免浏览器回退导致计算错误
            this.calculateContainerTop(true);
            this.$ctBus.$emit("refershPage");
        },
        async handleDefaultValue() {
            // 接口报错时，基础配置的所有表单项需要设置为默认值
            const errorBasicConfigForm = this.$refs.basicConfig?.$children[0]?.$options?.data()?.form;
            this.defaultData = cloneDeep(errorBasicConfigForm);
            // 接口报错时，CDN加速配置的所有表单项需要设置为默认值
            this.form = this.$options?.data()?.form;
            // 接口报错时，基础信息的表单项需要设置为默认值
            this.handleBaseInfo();
        },
        dynamic_ability_change(val) {
            if (val === "off") {
                this.form.dynamic_config.route_type = 2;
                this.$set(this.form.origin_policy.http_config, "origin_type", "poll");
            } else {
                const originalConf = SecurityAbilityModule.securityOriginForm;
                if (originalConf.dynamic_ability === "on") {
                    this.form.dynamic_config.route_type = originalConf.dynamic_config.route_type;
                    this.$set(this.form.origin_policy.http_config, "origin_type", originalConf.origin_policy.http_config.origin_type);
                }
            }
        },
        onMp4Change(v) {
            this.form.mp4_conf_info.switch = v.switch;
            this.form.mp4_conf_info.mode = v.mode;
            this.form.mp4_conf_info.content = v.content;
            this.form.mp4_conf_info.start_params = v.start_params;
            this.form.mp4_conf_info.end_params = v.end_params;
        },
        onFlvChange(v, visible) {
            this.form.cus_flv_info = cloneDeep(v);
            this.flv_info_switch = !!visible;
        },
        // 缓存配置子组件传值给父组件
        onFiletypeTtlChange(v) {
            const data = v || [];
            this.form.filetype_ttl = cloneDeep(data);
        },
        // URL重定向传值给父组件
        onDefineRedirectChange(v) {
            const data = v || [];
            this.form.define_redirect = cloneDeep(data);
        },
        onStaticsAbilityChange(v) {
            const originalConf = SecurityAbilityModule.securityOriginForm;
            this.form.filetype_ttl = cloneDeep(originalConf.filetype_ttl);
            this.form.error_code = cloneDeep(originalConf.error_code);
            this.form.cachekey_args = cloneDeep(originalConf.cachekey_args);
            this.form.cachekey_uri = cloneDeep(originalConf.cachekey_uri);
            this.form.shared_host = cloneDeep(originalConf.shared_host);
        },
        // 错误页面重定向
        onErrorPageChange(v) {
            const data = v || [];
            this.form.error_page = cloneDeep(data);
        },
        // 缓存参数子组件传值给父组件
        onCacheKeyArgsChange(v) {
            const data = v || [];
            this.form.cachekey_args = cloneDeep(data);
        },
        onCacheKeyDeleteIdList(val) {
            this.cacheKeyDeleteIdList = val;
        },
        onCacheKeyIdList(val) {
            this.cacheKeyIdList = val;
        },
        // 缓存URI子组件传值给父组件
        onCacheKeyUriChange(v) {
            const data = v || [];
            this.form.cachekey_uri = cloneDeep(data);
        },
        // 状态码缓存子组件传值给父组件
        onErrorCodeChange(v) {
            const data = v || [];
            this.form.error_code = cloneDeep(data);
        },
        validateuploadSpeedORwebsocketSpeed(rule, value, callback) {
            if (
                this.form.upload_speed === 1 &&
                this.$refs.basicConfig?.$children[0]?.form?.websocket_speed === 1
            ) {
                return callback(new Error(this.$t("domain.detail.placeholder79")));
            } else {
                return callback();
            }
        },
        upload_speed_change(val) {
            this.$ctBus.$emit("uploadSpeedChange", val);
        },
        async getDomainIsVivo() {
            const res = await this.$ctFetch(nDomainUrl.domainIsVivo);
            this.isVivo = res.vivo;
        },
    },
};
</script>

<style lang="scss" scoped>
@import "../index.scss";
.content-wrap {
    overflow: hidden;
    flex: 1;
    padding-bottom: 100px;
    min-height: 300px;
    ::v-deep {
        .el-form--label-left .el-form-item__label {
            // width: 140px !important;
            text-align: right;
        }
        // .el-form-item__content {
        //     margin-left: 140px !important;
        // }
    }
    .basic-style {
        margin-bottom: 20px !important;
    }
}
.header-tip-style {
    margin: 0 20px;
}
.base-info-style {
    margin-left: 20px !important;
}
.domain-edit {
    // overflow-y: hidden;
    overflow: auto;
    padding: 0 20px 20px 20px;
    display: flex;
    background: #fff;
    margin-bottom: 12px;
    min-width: 0;
}
.content-wrap_left {
    // flex: 1;
    width: calc(100% - 175px);
    margin-right: 20px;

    ::v-deep .el-scrollbar__wrap {
        overflow-x: hidden;
    }
}
.label-name {
    font-weight: 500;
    width: 600px;
    overflow: hidden;
    border-left: 4px solid $theme-color;
    line-height: 14px;
    padding-left: 12px;
    margin: 20px 0;
    height: 14px;
}
.mt-0 {
    margin-top: 0;
}
.input-wrapper {
    width: 360px;
}
.btn-bg {
    color: #3d73f5;
    cursor: pointer;
}
.radio-group {
    ::v-deep {
        .el-radio.is-bordered + .el-radio.is-bordered {
            margin-left: 0;
            margin-bottom: 8px;
        }

        .el-radio-button__inner {
            min-width: 150px;
        }
    }
}
.combination-box {
    position: relative;
    z-index: 10;

    .is-focus {
        z-index: 1000 !important;
    }

    .select-box {
        flex: 1.2 !important;
        min-width: 60px !important;
        ::v-deep {
            .el-input.el-input--medium .el-input__inner {
                border-top-left-radius: 0px !important;
                border-bottom-left-radius: 0px !important;
            }
        }
    }

    .input-box {
        flex: 2 !important;
        min-width: 70px !important;
        position: relative;
        left: 1px !important;
        z-index: 101;
        ::v-deep {
            .el-input__inner {
                border-top-right-radius: 0px !important;
                border-bottom-right-radius: 0px !important;
            }
        }
    }
    ::v-deep {
        .el-input.el-input--medium .el-input__inner {
            // border-top-left-radius: 0px !important;
            // border-bottom-left-radius: 0px !important;
            // border-radius: 0 !important;
            border-color: #d9d9d9 !important;
        }
        .el-select .el-input.is-focus .el-input__inner {
            border-color: #d9d9d9 !important;
        }
    }
}
</style>
