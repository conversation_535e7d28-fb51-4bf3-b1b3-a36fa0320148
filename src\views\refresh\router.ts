import { RouteConfig } from "vue-router";

const indexRouter: RouteConfig = {
    path: "/refresh",
    name: "refresh",
    component: () => import("./refresh.vue"),
    meta: {
        breadcrumb: {
            title: '$t("refresh.headerText")',
            route: ["refresh"],
        },
        perm: "refresh",
    },
    beforeEnter(to, from, next) {
        // 不阻塞页面加载
        // DomainModule.GetDomainList(DomainActionEnum.Refresh);
        next();
    },
};

export default indexRouter;
