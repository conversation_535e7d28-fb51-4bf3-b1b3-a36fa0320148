<template>
    <div class="flex-row-style time-picker">
        <div
            v-for="(item, key) in pickerOptions"
            :key="key"
            class="picker-item"
            :class="{ active: currentIndex === key, 'length-sort': pickerOptions.length === 4 }"
            :style="itemStyle(key)"
            @click="handleChange(item, key)"
        >
            {{ item.label }}
        </div>
        <template v-if="get(currentOption, 'value') === 'custom'">
            <el-date-picker
                v-model="timeAry"
                :type="customType"
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                :picker-options="timePickerOptions"
                :default-time="['00:00:00', '23:59:59']"
                value-format="yyyy-MM-dd HH:mm:ss"
                v-bind="$attrs"
                @change="handleTimePickChange"
            />
        </template>
    </div>
</template>

<script>
import { cloneDeep, findIndex, get } from "lodash-es";

export default {
    name: "index",
    model: {
        prop: "value",
        event: "change",
    },
    props: {
        value: {
            type: [String, Array],
            default: () => [],
        },
        // 选项卡
        options: {
            type: Array,
            required: false,
            default: () => {
                return [
                    {
                        label: "近1天",
                        value: 1,
                    },
                    {
                        label: "近7天",
                        value: 7,
                    },
                    {
                        label: "近15天",
                        value: 15,
                    },
                    {
                        label: "近30天",
                        value: 30,
                    },
                    {
                        label: "自定义",
                        value: "custom",
                    },
                ];
            },
        },
        // 结果格式化
        formatStr: {
            type: String,
            required: false,
            default: "YYYY-MM-DD HH:mm:ss",
        },
        // 最小值
        minValue: {
            type: String,
            default: "",
        },
        fixedValue: {
            type: String,
            default: "",
        },
        defaultValue: {
            type: [String, Number],
            default: 7,
        },
        customType: {
            type: String,
            default: "datetimerange",
        },
    },
    data() {
        return {
            currentIndex: null,
            currentOption: null,
            pickerOptions: [],
            timeAry: [],

            choiceDate: null,

            timePickerOptions: {
                onPick: ({ minDate }) => {
                    if (minDate) {
                        this.choiceDate = minDate;
                    }
                },
                disabledDate: time => {
                    if (this.minValue && this.fixedValue) {
                        const [minValue, minUnit] = this.minValue.split(",");
                        const minTime = this.$dayjs()
                            .subtract(minValue, minUnit)
                            .toDate();

                        const [fixValue, fixUnit] = this.fixedValue.split(",");
                        let fixMinTime = "";
                        let fixMaxTime = "";
                        const minDate = this.choiceDate;

                        if (minDate) {
                            fixMinTime = this.$dayjs(minDate)
                                .subtract(fixValue, fixUnit)
                                .startOf("day")
                                .toDate();
                            fixMaxTime = this.$dayjs(minDate)
                                .add(fixValue, fixUnit)
                                .endOf("day")
                                .toDate();
                        } else {
                            fixMinTime = "";
                            fixMaxTime = "";
                        }
                        const start =
                            new Date(new Date().toLocaleDateString()).getTime() + 24 * 60 * 60 * 1000 - 1;
                        return (
                            time.getTime() >= start ||
                            time.getTime() <= minTime.getTime() ||
                            (fixMinTime &&
                                fixMaxTime &&
                                (time.getTime() > fixMaxTime || time.getTime() < fixMinTime.getTime()))
                        );
                    }

                    if (this.minValue) {
                        const [value, unit] = this.minValue.split(",");
                        const minTime = this.$dayjs()
                            .subtract(value, unit)
                            .toDate();
                        // 当天的23:59:59时间戳
                        const start =
                            new Date(new Date().toLocaleDateString()).getTime() + 24 * 60 * 60 * 1000 - 1;
                        return time.getTime() > start || time.getTime() <= minTime.getTime();
                    }

                    if (this.fixedValue) {
                        const [value, unit] = this.fixedValue.split(",");
                        let minTime = "";
                        let maxTime = "";
                        const minDate = this.choiceDate;
                        if (minDate) {
                            minTime = this.$dayjs(minDate)
                                .subtract(value, unit)
                                .startOf("day")
                                .toDate();
                            maxTime = this.$dayjs(minDate)
                                .add(value, unit)
                                .end("day")
                                .toDate();
                        } else {
                            minTime = "";
                            maxTime = "";
                        }
                        return (
                            minTime &&
                            maxTime &&
                            (time.getTime() > maxTime || time.getTime() < minTime.getTime())
                        );
                    }

                    return false;
                },
            },
        };
    },
    computed: {
        itemStyle() {
            return key => {
                if (!this.currentOption) {
                    return null;
                }

                const index = this.currentIndex + 1;
                if (key === index && index <= this.pickerOptions.length - 1) {
                    return {
                        "border-left": "unset",
                    };
                }
                return null;
            };
        },
    },
    watch: {
        options: {
            handler() {
                this.initPickerOptions();
            },
            immediate: true,
        },
        defaultValue: {
            handler(val) {
                this.setDefaultValue();
            },
            immediate: true,
        },
    },
    methods: {
        /**
         * 初始化选项
         */
        initPickerOptions() {
            // 传入的options全为数字的情况
            if (this.options.every(item => typeof item === "number")) {
                this.pickerOptions = this.options.map(item => {
                    return {
                        label: `近${item}天`,
                        value: item,
                    };
                });
                this.pickerOptions.push({
                    label: "自定义",
                    value: "custom",
                });
                return;
            }

            this.pickerOptions = cloneDeep(this.options);
        },
        /**
         * 处理当前类型改变
         * @param item
         * @param index
         */
        handleChange(item, index) {
            if (this.currentIndex === index && this.currentIndex !== null) {
                return;
            }
            this.currentOption = item;
            this.currentIndex = index;
            const time = this.changeTime(item);
            this.$emit("change", time);

            this.handleCustomTime(item, index);
        },
        /**
         * 处理自定义时间
         */
        handleCustomTime(item, index) {
            const customIndex = findIndex(this.pickerOptions, { value: "custom" });
            if (!item) {
                return;
            }

            if (item.value === "custom") {
                this.pickerOptions.splice(index, 1);
                return;
            }

            if (customIndex === -1) {
                this.pickerOptions.push({
                    label: "自定义",
                    value: "custom",
                });
            }
        },
        /**
         * 时间变化
         */
        changeTime(item) {
            if (!item) {
                return [];
            }

            // 自定义
            if (item.value === "custom") {
                // this.$emit("change", ["", ""]);
                this.timeAry = [];
                // this.$emit("change", []);
                return ["", ""];
            }

            // 今天
            if (item.value === "today") {
                const startTime = this.$dayjs()
                    .startOf("day")
                    .format(this.formatStr);
                const endTime = this.$dayjs()
                    .endOf("day")
                    .format(this.formatStr);
                return [startTime, endTime];
            }

            // 昨天
            if (item.value === "yesterday") {
                const startTime = this.$dayjs()
                    .subtract(1, "day")
                    .startOf("day")
                    .format(this.formatStr);
                const endTime = this.$dayjs()
                    .subtract(1, "day")
                    .endOf("day")
                    .format(this.formatStr);
                return [startTime, endTime];
            }

            // value为数字的时间段
            if (item.value && typeof item.value === "number") {
                const startTime = this.$dayjs()
                    .subtract(item.value, "days")
                    .format("YYYY-MM-DD HH:mm:ss");
                const endTime = this.$dayjs().format("YYYY-MM-DD HH:mm:ss");
                return [startTime, endTime];
            }

            // 时间跨度，格式: 多久（数字类型）,单位
            if (item.value && typeof item.value === "string") {
                const ary = item.value.split(",");
                let startTime = "";
                let endTime = "";
                // 表示什么起，如本月起month
                if (ary.length === 1) {
                    startTime = this.$dayjs()
                        .startOf(ary[0])
                        .startOf("day")
                        .format(this.formatStr);
                    endTime = this.$dayjs()
                        .endOf("day")
                        .format(this.formatStr);
                }

                if (ary.length === 2) {
                    startTime = this.$dayjs()
                        .subtract(ary[0], ary[1])
                        .startOf("day")
                        .format(this.formatStr);
                    endTime = this.$dayjs()
                        .endOf("day")
                        .format(this.formatStr);
                }

                return [startTime, endTime];
            }

            // 本身有值的情况
            if (item.startTime && item.endTime) {
                // this.$emit("change", [item.startTime, item.endTime]);
                return [item.startTime, item.endTime];
            }
        },
        /**
         * 外层调用时间改变
         */
        outsideChangeTime() {
            if (!this.currentOption) {
                return;
            }

            if (this.currentOption.value === "custom") {
                this.$emit("change", this.timeAry);
                return;
            }

            const time = this.changeTime(this.currentOption);
            this.$emit("change", time);
        },
        /**
         * 设置默认值
         */
        setDefaultValue(value) {
            const val = value || this.defaultValue;
            if (!val) {
                this.currentIndex = null;
                this.currentOption = null;
                return;
            }

            const index = findIndex(this.pickerOptions, { value: val });
            if (index > -1) {
                this.currentIndex = index;
                this.currentOption = this.pickerOptions[index];
                this.handleCustomTime(this.currentOption, this.currentIndex);
            } else {
                this.currentIndex = null;
                this.currentOption = null;
            }
        },
        /**
         * 处理element 组件变化
         * @param val
         */
        handleTimePickChange(val) {
            !val && (this.choiceDate = null);
            this.$emit("change", [get(val, "[0]", ""), get(val, "[1]", "")]);
        },
        get,
    },
};
</script>

<style scoped lang="scss">
@import "./index.scss";
</style>
