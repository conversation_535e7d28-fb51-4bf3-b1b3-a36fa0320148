<template>
    <el-dialog
        :title="dialogTitle"
        :append-to-body="true"
        :close-on-click-modal="false"
        :visible.sync="dialogVisible"
        :before-close="cancel"
        width="900px"
        class="filetype-ttl-dialog"
    >
        <el-form
            :rules="addRules"
            :model="filetypeTtlForm"
            ref="filetypeTtlForm"
            class="cache-form"
            label-width="160px"
        >
            <el-form-item :label="$t('domain.type')" prop="mode">
                <el-radio-group
                    v-model.number="filetypeTtlForm.mode"
                    @change="typeChange"
                    class="cache-form--selector"
                >
                    <!-- <el-radio :label="0">{{ $t("domain.detail.cacheModeMap[0]") }}</el-radio>
                    <el-radio :label="1">{{ $t("domain.detail.cacheModeMap[1]") }}</el-radio>
                    <el-radio :label="2">{{ $t("domain.detail.cacheModeMap[2]") }}</el-radio>
                    <el-radio :label="3">{{ $t("domain.detail.cacheModeMap[3]") }}</el-radio>
                    <el-radio :label="4">{{ $t("domain.detail.cacheModeMap[4]") }}</el-radio> -->
                    <el-radio
                        v-for="opt in cacheModeOptions"
                        :key="opt.value"
                        :label="opt.value"
                        :disabled="opt.disabled"
                    >
                        {{ opt.label }}
                    </el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item key="file_type" :label="$t('domain.content')" prop="file_type" v-if="dialogVisible">
                <el-input
                    v-model="filetypeTtlForm.file_type"
                    :placeholder="fileTypePlaceholder()"
                    :disabled="filetypeTtlForm.mode === 2 || filetypeTtlForm.mode === 3"
                    @focus="showNameSet(filetypeTtlForm)"
                ></el-input>
            </el-form-item>
            <el-form-item :label="$t('domain.detail.label32')" prop="cache_type">
                <el-radio-group v-model.number="filetypeTtlForm.cache_type" @change="handleCacheTypeChange">
                    <el-radio :label="1">{{ $t("domain.detail.label36") }}</el-radio>
                    <el-radio :label="2">{{ $t("domain.detail.label37") }}</el-radio>
                    <el-radio :label="3">{{ $t("domain.detail.label38") }}</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item :label="$t('domain.detail.label33')" prop="ttl" v-show="dialogVisible && filetypeTtlForm.cache_type !== 1">
                <el-input v-model.number="filetypeTtlForm.ttl" maxlength="16">
                    <el-select
                        v-model="filetypeTtlForm.timeType"
                        slot="append"
                        :placeholder="$t('domain.editPage.placeholder8')"
                        style="width: 100px"
                    >
                        <el-option :label="$t('simpleForm.alogicCacheMixin.CacheTtlMap.2')" value="2" />
                        <el-option :label="$t('simpleForm.alogicCacheMixin.CacheTtlMap.3')" value="3" />
                        <el-option :label="$t('simpleForm.alogicCacheMixin.CacheTtlMap.4')" value="4" />
                        <el-option :label="$t('simpleForm.alogicCacheMixin.CacheTtlMap.5')" value="5" />
                    </el-select>
                </el-input>
            </el-form-item>
            <el-form-item :label="$t('domain.detail.label34')" prop="cache_with_args" class="cache-with-args">
                <el-switch
                    v-model="filetypeTtlForm.cache_with_args"
                    :active-value="0"
                    :inactive-value="1"
                ></el-switch>
            </el-form-item>
            <el-form-item :label="$t('domain.create.weight')" prop="priority">
                <el-input v-model.number="filetypeTtlForm.priority" maxlength="16" clearable placeholder="1~100"></el-input>
            </el-form-item>
        </el-form>
        <div slot="footer">
            <el-button class="dialog-btn" @click="cancel">{{ $t("domain.cancel") }} </el-button>
            <el-button type="primary" class="dialog-btn" @click="submit">{{ $t("common.dialog.submit") }}</el-button>
        </div>
        <el-dialog
            :title="$t('domain.detail.cacheModeMap.0')"
            :append-to-body="true"
            :close-on-click-modal="false"
            :modal-append-to-body="false"
            :visible.sync="extensionsDialogVisible"
            custom-class="aocdn-cache-extensions-dialog"
        >
            <div class="aocdn-cache-extensions" v-for="(val, key) in extensionsOptions" :key="val.title">
                <div class="extension-title">
                    <el-checkbox
                        v-model="extensionAllSelected[key]"
                        @change="checkAllChange(key)"
                        :label="val.title"
                    />
                </div>
                <div class="extension-list">
                    <el-checkbox-group
                        v-model="extensionSelected[key]"
                        @change="checkSingleChange(key)"
                    >
                        <el-checkbox
                            v-for="extension in val.list"
                            :label="extension"
                            :key="extension"
                        />
                    </el-checkbox-group>
                </div>
            </div>
            <div class="aocdn-cache-extensions">
                <div class="extension-title">{{ $t("domain.detail.label40") }}</div>
                <div class="extension-list">
                    <el-input
                        type="textarea"
                        :rows="2"
                        :placeholder="$t('domain.detail.placeholder1')"
                        v-model="extensionOther"
                    />
                </div>
            </div>
            <div slot="footer" class="dialog-footer">
                <el-button @click="extensionsDialogVisible = false">{{ $t("common.dialog.cancel") }}</el-button>
                <el-button type="primary" @click="setName">{{ $t("common.dialog.submit") }}</el-button>
            </div>
        </el-dialog>
    </el-dialog>
</template>

<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";
import pattern from "@/config/pattern";
import { formValidate2Promise } from "@/utils";
import { Form } from "element-ui";
import i18n from "@/i18n";
import { extensionsOptions } from "@/components/simpleform/nAlogicCacheTable/config";
import { nUserModule } from "@/store/modules/nuser";

const extensionsMap: any = {};
Object.keys(extensionsOptions).forEach((key: any) => {
    (extensionsOptions as any)[key].list.forEach((k: any) => {
        extensionsMap[k] = key;
    });
});

// 缓存配置-类型：字典翻译
const CacheModeMap: any = {
    0: i18n.t("domain.detail.cacheModeMap[0]"),
    1: i18n.t("domain.detail.cacheModeMap[1]"),
    2: i18n.t("domain.detail.cacheModeMap[2]"),
    3: i18n.t("domain.detail.cacheModeMap[3]"),
    4: i18n.t("domain.detail.cacheModeMap[4]"),
    // 5: i18n.t("domain.detail.cacheModeMap[5]"),
};
const cacheModeOptions = Object.keys(CacheModeMap).map((mode: any) => ({
    label: CacheModeMap[mode],
    value: mode * 1, // 需要是 number 类型
}));

type filetypeTtlParam = {
    mode: string;
    file_type: string;
    ttl: number;
    cache_type: number;
    cache_with_args: string;
    priority: string;
    timeType: string;
};
const formValidate2Field = (form: Form) =>
    new Promise((resolve, reject) => {
        form.validate(valid => {
            if (valid) {
                resolve(true);
            }
        });
    });

@Component({})
export default class UpdateDialog extends Vue {
    private pattern = pattern;
    @Prop({ default: "create", type: String }) private from!: string;
    // 弹框显示标志位
    @Prop({ default: false, type: Boolean }) private dialogVisible!: boolean;
    @Prop({
        default: {
            mode: 0,
            file_type: "",
            ttl: 30,
            cache_type: 3,
            cache_with_args: 0,
            priority: 10,
            timeType: "5",
        },
    })
    private filetypeTtlForm!: filetypeTtlParam;
    @Prop({ required: true, type: Array }) filetype_ttl!: any[];

    extensionAllSelected: any = {
        dynamic: false,
        image: false,
        style: false,
        av: false,
        download: false,
        page: false,
    }
    extensionSelected: any = {
        dynamic: [],
        image: [],
        style: [],
        av: [],
        download: [],
        page: [],
    }
    extensionOther = ""
    extensionsDialogVisible = false
    extensionsOptions: any = extensionsOptions

    get dialogTitle() {
        const separate = nUserModule.lang === "en" ? " " : "";
        return `${this.from === "create" ? i18n.t('domain.add2') : i18n.t('domain.modify')}${separate}${i18n.t('domain.detail.label90')}`;
    }

    get values() {
        // 处理换行、所有空格、过滤空数据
        return this.filetypeTtlForm;
    }

    // 校验规则
    get addRules() {
        return {
            mode: [{ required: true, message: this.$t("domain.detail.tip42"), trigger: "change" }],
            file_type: [
                {
                    validator: (rule: any, value: string, callback: Function) => {
                        const paths = value?.split(",");
                        const allPathPattern = new RegExp("^([A-Za-z0-9]|[!#$&'()*+,-./:;=?@_~])*$");
                        paths?.forEach(path => {
                            if (
                                parseInt(this.filetypeTtlForm.mode) === 4 &&
                                (path.includes("?") || path.includes("？"))
                            )
                                callback(this.$t("domain.detail.placeholder48"));
                            if (
                                parseInt(this.filetypeTtlForm.mode) === 4 &&
                                path.length > 0 &&
                                path[0] !== "/"
                            )
                                callback(this.$t("domain.detail.placeholder49"));
                        });
                        if (parseInt(this.filetypeTtlForm.mode) === 0 && value === "")
                            callback(this.$t("domain.htmlForbid.forbid4"));
                        if (parseInt(this.filetypeTtlForm.mode) === 0 && !/^\w{1,9}(,\w{1,9})*$/.test(value))
                            callback(this.$t("domain.detail.placeholder50"));
                        if (parseInt(this.filetypeTtlForm.mode) === 1 && value === "")
                            callback(this.$t("domain.detail.placeholder73"));
                        if (
                            parseInt(this.filetypeTtlForm.mode) === 1 &&
                            !/^((\/[\w-.]+)+,)*(\/[\w-.]+)+$/.test(value)
                        )
                            callback(this.$t("domain.detail.placeholder51"));
                        if (parseInt(this.filetypeTtlForm.mode) === 2 && value === "")
                            callback(this.$t("domain.detail.placeholder52"));
                        if (parseInt(this.filetypeTtlForm.mode) === 3 && value === "")
                            callback(this.$t("domain.detail.placeholder53"));
                        if (parseInt(this.filetypeTtlForm.mode) === 4 && value.trim() === "")
                            callback(this.$t("domain.detail.placeholder74"));
                        if (parseInt(this.filetypeTtlForm.mode) === 4 && !allPathPattern.test(value))
                            callback(this.$t("domain.detail.placeholder14"));
                        else callback();
                    },
                    trigger: ["blur", "change"],
                },
                { required: true, message: this.$t("domain.detail.placeholder54"), trigger: "blur" },
            ],
            ttl: [
                { required: true, message: this.$t("domain.detail.placeholder29"), trigger: "blur" },
                { trigger: "blur", pattern: /^[0-9]*$/, message: this.$t("domain.detail.placeholder59") },
                {
                    validator: (rule: any, value: number, callback: Function) => {
                        if (this.filetypeTtlForm.timeType === "2" && Number(this.filetypeTtlForm.ttl) > 1095)
                            callback(this.$t("domain.detail.ttlTip[3]"));
                        if (this.filetypeTtlForm.timeType === "3" && Number(this.filetypeTtlForm.ttl) > 26280)
                            callback(this.$t("domain.detail.ttlTip[4]"));
                        if (
                            this.filetypeTtlForm.timeType === "4" &&
                            Number(this.filetypeTtlForm.ttl) > 1576800
                        )
                            callback(this.$t("domain.detail.ttlTip[5]"));
                        if (
                            this.filetypeTtlForm.timeType === "5" &&
                            Number(this.filetypeTtlForm.ttl) > 94608000
                        )
                            callback(this.$t("domain.detail.ttlTip[6]"));
                        else callback();
                    },
                    trigger: ["blur", "change"],
                },
            ],
            cache_type: [
                { required: true, message: this.$t("domain.detail.placeholder58"), trigger: "change" },
            ],
            cache_with_args: [
                { required: true, message: this.$t("domain.detail.placeholder64"), trigger: "change" },
            ],
            priority: [
                { required: true, message: this.$t("domain.detail.placeholder20"), trigger: "blur" },
                { trigger: "blur", pattern: /^[0-9]*$/, message: this.$t("domain.detail.placeholder65") },
                {
                    validator: (rule: any, value: number, callback: Function) => {
                        if (value > 100) callback(this.$t("domain.detail.placeholder66"));
                        if (value < 1) callback(this.$t("domain.detail.placeholder67"));
                        else callback();
                    },
                    trigger: ["blur", "change"],
                },
            ],
        };
    }
    // 动态计算选项
    get cacheModeOptions() {
        return cacheModeOptions?.map(option => {
            let disabled = false;

            // mode 2、3 只支持选一个
            if (option.value === 2 || option.value === 3) {
                disabled = this.filetype_ttl.some(item => item.mode === option.value);
            }

            return {
                disabled,
                ...option,
            };
        });
    }
    private fileTypePlaceholder() {
        return parseInt(this.filetypeTtlForm.mode) === 0
            ? this.$t("simpleForm.alogicCacheMixin.FileSuffix.0")
            : parseInt(this.filetypeTtlForm.mode) === 1
            ? this.$t("simpleForm.alogicCacheMixin.FileSuffix.1")
            : this.$t("simpleForm.alogicCacheMixin.FileSuffix.4");
    }
    private handleCacheTypeChange() {
        const { cache_type } = this.filetypeTtlForm;
        if (cache_type === 1) {
            this.filetypeTtlForm.ttl = 0;
        } else {
            this.filetypeTtlForm.ttl = 80;
        }
        this.filetypeTtlForm.timeType = "5";
    }
    private async typeChange(val: number) {
        if (val === 2 || val === 3) {
            this.filetypeTtlForm.file_type = "/";
        } else {
            this.filetypeTtlForm.file_type = "";
        }
        await formValidate2Field(this.$refs.filetypeTtlForm as Form);
    }
    private async submit() {
        await formValidate2Promise(this.$refs.filetypeTtlForm as Form);
        this.$emit("submit");
    }
    private cancel() {
        this.$emit("cancel", "filetypeTtlDialogVisible");
    }
    showNameSet(cache: any) {
        // 内容为后缀名才弹出弹框
        if (cache.mode !== 0) return;
        // 清掉缓存
        Object.keys(this.extensionAllSelected).forEach((key: any) => {
            this.extensionAllSelected[key] = false;
            this.extensionSelected[key] = [];
        });
        const other: any = [];
        // 过滤有复选框的后缀名
        cache.file_type
            .split(",")
            .filter((val: any) => val)
            .forEach((item: any) => {
                if (extensionsMap[item]) {
                    this.extensionSelected[extensionsMap[item]].push(item);
                } else {
                    other.push(item);
                }
            });
        // 其他后缀名
        this.extensionOther = other.join(",");
        // 全选按钮是否选中
        Object.keys(this.extensionSelected).forEach(key => {
            this.extensionAllSelected[key] =
                new Set(this.extensionSelected[key]).size === (extensionsOptions as any)[key].list.length;
        });
        this.extensionsDialogVisible = true;
    }
    // 全选按钮逻辑
        checkAllChange(key: any) {
            const val = this.extensionAllSelected[key];
            this.extensionSelected[key] = val ? (extensionsOptions as any)[key].list : [];
        }
        // 选项是否触发全选
        checkSingleChange(key: any) {
            const val = this.extensionSelected[key];
            this.extensionAllSelected[key] = val.length === (extensionsOptions as any)[key].list.length;
        }
        setName() {
            const extentionReg = new RegExp("^\\w{1,9}(?:,\\w{1,9})*$|^$");
            if (!extentionReg.test(this.extensionOther)) {
                (this.$message.error as any)(this.$t("domain.detail.tip30"));
                return;
            }
            this.extensionsDialogVisible = false;
            // 选中复选框的后缀
            const extensions = Object.keys(this.extensionSelected)
                .reduce((rst, key) => {
                    if (this.extensionSelected[key].length > 0) {
                        return rst + "," + this.extensionSelected[key].join(",");
                    }
                    return rst;
                }, "")
                .slice(1);

            // 没有更多，则直接用计算出的结果；如果有更多，则进行拼接
            this.filetypeTtlForm.file_type = !this.extensionOther
                ? extensions
                : `${extensions}${extensions ? "," : ""}${this.extensionOther}`;
        }
}
</script>

<style lang="scss" scoped>
.filetype-ttl-dialog {
    ::v-deep {
        .el-dialog__body {
            padding: 24px !important;
        }
    }
}
// 缓存 URL 弹窗的样式
::v-deep .el-dialog {
    width: 60%;
    min-width: 340px;
    .cache-with-args {
        .el-form-item__label {
            word-break: normal;
        }
    }
}
</style>
