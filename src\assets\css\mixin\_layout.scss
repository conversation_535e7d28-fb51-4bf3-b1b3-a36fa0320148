// @author:		<PERSON><PERSON><PERSON><PERSON>
// @description 定义布局相关的混合器

// 等高布局
@mixin g-layout-equal-height($child-selector) {
    & {
        overflow: hidden;
    }

    #{$child-selector} {
        padding-bottom: 9999px;
        margin-bottom: -9999px;
        vertical-align: top;
    }
}

//设置父亲相对布局，子元素绝对布局
@mixin g-layout-rel-abs($child-selector, $options: ()) {
    & {
        @include g-set-position($position: relative);
    }

    #{$child-selector} {
        @include g-set-position($position: absolute);
        $left: map-get($options, 'left');
        $right: map-get($options, 'right');
        $top: map-get($options, 'top');
        $bottom: map-get($options, 'left');

        @if $left {
            left: $left;
        }

        @if $right {
            right: $right;
        }

        @if $top {
            top: $top;
        }

        @if $bottom {
            bottom: $bottom;
        }

        @content;
    }
}

//行内块：垂直居中
@mixin g-layout-inline-block-vcenter($child-selector) {
    %vcenter {
        display: inline-block;
        vertical-align: middle;
    }

    #{$child-selector} {
        @extend %vcenter;
        @content;
    }

    &::after {
        content: '';
        height: 100%;
        @extend %vcenter;
    }
}

// 块级元素高度固定，垂直居中
@mixin g-layout-height-known-vcenter($child-selector, $child-height) {
    @include g-layout-rel-abs($child-selector, (top: 0, bottom: 0)) {
        height: $child-height;
        margin: auto 0;
    }
}

// 块级元素高度未知，垂直居中
@mixin g-layout-height-unknown-vcener($child-selector) {
    & {
        display: table;
    }

    #{$child-selector} {
        display: table-cell;
        vertical-align: middle;
        @content;
    }
}

//水平居中
@mixin g-layout-horizontal-center {
    margin: {
        left: auto;
        right: auto;
    }
}

// 左右上下居中
@mixin g-layout-center($child-selector, $options: ()) {

    @include g-layout-rel-abs($child-selector, (left: 50%,
            top: 50%)) {
        transform: translate(-50%, -50%);
        @content;
    }

    ;
}






// 设置字体大小，颜色
@mixin g-set-font($font-size: 14px, $color: #000, $font-weight: normal) {

    font: {
        size: $font-size;
        weight: $font-weight;
    }

    ;
    color: $color;
}

@mixin g-text-justify {
    text-align: justify;
    //IE,FF下 对于中英文混合，text-align：justify不起作用，两端还是没对齐，加上text-justify修正
    text-justify: distribute;
}

// 属性设置为inherit
@mixin g-inherit($properties...) {
    @each $prop in $properties {
        #{$prop}: inherit;
    }
}

@mixin g-full-percent($properties...) {
    @each $prop in $properties {
        #{$prop}: 100%;
    }
}

//设置media
//
@mixin g-set-media($max-width) {
    @media screen and (max-width: $max-width) {

        html,
        body {
            width: max-width;
        }
    }
}



@mixin g-ie9 {
    @media all and (min-width:0\0) and (min-resolution:.001dpcm) {
        @content;
    }

}

// 设置高度，行高
@mixin g-height-lineheight($height: null, $line-height: null) {
    height: $height;
    line-height: if($line-height, $line-height, $height)
}

@mixin g-set-position($position: relative, $left: -1, $right: -1, $top: -1, $bottom: -1) {
    position: $position;

    @if $left !=-1 {
        left: $left;
    }

    @if $right !=-1 {
        right: $right;
    }

    @if $top !=-1 {
        top: $top;
    }

    @if $bottom !=-1 {
        bottom: $bottom;
    }
}





//给元素前后添加中划水平线
@mixin g-add-horizontal-line($line-width, $line-height: 1px, $color: #E5E5E5) {
    & {
        @include g-set-position($position: relative);
    }

    &::before,
    &::after {
        @include g-set-position($position: absolute, $top: 50%);
        content: '';
        display: block;
        width: $line-width;
        height: $line-height;
        background: $color;
    }

    &::before {
        left: 0;
    }

    &::after {
        right: 0;
    }
}



// 浮动
@mixin g-float($side: left) {
    float: unquote($side);
}

// 左浮动
@mixin g-pull-left {
    @include g-float(left);
}

// 右浮动
@mixin g-pull-right {
    @include g-float(right);
}

//清除浮动
@mixin g-clear-fix {

    &::before,
    &::after {
        display: table;
        content: '';
    }

    &::after {
        clear: both;
    }
}

//单行文本截取： 1.明确的宽度 2.强制不换行 3.隐藏多余文本
@mixin g-singleline-ellipsis($width: null, $substract: null) {
    white-space: nowrap;
    ;
    overflow: hidden;
    text-overflow: ellipsis;

    @if not $width==null {
        width: $width;
    }

    @if not $substract==null {
        width: calc(100% - #{$substract});
    }
}

// 多行省略
// 1.指定webkit-box布局，并指定垂直方向
// 2.行数
// 3.隐藏
@mixin g-multiline-ellipsis($line-clamp: 1) {
    @include g-set-position($position: relative);
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: $line-clamp;
    overflow: hidden;
    text-overflow: ellipsis;

    // IE下通过模拟的省略号实现
    &::after {
        content: '...'\9;
        @include g-set-position($position: absolute, $right: 0, $bottom: 0);
        padding-left: 15px\9;
        background: linear-gradient(to right, transparent, #fff 55%)\9;
    }
}

//一行上多个元素
//参考https://kyusuf.com/post/almost-complete-guide-to-flexbox-without-flexbox
@mixin g-row($parent-selector, $item-selector, $font-size: 12px, $vertical-align: top) {
    #{$parent-selector} {
        // 当容器内子元素是inline inline-block时，会出现子元素之间有空隙，或者子元素宽度之和等于容器宽度却有子元素掉下去了的bug,
        // 解决方案就是 给容器设置font-size:0,去除 排版 缩进 时候生成的文本节点
        font-size: 0;
    }

    #{$item-selector} {
        display: inline-block;
        //如果不设置vertical-align为top,容器内的子元素如果有些包含文本节点，有些没，那么子元素会高低不一致，不对齐
        vertical-align: $vertical-align;
        // 由于设置了父元素的font-size为0。font-size有继承性
        font-size: $font-size;
    }
}

// 单个块级元素居中
@mixin g-center-block {
    display: block;

    margin: {
        left: auto;
        right: auto;
    }
}

// 多个块级元素居中
@mixin g-center-blocks($parent-selector, $item-selector, $font-size: 12px, $vertical-align: top) {
    //块级元素在一行
    @include g-row($parent-selector, $item-selector, $font-size, $vertical-align);

    //居中
    #{$parent-selector} {
        text-align: center;
    }
}


// 两端对齐
@mixin g-justify-blocks($parent-selector, $item-selector, $font-size: 12px, $vertical-align: top) {
    @include g-row($parent-selector, $item-selector, $font-size: 12px, $vertical-align: top);

    #{$parent-selector} {
        text-align: justify;

        &::after {
            content: '';
            display: inline-block;
            width: 100%;
        }
    }
}

// 占据剩余部分 类似 flex-grow
@mixin g-item-fill($parent-selector, $item-selector, $fill-item-selector, $vertical-align: top) {
    #{$parent-selector} {
        display: table; // 兼容性相当好。IE8+ 都支持 http://caniuse.com/#feat=css-table
    }

    #{$item-selector} {
        display: table-cell; // 兼容性相当好。IE8+ 都支持 http://caniuse.com/#feat=css-table
        vertical-align: $vertical-align;
    }

    #{$fill-item-selector} {
        width: 100%;
    }
}

/* 定义滚动条样式 圆角和阴影不需要则传入null */
@mixin g-scrollbar($width: 10px, $height: 10px, $outColor: #FFF, $innerColor: #DCDCDC, $radius:5px, $shadow:null) {

    /*定义滚动条高宽及背景 高宽分别对应横竖滚动条的尺寸*/
    &::-webkit-scrollbar {
        width: $width;
        height: $height;
        background-color: $outColor;
    }

    /*定义滚动条轨道 内阴影+圆角*/
    &::-webkit-scrollbar-track {
        @if ($shadow !=null) {
            -webkit-box-shadow: $shadow;
        }

        @if ($radius !=null) {
            border-radius: $radius;
        }

        background-color: $outColor;
    }

    /*定义滑块 内阴影+圆角*/
    &::-webkit-scrollbar-thumb {
        @if ($shadow !=null) {
            -webkit-box-shadow: $shadow;
        }

        @if ($radius !=null) {
            border-radius: $radius;
        }

        background-color: $innerColor;
        border: 1px solid $innerColor;
    }
}
