import { RouteConfig } from "vue-router";
import { StatisticsModule } from "@/store/modules/statistics";
import { ProductModule } from "@/store/modules/ncdn/nproduct";
import { DomainActionEnum, getDomainAction } from "@/config/map";
import { DomainModule } from "@/store/modules/domain";
import { nUserModule } from "@/store/modules/nuser";

const indexRouter: RouteConfig[] = [
    {
        path: "/reportManagement",
        name: "reportManagement",
        component: () => import("./index.vue"),
        meta: {
            ucode: "reportManagement",
            breadcrumb: {
                title: '$t("report.title")',
                route: ["reportManagement"],
            },
            perm: "reportManagement",
        },
        async beforeEnter(to, from, next) {
            // 不阻塞页面加载
            const isFcdnCtyunCtclouds = nUserModule.isFcdnCtyunCtclouds;
            isFcdnCtyunCtclouds
                ? DomainModule.GetDomainList({ action: getDomainAction("Report") })
                : DomainModule.GetDomainList({ action: DomainActionEnum.Data });
            next();
        },
    },
    {
        path: "/reportManagement/create",
        name: "reportManagement.create",
        component: () => import("./create.vue"),
        meta: {
            breadcrumb: {
                title: '$t("report.addSubscription")',
                route: ["reportManagement", "reportManagement.create"],
            },
            perm: "reportManagement",
        },
        async beforeEnter(to, from, next) {
            window.__POWERED_BY_QIANKUN__ && StatisticsModule.nGetAllProduct();
            next();
        },
    },
    {
        path: "/reportManagement/update",
        name: "reportManagement.update",
        component: () => import("./create.vue"),
        meta: {
            breadcrumb: {
                title: '$t("report.updateSubscription")',
                route: ["reportManagement", "reportManagement.update"],
            },
            perm: "reportManagement",
        },
        async beforeEnter(to, from, next) {
            window.__POWERED_BY_QIANKUN__ && StatisticsModule.nGetAllProduct();
            next();
        },
    },
];
export default indexRouter;