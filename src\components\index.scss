@import "./variable.scss";
@import "./fix-element.scss";
@include fix-element;

.form-wrapper {
    color: $black1;
    white-space: nowrap;
    width: 100%;
    min-width: 625px;
    line-height: #{$label-line-height}px;
    font-size: #{$label-font-size}px;

    // @include fix-element-ui;
    // .form-item-arr {
    //     background-color: #f7f7f7;
    //     overflow: hidden;
    //     margin-bottom: 20px;
    //     padding-right: 20px;
    // }
    .form-item-arr-hidden {
        display: none;
    }

    .form-item-arr-name {
        font-weight: bold;
        margin: 15px 0 15px 20px;
        width: 600px;
        overflow: hidden;
    }

    .absolute-form-item {
        padding-left: 32px;
        padding-right: 32px;

        ::v-deep {
            .tooltips {
                font-size: #{$tooltips-font-size}px;
                line-height: #{$tooltips-font-size}px;
                margin-top: 8px;
                color: $neutral-7;
            }

            .danger {
                color: $color-danger;
            }
        }
    }

    .form-item-div {
        &:last-of-type {
            border-bottom: none;
        }

        .nested-form {
            background-color: #eee;
            position: relative;
            padding-top: 40px;

            &:before {
                content: "以下为嵌套表单部分";
                position: absolute;
                top: 0;
                left: 50%;
                transform: translateX(-50%);
            }

            .form-item-div:last-of-type {
                margin-bottom: 0;
            }
        }

        .indent {
            padding-left: 140px;
        }

        ::v-deep .el-radio-button__orig-radio:checked+.el-radio-button__inner {
            color: #fff;
        }
    }

    .autoform-item {
        display: inline-block;
        position: relative;
        vertical-align: top;
        white-space: normal;
        word-break: break-all;
        // width: calc(100% - #{$label-width}px - 20px);
        @include g-width(100%, 100%, calc(100% - #{$label-width}px - 20px));
        // margin-bottom: 20px;
        // margin-left: 15px;
        font-size: #{$content-font-size}px;

        ::v-deep {
            .tooltips {
                font-size: #{$tooltips-font-size}px;
                line-height: #{$tooltips-font-size}px;
                margin-top: 8px;
                color: $color-neutral-7;
            }

            .danger {
                color: $color-danger;
            }

            .option-note {
                font-size: #{$tooltips-font-size}px;
                line-height: 1;
                margin-top: 8px;
                color: $color-neutral-7;
            }

            .q-tip {
                position: relative;

                i {
                    position: relative;
                    top: 1px;
                    font-size: 16px;
                    cursor: help;
                }

                &>div {
                    $corner-width: 10;
                    display: none;
                    position: absolute;
                    padding: 12px;
                    left: calc(100% + #{$corner-width / 2}px);
                    top: -12px;
                    width: 300px;
                    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
                    word-break: break-all;
                    background: #fff;
                    border-radius: 4px;
                    border: 1px solid $border-color;
                    z-index: 2000;
                    line-height: 20px;

                    .corner {
                        position: absolute;
                        width: #{$corner-width}px;
                        height: #{$corner-width}px;
                        box-sizing: border-box;
                        border-right: #{$corner-width / 2}px solid $border-color;
                        left: -#{$corner-width}px;
                        border-top: #{$corner-width / 2}px solid transparent;
                        border-bottom: #{$corner-width / 2}px solid transparent;
                    }
                }

                &:hover {
                    div {
                        display: block;
                    }

                    i {
                        color: $color-master;
                    }
                }
            }
        }
    }

    ::v-deep {

        span.primary,
        i.primary {
            color: $color-master;
        }

        span.success,
        i.success {
            color: $color-success;
        }

        span.warning,
        i.warning {
            color: $color-warning;
        }

        span.danger,
        i.danger {
            color: $color-danger;
        }

        span.info,
        i.info {
            color: $neutral-7;
        }
    }
}