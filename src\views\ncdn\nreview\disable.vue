<template>
    <ct-section-wrap
        headerText="CDN内容审核"
        headerTip="CDN内容审核是CDN加速的一项增值服务，开通CDN内容审核功能后，系统会自动智能检测通过CDN加速的图片是否涉黄涉暴恐涉政，鉴定为违规图片的URL将会被记录下来供客户导出，同时支持对违规内容做自动封禁，实现”净网分发“。目前内容审核主要针对图片鉴黄业务。"
    >
        <ct-box>
            <div class="tip">
                <p>
                    CDN内容审核为增值服务，配置开启后将产生额外费用。
                    <a class="aocdn-ignore-link" @click="$docHelp(billLink)">点击查看费用详情</a>
                </p>
                <p>注意：当前该功能仅支持线下配置开启，如有需要，请联系对应客户经理。</p>
            </div>
        </ct-box>
    </ct-section-wrap>
</template>

<script>
import { nUserModule } from "@/store/modules/nuser";
export default {
    computed: {
        isCtclouds() {
            return nUserModule.isCtclouds;
        },
        billLink() {
            return this.isCtclouds
                ? nUserModule.lang === "zh"
                    ? "https://www.esurfingcloud.com/document/zh-cn/10015932/10028512"
                    : "https://www.esurfingcloud.com/document/10015932/10028512"
                : "https://www.ctyun.cn/document/10015932/10448891";
        },
    },
};
</script>

<style lang="scss" scoped>
.ct-box {
    height: 100%; // min-height会导致子元素获取不到实际高度，故设置为height
    position: relative;
}
.tip {
    left: 0;
    padding: 0 16px;
    width: 100%;
    position: absolute;
    text-align: center;
    top: 50%;
    transform: translateY(-50%);
    p {
        font-size: 14px;
        color: #666;
    }
}
</style>
