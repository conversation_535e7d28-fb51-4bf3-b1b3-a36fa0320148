import { SecurityAbilityModule } from "@/store/modules/securityAbility";
import { get } from "lodash-es";
import { nUserModule } from "@/store/modules/nuser";

export default {
    components: {},
    data() {
        return {};
    },
    computed: {
        /**
         * 判断当前环境是否具有编辑权限
         * 
         * 此方法用于调用SecurityAbilityModule模块中的isEdit属性，以检查当前环境或用户是否具有编辑权限
         * 主要用于在执行编辑操作前，进行权限的校验
         * 
         * @returns {boolean} 返回SecurityAbilityModule.isEdit的值，true表示具有编辑权限，false表示没有编辑权限
         */
        isEdit() {
            return SecurityAbilityModule.isEdit;
        },
        // 域名是否启用中
        isService() {
            const status = get(SecurityAbilityModule.securityDomainInfo, "domainStatus");
            return status === "NORMAL";
        },
        /**
         * 检查当前应用是否由Qiankun框架驱动
         * 
         * Qiankun是一个用于构建和管理多个子应用的微前端框架此函数通过检查全局变量来判断当前应用是否使用了Qiankun框架
         * 
         * @returns {boolean} 如果由Qiankun框架驱动返回true，否则返回false
         */
        isPoweredByQiankun() {
            return window.__POWERED_BY_QIANKUN__;
        },
        isCtclouds() {
            return nUserModule.isCtclouds;
        },
    },
    methods: {},
};
