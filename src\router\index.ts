import Vue from "vue";
import VueRouter, { RouteConfig } from "vue-router";
import "./shims";
import { prefix, microAppRoutes, normalAppRoute, setRoutes } from "./routeData";
import * as routerUtils from "@cdnplus/common/router/utils";
import { nUserModule } from "@/store/modules/nuser";
import { MenuModule } from "@/store/modules/menu";
import { StatisticsModule } from "@/store/modules/statistics";
import { LoginUrl, nProductList } from "@/config/url";
import { ctFetch } from "@/utils";
import { getMenuDomain } from "@/utils/logic/url";
import { AppModule } from "@/store/modules/app";

Vue.use(VueRouter);

// 路由配置
export const routes: RouteConfig[] = prefix ? microAppRoutes : normalAppRoute;

/**
 * 动态导入各目录下的路由配置
 * webpack文档规定 require.context 参数只接收字面量，不要用变量去控制
 */
const ctx = require.context("../views", true, /router.(j|t)s$/);
ctx.keys().forEach(file => {
    // 获取到配置
    let route = ctx(file).default || ctx(file);
    // 可能是数组或者对象
    route = Array.isArray(route) ? route : [route];
    // 添加进路由缓存
    if (prefix) {
        (routes[0] as any).children.push(...route);
    } else {
        routes.push(...route);
    }
});

if (prefix) {
    setRoutes(routes[0].children);
}

const createRouter = () =>
    new VueRouter({
        routes,
    });
const router = createRouter();

/**
 * 处理页面跳转
 * @param to
 * @param next
 */
async function handlePageChange(to: any, next: any) {
    // 自动填充 query: workspaceId
    if (/.*\?(.*&)?workspaceId=[^\s]+/.test(to.fullPath)) {
        next();
        // 暂时注释：iam没配菜单，不能通过链接访问
        // if (!Object.values(MenuOnlineModule.menuMap).length) {
        //     const workspaceId = to.query.workspaceId;
        //     await MenuOnlineModule.GetMenuAllList(workspaceId as string);
        // }
        // if ((to.meta && to.meta.perm && MenuOnlineModule.menuMap[to.meta.perm]) || to.meta.isSkipMicroApp) {
        //     next();
        //     return;
        // }
        // // 不在在线列表中的情况
        // if (!MenuOnlineModule.menuMap[(to as any).path] && !MenuOnlineModule.menuMap[(to as any).name]) {
        //     // 跳转到404页面
        //     next({
        //         name: "errorPage",
        //     });
        //     return;
        // }
        // next();
        // return;
    } else {
        // vue-router v3.1.0 return a promise with push and replace
        // https://github.com/vuejs/vue-router/issues/2881#issuecomment-520554378
        // 如果这里报错了，把 vue-router 版本回退到 v3.0.7
        next({
            name: to.name,
            params: to.params,
            query: to.query,
        });
    }
}


router.beforeEach(async (to, from, next) => {
    await nUserModule.GetUserInfo();

    if (!nUserModule.isLoggedIn) {
        return next(false);
    }

    // 从 route 中获取 workspaceId ，做一次写 store 操作
    // 调整后允许在 query 中修改 workspaceId 来变更工作区
    let { workspaceId } = to.query;

    if (workspaceId && workspaceId !== nUserModule.workspaceId) {
        // 处理2个同名参数引起的登录异常问题
        const wid = typeof workspaceId === "string" ? workspaceId : (workspaceId[0] as string);

        nUserModule.SET_WORKSPACEID(wid);
        to.query.workspaceId = wid;
    } else {
        workspaceId = to.query.workspaceId = nUserModule.workspaceId;
    }

    if (to.name === "interceptor") {
        next();
        return;
    }

    if (to.name === "errorPage") {
        next();
        return;
    }

    if (!workspaceId) {
        // vip 用户跳转到工作区选择页面，普通用户跳转到登录页面
        nUserModule.isVip ? next({ name: "interceptor" }) : (window.location.href = LoginUrl);
        return;
    }

    if (window.__POWERED_BY_QIANKUN__) {
        handlePageChange(to, next);
    } else {
        await StatisticsModule.GetBasicConfig();
        // 白屏优化开关关闭的情况，需要走旧逻辑
        if (!StatisticsModule.loadOptEnable) {
            try {
                const { hasProduct, packageCount } = await ctFetch<{
                    hasProduct: string | boolean;
                    packageCount: string;
                }>(nProductList, {
                    cache: true,
                    data: {
                        language: "zh",
                        workspaceId: workspaceId as string,
                    },
                });
                if (to.name !== "forbidden" && !hasProduct && parseInt(packageCount) === 0) {
                    next({ name: "forbidden", query: { type: "service" } });
                }
            } catch (err) {
                window.console.warn(err);
            }
        }
        // 自动填充 query: workspaceId
        if (!/.*\?(.*&)?workspaceId=[^\s]+/.test(to.fullPath)) {
            // vue-router v3.1.0 return a promise with push and replace
            // https://github.com/vuejs/vue-router/issues/2881#issuecomment-520554378
            // 如果这里报错了，把 vue-router 版本回退到 v3.0.7
            next({
                name: to.name,
                params: to.params,
                query: to.query,
            });
        } else if (MenuModule.permStrict && !StatisticsModule.loadOptEnable) {
            // 白屏优化开关关闭的情况，需要走旧逻辑
            const menuDomain = getMenuDomain();
            await MenuModule.GetMenuList({
                domain: menuDomain,
                workspaceId: workspaceId as string,
            });
            // 如果启用严格模式
            // 1、判断 route.meta.perm 是否存在于 permList
            // 如果没有设置 perm 则认为不需要鉴权
            const perm = to?.meta?.perm;
            if (!perm || MenuModule.permList.some(p => p === perm)) {
                next();
            } else {
                // 2、不存在则跳到第一个有权限的路由
                const firstMenu = MenuModule.menuList[0];
                if (firstMenu) {
                    const path = (firstMenu.hrefLocal || firstMenu?.items?.[0].hrefLocal)?.replace("#", "");
                    next({ path });
                } else {
                    next({ name: "forbidden", query: { type: "auth" } });
                }
            }
        } else {
            next();
        }
    }
});

// 导出 router utils
export const utils = {
    ...routerUtils,
    // 存在依赖关系，只能在 router 后导出
    redirect: routerUtils.redirectGenerator(router),
};

export default router;
