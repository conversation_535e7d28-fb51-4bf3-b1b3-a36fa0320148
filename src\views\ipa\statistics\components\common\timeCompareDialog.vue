<template>
    <el-dialog
        :modal-append-to-body="false"
        title="设置数据对比时间"
        :visible="visible"
        :close-on-click-modal="false"
        @close="$emit('cancel')"
        width="600px"
    >
        <div class="search-row">
            <ct-time-compare
                :maxDayBeforeNow="maxDayBeforeNow"
                class="compare-wrapper"
                v-model="timeRangeArr"
                key="timeCompare"
                :compareShow="true"
            />
        </div>
        <div slot="footer">
            <el-button type="primary" size="medium" @click="handleSubmit">确 定</el-button>
            <el-button size="medium" @click="$emit('cancel')">取 消</el-button>
        </div>
    </el-dialog>
</template>

<script lang="ts">
import { Component, Prop, Watch, Vue } from "vue-property-decorator";
import ctTimeCompare from "@/components/ctTimeCompare/index.vue";

@Component({
    components: { ctTimeCompare },
})
export default class TimeCompareDialog extends Vue {
    @Prop({ type: Boolean, default: false }) private visible!: boolean;
    @Prop({ type: Number, default: 365 }) private maxDayBeforeNow!: number;
    @Prop({ type: Array, default: () => [null, null] }) private parentTimeRangeArr!: (Date | null)[];

    private timeRangeArr = [null, null];

    handleSubmit() {
        if (this.timeRangeArr?.includes(null)) {
            this.$message.error("请选择时间");
            return;
        }
        this.$emit("confirm", this.timeRangeArr);
    }
}
</script>
<style lang="scss" scoped>
.compare-wrapper {
    ::v-deep {
        .ct-time-compare {
            .search-label {
                display: inline-block;
                margin-right: 16px !important;
            }
        }
        .text {
            margin: 8px 0 !important;
            text-align: center;
            padding-left: 68px;
        }
    }
}
.search-row {
    ::v-deep {
        .el-date-editor--datetimerange.el-input__inner {
            width: 336px !important;
        }
    }
}
</style>
