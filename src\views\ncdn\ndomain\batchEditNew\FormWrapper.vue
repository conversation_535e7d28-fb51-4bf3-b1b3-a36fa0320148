<template>
    <ct-section-wrap class="form-wrapper" :show-breadcrumb="false" scroll-class="ct-config">
        <el-alert :title="$t('domain.batch.以下根据勾选的功能进行覆盖下发')" type="warning" show-icon />
        <div class="anchor-scroll-wrapper">
            <div class="anchor-scroll-content">
                <p id="div1" class="label-name">
                    {{ $t("domain.detail.tab3") }}
                </p>
                <component
                    v-for="item in ['origin', 'protocol', 'proxyGmsslMode', 'originHost']"
                    :ref="item"
                    :is="item"
                    :key="item"
                    :domain-list="domainList"
                ></component>
                <p id="div2" class="label-name">
                    <!-- cdn 入口 小标题静态配置改为缓存配置，对应锚点也修改 -->
                    {{ isPoweredByQiankun ? $t("domain.editPage.label27") : $t("domain.detail.tab6") }}
                </p>
                <component
                    v-for="item in ['fileTypeTtl', 'errorCode']"
                    :ref="item"
                    :is="item"
                    :key="item"
                ></component>
                <!-- 头部修改 -->
                <p id="div3" class="label-name">
                    {{ $t("domain.editPage.label14") }}
                </p>
                <component
                    v-for="item in ['responseHeader', 'requestHeader']"
                    :ref="item"
                    :is="item"
                    :key="item"
                ></component>
                <template v-if="!isPoweredByQiankun">
                    <p id="div4" class="label-name">
                        {{ $t("domain.create.accessControl") }}
                    </p>
                    <component
                        v-for="item in ['ipBlackWhiteList', 'referer']"
                        :ref="item"
                        :is="item"
                        :key="item"
                    ></component>
                </template>
                <!-- 辅助锚点定位 -->
                <div class="anchor-helper-block-1" />
            </div>

            <ct-anchor
                class="anchor-scroll-anchor"
                ref="anchor"
                :data-key="anchorChildKey"
                :data="basicAnchorList"
                scroll-dom=".ct-config"
                @update-child="updateAnchorChild"
            />
        </div>
    </ct-section-wrap>
</template>

<script lang="ts">
import { Component, Mixins, Prop } from "vue-property-decorator";
import AnchorMixin from "./mixins/anchor.mixin";
// 回源配置
import origin from "./origin/origin.vue";
import protocol from "./origin/protocol.vue";
import originHost from "./origin/originHost.vue";
import proxyGmsslMode from "./origin/proxy_gmssl_mode.vue";
// 头部修改
import requestHeader from "./header/request.vue";
import responseHeader from "./header/response.vue";
// 缓存配置
import fileTypeTtl from "./cache/fileTypeTtl.vue";
import errorCode from "./cache/errorCode.vue";
// 访问控制
import referer from "./access/referer.vue";
import ipBlackWhiteList from "./access/ip.vue";
import BatchItemMixin from "./mixins/batch.mixin";
import { withResolver } from "@/utils";

interface ValidateResult {
    origin: boolean;
    protocol: boolean;
    originHost: boolean;
    fileTypeTtl: boolean;
    errorCode: boolean;
    requestHeader: boolean;
    responseHeader: boolean;
    referer: boolean;
    ipBlackWhiteList: boolean;
}

@Component({
    components: {
        origin,
        protocol,
        originHost,
        proxyGmsslMode,
        requestHeader,
        responseHeader,
        fileTypeTtl,
        errorCode,
        referer,
        ipBlackWhiteList,
    },
})
export default class BatchEditFormWrapper extends Mixins(AnchorMixin) {
    @Prop({ type: Array, default: () => [] }) domainList!: string[];

    get isPoweredByQiankun() {
        return window.__POWERED_BY_QIANKUN__;
    }

    basicAnchorList = [
        { label: `${this.$t("domain.detail.tab3")}`, prop: "#div1" },
        {
            label: window.__POWERED_BY_QIANKUN__
                ? `${this.$t("domain.editPage.label27")}`
                : `${this.$t("domain.detail.tab6")}`,
            prop: "#div2",
        },
        { label: `${this.$t("domain.editPage.label14")}`, prop: "#div3" },
        { label: `${this.$t("domain.create.accessControl")}`, prop: "#div4" },
    ];

    validateResult: ValidateResult = {
        origin: false,
        protocol: false,
        originHost: false,
        fileTypeTtl: false,
        errorCode: false,
        requestHeader: false,
        responseHeader: false,
        referer: false,
        ipBlackWhiteList: false,
    };

    anchorSelector = {
        contentArea: ".anchor-scroll-content",
        viewArea: ".ct-section-wrap",
        emptyBlock: ".anchor-helper-block-1",
    };

    get batchItemRefs() {
        const thisRefs = this.$refs as any;
        const refs = [
            thisRefs.origin[0],
            thisRefs.protocol[0],
            thisRefs.originHost[0],
            thisRefs.proxyGmsslMode[0],
            thisRefs.fileTypeTtl[0],
            thisRefs.errorCode[0],
            thisRefs.requestHeader[0],
            thisRefs.responseHeader[0],
            thisRefs.referer[0],
            thisRefs.ipBlackWhiteList[0],
        ] as BatchItemMixin[];

        return refs;
    }
    async handleValidate() {
        const refs = this.batchItemRefs;
        const atLeastOneChecked = refs.some(ref => ref.checked);
        const promises = refs
            .filter(ref => ref.checked)
            .map(ref => {
                const { promise, resolve } = withResolver<{ valid: boolean; message: string }>();
                const elForm = ref.formRef;
                elForm.validate((valid, invalidFields) => {
                    resolve({
                        valid,
                        message:
                            !valid && Object.keys(invalidFields).length > 0
                                ? Object.values(invalidFields)[0][0].message
                                : null,
                    });
                });

                return promise;
            });
        const allValid = await Promise.all(promises);

        return {
            atLeastOneChecked,
            allValid: allValid.every(v => v.valid),
            message: allValid.find(v => !v.valid)?.message,
        };
    }
    get formData() {
        const refs = this.batchItemRefs;
        return refs
            .filter(ref => ref.checked)
            .map(ref => ref.formData)
            .reduce((acc, cur) => {
                return {
                    ...acc,
                    ...cur,
                };
            }, {});
    }
}
</script>
<style lang="scss" scoped>
.form-wrapper {
    width: calc(100% - 40px);
    height: 100%;
    margin: 20px;
    overflow: hidden;
    display: flex;
    flex-direction: row;
    background: #fff;
    ::v-deep {
        .ct-section-wrap > .el-scrollbar {
            margin: 0;
        }

        .el-scrollbar__wrap {
            scroll-behavior: smooth;
        }
    }
}
.label-name {
    font-weight: 500;
    width: 600px;
    overflow: hidden;
    border-left: 4px solid $theme-color;
    line-height: 14px;
    padding-left: 12px;
    margin: 20px 0;
    height: 14px;
}
.anchor-scroll-wrapper {
    position: relative;

    .anchor-scroll-content {
        width: calc(100% - 180px);
        margin-right: 12px;
    }

    .anchor-scroll-anchor {
        width: 155px;
        position: fixed;
        right: 20px;
        top: 345px;
    }
}
</style>
