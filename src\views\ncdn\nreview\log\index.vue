<template>
    <ct-section-wrap headerText="审核日志" headerTip="支持一年内、最长时间跨度为一个月的审核日志查询。">
        <ct-box :class="[{ 'fix-box': dataList.length > 11 }, 'table-scroll-wrap']">
            <div class="search-bar-wrapper">
                <el-button type="primary" :disabled="!multipleSelection.length" @click="multiDownload">
                    批量下载
                </el-button>
                <div class="search-bar">
                    <div>
                        <label class="search-label">选择域名</label>
                        <domain-select v-model="selectedDomain" :domainOptions="domainOptions" :multiple="true" :loading="domainOptionsLoading" />
                    </div>
                    <div>
                        <label class="search-label">选择时间</label>
                        <el-date-picker
                            v-model="dateRange"
                            type="datetimerange"
                            :clearable="false"
                            value-format="timestamp"
                            :default-time="['00:00:00', '23:59:59']"
                            range-separator="至"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            :picker-options="pickerOptions"
                            @blur="blurDate"
                        />
                    </div>
                    <el-button type="primary" plain @click="search">查 询</el-button>
                </div>
            </div>
            <ct-table
                :dataList="showList"
                :columnConfig="conlumnConfig"
                :tableConfig="{ stripe: false }"
                @selection-change="handleSelectionChange"
                v-loading="loading"
            >
                <template slot="operation-slot" slot-scope="{ scope }">
                    <img
                        class="cloud-download"
                        src="@cdnplus/common/assets/images/common/cloud-download.svg"
                        @click="download(scope.row)"
                    />
                </template>
            </ct-table>

            <div class="pager">
                <el-pagination
                    :small="isXs"
                    :layout="isXs ? 'prev, pager, next' : 'total, sizes, prev, pager, next, jumper'"
                    :total="dataList.length"
                    :current-page.sync="page"
                    :page-size="pageSize"
                    :page-sizes="[10, 30, 50, 100]"
                    :hide-on-single-page="false"
                    @size-change="sizeChange"
                />
            </div>
        </ct-box>
    </ct-section-wrap>
</template>
<script>
import DomainSelect from "@/components/domainsSelect/index.vue";
import { DomainModule } from "@/store/modules/domain";
import { DomainActionEnum, getDomainAction } from "@/config/map";
import { getAm0 } from "@/utils";
import { nReviewUrl } from "@/config/url/ncdn/nreview";
import { ScreenModule } from "@/store/modules/screen";
import { nUserModule } from "@/store/modules/nuser";

export default {
    components: { DomainSelect },
    computed: {
        domainAction() {
            return this.isFcdnCtyunCtclouds ? getDomainAction("ContentAuditLog") : DomainActionEnum.Review;
        },
        // 这个变量貌似没有用到
        domainList() {
            // fcdn-ctyun 体系（国内+国际）需要取ctiam权限管控下的域名数据
            return DomainModule[this.domainAction].list;
        },
        domainOptions() {
            // fcdn-ctyun 体系（国内+国际）需要取ctiam权限管控下的域名数据
            return DomainModule[this.domainAction].options;
        },
        domainOptionsLoading() {
            return DomainModule[this.domainAction].loading;
        },
        isXs() {
            return ScreenModule.width < 600;
        },
        showList() {
            // 分页
            const start = (this.page - 1) * this.pageSize;
            const end = this.page * this.pageSize;
            const showList = this.dataList.slice(start, end);
            return showList;
        },
        isFcdnCtyunCtclouds() {
            return nUserModule.isFcdnCtyunCtclouds;
        },
    },

    data() {
        return {
            loading: false,
            dateRange: "",
            pickerMinDate: "",
            pickerOptions: {
                onPick: obj => {
                    this.pickerMinDate = new Date(obj.minDate).getTime();
                },
                disabledDate: time => {
                    if (this.pickerMinDate) {
                        const day31 = 31 * 24 * 3600 * 1000;
                        const maxTime = this.pickerMinDate + day31;
                        const minTime = this.pickerMinDate - day31;
                        return (
                            time.getTime() > maxTime - 1 ||
                            time.getTime() < minTime + 1 ||
                            time.getTime() > +getAm0(new Date()) + 24 * 3600 * 1000 - 1 ||
                            time.getTime() < Date.now() - 365 * 24 * 3600 * 1000
                        );
                    } else {
                        return (
                            time.getTime() > +getAm0(new Date()) + 24 * 3600 * 1000 - 1 ||
                            time.getTime() < Date.now() - 365 * 24 * 3600 * 1000
                        );
                    }
                },
            },
            multipleSelection: [],
            dataList: [],
            conlumnConfig: [
                {
                    type: "selection",
                },
                //编号
                // {
                //     type: "index",
                //     align: "left",
                // },
                {
                    prop: "domain",
                    label: "文件名",
                    minWidth: "120",
                    align: "left",
                },
                {
                    prop: "startTime",
                    label: "开始时间",
                    minWidth: "120",
                    align: "center",
                },
                {
                    prop: "endTime",
                    label: "结束时间",
                    minWidth: "120",
                    align: "center",
                },
                {
                    type: "slot",
                    name: "operation-slot",
                    baseConfig: { label: "操作", minWidth: "150", align: "center" },
                },
            ],
            selectedDomain: [],
            page: 1,
            pageSize: 10,
        };
    },
    watch: {
        domainOptions: function (arr) {
            if (arr.length > 0) {
                this.selectedDomain = [];
                this.refresh = true;
            }
        },
    },
    methods: {
        // 将时间戳格式化为yyyy-MM-dd
        timeFormat(time) {
            const date = new Date(time);
            const year = date.getFullYear();
            let month = date.getMonth() + 1;
            let day = date.getDate();
            month = month < 10 ? "0" + month : month;
            day = day < 10 ? "0" + day : day;
            return year + "-" + month + "-" + day;
        },

        dataListFormat(list) {
            for (const item of list) {
                item.startTime = item.event_date + " 00:00:00";
                item.endTime = item.event_date + " 23:59:59";
            }
            return list;
        },

        handleSelectionChange(val) {
            this.multipleSelection = val;
        },
        multiDownload() {
            if (this.multipleSelection.length === 0) {
                this.$message.warning("请选择文件后再试");
            } else {
                this.multipleSelection.forEach((log, index) => {
                    const anchor = document.createElement("a");
                    anchor.setAttribute("href", log.log_url);
                    anchor.setAttribute("download", "");
                    anchor.setAttribute("style", "position:absolute;top:-9999px;");
                    document.body.appendChild(anchor);
                    setTimeout(() => {
                        anchor.dispatchEvent(
                            new MouseEvent("click", { bubbles: true, cancelable: true, view: window })
                        );
                        setTimeout(() => {
                            document.body.removeChild(anchor);
                        }, 1000);
                    }, index * 1000);
                });
            }
        },
        download(row) {
            window.open(row.log_url);
        },
        // 重新选择日期时，一年内的日期均可选
        blurDate() {
            this.pickerMinDate = "";
        },
        sizeChange(val) {
            // 恢复到第一页
            this.page = 1;
            this.pageSize = val;
        },
        async search() {
            const startTime = this.dateRange[0] ? this.timeFormat(this.dateRange[0]) : this.dateRange[0];
            const endTime = this.dateRange[1]
                ? this.timeFormat(this.dateRange[1] + 86400000)
                : this.dateRange[1]; // 结束时间与开始时间为同一天时底层无法返回数据，所以改为+1天
            this.loading = true;
            const res = await this.$ctFetch(nReviewUrl.offlineLogList, {
                method: "POST",
                transferType: "json",
                clearEmptyParams: false,
                data: {
                    startTime,
                    endTime,
                    domainList: this.selectedDomain,
                },

                headers: {
                    "Content-Type": "application/json",
                },
            });
            this.dataList = res.list;
            for (const item of this.dataList) {
                item.startTime = item.event_date + " 00:00:00";
                item.endTime = item.event_date + " 23:59:59";
            }
        },
    },
    created() {
        const now = new Date();
        const d0am = getAm0(now); // 当日0点
        const d31ago = new Date(+d0am - 30 * 24 * 3600 * 1000); // 31天前
        // 默认以当前时间结束
        this.dateRange = [+d31ago, +now];
    },
};
</script>

<style lang="scss" scoped>
.search-bar-wrapper {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 12px;
    .search-bar {
        display: flex;
        gap: 12px;
        flex-wrap: wrap;
        > div {
            flex-shrink: 0;
            display: flex;
            gap: 12px;
            align-items: center;
            flex-wrap: nowrap;
            label {
                font-size: 12px;
            }
        }
    }
}

.cloud-download {
    width: 24px;
    cursor: pointer;
    vertical-align: middle;
}

// 非小屏才需要，小屏下还是滚动吧
@media screen and (min-width: $g-media-xs) {
    .fix-box {
        display: flex;
        flex-direction: column;
        height: calc(100vh - 50px - 90px - 40px);
        @include g-height(calc(100vh - 50px - 90px - 40px), calc(100vh - 50px - 90px - 40px - 60px));

        .el-table {
            flex: 1;
        }
    }
    ::v-deep .el-table__body-wrapper {
        height: 100%;
        overflow-y: auto;
    }
}

//更改表格滚动条样式
::v-deep {
    .el-table {
        ::-webkit-scrollbar {
            width: 0.25rem;
            height: 0.25rem;
            background-color: rgba(144, 147, 153, 0.1);
        }

        ::-webkit-scrollbar-track {
            border-radius: 0;
        }

        ::-webkit-scrollbar-thumb {
            border-radius: 0;
            background-image: linear-gradient(
                135deg,
                rgba(144, 147, 153, 0.3) 0%,
                rgba(144, 147, 153, 0.3) 72%,
                rgba(144, 147, 153, 0.3) 100%
            );
            transition: all 0.2s;
            border-radius: 0.25rem;

            &:hover {
                background-color: rgba(144, 147, 153, 0.5);
            }
        }
    }
    .el-date-editor .el-range-separator {
        padding: 0;
    }
    .ist-pager {
        @include g-mg-tp(5px, 8px, 10px);
        ::v-deep .el-pagination {
            text-align: left;
        }
    }
}
</style>
