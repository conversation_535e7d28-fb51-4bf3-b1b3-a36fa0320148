<template>
    <section class="ct-section-wrap" v-loading="detailLoading">
        <el-scrollbar wrap-class="ct-config">
            <section class="content-wrap">
                <template v-if="!isService && !fromAccelerationConfig">
                    <el-alert
                        class="header-tip-style"
                        type="info"
                        show-icon
                        :title="currentAbnormalText"
                        :closable="false"
                    />
                </template>
                <div id="section_div0">
                    <p id="div0" class="label-name base-info-style" v-if="!fromAccelerationConfig">
                        {{ $t("domain.editPage.label1") }}
                    </p>
                    <basic-info
                        v-if="!fromAccelerationConfig"
                        :baseInfo="baseInfo"
                        :defaultData="defaultData"
                    />
                </div>
                <div class="line-style" v-if="!fromAccelerationConfig"></div>
                <section
                    class="domain-edit"
                    :class="[fromAccelerationConfig && 'domain-edit2', !isFlatten && 'domain-edit-spacing']"
                >
                    <div
                        class="content-wrap_left"
                        :class="fromAccelerationConfig ? 'content-wrap_left2' : ''"
                    >
                        <el-form
                            class="simple-create-form"
                            name="form"
                            ref="form"
                            :model="form"
                            label-width="141px"
                            label-position="left"
                            :disabled="!isEdit || !isService"
                        >
                            <div>
                                <div id="section_div1" class="form-section">
                                    <p id="div1" class="label-name" :class="{ 'mt-0': !isPoweredByQiankun }">
                                        {{ $t("domain.detail.tab3") }}
                                    </p>
                                    <!-- 源站：子组件 -->
                                    <!-- @onBucketChange="onBucketChange" -->
                                    <lock-tip
                                        :lock="isLockOrigin || isLockXosOrigin || isLockZosOrigin"
                                        :isDomainEndsWithCtyun="isDomainEndsWithCtyun"
                                    >
                                        <origin
                                            ref="subOrigin"
                                            :isLockOrigin="isLockOrigin"
                                            :isLockXosOrigin="isLockXosOrigin"
                                            :isLockZosOrigin="isLockZosOrigin"
                                            :isDomainEndsWithCtyun="isDomainEndsWithCtyun"
                                            :datas="form.origin"
                                            :domain="form.domain"
                                            :reqHost="form.req_host"
                                            :isNewEcgw="isNewEcgw"
                                            :temp_domain_detail="temp_domain_detail"
                                            @onChange="onOriginChange"
                                            @onBucketChange="onBucketChange"
                                            @validateReqHost="handleValidateReqHost"
                                        ></origin>
                                    </lock-tip>

                                    <!-- 回源协议 -->
                                    <domain-ends-tip :endsup="isDomainEndsWithCtyun">
                                        <el-form-item
                                            prop="backorigin_protocol"
                                            :rules="rules.backorigin_protocol"
                                        >
                                            <span slot="label">
                                                {{ $t("domain.create.originPolicy") }}
                                                <span>
                                                    <el-tooltip
                                                        placement="top"
                                                        :content="$t('domain.create.tip6')"
                                                    >
                                                        <ct-svg-icon
                                                            icon-class="question-circle"
                                                            class-name="ct-sort-drag-icon"
                                                        ></ct-svg-icon>
                                                    </el-tooltip>
                                                </span>
                                            </span>
                                            <el-radio-group
                                                v-model="form.backorigin_protocol"
                                                @change="origin_protocol_change"
                                                :disabled="isDomainEndsWithCtyun"
                                            >
                                                <el-radio label="http">{{
                                                    $t("domain.editPage.radio1[0]")
                                                }}</el-radio>
                                                <el-radio label="https">{{
                                                    $t("domain.editPage.radio1[1]")
                                                }}</el-radio>
                                                <el-radio label="follow_request">{{
                                                    $t("domain.editPage.radio1[2]")
                                                }}</el-radio>
                                            </el-radio-group>
                                        </el-form-item>
                                    </domain-ends-tip>
                                    <!-- 回源加密算法-->
                                    <domain-ends-tip
                                        :endsup="isDomainEndsWithCtyun"
                                        :addon="form.backorigin_protocol !== 'http'"
                                    >
                                        <el-form-item
                                            :label="$t('domain.create.originEncodeSelf')"
                                            prop="proxy_gmssl_mode"
                                        >
                                            <el-select
                                                v-model="form.proxy_gmssl_mode"
                                                :placeholder="$t('domain.create.originEncodeSelfPlaceholder')"
                                                style="width:395px"
                                                :disabled="isDomainEndsWithCtyun"
                                                clearable
                                            >
                                                <el-option
                                                    :label="$t('domain.create.proxyGmsslModeList.0')"
                                                    value="on"
                                                />
                                                <el-option
                                                    :label="$t('domain.create.proxyGmsslModeList.1')"
                                                    value="off"
                                                />
                                                <el-option
                                                    :label="$t('domain.create.proxyGmsslModeList.2')"
                                                    value="follow"
                                                />
                                            </el-select>
                                        </el-form-item>
                                    </domain-ends-tip>
                                    <!-- 回源端口 -->
                                    <div class="server_port_wrapper">
                                        <lock-tip
                                            :isDomainEndsWithCtyun="isDomainEndsWithCtyun"
                                            :originPortDisabled="originPortDisabled"
                                        >
                                            <div class="server_style">
                                                <el-form-item
                                                    :label="$t('domain.editPage.label4')"
                                                    class="is-required"
                                                >
                                                </el-form-item>
                                                <el-form-item
                                                    v-if="form.backorigin_protocol !== 'https'"
                                                    label="HTTP"
                                                    prop="basic_conf.http_origin_port"
                                                    :rules="rules.http_origin_port"
                                                    class="server_http_port_wrapper"
                                                >
                                                    <el-input
                                                        v-model.number="form.basic_conf.http_origin_port"
                                                        maxlength="16"
                                                        class="port-input"
                                                        :disabled="
                                                            isDomainEndsWithCtyun || originPortDisabled
                                                        "
                                                    ></el-input>
                                                </el-form-item>
                                                <el-form-item
                                                    v-if="form.backorigin_protocol !== 'http'"
                                                    label="HTTPS"
                                                    prop="basic_conf.https_origin_port"
                                                    :rules="rules.https_origin_port"
                                                    class="server_https_port_wrapper"
                                                >
                                                    <el-input
                                                        v-model.number="form.basic_conf.https_origin_port"
                                                        maxlength="16"
                                                        class="port-input"
                                                        :disabled="
                                                            isDomainEndsWithCtyun || originPortDisabled
                                                        "
                                                    ></el-input>
                                                </el-form-item>
                                            </div>
                                        </lock-tip>
                                        <!-- 跟随请求端口回源 -->
                                        <el-form-item prop="follow_request_backport" label-width="150px">
                                            <span slot="label">
                                                {{ $t("domain.editPage.label32") }}
                                                <el-tooltip
                                                    placement="top"
                                                    :content="$t('domain.editPage.originPortTip.tip2')"
                                                >
                                                    <ct-svg-icon
                                                        icon-class="question-circle"
                                                        class-name="ct-sort-drag-icon"
                                                    ></ct-svg-icon>
                                                </el-tooltip>
                                            </span>
                                            <el-switch
                                                v-model="form.follow_request_backport"
                                                :active-value="1"
                                                :inactive-value="0"
                                            ></el-switch>
                                        </el-form-item>
                                    </div>
                                    <!-- 分片回源 -->
                                    <lock-tip
                                        :lock="isLockSplitSet"
                                        :addon="isNewEcgw && form.split_white && !!form.split_set"
                                    >
                                        <split-set
                                            v-model="form.split_set"
                                            ref="splitSetRef"
                                            :disabled="!isEdit || !isService || isLockSplitSet"
                                        />
                                    </lock-tip>
                                    <lock-tip
                                        :addon="(showForStaticAndIcdn || isPoweredByQiankun) && isNewEcgw"
                                    >
                                        <sni v-model="form.backorigin_sni" />
                                    </lock-tip>
                                    <!-- 私有Bucket回源：无功能锁 -->
                                    <lock-tip :lock="isLockXosOrigin || isLockZosOrigin">
                                        <private-bucket-origin
                                            :datas="form"
                                            :isLockXosOrigin="isLockXosOrigin"
                                            :isLockZosOrigin="isLockZosOrigin"
                                            :temp_domain_detail="temp_domain_detail"
                                            :isShowPrivateBucketOrigin="isShowPrivateBucketOrigin"
                                            @onChange="onPrivateBucketOriginChange"
                                            ref="privateBucketOrigin"
                                        ></private-bucket-origin>
                                    </lock-tip>
                                    <!-- backOriginTime：无功能锁 -->
                                    <lock-tip :lock="isLockBackOriginTime">
                                        <backOriginTime
                                            :datas="form"
                                            :isLockBackOriginTime="isLockBackOriginTime"
                                            :temp_domain_detail="temp_domain_detail"
                                            @onChange="onBackOriginTimeChange"
                                            ref="backoriginTime"
                                        ></backOriginTime>
                                    </lock-tip>
                                    <!-- 回源302/301跟随 -->
                                    <div v-if="isNewEcgw">
                                        <el-form-item :label="$t('domain.detail.label72')" prop="follow_302">
                                            <el-switch
                                                v-model="form.follow_302"
                                                :active-value="1"
                                                :inactive-value="0"
                                                @change="follow_302_change"
                                            ></el-switch>
                                        </el-form-item>
                                        <div v-if="form.follow_302 === 1" class="switch-wrapper">
                                            <el-form-item
                                                :label="$t('domain.detail.label74')"
                                                prop="follow302_times"
                                                :rules="rules.follow302_times"
                                                ><span slot="label"
                                                    >{{ $t("domain.detail.label74") }}
                                                    <span>
                                                        <el-tooltip
                                                            placement="top"
                                                            :content="$t('domain.detail.tip64')"
                                                        >
                                                            <ct-svg-icon
                                                                icon-class="question-circle"
                                                                class-name="ct-sort-drag-icon"
                                                            ></ct-svg-icon></el-tooltip></span
                                                ></span>
                                                <el-input
                                                    v-model.number="form.follow302_times"
                                                    maxlength="16"
                                                    :placeholder="$t('domain.enter')"
                                                    style="width: 395px"
                                                ></el-input>
                                            </el-form-item>
                                        </div>
                                    </div>
                                    <div class="req_host_style">
                                        <lock-tip :isDomainEndsWithCtyun="isDomainEndsWithCtyun">
                                            <!-- 回源HOST -->
                                            <div>
                                                <host-select-mod
                                                    :label="$t('domain.默认回源HOST')"
                                                    v-model="form.req_host"
                                                    prop="req_host"
                                                    :accelerate-domains="[securityDomain]"
                                                    :origin-domains="
                                                        form.origin
                                                            .map(itm => itm.origin)
                                                            .filter(itm => !!itm)
                                                    "
                                                    :rules="rules.req_host"
                                                    ref="req_host"
                                                    :disabled="isDomainEndsWithCtyun"
                                                    @change="handleReqHostChange"
                                                ></host-select-mod>
                                            </div>
                                        </lock-tip>
                                        <!-- 回源302/301跟随时生效 -->
                                        <div style="margin-left: 20px">
                                            <el-form-item label-width="0">
                                                <el-checkbox-group
                                                    v-model="form.follow302_is_valid"
                                                    v-if="form.follow_302 === 1 && isNewEcgw"
                                                >
                                                    <el-checkbox :label="1">
                                                        {{ $t("domain.editPage.label18") }}
                                                    </el-checkbox>
                                                </el-checkbox-group>
                                            </el-form-item>
                                        </div>
                                    </div>
                                    <!-- 回源URI改写 子组件 -->
                                    <lock-tip :lock="isLockOriginUrlRewrite">
                                        <internal-uri-rewrite-new
                                            ref="internalUriRewriteNew"
                                            :datas="form"
                                            :isLockOriginUrlRewrite="isLockOriginUrlRewrite"
                                            :temp_domain_detail="temp_domain_detail"
                                            @onChange="onInternalUriRewriteNewChange"
                                        ></internal-uri-rewrite-new>
                                    </lock-tip>
                                    <!-- 忽略回源参数开关 有自助功能锁 -->
                                    <lock-tip :lock="isLockUrlParams">
                                        <el-form-item
                                            :label="$t('domain.detail.label75')"
                                            prop="ignore_backorigin_args"
                                            :rules="rules.ignore_backorigin_args"
                                            ref="ignore_backorigin_args"
                                            v-if="temp_domain_detail.use_ecgw === 1"
                                        >
                                            <el-switch
                                                v-model="form.ignore_backorigin_args"
                                                :active-value="1"
                                                :inactive-value="0"
                                                :disabled="isLockUrlParams"
                                            ></el-switch>
                                        </el-form-item>
                                    </lock-tip>
                                    <!-- 回源参数规则 -->
                                    <lock-tip :lock="isLockParamsRewrite">
                                        <el-form-item
                                            :label="$t('domain.detail.label30')"
                                            prop="backorigin_arg_rewrite_other"
                                            ref="backoriginArgRewriteOther"
                                            class="cache-table-style"
                                            v-if="temp_domain_detail.use_ecgw === 1"
                                            ><span slot="label"
                                                >{{ $t("domain.detail.label30") }}
                                                <span>
                                                    <el-tooltip placement="top">
                                                        <div slot="content">
                                                            {{ $t("domain.detail.tip69") }}
                                                            <a
                                                                v-if="show_url"
                                                                :underline="false"
                                                                class="word-wrap aocdn-ignore-link"
                                                                style="color:#3d73f5"
                                                                @click="
                                                                    $docHelp(backorigin_arg_rewrite_other_url)
                                                                "
                                                                >{{ $t("domain.detail.tip23") }}
                                                            </a>
                                                        </div>
                                                        <ct-svg-icon
                                                            icon-class="question-circle"
                                                            class-name="ct-sort-drag-icon"
                                                        ></ct-svg-icon></el-tooltip></span
                                            ></span>
                                            <div class="ct-table-wrapper">
                                                <el-table
                                                    class="origin-table"
                                                    :data="form.backorigin_arg_rewrite_other"
                                                    ref="filetypeTtl"
                                                >
                                                    <el-table-column
                                                        prop="keep_args_order"
                                                        :label="$t('domain.detail.label121')"
                                                    >
                                                        <template slot-scope="scope">{{
                                                            scope.row.keep_args_order === "on"
                                                                ? $t("domain.detail.label96")
                                                                : $t("domain.detail.label97")
                                                        }}</template>
                                                    </el-table-column>
                                                    <el-table-column
                                                        prop="need_encode_args"
                                                        :label="$t('domain.detail.label122')"
                                                    >
                                                        <template slot-scope="scope">{{
                                                            scope.row.keep_args_order === "on"
                                                                ? scope.row.need_encode_args === "on"
                                                                    ? $t("domain.editPage.label24")
                                                                    : $t("domain.editPage.label25")
                                                                : ""
                                                        }}</template>
                                                    </el-table-column>
                                                    <el-table-column
                                                        prop="mode"
                                                        :label="$t('domain.detail.label76')"
                                                    >
                                                        <template slot-scope="scope">{{
                                                            rewriteModeMap[scope.row.mode]
                                                        }}</template>
                                                    </el-table-column>
                                                    <el-table-column
                                                        prop="priority"
                                                        :label="$t('domain.detail.label48')"
                                                    />
                                                    <el-table-column
                                                        prop="argsList"
                                                        :label="$t('domain.detail.label77')"
                                                    >
                                                        <template #default="{ row }">
                                                            <div v-for="(arg, ix) in row.argsList" :key="ix">
                                                                {{ arg.name }}: {{ arg.value }}
                                                            </div>
                                                        </template>
                                                    </el-table-column>
                                                    <el-table-column
                                                        :label="$t('domain.operate')"
                                                        width="120"
                                                    >
                                                        <template slot-scope="scope">
                                                            <el-button
                                                                type="text"
                                                                :disabled="
                                                                    !isEdit ||
                                                                        !isService ||
                                                                        isLockParamsRewrite
                                                                "
                                                                @click="
                                                                    handleOper(
                                                                        scope.row,
                                                                        'edit',
                                                                        'backorigin_arg_rewrite_other',
                                                                        scope.$index
                                                                    )
                                                                "
                                                                >{{ $t("domain.modify") }}</el-button
                                                            >
                                                            <el-button
                                                                type="text"
                                                                :disabled="
                                                                    !isEdit ||
                                                                        !isService ||
                                                                        isLockParamsRewrite
                                                                "
                                                                @click="
                                                                    handleOper(
                                                                        scope.row,
                                                                        'delete',
                                                                        'backorigin_arg_rewrite_other',
                                                                        scope.$index
                                                                    )
                                                                "
                                                                >{{ $t("domain.delete") }}</el-button
                                                            >
                                                        </template>
                                                    </el-table-column>
                                                </el-table>
                                                <!-- 回源参数规则 添加按钮 -->
                                                <div class="flex-row-style button-box">
                                                    <el-button
                                                        class="btn"
                                                        type="text"
                                                        :disabled="
                                                            !isEdit || !isService || isLockParamsRewrite
                                                        "
                                                        @click="
                                                            handleOper(
                                                                null,
                                                                'create',
                                                                'backorigin_arg_rewrite_other'
                                                            )
                                                        "
                                                    >
                                                        + {{ addButtonText }}
                                                    </el-button>
                                                </div>
                                            </div>
                                        </el-form-item>
                                    </lock-tip>
                                </div>

                                <div id="section_div2" class="form-section">
                                    <!-- 请求协议 -->
                                    <p id="div2" class="label-name">
                                        {{ $t("domain.editPage.label12") }}
                                    </p>
                                    <el-form-item
                                        :label="$t('domain.editPage.label12')"
                                        prop="request_protocol"
                                        :rules="rules.request_protocol"
                                    >
                                        <el-checkbox-group
                                            v-model="form.request_protocol"
                                            @change="handleHttpsStatusChange"
                                        >
                                            <el-checkbox label="http">HTTP</el-checkbox>
                                            <!-- https未开通提示优先于功能锁 -->
                                            <cdn-https-support-tip
                                                :already-open="
                                                    (
                                                        (securityBasicConfigOriginForm &&
                                                            securityBasicConfigOriginForm.request_protocol) ||
                                                        []
                                                    ).includes('https')
                                                "
                                                :isCDN="temp_domain_detail.product_code === '008'"
                                                style="display: inline-block; margin-left: 16px"
                                            >
                                                <div slot-scope="{ httpsDisabled }">
                                                    <lock-tip :lock="isLockRequestProtocol && !httpsDisabled">
                                                        <el-checkbox
                                                            label="https"
                                                            :disabled="isLockRequestProtocol || httpsDisabled"
                                                            >HTTPS</el-checkbox
                                                        >
                                                    </lock-tip>
                                                </div>
                                            </cdn-https-support-tip>
                                        </el-checkbox-group>
                                    </el-form-item>
                                    <!-- 服务端口 -->
                                    <div>
                                        <server-port
                                            :datas="form"
                                            :requestProtocolIncludesHttp="requestProtocolIncludesHttp"
                                            :requestProtocolIncludesHttps="requestProtocolIncludesHttps"
                                            :isServerPortChange="isServerPortChange"
                                            :isLockRequestProtocol="isLockRequestProtocol"
                                            @onChange="onServerPortChange"
                                            ref="serverPort"
                                        ></server-port>
                                    </div>
                                </div>
                                <div id="section_div3" class="form-section">
                                    <p id="div3" class="label-name">
                                        {{ $t("domain.detail.tab5") }}
                                        <span v-show="!requestProtocolIncludesHttps">
                                            <span>
                                                <ct-svg-icon
                                                    icon-class="info-circle"
                                                    class-name="icon-column-label icon-column-label2"
                                                />
                                            </span>
                                            <span class="https-tip">
                                                {{ $t("domain.editPage.tip3") }}
                                            </span>
                                        </span>
                                    </p>
                                    <lock-tip :lock="isLockRequestProtocol">
                                        <div>
                                            <div v-if="isNewUserAndIsCdn" class="origin-tip-header">
                                                <span>
                                                    <ct-svg-icon
                                                        icon-class="info-circle"
                                                        class-name="icon-column-label"
                                                    />
                                                </span>
                                                <span class="origin-tip">
                                                    <i18n path="domain.create.tip49">
                                                        <a
                                                            class="aocdn-ignore-link"
                                                            @click="
                                                                $docHelp(
                                                                    'https://www.ctyun.cn/document/10015932/10448893'
                                                                )
                                                            "
                                                            >{{ $t("domain.create.tip49-1") }}</a
                                                        >
                                                    </i18n>
                                                </span>
                                            </div>
                                            <!-- <el-form-item v-show="!requestProtocolIncludesHttps">
                                                <div>
                                                    <span>
                                                        <ct-svg-icon
                                                            icon-class="info-circle"
                                                            class-name="icon-column-label icon-column-label2"
                                                        />
                                                    </span>
                                                    <span class="https-tip">
                                                        {{ $t("domain.editPage.tip3") }}
                                                    </span>
                                                </div>
                                            </el-form-item> -->

                                            <!-- 国际标准证书 -->
                                            <cert-name
                                                v-if="requestProtocolIncludesHttps"
                                                v-model="form.cert_name"
                                                :enable="requestProtocolIncludesHttps"
                                                :lock="isLockRequestProtocol || isLockSslCiphers"
                                                :cert_name_list="cert_name_list"
                                                :getCertList="getCertList"
                                                @change="val => handleCertNameChange(val, false)"
                                                ref="cert_name"
                                                prop="cert_name"
                                                :rules="cert_name_rule"
                                            />
                                            <!-- 国密证书 -->
                                            <cert-name
                                                v-if="requestProtocolIncludesHttps"
                                                v-model="form.cert_name_gm"
                                                :enable="requestProtocolIncludesHttps"
                                                :lock="isLockRequestProtocol || isLockSslCiphers"
                                                :cert_name_list="cert_name_list"
                                                :getCertList="getCertList"
                                                isSm2
                                                @change="val => handleCertNameChange(val, true)"
                                                ref="cert_name_gm"
                                                prop="cert_name_gm"
                                                :rules="[...cert_name_rule, ...rules.cert_name_gm_quic]"
                                            />

                                            <el-form-item
                                                label="HTTP2.0"
                                                v-show="requestProtocolIncludesHttps && hasCertName"
                                            >
                                                <el-switch
                                                    v-model="form.basic_conf.use_http2"
                                                    :disabled="
                                                        !requestProtocolIncludesHttps || isLockRequestProtocol
                                                    "
                                                    :active-value="1"
                                                    :inactive-value="0"
                                                ></el-switch>
                                            </el-form-item>
                                            <!-- OCSP Stapling -->
                                            <el-form-item
                                                label="OCSP Stapling"
                                                prop="ssl_stapling"
                                                v-if="showOcspStapling && hasCertName"
                                            >
                                                <el-switch
                                                    v-model="form.ssl_stapling"
                                                    active-value="on"
                                                    inactive-value="off"
                                                    :disabled="isLockRequestProtocol"
                                                ></el-switch>
                                            </el-form-item>
                                            <!-- TLS版本 -->
                                            <el-form-item
                                                v-if="
                                                    requestProtocolIncludesHttps && isNewEcgw && hasCertName
                                                "
                                                :label="$t('domain.create.tls')"
                                                prop="ssl"
                                                :rules="rules.ssl"
                                            >
                                                <el-tooltip
                                                    placement="top"
                                                    :content="$t('domain.create.placeholder2')"
                                                >
                                                    <el-select
                                                        style="width: 380px"
                                                        v-model="form.ssl"
                                                        ref="ssl"
                                                        clearable
                                                        multiple
                                                        :placeholder="$t('domain.create.placeholder2')"
                                                        filterable
                                                        default-first-option
                                                        :disabled="isLockRequestProtocol"
                                                    >
                                                        <el-option
                                                            v-for="itm in ssl_list"
                                                            :key="itm"
                                                            :label="itm"
                                                            :value="itm"
                                                        ></el-option>
                                                    </el-select>
                                                </el-tooltip>
                                            </el-form-item>

                                            <!-- HSTS -->
                                            <div v-if="isNewEcgw">
                                                <hsts
                                                    ref="hsts"
                                                    :datas="form"
                                                    @onChange="onHstsChange"
                                                    :requestProtocolIncludesHttps="
                                                        requestProtocolIncludesHttps
                                                    "
                                                    :isLockRequestProtocol="isLockRequestProtocol"
                                                ></hsts>
                                            </div>

                                            <!-- 加密套件 -->
                                            <lock-tip
                                                :lock="isLockSslCiphers"
                                                :addon="requestProtocolIncludesHttps"
                                            >
                                                <encryption-suite
                                                    ref="encryptionSuite"
                                                    :datas="form"
                                                    :isHttpsStatusOpen="true"
                                                    :certName="hasCertName"
                                                    :isLockRequestProtocol="isLockSslCiphers"
                                                    @onChange="onEncryptionSuiteChange"
                                                    :isSm2="!!form.cert_name_gm"
                                                    :isIntlCert="!!form.cert_name"
                                                ></encryption-suite>
                                            </lock-tip>

                                            <quic-config-component
                                                v-if="
                                                    getQuicParams.quicEnable &&
                                                        isNewEcgw &&
                                                        requestProtocolIncludesHttps &&
                                                        hasCertName
                                                "
                                                v-model="form.quic.switch"
                                                :isSm2="!!form.cert_name_gm"
                                                @change="publicRevalidator(['cert_name_gm'])"
                                            />

                                            <!-- 强制跳转 -->
                                            <el-form-item
                                                :label="$t('domain.detail.label22')"
                                                prop="force_jump.jump_enable"
                                                ><span slot="label"
                                                    >{{ $t("domain.detail.label22") }}
                                                    <span>
                                                        <el-tooltip
                                                            placement="top"
                                                            :content="$t('domain.editPage.tip5')"
                                                        >
                                                            <ct-svg-icon
                                                                icon-class="question-circle"
                                                                class-name="ct-sort-drag-icon"
                                                            ></ct-svg-icon></el-tooltip></span
                                                ></span>
                                                <el-switch
                                                    v-model="form.force_jump.jump_enable"
                                                    @change="onJumpEnableChange"
                                                ></el-switch>
                                            </el-form-item>

                                            <div v-if="form.force_jump.jump_enable" class="switch-wrapper">
                                                <el-form-item
                                                    :label="$t('domain.detail.label70')"
                                                    prop="force_jump.force_type"
                                                    :rules="rules.force_type"
                                                    key="force_type"
                                                    class="jump-style"
                                                >
                                                    <el-radio-group
                                                        v-model.trim="form.force_jump.force_type"
                                                        :disabled="!form.force_jump.jump_enable"
                                                    >
                                                        <el-radio
                                                            v-if="requestProtocolIncludesHttp"
                                                            label="http"
                                                            >Https -> Http</el-radio
                                                        >
                                                        <el-radio
                                                            v-if="requestProtocolIncludesHttps"
                                                            label="https"
                                                            >Http -> Https</el-radio
                                                        >
                                                    </el-radio-group>
                                                </el-form-item>
                                                <el-form-item
                                                    :label="$t('domain.detail.label71')"
                                                    prop="force_jump.force_status"
                                                    :rules="rules.force_status"
                                                    class="jump-style"
                                                >
                                                    <el-radio-group
                                                        v-model="form.force_jump.force_status"
                                                        :disabled="!form.force_jump.jump_enable"
                                                    >
                                                        <el-radio label="302">
                                                            {{ $t("domain.detail.jumpType[0]") }}
                                                        </el-radio>
                                                        <el-radio label="301">
                                                            {{ $t("domain.detail.jumpType[1]") }}
                                                        </el-radio>
                                                    </el-radio-group>
                                                </el-form-item>
                                            </div>
                                        </div>
                                    </lock-tip>
                                </div>

                                <div id="section_div4" class="form-section">
                                    <p id="div4" class="label-name">
                                        {{ $t("domain.editPage.label14") }}
                                    </p>
                                    <!-- HTTP响应头 子组件 -->
                                    <lock-tip
                                        :lock="isLockRespHeader"
                                        :isDomainEndsWithCtyun="isDomainEndsWithCtyun"
                                        :fromRespHeaders="true"
                                        :respHeadersShowXosDomainTip="respHeadersShowXosDomainTip"
                                    >
                                        <div>
                                            <resp-Headers
                                                ref="respHeaders"
                                                :datas="form"
                                                @onChange="onRespHeadersChange"
                                                :isLockRespHeader="isLockRespHeader"
                                                :isDomainEndsWithCtyun="isDomainEndsWithCtyun"
                                            >
                                            </resp-Headers>
                                        </div>
                                    </lock-tip>
                                    <!-- 回源HTTP请求头 子组件 -->
                                    <lock-tip :lock="isLockReqHeader">
                                        <div>
                                            <req-headers
                                                ref="reqHeaders"
                                                :datas="form"
                                                @onChange="onReqHeadersChange"
                                                :isLockReqHeader="isLockReqHeader"
                                            ></req-headers>
                                        </div>
                                    </lock-tip>
                                </div>

                                <!-- 文件处理 -->
                                <div v-if="isNewEcgw" id="section_div5" class="form-section">
                                    <p id="div5" class="label-name">
                                        {{ $t("domain.detail.tab10") }}
                                    </p>
                                    <!-- 文件压缩 子组件 -->
                                    <lock-tip :lock="isLockGzipConf">
                                        <cus-gzip
                                            ref="cusGzip"
                                            :datas="form"
                                            :isLockGzipConf="isLockGzipConf"
                                            @onChange="onCusGzipChange"
                                        ></cus-gzip>
                                    </lock-tip>
                                </div>

                                <div v-if="isNewEcgw" id="section_URLAuthentication" class="form-section">
                                    <p id="URLAuthentication" class="label-name">
                                        {{ $t("domain.detail.label17") }}
                                    </p>
                                    <!-- URL鉴权 -->
                                    <lock-tip :lock="urlAuthFormDisabled || isLockUrlAuth">
                                        <url-auth
                                            :datas="form"
                                            :urlAuthFormDisabled="urlAuthFormDisabled"
                                            :isLockUrlAuth="isLockUrlAuth"
                                            :originalConf="temp_domain_detail"
                                            @onChange="onUrlAuthChange"
                                            ref="urlAuth"
                                        >
                                        </url-auth>
                                    </lock-tip>
                                </div>

                                <!-- 远程同步鉴权 -->
                                <remote-sync-auth
                                    id="section_remoteSyncAuth"
                                    key="remote_sync_auth_key"
                                    ref="remote_sync_auth"
                                    :class="{ 'form-section': isPoweredByQiankun }"
                                    :isNewEcgw="isNewEcgw"
                                    :isLockRemoteSyncAuth="isLockRemoteSyncAuth"
                                />

                                <!-- 访问控制：该功能cdn独有，aocdn没有该功能 -->
                                <div v-if="showForStaticAndIcdn" id="section_deny" class="form-section">
                                    <!-- 访问控制 -->
                                    <p id="deny" class="label-name">
                                        {{ $t("domain.create.accessControl") }}
                                    </p>
                                    <!-- Referer防盗链 -->
                                    <lock-tip :lock="isLockReferer">
                                        <div>
                                            <referer-chain
                                                :datas="form"
                                                :isNewEcgw="isNewEcgw"
                                                :isLockReferer="isLockReferer"
                                                @onChange="refererChainChange"
                                                ref="refererChain"
                                            ></referer-chain>
                                        </div>
                                    </lock-tip>
                                    <!-- ipset黑白名单 -->
                                    <div v-if="ipsetWhitelist">
                                        <ipset-list
                                            :datas="form"
                                            @onChange="ipsetListChange"
                                            ref="ipsetList"
                                        ></ipset-list>
                                    </div>
                                    <!-- ip黑白名单 -->
                                    <div>
                                        <ip-black-white-list
                                            :datas="form"
                                            @onChange="ipBlackWhiteListChange"
                                            ref="ipBlackWhiteList"
                                        ></ip-black-white-list>
                                    </div>

                                    <!-- ua黑白名单 -->
                                    <lock-tip :lock="isLockUa" :addon="isNewEcgw">
                                        <user-agent
                                            :datas="form"
                                            :isLockUa="isLockUa"
                                            @onChange="uaChange"
                                            ref="userAgent"
                                        ></user-agent>
                                    </lock-tip>
                                    <!-- URL黑白名单 -->
                                    <div v-if="isNewEcgw">
                                        <uri-deny
                                            :datas="form"
                                            @onChange="uriChange"
                                            ref="uriDeny"
                                        ></uri-deny>
                                    </div>
                                </div>

                                <div v-if="isNewEcgw" id="section_limitSpeed">
                                    <lock-tip :lock="isLockLimitSpeed" :addon="showForStaticAndIcdn">
                                        <limit-speed
                                            v-model="form.limit_speed_const"
                                            :lock="isLockLimitSpeed"
                                        />
                                    </lock-tip>
                                </div>

                                <div v-if="isNewEcgw" id="section_entryLimit">
                                    <lock-tip :lock="isLockEntryLimit" :addon="showForStaticAndIcdn">
                                        <entry-limit v-model="form.entry_limits" :lock="isLockEntryLimit" />
                                    </lock-tip>
                                </div>

                                <div v-if="isPoweredByQiankun" id="section_div6" class="form-section">
                                    <!-- IPv6外链改造 -->
                                    <p id="div6" class="label-name">
                                        {{ $t("domain.editPage.label16") }}
                                    </p>
                                    <div class="ipv6-container">
                                        <div class="ipv6-style" v-if="!ipv6OutLinkReformEnable">
                                            <div class="title-box">{{ $t("domain.editPage.label16") }}</div>
                                            <div class="content-box">
                                                {{ $t("domain.editPage.tip11") }}
                                            </div>
                                            <el-button
                                                class="btn-style"
                                                type="primary"
                                                @click="toHighVersion"
                                                :disabled="false"
                                                >{{ $t("domain.editPage.btn1") }}</el-button
                                            >
                                        </div>

                                        <!-- 网站首页IPv6标识 -->
                                        <div class="ipv6-wrap" v-if="ipv6OutLinkReformEnable">
                                            <el-form-item :label="$t('domain.editPage.label21')"
                                                ><span slot="label"
                                                    >{{ $t("domain.editPage.label21") }}
                                                    <span>
                                                        <el-tooltip
                                                            placement="top"
                                                            :content="$t('domain.editPage.tooltip2')"
                                                        >
                                                            <ct-svg-icon
                                                                icon-class="question-circle"
                                                                class-name="ct-sort-drag-icon"
                                                            ></ct-svg-icon></el-tooltip></span
                                                ></span>
                                                <el-switch
                                                    v-model="form.website_ipv6_access_mark.switch"
                                                    :active-value="1"
                                                    :inactive-value="0"
                                                    @change="website_ipv6_access_mark_switch_change"
                                                ></el-switch>
                                            </el-form-item>
                                        </div>
                                        <!-- 网站首页IPv6标识 -->
                                        <div
                                            class="switch-wrapper"
                                            v-if="
                                                form.website_ipv6_access_mark.switch === 1 &&
                                                    ipv6OutLinkReformEnable
                                            "
                                        >
                                            <el-form-item
                                                :label="$t('domain.editPage.label22')"
                                                prop="website_ipv6_access_mark.show_content"
                                                :rules="rules.show_content"
                                                label-width="160px"
                                            >
                                                <span slot="label" class="text">
                                                    {{ $t("domain.editPage.label22") }}
                                                    <span class="question-style" style="margin: unset">
                                                        <el-tooltip
                                                            placement="top"
                                                            :content="$t('domain.editPage.tooltip3')"
                                                        >
                                                            <ct-svg-icon
                                                                type="info"
                                                                icon-class="question-circle"
                                                                class-name="ct-sort-drag-icon"
                                                            ></ct-svg-icon> </el-tooltip
                                                    ></span>
                                                </span>

                                                <el-input
                                                    class="input-style"
                                                    :placeholder="$t('domain.enter')"
                                                    v-model="form.website_ipv6_access_mark.show_content"
                                                ></el-input>
                                            </el-form-item>
                                            <el-form-item
                                                :label="$t('domain.editPage.label23')"
                                                prop="website_ipv6_access_mark.show_times"
                                                :rules="rules.show_times"
                                                label-width="160px"
                                            >
                                                <el-input
                                                    class="input-style"
                                                    v-model.number="form.website_ipv6_access_mark.show_times"
                                                    maxlength="16"
                                                >
                                                    <template slot="append">s</template></el-input
                                                >
                                            </el-form-item>
                                        </div>
                                        <!-- IPv6外链改造 -->
                                        <div class="ipv6-wrap" v-if="ipv6OutLinkReformEnable">
                                            <el-form-item :label="$t('domain.editPage.label16')"
                                                ><span slot="label"
                                                    >{{ $t("domain.editPage.label16") }}
                                                    <span>
                                                        <el-tooltip
                                                            placement="top"
                                                            :content="$t('domain.editPage.tooltip4')"
                                                        >
                                                            <ct-svg-icon
                                                                icon-class="question-circle"
                                                                class-name="ct-sort-drag-icon"
                                                            ></ct-svg-icon></el-tooltip></span
                                                ></span>
                                                <el-switch
                                                    :value="form.outlink_replace_filter.switch"
                                                    :active-value="1"
                                                    :inactive-value="0"
                                                    @change="outlink_switch_change"
                                                ></el-switch>
                                            </el-form-item>
                                        </div>
                                        <div
                                            class="switch-wrapper"
                                            v-if="
                                                form.outlink_replace_filter.switch === 1 &&
                                                    ipv6OutLinkReformEnable
                                            "
                                        >
                                            <!-- 外链改造层数 -->
                                            <el-form-item
                                                :label="$t('domain.detail.label84')"
                                                label-width="160px"
                                                :prop="`outlink_replace_filter.times`"
                                                :rules="rules.times"
                                            >
                                                <span slot="label"
                                                    >{{ $t("domain.detail.label84") }}
                                                    <span>
                                                        <el-tooltip
                                                            placement="top"
                                                            :content="$t('domain.editPage.tooltip5')"
                                                        >
                                                            <ct-svg-icon
                                                                icon-class="question-circle"
                                                                class-name="ct-sort-drag-icon"
                                                            ></ct-svg-icon></el-tooltip></span
                                                ></span>

                                                <el-select
                                                    v-model="form.outlink_replace_filter.times"
                                                    :placeholder="$t('domain.editPage.placeholder8')"
                                                    class="out-chain-input-style"
                                                >
                                                    <el-option
                                                        v-for="times_item in timesList"
                                                        :key="times_item.value"
                                                        :label="times_item.label"
                                                        :value="times_item.value"
                                                    ></el-option>
                                                </el-select>
                                            </el-form-item>
                                            <!-- 改造生效范围 -->
                                            <el-form-item
                                                :label="$t('domain.detail.label85')"
                                                label-width="160px"
                                                :prop="`outlink_replace_filter.validity_scope`"
                                                :rules="rules.validity_scope"
                                            >
                                                <span slot="label"
                                                    >{{ $t("domain.detail.label85") }}
                                                    <span>
                                                        <el-tooltip
                                                            placement="top"
                                                            :content="$t('domain.editPage.tooltip6')"
                                                        >
                                                            <ct-svg-icon
                                                                icon-class="question-circle"
                                                                class-name="ct-sort-drag-icon"
                                                            ></ct-svg-icon></el-tooltip></span
                                                ></span>
                                                <el-radio-group
                                                    v-model="form.outlink_replace_filter.validity_scope"
                                                    :placeholder="$t('domain.editPage.placeholder8')"
                                                    class="out-chain-input-style"
                                                >
                                                    <el-radio label="ipv6">{{
                                                        $t("domain.detail.label86")
                                                    }}</el-radio>
                                                    <el-radio label="ipv6_ipv4">{{
                                                        $t("domain.detail.label87")
                                                    }}</el-radio>
                                                </el-radio-group>
                                            </el-form-item>
                                            <!-- 外链改造黑名单 -->
                                            <el-form-item
                                                :label="$t('domain.detail.label88')"
                                                label-width="160px"
                                            >
                                                <span slot="label"
                                                    >{{ $t("domain.detail.label88") }}
                                                    <span>
                                                        <el-tooltip
                                                            placement="top"
                                                            :content="$t('domain.editPage.tooltip7')"
                                                        >
                                                            <ct-svg-icon
                                                                icon-class="question-circle"
                                                                class-name="ct-sort-drag-icon"
                                                            ></ct-svg-icon></el-tooltip></span
                                                ></span>
                                                <el-input
                                                    v-model="form.outlink_replace_filter.blacklist"
                                                    type="textarea"
                                                    :placeholder="$t('domain.editPage.tooltip8')"
                                                    class="input-style out-chain-input-style"
                                                ></el-input>
                                            </el-form-item>
                                            <!-- 点击类外链改造 -->
                                            <el-form-item
                                                :label="$t('domain.detail.label89')"
                                                label-width="160px"
                                                ><span slot="label"
                                                    >{{ $t("domain.detail.label89") }}
                                                    <span>
                                                        <el-tooltip
                                                            placement="top"
                                                            :content="$t('domain.editPage.tooltip9')"
                                                        >
                                                            <ct-svg-icon
                                                                icon-class="question-circle"
                                                                class-name="ct-sort-drag-icon"
                                                            ></ct-svg-icon></el-tooltip></span
                                                ></span>
                                                <el-switch
                                                    v-model="form.outlink_replace_filter.html_act.switch"
                                                    :active-value="1"
                                                    :inactive-value="0"
                                                ></el-switch>
                                            </el-form-item>
                                        </div>
                                    </div>
                                </div>

                                <!-- JA3指纹配置 -->
                                <div v-if="isPoweredByQiankun" id="section_div6_1" class="form-section">
                                    <p id="div6_1" class="label-name">
                                        {{ $t("domain.detail.ja3_fingerprint") }}
                                    </p>
                                    <lock-tip :lock="waf_secure_conf.secure_power != 1">
                                        <ja3
                                            :formDatas="form"
                                            :isLocked="waf_secure_conf.secure_power != 1"
                                            @onChange="onJa3Change"
                                            ref="ja3"
                                        ></ja3>
                                    </lock-tip>
                                </div>

                                <div
                                    id="section_div7"
                                    class="form-section"
                                    :class="{ 'no-border': !isShowWebsocket }"
                                >
                                    <p id="div7" class="label-name" v-if="isShowWebsocket">
                                        websocket
                                    </p>
                                    <div class="socket-style" v-if="!webSocketEnable && isPoweredByQiankun">
                                        <div class="socket-title">websocket</div>
                                        <div class="socket-content">
                                            {{ $t("domain.editPage.websocketTip") }}
                                        </div>
                                        <el-button
                                            class="btn-style"
                                            type="primary"
                                            @click="toHighVersion"
                                            :disabled="false"
                                            >{{ $t("domain.editPage.btn1") }}</el-button
                                        >
                                    </div>
                                    <!-- websocket -->
                                    <lock-tip :lock="isLockWebSocket" :addon="isShowWebsocket">
                                        <div :class="{ 'dynamic-wrap': !isLockWebSocket }">
                                            <websocket
                                                :formDatas="form"
                                                :websocket_url="websocket_link"
                                                :show_url="show_url"
                                                :isLockWebSocket="isLockWebSocket"
                                                :uploadSpeed="upload_speed"
                                                :webSocketEnable="webSocketEnable"
                                                :isShowWebsocket="isShowWebsocket"
                                                @onChange="onWebsocketChange"
                                                ref="websocket"
                                            ></websocket>
                                        </div>
                                    </lock-tip>
                                </div>
                                <div v-if="isNewEcgw" id="section_htmlForbid" class="form-section">
                                    <!-- 页面优化 -->
                                    <p id="htmlForbid" class="label-name">
                                        {{ $t("domain.editPage.label30") }}
                                    </p>
                                    <!-- html禁止操作 -->
                                    <lock-tip :lock="isLockHtmlForbid">
                                        <html-forbid
                                            ref="htmlForbid"
                                            :datas="form"
                                            :isLockHtmlForbid="isLockHtmlForbid"
                                            @onChange="onHtmlForbidChange"
                                        ></html-forbid>
                                    </lock-tip>
                                </div>
                                <!-- 辅助锚点定位 -->
                                <div class="anchor-helper-block-1" />
                            </div>
                        </el-form>
                    </div>
                    <ct-anchor
                        v-if="!fromAccelerationConfig"
                        v-sort-directive="'sortDetailAnchor'"
                        v-move-directive="'moveDetailAnchor'"
                        ref="anchor"
                        :data-key="anchorChildKey"
                        class="anchor-position anchor-style"
                        :data="basicAnchorList"
                        scroll-dom=".ct-config"
                        @update-child="updateAnchorChild"
                    />
                </section>
                <cute-fixed-footer class="submit" v-if="!fromAccelerationConfig">
                    <div class="footer-content">
                        <!-- 不可编辑：域名状态：非 已启用，或者：isBillingSuccess 为 false -->
                        <el-tooltip
                            v-if="!isService || !isBillingSuccess"
                            class="item"
                            effect="dark"
                            :content="editConfigContent"
                            placement="top"
                        >
                            <el-button
                                type="primary"
                                @click="editConfig"
                                size="medium"
                                :disabled="!isService || !isBillingSuccess"
                            >
                                {{ $t("domain.editPage.btn2") }}
                            </el-button>
                        </el-tooltip>

                        <!-- 可编辑：域名状态：已启用，并且：isBillingSuccess 为 true -->
                        <el-button
                            type="primary"
                            @click="editConfig"
                            size="medium"
                            v-if="!isEdit && isService && isBillingSuccess"
                        >
                            {{ $t("domain.editPage.btn2") }}
                        </el-button>
                        <el-button
                            key="cacel"
                            plain
                            @click="handleCancel"
                            :loading="submitLoading"
                            size="medium"
                            v-if="isEdit"
                        >
                            {{ $t("domain.editPage.btn3") }}
                        </el-button>
                        <el-button
                            key="submit"
                            :disabled="isSecurityFormSame || isWaitingFormValidate"
                            :type="submitDesc.type"
                            @click="handleUpdateDomain(null, null)"
                            :loading="submitLoading"
                            size="medium"
                            v-if="isEdit"
                        >
                            {{ $t("domain.editPage.btn4") }}
                        </el-button>
                    </div>
                </cute-fixed-footer>
                <tip-dialog :dialogVisible="tipDialogVisible" @cancel="cancel" @submit="submitTip" />
                <ipv-6-act-dialog :dialogVisible="ipv6ActVisible" @cancel="cancel" @submit="submitIpv6Act" />
                <!-- 回源参数规则 弹窗 -->
                <back-origin-dialog
                    :dialogVisible="dialogBackOriginVisible"
                    :backOriginForm="backOriginForm"
                    :from="currentType"
                    @cancel="cancel"
                    @submit="submitBackOrigin"
                />
                <work-order-tip-dialog
                    :dialogVisible="workOrderVisible"
                    :existOrderLoading="existOrderLoading"
                    @cancel="cancel"
                    @submit="submitWorkOrder"
                />
                <server-port-tip-dialog
                    :dialogVisible="serverPortTipVisible"
                    @submit="submitServerPortTip"
                    :errorCode="serverPortErrorCode"
                />
            </section>
        </el-scrollbar>
    </section>
</template>

<script>
import { siteUrl } from "@/config/url";
import ctSvgIcon from "@/components/ctSvgIcon";
import validFieldMixin from "@/views/domainConfig/validFieldMixin";
import tipDialog from "@/views/domainConfig/basicConfig/components/tipDialog.vue";
import ipv6ActDialog from "@/views/domainConfig/basicConfig/components/ipv6ActDialog.vue";
import { formValidate2Promise } from "@/utils";
import { SecurityAbilityModule } from "@/store/modules/securityAbility";
import basicInfo from "@/views/domainConfig/basicInfo";
import moduleMixin from "@/views/domainConfig/moduleMixin";
import { get, isEqual, cloneDeep } from "lodash-es";
import ctAnchor from "@/components/ctAnchor";
import backOriginDialog from "./components/backOriginDialog.vue";
import workOrderTipDialog from "@/views/domainConfig/basicConfig/components/workOrderTipDialog.vue";
import refreshMixin from "@/views/domainConfig/refreshMixin";
import store from "@/store";
import gatewayDataMixin from "@/views/domainConfig/gatewayDataMixin";
import serverPortTipDialog from "@/views/domainConfig/basicConfig/components/serverPortTipDialog.vue";
import i18n from "@/i18n/index";
import handleAnchorMixin from "@/views/domainConfig/handleAnchorMixin";
import { nUserModule } from "@/store/modules/nuser";
import formDataMixin from "../mixins/basicConfigFormData.mixin";
import lockTip from "../components/lockTIp.vue";
import domainEndsTip from "../components/domainEndsTip.vue";
import { commonLinks } from "@/utils/logic/url";
import { moveDirective, sortDirective } from "@/directives/ctSort";
import urlTransformer from "@/utils/logic/url";
import { StatisticsModule } from "@/store/modules/statistics";
import { domainEndsWithSpecialSuffix } from "@/utils/product";
import { conditionDestruct, deepDiff } from "@/utils/utils";
import { CdnConfigModule } from "@/store/modules/cdnConfig";
import { checkReqHost } from "@/utils/pattern";
import encryptionSuite from "@/views/domainConfig/basicConfig/config-components/encryptionSuite/index.vue";
import CdnHttpsSupportTip from "@/views/domainConfig/components/cdnHttpsSupportTip.vue";
import { CODE_SERVER_PORT_ERROR_2364, CODE_SERVER_PORT_ERROR_2367 } from "@/utils/ctFetch/errorConfig";
import configModuleMixin from "@/mixins/configModuleMixin"; // 可插拔配置，引入 mixin
import { ConfigModulesModule } from "@/store/modules/configModules"; // 可插拔配置，引入 MODULE
import certName from "./config-components/cert_name.vue";

export default {
    directives: {
        moveDirective,
        sortDirective,
    },
    components: {
        ctSvgIcon,
        tipDialog,
        ipv6ActDialog,
        basicInfo,
        ctAnchor,
        backOriginDialog,
        workOrderTipDialog,
        serverPortTipDialog,
        lockTip,
        domainEndsTip,
        encryptionSuite,
        CdnHttpsSupportTip,
        certName,
    },
    mixins: [
        validFieldMixin,
        moduleMixin,
        refreshMixin,
        gatewayDataMixin,
        handleAnchorMixin,
        formDataMixin,
        configModuleMixin,
    ],
    props: {
        defaultData: Object,
        fromAccelerationConfig: Boolean,
        showForStaticAndIcdn: {
            type: Boolean,
            default: false,
        },
        isFlatten: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            anchorChildKey: null,
            respHeadersShowXosDomainTip: false,
            wholeStationProductCode: ["006", "104", "105"], // 全站加速的 product_code
            temp_domain_detail: {},
            signature: "",
            tipDialogVisible: false,
            upload_speed: 0,
            ipv6_switch: "",
            ipv6ActVisible: false,
            workOrderVisible: false,
            serverPortTipVisible: false,
            isQueryDomainDetail: false,
            is_exist: true, // 是否有在途工单：true表示有在途工单；false表示没有在途工单，即工单已完结
            existOrderLoading: false,
            isDomainChange: false,
            addButtonText: i18n.t("domain.editPage.label10"),
            activeIndex: 0,
            funcName: [],
            temp_bucket: "", // 用来赋值给xos_origin的bucket
            keepUrlToAnchor: false,
            anchorInterval: null,
            waf_secure_conf: {}, // secure_power 与 ssl_ja3_enable 有联动
            getDomainDetailAbortController: null,
            form: {
                product_code: "",
                backorigin_switch: 0, // 回源超时时间设置 开关
                backup_origin_timeout: "", // 回源连接超时时间
                backup_origin_resptimeout: "", // 回源请求超时时间

                websocket_speed: 0, // WebSocket开关
                websocket_time: {
                    backup_origin_timeout: "",
                    backup_origin_resptimeout: "", // 不展示在界面，需要回传给后端
                    backup_origin_sendtimeout: "", // 不展示在界面，需要回传给后端
                },
                ssl_ja3_enable: null, // JA3指纹记录开关，当on时，要求waf_secure_conf:secure_power为开启状态

                resp_headers: [], // HTTP响应头
                req_headers: [], // 回源HTTP请求头
                origin: [], // 源站
                xos_origin: {
                    origin: "",
                    bucket: "",
                },
                zos_origin: {
                    switch: 0,
                    origin: "",
                    private_bucket: 0,
                    ak: "",
                    sk: "",
                },

                backorigin_protocol: "http", // 回源协议
                proxy_gmssl_mode: "", // 回源加密算法
                req_host: "", // 回源host
                internal_uri_rewrite_new: [], // 回源URL改写
                ignore_backorigin_args: 0, // 忽略回源参数开关
                backorigin_arg_rewrite_other: [], // 回源参数规则
                // 强制跳转
                force_jump: {
                    jump_enable: false,
                    force_type: "http",
                    force_status: "302",
                },
                // 网站首页Ipv6标识
                website_ipv6_access_mark: {
                    switch: 0,
                    show_content: i18n.t("domain.editPage.ipv6Tip"),
                    show_times: 10,
                    homepage_url: "",
                },
                // Ipv6外链改造
                outlink_replace_filter: {
                    switch: 0,
                    times: 0,
                    ignore_status: [],
                    blacklist: "",
                    validity_scope: "ipv6",
                    match_list: [
                        {
                            priority: 10,
                            type: "",
                            value: "",
                            regex_list: [
                                {
                                    regex: "",
                                    pos_tab: [],
                                    suffix: [],
                                    visit: "",
                                },
                            ],
                        },
                    ],
                    html_act: {
                        switch: 0,
                    },
                },
                domain: "",
                request_protocol: [],
                cert_name: "",
                cert_name_gm: "",
                basic_conf: {
                    use_http2: 0,
                    https_origin_port: 443,
                    http_origin_port: 80,
                },
                ssl: [], // TLS版本
                ssl_stapling: "off", // OCSP Stapling开关
                cus_gzip: [], // 文件压缩
                // 回源302/301跟随
                follow_302: 0,
                follow302_is_valid: [],
                follow302_times: 1,
                // URL鉴权
                url_auth: {
                    switch: false,
                },
                hsts: {
                    switch: 0,
                    max_age: 1200,
                    include_sub_domains: null,
                },
                http_server_port: "",
                https_server_port: "",

                // 私有Bucket回源
                bucket_status: false,
                ak: "",
                sk: "",
                use_ori_uri: "",
                // Referer防盗链
                referer: "off",
                allow_empty: "on",
                referer_empty_protocol: "off",
                match_all_ports: "off",
                ignore_case: "off",
                is_append: 0,
                domainList: "",
                refererType: "allow",
                // ip黑白名单
                ip_switch: "off",
                ipType: "allow",
                ip: "",
                // ua黑白名单
                ua_switch: "off",
                user_agent: {
                    type: 1,
                    ua: "",
                    ignore_case: "on",
                    mode: 1,
                },
                // URL黑白名单
                uri_switch: "off",
                uri_deny: {
                    type: 1,
                    uri: "",
                },
                // 单请求限速
                limit_speed_const: [],
                // 回源ip限频
                entry_limits: [],
                // 加速区域
                area_scope: null,
                html_forbid_op: [], // html禁止操作
                // 回源端口
                follow_request_backport: 0,
                version: "", // 当前操作的配置版本
                backorigin_sni: {
                    switch: 0,
                    sni: "",
                },
                split_set: {
                    split_set: "off",
                    mode: null,
                    content: "",
                },
                split_white: false, // 是否为分片回源白名单
                ssl_ciphers: "", // 加密套件
                custom_ssl_ciphers: "", // 自定义加密套件
                // IP黑白名单集合相关配置
                ip_set_forbid: {
                    switch: null,
                    forbid_type: null, // 0黑名单，1白名单
                    alias_name: null,
                },
                quic: {
                    switch: 0,
                },
            },
            temp_follow302_times: 1,
            // Referer防盗链
            domainList: [],
            // ua黑白名单
            uaList: [],
            // url黑白名单
            uriList: [],
            temp_backorigin_arg_rewrite_other: [],
            temp_request_protocol: [],
            temp_http_server_port: "",
            temp_https_server_port: "",
            urlAuthFormDisabled: false,
            learnLink: "https://www.ctyun.cn/document/10065985/10339350",
            // 文件压缩-文件压缩方式
            typeList: [
                { name: "gzip", value: 0 },
                { name: "brotli", value: 1 },
            ],
            detailLoading: false,
            submitLoading: false,
            submitDesc: {
                text: i18n.t("domain.editPage.btn4"),
                cancelText: i18n.t("domain.editPage.btn3"),
                type: "primary",
            },
            cert_name_list: [],
            lastSelectedCertName: "", // 上次选中的证书名
            lastSelectedCertNameGm: "", // 上次选中的国密证书名
            currentType: "create",
            websocket_url: "https://www.ctyun.cn/document/10065985/10192895",
            // origin_uri_url: "https://www.ctyun.cn/document/10065985/10192893",
            // backorigin_arg_rewrite_other_url: "https://www.ctyun.cn/document/10065985/10192894",
            baseInfo: [
                { label: i18n.t("domain.create.domainName"), value: "" },
                { label: "CNAME", value: "" },
                {
                    label: i18n.t(
                        window.__POWERED_BY_QIANKUN__ ? "domain.editPage.label2" : "domain.create.acceType"
                    ),
                    value: "",
                },
                {
                    label: i18n.t(
                        window.__POWERED_BY_QIANKUN__
                            ? "domain.create.serviceArea"
                            : "domain.list.tableLabel4"
                    ),
                    value: "",
                },
                { label: i18n.t("domain.list.tableLabel6"), value: "" },
            ],
            containerToTop: 0,
            // 回源参数规则 相关
            currentBackOriginIndex: "",
            dialogBackOriginVisible: false,
            backOriginForm: {
                mode: "add",
                argsList: [
                    {
                        name: "",
                        value: "",
                    },
                ],
                priority: 10,
            },
            rules: {
                backorigin_protocol: [
                    { required: true, message: i18n.t("domain.detail.tip75"), trigger: "change" },
                ],
                http_origin_port: [
                    { required: true, validator: this.valid_http_origin_port, trigger: "blur" },
                ],
                https_origin_port: [
                    { required: true, validator: this.https_origin_port_valid, trigger: "blur" },
                ],

                req_host: [{ required: false, validator: this.checkReqHost, trigger: "blur" }],
                follow302_times: [{ validator: this.valid_follow302_times, trigger: "blur" }],
                // 请求协议
                request_protocol: [
                    { required: true, validator: this.validRequestProtocol, trigger: "change" },
                ],
                force_type: [{ required: true, validator: this.valid_force_type, trigger: "change" }],
                force_status: [{ required: true, message: i18n.t("domain.detail.tip62"), trigger: "change" }],
                // HTTPS配置
                // use_http2: [{ required: true, message: "请选择HTTP2.0", trigger: "change" }],
                // 头部修改
                // IPv6外链改造
                show_content: [
                    { required: true, message: i18n.t("domain.editPage.ruleTip4") },
                    {
                        validator: (rule, value, callback) => {
                            if (value.length > 50) {
                                return callback(new Error(i18n.t("domain.editPage.ruleTip5")));
                            } else callback();
                        },
                        trigger: ["blur", "change"],
                    },
                ],
                show_times: [
                    { required: true, message: i18n.t("domain.editPage.ruleTip6") },
                    {
                        validator: (rule, value, callback) => {
                            const num = /^[0-9]*$/;
                            if (!num.test(value)) {
                                return callback(new Error(i18n.t("domain.editPage.ruleTip7")));
                            } else if (value < 1 || value > 2147483647) {
                                return callback(new Error(i18n.t("domain.editPage.ruleTip8")));
                            } else callback();
                        },
                        trigger: ["blur", "change"],
                    },
                ],
                times: [{ required: true, message: i18n.t("domain.editPage.ruleTip9"), trigger: "change" }],
                validity_scope: [
                    { required: true, message: i18n.t("domain.editPage.ruleTip10"), trigger: "change" },
                ],
                cert_name_gm_quic: [
                    { required: false, validator: this.valid_cert_name_gm_quic, trigger: "change" },
                ],
                // htmlActSwitch: [{ required: true, message: "请选择点击类外链改造", trigger: "change" }],
                // 文件压缩
                // URL鉴权
            },
            validateForm: [],
            validateComponents: [
                "backoriginTime",
                "subOrigin",
                "privateBucketOrigin",
                "respHeaders",
                "reqHeaders",
                "internalUriRewriteNew",
            ],
            cert_name_loading: false, // 证书列表加载状态
            serverPortErrorCode: CODE_SERVER_PORT_ERROR_2364,
        };
    },
    computed: {
        ssl_list() {
            const intl = ["TLSv1", "TLSv1.1", "TLSv1.2", "TLSv1.3"];
            const sm2 = ["GMTLSv1.1"];
            const list = [];

            if (this.form.cert_name) {
                list.push(...intl);
            }

            if (this.form.cert_name_gm) {
                list.push(...sm2);
            }

            return list;
        },
        hasCertName() {
            return this.form.cert_name || this.form.cert_name_gm;
        },
        securityBasicConfigOriginForm() {
            return SecurityAbilityModule.securityBasicConfigOriginForm;
        },
        getQuicParams() {
            return {
                isNewQuic: !this.temp_domain_detail?.quic?.switch && this.form.quic.switch === 1,
                quicEnable: this.temp_domain_detail?.quicEnable ?? false,
            };
        },
        cert_name_rule() {
            return [
                {
                    required: false,
                    validator: (rule, value, callback) => {
                        if (this.isLockRequestProtocol) {
                            return callback();
                        }
                        if (this.requestProtocolIncludesHttps && !this.hasCertName && this.isEdit) {
                            return callback(this.$t("certificate.国际标准证书和国密证书至少要有一种有配置"));
                        }
                        return callback();
                    },
                    trigger: "change",
                },
                { required: false, validator: this.checkCertExistAndUpdate, trigger: "change" },
            ];
        },
        isWaitingFormValidate() {
            return CdnConfigModule.isWaitingFormValidate;
        },
        websocket_link() {
            return urlTransformer({
                a1Ctyun: this.websocket_url,
                a1Ctcloud: "https://www.esurfingcloud.com/document/zh-cn/20689737/20689846",
            });
        },
        showOcspStapling() {
            if (this.isPoweredByQiankun) return this.requestProtocolIncludesHttps;
            // aocdn 不需要下面的判断逻辑，下面的是原ncdn的逻辑
            return (
                ["001", "003", "004", "005", "006", "008", "014"].includes(
                    this.temp_domain_detail.product_code
                ) &&
                this.requestProtocolIncludesHttps &&
                this.isNewEcgw
            );
        },
        // 是否为新框架
        isNewEcgw() {
            return this.temp_domain_detail.use_ecgw === 1;
        },
        // 新用户 & 产品为 cdn加速
        isNewUserAndIsCdn() {
            return (
                nUserModule.isNewUser &&
                this.form.product_code === "008" &&
                nUserModule.userInfo.saleChannel === "2"
            );
        },
        isCtclouds() {
            return nUserModule.isCtclouds;
        },
        show_url() {
            return (
                window.__POWERED_BY_QIANKUN__ || (!window.__POWERED_BY_QIANKUN__ && !nUserModule.isCtclouds)
            );
        },
        origin_uri_url() {
            return window.__POWERED_BY_QIANKUN__
                ? "https://www.ctyun.cn/document/10065985/10192893"
                : this.isCtclouds
                ? nUserModule.lang === "zh"
                    ? "https://www.esurfingcloud.com/document/zh-cn/10015932/20686451"
                    : "https://www.esurfingcloud.com/document/10015932/20686451"
                : "https://www.ctyun.cn/document/10015932/10638040";
        },
        backorigin_arg_rewrite_other_url() {
            return urlTransformer({
                fcdnCtyun: "https://www.ctyun.cn/document/10015932/10638043",
                a1Ctyun: "https://www.ctyun.cn/document/10065985/10192894",
                a1Ctcloud: "https://www.esurfingcloud.com/document/zh-cn/20689737/20689826",
            });
        },
        webSocketEnable() {
            return this.isPoweredByQiankun ? SecurityAbilityModule.webSocketEnable : true;
        },
        ipv6OutLinkReformEnable() {
            return SecurityAbilityModule.ipv6OutLinkReformEnable;
        },
        securityDomain() {
            return SecurityAbilityModule.securityDomain;
        },
        // 域名配置是否可绑定ip集
        ipsetWhitelist() {
            return StatisticsModule.ipsetWhitelist;
        },
        xosDefaultAccelerateSuffix() {
            return StatisticsModule.xosDefaultAccelerateSuffix;
        },
        zosDefaultAccelerateSuffix() {
            return StatisticsModule.zosDefaultAccelerateSuffix;
        },
        // 域名是否以.ctyuncs.cn / zoscdn.cn为后缀
        isDomainEndsWithCtyun() {
            return domainEndsWithSpecialSuffix(this.securityDomain, [
                this.xosDefaultAccelerateSuffix,
                this.zosDefaultAccelerateSuffix,
            ]);
        },
        securityDomainStatus() {
            return SecurityAbilityModule.securityDomainStatus;
        },
        isEdit() {
            return SecurityAbilityModule.isEdit;
        },
        isSecurityFormSame() {
            // [Helper] 开发配置项时，可以打开下面这行代码，查看 SecurityForm 的 diff 辅助调试
            // process.env.NODE_ENV === 'development' && console.log('getSecurityFormDiff =', this.getSecurityFormDiff());
            return SecurityAbilityModule.isSecurityFormSame;
        },
        // 域名是否启用中
        isService() {
            const status = get(SecurityAbilityModule.securityDomainInfo, "domainStatus");
            return status === "NORMAL";
        },
        isLockSplitSet() {
            return this.funcName?.includes("split_set");
        },
        // 请求头功能锁
        isLockReqHeader() {
            return this.funcName?.includes("req_headers");
        },
        // 响应头功能锁
        isLockRespHeader() {
            return this.funcName?.includes("resp_headers");
        },
        // 请求协议功能锁
        isLockRequestProtocol() {
            // TODO 0619
            return this.funcName?.includes("https_cert");
        },
        // 加密套件功能锁
        isLockSslCiphers() {
            return this.funcName?.includes("ssl_ciphers");
        },
        // 源站-功能锁
        isLockOrigin() {
            return this.funcName?.includes("gateway_origin");
        },
        // xos功能锁：包括锁住：源站 和 私有Bucket回源
        isLockXosOrigin() {
            return this.funcName?.includes("xos_origin");
        },
        // zos 功能锁，非功能锁时数据需要回传，功能锁逻辑优先
        isLockZosOrigin() {
            // return this.funcName?.includes("zos_origin");
            return false;
        },
        // 忽略回源参数功能锁：存在gateway_origin字段则不允许修改配置
        isLockUrlParams() {
            return this.funcName?.includes("origin_url_params");
        },
        // 回源参数规则功能锁：存在gateway_origin字段则不允许修改配置
        isLockParamsRewrite() {
            return this.funcName?.includes("origin_params_rewrite");
        },
        // referer 黑白名单功能锁
        isLockReferer() {
            return this.funcName?.includes("referer_theft_chain");
        },
        // 是否域名新增中
        isAddingDomain() {
            return get(SecurityAbilityModule.securityDomainInfo, "workStatus") === 2;
        },
        // 非常规提示语
        currentAbnormalText() {
            if (this.isAddingDomain) {
                return this.$t("domain.editPage.tip14");
            }

            return this.$t("domain.editPage.tip1");
        },
        requestProtocolIncludesHttp() {
            return this.form.request_protocol.includes("http");
        },
        requestProtocolIncludesHttps() {
            return this.form.request_protocol.includes("https");
        },
        isDomainCorrect() {
            return this.securityDomain === this.form.domain;
        },
        isServerPortChange() {
            return (
                this?.form?.http_server_port !== this.temp_http_server_port ||
                this?.form?.https_server_port !== this.temp_https_server_port ||
                !isEqual(this.temp_request_protocol?.sort(), this.form.request_protocol?.sort())
            );
        },
        // websocket 连接超时时间-功能锁
        isLockWebSocket() {
            return this.funcName?.includes("websocket_time");
        },
        // 回源超时时间-功能锁
        isLockBackOriginTime() {
            return this.funcName?.includes("backup_origin_timeout");
        },
        // html禁止操作-功能锁
        isLockHtmlForbid() {
            return this.funcName?.includes("html_forbid_op");
        },
        // 文件压缩 功能锁
        isLockGzipConf() {
            return this.funcName?.includes("gzip_conf");
        },
        // URL鉴权 功能锁
        isLockUrlAuth() {
            return this.funcName?.includes("md5_theft_chain");
        },
        // 远程同步鉴权 功能锁
        isLockRemoteSyncAuth() {
            return this.funcName?.includes("remote_sync_auth");
        },
        // 单请求限速
        isLockLimitSpeed() {
            return this.funcName?.includes("speed_limit");
        },
        // 回源ip限频
        isLockEntryLimit() {
            return this.funcName?.includes("request_entry_limit");
        },
        // 回源URI改写 功能锁
        isLockOriginUrlRewrite() {
            return this.funcName?.includes("origin_inner_url_rewrite");
        },
        // ua黑白名单 功能锁
        isLockUa() {
            return this.funcName?.includes("ua_black_white_list");
        },
        editConfigContent() {
            // 如果 isBillingSuccess 为false，并且是已启用，就要用提示：获取业务中心能力开关失败，请重新刷新页面。
            if (!this.isBillingSuccess && this.isService) {
                return this.$t("domain.editPage.tip15");
            }
            return this.$t("domain.editPage.tip16");
        },
        isPoweredByQiankun() {
            return window.__POWERED_BY_QIANKUN__;
        },
        // isBillingSuccess 为 true，表示该域名有：websocket 动态 ipv6 等套餐的能力，如果为 false，编辑配置按钮就需要置灰不可编辑，并且提示：获取业务中心能力开关失败，请重新刷新页面。
        isBillingSuccess() {
            return this.isPoweredByQiankun ? SecurityAbilityModule.isBillingSuccess : true;
        },
        orderLink() {
            return commonLinks.orderLink;
        },
        documentOriginLink() {
            return this.isCtclouds
                ? nUserModule.lang === "zh"
                    ? "https://www.esurfingcloud.com/document/zh-cn/10015932/20688145"
                    : "https://www.esurfingcloud.com/document/10015932/20688145"
                : "https://www.ctyun.cn/document/10015932/10639779";
        },
        rangeOriginLink() {
            return this.isCtclouds
                ? nUserModule.lang === "zh"
                    ? "https://www.esurfingcloud.com/document/zh-cn/10015932/20688146"
                    : "https://www.esurfingcloud.com/document/10015932/20688146"
                : "https://www.ctyun.cn/document/10015932/10639835";
        },
        isShowPrivateBucketOrigin() {
            // 全站底座或全站产品或静态底座且新框架且ATS回源模式
            // return (
            //     (!window.__POWERED_BY_QIANKUN__ &&
            //         !this.wholeStationProductCode.includes(this.temp_domain_detail.product_code) &&
            //         this.temp_domain_detail.use_ecgw === 1 &&
            //         this.temp_domain_detail.backorigin_mode === 0) ||
            //     this.temp_domain_detail.gateway_type === 6 ||
            //     this.wholeStationProductCode.includes(this.temp_domain_detail.product_code) ||
            //     window.__POWERED_BY_QIANKUN__
            // );

            // 没有选择zos或者xos源站不显示bucket
            const isXos = this.form.origin?.find(itm => itm.is_xos === 1);
            const isZos = this.form.origin?.find(itm => itm.is_xos === 2);

            if (isZos) return true;
            if (isXos) {
                if (CdnConfigModule.isCtBackupSourceSelected) return false;
                return true;
            }

            return false;
        },
        originPortDisabled() {
            return this.form.follow_request_backport === 1;
        },
    },
    watch: {
        defaultData: {
            handler(val) {
                if (!val) {
                    return;
                }
                this.init(val);
            },
            immediate: true,
        },
        securityDomain: {
            async handler(val) {
                this.isDomainChange = true;
                this.getCertList();
                if (!val || this.fromAccelerationConfig) {
                    // if (this.isAddingDomain) {
                    this.form = this.$options?.data()?.form;
                    SecurityAbilityModule.SET_SECURITY_BASIC_ORIGIN_FORM(cloneDeep(this.form));
                    SecurityAbilityModule.SET_SECURITY_BASIC_CURRENT_FORM(cloneDeep(this.form));

                    const baseInfo = [
                        { label: this.$t("domain.create.domainName"), value: "-" },
                        { label: "CNAME", value: "-" },
                        {
                            label: this.$t(
                                window.__POWERED_BY_QIANKUN__
                                    ? "domain.editPage.label2"
                                    : "domain.create.acceType"
                            ),
                            value: "-",
                        },
                        {
                            label: this.$t(
                                window.__POWERED_BY_QIANKUN__
                                    ? "domain.create.serviceArea"
                                    : "domain.list.tableLabel4"
                            ),
                            value: "-",
                        },
                        { label: this.$t("domain.list.tableLabel6"), value: "-" },
                    ];
                    this.baseInfo = baseInfo;
                    // }
                    return;
                }
                // 可插拔配置-加载配置项状态
                this.loadConfigModules(val); // 可插拔配置
                this.getDomainDetail();

                SecurityAbilityModule.SET_IS_EDIT(false);
            },
            immediate: true,
        },
        securityDomainStatus: {
            deep: true,
            async handler(newVal, oldVal) {
                if (this.isEdit || this.fromAccelerationConfig || this.isDomainChange) {
                    this.isDomainChange = false;
                    return;
                }
                if (newVal !== oldVal && oldVal !== undefined) {
                    this.getDomainDetail();
                }
            },
            immediate: true,
        },
        form: {
            deep: true,
            handler(val) {
                // if (this.isAddingDomain) {
                //     return;
                // }
                SecurityAbilityModule.SET_SECURITY_BASIC_CURRENT_FORM(val);
            },
            immediate: true,
        },
        // 通知主应用
        isSecurityFormSame: {
            handler() {
                const data = {
                    icChange: !this.isSecurityFormSame,
                    fn: this.handleSubmit,
                };
                window.custom.emit("handleCDNConfigChange", data);
            },
            // 这里需要使用到immediate，因为在提交成功后，renderKey刷新，迫使FormWrapper组件的生命周期重新走一遍
            // 初始状态下并不会发送交互事件给主应用，所以这里需要一个初始发送通知，告知主应用
            immediate: true,
        },
        "form.request_protocol": {
            deep: true,
            handler() {
                // 请求协议都有 HTTP和HTTPS的时候，跳转类型展示详情接口返回的值
                if (this.requestProtocolIncludesHttp && this.requestProtocolIncludesHttps) {
                    this.$set(
                        this.form.force_jump,
                        "force_type",
                        this.temp_domain_detail?.force_jump?.force_type
                    );
                } else if (this.requestProtocolIncludesHttp && !this.requestProtocolIncludesHttps) {
                    // 请求协议只有 HTTP的时候，跳转类型展示Https->Http
                    this.$set(this.form.force_jump, "force_type", "http");
                } else if (this.requestProtocolIncludesHttps && !this.requestProtocolIncludesHttp) {
                    // 请求协议只有 HTTPS的时候，跳转类型展示Http->Https
                    this.$set(this.form.force_jump, "force_type", "https");
                } else {
                    this.$set(this.form.force_jump, "force_type", "");
                }
            },
            immediate: true,
        },
        workOrderVisible: {
            deep: true,
            handler(val) {
                if (!val) {
                    this.existOrderLoading = false;
                    this.handleClearTimer();
                    this.handleClearOrderTimer();

                    if (this.isQueryDomainDetail) {
                        setTimeout(() => {
                            this.getDomainDetail();
                        }, 3100);
                    }

                    return;
                }
            },
            immediate: true,
        },
        // 点击保存 (handleUpdateDomain) 后，弹窗，点击确定，调用域名更新接口 (handleUpdate)，
        // 这时候需要调用查询在途工单接口 (handleExistOrder) ，如果返回false，需要每隔1秒调用一次：查询在途工单接口，
        // 如果查在途工单接口一直返回false，10秒后自动关闭弹窗，不再调用接口
        // 如果查在途工单接口返回true，自动关闭窗口，不再调用接口
        // 查在途工单接口：true：代表有在途工单；false：代表无在途工单
        is_exist: {
            deep: true,
            handler(val) {
                if (val === null) return;
                this.handleRefresh(val);
            },
            immediate: true,
        },
        ssl_list(list) {
            if (this.form.ssl?.length) {
                this.form.ssl = this.form.ssl.filter(item => list.includes(item));
            }
        },
    },
    mounted() {
        this.initEditConfBtn();
        this.keepUrlToAnchor = true; // 设置状态，保持url跳转锚点
        this.fromUrlToAnchor();

        if (!this.fromAccelerationConfig) {
            this.calculateContainerTop(!this.fromAccelerationConfig);

            const scroller = document.querySelector(".ct-config");
            if (!scroller) {
                return;
            }

            scroller.addEventListener("scroll", this.handleScroller);
        }
    },
    updated(...data) {
        // 更新 anchorHelperBlock 的高度
        this.updateAnchorHeight();
        this.fromUrlToAnchor();
        this.$emit("update-child", new Date().getTime());
    },
    beforeDestroy() {
        this.getDomainDetailAbortController?.abort("cancel by user");

        const scroller = document.querySelector(".ct-config");
        if (!scroller) {
            return;
        }

        scroller.removeEventListener("scroll", this.handleScroller);
        scroller.removeEventListener("scroll", this.setKeepUrlToAnchor);

        this.handleClearTimer();
    },
    methods: {
        /**
         * 触发联动校验
         */
        publicRevalidator(props) {
            this.$refs.form.validateField(props);
        },
        // 校验国密证书和quic不能同时开启
        valid_cert_name_gm_quic(rule, value, callback) {
            if (this.form.cert_name_gm && this.getQuicParams.quicEnable && this.form.quic.switch) {
                return callback(new Error(this.$t("certificate.国密证书和quic不能同时开启")));
            }
            callback();
        },
        // 调试方法：获取表单diff详情
        getSecurityFormDiff() {
            if (this.isDomainDetailLoading || !this.securityDomain) {
                console.log("🔍 SecurityForm Debug: 配置获取中或域名为空，跳过对比");
                return null;
            }

            const securityFormDiff = deepDiff(
                SecurityAbilityModule.securityOriginForm,
                SecurityAbilityModule.securityCurrentForm
            );
            const basicConfigDiff = deepDiff(
                SecurityAbilityModule.securityBasicConfigOriginForm,
                SecurityAbilityModule.securityBasicConfigCurrentForm
            );

            console.group("🔍 SecurityForm Debug");
            console.log("Domain:", SecurityAbilityModule.securityDomain);
            console.log("isDomainDetailLoading:", SecurityAbilityModule.isDomainDetailLoading);

            console.group("📝 Security Form Diff");
            if (securityFormDiff.length === 0) {
                console.log("✅ 安全表单无变化");
            } else {
                console.log("❌ 安全表单发现变化:", securityFormDiff);
                securityFormDiff.forEach((diff, index) => {
                    console.log(
                        `  ${index + 1}. [${diff.type}] ${diff.path}:`,
                        diff.type === "added"
                            ? `+ ${JSON.stringify(diff.newValue)}`
                            : diff.type === "removed"
                            ? `- ${JSON.stringify(diff.oldValue)}`
                            : `${JSON.stringify(diff.oldValue)} → ${JSON.stringify(diff.newValue)}`
                    );
                });
            }
            console.groupEnd();

            console.group("⚙️ Basic Config Diff");
            if (basicConfigDiff.length === 0) {
                console.log("✅ 基础配置无变化");
            } else {
                console.log("❌ 基础配置发现变化:", basicConfigDiff);
                basicConfigDiff.forEach((diff, index) => {
                    console.log(
                        `  ${index + 1}. [${diff.type}] ${diff.path}:`,
                        diff.type === "added"
                            ? `+ ${JSON.stringify(diff.newValue)}`
                            : diff.type === "removed"
                            ? `- ${JSON.stringify(diff.oldValue)}`
                            : `${JSON.stringify(diff.oldValue)} → ${JSON.stringify(diff.newValue)}`
                    );
                });
            }
            console.groupEnd();

            const isSecuritySame = securityFormDiff.length === 0;
            const isBasicSame = basicConfigDiff.length === 0;
            const isAllSame = isSecuritySame && isBasicSame;

            console.log("📊 Summary:");
            console.log(`  Security Form Same: ${isSecuritySame ? "✅" : "❌"}`);
            console.log(`  Basic Config Same: ${isBasicSame ? "✅" : "❌"}`);
            console.log(`  Overall Same: ${isAllSame ? "✅" : "❌"}`);
            console.groupEnd();

            return {
                isAllSame,
                isSecuritySame,
                isBasicSame,
                securityFormDiff,
                basicConfigDiff,
            };
        },
        updateAnchorChild(val) {
            this.anchorChildKey = val;
        },
        updateAnchorHeight() {
            this.$nextTick(() => {
                // 基础配置高度补全只有 a1 -> 基础配置 入口 需要
                if (!window.__POWERED_BY_QIANKUN__ || !this.isUrlIncludeBasicConfig) {
                    return;
                }
                const lastLabelDom = document.querySelector(
                    this.basicAnchorList[this.basicAnchorList.length - 1]?.prop
                );
                const contentAreaHeight = document.querySelector(".content-wrap")?.offsetHeight;
                const viewAreaHeight = document.querySelector(".ct-config")?.offsetHeight;
                const emptyBlock = document.querySelector(".anchor-helper-block-1");

                const height =
                    viewAreaHeight - (contentAreaHeight - lastLabelDom?.offsetTop) + emptyBlock?.offsetHeight;
                // 浏览器前进后退导致emptyBlock未渲染时js报错
                if (emptyBlock) {
                    emptyBlock.style.height = `${height < 0 ? 0 : height}px`;
                }
            });
        },
        // 初始化时调用，用于解决获取证书接口可能返回速度较慢的问题，会导致证书没有回显
        checkCertExistAndUpdate(rule, value, callback) {
            // 如果detail接口返回的证书不存在与证书列表中，则清空所绑定的证书
            if (
                !this.isLockRequestProtocol &&
                value &&
                this.isEdit &&
                !this.cert_name_list.some(cert => cert.cert_name === value)
            ) {
                callback(new Error(this.$t("domain.editPage.tip49")));
            }
            callback();
        },
        setKeepUrlToAnchor() {
            !this.detailLoading && (this.keepUrlToAnchor = false); // 解除url跳转锚点
        },
        handleScroller(e) {
            const ele = e.target;
            const scrollTop = ele.scrollTop; // 容器滚动的距离
            const anchor = this.$refs.anchor.$el;
            // const positionInfo = anchor.getBoundingClientRect();
            // 当前锚点距离窗口的距离
            // const left = get(positionInfo, "left", 0);
            // 锚点动态样式
            if (scrollTop >= this.containerToTop && this.containerToTop > 0) {
                anchor.style.position = "fixed";
                // anchor.style.top = this.containerToTop + "px";
                anchor.style.top = this.isPoweredByQiankun
                    ? `${this.containerToTop + 5}px`
                    : `${this.containerToTop + 60}px`;
                // anchor.style.left = left + "px";
                anchor.style.right = "30px";
                return;
            }

            anchor.style.position = "relative";
            anchor.style.top = 0;
            anchor.style.right = "5px";
            // anchor.style.left = 0;
        },
        /**
         * 根据url跳转到对应的锚点
         * 1、由于每次渲染页面的布局都有可能发生变化，会影响跳转的准确性，在每次updated后调用该功能
         * 2、用户手动scroll也会触发updated，使用keepUrlToAnchor来判断是否是用户行为
         * 3、锚点自动跳转时，也会触发setKeepUrlToAnchor，需要跳转前后进行卸载安装
         *  */
        fromUrlToAnchor() {
            if (this.fromAccelerationConfig || !this.keepUrlToAnchor) return;
            const anchor = window.location.hash?.split("anchor=")[1]?.split("&")[0];
            this.anchorInterval && clearInterval(this.anchorInterval);
            if (anchor) {
                const scroller = document.querySelector(".ct-config");
                scroller.removeEventListener("scroll", this.setKeepUrlToAnchor);
                if (this.$refs.anchor) {
                    this.$refs.anchor.handleScrollToTarget({ prop: `#${anchor}` });
                    clearInterval(this.anchorInterval);
                }
                this.anchorInterval = setTimeout(() => {
                    scroller.addEventListener("scroll", this.setKeepUrlToAnchor);
                }, 1000);
            }
        },
        async validRequestProtocol(rule, value, callback) {
            if (value?.length === 0 && this.isEdit) {
                return callback(new Error(this.$t("domain.editPage.placeholder9")));
            }
            return callback();
        },
        // 回源HOST: req_host 的校验方法
        checkReqHost(rule, value, callback) {
            if (value && !checkReqHost(value)) {
                callback(new Error(this.$t("domain.create.tip19")));
            } else if (this.isNewEcgw && this.form.origin.some(o => o.origin_host) && this.form.req_host) {
                callback(new Error(this.$t("domain.create.tip20")));
            }
            callback();
        },
        editConfig() {
            if (!this.isDomainCorrect) {
                this.$message.warning(this.$t("domain.editPage.domainFailTip"));
                return;
            }
            const isEdit = true;
            SecurityAbilityModule.SET_IS_EDIT(isEdit);
        },
        // 根据不同 type 设置初始值
        oAuthChange(v) {
            this.form.url_auth.key = "";
            this.form.url_auth.delim_char = v === 1 ? "-" : "";
            this.form.url_auth.time_limit = null;

            if (v === 1) {
                this.form.url_auth.auth_key = "auth_key";
            } else if (v === 3) {
                this.form.url_auth.timestamp_key = "timestamp";
                this.form.url_auth.auth_key = "auth_key";
            }
        },
        onJumpEnableChange(val) {
            const originalConf = SecurityAbilityModule.securityBasicConfigOriginForm;
            this.form.force_jump.force_type = originalConf?.force_jump?.force_type;
            this.form.force_jump.force_status = originalConf?.force_jump?.force_status;
        },
        follow_302_change(val) {
            const originalConf = SecurityAbilityModule.securityBasicConfigOriginForm;
            this.$set(this.form, "follow302_is_valid", originalConf?.follow302_is_valid?.length ? [1] : []);
            this.$set(this.form, "follow302_times", originalConf?.follow302_times);
        },
        // 校验：指定源站回源HOST
        async valid_origin_host(rule, value, callback) {
            if (value === "" || value === null || value === undefined) return callback();
            if (this.isNewEcgw && value && this.form.req_host && this.isEdit) {
                return callback(this.$t("domain.create.tip20"));
            }
            return callback();
        },
        async valid_follow302_times(rule, value, callback) {
            const num = /^[0-9]*$/;
            if (!this.form.follow302_times && this.form.follow302_times !== 0) return callback();
            if (this.form.follow302_times < 1 || this.form.follow302_times > 5) {
                return callback(this.$t("domain.detail.tip94"));
            }
            if (!num.test(value)) {
                return callback(this.$t("domain.detail.tip95"));
            }
            return callback();
        },
        // 跳转类型
        async valid_force_type(rule, value, callback) {
            if (!this.isEdit) return callback();
            if (!value) {
                return callback(new Error(i18n.t("domain.detail.tip61")));
            }
            return callback();
        },
        handleHttpsStatusChange(val) {
            if (val?.includes("https")) {
                const originalConf = SecurityAbilityModule.securityBasicConfigOriginForm;
                this.$set(this.form, "cert_name", originalConf?.cert_name);
                this.$set(this.form, "cert_name_gm", originalConf?.cert_name_gm);
                if (originalConf?.ssl?.length > 0) {
                    this.$set(this.form, "ssl", cloneDeep(originalConf?.ssl));
                }
                this.$set(this.form, "ssl_stapling", cloneDeep(originalConf?.ssl_stapling));
                this.$set(this.form.basic_conf, "use_http2", cloneDeep(originalConf?.basic_conf?.use_http2));
                this.$set(this.form, "ssl_ciphers", cloneDeep(originalConf?.ssl_ciphers));
                this.$set(this.form, "custom_ssl_ciphers", cloneDeep(originalConf?.custom_ssl_ciphers));
                this.$set(this.form, "hsts", cloneDeep(originalConf?.hsts));
                this.$set(this.form, "quic", cloneDeep(originalConf?.quic));
                this.$set(this.form, "force_jump", cloneDeep(originalConf?.force_jump));
            } else {
                this.$set(this.form, "ssl", []);
                this.$set(this.form, "ssl_stapling", "off");
                this.$set(this.form.basic_conf, "use_http2", 0);
                this.$set(this.form, "ssl_ciphers", "");
                this.$set(this.form, "custom_ssl_ciphers", []);
            }
        },

        async handleCancel() {
            if (this.isSecurityFormSame) {
                SecurityAbilityModule.SET_IS_EDIT(false);
                this.$refs.form.clearValidate();
            } else {
                await this.$confirm(this.$t("domain.editPage.tip13"), this.$t("common.messageBox.title"), {
                    confirmButtonText: this.$t("common.dialog.submit"),
                    cancelButtonText: this.$t("common.dialog.cancel"),
                    type: "warning",
                }).then(() => {
                    if (!this.isSecurityFormSame) {
                        this.getDomainDetail();
                        this.$refs.form.clearValidate();
                    } else {
                        this.$refs.form.clearValidate();
                    }
                });
            }
        },
        submitWorkOrder() {
            this.handleUpdate();
        },
        renderFormValidate() {
            this.validateForm = [];
            this.validateForm.push(this.$refs.form);
            if (
                this.webSocketEnable &&
                this.isShowWebsocket &&
                this.validateComponents.indexOf("websocket") < 0
            ) {
                this.validateComponents.push("websocket");
            }
            // 访问控制子组件表单校验
            if (this.showForStaticAndIcdn) {
                // referer防盗链
                if (this.validateComponents.indexOf("refererChain") < 0) {
                    this.validateComponents.push("refererChain");
                }
                // ipset黑白名单
                if (this.ipsetWhitelist && this.validateComponents.indexOf("ipsetList") < 0) {
                    this.validateComponents.push("ipsetList");
                }
                // ip黑白名单
                if (this.validateComponents.indexOf("ipBlackWhiteList") < 0) {
                    this.validateComponents.push("ipBlackWhiteList");
                }
            }
            // ua 黑白名单除了需要满足 showForStaticAndIcdn， 还需要是新框架
            if (this.showForStaticAndIcdn && this.isNewEcgw) {
                // ua黑白名单
                if (this.validateComponents.indexOf("userAgent") < 0) {
                    this.validateComponents.push("userAgent");
                }
                // url黑白名单
                if (this.validateComponents.indexOf("uriDeny") < 0) {
                    this.validateComponents.push("uriDeny");
                }
            }

            // hsts子组件表单校验添加到form表单中
            if (
                this.isNewEcgw &&
                this.requestProtocolIncludesHttps &&
                this.validateComponents.indexOf("hsts") < 0
            ) {
                this.validateComponents.push("hsts");
            }
            // 服务端口子组件表单校验添加到form表单中
            if (
                (this.requestProtocolIncludesHttp || this.requestProtocolIncludesHttps) &&
                this.validateComponents.indexOf("serverPort") < 0
            ) {
                this.validateComponents.push("serverPort");
            }

            // 加密套件，添加表单校验
            if (
                this.requestProtocolIncludesHttps &&
                this.hasCertName &&
                this.validateComponents.indexOf("encryptionSuite") < 0
            ) {
                this.validateComponents.push("encryptionSuite");
            } else {
                this.validateComponents = this.validateComponents?.filter(item => item !== "encryptionSuite");
            }

            // 新框架才支持 页面优化、url 鉴权
            if (this.isNewEcgw) {
                this.validateComponents.push("htmlForbid");
                this.validateComponents.push("urlAuth");
                this.validateComponents.push("cusGzip");
                this.$refs.splitSetRef && this.validateComponents.push("splitSetRef");
            }

            // 抽离成组件后，需要将组件中的表单加入校验
            this.validateComponents.forEach(name => {
                this.validateForm.push(this.$refs[name]?.$refs[`${name}Form`]);
            });

            // 可插拔配置-添加可配置模块的表单校验
            this.validateForm.push(...this.renderConfigModuleValidate());
        },
        // 点击弹窗的 提交保存 按钮时，触发事件
        async handleSubmit(resolve, reject) {
            this.renderFormValidate();
            try {
                await Promise.all(this.validateForm.map(itm => formValidate2Promise(itm)));
            } catch (err) {
                this.$message({
                    showClose: true,
                    message: err.message || err,
                    type: "error",
                });

                reject && reject(false);
                return false;
            }
            this.$message.warning(this.$t("domain.detail.placeholder33"));
            try {
                this.detailLoading = true;
                this.submitLoading = true;
                await this.$ctFetch(siteUrl.domainUpdate, {
                    method: "POST",
                    transferType: "json",
                    body: {
                        ...this.formData,
                        ...this.getApiData(), // 可插拔配置-合并可配置模块的数据
                    },
                    clearEmptyParams: false,
                });
                this.$message.success(this.$t("domain.create.tip27"));
                const isEdit = false;
                SecurityAbilityModule.SET_IS_EDIT(isEdit);
                SecurityAbilityModule.SET_SECURITY_RENDER_KEY();
                resolve && resolve(true);
            } catch (e) {
                this.submitLoading = false;
                this.detailLoading = false;
                reject && reject(false);

                const code = e?.data?.code || "";
                if ([CODE_SERVER_PORT_ERROR_2364, CODE_SERVER_PORT_ERROR_2367].includes(code)) {
                    this.serverPortTipVisible = true;
                    this.serverPortErrorCode = code;
                } else {
                    this.$errorHandler(e);
                }
            } finally {
                this.detailLoading = false;
                this.submitLoading = false;
            }
        },
        // 点击页面右下角：保存 按钮时，触发事件
        async handleUpdateDomain(resolve, reject) {
            this.renderFormValidate();
            try {
                await Promise.all(this.validateForm.map(itm => formValidate2Promise(itm)));
            } catch (err) {
                this.$message({
                    showClose: true,
                    message: err.message || err,
                    type: "error",
                });

                reject && reject(false);
                return false;
            }
            this.existOrderLoading = false;
            this.workOrderVisible = true;
            this.isQueryDomainDetail = false;
        },
        async handleUpdate(resolve, reject) {
            try {
                this.detailLoading = true;
                this.submitLoading = true;
                this.existOrderLoading = true;
                // 重置is_exist，确保每次请求弹窗会在10s内关闭
                this.is_exist = null;
                await this.$ctFetch(siteUrl.domainUpdate, {
                    method: "POST",
                    transferType: "json",
                    body: {
                        ...this.formData,
                        ...this.getApiData(),
                    },
                    clearEmptyParams: false,
                });
                this.handleExistOrder();
                this.handleOrderRefresh();
                this.$message.success(this.$t("domain.create.tip27"));
                const isEdit = false;
                SecurityAbilityModule.SET_IS_EDIT(isEdit);
                resolve && resolve(true);
            } catch (e) {
                this.submitLoading = false;
                this.detailLoading = false;
                this.workOrderVisible = false; // 域名更新接口如果返回失败，需要关闭弹窗
                // this.isQueryDomainDetail = true;

                const code = e?.data?.code || "";
                if ([CODE_SERVER_PORT_ERROR_2364, CODE_SERVER_PORT_ERROR_2367].includes(code)) {
                    this.serverPortTipVisible = true;
                    this.serverPortErrorCode = code;
                } else {
                    this.$errorHandler(e);
                }
                reject && reject(false);
            } finally {
                this.detailLoading = false;
                this.submitLoading = false;
            }
        },
        async handleExistOrder() {
            try {
                const res = await this.$ctFetch(siteUrl.isExistOrder, {
                    method: "GET",
                    transferType: "json",
                    data: {
                        domain: this.securityDomain,
                    },
                });
                // 回填数据
                if (res) {
                    // 是否有在途工单：true表示有在途工单；false表示没有在途工单，即工单已完结
                    this.is_exist = res?.is_exist;
                    if (res?.is_exist === true) {
                        this.workOrderVisible = false;
                        this.isQueryDomainDetail = true;
                        // SecurityAbilityModule.SET_SECURITY_RENDER_KEY(); // 该行代码作用：生命周期会重新走一遍，然后重新调用获取数据的接口
                    }
                }
            } catch (error) {
                this.$errorHandler(error);
            }
        },
        website_ipv6_access_mark_switch_change(val) {
            if (val) {
                this.$set(
                    this.form.website_ipv6_access_mark,
                    "show_content",
                    this.$t("domain.editPage.ipv6Tip")
                );
                this.$set(this.form.website_ipv6_access_mark, "show_times", 10);

                const originalConf = SecurityAbilityModule.securityBasicConfigOriginForm;
                if (originalConf?.website_ipv6_access_mark?.switch) {
                    this.$set(
                        this.form.website_ipv6_access_mark,
                        "show_content",
                        originalConf?.website_ipv6_access_mark?.show_content
                    );
                    this.$set(
                        this.form.website_ipv6_access_mark,
                        "show_times",
                        originalConf?.website_ipv6_access_mark?.show_times
                    );
                }
            } else {
                this.$set(this.form.website_ipv6_access_mark, "show_content", null);
                this.$set(this.form.website_ipv6_access_mark, "show_times", null);
            }
        },

        // 处理基础信息
        handleBaseInfo(data) {
            // 处理加速区域
            const baseInfo = [
                { label: this.$t("domain.create.domainName"), value: data?.domain || "-" },
                { label: "CNAME", value: data?.cname || "-" },
                {
                    label: this.$t(
                        window.__POWERED_BY_QIANKUN__ ? "domain.editPage.label2" : "domain.create.acceType"
                    ),
                    value: this.productCodeLabel(data?.product_type || data?.product_code) || "-",
                },
                {
                    label: this.$t(
                        window.__POWERED_BY_QIANKUN__
                            ? "domain.create.serviceArea"
                            : "domain.list.tableLabel4"
                    ),
                    value: this.areaScopeLabel(data?.area_scope) || "-",
                },
                { label: this.$t("domain.list.tableLabel6"), value: data?.create_time || "-" },
            ];
            this.baseInfo = cloneDeep(baseInfo);
        },

        // 判断证书链是否完整，提示用户是否需要变更
        async handleCertNameChange(val, isGm = false) {
            // 根据是否为国密证书决定使用哪个变量和引用
            const certNameField = isGm ? "cert_name_gm" : "cert_name";
            const lastSelectedField = isGm ? "lastSelectedCertNameGm" : "lastSelectedCertName";
            const selected = this.cert_name_list.find(cert => cert.cert_name === val);

            if (!selected || !val) {
                this[lastSelectedField] = "";

                // 国密证书和国际证书都没有配置时，重置 HTTP2.0、ocsp stapling、TLS版本和QUIC
                if (!this.hasCertName) {
                    // 清空证书，重置 HTTP2.0、ocsp stapling、TLS版本和QUIC
                    this.form.basic_conf.use_http2 = 0;
                    this.form.ssl_stapling = "off";
                    this.form.ssl = [];
                    this.form.quic.switch = 0;
                }

                this.$refs.form.validateField(["cert_name", "cert_name_gm"]);
                this.$nextTick(() => this.publicRevalidator(["quic.switch"]));

                return;
            }

            const callback = () => {
                this.$refs.form.validateField(["cert_name", "cert_name_gm"]);
                this.$nextTick(() => this.publicRevalidator(["quic.switch"]));

                if (this.isPoweredByQiankun && !this[lastSelectedField]) {
                    if (this.form.cert_name && this.form.cert_name_gm) {
                        if (isGm) {
                            if (this.form.ssl.length) this.form.ssl.push("GMTLSv1.1");
                        } else {
                            this.form.ssl = ["TLSv1.2", "TLSv1.3", "GMTLSv1.1"];
                        }
                    } else if (this.form.cert_name) {
                        this.form.ssl = ["TLSv1.2", "TLSv1.3"];
                    } else if (this.form.cert_name_gm) {
                        this.form.ssl = [];
                    }
                }

                this[lastSelectedField] = this.form[certNameField];
            };

            if (!selected.is_chain_complete) {
                try {
                    this.form[certNameField] = this[lastSelectedField];
                    await this.$confirm(
                        `${this.$t(
                            "domain.您使用的证书存在证书链不完整的情况，存在一定安全隐患，如您仍需提交则点击“继续”按钮进行提交，如您想取消操作则点击“取消”按钮。"
                        )}`,
                        `${this.$t("certificate.chain.title")}`,
                        {
                            confirmButtonText: `${this.$t("certificate.chain.ensure")}`,
                            cancelButtonText: `${this.$t("common.dialog.cancel")}`,
                            showClose: false,
                            iconClass: "el-icon-warning",
                        }
                    );
                    this.form[certNameField] = selected.cert_name;
                    callback();
                } catch (err) {
                    this.form[certNameField] = this[lastSelectedField];
                } finally {
                    if (!isGm) {
                        this.$refs.cert_name.blur();
                    } else {
                        this.$refs.cert_name_gm.blur();
                    }
                }
            } else {
                callback();
            }
        },

        init(val) {
            if (!val) return;
            if (this.securityDomain !== val.domain) return;
            this.$nextTick(async () => {
                const thisData = cloneDeep(val);
                this.temp_domain_detail = cloneDeep(val);

                // 可插拔配置-在原有初始化之前,先合并可配置模块的字段
                this.mergeConfigModuleFields();

                // 域名详情接口如果没有返回 backorigin_mode 字段，前端需要将 backorigin_mode 设置为默认值：0
                if (
                    this.temp_domain_detail?.backorigin_mode === "" ||
                    this.temp_domain_detail?.backorigin_mode === null ||
                    this.temp_domain_detail?.backorigin_mode === undefined
                ) {
                    this.$set(this.temp_domain_detail, "backorigin_mode", 0);
                }
                Object.assign(this.$data, { ...thisData });

                this.handleBaseInfo(cloneDeep(thisData));

                this.form.area_scope = thisData?.area_scope;
                // 回源配置
                let originArr = [];
                if (thisData?.origin_host_type === 1 && Object.keys(thisData?.origin_host_http).length > 0) {
                    originArr = (thisData?.origin || []).map(item => {
                        return {
                            origin_host: thisData?.origin_host_http[item?.origin] || "",
                            ...item,
                        };
                    });
                } else {
                    originArr = (thisData?.origin || []).map(item => {
                        return {
                            origin_host: "",
                            ...item,
                        };
                    });
                }
                let origin = [];
                origin = originArr.map(item => {
                    if (
                        this.isNewEcgw &&
                        item.origin === thisData.xos_origin?.origin &&
                        thisData.xos_origin_is === 1
                    ) {
                        return { is_xos: 1, ...item };
                    } else if (this.isNewEcgw && item.origin === thisData.zos_origin?.origin) {
                        return { is_xos: 2, ...item };
                    } else {
                        return { is_xos: 0, ...item };
                    }
                });
                this.$set(this.form, "origin", origin);
                this.form.domain = thisData.domain;

                this.form.backorigin_protocol = thisData?.backorigin_protocol || "http";
                this.form.proxy_gmssl_mode = thisData?.proxy_gmssl_mode || "";
                // 忽略回源参数开关
                this.form.ignore_backorigin_args = thisData?.ignore_backorigin_args || 0;
                // 回源参数规则
                const backorigin_arg_rewrite_other = cloneDeep(thisData?.backorigin_arg_rewrite_other) || [];
                backorigin_arg_rewrite_other?.forEach(item => {
                    if (item?.args && Object.keys(item.args)?.length > 0) {
                        // args 转换成 argsList格式
                        item.argsList = Object.keys(item?.args).map(key => {
                            return { name: key, value: item?.args[key] };
                        });
                    }
                });
                this.$set(
                    this,
                    "temp_backorigin_arg_rewrite_other",
                    cloneDeep(backorigin_arg_rewrite_other) || []
                );
                this.$set(this.form, "backorigin_arg_rewrite_other", backorigin_arg_rewrite_other);

                // 分片回源
                if (this.isNewEcgw) {
                    this.form.split_white = thisData?.split_white ?? false;
                    if (thisData?.split_set) {
                        this.form.split_set.split_set = thisData?.split_set || "off";
                        if (
                            this.form.split_set.split_set !== "off" &&
                            Object.keys(thisData?.split_set_condition).length
                        ) {
                            this.form.split_set.mode = thisData?.split_set_condition["split_set"]?.[0]?.mode;
                            this.form.split_set.content =
                                thisData?.split_set_condition["split_set"]?.[0]?.content;
                        }
                    } else {
                        this.$set(this.form, "split_set", thisData?.split_set ?? "");
                    }
                }

                // http_origin_port 端口有返回值的情况
                if (
                    thisData?.basic_conf &&
                    Object.keys(thisData?.basic_conf).length > 0 &&
                    thisData?.basic_conf?.http_origin_port
                ) {
                    this.$set(
                        this.form.basic_conf,
                        "http_origin_port",
                        thisData?.basic_conf?.http_origin_port
                    );
                } else {
                    this.$set(this.form.basic_conf, "http_origin_port", "80");
                }

                // https_origin_port 端口有返回值的情况
                if (
                    thisData?.basic_conf &&
                    Object.keys(thisData.basic_conf).length > 0 &&
                    thisData?.basic_conf?.https_origin_port
                ) {
                    this.$set(
                        this.form.basic_conf,
                        "https_origin_port",
                        thisData?.basic_conf?.https_origin_port
                    );
                } else {
                    this.$set(this.form.basic_conf, "https_origin_port", "443");
                }

                // 服务端口
                // HTTP端口
                const http_server_port = thisData?.basic_conf?.http_server_port || "";
                this.$set(this.form, "http_server_port", http_server_port);

                this.$set(this, "temp_http_server_port", http_server_port);
                // HTTPS端口
                const https_server_port = thisData?.basic_conf?.https_server_port || "";
                this.$set(this.form, "https_server_port", https_server_port);

                this.$set(this, "temp_https_server_port", https_server_port + "");

                // 跟随请求端口回源
                this.$set(
                    this.form,
                    "follow_request_backport",
                    [0, 1].includes(thisData?.follow_request_backport) ? thisData?.follow_request_backport : 0
                );

                // 回源SNI
                this.$set(
                    this.form,
                    "backorigin_sni",
                    Object.assign({ ...this.form.backorigin_sni }, thisData?.backorigin_sni || {})
                );

                this.form.req_host = thisData?.req_host || "";
                this.form.internal_uri_rewrite_new = cloneDeep(thisData?.internal_uri_rewrite_new) || [];
                // 回源302/301跟随
                this.form.follow_302 = thisData?.basic_conf?.follow_302 || 0;
                if (thisData?.basic_conf?.follow_302 === 1) {
                    this.form.follow302_times = thisData?.follow302_times || 1;
                    this.temp_follow302_times = thisData?.follow302_times || 1;
                }
                if (thisData?.follow302_is_valid === 1) {
                    this.form.follow302_is_valid = [1];
                } else {
                    this.form.follow302_is_valid = [];
                }
                // 证书
                this.form.cert_name = this.temp_domain_detail?.cert_name || "";
                this.form.cert_name_gm = this.temp_domain_detail?.cert_name_gm || "";
                this.lastSelectedCertName = this.temp_domain_detail?.cert_name || "";
                this.lastSelectedCertNameGm = this.temp_domain_detail?.cert_name_gm || "";
                // quic
                this.form.quic = Object.assign({ switch: 0 }, this.temp_domain_detail?.quic || {});
                // 请求协议
                let request_protocol = []; // 切换域名时，将request_protocol清空，防止重复push
                if (thisData?.http_status === "on") {
                    request_protocol.push("http");
                }
                if (thisData?.https_status === "on") {
                    request_protocol.push("https");
                }
                if (!thisData?.http_status === "on" && !thisData?.https_status === "on") {
                    request_protocol = [];
                }
                this.$set(this.form, "request_protocol", request_protocol);
                this.$set(this, "temp_request_protocol", request_protocol);
                // 强制跳转-开关
                this.form.force_jump.jump_enable = thisData?.force_jump?.jump_enable || false;
                // 跳转类型
                const force_type = (thisData?.force_jump && thisData?.force_jump?.force_type) || "http";

                // 请求协议都有 HTTP和HTTPS的时候，跳转类型展示详情接口返回的值
                if (this.requestProtocolIncludesHttp && this.requestProtocolIncludesHttps) {
                    this.$set(this.form.force_jump, "force_type", force_type);
                } else if (this.requestProtocolIncludesHttp && !this.requestProtocolIncludesHttps) {
                    // 请求协议只有 HTTP的时候，跳转类型展示Https->Http
                    this.$set(this.form.force_jump, "force_type", "http");
                } else if (this.requestProtocolIncludesHttps && !this.requestProtocolIncludesHttp) {
                    // 请求协议只有 HTTPS的时候，跳转类型展示Http->Https
                    this.$set(this.form.force_jump, "force_type", "https");
                } else {
                    this.$set(this.form.force_jump, "force_type", "");
                }
                // 跳转方式
                this.form.force_jump.force_status = thisData?.force_jump?.force_status || "302";
                // HTTPS配置
                const use_http2 = thisData?.basic_conf?.use_http2 || 0;
                this.$set(this.form.basic_conf, "use_http2", use_http2);
                // OCSP Stapling
                this.form.ssl_stapling = thisData?.ssl_stapling || "off";
                // TLS版本
                let temp_ssl_data = [];
                if (thisData?.ssl && typeof thisData?.ssl === "string") {
                    temp_ssl_data = thisData?.ssl?.split(",");
                }
                this.form.ssl = temp_ssl_data;

                // 头部修改
                const resp_headers = cloneDeep(thisData?.resp_headers) || [];
                const str1 = "content-type";
                const str2 = "content-disposition";
                resp_headers.map(item => {
                    if (
                        this.isDomainEndsWithCtyun &&
                        (item.key?.toUpperCase() === str1?.toUpperCase() ||
                            item.key?.toUpperCase() === str2?.toUpperCase())
                    ) {
                        return (item.disabled = true);
                    }
                });
                let respHeadersShowXosDomainTipData = false;
                respHeadersShowXosDomainTipData = resp_headers?.some(
                    itm =>
                        itm.key.toUpperCase() === str1?.toUpperCase() ||
                        itm.key?.toUpperCase() === str2?.toUpperCase()
                );
                this.respHeadersShowXosDomainTip = respHeadersShowXosDomainTipData;
                this.form.resp_headers = cloneDeep(resp_headers) || [];
                this.form.req_headers = cloneDeep(thisData?.req_headers) || [];
                // 乾坤入口，才需要回填
                if (this.isPoweredByQiankun) {
                    // IPv6外链改造
                    const outlink_replace_filter = {
                        switch: 0,
                        times: 0,
                        ignore_status: [],
                        blacklist: "",
                        validity_scope: "ipv6",
                        match_list: [
                            {
                                priority: 10,
                                type: "",
                                value: "",
                                regex_list: [
                                    {
                                        regex: "",
                                        pos_tab: [],
                                        suffix: [],
                                        visit: "",
                                    },
                                ],
                            },
                        ],
                        html_act: {
                            switch: 0,
                        },
                    };
                    this.form.outlink_replace_filter =
                        cloneDeep(thisData?.outlink_replace_filter) || outlink_replace_filter;
                    if (thisData?.outlink_replace_filter?.blacklist) {
                        this.form.outlink_replace_filter.blacklist = thisData?.outlink_replace_filter?.blacklist?.join();
                    } else {
                        this.$set(this.form.outlink_replace_filter, "blacklist", "");
                    }
                    // 改造生效范围：接口可能没返回这个值，需要设置默认值
                    const validity_scope = thisData?.outlink_replace_filter?.validity_scope || "ipv6";
                    this.$set(this.form.outlink_replace_filter, "validity_scope", validity_scope);

                    // 点击类外链改造
                    let html_act = {};
                    if (
                        thisData?.outlink_replace_filter?.html_act &&
                        Object.keys(thisData?.outlink_replace_filter?.html_act).length > 0
                    ) {
                        html_act = thisData?.outlink_replace_filter?.html_act;
                    } else {
                        html_act = {
                            switch: 0,
                        };
                    }
                    this.$set(this.form.outlink_replace_filter, "html_act", html_act);

                    this.form.outlink_replace_filter.switch =
                        cloneDeep(thisData?.outlink_replace_filter?.switch) || 0;

                    if (thisData?.outlink_replace_filter?.switch === 1) {
                        this.form.outlink_replace_filter.times = cloneDeep(
                            thisData?.outlink_replace_filter?.times
                        );
                    }
                    // 网站首页IPv6标识：详情返回值如果为undefined，就给默认值
                    const website_ipv6_access_mark = {
                        switch: 0,
                        show_content: this.$t("domain.editPage.ipv6Tip"),
                        show_times: 10,
                        homepage_url: "",
                    };
                    this.form.website_ipv6_access_mark =
                        cloneDeep(thisData?.website_ipv6_access_mark) || website_ipv6_access_mark;
                    // 网站首页IPv6标识
                    this.form.website_ipv6_access_mark.switch =
                        cloneDeep(thisData?.website_ipv6_access_mark?.switch) || 0;

                    if (thisData?.website_ipv6_access_mark?.switch === 1) {
                        this.form.website_ipv6_access_mark.show_content =
                            thisData?.website_ipv6_access_mark?.show_content;

                        this.form.website_ipv6_access_mark.show_times =
                            thisData?.website_ipv6_access_mark?.show_times;
                    }
                    this.form.waf_secure_conf = cloneDeep(thisData?.waf_secure_conf) || {};
                    // 回填 JA3 指纹配置
                    this.form.ssl_ja3_enable = thisData?.ssl_ja3_enable || null;
                }
                // 回源超时时间设置
                this.form.backorigin_switch =
                    thisData?.backup_origin_timeout || thisData?.backup_origin_resptimeout ? 1 : 0;

                if (this.form.backorigin_switch) {
                    this.form.backup_origin_timeout = thisData?.backup_origin_timeout || "";
                    this.form.backup_origin_resptimeout = thisData?.backup_origin_resptimeout || "";
                }
                // websocket
                // 乾坤入口 或者 域名类型等于全站加速-websocket时("105": "全站加速-websocket加速")， 才需要回填
                if (this.isShowWebsocket) {
                    this.form.websocket_speed = thisData?.websocket_speed || 0;
                    this.form.websocket_time.backup_origin_timeout =
                        thisData?.websocket_time?.backup_origin_timeout || "";
                    this.form.websocket_time.backup_origin_sendtimeout =
                        thisData?.websocket_time?.backup_origin_sendtimeout; // 非自助，值回传
                    this.form.websocket_time.backup_origin_resptimeout =
                        thisData?.websocket_time?.backup_origin_resptimeout; // 非自助，值回传
                    this.websocket_url =
                        thisData?.websocket_url || "https://www.ctyun.cn/document/10065985/10192895";
                }
                // 文件压缩
                this.form.cus_gzip = cloneDeep(thisData?.cus_gzip) || [];
                // 用于自助功能锁
                this.funcName = thisData?.funcName || [];
                // 可插拔配置-同步功能锁数据到 store
                ConfigModulesModule.SET_FUNC_LOCKS(this.funcName);

                // ipv6_switch
                this.ipv6_switch = thisData?.ipv6_switch === 0 ? thisData?.ipv6_switch : "";
                // URL鉴权
                let url_auth = {};
                if (thisData?.url_auth && Object.keys(thisData?.url_auth).length > 0) {
                    url_auth = cloneDeep(thisData?.url_auth);
                } else {
                    url_auth = {
                        switch: false,
                    };
                }
                this.$set(this.form, "url_auth", url_auth);

                // 单请求限速
                this.form.limit_speed_const = conditionDestruct(
                    get(thisData, "limit_speed_const_condition", {}),
                    cloneDeep(get(thisData, "limit_speed_const", []))
                );

                // 访问限频
                this.form.entry_limits = conditionDestruct(
                    get(thisData, "entry_limits_condition", {}),
                    cloneDeep(get(thisData, "entry_limits", []))
                );

                this.urlAuthFormDisabled = thisData?.url_auth?.switch && thisData?.url_auth?.type === 0;
                // HSTS
                this.form.hsts.switch = thisData?.hsts?.switch;
                if (thisData?.hsts?.switch === 1) {
                    this.form.hsts.max_age = thisData?.hsts?.max_age;
                    this.form.hsts.include_sub_domains = thisData?.hsts?.include_sub_domains || null;
                } else {
                    this.form.hsts.max_age = 1200;
                    this.form.hsts.include_sub_domains = null;
                }

                // 私有Bucket回源
                const ak = thisData?.xos_origin?.ak || "";
                const sk = thisData?.xos_origin?.sk || "";
                this.form.use_ori_uri = thisData?.xos_origin?.use_ori_uri;
                let bucket_status = false;
                if (!!ak || !!sk) {
                    bucket_status = true;
                }
                this.form.bucket_status = bucket_status;
                this.form.ak = ak;
                this.form.sk = sk;

                // zos
                const zos_origin = thisData?.zos_origin || {};
                if (zos_origin.switch) {
                    this.form.zos_origin = Object.assign({ ...this.form.zos_origin }, zos_origin);
                    if (zos_origin.private_bucket) {
                        this.form.ak = zos_origin.ak;
                        this.form.sk = zos_origin.sk;
                        this.form.bucket_status = true;
                    }
                }

                // 访问控制
                if (!this.isPoweredByQiankun) {
                    // 访问控制-Referer防盗链
                    this.form.refererType = "allow";
                    this.$set(this.form, "domainList", "");
                    this.domainList = [];
                    // Referer防盗链-白名单
                    if (thisData.white_referer && JSON.stringify(thisData.white_referer) !== "{}") {
                        this.form.referer = "on";
                        this.form.allow_empty = thisData.white_referer.allow_empty;
                        this.form.referer_empty_protocol = thisData.white_referer.referer_empty_protocol;
                        this.form.match_all_ports = thisData.white_referer.match_all_ports;
                        this.form.ignore_case = thisData.white_referer.ignore_case;
                        this.form.is_append = thisData.white_referer.is_append;
                        if (
                            thisData.white_referer.allow_list &&
                            thisData.white_referer.allow_list.length > 0
                        ) {
                            this.form.domainList = thisData.white_referer.allow_list.join("\n");

                            this.domainList = cloneDeep(thisData.white_referer.allow_list);
                        } else {
                            this.form.domainList = "";
                        }
                    }
                    // Referer防盗链-黑名单
                    if (thisData.black_referer && JSON.stringify(thisData.black_referer) !== "{}") {
                        this.form.referer = "on";
                        this.form.allow_empty = thisData.black_referer.allow_empty;
                        this.form.referer_empty_protocol = thisData.black_referer.referer_empty_protocol;
                        this.form.match_all_ports = thisData.black_referer.match_all_ports;
                        this.form.ignore_case = thisData.black_referer.ignore_case;
                        this.form.is_append = thisData.black_referer.is_append;
                        this.form.refererType = "block";
                        if (
                            thisData.black_referer.allow_list &&
                            thisData.black_referer.allow_list.length > 0
                        ) {
                            this.form.domainList = thisData.black_referer.allow_list.join("\n");

                            this.domainList = cloneDeep(thisData.black_referer.allow_list);
                        } else {
                            this.form.domainList = "";
                        }
                    }
                    // ipset黑白名单
                    if (thisData?.ip_set_forbid) {
                        this.form.ip_set_forbid.switch = thisData.ip_set_forbid.switch;
                        this.form.ip_set_forbid.forbid_type = thisData.ip_set_forbid.forbid_type;
                        this.form.ip_set_forbid.alias_name = thisData.ip_set_forbid.alias_name;
                    }

                    // 访问控制-ip黑白名单
                    this.$set(this.form, "ip", "");
                    // ip黑白名单-黑名单
                    if (thisData?.ip_black_list) {
                        this.form.ip_switch = "on";
                        this.form.ipType = "block";
                        this.$set(this.form, "ip", thisData?.ip_black_list?.split(",")?.join("\n"));
                    }
                    // ip黑白名单-白名单
                    if (thisData?.ip_white_list) {
                        this.form.ip_switch = "on";
                        this.form.ipType = "allow";
                        this.$set(this.form, "ip", thisData?.ip_white_list?.split(",")?.join("\n"));
                    }

                    // 访问控制-ua黑白名单
                    const defaultUserAgent = { ignore_case: "on", mode: 1 };
                    if (thisData?.user_agent && Object.keys(thisData.user_agent).length > 0) {
                        this.form.user_agent = {
                            ...defaultUserAgent,
                            ...cloneDeep(thisData?.user_agent),
                        };
                        this.form.ua_switch = "on";

                        if (thisData?.user_agent?.ua && thisData?.user_agent?.ua?.length > 0) {
                            this.form.user_agent.ua = thisData?.user_agent?.ua?.join("\n");
                            this.uaList = cloneDeep(thisData?.user_agent?.ua);
                        }
                    } else {
                        this.form.ua_switch = "off";
                        this.$set(this.form, "user_agent", { ...defaultUserAgent, type: 1, ua: "" });
                    }
                    // 访问控制-url黑白名单
                    if (thisData?.uri_deny && Object.keys(thisData.uri_deny).length > 0) {
                        this.form.uri_deny = cloneDeep(thisData?.uri_deny);
                        this.form.uri_switch = "on";

                        if (thisData?.uri_deny?.uri && thisData?.uri_deny?.uri?.length > 0) {
                            this.form.uri_deny.uri = thisData?.uri_deny?.uri?.join("\n");
                            this.uriList = cloneDeep(thisData?.uri_deny?.uri);
                        }
                    } else {
                        this.form.uri_switch = "off";
                        this.$set(this.form, "uri_deny", { type: 1, uri: "" });
                    }
                }
                this.upload_speed = thisData?.upload_speed || 0;
                this.signature = thisData?.signature;
                this.form.product_code = thisData?.product_code;
                // html禁止操作
                let html_forbid_op = [];
                if (thisData.html_forbid_op && thisData.html_forbid_op.length > 0) {
                    html_forbid_op = cloneDeep(thisData.html_forbid_op);
                } else {
                    html_forbid_op = [];
                }

                const temp_html_forbid_op = cloneDeep(html_forbid_op);
                if (
                    thisData?.html_forbid_op_condition &&
                    Object.keys(thisData?.html_forbid_op_condition).length > 0
                ) {
                    temp_html_forbid_op.map(item => {
                        if (thisData?.html_forbid_op_condition[item?.id]) {
                            item.mode = thisData?.html_forbid_op_condition[item.id][0]?.mode;
                            item.content = thisData?.html_forbid_op_condition[item.id][0]?.content;
                        }
                    });
                }
                this.form.html_forbid_op = cloneDeep(temp_html_forbid_op);

                // 域名版本回填
                this.form.version = thisData?.version || "";

                // 加密套件-回填
                this.form.ssl_ciphers = thisData?.ssl_ciphers || "";
                this.form.custom_ssl_ciphers = (
                    (thisData?.custom_ssl_ciphers && thisData?.custom_ssl_ciphers?.split(":")) ||
                    []
                ).filter(item => {
                    return [...StatisticsModule.customSsl, ...StatisticsModule.gmCustomSsl].includes(item);
                });

                // 可插拔配置-初始化可配置模块的数据
                this.initConfigModules(thisData);

                SecurityAbilityModule.SET_SECURITY_BASIC_ORIGIN_FORM(cloneDeep(this.form));
                SecurityAbilityModule.SET_SECURITY_BASIC_CURRENT_FORM(cloneDeep(this.form));
            });
        },
        async getDomainDetail() {
            if (!this.securityDomain) return;
            this.form = this.$options?.data()?.form; // 获取数据前先重置为默认值
            this.detailLoading = true;
            SecurityAbilityModule.SET_IS_DOMAIN_DETAIL_LOADING(true);

            try {
                this.getDomainDetailAbortController?.abort("cancel by user");
                this.getDomainDetailAbortController = new AbortController();
                const res = await this.$ctFetch(siteUrl.domainDetail, {
                    method: "GET",
                    transferType: "json",
                    data: {
                        domain: this.securityDomain,
                    },
                    signal: this.getDomainDetailAbortController.signal,
                });
                // 回填数据
                if (res) {
                    this.init(res);
                    SecurityAbilityModule.SET_IS_EDIT(false);
                }
            } catch (error) {
                if (error.name === "AbortError" || error.raw === "cancel by user") {
                    return;
                }

                SecurityAbilityModule.SET_IS_DOMAIN_DETAIL_LOADING(false);
                SecurityAbilityModule.SET_SECURITY_BASIC_ORIGIN_FORM(cloneDeep(this.form));
                SecurityAbilityModule.SET_SECURITY_BASIC_CURRENT_FORM(cloneDeep(this.form));
                this.$errorHandler(error);
            }

            this.detailLoading = false;
            SecurityAbilityModule.SET_IS_DOMAIN_DETAIL_LOADING(false);
            // detail接口调用完成后计算，避免浏览器回退导致计算错误
            this.calculateContainerTop(!this.fromAccelerationConfig);
            this.$ctBus.$emit("refershPage");
        },
        outlink_switch_change(val) {
            if (val) {
                if (this.ipv6_switch === 0) {
                    this.ipv6ActVisible = true;
                } else {
                    this.tipDialogVisible = true;
                }
            } else {
                const originalConf = SecurityAbilityModule.securityBasicConfigOriginForm;
                this.$set(this.form, "outlink_replace_filter", {
                    ...originalConf?.outlink_replace_filter,
                    switch: 0,
                });
            }
        },
        submitIpv6Act() {
            this.ipv6ActVisible = false;
            this.tipDialogVisible = true;
        },
        submitTip() {
            this.$set(this.form.outlink_replace_filter, "switch", 1);

            const originalConf = SecurityAbilityModule.securityBasicConfigOriginForm;
            this.$set(
                this.form.outlink_replace_filter,
                "times",
                originalConf?.outlink_replace_filter?.times || 0
            );
            this.$set(
                this.form.outlink_replace_filter,
                "validity_scope",
                originalConf?.outlink_replace_filter?.validity_scope || "ipv6"
            ); // 改造生效范围
            this.$set(
                this.form.outlink_replace_filter,
                "blacklist",
                originalConf?.outlink_replace_filter?.blacklist || ""
            ); // 外链改造黑名单
            this.$set(
                this.form.outlink_replace_filter.html_act,
                "switch",
                originalConf?.outlink_replace_filter?.html_act?.switch || 0
            ); // 点击类外链改造

            this.tipDialogVisible = false;
        },
        submitServerPortTip() {
            this.serverPortTipVisible = false;
        },
        cancel(dialogKey) {
            this[dialogKey] = false;
            if (dialogKey === "tipDialogVisible") {
                this.$set(this.form.outlink_replace_filter, "switch", 0);
            }
        },
        // 证书选择下拉框
        async getCertList() {
            if (!this.securityDomain) return;
            const params = {
                domain: this.securityDomain,
            };
            if (!this.isDomainEndsWithCtyun) {
                params.biz_type = 0;
            }
            try {
                this.cert_name_loading = true;
                const res = await this.$ctFetch(siteUrl.certSelector, {
                    method: "GET",
                    data: params,
                });
                // 回填数据
                if (res) {
                    this.cert_name_list = cloneDeep(res);
                }
            } catch (e) {
                store.$errorHandler(e);
            } finally {
                this.cert_name_loading = false;
            }
        },
        async submitBackOrigin() {
            if (this.currentType === "create") {
                this.form.backorigin_arg_rewrite_other.push({
                    ...this.backOriginForm,
                });
            } else {
                this.$set(
                    this.form.backorigin_arg_rewrite_other,
                    this.currentBackOriginIndex,
                    this.backOriginForm
                );
            }
            this.dialogBackOriginVisible = false;
        },
        async handleOper(row, currentType, tabName, i) {
            this.currentType = currentType;

            if (tabName === "backorigin_arg_rewrite_other") {
                this.currentBackOriginIndex = i;
            }
            if (currentType === "create") {
                const defaultFormMap = {
                    // 回源参数规则
                    backorigin_arg_rewrite_other: {
                        mode: "add",
                        argsList: [
                            {
                                name: "",
                                value: "",
                            },
                        ],
                        priority: 10,
                        keep_args_order: "off",
                        need_encode_args: "on",
                    },
                };
                row = defaultFormMap[tabName];
            }
            if (currentType === "delete") {
                const msgMap = {
                    backorigin_arg_rewrite_other: this.$t("domain.editPage.tip20"),
                };

                // 二次确认弹窗
                await this.$confirm(msgMap[tabName], this.$t("domain.delete"), {
                    type: "warning",
                });
                this.form[tabName].splice(i, 1);
            } else {
                if (tabName === "backorigin_arg_rewrite_other") {
                    this.dialogBackOriginVisible = true;
                    const rowData = cloneDeep(row);
                    this.$set(this, "backOriginForm", rowData);
                }
            }
        },
        initEditConfBtn() {
            const isEdit = false;
            SecurityAbilityModule.SET_IS_EDIT(isEdit);
        },
        async origin_protocol_change(val) {
            const originalConf = SecurityAbilityModule.securityBasicConfigOriginForm;
            if (val === "http") {
                if (originalConf.basic_conf.http_origin_port) {
                    this.$set(
                        this.form.basic_conf,
                        "http_origin_port",
                        originalConf.basic_conf.http_origin_port
                    );
                } else {
                    this.$set(this.form.basic_conf, "http_origin_port", "80");
                }
            } else if (val === "https" || val === "follow_request") {
                if (originalConf.basic_conf.http_origin_port) {
                    this.$set(
                        this.form.basic_conf,
                        "http_origin_port",
                        originalConf.basic_conf.http_origin_port
                    );
                } else {
                    this.$set(this.form.basic_conf, "http_origin_port", "80");
                }
                if (originalConf.basic_conf.https_origin_port) {
                    this.$set(
                        this.form.basic_conf,
                        "https_origin_port",
                        originalConf.basic_conf.https_origin_port
                    );
                } else {
                    this.$set(this.form.basic_conf, "https_origin_port", "443");
                }
            }

            this.$set(this.form, "proxy_gmssl_mode", originalConf.proxy_gmssl_mode);

            this.$refs.form.validateField(["basic_conf.http_origin_port", "basic_conf.https_origin_port"]);
        },
        onHstsChange(val) {
            if (!val) return;
            this.form.hsts.switch = val?.switch;
            this.form.hsts.max_age = val?.max_age;
            this.form.hsts.include_sub_domains = val?.include_sub_domains || null;
        },
        onEncryptionSuiteChange(val) {
            if (!val) return;
            this.form.ssl_ciphers = val?.ssl_ciphers;
            this.form.custom_ssl_ciphers = val?.custom_ssl_ciphers || [];
        },
        onWebsocketChange(val) {
            if (!val) return;
            this.form.websocket_speed = val.websocket_speed;
            this.form.websocket_time.backup_origin_timeout = val.backup_origin_timeout;
        },
        onHtmlForbidChange(val) {
            if (val && val.length > 0) {
                this.form.html_forbid_op = cloneDeep(val);
            } else {
                this.form.html_forbid_op = [];
            }
        },
        onBackOriginTimeChange(v) {
            if (!v) return;
            this.form.backorigin_switch = v.backorigin_switch;
            this.form.backup_origin_timeout = v.backup_origin_timeout;
            this.form.backup_origin_resptimeout = v.backup_origin_resptimeout;
        },
        onPrivateBucketOriginChange(val) {
            if (!val) return;
            this.form.bucket_status = val?.bucket_status;
            this.form.ak = val?.ak;
            this.form.sk = val?.sk;
        },
        onServerPortChange(val) {
            if (!val) return;
            this.form.http_server_port = val?.http_server_port;
            this.form.https_server_port = val?.https_server_port;
        },
        onOriginChange(val) {
            if (val && val.length > 0) {
                this.form.origin = cloneDeep(val);
            } else {
                this.form.origin = [];
            }
        },
        onBucketChange(v) {
            this.temp_bucket = cloneDeep(v);
        },
        // url鉴权
        onUrlAuthChange(val) {
            this.form.url_auth = val;
        },
        // HTTP响应头
        onRespHeadersChange(val) {
            if (val && val.length > 0) {
                this.form.resp_headers = cloneDeep(val);
            } else {
                this.form.resp_headers = [];
            }
        },
        // 回源HTTP请求头
        onReqHeadersChange(val) {
            if (val && val.length > 0) {
                this.form.req_headers = cloneDeep(val);
            } else {
                this.form.req_headers = [];
            }
        },
        // 回源URL改写
        onInternalUriRewriteNewChange(val) {
            if (val && val.length > 0) {
                this.form.internal_uri_rewrite_new = cloneDeep(val);
            } else {
                this.form.internal_uri_rewrite_new = [];
            }
        },
        // 文件压缩
        onCusGzipChange(val) {
            if (val && val.length > 0) {
                this.form.cus_gzip = cloneDeep(val);
            } else {
                this.form.cus_gzip = [];
            }
        },
        // Referer防盗链
        refererChainChange(val, data) {
            if (!val) return;
            this.form.referer = val?.referer;
            this.form.allow_empty = val?.allow_empty;
            this.form.referer_empty_protocol = val?.referer_empty_protocol;
            this.form.match_all_ports = val?.match_all_ports;
            this.form.ignore_case = val?.ignore_case;
            this.form.is_append = val?.is_append;
            this.form.refererType = val?.refererType;
            this.form.domainList = val?.domainList;
            this.domainList = data;
        },
        // ip黑白名单
        ipBlackWhiteListChange(val) {
            if (!val) return;
            this.form.ip_switch = val?.ip_switch;
            this.form.ipType = val?.ipType;
            this.form.ip = val?.ip;
        },
        // ua黑白名单
        uaChange(val, data) {
            if (!val) return;
            this.form.ua_switch = val?.ua_switch;
            this.form.user_agent.type = val?.user_agent?.type;
            this.form.user_agent.ua = val?.user_agent?.ua;
            this.form.user_agent.ignore_case = val?.user_agent?.ignore_case;
            this.form.user_agent.mode = val?.user_agent?.mode;
            this.uaList = cloneDeep(data);
        },
        // url黑白名单
        uriChange(val, data) {
            if (!val) return;
            this.form.uri_switch = val?.uri_switch;
            this.form.uri_deny.type = val?.uri_deny?.type;
            this.form.uri_deny.uri = val?.uri_deny?.uri;
            this.uriList = cloneDeep(data);
        },
        onJa3Change(val) {
            if (!val) return;
            this.form.ssl_ja3_enable = val?.ssl_ja3_enable;
        },
        ipsetListChange(val) {
            if (!val) return;
            this.form.ip_set_forbid.switch = val.switch;
            this.form.ip_set_forbid.forbid_type = val.forbid_type;
            this.form.ip_set_forbid.alias_name = val.alias_name;
        },
        /**
         * 校验回源HOST
         */
        handleValidateReqHost() {
            this.$refs.form.validateField("req_host");
        },
        /**
         * 联动校验源站中-指定源站回源HOST
         */
        handleReqHostChange() {
            // 联动校验
            this.$refs.subOrigin && this.$refs.subOrigin.handleCheckOriginHost();
        },
    },
};
</script>
<style lang="scss" scoped>
@import "../index.scss";
.content-wrap {
    overflow: hidden;
    flex: 1;
    min-height: 300px;
    ::v-deep {
        .el-form--label-left .el-form-item__label {
            // width: 140px !important;
            text-align: right;
        }
        // .el-form-item__content {
        //     margin-left: 140px !important;
        // }
    }
    .server_port_wrapper {
        display: flex;
        justify-content: start;
        .server_style {
            display: flex;
            .server_http_port_wrapper {
                ::v-deep {
                    .el-form-item__label {
                        width: 48px !important;
                    }
                    .el-form-item__content {
                        margin-left: 56px !important;
                        margin-bottom: 16px !important;
                    }
                }
            }
            .server_https_port_wrapper {
                ::v-deep {
                    .el-form-item__label {
                        width: 48px !important;
                    }
                    .el-form-item__content {
                        margin-left: 60px !important;
                        margin-bottom: 16px !important;
                    }
                }
            }
        }
    }
}
.header-tip-style {
    margin: 0 20px;
}
.base-info-style {
    margin-left: 20px !important;
}
.domain-edit {
    overflow-y: hidden;
    display: flex;
    background: #fff;
    margin-bottom: 60px;
    min-width: 0;
}

.domain-edit-spacing {
    margin-bottom: 12px;
    padding: 0 20px;
}

.domain-edit2 {
    margin-bottom: 0;
    padding-right: 0;
    .content-wrap_left {
        margin-right: 0;
    }
}
.content-wrap {
    margin-bottom: 15px; // 抵消el-scrollbar的-15px
}
.content-wrap_left {
    width: calc(100% - 175px);
    margin-right: 20px;
    // flex: 1; // 会导致宽度自适应

    ::v-deep .el-scrollbar__wrap {
        overflow-x: hidden;
    }
}
.content-wrap_left2 {
    width: calc(100%) !important;
}
.label-name {
    font-weight: 500;
    width: 600px;
    overflow: hidden;
    border-left: 4px solid $theme-color;
    line-height: 14px;
    padding-left: 12px;
    margin: 20px 0;
    height: 14px;
}
.mt-0 {
    margin-top: 0;
}
.input-wrapper {
    display: flex;
    justify-content: flex-start;
    width: 1000px;
}

.submit {
    .footer-content {
        display: flex;
        justify-content: flex-end;
    }
}

.radio-group {
    ::v-deep {
        .el-radio.is-bordered + .el-radio.is-bordered {
            margin-left: 0;
            margin-bottom: 8px;
        }

        .el-radio-button__inner {
            min-width: 150px;
        }
    }
}
.req_host_style {
    display: flex;
    justify-content: start;
}
</style>
