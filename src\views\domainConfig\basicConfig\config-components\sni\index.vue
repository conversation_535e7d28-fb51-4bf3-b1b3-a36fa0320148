<template>
    <div>
        <!-- 回源SNI -->
        <el-form-item prop="backorigin_sni.switch" :label="$t('domain.editPage.sni.tip1')">
            <el-switch
                v-model="form.backorigin_sni.switch"
                :active-value="1"
                :inactive-value="0"
                @change="onSwitch"
            ></el-switch>
        </el-form-item>
        <div v-if="form.backorigin_sni.switch" class="switch-wrapper">
            <el-form-item prop="backorigin_sni.sni" :rules="rules.sni">
                <span slot="label">
                    SNI
                    <el-tooltip placement="top" :content="$t('domain.editPage.sni.tip4')">
                        <ct-svg-icon icon-class="question-circle" class-name="ct-sort-drag-icon"></ct-svg-icon
                    ></el-tooltip>
                </span>
                <el-input
                    class="input-box"
                    v-model="form.backorigin_sni.sni"
                    :placeholder="$t('domain.editPage.sni.tip2')"
                    @change="handleChange"
                ></el-input>
            </el-form-item>
        </div>
    </div>
</template>

<script>
import ctSvgIcon from "@/components/ctSvgIcon";
import { exactDomainRegexp } from "@/utils/validator.utils";
import { SecurityAbilityModule } from "@/store/modules/securityAbility";

const getDefaultBackoriginSni = () => ({
    switch: 0,
    sni: "",
});

export default {
    name: "sniConfigCmp",
    components: { ctSvgIcon },
    props: {
        value: {
            type: Object,
            default: getDefaultBackoriginSni,
        },
    },
    data() {
        return {
            form: {
                backorigin_sni: getDefaultBackoriginSni(),
            },
        };
    },
    computed: {
        rules() {
            return {
                sni: [
                    {
                        required: true,
                        message: this.$t("domain.editPage.sni.tip3"),
                    },
                    {
                        validator: (r, v, c) => {
                            if (!exactDomainRegexp.test(v)) {
                                c(new Error(this.$t("domain.editPage.sni.tip5")));
                            }
                            c();
                        },
                    },
                ],
            };
        },
    },
    watch: {
        value: {
            handler(val) {
                if (!val) return;
                this.form.backorigin_sni = val;
            },
            immediate: true,
        },
    },
    methods: {
        onSwitch(v) {
            const originalConf = SecurityAbilityModule.securityBasicConfigOriginForm;
            if (v && originalConf?.backorigin_sni?.switch) {
                this.form.backorigin_sni.sni = originalConf?.backorigin_sni?.sni;
            }

            this.handleChange()
        },
        handleChange() {
            this.$nextTick(() => {
                if (!this.form.backorigin_sni.switch) {
                    this.form.backorigin_sni = getDefaultBackoriginSni();
                }
                this.$emit("input", this.form.backorigin_sni);
            });
        },
    },
};
</script>
<style lang="scss" scoped>
.input-box {
    width: 395px;
}
</style>
