/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @description: 响应式设计
    1、调整自适应尺寸设定，对齐 bootstrp 设计
    2、调整常用预设工具的参数顺序，从小到大
    3、g-media-attr 可以仅指定部分内容参数（sm 和 xs 至少要设定一个），常用预设工具则只能顺序缺少参数（从后向前）
 */

@import '../_var.scss';

// 栅栏数
$gutterCount: 100;

// sass无法元编程，通过循环生成各种mixin，故layoutMap无法使用去优化生成mixin的代码了
// https://stackoverflow.com/questions/39380955/how-to-generate-multiple-mixins-mixin-lib-with-loop-in-sass
// $layoutMap: (
//     width: width,
//     display: display,
//     left: left,
//     right: right,
//     top: top,
//     bottom: bottom,
//     margin-left: mg-lf,
//     margin-right: mg-rt,
//     margin-top: mg-tp,
//     margin-bottom: mg-bt,
//     padding-left: pd-lf,
//     padding-right: pd-rt,
//     padding-top: pd-tp,
//     padding-bottom: pd-bt
// );

// 大屏-设置多个样式
@mixin g-media-lg() {
    @media screen and (min-width: $g-media-lg) {
        @content;
    }
}

// 中屏-设置多个样式
@mixin g-media-md() {
    @media screen and (min-width: $g-media-md) {
        @content;
    }
}

// 小屏-设置多个样式
@mixin g-media-sm() {
    @media screen and (min-width: $g-media-sm) {
        @content;
    }
}

// 移动端-设置多个样式
@mixin g-media-xs() {
    @media screen and (max-width: $g-media-xs) {
        @content;
    }
}

// 适用场景：大中小屏下，设置单个样式值
@mixin g-media-attr($options: ()) {
    // 由于 g-media-sm、md、lg 都是使用的 min-width ，可以根据需要省略部分配置
    $lg: map-get($options, lg);
    $md: map-get($options, md);
    // sm 和 xs 至少要设定一个值
    $sm: map-get($options, sm);
    $xs: map-get($options, xs);
    $attr: map-get($options, attr);

    // 由于 g-media-sm、md、lg 都是使用的 min-width ，所以需要调整生成顺序

    @if $xs {
        @include g-media-xs() {
            #{$attr}: $xs;
        }
    }

    @else if $sm {
        @include g-media-xs() {
            #{$attr}: $sm;
        }
    }

    @if $sm {
        @include g-media-sm() {
            #{$attr}: $sm;
        }
    }

    @else if $xs {
        @include g-media-sm() {
            #{$attr}: $xs;
        }
    }


    @if $md {
        @include g-media-md() {
            #{$attr}: $md;
        }
    }

    @if $lg {
        @include g-media-lg() {
            #{$attr}: $lg;
        }
    }
}

// 定义被 @extend 的 样式代码 重构成容易被理解的形式
@for $idx from 1 through $gutterCount {
    $per: percentage($idx / $gutterCount);

    // 生成各种设备下百分比 placeholder
    // 这里 写成 @include g-media-md(){
    // 	%g-col-md-#{$idx}{
    // 			xxxx
    // 	}
    // }
    // 一样可以，说明如果适用 @extend 的地方，没有被 media 包围，那么被继承的样式不管是
    // 样式包裹 media 还是 media 包裹样式的情况，都能被 @extend 继承到
    %g-col-lg-#{$idx} {
        @include g-media-lg() {
            width: $per;
        }
    }

    %g-col-md-#{$idx} {
        @include g-media-md() {
            width: $per;
        }
    }

    %g-col-sm-#{$idx} {
        @include g-media-sm() {
            width: $per;
        }
    }

    %g-col-xs-#{$idx} {
        @include g-media-xs() {
            width: $per;
        }
    }
}

// 生成列布局 mixin
// 遇到的 bug: 如果 @extend 被 media 包围，那么 @extend 只能继承同样是 @media 里面的样式；不能继承没有定义在 @media 中的外部样式
@mixin g-col($xs: null, $sm: null, $md: null, $lg: null) {
    @if $lg {
        @extend %g-col-lg-#{$lg}
    }

    @if $md {
        @extend %g-col-md-#{$md};
    }

    @if $sm {
        @extend %g-col-sm-#{$sm};
    }

    @if $xs {
        @extend %g-col-xs-#{$xs};
    }
}

// ======= 一些常用的预设 ======
// 调整了参数顺序，改为从小到大，至少要有2个参数（1个参数下没有实际使用意义）
@mixin g-width($xs: null, $sm: null, $md: null, $lg: null) {
    @include g-media-attr($options: (lg: $lg, md: $md, sm: $sm, xs: $xs, attr: width));
}

@mixin g-height($xs: null, $sm: null, $md: null, $lg: null) {
    @include g-media-attr($options: (lg: $lg, md: $md, sm: $sm, xs: $xs, attr: height));
}

@mixin g-pd-lf($xs: null, $sm: null, $md: null, $lg: null) {
    @include g-media-attr($options: (lg: $lg, md: $md, sm: $sm, xs: $xs, attr: padding-left));
}

@mixin g-pd-rt($xs: null, $sm: null, $md: null, $lg: null) {
    @include g-media-attr($options: (lg: $lg, md: $md, sm: $sm, xs: $xs, attr: padding-right));
}

@mixin g-pd-tp($xs: null, $sm: null, $md: null, $lg: null) {
    @include g-media-attr($options: (lg: $lg, md: $md, sm: $sm, xs: $xs, attr: padding-top));
}

@mixin g-pd-bt($xs: null, $sm: null, $md: null, $lg: null) {
    @include g-media-attr($options: (lg: $lg, md: $md, sm: $sm, xs: $xs, attr: padding-bottom));
}

@mixin g-mg-lf($xs: null, $sm: null, $md: null, $lg: null) {
    @include g-media-attr($options: (lg: $lg, md: $md, sm: $sm, xs: $xs, attr: margin-left));
}

@mixin g-mg-rt($xs: null, $sm: null, $md: null, $lg: null) {
    @include g-media-attr($options: (lg: $lg, md: $md, sm: $sm, xs: $xs, attr: margin-right));
}

@mixin g-mg-tp($xs: null, $sm: null, $md: null, $lg: null) {
    @include g-media-attr($options: (lg: $lg, md: $md, sm: $sm, xs: $xs, attr: margin-top));
}

@mixin g-mg-bt($xs: null, $sm: null, $md: null, $lg: null) {
    @include g-media-attr($options: (lg: $lg, md: $md, sm: $sm, xs: $xs, attr: margin-bottom));
}

@mixin g-display($xs: null, $sm: null, $md: null, $lg: null) {
    @include g-media-attr($options: (lg: $lg, md: $md, sm: $sm, xs: $xs, attr: display));
}
