import { RouteConfig } from "vue-router";
import { DomainModule } from "@/store/modules/domain";
import { nUserModule } from "@/store/modules/nuser";
import { DomainActionEnum, getDomainAction } from "@/config/map";

const logRouter: RouteConfig = {
    path: "/log/buslog",
    name: "log",
    component: () => import("./buslog.vue"),
    meta: {
        breadcrumb: {
            title: '$t("log.title")',
            route: ["log"],
        },
        perm: "log",
    },
    beforeEnter(to, from, next) {
        // 不阻塞页面加载
        const isFcdnCtyunCtclouds = nUserModule.isFcdnCtyunCtclouds;
        isFcdnCtyunCtclouds
            ? DomainModule.GetDomainList({ action: getDomainAction("Log") })
            : DomainModule.GetDomainList({ action: DomainActionEnum.Data });
        next();
    },
    // children: [
    //     {
    //         path: "buslog",
    //         name: "buslog",
    //         component: () => import("./buslog.vue"),
    //         meta: {
    //             breadcrumb: {
    //                 title: "域名离线日志",
    //                 route: ["home", "log", "buslog"],
    //             },
    //             perm: "buslog",
    //         },
    //     },
    // ],
};
export default logRouter;
