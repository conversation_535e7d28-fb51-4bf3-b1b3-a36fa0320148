<template>
    <el-dialog
        :title="dialogTitle"
        :append-to-body="true"
        :close-on-click-modal="false"
        :visible.sync="dialogVisible"
        :before-close="cancel"
        width="1000px"
        class="back-origin-dialog"
    >
        <el-form
            :rules="addRules"
            :model="backOriginForm"
            ref="backOriginForm"
            class="cache-form"
            label-width="200px"
        >
            <el-row>
                <el-col :span="10">
                    <el-form-item prop="keep_args_order">
                        <span slot="label">
                            {{ $t("domain.detail.label121")  }}
                            <span class="question-style">
                                <el-tooltip placement="top">
                                    <div slot="content">{{ $t("domain.detail.tip110") }}</div>
                                    <ct-svg-icon icon-class="question-circle" class-name="question-circle"/>
                                </el-tooltip>
                            </span>
                        </span>
                        <el-switch v-model="backOriginForm.keep_args_order" active-value="on" inactive-value="off" @change="keepArgsOrderChange"></el-switch>
                    </el-form-item>
                </el-col>
                <el-col :span="10" v-if="backOriginForm.keep_args_order === 'on'">
                    <el-form-item :label="$t('domain.detail.label122')" prop="need_encode_args">
                        <el-switch v-model="backOriginForm.need_encode_args" active-value="on" inactive-value="off"></el-switch>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="10">
                    <el-form-item :label="$t('domain.detail.label76')" prop="mode">
                        <el-select v-model="backOriginForm.mode" @change="backOriginModeChange">
                            <el-option value="add" :label="$t('domain.detail.label80')" />
                            <el-option value="cover" :label="$t('domain.detail.label81')"  />
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="11">
                    <el-form-item :label="$t('domain.detail.label48')" prop="priority">
                        <el-input v-model.number="backOriginForm.priority" maxlength="16" />
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row v-for="(it, ix) in backOriginForm.argsList" :key="`arg_${ix}`">
                <el-col :span="10">
                    <el-form-item
                        :label="$t('domain.detail.label78')"
                        :prop="`argsList.${ix}.name`"
                        :rules="[{ required: true, message: $t('domain.detail.tip83'), trigger: 'blur' }]"
                    >
                        <el-input v-model.trim="it.name"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="11">
                    <el-form-item :label="$t('domain.detail.label79')" :prop="`argsList.${ix}.value`">
                        <span slot="label" class="res-header-style"
                            >{{ $t("domain.detail.label79") }}
                            <span class="question-style">
                                <el-tooltip placement="top">
                                    <div slot="content">
                                        {{ $t("domain.detail.tip70")}} <br />
                                        {{ $t("domain.detail.tip71")}}
                                    </div>
                                    <ct-svg-icon
                                        icon-class="question-circle"
                                        class-name="question-circle"/></el-tooltip></span
                        ></span>
                        <el-input v-model.trim="it.value"></el-input>
                        <!-- <div class="path-note" v-if="ix === backOriginForm.argsList.length - 1">
                            {{ "参数值为空代表删除对应参数。" }}<br />
                            {{
                                "保留指定参数示例：参数名:a，参数值：$arg_a，其中$arg_a代表问号后参数a的值。不支持参数名带中划线，如参数名：a-b，如需配置，请提交工单。"
                            }}
                        </div> -->
                    </el-form-item>
                </el-col>
                <el-col :span="3">
                    &nbsp;&nbsp;
                    <el-button
                        size="mini"
                        type="danger"
                        icon="el-icon-minus"
                        circle
                        @click="delParamterArgs(ix)"
                    ></el-button>
                    <el-button
                        size="mini"
                        type="success"
                        icon="el-icon-plus"
                        circle
                        @click="addParamterArgs()"
                    ></el-button>
                </el-col>
            </el-row>
        </el-form>
        <div slot="footer">
            <el-button class="dialog-btn" @click="cancel">{{ $t("domain.cancel") }} </el-button>
            <el-button type="primary" class="dialog-btn" @click="submit">
                {{ $t("common.dialog.submit") }}
            </el-button>
        </div>
    </el-dialog>
</template>

<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";
import { formValidate2Promise } from "@/utils";
import { Form } from "element-ui";
import ctSvgIcon from "@/components/ctSvgIcon/index.vue";
import i18n from "@/i18n";
import { nUserModule } from "@/store/modules/nuser";

type backOriginParam = {
    mode: string;
    priority: string;
    argsList: any;
    keep_args_order: string;
    need_encode_args: string;
};
const formValidate2Field = (form: Form) =>
    new Promise((resolve, reject) => {
        form.validate(valid => {
            if (valid) {
                resolve(true);
            }
        });
    });

@Component({
    components: { ctSvgIcon },
})
export default class UpdateDialog extends Vue {
    @Prop({ default: "create", type: String }) private from!: string;
    // 弹框显示标志位
    @Prop({ default: false, type: Boolean }) private dialogVisible!: boolean;
    @Prop({
        default: {
            mode: 0,
            priority: 10,
            argsList: [
                {
                    name: "",
                    value: "",
                },
            ],
            keep_args_order: "off",
            need_encode_args: "on",
        },
    })
    private backOriginForm!: backOriginParam;

    get dialogTitle() {
        const separate = nUserModule.lang === "en" ? " " : "";
        return `${this.from === 'create' ? i18n.t('domain.add2') : i18n.t('domain.modify')}${separate}${i18n.t('domain.detail.label30')}`
    }
    get values() {
        // 处理换行、所有空格、过滤空数据
        return this.backOriginForm;
    }
    // 校验规则
    get addRules() {
        return {
            priority: [
                { required: true, message: this.$t("domain.detail.placeholder22"), trigger: "blur" },
                { trigger: "blur", pattern: /^[0-9]*$/, message: this.$t("domain.detail.placeholder32") },
                {
                    validator: (rule: any, value: number, callback: Function) => {
                        if (value > 100 || value < 1) {
                            callback(this.$t("domain.detail.placeholder32"));
                        } else callback();
                    },
                    trigger: ["blur", "change"],
                },
            ],
        };
    }
    // 回源改写新增参数
    addParamterArgs() {
        this.backOriginForm.argsList.push({ name: "", value: "" });
    }
    delParamterArgs(idx: any) {
        if (this.backOriginForm.mode !== "cover" && this.backOriginForm.argsList.length < 2) {
            this.$message({
                showClose: true,
                message: this.$t("domain.detail.tip88") as string,
            });
            return;
        }
        this.backOriginForm.argsList.splice(idx, 1);
    }
    keepArgsOrderChange(val: string) {
        if (val === "on") {
            this.backOriginForm.need_encode_args = "on";
        }
    }
    private async submit() {
        await formValidate2Promise(this.$refs.backOriginForm as Form);
        this.$emit("submit");
    }
    private cancel() {
        this.$emit("cancel", "dialogBackOriginVisible");
    }
    backOriginModeChange() {
        if (this.backOriginForm?.argsList?.length < 1) {
            const argsList = [{ name: "", value: "" }];
            this.$set(this.backOriginForm, "argsList", argsList);
        }
    }
}
</script>

<style lang="scss" scoped>
.filetype-ttl-dialog {
    ::v-deep {
        .el-dialog__body {
            padding: 24px !important;
        }
    }
}
</style>
