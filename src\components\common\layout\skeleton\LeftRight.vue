<template>
    <div class="page">
        <div
            v-if="contenLoading"
            class="page-content loading-panel"
            v-loading="contenLoading"
            :element-loading-text="$t('common.loading')"
        ></div>
        <section
            class="page-menu"
            ref="sidebar"
            v-if="!withoutAuth && !contenLoading"
            :style="{ width: isOpen ? '190px' : '0px' }"
        >
            <brief ref="brief" />
            <sidebar-menu :menuList="menuList" :pathMap="pathMap" />
            <div class="sidebar_knob" @click="toggleCollapse">
                <i :class="isOpen ? 'el-icon-arrow-left' : 'el-icon-arrow-right'"></i>
            </div>
        </section>
        <section class="page-content" v-if="!contenLoading">
            <template v-if="isBaseApp">
                <keep-alive>
                    <router-view v-if="$route.meta.keepAlive">
                        <!-- 这里是会被缓存的视图组件，比如 Home！ -->
                    </router-view>
                </keep-alive>

                <router-view v-if="!$route.meta.keepAlive">
                    <!-- 这里是不被缓存的视图组件，比如 Edit！ -->
                </router-view>
            </template>
            <micro-app v-else />
        </section>
        <ct-toolbar :menuList="menuList" :pathMap="pathMap" v-if="!withoutAuth" />
    </div>
</template>
<script>
import Brief from "../sidebar/Brief.vue";
import SidebarMenu from "../sidebar/Menu.vue";
import CtToolbar from "../toolbar/index.vue";
import microAppRoutes from "@/router/microAppRoutes";
import microApp from "@/views/microApp";
import { AppModule } from "@/store/modules/app";

export default {
    name: "LeftRight",
    props: {
        menuList: {
            required: true,
            type: Array,
        },
        pathMap: {
            required: true,
            type: Map,
        },
    },
    data() {
        return {
            isOpen: true,
        };
    },
    components: {
        Brief,
        SidebarMenu,
        CtToolbar,
        microApp,
    },
    computed: {
        contenLoading() {
            return AppModule.contenProductLoading || AppModule.contentMenuLoading;
        },
        withoutAuth() {
            return this.$route.meta.withoutAuth ? this.$route.meta.withoutAuth : false;
        },
        // 是否基座app
        isBaseApp() {
            const microApps = microAppRoutes.map(item => item.name);
            const routeName = this.$route.name;
            return !microApps.includes(routeName);
        },
    },
    methods: {
        toggleCollapse() {
            this.isOpen = !this.isOpen;
        },
    },
};
</script>
<style lang="scss" scoped>
.page {
    display: flex;
    // 直接100%高度，给内部的菜单提供高度
    @include g-full-percent(width, height);
    position: relative;
    overflow: hidden;
    height: calc(100vh - var(--cty-consoleLayout-headerHeight, 48px));
    ::v-deep {
        .loading-panel .el-loading-spinner .el-loading-text {
            color: #7C818C;
        }
    }
}
.page-menu {
    position: relative;
    @include g-display(none, block);
    height: 100%;
    width: 190px;
    // overflow: hidden; // 避免撑开容器
    vertical-align: top;
    // transition: all ease 0.3s;
    // border-right: 1px solid #e6e6e6;
    background: #fff;

    ::v-deep {
        .el-scrollbar {
            height: calc(100% - 78px);

            .el-scrollbar__wrap {
                overflow-x: hidden; // 不展示横向滚动轴
            }
        }
    }
}

.sidebar_knob {
    z-index: 100;
    height: 80px;
    width: 12px;
    position: absolute;
    top: calc(50% - 40px - 50px / 2);
    right: -12px;
    border-top-right-radius: 10px;
    border-bottom-right-radius: 10px;
    display: flex;
    background: #fff;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    cursor: pointer;
    color: #666;
    &:focus {
        height: 100px;
    }
}
.page-content {
    // position: absolute;
    flex: 1;
    min-width: 0;
    position: relative;
    padding: 0;
    overflow: auto;
    min-height: 100%;
    font-size: 12px;
    line-height: 1.5;

    white-space: normal; // 层叠掉 layout 中的配置，避免文本流样式错乱

    background-color: $color-neutral-2;
}
</style>
