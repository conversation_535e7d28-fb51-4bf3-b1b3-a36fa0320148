<template>
    <div class="chart-wrapper" v-loading="loading">
        <div class="ipa-overview-chart-text">{{ chartText }}</div>
        <v-chart class="chart-overview" element-loading-text="正在拼命绘图中..." autoresize theme="cdn" :options="options" />
    </div>
</template>

<script>
/* eslint-disable @typescript-eslint/camelcase */
import { OverviewUrl } from "@/config/url";
import { statistics as StatisticsUrl } from "@/config/url/ipa/statistics";
import { ScaleModule } from "@/store/modules/scale";
import { convertBandwidthB2G, convertFlowB2P } from "@/utils";
import { getMaxFromList } from "@/utils/utils";
import { get } from "lodash-es";
import { cloneDeep } from "lodash-es";
import { THEME_AREA_STYLE } from "@/config/echart/cdn-theme";
import { themeColorArr } from "@cdnplus/common/config/echart/theme";
import { IpaConfigModule } from "@/store/modules/ipa/config";

export default {
    name: "overview-chart",
    props: {
        activeType: {
            type: String,
            default: "",
        },
        timeRange: {
            type: Array,
            default: null,
        },
        domain: {
            type: Array || String,
            default: () => [],
        },
        domainOptions: {
            type: Array,
            default: () => [],
        }
    },
    data() {
        return {
            options: {}, // 图表的选项
            flowDataList: [],
            bandWithDataList: [],
            charData: [],
            xData: [],
            loading: true,
            avgFlow: 0,
            peakBandwidth: 0,
            loadingIndex: 1,
        };
    },
    computed: {
        getChartAxisName() {
            return `${this.$t("home.chart.unit")}: ${this.isFlow ? this.flowIndention[1] : this.bandwidthIndention[1]}`
        },
        getUnit() {
            return {
                bandwidth: this.bandwidthIndention[1],
                flow: this.flowIndention[1],
                connections: "个"
            }[this.activeType];
        },
        chartText() {
            const map = new Map([
                ["flow", "流量"],
                ["bandwidth", "带宽"],
                ["connections", "连接数"],
            ]);
            return map.get(this.activeType) || this.activeType;
        },
        isFlow() {
            return this.activeType === "flow";
        },
        // ***单位换算：获取进制基数
        scale() {
            return ScaleModule.scale;
        },
        //通过平均流量*300,通过此指标来执行五分钟流量缩进,返回[缩进倍数，单位]
        flowIndention() {
            const size = {
                B: 1,
                KB: this.scale,
                MB: Math.pow(this.scale, 2),
                GB: Math.pow(this.scale, 3),
                TB: Math.pow(this.scale, 4),
                PB: Math.pow(this.scale, 5),
            };
            const rst = convertFlowB2P(getMaxFromList(this.flowDataList, "flow"), this.scale);
            return [size[rst._unit], rst.unit];
        },
        // 获取图标数据的起始时间对象
        getStartEndTime() {
            if (!this.timeRange || !this.timeRange.length) return {};
            const time = {
                start_time: Math.floor(this.timeRange[0].getTime() / 1000),
                end_time: Math.floor(this.timeRange[1].getTime() / 1000),
            };
            return time;
        },
        //以峰值带宽为指标,采用自缩进方式，返回[缩进倍数，单位]
        bandwidthIndention() {
            const size = {
                bps: 1,
                Kbps: this.scale,
                Mbps: Math.pow(this.scale, 2),
                Gbps: Math.pow(this.scale, 3),
                Tbps: Math.pow(this.scale, 4),
                Pbps: Math.pow(this.scale, 5),
            };
            const rst = convertBandwidthB2G(getMaxFromList(this.bandWithDataList, "bandwidth"), this.scale);
            return [size[rst._unit], rst.unit];
        },
        // 域名限制数量
        domainCountLimit() {
            return IpaConfigModule.ipaDomainSelectorLimit;
        }
    },
    mounted() {
        this.initFlowData();
    },
    watch: {
        activeType() {
            this.getChartData();
        },
        timeRange() {
            if (!this.timeRange || !this.timeRange.length) return;
            this.getChartData();
        },
    },
    methods: {
        async initFlowData() {
            const params = this.getRequestParams();
            if (get(params.domain, "length") > this.domainCountLimit) {
                return;
            }

            const loadingIndex = this.loadingIndex++;
            const { result } = await this.getFlowList();
            if (loadingIndex !== this.loadingIndex - 1)
                return console.warn("get different loading index! stop rendering.");
            const number = this.getIntervalNum(result.list);
            this.charData = []; //重置
            this.xData = []; //重置

            this.flowDataList = cloneDeep(result.list);

            result.list
                // 时间升序排序
                .sort((a, b) => a.timestamp - b.timestamp)
                .map(item => {
                    this.xData.push(this.formatTimeNew(item.timestamp));
                    this.charData.push((item.flow / this.flowIndention[0]).toFixed(2));
                });
            this.avgFlow =
                (result.list.reduce((prev, cur) => prev + cur.flow, 0) / (result.list.length || 1)) * 300;
            this.renderChart(number, "flow");
            this.loading = false;
        },
        /**
         * 获取间隔周期
         * @param result
         */
        getIntervalNum(result) {
            const map = {};
            result.forEach(item => {
                const time = this.$dayjs?.unix(item.timestamp).format("YYYY-MM-DD");
                if (!map[time]) {
                    map[time] = [];
                }
                map[time].push(item);
            });
            return get(Object.values(map)[0], "length");
        },
        async initBandWidthData() {
            const loadingIndex = this.loadingIndex++;
            const { result } = await this.getBandWithList();
            if (loadingIndex !== this.loadingIndex - 1)
                return console.warn("get different loading index! stop rendering.");
            const number = this.getIntervalNum(result.bandw);
            this.charData = []; //重置
            this.xData = []; //重置

            this.bandWithDataList = cloneDeep(result.bandw);

            result.bandw
                // 时间升序排序
                .sort((a, b) => a.timestamp - b.timestamp)
                .map(item => {
                    this.xData.push(this.formatTimeNew(item.timestamp));
                    this.charData.push((item.bandwidth / this.bandwidthIndention[0]).toFixed(2));
                });
            this.peakBandwidth = result?.peek?.bandwidth || 0;
            this.renderChart(number, "bandwidth");
            this.loading = false;
        },
        async initConnectionsData() {
            const loadingIndex = this.loadingIndex++;
            const { result } = await this.getConnectionsList();
            if (loadingIndex !== this.loadingIndex - 1)
                return console.warn("get different loading index! stop rendering.");
            const number = this.getIntervalNum(result.list);
            this.charData = []; //重置
            this.xData = []; //重置
            result.list.map(item => {
                this.xData.push(this.formatTimeNew(item.timestamp)); // ***s->ms
                this.charData.push(item.connections);
            });
            this.renderChart(number, "connections");
            this.loading = false;
        },
        formatTimeNew(t) {
            t = this.$dayjs(t * 1000);
            return `${t.format("YYYY-MM-DD")}\n${t.format("HH:mm:ss")}`;
        },
        formatTime(timestamp) {
            let time = this.$dayjs(timestamp * 1000).format("d M-DD");
            const timeArr = time.split("");
            const weekCn = this.getweek(timeArr[0]);
            timeArr.splice(0, 1, weekCn);
            time = timeArr.join("");
            const temp = time.split(" ");
            time = temp[1] + " " + temp[0];
            return time;
        },
        getweek(e) {
            return ["周日", "周一", "周二", "周三", "周四", "周五", "周六"][e];
        },
        getChartData() {
            const val = this.activeType;
            this.loading = true;
            if (val === "flow") {
                this.initFlowData();
            } else if (val === "bandwidth") {
                this.initBandWidthData();
            } else if (val === "connections") {
                this.initConnectionsData();
            }
        },
        getRequestParams() {
            const data = {
                account_id: this.$store.state.user.userInfo.userId,
                ...this.getStartEndTime,
            };
            if (this.domain?.length) data.domain = this.domain;
            if (this.domainOptions.length === this.domain?.length) delete data.domain;
            return data;
        },
        getFlowList() {
            return this.$ctFetch(OverviewUrl.flowList, {
                method: "POST",
                transferType: "json",
                body: {
                    data: this.getRequestParams(),
                },
            });
        },
        getBandWithList() {
            return this.$ctFetch(OverviewUrl.bandWithList, {
                method: "POST",
                transferType: "json",
                body: {
                    data: this.getRequestParams(),
                },
            });
        },
        getConnectionsList() {
            return this.$ctFetch(StatisticsUrl.connectionsList, {
                method: "POST",
                transferType: "json",
                body: {
                    data: this.getRequestParams(),
                },
            });
        },
        renderChart(number, type) {
            const { activeType } = this;
            const typeMap = {
                flow: {},
                connections: {
                    yAxis: {
                        name: `${this.$t("statistics.eas.connections.unit1")}`,
                        type: "value",
                        minInterval: 1,
                    }
                },
                bandwidth: {},
            };
            if (!Object.keys(typeMap).includes(type)) return;

            let count = 0;

            const options = {
                tooltip: {
                    trigger: "axis",
                    formatter: ([params]) => {
                        const time = this.$dayjs(params.axisValue).format("YYYY-MM-DD HH:mm:ss");
                        const val = params.value;
                        return `
                            <div>
                                <div style="display:inline-block;width:8px;height:8px;background-color:#6694D5;border-radius:50%;"></div>
                                ${time}</div>
                            <div>${this.chartText}：${val} ${this.getUnit}</div>
                            `;
                    },
                },
                dataZoom: [
                    // 导航条，同时支持侧边栏和坐标系内置
                    {
                        type: "slider",
                        filterMode: "filter",
                    },
                    { type: "inside" },
                ],
                grid: {
                    // 容器各个方向的留白
                    left: "5%",
                    right: "6%",
                    top: "8%", // 流出y轴name的显示空间
                    bottom: "16%",
                    containLabel: true,
                },
                xAxis: {
                    type: "category",
                    data: this.xData,
                    axisLabel: {
                        showMinLabel: true,
                        formatter: value => {
                            const time = this.$dayjs(value).format("MM-DD HH:mm");
                            const arr = time.split(" ");
                            count += 1;
                            if (count % 2 === 1) return `${arr[0]}\n${arr[1]}`;
                        },
                    },
                },
                yAxis: typeMap[activeType].yAxis || {
                    type: "value",
                    name: this.getChartAxisName,
                    splitLine: {
                        show: true,
                        lineStyle: {
                            type: "dashed",
                            color: "#ddd",
                        },
                    },
                },
                series: [
                    {
                        name: `${this.$t("statistics.provider.traffic")}1`,
                        type: "line",
                        data: this.charData,
                        areaStyle: THEME_AREA_STYLE[themeColorArr[0]],
                    },
                ],
            };
            this.options = options;
        },
    },
};
</script>
<style lang="scss">
.ipa-overview-chart-text {
    padding-right: 6%;
    text-align: right;
    font-size: 12px;
    color: #333333;
    line-height: 18px;
    font-weight: 400;

    &::before {
        content: "";
        display: inline-block;
        margin-right: 8px;
        width: 6px;
        height: 6px;
        border-radius: 50%;
        background: #78a1da;
    }
}

.ipa-overview-tooltip-text {
    font-size: 12px;
    color: #666666;
    line-height: 24px;
    font-weight: 400;
    margin-top: 8px;
    text-align: center;
    padding-right: 0;
    display: flex;
    justify-content: space-between;

    &-title::before {
        content: "";
        display: inline-block;
        margin-right: 8px;
        width: 6px;
        height: 6px;
        border-radius: 50%;
        background: #78a1da;
    }
}
</style>
<style lang="scss" scoped>
.chart-wrapper {
    .chart-overview {
        width: 100%;
        height: 397px;
    }
}
</style>
