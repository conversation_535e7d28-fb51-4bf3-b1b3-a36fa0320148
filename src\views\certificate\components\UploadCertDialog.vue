<template>
    <el-dialog
        :title="$t('domain.create.title3')"
        :append-to-body="true"
        :close-on-click-modal="false"
        :visible.sync="addVisible"
        :before-close="cancel"
        class="add-dialog"
        width="800px"
    >
        <el-alert type="info" :closable="false" show-icon class="cert-tips">
            <template slot="title">
                <div>{{ $t("billing.confirmTip.title") }}</div>
                <div v-html="$t('certificate.text4', { orderLink })"></div>

                <div>{{ $t("certificate.text5") }}</div>
                <div>{{ $t("certificate.text6") }}</div>
            </template>
        </el-alert>
        <el-form
            :rules="addRules"
            :model="certificate"
            ref="addForm"
            class="add-form"
            @validate="onValidate"
            :label-width="isEn ? '220px' : '160px'"
            :validate-on-rule-change="false"
        >
            <el-form-item
                :label="$t('certificate.证书类型')"
                prop="certType"
                :class="[isAddMargin ? 'mb-36' : '']"
            >
                <el-radio-group v-if="isFromCertList" v-model="certificate.algorithm_type">
                    <el-radio :label="0">{{ $t("certificate.国际标准证书") }}</el-radio>
                    <el-radio :label="1">{{ $t("certificate.国密证书") }}</el-radio>
                </el-radio-group>
                <div v-else>
                    {{
                        certificate.algorithm_type === 1
                            ? $t("certificate.国密证书")
                            : $t("certificate.国际标准证书")
                    }}
                </div>
            </el-form-item>
            <el-form-item
                :label="$t('domain.create.certName')"
                prop="certName"
                :class="[isAddMargin ? 'mb-36' : '']"
            >
                <el-input v-model="certificate.certName" :placeholder="$t('certificate.text1')"></el-input>
            </el-form-item>
            <template v-if="isSM2">
                <el-form-item :label="$t('certificate.签名证书公钥')" prop="certs_sign">
                    <!-- 尝试禁用语法检查，避免某些浏览器因为语法检查出现的红下划线 -->
                    <el-input
                        type="textarea"
                        :spellcheck="false"
                        :placeholder="$t('certificate.请输入PEM格式的签名证书公钥内容')"
                        v-model="certificate.certs_sign"
                    ></el-input>
                    <div class="pem-sample-link">
                        <el-button
                            type="text"
                            @click="
                                pemSampleType = 'sm2PublicKey';
                                showPemSample = true;
                            "
                        >
                            {{ $t("certificate.PEM编码参考样例") }}
                        </el-button>
                    </div>
                </el-form-item>
                <el-form-item :label="$t('certificate.签名证书私钥')" prop="key_sign">
                    <el-input
                        type="textarea"
                        :spellcheck="false"
                        :placeholder="$t('certificate.请输入PEM格式的签名证书私钥内容')"
                        v-model="certificate.key_sign"
                    ></el-input>
                    <div class="pem-sample-link">
                        <el-button
                            type="text"
                            @click="
                                pemSampleType = 'sm2PrivateKey';
                                showPemSample = true;
                            "
                        >
                            {{ $t("certificate.PEM编码参考样例") }}
                        </el-button>
                    </div>
                </el-form-item>
            </template>
            <el-form-item
                :label="isSM2 ? $t('certificate.加密证书公钥') : $t('domain.create.certs')"
                prop="certs"
            >
                <!-- 尝试禁用语法检查，避免某些浏览器因为语法检查出现的红下划线 -->
                <el-input
                    type="textarea"
                    :spellcheck="false"
                    :placeholder="
                        isSM2
                            ? $t('certificate.请输入PEM格式的加密证书公钥内容')
                            : $t('certificate.请输入PEM格式的证书公钥内容')
                    "
                    v-model="certificate.certs"
                ></el-input>
                <div class="pem-sample-link">
                    <el-button
                        type="text"
                        @click="
                            pemSampleType = isSM2 ? 'sm2PublicKey' : 'publicKey';
                            showPemSample = true;
                        "
                    >
                        {{ $t("certificate.PEM编码参考样例") }}
                    </el-button>
                </div>
            </el-form-item>
            <el-form-item
                :label="isSM2 ? $t('certificate.加密证书私钥') : $t('domain.create.key')"
                prop="key"
            >
                <el-input
                    type="textarea"
                    :spellcheck="false"
                    :placeholder="
                        isSM2
                            ? $t('certificate.请输入PEM格式的加密证书私钥内容')
                            : $t('certificate.请输入PEM格式的证书私钥内容')
                    "
                    v-model="certificate.key"
                ></el-input>
                <div class="pem-sample-link">
                    <el-button
                        type="text"
                        @click="
                            pemSampleType = isSM2 ? 'sm2PrivateKey' : 'privateKey';
                            showPemSample = true;
                        "
                    >
                        {{ $t("certificate.PEM编码参考样例") }}
                    </el-button>
                </div>
            </el-form-item>
        </el-form>
        <div v-loading="loading" slot="footer">
            <el-button @click="cancel">{{ $t("common.dialog.cancel") }}</el-button>
            <el-button type="primary" class="dialog-btn" @click="submit">
                {{ $t("domain.submit1") }}
            </el-button>
        </div>
        <chain-dialog
            :chainVisible="chainVisible"
            :chainDetail="chainDetail"
            @chainEnsure="onChainEnsure"
            @certClose="onCertClose"
            isAddCert
        ></chain-dialog>

        <pem-sample-dialog
            v-model="showPemSample"
            :show-s-m2="true"
            :sample-type="pemSampleType"
        ></pem-sample-dialog>
    </el-dialog>
</template>

<script lang="ts">
import { Component, Prop, Watch, Ref, Vue } from "vue-property-decorator";
import { SecretsItem } from "@/types/certificate";
import { formValidate2Promise, getLang } from "@/utils";
import { nUserModule } from "@/store/modules/nuser";
import ChainDialog from "./ChainDialog.vue";
import PemSampleDialog from "./PemSampleDialog.vue";
import { nCertificateUrl } from "@/config/url";
import { commonLinks } from "@/utils/logic/url";
import { CODE_CERT_ERROR_3001 } from "@/utils/ctFetch/errorConfig";
import { ElForm } from "element-ui/types/form";
import { certParam, certsRules, getDefaultCertParam } from "../util";

@Component({
    name: "UploadCertDialog",
    components: {
        ChainDialog,
        PemSampleDialog,
    },
})
export default class UpdateDialog extends Vue {
    // 弹框显示标志位
    @Prop({ default: false, type: Boolean }) private addVisible!: boolean;
    // 加载状态标志位
    @Prop({ default: false, type: Boolean }) private loading!: boolean;
    // 证书
    @Prop({
        default: getDefaultCertParam(),
        type: Object,
    })
    private certificate!: certParam;
    @Prop({
        default: () => [],
        type: Array,
    })
    private dataList!: SecretsItem[];
    @Prop({ default: true, type: Boolean }) private isFromCertList!: boolean;
    @Ref("addForm")
    readonly addFormRef!: ElForm;
    private chainError = false;
    private chainVisible = false;
    private chainDetail = { reason: "", data: { metadata: [] } };
    // 是否增加和底部的距离
    private isAddMargin = false;
    // 是否显示PEM样例弹窗
    private showPemSample = false;
    private pemSampleType = "publicKey"; // 当前显示的样例类型
    get lang() {
        return nUserModule.lang;
    }
    // 校验规则
    get addRules() {
        return {
            certName: [
                {
                    required: true,
                    message: this.$t("certificate.update.label1Tip1"),
                    trigger: "blur",
                },
                {
                    pattern: "^[\\u4e00-\\u9fa5\\w-\\*\\.\\(\\)]+$",
                    message: this.$t("certificate.update.label1Tip2"),
                    trigger: ["blur", "change"],
                },
                {
                    max: 255,
                    message: this.$t("certificate.update.label1Tip3"),
                    trigger: ["blur", "change"],
                },
                {
                    validator: (rule: any, value: string, callback: Function) => {
                        if (this.dataList.some(certificate => certificate.name === value))
                            callback(new Error(this.$t("certificate.update.label1Tip4") as string));
                        else callback();
                    },
                    trigger: ["blur", "change"],
                },
            ],
            ...certsRules(this.isSM2),
        };
    }
    get isEn() {
        return getLang() === "en";
    }
    get isCtclouds() {
        return nUserModule.isCtclouds;
    }
    get orderLink() {
        return commonLinks.orderLink;
    }
    get isSM2() {
        return this.certificate.algorithm_type === 1;
    }
    onCertClose() {
        this.chainVisible = false;
    }
    onChainEnsure() {
        this.chainVisible = false;
        this.$emit("submit");
    }
    @Watch("addVisible")
    onAddVisibleChange(val: boolean) {
        if (!val) {
            this.addFormRef.clearValidate();
        }
    }
    async submit() {
        await formValidate2Promise(this.addFormRef);

        // 新增增加证书链校验
        this.chainVisible = false;
        this.chainError = false;
        const data = {
            certs: this.certificate.certs,
            language: this.lang,
            certs_sign: this.certificate.certs_sign,
        };
        if (!this.isSM2) {
            delete data.certs_sign;
        }

        await this.$ctFetch(nCertificateUrl.chains, {
            method: "POST",
            body: {
                data,
            },
            headers: {
                "Content-Type": "application/json",
            },
        }).catch(e => {
            this.chainError = true;
            if (e?.data?.code === CODE_CERT_ERROR_3001) {
                this.chainVisible = true;
                this.chainDetail = e?.data || { reason: "", data: { metadata: [] } };
            } else {
                this.$errorHandler(e);
            }
        });
        if (this.chainError) return;
        this.$emit("submit");
    }
    cancel() {
        this.$emit("cancel");
    }
    onValidate(prop: string, valid: boolean, msg: string) {
        if (prop === "certName") {
            if (valid) return (this.isAddMargin = false);
            if (msg.length > 70) this.isAddMargin = true;
        }
    }
}
</script>

<style lang="scss" scoped>
.add-dialog {
    .dialog-icon {
        color: $color-warning;
        font-size: 12px;
    }

    .dialog-tip {
        color: $color-warning;
        font-size: 12px;
        margin-bottom: 8px;
        display: inline-block;
        margin-left: 160px;
    }

    ::v-deep .el-dialog {
        @include g-width(90%, 60%, 45%);
    }

    .add-form {
        margin-top: 16px;

        ::v-deep {
            .el-form-item__label {
                display: inline-block;
                @include g-width(100%, 160px, 160px);
            }

            .el-form-item__content {
                @include g-width(100%, auto, auto);
                @include g-media-attr(
                    (
                        attr: margin-left,
                        md: 160px,
                        sm: 160px,
                        xs: 0,
                    )
                );
            }
        }
    }
}

.mb-36 {
    margin-bottom: 36px;
    ::v-deep {
        .el-form-item__error {
            line-height: 1 !important;
        }
    }
}
.aocdn-ignore-cert-wrapper {
    a {
        color: $color-master;
    }
}
</style>
